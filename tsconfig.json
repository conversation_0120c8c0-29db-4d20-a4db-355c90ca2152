{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "Node", "strict": false, "jsx": "preserve", "importHelpers": true, "experimentalDecorators": true, "strictFunctionTypes": false, "skipLibCheck": true, "esModuleInterop": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "sourceMap": true, "baseUrl": ".", "allowJs": false, "resolveJsonModule": true, "lib": ["dom", "esnext"], "paths": {"@/*": ["src/*"], "@build/*": ["build/*"]}, "types": ["node", "vite/client", "dompurify", "sm-crypto", "element-plus/global", "@pureadmin/table/volar", "@pureadmin/descriptions/volar"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/*.d.ts", "vite.config.ts"], "exclude": ["dist", "**/*.js", "node_modules"]}