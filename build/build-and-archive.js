const colors = require('picocolors');
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 定义目标路径和输出文件名
const targetDir = path.resolve(__dirname, '../target');
const outputFilePath = path.join(targetDir, 'dist.tar');

// 检查并创建 target 目录（如果不存在）
if (!fs.existsSync(targetDir)) {
  fs.mkdirSync(targetDir, {recursive: true});
}

// 创建输出流
const output = fs.createWriteStream(outputFilePath);

// 初始化 archiver 实例
const archive = archiver('tar', {
  gzip: true,
  gzipOptions: {
    level: 9 // 设置压缩级别
  }
});

// 监听所有错误
archive.on('error', function (err) {
  throw err;
});

// 管道传输到文件
archive.pipe(output);

// 添加 dist 文件夹到归档
archive.directory('dist/', 'dist');

// 完成归档
archive.finalize();

// 监听归档完成事件，并在完成后关闭输出流
output.on('close', function () {
  console.log(colors.bold(colors.green(`编译完成文件已打包至 ${colors.red(outputFilePath)}`)));
});
