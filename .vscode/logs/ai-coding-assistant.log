2025-05-08T07:58:01.043Z INFO: 缓存服务初始化完成 
2025-05-08T07:58:01.056Z WARN: 依赖 tree-sitter 不可用: Cannot find module 'tree-sitter'
Require stack:
- /Users/<USER>/.vscode/extensions/ai-coding-assistant.ai-coding-assistant-0.0.1/dist/extension.js
- /Applications/Visual Studio Code.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js 
2025-05-08T07:58:01.066Z WARN: 缺失以下依赖，某些功能可能不可用: tree-sitter 
2025-05-08T08:04:19.429Z INFO: 缓存服务初始化完成 
2025-05-08T08:04:19.432Z WARN: 依赖 tree-sitter 不可用: Cannot find module 'tree-sitter'
Require stack:
- /Users/<USER>/.vscode/extensions/ai-coding-assistant.ai-coding-assistant-0.0.1/dist/extension.js
- /Applications/Visual Studio Code.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js 
2025-05-08T08:04:19.434Z WARN: 缺失以下依赖，某些功能可能不可用: tree-sitter 
2025-05-08T08:22:43.023Z INFO: 缓存服务初始化完成 
2025-05-08T08:22:43.029Z WARN: 依赖 tree-sitter 不可用: Cannot find module 'tree-sitter'
Require stack:
- /Users/<USER>/.vscode/extensions/ai-coding-assistant.ai-coding-assistant-0.0.1/dist/extension.js
- /Applications/Visual Studio Code.app/Contents/Resources/app/out/vs/workbench/api/node/extensionHostProcess.js 
2025-05-08T08:22:43.034Z WARN: 缺失以下依赖，某些功能可能不可用: tree-sitter 
