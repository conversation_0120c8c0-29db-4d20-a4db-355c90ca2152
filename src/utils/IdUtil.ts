import FingerprintJS from "@fingerprintjs/fingerprintjs";

//根据当前时间生成唯一ID
const generateUniqueIdByTime = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2);
  return timestamp + random;
};

//获取浏览器客户端指纹ID
const getFingerId = () => {
  let clientId = localStorage.getItem("clientId");
  if (clientId == null) {
    FingerprintJS.load().then(fp => {
      fp.get().then(result => {
        clientId = result.visitorId;
        localStorage.setItem("clientId", clientId);
      });
    });
  }
  return clientId;
};

export { generateUniqueIdByTime, getFingerId };
