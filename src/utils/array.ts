/**
 * @description 判断一个数组（这里简称为母体）中是否包含了另一个由基本数据类型组成的数组（这里简称为子体）中的任意元素，
 * 只要包含其中一个即返回true
 * @param child 子体
 * @param mother 母体
 */
export const isIncludeAnyChildren = (
  child: Array<string | number | unknown>,
  mother: Array<unknown>
): boolean => {
  for (let i = 0; i < child.length; i++) {
    const c = child[i];
    if (mother.includes(c)) {
      return true;
    }
  }
  return false;
};

/**
 * 在原地移动数组中的元素
 *
 * 该函数通过指定的索引位置，将数组中的一个元素从一个位置移动到另一个位置
 * 这个操作是直接在原数组上进行的，不会创建新的数组
 *
 * @param arr 要操作的数组
 * @param fromIndex 元素当前的位置索引
 * @param toIndex 元素需要移动到的目标索引
 * @throws 如果提供的索引超出数组范围，则会抛出错误
 */
export function moveArrayItemInPlace<T>(
  arr: T[],
  fromIndex: number,
  toIndex: number
): T[] {
  // 首先检查索引是否有效
  if (
    fromIndex < 0 ||
    fromIndex >= arr.length ||
    toIndex < 0 ||
    toIndex >= arr.length
  ) {
    throw new Error("Invalid index");
  }

  // 如果 fromIndex 和 toIndex 相同，则不需要做任何事情
  if (fromIndex === toIndex) {
    return arr;
  }

  // 创建一个新的数组以避免修改原始数组
  const newArr = [...arr];
  const [item] = newArr.splice(fromIndex, 1); // 移除指定位置的元素
  newArr.splice(toIndex, 0, item); // 在新位置插入该元素

  return newArr;
}
