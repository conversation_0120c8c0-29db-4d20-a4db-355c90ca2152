import { AxiosError, AxiosRequestConfig, AxiosResponse, Method } from "axios";

/**
 * 请求响应Status枚举
 */
export enum ResultStatus {
  //操作成功
  Success = "0",

  //操作失败 且需要提示
  Failure = "6",

  //已退出当前系统
  HadLogOut = "1",

  //未登录或Token已失效
  NoSystemAuth = "203",

  //无操作权限
  NoOperationAuth = "204"
}

/**
 * http请求类型枚举
 */
export enum HttpRequestMethod {
  Post = "post",
  Get = "get",
  Put = "put",
  Delete = "delete"
}

/**
 * http响应类型枚举
 */
export enum HttpResponseType {
  Json = "json",
  Blob = "blob"
}

export type RequestMethods = Extract<
  Method,
  "get" | "post" | "put" | "delete" | "patch" | "option" | "head"
>;

export interface PureHttpError extends AxiosError {
  isCancelRequest?: boolean;
}

// @ts-ignore
export interface PureHttpResponse extends AxiosResponse {
  config: PureHttpRequestConfig;
}

export interface PureHttpRequestConfig extends AxiosRequestConfig {
  beforeRequestCallback?: (request: PureHttpRequestConfig) => void;
  beforeResponseCallback?: (response: PureHttpResponse) => void;
}
