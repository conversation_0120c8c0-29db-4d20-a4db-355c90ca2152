/**
 * 注册服务名，避免服务端变更造成大面积代码修改
 */
export class ServerNames {
  //服务端请求基础路径
  static readonly baseUrl: string = "/rest";

  //门户服务
  static readonly portalServer: string = "/portal-server";

  //安全事件服务
  static readonly securityEventServer: string = "/security-event";

  //流程服务
  static readonly eamPmServer: string = "/eam-pm";

  //IP地址规划
  static readonly ipPlanServer: string = "/eam-ip-plan";

  //文件服务
  static readonly fileServer: string = "/public-file";

  //通知服务
  static readonly noticeServer: string = "/public-notice";

  //开发平台
  static readonly devplatformService: string = "/devplatform-service";

  //资产管理服务
  static readonly eamCoreServer: string = "/eam-core/zcgl-common";

  //资产测绘
  static readonly eamCollectServer: string = "/eam-collection";

  //前端业务代理服务
  static readonly restProxyServer: string = "/rest-proxy";

  //漏洞扫描服务
  static readonly securityVulServer: string = "/security-vul";

  //IP计划
  static readonly eamIpPlan: string = "/eam-ip-plan";
}
