const signIdExp = /^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$/;

//标识ID 格式校验 示例：business_group
export function validSignId(id: string): boolean {
  return signIdExp.test(id);
}

const pwdExp =
  /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$/;

//密码强度校验
export function validatePwd(pwd: string): boolean {
  if (pwd != null) {
    return pwdExp.test(pwd);
  }
  return false;
}

const ipv4Pattern = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
const ipv6Pattern = /^(?:(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}|(?:(?:(?:[A-F0-9]{1,4}:)+)?::(?:(?:[A-F0-9]{1,4}:)*[A-F0-9]{1,4})?))$/i;

//IP 地址格式校验
export function validIPAddress(ip: string): boolean {
  if (ip != null) {
    return ipv4Pattern.test(ip) || ipv6Pattern.test(ip);
  }
  return false;
}
