export const downloadText = (text, filename) => {
  // 创建一个blob对象，指定文本类型
  const blob = new Blob([text], { type: "text/plain;charset=utf-8;" });
  // 创建一个指向blob的URL
  const url = URL.createObjectURL(blob);
  // 创建一个a标签并模拟点击以触发下载
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", filename); // 设置下载文件的名称
  document.body.appendChild(link);
  link.click();
  // 清理DOM和释放URL对象
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const readTextFile = (
  file: File,
  success: (text: string) => void,
  fail?: (error) => void
) => {
  const reader = new FileReader(); // 创建 FileReader 对象
  // 读取完成时的回调
  reader.onload = e => {
    success(e.target.result as string);
  };
  // 读取错误处理
  reader.onerror = e => {
    console.error("读取错误:", e.target.error);
    fail && fail(e.target.error);
  };
  reader.readAsText(file, "UTF-8"); // 以文本格式读取文件
};
