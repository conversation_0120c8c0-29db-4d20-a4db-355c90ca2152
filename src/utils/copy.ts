export async function copyToClip(text: string) {
  // 检查 navigator.clipboard 是否可用
  if (navigator.clipboard) {
    try {
      await navigator.clipboard.writeText(text);
      return text;
    } catch (error) {
      throw error;
    }
  } else {
    // 创建一个临时的 textarea 元素
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.style.position = "fixed";
    textarea.style.opacity = "0";
    document.body.appendChild(textarea);
    textarea.select();

    try {
      // 尝试使用 document.execCommand 复制文本
      const successful = document.execCommand("copy");
      if (successful) {
        return text;
      } else {
        throw new Error("复制文本失败");
      }
    } catch (error) {
      throw error;
    } finally {
      // 移除临时的 textarea 元素
      document.body.removeChild(textarea);
    }
  }
}

/**
 * 复制html代码可以在word中粘贴（附带字体大小颜色等换行样式）
 *
 * @param htmlCode 返回空代表复制成功,否则复制失败；
 */
export const copyHtmlToStyleText = htmlCode => {
  const div = document.createElement("div");
  div.innerHTML = htmlCode;
  div.style.position = "fixed";
  div.style.opacity = "0";
  div.style.userSelect = "text";
  document.body.appendChild(div);
  try {
    const range = document.createRange(); // 创建一个Range对象
    range.selectNodeContents(div);
    const selection = window.getSelection(); // 获取当前的选择对象
    selection.removeAllRanges(); // 清除当前所有选择
    selection.addRange(range);
    document.execCommand("copy", false, null);
    return null;
  } catch (error) {
    return error;
  } finally {
    // 移除临时的 textarea 元素
    document.body.removeChild(div);
  }
};
