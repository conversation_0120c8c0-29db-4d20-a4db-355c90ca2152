import DOMPurify from "dompurify";

const leftBrackets = "&qwedyhgult@;";
const rightBrackets = "&qwedyhgugt@;";

const purifyConfig = {
  FORBID_TAGS: ["a"], // 禁止标签
  ALLOWED_URI_REGEXP: /^\/rest\/public-file\//
};

//提交转码
const commitTransform = (val: string) => {
  let secStr = DOMPurify.sanitize(val, purifyConfig);
  secStr = secStr.replaceAll("<", leftBrackets);
  secStr = secStr.replaceAll(">", rightBrackets);
  return secStr;
};

//显示渲染转码
const renderTransform = (val: string) => {
  if (val != null) {
    const trans = val
      .replaceAll(leftBrackets, "<")
      .replaceAll(rightBrackets, ">");
    return DOMPurify.sanitize(trans, purifyConfig);
  }
  return "";
};

export { commitTransform, renderTransform };
