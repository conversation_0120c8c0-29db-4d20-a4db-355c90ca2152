const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: VITE_HIDE_HOME !== "true" ? "/welcome" : "/redirectHome",
  meta: {
    icon: "EP-HomeFilled",
    title: "首页",
    rank: 0,
    showLink: VITE_HIDE_HOME !== "true"
  },
  children: [
    {
      path: "/welcome",
      name: "Welcome",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: "首页",
        showLink: VITE_HIDE_HOME !== "true"
      }
    },
    {
      path: "/redirectHome",
      name: "RedirectHome",
      component: () => import("@/views/welcome/redirect_home.vue"),
      meta: {
        title: "重定向到首页",
        showLink: false
      }
    }
  ]
} as RouteConfigsTable;
