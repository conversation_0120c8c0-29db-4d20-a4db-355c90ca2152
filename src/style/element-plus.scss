.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-dropdown-menu {
  padding: 0 !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.is-dark {
  z-index: 9999 !important;
}

/* 重置 el-button 中 icon 的 margin */
.reset-margin [class*="el-icon"] + span {
  margin-left: 2px !important;
}

/* 自定义 popover 的类名 */
.pure-popper {
  padding: 0 !important;
}

/* 自定义 tooltip 的类名 */
.pure-tooltip {
  // 右侧操作面板right-panel类名的z-index为40000，tooltip需要大于它才能显示
  z-index: 41000 !important;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow: 0 0 10px var(--el-color-primary),
    0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}

.pure-dialog {
  .pure-dialog-svg {
    color: var(--el-color-info);
  }

  .el-dialog__headerbtn {
    top: 20px;
    right: 14px;
    width: 24px;
    height: 24px;
  }
}

/* 全局覆盖element-plus的el-dialog、el-drawer、el-message-box、el-notification组件右上角关闭图标的样式，表现更鲜明 */
.el-dialog__headerbtn,
.el-message-box__headerbtn {
  &:hover {
    .el-dialog__close {
      color: var(--el-color-info) !important;
    }
  }
}

.el-icon {
  &.el-dialog__close,
  &.el-drawer__close,
  &.el-message-box__close,
  &.el-notification__closeBtn {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    outline: none;
    transition: background-color 0.2s, color 0.2s;

    &:hover {
      color: rgb(0 0 0 / 88%) !important;
      text-decoration: none;
      background-color: rgb(0 0 0 / 6%);

      .pure-dialog-svg {
        color: rgb(0 0 0 / 88%) !important;
      }
    }
  }
}

/* 克隆并自定义 ElMessage 样式，不会影响 ElMessage 原本样式，在 src/utils/message.ts 中调用自定义样式 ElMessage 方法即可，暗黑模式在 src/style/dark.scss 文件进行了适配 */
.pure-message {
  padding: 10px 13px !important;
  background: #fff !important;
  border-width: 0 !important;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
  0 9px 28px 8px #0000000d !important;

  &.el-message.is-closable .el-message__content {
    padding-right: 17px !important;
  }

  & .el-message__content {
    color: #000000d9 !important;
    pointer-events: all !important;
    background-image: initial !important;
  }

  & .el-message__icon {
    margin-right: 8px !important;
  }

  & .el-message__closeBtn {
    right: 9px !important;
    border-radius: 4px;
    outline: none;
    transition: background-color 0.2s, color 0.2s;

    &:hover {
      background-color: rgb(0 0 0 / 6%);
    }
  }
}

/* 自定义菜单搜索样式 */
.pure-search-dialog {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding-top: 12px;
    padding-bottom: 0;
  }

  .el-input__inner {
    font-size: 1.2em;
  }

  .el-dialog__footer {
    padding-bottom: 10px;
    box-shadow: 0 -1px 0 0 #e0e3e8, 0 -3px 6px 0 rgb(69 98 155 / 12%);
  }
}

.el-popper {
  border-radius: var(--el-border-radius-base) !important;
}

.el-menu--popup {
  border-radius: var(--el-border-radius-base) !important;
}

.el-check-tag {
  color: var(--el-text-color-primary) !important;
  font-size: var(--el-font-size-small) !important;
}

.el-segmented--small {
  font-size: var(--el-font-size-small) !important;
}

.el-timeline-item__timestamp {
  color: var(--el-color-primary) !important;
}

.el-check-tag.el-check-tag--primary.is-checked {
  background-color: var(--el-color-primary) !important;
  color: #fff !important;
}

/**
 * avue-crud 弹框关闭按钮样式覆盖
 */
.avue-crud__dialog {
  .el-dialog__headerbtn {
    top: 4px !important;
  }
}

.avue-dialog {
  border-radius: var(--el-dialog-border-radius) !important;
}

.avue-dialog__footer {
  margin-bottom: var(--el-dialog-border-radius) !important;
  padding-bottom: 5px !important;
}

.avue-crud__dialog .el-dialog__body {
  margin-bottom: var(--el-dialog-border-radius) !important;
}

.el-button.el-button--primary.el-button--small.is-text.avue-crud__delBtn {
  color: #ff4949;
}

.el-divider--horizontal {
  margin: 6px 0 !important;
}
