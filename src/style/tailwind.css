@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .flex-c {
    @apply flex justify-center items-center;
  }

  .flex-sc {
    @apply flex justify-start items-center;
  }

  .flex-ac {
    @apply flex justify-around items-center;
  }

  .flex-bc {
    @apply flex justify-between items-center;
  }

  .bg-dark-color {
    @apply bg-[#141414];
  }

  .navbar-bg-hover {
    @apply dark:text-white dark:hover:!bg-[#242424];
  }

  /*把EL-Tree当作选项列表时的内容样式*/
  .tree-list-content {
    @apply w-full flex justify-between space-x-2 pr-5;
  }

  /*把EL-Tree当作选项列表时的左侧图标和label的样式*/
  .tree-node-left {
    @apply flex-c justify-items-start space-x-0.5;
  }
}
