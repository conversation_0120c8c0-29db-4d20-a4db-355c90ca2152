@import "./transition";
@import "./element-plus";
@import "./sidebar";
@import "./dark";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;
  --el-border-radius-base: 6px !important;
  --el-border-radius-small: 3px !important;
  --el-text-color-primary: #000000 !important;
  --el-text-color-regular: #000000 !important;
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

.enable-switch-color {
  --el-switch-on-color: #1ab901 !important;
  --el-switch-off-color: #f12815 !important;
}
