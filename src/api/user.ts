import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";
import { PureHttpRequestConfig } from "@/utils/http/types";
import { VerifyTypeCodeResult } from "@/views/login/utils/types";

export type UserSessionInfo = {
  userId: string;
  userName: string;
  realName: string;
  userAvatar: string;
  zoneId: string;
  zoneType: number;
  deptId: string;
  enableSwitch: string;
  projectCode: string;
  pwdInvalid: boolean;
  expireTime: number;
  roleIds: Array<string>;
  maxRoleLevel: number;
  tenantIds: Array<string>;
};

const formRequestConfig = {
  headers: {
    "Content-Type": "application/x-www-form-urlencoded"
  }
} as PureHttpRequestConfig;

/** 登录 */
const getLogin = (data?: object) => {
  return http.post<any, RestResult<string>>(
    `${ServerNames.portalServer}/login`,
    {
      data
    },
    formRequestConfig
  );
};

/** 刷新token */
const refreshTokenApi = (data?: any) => {
  return http.request<RestResult<string>>(
    "get",
    `${ServerNames.portalServer}/login/token/refresh`,
    { data },
    {
      headers: {
        Authorization: data.refreshToken
      }
    }
  );
};

/** 验证token */
const verifyToken = (data?: object) =>
  http.post<any, RestResult<string>>(
    `${ServerNames.portalServer}/login/token/verify`,
    { data },
    formRequestConfig
  );

/** 获取验证码 **/
const getVerifyCode = (backColor: string) =>
  http.get<any, RestResult<VerifyTypeCodeResult>>(
    `${ServerNames.portalServer}/login/verifycode?id=5&backColor=${backColor}`
  );

export { getLogin, refreshTokenApi, getVerifyCode, verifyToken };
