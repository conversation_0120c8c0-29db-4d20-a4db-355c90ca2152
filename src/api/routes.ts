import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

//路由访问日志请求
export type AccessLogRequest = {
  resourceId: string;
  resourceName: string;
  resourcePath: string;
};

//获取路由数据
const getAsyncRoutes = () => {
  return http.request<RestResult<Array<any>>>(
    "post",
    `${ServerNames.portalServer}/framework/sysmanage/resources/get-current-user-menu`
  );
};

//记录路由访问日志
const logAccessInfo = async (log: AccessLogRequest) =>
  http.postJson<RestResult<string>>(
    `${ServerNames.portalServer}/framework/log/access/add`,
    log
  );

export { getAsyncRoutes, logAccessInfo };
