import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import ElementPlus from "element-plus";
import { getServerConfig } from "./config";
import { createApp, Directive } from "vue";
import { MotionPlugin } from "@vueuse/motion";
import { useEcharts } from "@/plugins/echarts";
import { injectResponsiveStorage } from "@/utils/responsive";
import Avue from "@smallwei/avue";
import CronElementPlusPlugin from "@vue-js-cron/element-plus";
import ItsmCommon, {
  setGlobalTableColumnStorge
} from "@/components/ItsmCommon";
// 大屏设计器集成
import ddls from "@/views/modules/ddls";
// 导入扫描
import "@/views/modules/ddls/scan";
// 自定义指令
import * as directives from "@/directives";
// 全局注册`@iconify/vue`图标库
import {
  FontIcon,
  IconifyIconOffline,
  IconifyIconOnline
} from "./components/ReIcon";
// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth";

// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
import "element-plus/dist/index.css";
import "@smallwei/avue/lib/index.css";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "@vue-js-cron/element-plus/dist/element-plus.css";
// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";
import { queryTableSet, saveTableSet } from "@/views/system/columnmanage/api";
// 设置查询和保存2个api，解耦im-table组件和字段管理的强关联关系
setGlobalTableColumnStorge(queryTableSet, saveTableSet);

const app = createApp(App);

Object.keys(directives).forEach(key => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);
app.component("Auth", Auth);

getServerConfig(app).then(async config => {
  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  setupStore(app);
  app
    .use(MotionPlugin)
    .use(ElementPlus, { size: "small" })
    .use(Avue, { size: "small" })
    .use(ItsmCommon)
    .use(ddls)
    .use(useEcharts)
    .use(CronElementPlusPlugin);
  app.mount("#app");
});
