<template>
  <div>
    <el-input v-model="keyWord" placeholder="请输入关键字" :class="inputClassName" clearable @input="updateModelValue">
      <template #prepend>
        <el-select v-model="columnVal" placeholder="请选择" :style="{width: columnSelectWidth+'px'}"
                   @change="updateColumnVal">
          <el-option v-for="item in columnOptions" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </template>
      <template #append>
        <el-checkbox v-model="fuzzy" label="模糊" @change="updateFuzzyEnable"/>
      </template>
    </el-input>
    <el-button type="primary" class="ml-3" @click="search">查询</el-button>
    <el-button type="primary" @click="reset">重置</el-button>
  </div>
</template>

<script lang="ts" setup>
import {reactive, toRefs, watch} from 'vue';
import {ColumnSelectOption} from "@/components/Search/type";

// 组件属性
const props = defineProps({
  modelValue: {
    type: String,
  },
  columnOptions: {
    type: Array<ColumnSelectOption>,
    default: () => []
  },
  columnVal: {
    type: String,
    default: ''
  },
  columnSelectWidth: {
    type: Number,
    default: 120
  },
  fuzzyEnable: {
    type: Boolean,
    default: true
  },
  inputClassName: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'update:fuzzyEnable', 'update:columnVal', 'reset', 'search']);

// 数据对象
const state = reactive({
  keyWord: props.modelValue,
  columnVal: props.columnVal,
  fuzzy: props.fuzzyEnable,
});

const {keyWord, columnVal, fuzzy} = toRefs(state);

// 监听组件属性
watch(() => props.modelValue, (newValue) => {
  state.keyWord = newValue;
});

watch(() => props.columnVal, (newValue) => {
  state.columnVal = newValue;
});

watch(() => props.fuzzyEnable, (newValue) => {
  state.fuzzy = newValue;
});

// 更新事件
const updateModelValue = () => {
  emit('update:modelValue', keyWord.value);
};

const updateColumnVal = () => {
  emit('update:columnVal', columnVal.value);
};

const updateFuzzyEnable = () => {
  emit('update:fuzzyEnable', fuzzy.value);
};

const search = () => {
  emit('search');
};

const reset = () => {
  emit('reset');
};
</script>
