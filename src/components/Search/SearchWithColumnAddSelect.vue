<template>
  <div>
    <!-- <el-input v-model="keyWord" placeholder="请输入关键字" :class="inputClassName" clearable @input="updateModelValue">
      <template #prepend>
        <el-select v-model="columnVal" placeholder="请选择" :style="{ width: columnSelectWidth + 'px' }"
          @change="updateColumnVal">
          <el-option v-for="item in columnOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </template>
<template #append>
        <el-checkbox v-model="fuzzy" label="模糊" @change="updateFuzzyEnable" />
      </template>
</el-input> -->
    <el-autocomplete
      :popper-class="
        searchType == 'select' ? 'viewPop' : 'el-autocomplete__popper_unPop'
      "
      @select="handleSelect"
      :fetch-suggestions="querySearch[searchType]"
      :activated="activated"
      v-model="keyWord"
      :placeholder="searchType == 'select' ? '请选择关键字' : '请输入关键字'"
      :class="inputClassName"
      clearable
      @input="updateModelValue"
    >
      <template #prepend>
        <el-select
          v-model="columnVal"
          placeholder="请选择"
          :style="{ width: columnSelectWidth + 'px' }"
          @change="updateColumnVal"
        >
          <el-option
            v-for="item in columnOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>
      <template #append>
        <el-checkbox
          :disabled="searchType == 'select'"
          v-model="fuzzy"
          label="模糊"
          @change="updateFuzzyEnable"
        />
      </template>
    </el-autocomplete>

    <el-button type="primary" class="ml-3" @click="search">查询</el-button>
    <el-button type="primary" @click="reset">重置</el-button>
  </div>
</template>

<script lang="ts" setup>
import { reactive, toRefs, watch, ref } from "vue";
import { ColumnSelectOption } from "@/components/Search/type";
import { nextTick } from "process";

// 组件属性
const props = defineProps({
  modelValue: {
    type: String
  },
  columnOptions: {
    type: Array<ColumnSelectOption>,
    default: () => []
  },
  columnVal: {
    type: String,
    default: ""
  },
  columnSelectWidth: {
    type: Number,
    default: 120
  },
  fuzzyEnable: {
    type: Boolean,
    default: true
  },
  inputClassName: {
    type: String,
    default: ""
  }
});

const activated = ref(false);
const searchType = ref("input");
const querySearch = {
  input: (queryString: string, cb: any) => {
    console.log(queryString);
    cb([]);
  },
  select: (queryString: string, cb: any) => {
    console.log(queryString);
    cb([
      {
        id: "5",
        value: "危急"
      },
      {
        id: "4",
        value: "高危"
      },
      {
        id: "3",
        value: "中危"
      },
      {
        id: "2",
        value: "低危"
      },
      {
        id: "1",
        value: "信息"
      }
    ]);
  }
};

const handleSelect = (item: Record<string, any>) => {
  console.log(item);
  emit("update:modelValue", item.value);
};

// 定义事件
const emit = defineEmits([
  "update:modelValue",
  "update:fuzzyEnable",
  "update:columnVal",
  "reset",
  "search"
]);

// 数据对象
const state = reactive({
  keyWord: props.modelValue,
  columnVal: props.columnVal,
  fuzzy: props.fuzzyEnable
});

const { keyWord, columnVal, fuzzy } = toRefs(state);

// 监听组件属性
watch(
  () => props.modelValue,
  newValue => {
    state.keyWord = newValue;
  }
);

watch(
  () => props.columnVal,
  newValue => {
    state.columnVal = newValue;
    // console.log(newValue);
    props.columnOptions.forEach(item => {
      // console.log(newValue , item);
      if (item.value == newValue && item["type"] == "Select") {
        console.log(item.value);
        searchType.value = "select";
        activated.value = true;
      } else {
        searchType.value = "input";
        activated.value = false;
        nextTick(() => {
          // console.log(activated);
          activated.value = false;
        });
      }
    });
  }
);

watch(
  () => props.fuzzyEnable,
  newValue => {
    state.fuzzy = newValue;
  }
);

// 更新事件
const updateModelValue = () => {
  nextTick(() => {
    emit("update:modelValue", keyWord.value);
  });
};

const updateColumnVal = () => {
  emit("update:columnVal", columnVal.value);
};

const updateFuzzyEnable = () => {
  emit("update:fuzzyEnable", fuzzy.value);
};

const search = () => {
  emit("search");
};

const reset = () => {
  emit("reset");
};
</script>
<style lang="scss">
.el-autocomplete__popper_viewPop {
  display: none !important;
}
.el-autocomplete__popper_unPop {
  display: none !important;
}
</style>
