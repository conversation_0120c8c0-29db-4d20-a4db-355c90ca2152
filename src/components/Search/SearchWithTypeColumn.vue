<template>
  <div>
    <div style="float:left;" class="leftDiv">
      <el-select v-model="searchCondition.field" placeholder="请选择" :style="{width: columnSelectWidth+'px',border:'0px'}" @change="changeSelection">
        <el-option v-for="item in columnOptions" :key="item.field" :label="item.name" :value="item.field"/>
      </el-select>
    </div>
    <div style="float:left;width: 400px;" class="rightDiv">
      <el-input v-if="searchCondition.type=='Input'" v-model="searchCondition.value" placeholder="请输入关键字" clearable>
<!--        <template #prepend>-->
<!--          <el-select v-model="searchCondition.field" placeholder="请选择" :style="{width: columnSelectWidth+'px'}"-->
<!--                     @change="updateColumnVal">-->
<!--            <el-option v-for="item in columnOptions" :key="item.value" :label="item.label" :value="item.value"/>-->
<!--          </el-select>-->
<!--        </template>-->
        <template #append>
          <el-checkbox v-model="searchCondition.fuzzy" label="模糊"/>
        </template>
      </el-input>
      <el-select v-if="searchCondition.type=='Select'" v-model="searchCondition.value">
        <el-option v-for="item in searchCondition.options" :label="item.label" :value="item.value" :key="item.value"></el-option>
      </el-select>
      <el-date-picker v-if="searchCondition.type=='DatePicker'" clearable v-model="searchCondition.value" :type="searchCondition.props.type" range-separator="到" value-format="YYYY-MM-DD HH:mm:ss"
                      format="YYYY-MM-DD HH:mm:ss"
                      start-placeholder="开始时间" end-placeholder="结束时间" />
    </div>
    <div style="float:left;">
    <el-button type="primary" class="ml-3" @click="search">查询</el-button>
    <el-button type="primary" @click="reset">重置</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {ColumnSelectOption} from "@/components/Search/type";

// 组件属性
const props = defineProps({
  searchCondition:{
    type: Object,
    default:{
      field:"",
      fuzzy: true,
      type: "Input",
      props:{}
    }
  },
  columnOptions: {
    type: Array<ColumnSelectOption>,
    default: () => []
  },
  columnSelectWidth: {
    type: Number,
    default: 120
  },
});


const changeSelection = (value) =>{
  for(let i =0; i<props.columnOptions.length;i++){
    if(props.columnOptions[i].field==value){
      let conditions = {
        field: props.columnOptions[i].field,
        fuzzy: true,
        props: props.columnOptions[i].component.props,
        type: props.columnOptions[i].component.type,
        options: props.columnOptions[i].component.options,
        value: null
      };
      emit('update:searchCondition',conditions);
    }
  }
}
// 定义事件
const emit = defineEmits(['update:searchCondition', 'reset', 'search']);

const search = () => {
  emit('search');
};

const reset = () => {
  emit('reset');
};
</script>
<style scoped>
.leftDiv{
  :deep(.el-select__wrapper){
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
    background-color:#F5F7FA;
  }
}
.rightDiv{
  :deep(.el-select__wrapper){
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
  :deep(.el-input__wrapper){
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
  }
}
</style>
