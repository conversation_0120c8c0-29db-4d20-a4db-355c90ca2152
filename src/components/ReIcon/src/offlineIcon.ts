import { addIcon } from "@iconify/vue/dist/offline";
import * as epSystemIcons from "./icons/element/system.d";
import * as epArrowIcons from "./icons/element/arrow.d";
import * as epDocumentIcons from "./icons/element/document.d";
import * as epFoodIcons from "./icons/element/food.d";
import * as epItemsIcons from "./icons/element/items.d";
import * as epMediaIcons from "./icons/element/media.d";
import * as epOtherIcons from "./icons/element/other.d";
import * as epTrafficIcons from "./icons/element/traffic.d";
import * as epWeatherIcons from "./icons/element/weather.d";
import * as riBuildIcons from "./icons/remix/buildings.d";
import * as riBusinessIcons from "./icons/remix/business.d";
import * as riCommunicationIcons from "./icons/remix/communication.d";
import * as riDesignIcons from "./icons/remix/design.d";
import * as riDevelopmentIcons from "./icons/remix/development.d";
import * as riDeviceIcons from "./icons/remix/device.d";
import * as riDocumentIcons from "./icons/remix/document.d";
import * as riEditorIcons from "./icons/remix/editor.d";
import * as riFinanceIcons from "./icons/remix/finance.d";
import * as riHealthIcons from "./icons/remix/health.d";
import * as riLogosIcons from "./icons/remix/logos.d";
import * as riMapIcons from "./icons/remix/map.d";
import * as riMediaIcons from "./icons/remix/media.d";
import * as riOthersIcons from "./icons/remix/others.d";
import * as riSystemIcons from "./icons/remix/system.d";
import * as riUserIcons from "./icons/remix/user.d";
import * as riWeatherIcons from "./icons/remix/weather.d";
import { clone } from "@pureadmin/utils";

export interface IconSet {
  //图标集前缀
  prefix: string;

  //图标集名称
  setName: string;

  //图标分组
  groups: Array<IconGroup>;
}

export interface IconGroup {
  //图标分组
  groupCode: string;

  //图标分组名称
  groupName: string;

  //图标名称集合
  iconNames: Array<string>;
}

// 每批添加的图标数量
const batchSize = 50;

export const epPrefix = "EP-";
export const riPrefix = "RI-";

const iconSets: Array<IconSet> = [];

//根据前缀查询图标集
export function getSetByPrefix(prefix: string): IconSet {
  const pSetArray = iconSets.filter(set => set.prefix === prefix);
  if (pSetArray.length > 0) {
    return pSetArray[0];
  }
  return null;
}

//查询指定图标集内的某个分组
export function getSetGroup(setPrefix: string, groupCode: string): IconGroup {
  const iconSet = getSetByPrefix(setPrefix);
  const groupArray = iconSet.groups.filter(
    group => group.groupCode === groupCode
  );
  if (groupArray.length > 0) {
    return groupArray[0];
  }
  return null;
}

// 将图标分批添加到本地缓存中
async function addIcons(icons: Object, prefix: string, group: IconGroup) {
  const iconNames = Object.keys(icons);

  //添加数据集数据
  group.iconNames = clone(iconNames);
  const iconSet = getSetByPrefix(prefix);
  if (iconSet) {
    iconSet.groups.push(group);
  } else {
    const iconSet: IconSet = {
      prefix: prefix,
      setName: prefix === epPrefix ? "ElementPlus" : "Remix",
      groups: []
    };
    iconSet.groups.push(group);
    iconSets.push(iconSet);
  }

  while (iconNames.length > 0) {
    const batch = iconNames.splice(0, batchSize);

    //添加缓存
    const promises = batch.map(iconName =>
      addIcon(prefix + iconName, icons[iconName])
    );
    await Promise.all(promises);
  }
}

//注册ElementPlus图标
addIcons(epSystemIcons, epPrefix, {
  groupCode: "system",
  groupName: "系统",
  iconNames: []
});
addIcons(epArrowIcons, epPrefix, {
  groupCode: "arrow",
  groupName: "箭头",
  iconNames: []
});
addIcons(epDocumentIcons, epPrefix, {
  groupCode: "document",
  groupName: "文档",
  iconNames: []
});
addIcons(epFoodIcons, epPrefix, {
  groupCode: "food",
  groupName: "食品",
  iconNames: []
});
addIcons(epItemsIcons, epPrefix, {
  groupCode: "items",
  groupName: "项目",
  iconNames: []
});
addIcons(epMediaIcons, epPrefix, {
  groupCode: "media",
  groupName: "媒体",
  iconNames: []
});
addIcons(epTrafficIcons, epPrefix, {
  groupCode: "traffic",
  groupName: "交通",
  iconNames: []
});
addIcons(epWeatherIcons, epPrefix, {
  groupCode: "weather",
  groupName: "天气",
  iconNames: []
});
addIcons(epOtherIcons, epPrefix, {
  groupCode: "other",
  groupName: "其他",
  iconNames: []
});

//注册Remix图标
addIcons(riBuildIcons, riPrefix, {
  groupCode: "build",
  groupName: "建筑",
  iconNames: []
});
addIcons(riBusinessIcons, riPrefix, {
  groupCode: "business",
  groupName: "商务",
  iconNames: []
});
addIcons(riDesignIcons, riPrefix, {
  groupCode: "design",
  groupName: "设计",
  iconNames: []
});
addIcons(riCommunicationIcons, riPrefix, {
  groupCode: "communication",
  groupName: "通信",
  iconNames: []
});
addIcons(riDevelopmentIcons, riPrefix, {
  groupCode: "development",
  groupName: "研发",
  iconNames: []
});
addIcons(riDeviceIcons, riPrefix, {
  groupCode: "device",
  groupName: "设备",
  iconNames: []
});
addIcons(riDocumentIcons, riPrefix, {
  groupCode: "document",
  groupName: "文档",
  iconNames: []
});
addIcons(riEditorIcons, riPrefix, {
  groupCode: "editor",
  groupName: "编辑",
  iconNames: []
});
addIcons(riFinanceIcons, riPrefix, {
  groupCode: "finance",
  groupName: "金融",
  iconNames: []
});
addIcons(riHealthIcons, riPrefix, {
  groupCode: "health",
  groupName: "健康",
  iconNames: []
});
addIcons(riLogosIcons, riPrefix, {
  groupCode: "logo",
  groupName: "商标",
  iconNames: []
});
addIcons(riMapIcons, riPrefix, {
  groupCode: "map",
  groupName: "地图",
  iconNames: []
});
addIcons(riMediaIcons, riPrefix, {
  groupCode: "media",
  groupName: "媒体",
  iconNames: []
});
addIcons(riSystemIcons, riPrefix, {
  groupCode: "system",
  groupName: "系统",
  iconNames: []
});
addIcons(riUserIcons, riPrefix, {
  groupCode: "user",
  groupName: "用户",
  iconNames: []
});
addIcons(riWeatherIcons, riPrefix, {
  groupCode: "weather",
  groupName: "天气",
  iconNames: []
});
addIcons(riOthersIcons, riPrefix, {
  groupCode: "other",
  groupName: "其他",
  iconNames: []
});

//导入图标集时使用 用于将图标名称转换为 export 导入语句
export function hyphenToCamelCase(str: string): string {
  return str
    .replace(/-([a-z])/g, function (match, p1) {
      return p1.toUpperCase();
    })
    .replace(/-\d+/g, function (match) {
      return match.replace(/-/g, "");
    })
    .replace(/^[a-z]/, function (match) {
      return match.toUpperCase();
    });
}

export default { iconSets };
