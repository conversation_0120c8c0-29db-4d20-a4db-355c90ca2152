<template>
  <el-drawer
    v-model="drawerVisible"
    size="42%"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3"> 选择图标 </span>
        </template>
      </el-page-header>
    </template>
    <div class="flex-sc">
      <span class="text-xs">图标集：</span>
      <el-select
        v-model="queryForm.selSet"
        @change="setChangeHandler"
        style="width: 110px"
      >
        <el-option
          v-for="s in offlineIcon.iconSets"
          :key="s.prefix"
          :value="s.prefix"
          :label="s.setName"
        />
      </el-select>
      <span class="text-xs pl-3">分组：</span>
      <el-select
        v-model="queryForm.selGroup"
        filterable
        style="width: 110px"
        @change="groupChangeHandler"
      >
        <el-option
          v-for="g in groupData"
          :key="g.groupCode"
          :value="g.groupCode"
          :label="g.groupName"
        />
      </el-select>
    </div>
    <el-space
      wrap
      :size="15"
      class="mt-5"
      alignment="center"
      :fill="true"
      :fill-ratio="10"
    >
      <el-tooltip
        effect="dark"
        placement="top"
        v-for="(name, index) in iconNames"
        :show-after="500"
        :hide-after="500"
        :key="index"
        :content="queryForm.selSet + name"
      >
        <el-button
          size="large"
          :key="index"
          @click="selectIconHandler(queryForm.selSet + name)"
        >
          <IconifyIconOffline :icon="queryForm.selSet + name" width="18" />
        </el-button>
      </el-tooltip>
    </el-space>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, toRefs, watch } from "vue";
import offlineIcon, {
  epPrefix,
  getSetByPrefix,
  getSetGroup
} from "@/components/ReIcon/src/offlineIcon";

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  }
});

//定义事件
const emit = defineEmits(["update:visible", "select"]);

//定义Data
const state = reactive({
  drawerVisible: false,
  queryForm: {
    selSet: epPrefix,
    selGroup: "system"
  },
  groupData: getSetByPrefix(epPrefix).groups,
  iconNames: getSetGroup(epPrefix, "system").iconNames
});
const { drawerVisible, queryForm, groupData, iconNames } = toRefs(state);

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
});

//图标集选择改变处理器
const setChangeHandler = (prefix: string) => {
  state.groupData = getSetByPrefix(prefix).groups;
  state.queryForm.selGroup = null;
  state.iconNames = [];
};

//分组改变处理器
const groupChangeHandler = (code: string) => {
  const group = getSetGroup(state.queryForm.selSet, code);
  state.iconNames = group.iconNames;
};

//图标选择事件处理器
const selectIconHandler = (iconName: string) => {
  emit("select", iconName);
  cancel();
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
