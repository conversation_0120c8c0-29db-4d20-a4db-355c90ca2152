// import chinaJson from "echarts/map/json/china.json";
import chinaJson from "./china.json";
import * as echarts from "echarts";
import worldJson from "./world.json";

// register china
echarts.registerMap("china", chinaJson as any);
// register world
echarts.registerMap("world", worldJson as any);
const provincesJsonMap: any = {};

/***
 * 注册所有省份边界
 */
const registerAllProvinceNamesMap = () => {
  const chinaJsonClone = JSON.parse(JSON.stringify(chinaJson));
  chinaJsonClone.features.forEach(function (item) {
    const provinceName = item.properties.name;
    const provinceJsonClone = JSON.parse(JSON.stringify(chinaJson));
    provinceJsonClone.features = [item];
    echarts.registerMap(provinceName, provinceJsonClone);
    provincesJsonMap[provinceName] = provinceJsonClone;
  });
};

registerAllProvinceNamesMap();
export { provincesJsonMap };
