<template>
  <div class="map-component">
    <div ref="mapEl" class="map-div" />
    <slot />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  onActivated,
  onBeforeUnmount,
  onDeactivated,
  onMounted,
  ref,
  toRefs,
  watch
} from "vue";
import { debounce } from "lodash";
import * as echarts from "echarts";
import { EChartsInitOpts, EChartsType } from "echarts";
import options from "./mapOptions";
import "./mapJson";
const emits = defineEmits(["update", "chart-click", "chart-dblclick"]);

const mapOptions = JSON.parse(JSON.stringify(options));
const props = defineProps({
  mapName: {
    type: String,
    default: "china"
  },
  showLabel: {
    type: Boolean,
    default: true
  },
  geoConfig: Object,
  showTooltip: Boolean,
  tooltip: Object,
  serieData: Array,
  lineData: Array,
  visualMap: Object,
  scatterData: Array,
  scatterSymbol: [String, Function]
});
const serieData = computed(() => props.serieData);
const lineData = computed(() => props.lineData);
const scatterData = computed(() => props.scatterData);
const { showTooltip, showLabel, mapName, visualMap, scatterSymbol, geoConfig } =
  toRefs(props);

const mapEl = ref(null);
const chart = ref<EChartsType>(null);

const reloadOption = (flag = false) => {
  chart.value?.setOption(mapOptions, flag);
  emits("update", mapOptions);
};

const setOption = (option, flag) => {
  chart.value?.setOption(option, flag);
};

const initChart = () => {
  const opt: EChartsInitOpts = {
    width: "auto",
    height: "auto"
    // renderer: "svg"
  };
  chart.value = echarts.init(mapEl.value, null, opt);
  chart.value.on("click", params => {
    emits("chart-click", params);
  });
  chart.value.on("dblclick", params => {
    emits("chart-dblclick", params);
  });
  try {
    mapOptions.series[0].map = mapName.value;
    mapOptions.geo.map = mapName.value;
    if (geoConfig.value) {
      Object.assign(mapOptions.geo, { ...geoConfig.value });
    }
    if (visualMap.value) {
      mapOptions.visualMap = visualMap.value;
    }
    if (serieData.value) {
      mapOptions.series[0].data = serieData.value;
    }
    if (lineData.value) {
      mapOptions.series[2].data = lineData.value;
    }
    if (scatterData.value) {
      mapOptions.series[1].data = scatterData.value;
    }
    if (scatterSymbol.value) {
      mapOptions.series[1].symbol = scatterSymbol.value;
    }
    if (showTooltip.value) {
      mapOptions.tooltip = props.tooltip || {
        trigger: "item",
        formatter: function (params) {
          return `${params.name}: ${params.value || 0}`;
        }
      };
    }
  } catch (e) {
    console.trace(e);
  }
  reloadCharOption();
};

const resizeOnDomChange = () => {
  const { width } = mapEl.value.getBoundingClientRect();
  if (width == 0) {
    return;
  }
  if (chart.value) {
    chart.value.resize();
    reloadCharOption(true);
  } else {
    initChart();
  }
};
const handleResize = debounce(resizeOnDomChange, 200);
const reloadCharOption = debounce(reloadOption, 50);
const ro = new ResizeObserver(handleResize);

const setChartSeries = series => {
  mapOptions.series = series;
  reloadCharOption(true);
};

onMounted(async () => {
  // watch resize
  ro.observe(mapEl.value);
  window.addEventListener("resize", handleResize);
  resizeOnDomChange();
});

onBeforeUnmount(() => {
  ro.disconnect();
  window.removeEventListener("resize", handleResize);
  try {
    chart.value.dispose();
  } catch (e) {}
});

const closeEffect = () => {
  try {
    if (mapOptions.series[1].rippleEffect) {
      mapOptions.series[1].rippleEffect.brushType = "none";
    }
    // 关闭连线的动画
    mapOptions.series[2].effect.show = false;
    chart.value?.setOption(mapOptions, true);
  } catch (e) {}
};

const openEffect = () => {
  try {
    if (mapOptions.series[1].rippleEffect) {
      mapOptions.series[1].rippleEffect.brushType = "stroke";
    }
    // 关闭连线的动画
    mapOptions.series[2].effect.show = true;
    chart.value?.setOption(mapOptions, true);
  } catch (e) {}
};

watch(mapName, val => {
  mapOptions.series[0].map = val;
  mapOptions.geo.map = val;
  if (geoConfig.value) {
    Object.assign(mapOptions.geo, { ...geoConfig.value });
  }
  reloadCharOption(true);
});

watch(
  showLabel,
  val => {
    try {
      mapOptions.geo.label.normal.show = val;
      reloadCharOption(true);
    } catch (e) {
      console.trace(e);
    }
  },
  {
    immediate: true
  }
);

watch(
  serieData,
  val => {
    mapOptions.series[0].data = val;
    reloadCharOption(true);
  },
  { deep: true }
);

watch(
  lineData,
  val => {
    mapOptions.series[2].data = val;
    reloadCharOption(true);
  },
  { deep: true }
);

watch(
  scatterData,
  val => {
    mapOptions.series[1].data = val;
    reloadCharOption(true);
  },
  { deep: true }
);

onActivated(() => {
  openEffect();
});

onDeactivated(() => {
  closeEffect();
});

defineExpose({
  setOption,
  setChartSeries,
  closeEffect,
  openEffect
});
</script>

<style lang="scss" scoped>
.map-component {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .map-div {
    width: 100%;
    height: 100%;
  }
}
</style>
