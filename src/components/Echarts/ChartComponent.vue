<template>
  <div
    class="chart-component relative overflow-hidden w-full h-full"
    :style="chartWrapStyle"
  >
    <div ref="chartEl" class="chart-div absolute w-full h-full" />
    <slot />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  onBeforeUnmount,
  onMounted,
  PropType,
  ref,
  watch
} from "vue";
import { debounce } from "lodash";
import type { EChartsInitOpts, EChartsType } from "echarts/core";
import * as echarts from "echarts/core";
import "./mapJson";
import "echarts-wordcloud";
import type { EChartsOption } from "echarts";

const emits = defineEmits(["resize", "chart-click", "chart-dblclick"]);
type RendererType = "svg" | "canvas";
const props = defineProps({
  // 是否自适应
  resizeable: {
    type: Boolean,
    default: true
  },
  height: [Number, String],
  // 图表主题
  theme: String,
  // 渲染方式默认svg
  renderer: {
    type: String as PropType<RendererType>,
    default: "svg"
  },
  initOpts: {
    type: String as PropType<EChartsInitOpts>
  },
  // 图表option数据
  option: Object as PropType<EChartsOption>
});

const chartWrapStyle = computed((): CSSProperties => {
  let height = null;
  if (typeof props.height == "string") {
    height = props.height;
  } else if (props.height > 0) {
    height = `${props.height}px`;
  }
  if (height) {
    return { height };
  }
  return {};
});

const chartOption = computed(() => props.option);

const chartEl = ref(null);
const chart: {
  value: EChartsType;
} = {
  value: null
};

const initChart = () => {
  const opt: EChartsInitOpts = props.initOpts || {
    // width: "auto",
    // height: "auto",
    renderer: props.renderer
  };
  if (chart.value) {
    chart.value.dispose();
  }
  chart.value = echarts.init(chartEl.value, props.theme, opt);
  if (chartOption.value) {
    chart.value.setOption(chartOption.value, true);
  }
  chart.value.on("click", params => {
    emits("chart-click", params);
  });
  chart.value.on("dblclick", params => {
    emits("chart-dblclick", params);
  });
};

const resizeOnDomChange = () => {
  const { width, height } = chartEl.value.getBoundingClientRect();
  if (width == 0) return;
  if (chart.value) {
    if (chartOption.value) {
      chart.value.setOption(chartOption.value);
      chart.value.resize();
    }
  } else {
    initChart();
  }
  emits("resize", width, height);
};
const handleResize = debounce(resizeOnDomChange, 200);
const ro = new ResizeObserver(handleResize);

onMounted(async () => {
  if (props.resizeable) {
    ro.observe(chartEl.value);
    window.addEventListener("resize", handleResize);
  }
  // resizeOnDomChange();
});

onBeforeUnmount(() => {
  ro.disconnect();
  window.removeEventListener("resize", handleResize);
  try {
    chart.value.dispose();
  } catch (e) {}
});

const updateOption = () => {
  chart.value.setOption(chartOption.value, true);
};

watch(
  chartOption,
  val => {
    if (!val) return;
    if (!chart.value) {
      if (!chartEl.value) {
        return;
      }
      const { width } = chartEl.value.getBoundingClientRect();
      if (width == 0) return;
      initChart();
    }
    chart.value.setOption(val, true);
  },
  { deep: true }
);

defineExpose({
  getInstance: () => chart.value,
  updateOption
});
</script>
