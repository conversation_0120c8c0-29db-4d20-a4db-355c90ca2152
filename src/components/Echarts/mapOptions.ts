export default {
  geo: {
    show: true,
    map: "china",
    zoom: 1,
    scaleLimit: {
      min: 1,
      max: 20
    },
    roam: true,
    label: {
      normal: {
        show: true,
        fontSize: "14",
        color: "#fff"
      }
    },
    emphasis: {
      disabled: true
    },
    itemStyle: {
      normal: {
        // borderColor: 'rgba(0, 0, 0, 0.7)'
        areaColor: "#357eda",
        borderColor: "#1c60ea",
        shadowColor: "#173b95",
        shadowBlur: 20,
        opacity: 0.95
      },
      emphasis: {
        color: "#fff",
        areaColor: "#04c6ff", //鼠标选择区域颜色
        shadowOffsetX: 0,
        shadowOffsetY: 0,
        shadowBlur: 20,
        borderWidth: 0,
        shadowColor: "rgba(0, 0, 0, 0.5)",
        opacity: 1
      }
    }
  },
  series: [
    {
      type: "map",
      map: "china",
      geoIndex: 0,
      zoom: 1,
      roam: false,
      selectedMode: false,
      itemStyle: {
        normal: {
          borderWidth: 0.5,
          borderColor: "#009fe8",
          areaColor: "#fff"
        }
      },
      data: []
    },
    {
      type: "effectScatter",
      // type: "scatter",
      coordinateSystem: "geo",
      geoIndex: 0,
      symbolSize: 4,
      symbolOffset: [0, 0],
      showEffectOn: "render",
      rippleEffect: {
        brushType: "stroke",
        scale: 10
      },
      hoverAnimation: true,
      label: {
        show: true,
        formatter: "{b}",
        position: "top",
        color: "#fff",
        fontSize: 14,
        textStyle: {
          color: "#fff",
          fontSize: 12
        },
        distance: 10
      },
      itemStyle: {
        color: "red"
      },
      zlevel: 6,
      data: []
    },
    {
      name: "lines",
      type: "lines",
      coordinateSystem: "geo",
      zlevel: 2,
      large: true,
      effect: {
        show: true, // 开启动态线条效果
        constantSpeed: 50, // 线条速度
        symbol: "arrow", // 标记的图形，支持图片和文字
        symbolSize: 10, // 标记的大小
        trailLength: 0.1, // 动态线条的长度
        loop: true // 是否循环动画效果
      },
      lineStyle: {
        normal: {
          color: "#66E1DF",
          width: 1,
          opacity: 0.4,
          curveness: 0.3 // 曲线程度
        }
        // emphasis: {
        //   opacity: 0.8,
        //   width: 5
        // }
      },
      data: []
    }
  ],
  grid: {
    top: "0px",
    left: "0px",
    right: "0px",
    bottom: "0px"
  }
};
