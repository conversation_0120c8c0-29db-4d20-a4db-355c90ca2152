<template>
  <el-radio-group v-model="cycleType">
    <div class="flex-sc w-full gap-2">
      <!-- 分周期 -->
      <el-radio value="m" v-if="enableMinute">
        <el-input-number
          v-model="perMinutes"
          :min="minMinute"
          :max="60"
          class="w-32"
        >
          <template #prefix>
            <span>每</span>
          </template>
          <template #suffix>
            <span>分钟</span>
          </template>
        </el-input-number>
      </el-radio>

      <!-- 小时周期 -->
      <el-radio value="H" v-if="enableHour">
        <el-input-number
          v-model="perHours"
          :min="1"
          :max="24"
          class="w-32"
        >
          <template #prefix>
            <span>每</span>
          </template>
          <template #suffix>
            <span>小时</span>
          </template>
        </el-input-number>
      </el-radio>


      <!-- 周、月 周期 -->
      <el-radio value="WM" v-if="enableWeekOrMonth">
        <div class="flex-sc gap-1">
          <el-select v-model="weekOrMonth.sign" class="w-16">
            <el-option label="每周" value="week"/>
            <el-option label="每月" value="month"/>
          </el-select>
          <el-select v-if="weekOrMonth.sign=='month'" v-model="weekOrMonth.monthDay" class="w-16">
            <el-option v-for="(item,i) in 31" :key="i" :label="`${item}日`" :value="item"/>
          </el-select>
          <el-select v-if="weekOrMonth.sign=='week'" v-model="weekOrMonth.weekDay" class="w-20">
            <el-option v-for="(item,index) in weekDayOptions" :key="index" :label="item.label" :value="item.value"/>
          </el-select>
          <el-input-number
            v-model="weekOrMonth.hour"
            :min="0"
            :max="23"
            controls-position="right"
            class="w-24"
          >
            <template #suffix>
              <span>时</span>
            </template>
          </el-input-number>
          <el-input-number
            v-model="weekOrMonth.minute"
            :min="0"
            :max="59"
            controls-position="right"
            class="w-24"
          >
            <template #suffix>
              <span>分</span>
            </template>
          </el-input-number>
        </div>
      </el-radio>
    </div>
  </el-radio-group>
</template>

<script lang="ts" setup>
import {reactive, toRefs} from 'vue';

//组件属性
const props = defineProps({
  exp: {
    type: String,
    default: null
  },
  enableMinute: {
    type: Boolean,
    default: true
  },
  enableHour: {
    type: Boolean,
    default: true
  },
  enableWeekOrMonth: {
    type: Boolean,
    default: true
  },
  actionType: {
    type: String,
    default: "m"
  },
  minMinute: {
    type: Number,
    default: 1
  },
  minHour: {
    type: Number,
    default: 10
  }
});

//声明事件
const emit = defineEmits(["update:exp"]);

//数据对象
const state = reactive({
  cycleType: "m",
  perMinutes: 10,
  perHours: 1,
  weekOrMonth: {
    sign: "week",
    weekDay: "1",
    monthDay: 1,
    hour: 0,
    minute: 0
  },
  weekDayOptions: [
    {label: "星期一", value: "1"},
    {label: "星期二", value: "2"},
    {label: "星期三", value: "3"},
    {label: "星期四", value: "4"},
    {label: "星期五", value: "5"},
    {label: "星期六", value: "6"},
    {label: "星期日", value: "7"},
  ]
})
const {
  cycleType,
  perMinutes,
  perHours,
  weekOrMonth,
  weekDayOptions
} = toRefs(state)

//生成表达式
const generateExp = () => {
  if (state.cycleType === "m") {
    generateMinuteExp();
  } else if (state.cycleType === "H") {
    generateHourExp();
  } else if (state.cycleType === "WM") {
    generateWeekOrMonthExp();
  }
}

//生成按分钟周期的表达式
const generateMinuteExp = () => {
  emit("update:exp", `${state.perMinutes}m`)
}

//生成按小时周期的表达式
const generateHourExp = () => {
  emit("update:exp", `${state.perHours}H`)
}

//生成按周或月周期的表达式
const generateWeekOrMonthExp = () => {
  if (state.weekOrMonth.sign === "week") {
    emit("update:exp", `${state.weekOrMonth.weekDay}W#${state.weekOrMonth.hour}H#${state.weekOrMonth.minute}m`)
  } else if (state.weekOrMonth.sign === "month") {
    emit("update:exp", `${state.weekOrMonth.monthDay}D#${state.weekOrMonth.hour}H#${state.weekOrMonth.minute}m`)
  }
}

//解析周期类型
const parseCycleType = (exp: string) => {
  if (exp.endsWith("m") && !exp.includes("#")) {
    state.cycleType = "m";
    state.perMinutes = parseInt(exp.replace("m", ""));
  } else if (exp.endsWith("H") && !exp.includes("#")) {
    state.cycleType = "H";
    state.perHours = parseInt(exp.replace("H", ""));
  } else if (exp.includes("W#") || exp.includes("D#")) {
    state.cycleType = "WM";
    const expArray = exp.split("#");
    if (expArray[0].endsWith("W")) {
      state.weekOrMonth.sign = "week";
      state.weekOrMonth.weekDay = expArray[0].replace("W", "");
    } else if (expArray[0].endsWith("D")) {
      state.weekOrMonth.sign = "month";
      state.weekOrMonth.monthDay = parseInt(expArray[0].replace("D", ""));
    }
    state.weekOrMonth.hour = parseInt(expArray[1].replace("H", ""));
    state.weekOrMonth.minute = parseInt(expArray[2].replace("m", ""));
  }
}

//初始化
const initExp = (exp: string) => {
  if (exp && exp.length > 0) {
    parseCycleType(exp);
  } else {
    state.cycleType = props.actionType;
  }
}

//对外暴露可调用方法
defineExpose({generateExp, initExp});

</script>
