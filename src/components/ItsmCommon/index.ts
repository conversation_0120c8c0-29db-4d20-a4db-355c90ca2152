import { App } from "vue";
import ImTable from "./table/ImTable.vue";
import ImTableColumn from "./table/ImTableColumn.vue";
import ImSearchForm from "./form/search/ImSearchForm.vue";

export * from "./table/types";
export * from "./sortable";

interface GlobalSetting {
  // 字段存储api
  columnStorge?: {
    // 获取表格配置信息
    getTableSet?: (code: string, instance: string) => Promise<any>;
    // 表格配置信息保存方法
    saveTableSet?: (tableSet: any) => Promise<any>;
  };
}

export const globalSet: GlobalSetting = {};
console.log("=== globalSet init");

/**
 * 支持设置全局的字段保存api来解耦表格组件的能力（不依赖public-config服务）
 *
 * @param getTableSet
 * @param saveTableSet
 */
export const setGlobalTableColumnStorge = (
  getTableSet: (code: string, instance: string) => Promise<any>,
  saveTableSet: (tableSet: any) => Promise<any>
) => {
  if (!globalSet.columnStorge) {
    globalSet.columnStorge = {};
  }
  globalSet.columnStorge.getTableSet = getTableSet;
  globalSet.columnStorge.saveTableSet = saveTableSet;

  console.log("setGlobalTableColumnStorge ", getTableSet, saveTableSet);
};

export default {
  install: (app: App) => {
    app.component("im-table", ImTable);
    app.component("im-table-column", ImTableColumn);
    app.component("im-search-form", ImSearchForm);
  }
};
