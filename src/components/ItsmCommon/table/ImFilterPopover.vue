<template>
  <el-popover
    ref="popover"
    popper-class="im-filter-popover"
    :width="360"
    trigger="click"
    v-bind="popoverProps"
    @show="onShow"
  >
    <div class="popover-wrap">
      <el-form>
        <el-form-item>
          <!-- 本地搜索 -->
          <template v-if="useLocalFilter">
            <el-select
              v-if="state.options.length > 10"
              placeholder="请选择过滤"
              clearable
              filterable
              collapse-tags
              :max-collapse-tags="4"
              multiple
              :teleported="false"
              v-model="state.localValues"
            >
              <el-option
                v-for="(option, index) in state.options"
                :key="index"
                v-bind="option"
              >
              </el-option>
            </el-select>
            <el-checkbox-group v-else style="" v-model="state.localValues">
              <el-checkbox
                v-for="(option, index) in state.options"
                v-bind="option"
                :key="index"
              >
                <span>{{ option.label || "(空)" }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </template>

          <!-- 使用过滤数据提供者提供，生成类似excel过滤控件 -->
          <template v-else-if="useFilterProvider">
            <el-card
              shadow="never"
              style="width: 100%; --el-card-padding: 12px"
            >
              <el-input
                v-model="state.filterTreeKeyWords"
                clearable
                placeholder="请输入关键字搜索"
                @input="handleFilterTree"
                style="margin-bottom: 4px"
              >
                <template #prefix>
                  <el-checkbox
                    v-model="state.filterTreeExact"
                    @change="handleFilterTree"
                    >精确</el-checkbox
                  >
                </template>
              </el-input>
              <el-checkbox
                v-model="state.filterAllChecked"
                :indeterminate="state.filterAllIndeterminate"
                @change="handleCheckFilterAll"
              >
                <div style="font-size: 1.1em; font-weight: bold">
                  <span>全部</span>
                  <span class="count-label-dark"
                    >({{ state.filterCheckedTotal }}/{{
                      state.filterProvideData.total
                    }})</span
                  >
                </div>
              </el-checkbox>
              <el-divider></el-divider>
              <el-tree-v2
                ref="filterTree"
                :key="state.filterTreeUpdateKey"
                class="filter-tree"
                style="max-width: 600px"
                :props="state.filterTreeProps"
                :data="state.filterProvideData.options"
                show-checkbox
                check-on-click-node
                :height="240"
                :filter-method="handleFilterMethod"
                @check-change="handleCheckUpdate"
              >
                <template #default="{ node }">
                  <span>
                    <span>{{
                      renderFilterTreeLabel(node.label, node.data)
                    }}</span>
                    <span class="count-label-dark"
                      >（{{ node.data.count }}）</span
                    >
                  </span>
                </template>
              </el-tree-v2>
            </el-card>
          </template>

          <!-- 自定义搜索 -->
          <template v-else>
            <el-input
              v-if="isComponentType(['Input', 'input'])"
              placeholder="请输入搜索"
              clearable
              v-bind="meta.props || ({} as any)"
              v-model="state.filterValue"
            >
              <template #suffix>
                <el-icon>
                  <Search></Search>
                </el-icon>
              </template>
            </el-input>
            <el-tree-select
              v-else-if="
                isComponentType(['SelectTree', 'tree', 'el-tree-select'])
              "
              clearable
              placeholder="请选择搜索"
              :teleported="false"
              v-bind="meta.props || {}"
              v-model="state.filterValue"
            ></el-tree-select>
            <el-select
              v-else-if="isComponentType(['Select', 'select', 'el-select'])"
              placeholder="请选择搜索"
              clearable
              :teleported="false"
              v-bind="meta.props || {}"
              v-model="state.filterValue"
            >
              <el-option
                v-for="(option, index) in enumOptions"
                :key="index"
                v-bind="option"
              ></el-option>
            </el-select>
            <el-date-picker
              v-else-if="
                isComponentType(['DatePicker', 'date', 'el-date-picker'])
              "
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :teleported="false"
              v-bind="meta.props || ({} as any)"
              v-model="state.filterValue"
            ></el-date-picker>
            <el-time-picker
              v-else-if="
                isComponentType(['TimePicker', 'time', 'el-time-picker'])
              "
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              :teleported="false"
              v-bind="meta.props || {}"
              v-model="state.filterValue"
            ></el-time-picker>
            <el-radio-group
              v-else-if="isComponentType(['Radio', 'radio'])"
              border
              v-bind="meta.props || {}"
              v-model="state.filterValue"
            >
              <el-radio
                v-for="(option, index) in enumOptions"
                :key="index"
                v-bind="option"
              ></el-radio>
            </el-radio-group>
            <!-- 自定义-->
            <component
              v-else
              :is="meta.filterType as any"
              v-bind="meta.props || {}"
              v-model="state.filterValue"
            ></component>
          </template>
        </el-form-item>
        <el-form-item>
          <el-button
            type="info"
            :icon="Refresh"
            @click="reset"
            title="取消当前选择的条件并过滤"
            link
            >重置
          </el-button>
          <el-button
            type="info"
            :icon="Refresh"
            @click="resetAll"
            title="取消所有列的条件并过滤"
            link
            >重置全部
          </el-button>
          <div style="flex: 1; text-align: right">
            <el-button @click="closePopover" title="关闭窗口，不做任何处理"
              >取消
            </el-button>
            <el-button type="primary" @click="handleFilter">确定</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #reference>
      <el-icon
        style="cursor: pointer; transform: translate(2px, 2px)"
        class="filter-icon"
        :class="{
          'filter-icon-current': isCurrentFilter,
          [refTableProps.filterIconClassName]:
            !!refTableProps.filterIconClassName
        }"
        :style="refTableProps.filterIconStyle || ({} as CSSProperties)"
        @click.stop="state.filterVisible = true"
      >
        <component :is="filterIcon as any"></component>
      </el-icon>
    </template>
  </el-popover>
</template>
<script lang="ts" setup>
import { Search, Refresh } from "@element-plus/icons-vue";
import type {
  HeaderFilterValue,
  ImTableColumnProps,
  TableContext
} from "./types";
import { EnumTypes } from "./types";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  nextTick,
  onMounted,
  PropType,
  reactive,
  ref,
  watch
} from "vue";
import { ComputedRef } from "@vue/reactivity";
import { TableFilterDataProvider } from "@/components/ItsmCommon/table/props";

const filterTree = ref();
let instance = getCurrentInstance();
const tableContext = inject("tableContext") as TableContext;
const tableEmits = inject("tableEmits") as Function;
const refTableProps = inject("tableProps") as {
  filterIconClassName: string;
  filterIconStyle: CSSProperties;
  filterDataProvider: TableFilterDataProvider;
};
const popover = ref<any>();
const props = defineProps({
  column: Object as PropType<ImTableColumnProps<any>>,
  filterIcon: [Object, Function]
});
const meta = computed(() => {
  return props.column.meta || {};
});

/**
 * 表格配置的过滤数据提供者
 */
const filterDataProvider = computed(() => {
  return refTableProps.filterDataProvider;
});

/**
 * 如果没有定义meta则使用本地过滤（去重下拉/去重复选框）支持多选
 */
const useLocalFilter = computed(() => {
  if (!props.column.meta || !props.column.meta.filterMode) {
    return !filterDataProvider.value;
  } else {
    return props.column.meta.filterMode == "local";
  }
});

/**
 * 是否使用过滤服务提供者
 */
const useFilterProvider = computed(() => {
  return !!filterDataProvider.value;
});

const renderFilterTreeLabel = (label, data) => {
  let { getOptionLabel } = filterDataProvider.value;
  try {
    return typeof getOptionLabel == "function"
      ? getOptionLabel(props.column.prop, label, data)
      : label;
  } catch (e) {
    console.error(e);
    return label;
  }
};

const popoverProps = computed(() => {
  return meta.value.popoverProps || {};
});
const isComponentType = (types: string[]): boolean => {
  return types.includes((meta.value.filterType || "Input") as string);
};
const state = reactive({
  filterVisible: false,
  filterValue: null,
  localValues: [],
  options: [],

  // 过滤数据提供
  filterTreeKeyWords: "",
  filterTreeUpdateKey: 1,
  filterTreeExact: false,
  filterCheckedTotal: 0,
  filterAllChecked: false,
  filterAllIndeterminate: false,
  filterTreeProps: {
    value: "value",
    label: "label",
    children: "children"
  },
  filterProvideData: {
    total: 0,
    options: []
  },
  // 关键字过滤后的所有选项列表
  filterVisibleOptions: [],
  headerFilterValue: <HeaderFilterValue>{
    prop: props.column.prop,
    values: []
  }
});

const enumOptions = computed(() => {
  let options = meta.value.options || [];
  if (Array.isArray(options)) {
    return options;
  }
  if (Array.isArray((options as ComputedRef).value)) {
    return (options as ComputedRef<any[]>).value;
  }
  return state.options || [];
});

const useEnumOptions = computed(() => {
  return EnumTypes.includes(meta.value.filterType as any);
});

const isCurrentFilter = computed(() => {
  if (useFilterProvider.value) {
    return tableContext
      .getHeaderFilterValues()
      .includes(state.headerFilterValue);
  }
  return instance.uid == tableContext.currentFilter;
});

const setLocalOptions = () => {
  if (Array.isArray(meta.value.options)) {
    state.options = meta.value.options;
  } else {
    state.options = tableContext.getLocalColumnOptions(props.column);
  }
};

const onShow = () => {
  // 每次打开重置部分属性
  state.filterTreeExact = false;
  state.filterTreeKeyWords = "";
  state.filterVisibleOptions.splice(0, state.filterVisibleOptions.length);
  nextTick(handleFilterTree);

  if (useLocalFilter.value) {
    // 本地数据刷选
    setLocalOptions();
  } else {
    if (useFilterProvider.value) {
      // 通过定制的接口或注入方法获取过滤分组列表（一般用在分页场景）
      // 获取选项列表提供参数 {prop: 'p', filters: []}
      // 可优化为当列表为空时发起请求，否则使用缓存
      // 暂时每次打开都刷新
      if (true /*state.filterProvideData.options.length == 0*/) {
        let filters = tableContext
          .getHeaderFilterValues()
          .filter(filter => filter.prop != props.column.prop);
        filterDataProvider.value
          .options(props.column.prop, filters)
          .then(res => {
            // 确保返回结构
            let { total, options } = res || {};
            if (!options || !total) {
              console.error(
                "the response structural is not { total, options }"
              );
              return;
            }
            state.filterProvideData.total = total;
            state.filterProvideData.options = options;
          });
      }
    } else {
      // 需要远程过滤，但过滤呈现的选项列表为字段中定义好的枚举列表或者普通的请求列表返回Promise
      if (useEnumOptions.value && typeof meta.value.options == "function") {
        if (meta.value.optionsNoCached === true || state.options.length == 0) {
          let promise: Promise<any[]> = meta.value.options();
          promise.then(res => {
            if (Array.isArray(res)) {
              state.options = res;
            }
          });
        }
      }
    }
  }
};

/**
 * 点击确定执行过滤
 */
const handleFilter = () => {
  tableContext.currentFilter = instance.uid;
  if (useLocalFilter.value) {
    // 本地过滤
    tableContext.doFilterLocal(state.localValues, props.column);
    if (meta.value.hiddenPopoverOnFilter !== false) {
      popover.value.hide();
    }
  } else if (useFilterProvider.value) {
    // 定制远程过滤1
    tableContext.addHeaderFilterValue(state.headerFilterValue);
    if (meta.value.hiddenPopoverOnFilter !== false) {
      popover.value.hide();
    }
    // 如果没有选择数据清除
    if (state.headerFilterValue.values.length == 0) {
      // remote current
      tableContext.removeHeaderFilterValue(state.headerFilterValue);
      // 清除选择节点
    }
    // 点击确定后同步选中和缓存的values
    filterTree.value.setCheckedKeys([...state.headerFilterValue.values]);
    // 触发定制过滤
    triggerFilterDataProviderFilter();
  } else {
    // 普通远程过滤2
    tableContext.doFilterEmits(
      {
        [props.column.prop]: state.filterValue
      },
      state.filterValue,
      props.column
    );
    if (meta.value.hiddenPopoverOnFilter !== false) {
      popover.value.hide();
    }
  }
};

const clear = () => {
  state.localValues = [];
  state.filterValue = null;
  // 清除
  if (useFilterProvider.value) {
    clearFilterProvider();
  }
};

const closePopover = () => {
  popover.value.hide();
};

// const handleCancel = () => {
//   clear();
//   if (isCurrentFilter.value) {
//     tableContext.currentFilter = null;
//     if (useLocalFilter.value) {
//       tableContext.doFilterLocal(state.localValues, props.column);
//     } else if (useFilterProvider.value) {
//       // 关闭pop
//       popover.value.hide();
//     } else {
//       tableContext.doFilterEmits(
//         {
//           [props.column.prop]: (state.filterValue = null)
//         },
//         state.filterValue,
//         props.column
//       );
//       if (meta.value.hiddenPopoverOnFilter !== false) {
//         popover.value.hide();
//       }
//     }
//   } else {
//     popover.value.hide();
//   }
// };

// 触发onFilter
const triggerFilterDataProviderFilter = () => {
  try {
    filterDataProvider.value.onFilter(
      tableContext.getHeaderFilterValues(),
      props.column.prop
    );
  } catch (e) {
    console.error(e);
  }
};

const clearFilterProvider = () => {
  state.filterTreeKeyWords = "";
  state.filterTreeExact = false;
  handleFilterTree();
  handleCheckFilterAll((state.filterAllChecked = false));
};

const reset = () => {
  if (useFilterProvider.value) {
    // reset
    clearFilterProvider();
    // remote current
    tableContext.removeHeaderFilterValue(state.headerFilterValue);
    triggerFilterDataProviderFilter();
    if (meta.value.hiddenPopoverOnFilter !== false) {
      popover.value.hide();
    }
  } else {
    clear();
    handleFilter();
    tableContext.currentFilter = null;
  }
};

const resetAll = () => {
  ++tableContext.filterUpdateCount;
  if (useFilterProvider.value) {
    tableContext.clearAllFilters();
    triggerFilterDataProviderFilter();
    if (meta.value.hiddenPopoverOnFilter !== false) {
      popover.value.hide();
    }
  } else {
    nextTick(() => {
      handleFilter();
      tableContext.currentFilter = null;
    });
  }
};

const handleCheckFilterAll = val => {
  if (val) {
    // 这里应该是过滤后的所有节点不是所有节点（options）
    let options = getVisibleOptions();
    filterTree.value.setCheckedKeys(options.map(option => option.value));
    state.filterCheckedTotal = state.filterProvideData.total =
      getOptionsTotal(options);
    nextTick(() => {
      let checkedDatas = filterTree.value
        .getCheckedNodes()
        .filter(option => options.includes(option));
      state.headerFilterValue.values = checkedDatas.map(
        checkedData => checkedData.value
      );
    });
  } else {
    filterTree.value.setCheckedKeys([]);
    state.filterCheckedTotal = 0;
    state.headerFilterValue.values = [];
  }
  // 清空半选
  state.filterAllIndeterminate = false;
};

const getOptionsTotal = (options: any[]): number => {
  try {
    // 更新总数量
    return options.length == 0
      ? 0
      : options.map(option => option.count).reduce((a, b) => a + b);
  } catch (e) {
    console.error(e);
  }
  return 0;
};

const handleCheckUpdate = () => {
  // 获取过滤后的树的所有下拉节点数据
  let options = getVisibleOptions();
  // 获取过滤后的树的所有选中数据(这里的树节点虽然隐藏了，但是可能仍然选中状态)
  let checkedDatas = filterTree.value
    .getCheckedNodes()
    .filter(option => options.includes(option));
  // 更新总数量
  state.filterProvideData.total = getOptionsTotal(options);
  let totalCount = 0;
  // 计算选择总数
  for (let checkData of checkedDatas) {
    totalCount += checkData.count || 0;
  }
  state.filterCheckedTotal = totalCount;

  state.filterAllChecked =
    checkedDatas.length == options.length && checkedDatas.length > 0;
  state.filterAllIndeterminate =
    checkedDatas.length != options.length && checkedDatas.length > 0;
  nextTick(() => {
    state.headerFilterValue.values = checkedDatas.map(
      checkedData => checkedData.value
    );
  });
};

const handleFilterTree = () => {
  // 每次过滤前重置当前可见的列表
  state.filterVisibleOptions.splice(0, state.filterVisibleOptions.length);
  // 触发tree过滤
  filterTree.value.filter(state.filterTreeKeyWords?.trim());
  // 更新选择状态
  handleCheckUpdate();
};

const getVisibleOptions = () => {
  if (!state.filterTreeKeyWords?.trim()) {
    return [...state.filterProvideData.options];
  } else {
    return state.filterVisibleOptions;
  }
};

const handleFilterMethod0 = (value, data, node) => {
  if (!value) return true;
  let label = node.label;
  let { getOptionLabel } = filterDataProvider.value;
  if (getOptionLabel) {
    try {
      label = getOptionLabel(props.column.prop, label, data);
    } catch (e) {
      console.error(e);
    }
  }
  if (state.filterTreeExact) {
    return label == value;
  }
  return label.indexOf(value) > -1;
};

const handleFilterMethod = (value, data, node) => {
  let result = handleFilterMethod0(value, data, node);
  if (result) {
    // 树组件没有获取可视节点列表的方法这里每次过滤手动添加
    if (!state.filterVisibleOptions.includes(data)) {
      state.filterVisibleOptions.push(data);
    }
  }
  return result;
};

onMounted(() => {
  if (useLocalFilter.value) {
    setLocalOptions();
  }
});

const clearProviderOptions = () => {
  state.filterProvideData.total = 0;
  state.filterProvideData.options = [];
};

watch(
  () => tableContext.filterUpdateCount,
  () => {
    tableContext.currentFilter = null;
    clear();
    clearProviderOptions();
  }
);
</script>

<style lang="scss">
.im-table .filter-span .filter-icon-current {
  color: var(--el-color-primary) !important;
}

.im-filter-popover {
  position: fixed !important;
  padding: 4px 12px !important;
  .el-form-item {
    margin-bottom: 6px !important;
  }
}
</style>

<style lang="scss" scoped>
.im-filter-popover {
  .popover-wrap {
    margin-top: 5px;
  }
  .filter-tree {
    :deep(.el-tree-node__expand-icon) {
      display: none;
    }
  }
  .count-label-dark {
    margin-left: 4px;
    opacity: 0.5;
  }
}
</style>
