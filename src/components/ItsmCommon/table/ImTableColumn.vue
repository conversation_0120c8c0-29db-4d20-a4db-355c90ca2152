<template>
  <el-table-column
    :key="state.headerKey"
    v-bind="computedColumnProps"
    :class-name="computedClassName"
  >
    <template v-if="columnSlot || columnRender" #default="scope">
      <span
        v-if="columnRender"
        v-html="
          column.render(
            scope.row,
            column,
            scope.$index,
            tableInstance.getOffsetIndex()
          )
        "
      >
      </span>
      <component
        v-else
        :is="columnSlot"
        v-bind="{
          ...scope,
          offsetIndex: tableInstance.getOffsetIndex(),
          size: tableInstance.getSize(),
          type: 'primary'
        }"
      ></component>
    </template>
    <template v-if="column.children">
      <im-table-column
        v-for="(col, index) in childrenColumns"
        :key="index"
        :column="col"
      >
      </im-table-column>
    </template>
    <template v-if="useHeaderSlot" #header="scope">
      <component
        v-if="columnHeaderSlot"
        :is="columnHeaderSlot"
        v-bind="scope"
      ></component>
      <span v-else>{{ column.label }}</span>
      <!-- 如果定义了filterable -->
      <span class="filter-span" v-if="filterable">
        <im-filter-popover
          :column="column"
          :filter-icon="(filterIconSlot || Filter) as Function"
        ></im-filter-popover>
      </span>
    </template>
    <template v-if="filterIconSlot" #filter-icon="scope">
      <component :is="filterIconSlot"></component>
    </template>
  </el-table-column>
</template>
<script lang="ts" setup>
import { computed, inject, PropType, reactive, useSlots, watch } from "vue";
import { Filter } from "@element-plus/icons-vue";
import type { ImTableColumnProps, ImTableInstance } from "./types";
import ImFilterPopover from "./ImFilterPopover.vue";

const slots = useSlots();
const tableSlots = inject("slots");
const tableInstance = inject("table") as ImTableInstance;
const props = defineProps({
  // 必填字段
  column: {
    type: Object as PropType<ImTableColumnProps<any>>,
    required: true
  },
  className: String
});

const state = reactive({
  headerKey: 1,
  filterVisible: false
});

// 通过表格定义的通用属性
const globalColumnProps = computed(() => {
  return {
    align: tableInstance.defaultAlign()
  };
});
const computedColumnProps = computed(() => {
  let {
    hidden,
    slot,
    filterable,
    filterIcon,
    children,
    headerSlot,
    meta,
    moreProps,
    ...otherProps
  } = props.column;
  return {
    ...globalColumnProps.value,
    ...otherProps,
    ...(moreProps || {})
  };
});
const computedClassName = computed(() => {
  let classNames = props.className ? [props.className] : [];
  let className = computedColumnProps.value.className;
  if (className) {
    classNames.push(className);
  }
  return classNames.join(" ");
});

const columnRender = computed(() => {
  return props.column.render;
});

const columnSlot = computed(() => {
  return (
    tableSlots[props.column.slot] ||
    tableSlots[props.column.prop] ||
    slots.default
  );
});
const filterIconSlot = computed(() => {
  return props.column.filterIcon || tableSlots["filter-icon"];
});
const columnHeaderSlot = computed(() => {
  return (
    tableSlots[props.column.headerSlot] ||
    tableSlots[props.column.prop + "-header"]
  );
});
const filterable = computed(() => {
  return props.column.filterable || props.column.meta?.filterable || false;
});

const useHeaderSlot = computed(() => {
  return filterable.value || !!columnHeaderSlot.value;
});

const childrenColumns = computed(() => {
  return props.column.children.filter(
    col => col.hidden != true && col.hidden != "true"
  );
});
watch(
  () => filterable.value,
  val => {
    ++state.headerKey;
  }
);
</script>
<style lang="scss" scoped>
.filter-span {
  font-weight: bold;
  font-size: 1.1em;
}
</style>
