import { ImTableColumnProps } from "@/components/ItsmCommon";

let seq = 1;
export const genId = () => {
  return new Date().getTime().toString(16) + "-" + seq++;
};
export const isDevelopment = (): boolean => {
  return import.meta.env.MODE == "development";
};

const suppoetedEvents = [
  // on-开头为表格拓展的事件，其余为ElTable支持的事件
  "on-columns-storage",
  "on-sort-finished",
  "on-load-before",
  "on-load-success",
  "on-header-dragend",
  "on-page-size-change",
  "on-page-current-change",
  "on-column-sort-change",
  "on-columns-storage",
  "on-reload",
  "on-setting",
  "on-filter",
  // 以下是ElTable支持事件列表
  "select",
  "select-all",
  "selection-change",
  "cell-mouse-enter",
  "cell-mouse-leave",
  "cell-contextmenu",
  "cell-click",
  "cell-dblclick",
  "row-click",
  "row-contextmenu",
  "row-dblclick",
  "header-click",
  "header-contextmenu",
  "sort-change",
  "filter-change",
  "current-change",
  "header-dragend",
  "expand-change",
  "scroll"
];

// 注册事件
export const buildEvents = emits => {
  const events = {};
  for (let event of suppoetedEvents) {
    events[event] = function () {
      emits(event, ...arguments);
    };
  }
  return events;
};

// export const createProxy = (target, self) => {
//   return new Proxy(target, {
//     get(target, prop) {
//       if (prop in target) {
//         return Reflect.get(target, prop);
//       } else {
//         return self[prop];
//       }
//     },
//     apply(target, thisArg, argumentsList) {
//       return Reflect.apply(target, thisArg, argumentsList);
//     }
//   });
// };

export const filterColumnsByProp = (
  prop: string,
  columns: ImTableColumnProps<any>[],
  results: ImTableColumnProps<any>[]
) => {
  for (let col of columns) {
    if (results.includes(col)) continue;
    if (col.prop == prop) {
      results.push(col);
    }
    if (col.children) {
      filterColumnsByProp(
        prop,
        col.children as ImTableColumnProps<any>[],
        results
      );
    }
  }
};

export const debounce = (func, wait) => {
  let timeout;
  return function (...args) {
    const context = this;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
};
