import { CSSProperties } from "vue";
import { TableProps } from "element-plus";
import {
  HeaderFilterValue,
  ImTableColumnProps,
  ImTablePaginationProps,
  TableColumnStorage,
  TableToolbar
} from "./types";
import { SortableOption } from "../sortable";

const BooleanTrue = {
  type: Boolean,
  default: true
};

export interface RequestResult {
  rows?: any[];
  total?: number;
  success: boolean;
}

export type RequestParams = {
  pageSize?: number;
  currentPage?: number;
  [key: string]: any;
};

export interface FilterOption {
  label: string;
  value: string | number;
  count: number;
}

export interface TableFilterDataProvider {
  // 请求uri, 当打开过滤浮窗时会发送postjson请求，参数: {prop: 'xxxx'}
  // uri: string;
  // 自定义请求返回
  options: (
    prop: string,
    filters: HeaderFilterValue[]
  ) => Promise<{
    total: number;
    options: FilterOption[];
  }>;
  // 提供枚举转化
  getOptionLabel?: (
    prop: string,
    label: string,
    option: FilterOption
  ) => string;
  // 过滤时触发调用
  onFilter: (filters: HeaderFilterValue[], prop: string) => void;
}

export interface TableAlert {
  closable?: boolean;
  className?: string;
  effect: string;
  showIcon: boolean;
  center: boolean;
  title: string;
}

export const imTableProps = {
  // 以下为ElTable定义的属性列表中筛选的部分属性，
  // 官网API: https://cn.element-plus.org/zh-CN/component/table.html#table-api
  data: Array as PropType<any[]>,
  size: String as PropType<"default" | "small" | "large">,
  width: [String, Number],
  height: [String, Number],
  maxHeight: [String, Number],
  fit: BooleanTrue,
  stripe: BooleanTrue,
  border: BooleanTrue,
  rowKey: [String, Function],
  showHeader: BooleanTrue,
  showSummary: Boolean,
  sumText: String,
  summaryMethod: Function,
  rowClassName: [String, Function],
  rowStyle: [
    Object as PropType<CSSProperties>,
    Function as PropType<(data: any) => CSSProperties>
  ],
  cellClassName: [String, Function],
  cellStyle: [
    Object as PropType<CSSProperties>,
    Function as PropType<(data: any) => CSSProperties>
  ],
  headerRowClassName: [String, Function],
  headerRowStyle: [
    Object as PropType<CSSProperties>,
    Function as PropType<(data: any) => CSSProperties>
  ],
  headerCellClassName: [String, Function],
  headerCellStyle: [
    Object as PropType<CSSProperties>,
    Function as PropType<(data: any) => CSSProperties>
  ],
  highlightCurrentRow: Boolean,
  currentRowKey: [String, Number],
  emptyText: String,
  expandRowKeys: Array as PropType<string[]>,
  defaultExpandAll: Boolean,
  tooltipEffect: String,
  spanMethod: Function,
  selectOnIndeterminate: Boolean,
  indent: Number,
  lazy: Boolean,
  load: Function,
  className: String,
  style: Object as PropType<CSSProperties>,
  scrollbarAlwaysOn: Boolean,
  flexible: Boolean,
  showOverflowTooltip: [Boolean, Object],
  appendFilterPanelTo: String,
  scrollbarTabindex: [Number, String],
  // ElTable定义的全量属性列表
  tableProps: Object as PropType<TableProps<any>>,

  // im-table封装拓展的增强属性配置
  // url
  url: String,
  // 查询参数
  query: [String, Object],
  // GET 或 POST
  method: { type: String, default: "GET" },
  // 以上是兼容2.0封装的ImTable支持的属性
  enhanced: Boolean,
  // 选择提示栏
  tableAlert: [Object, Boolean] as PropType<TableAlert | boolean>,
  // 工具栏配置
  toolbar: [Object, Boolean] as PropType<TableToolbar | boolean>,
  // 分页栏配置
  pagination: [Object, Boolean] as PropType<
    ImTablePaginationProps | object | boolean
  >,
  // 字段默认是否居中
  center: Boolean,
  // 字段列表
  columns: Array as PropType<ImTableColumnProps<any>[] | any[]>,
  // 是否显示复选框（不受columns数组控制，追加在第一列）
  showCheckbox: Boolean,
  // 是否显示计数（不受columns数组控制，追加在第一列）
  showIndex: Boolean,
  // 字段存储设置
  columnStorage: Object as PropType<TableColumnStorage>,
  // 字段是否支持列头排序
  columnSortable: [Boolean, Object] as PropType<boolean | SortableOption>,
  // 数据行是否支持排序
  sortable: [Boolean, Object] as PropType<boolean | SortableOption>,
  // 加载中
  loading: Boolean,
  // 操作列
  operator: [Object, Boolean] as PropType<
    ImTableColumnProps<any> | Object | boolean
  >,
  // 自定义请求(如果定义了request将忽略data属性)
  request: Function as PropType<
    (params: RequestParams, sort?, filter?) => Promise<RequestResult>
  >,
  // 是否自动发起请求，默认true
  autoRequest: BooleanTrue,
  // 自定义过滤图标的样式类
  filterIconClassName: String,
  // 自定义过滤图标的样式
  filterIconStyle: Object as PropType<CSSProperties>,
  // 过滤数据配置提供者(通过配置的url获取远程列表数据作为列的过滤数据来源)
  filterDataProvider: Object as PropType<TableFilterDataProvider>
};
