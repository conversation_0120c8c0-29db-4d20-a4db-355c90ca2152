/**
 * 获取时间范围
 *
 * @param value
 * @param isDaterange
 */
export const getDateRange = (value, isDaterange) => {
  const currentDate = new Date();
  let endTime = currentDate.getTime();
  let startTime = null;
  if (typeof value == "number" || /^-?\d+$/.test(value)) {
    startTime = endTime - value;
  } else if (value && typeof value == "string") {
    // 通过标识当前仅仅精确到小时且为整数单位，如果需要快捷最近多少分钟或半小时之类，可以自行折算或直接计算出毫秒数，然后给数值型直接返回
    // 比如1月可以定义1m或者30d，半月只能定义15d,半小时只能计算毫秒数
    // d级别及以下是精确因为1d=24h
    if (/^-?\d+[ymdh]$/.test(value)) {
      let unit = value.charAt(value.length - 1);
      let num = parseInt(value.substring(0, value.length - 1));
      let dateParam = {
        y: currentDate.getFullYear(),
        m: currentDate.getMonth(),
        d: currentDate.getDate(),
        h: currentDate.getHours()
      };
      dateParam[unit] = dateParam[unit] - num;
      currentDate.setFullYear(dateParam.y, dateParam.m, dateParam.d);
      currentDate.setHours(dateParam.h);
      startTime = currentDate.getTime();
    }
  } else if (Array.isArray(value)) {
    // 直接指定起止时间
    startTime = value[0];
    if (typeof value[0] == "number") {
      startTime = value[0];
      if (typeof value[1] == "number") {
        endTime = value[1];
      }
    }
  }

  // 返回数组
  if (startTime) {
    // 命中逻辑处理
    let fromTime = Math.min(startTime, endTime);
    let toTime = Math.max(startTime, endTime);
    return isDaterange
      ? [new Date(fromTime), new Date(toTime)]
      : new Date(startTime);
  } else {
    console.warn(`无效的快捷选项标识: ${value}`);
  }
  return isDaterange ? [currentDate, currentDate] : currentDate;
};

/**
 * 检查状态
 *
 * @param column
 * @param externalParams
 */
export const checkState = (column, externalParams?) => {
  let params = externalParams;
  let isRenderExpr =
    column && column.component && column.component.isRenderExpr;
  if (isRenderExpr) {
    let evalExprFn = new Function("params", `return ${isRenderExpr}`);
    try {
      return evalExprFn(params);
    } catch (e) {
      return false;
    }
  } else {
    return true;
  }
};

export const isRenderOption = (option, externalParams?) => {
  let { isRenderExpr } = option || {};
  if (!isRenderExpr) {
    return true;
  }
  let params = externalParams;
  let evalExprFn = new Function("params", `return ${isRenderExpr}`);
  try {
    console.log(params, isRenderExpr);
    return evalExprFn(params);
  } catch (e) {
    return false;
  }
};

export const isComponentType = (column, type, otherTypes?) => {
  if (!column) return false;
  let componentType = null;
  if (typeof column.component == "string") {
    componentType = column.component;
  } else {
    componentType = column.component && column.component.type;
  }
  if (componentType == type) return true;
  if (Array.isArray(otherTypes)) {
    return otherTypes.includes(componentType);
  }
  return false;
};

export const getComponentProps = column => {
  let component = column.component;
  let { props = {} } = component || {};
  return props;
};
//
// export const getDatePickerProps = column => {
//   let props = componentProps(column);
//   if (this.autoDateToRange) {
//     let dateType = props.type;
//     if (!dateType.endsWith("range")) {
//       props.type = dateType + "range";
//     }
//   }
//
//   let options = column.component && column.component.options;
//   if (Array.isArray(options) && options.length > 0) {
//     // 是否范围
//     let isDaterange =
//       props.type == "datetimerange" ||
//       props.type == "daterange" ||
//       props.type == "monthrange";
//     // 构建快捷选项
//     Object.assign(props, {
//       pickerOptions: {
//         shortcuts: options.map(option => {
//           return {
//             text: option.label,
//             onClick: picker => {
//               picker.$emit(
//                 "pick",
//                 this.getDateRange(option.value, isDaterange)
//               );
//             }
//           };
//         })
//       }
//     });
//   }
//   return props;
// };
