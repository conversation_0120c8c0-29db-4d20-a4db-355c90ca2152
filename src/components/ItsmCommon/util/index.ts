// 组织冒泡
export const stopAndPreventDefault = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
};

// empty functon
export const undo = () => {};

/**
 * 获取元素所在父元素的下标位置
 *
 * @param element
 */
export const getElementIndex = (element: HTMLElement) => {
  let children = element.parentElement.children as HTMLCollection;
  for (let i = 0, length = children.length; i < length; ++i) {
    if (element == children[i]) {
      return i;
    }
  }
  return -1;
};

/**
 * 获取鼠标落点所在容器的子元素位置
 *
 * @param container
 * @param event
 */
export const getDropElementIndex = (
  container: HTMLElement,
  event: DragEvent
) => {
  let { clientX, clientY } = event;
  // 根据鼠标位置调整placeHolder（dom节点）在容器的位置
  let dropItemIndex = 0;
  let children = container.children;
  for (let i = 0, length = children.length; i < length; ++i) {
    let childNode = children.item(i);
    let { left, top, width, height } = childNode.getBoundingClientRect();
    if (left + width <= clientX) {
      ++dropItemIndex;
      continue;
    }
    if (top + height <= clientY) {
      ++dropItemIndex;
      continue;
    }
    break;
  }
  return dropItemIndex;
};

export const checkDragIgnorePoint = (e: DragEvent, ignoreSelector: string) => {
  let dragElement = e.target as HTMLElement;
  let { clientX, clientY } = e;
  try {
    let domList = dragElement.querySelectorAll(ignoreSelector);
    let length = domList.length;
    if (length == 0) return false;
    for (let i = 0; i < length; ++i) {
      let ignoreDom = domList.item(i) as HTMLElement;
      let { left, top, width, height } = ignoreDom.getBoundingClientRect();
      if (
        clientX >= left &&
        clientX <= left + width &&
        clientY >= top &&
        clientY <= top + height
      ) {
        return true;
      }
    }
    return false;
  } catch (e) {
    return false;
  }
};
