import {
  getDropElementIndex,
  getElementIndex,
  checkDragIgnorePoint,
  stopAndPreventDefault,
  undo
} from "../util";

type El = string | HTMLElement;
export interface SortableOption {
  // 是否启用
  enable?: boolean;
  // 动画
  animation?: number;
  // 过滤类名
  filterClassName?: string;
  // 禁用类名
  disableClassName?: string;
  // 拖动忽略的句柄，比如某些子元素不想触发拖动(选择器)
  dragIgnoreSelector?: string;
  onStart?: (index: number, e: DragEvent) => void;
  onEnd?: Function;
  onDrag?: (index: number, e: DragEvent) => void;
  onDrop?: (
    fromDataIndex: number,
    toDataIndex: number,
    event?: DragEvent,
    fromElementIndex?: number,
    toElementIndex?: number
  ) => void;
  offsetIndex?: number;
  endIndex?: number;
  clone?: boolean;
}
const defaultOption: SortableOption = {
  enable: true,
  animation: 200,
  onStart: undo,
  onEnd: undo,
  onDrag: undo,
  onDrop: undo,
  offsetIndex: 0,
  endIndex: -1,
  clone: true
};

export class SortableHandler {
  private readonly _container: HTMLElement;
  private readonly _option: SortableOption;
  private _filter_class_name: string;
  private _disabled: boolean;
  private _destroyd: boolean;
  private _timer;
  private _on_drag_fn;
  private _on_dragstart_fn;
  private _on_dragend_fn;
  private _on_drop_fn;
  private _drag_data_index: number;
  private _drag_element_index: number;
  private _drop_data_index: number;
  private _drop_element_index: number;
  constructor(container: HTMLElement, option: SortableOption) {
    this._container = container;
    this._option = Object.assign({}, defaultOption, option);
    this._filter_class_name = this._option.filterClassName;
    this.init();
  }
  private init() {
    let option = this._option;
    let container = this._container;
    this._on_dragstart_fn = (e: DragEvent) => {
      let dragElement = e.target as HTMLElement;

      let ignoreSelector = option.dragIgnoreSelector;
      if (ignoreSelector) {
        let isDragIgnorePoint = checkDragIgnorePoint(e, ignoreSelector);
        if (isDragIgnorePoint) {
          stopAndPreventDefault(e);
          return false;
        }
      }

      let index = getElementIndex(dragElement);
      this._drag_element_index = index;
      try {
        this._drag_data_index = parseInt(dragElement.dataset.dragIndex);
        option.onStart(index, e);
      } catch (e) {
        console.error(e);
      }
    };
    this._on_drag_fn = (e: DragEvent) => {
      try {
        option.onDrag(this._drag_data_index, e);
      } catch (e) {
        console.error(e);
      }
    };
    this._on_dragend_fn = (e: DragEvent) => {
      try {
        option.onEnd(e);
      } catch (e) {
        console.error(e);
      }
      this.reset();
    };
    this._on_drop_fn = (e: DragEvent) => {
      let dropElementIndex = getDropElementIndex(this._container, e);
      let { _drag_data_index: fromDataIndex } = this;
      this._drop_element_index = dropElementIndex;
      let toDataIndex = this.getDropDataIndex(dropElementIndex);
      try {
        toDataIndex = toDataIndex == -1 ? fromDataIndex : toDataIndex;
        option.onDrop(
          fromDataIndex,
          toDataIndex,
          e,
          this._drag_element_index,
          this._drop_element_index
        );
      } catch (e) {
        console.error(e);
      }
    };
    this.update();
    container.addEventListener("dragover", stopAndPreventDefault);
    container.addEventListener("drop", this._on_drop_fn);
  }
  private getDropDataIndex(dropElementIndex: number): number {
    if (!this._filter_class_name) return dropElementIndex;
    let container = this._container;
    let children = container.children;
    let filterClass = this._option.filterClassName;
    for (let i = dropElementIndex, length = children.length; i < length; ++i) {
      let childNode = children.item(i) as HTMLElement;
      if (childNode.classList.contains(filterClass)) {
        try {
          let dragIndex = parseInt(childNode.dataset.dragIndex);
          return isNaN(dragIndex) ? i : dragIndex;
        } catch (e) {
          return i;
        }
      }
      break;
    }
    return -1;
  }
  private reset() {
    this._drag_data_index = -1;
    this._drag_element_index = -1;
    this._drop_data_index = -1;
    this._drop_element_index = -1;
    this.update();
  }
  private updateSortableDrag() {
    let { _container: container, _option: option } = this;
    this.clearDragDatas();
    try {
      let children: HTMLCollection = container.children;
      let length = children.length;
      if (length == 0) {
        if (this._disabled || this._destroyd) {
          return;
        }
        let me = this;
        this._timer = setTimeout(() => {
          me.updateSortableDrag();
        }, 1000);
      }
      const offsetIndex =
        option.offsetIndex < 0
          ? option.offsetIndex + length
          : option.offsetIndex;
      const endOffset =
        option.endIndex < 0 ? option.endIndex + length : option.endIndex;
      const size = Math.min(endOffset + 1, length);
      const filterClass = option.filterClassName;
      let index = 0;
      for (let i = offsetIndex; i < size; ++i) {
        let child = children.item(i) as HTMLElement;
        if (filterClass && !child.classList.contains(filterClass)) {
          continue;
        }
        if (!child.classList.contains(option.disableClassName)) {
          child.setAttribute("draggable", "true");
        }
        child.setAttribute("data-drag-index", index++ + "");
        child.addEventListener("dragend", this._on_dragend_fn);
        child.addEventListener("drag", this._on_drag_fn);
        child.addEventListener("dragstart", this._on_dragstart_fn);
      }
    } catch (e) {
      console.trace(e);
    }
  }
  private clearDragDatas() {
    // 卸载事件
    const container = this._container;
    let children: HTMLCollection = container.children;
    let length = children.length;
    for (let i = 0; i < length; ++i) {
      let child = children.item(i);
      child.removeAttribute("draggable");
      child.removeAttribute("data-drag-index");
      child.removeEventListener("dragstart", this._on_dragstart_fn);
      child.removeEventListener("drag", this._on_drag_fn);
      child.removeEventListener("dragend", this._on_dragend_fn);
    }
  }
  /**
   * 当子元素变化时外部可以主动调用更新
   */
  update() {
    this.updateSortableDrag();
  }
  disable() {
    this._disabled = true;
  }
  /**
   * 是否停用
   */
  isDisabled() {
    return this._disabled;
  }
  /**
   * 是否已经销毁
   */
  isDestroyd() {
    return !!this._destroyd;
  }
  /**
   * 销毁handler
   */
  destroy() {
    if (this._timer) {
      clearTimeout(this._timer);
    }
    try {
      this.clearDragDatas();
      this._container.removeEventListener("dragover", stopAndPreventDefault);
    } catch (e) {}
    this._destroyd = true;
  }
}

export const createSortable = (
  selector: El,
  option: SortableOption
): SortableHandler => {
  let element: HTMLElement;
  if (typeof selector == "string") {
    element = document.querySelector(selector);
  } else {
    // 判断selector是否为dom元素
    if (selector) {
    }
    element = selector as HTMLElement;
  }
  return new SortableHandler(element, option);
};
