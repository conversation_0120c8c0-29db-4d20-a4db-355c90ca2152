<template>
  <div ref="sortableEl" class="sortable-wrap">
    <template
      v-if="useItemSlot"
      v-for="(item, index) in modelValue"
      :key="getItemKey(item, index)"
    >
      <slot name="item" v-bind="{ item, index }"></slot>
    </template>
    <slot v-else></slot>
  </div>
</template>
<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, useSlots } from "vue";
import { createSortable, SortableOption } from "@/components/ItsmCommon";
/**
 * 使用方式: 包裹v-for或者使用插槽#item,两者二选一
 */
const props = defineProps({
  option: Object as PropType<SortableOption>,
  modelValue: Array as PropType<any[]>,
  itemKey: String
});
const sortableEl = ref();
const emit = defineEmits(["update:modelValue"]);
const slots = useSlots();
const useItemSlot = computed(() => {
  return !!slots["item"];
});

const state = reactive({
  sortableHandler: null
});

onMounted(() => {
  try {
    const container = sortableEl.value as HTMLElement;
    let sortableOption = props.option || {};
    let sortOption: SortableOption = {
      ...sortableOption,
      onDrop: (fromIndex: number, toIndex: number, e: DragEvent) => {
        if (props.modelValue) {
          // update
          const element = props.modelValue.splice(fromIndex, 1)[0];
          props.modelValue.splice(toIndex, 0, element);
        }

        console.log("fromIndex", fromIndex);
        console.log("toIndex", toIndex);
      }
    };
    state.sortableHandler = createSortable(container, sortOption);
  } catch (error) {
    console.trace(error);
  }
});

const getItemKey = (item, index) => {
  return props.itemKey ? item[props.itemKey] : index;
};
</script>
