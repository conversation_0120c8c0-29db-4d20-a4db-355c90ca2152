<template>
  <el-form-item class="search-form-item" :label="column.name">
    <template #label>
      <div :title="column.name" class="form-item-label">
        {{ column.name }}
      </div>
    </template>

    <el-input
      v-if="isComponentType(column, 'Input')"
      v-bind="componentProps(column)"
      v-model="model.value"
      :autosize="false"
      :rows="1"
    >
      <template v-if="model.fuzzyable" #append>
        <el-checkbox
          v-model="model.operator"
          true-label="fuzzy"
          false-label="exact"
          >模糊</el-checkbox
        >
      </template>
    </el-input>
    <el-input
      v-else-if="isComponentType(column, 'Number')"
      class="prpendable-number"
      type="number"
      v-bind="componentProps(column)"
      v-model.number="model.value"
    >
      <template v-if="column.component.prepend" #prepend>
        <el-select
          v-model="model.operator"
          placeholder="运算符"
          style="width: 100px"
        >
          <el-option
            v-for="(option, index) in column.component.prependOptions"
            :key="index"
            v-bind="option"
          ></el-option>
        </el-select>
      </template>
    </el-input>
    <el-select
      v-else-if="isComponentType(column, 'Select')"
      v-bind="componentProps(column)"
      v-model="model.value"
      filterable
      style="width: 100%"
    >
      <template
        v-for="(option, index) in column.component.options"
        :key="index"
      >
        <el-option
          v-if="isRenderOption(option)"
          :label="option.label"
          :value="option.value"
        ></el-option>
      </template>
    </el-select>
    <el-tree-select
      v-else-if="isComponentType(column, 'SelectTree')"
      v-bind="componentProps(column)"
      v-model="model.value"
    ></el-tree-select>
    <!--    <im-time-select-->
    <!--      v-else-if="isComponentType(column, 'TimeSelect')"-->
    <!--      v-bind="componentProps(column)"-->
    <!--      v-model="model.value"-->
    <!--    >-->
    <!--    </im-time-select>-->
    <el-checkbox-group
      v-else-if="isComponentType(column, 'Checkbox')"
      v-bind="componentProps(column)"
      v-model="model.value"
    >
      <el-checkbox
        v-for="(option, index) in column.component.options"
        :key="index"
        :label="option.value"
        >{{ option.label }}</el-checkbox
      >
    </el-checkbox-group>
    <el-radio-group
      v-else-if="isComponentType(column, 'Radio')"
      v-bind="componentProps(column)"
      v-model="model.value"
    >
      <el-radio-button
        v-for="(option, index) in column.component.options"
        :key="index"
        :label="option.value"
        >{{ option.label }}</el-radio-button
      >
    </el-radio-group>
    <el-date-picker
      v-else-if="isComponentType(column, 'DatePicker')"
      v-bind="getDatePickerProps(column)"
      v-model="model.value"
      clearable
      style="max-width: 100%"
    ></el-date-picker>
  </el-form-item>
</template>

<script lang="ts" setup>
import { computed, reactive } from "vue";
import {
  getDateRange,
  isRenderOption,
  isComponentType,
  getComponentProps
} from "@/components/ItsmCommon/util/form";

const props = defineProps({
  column: Object,
  index: Number,
  model: Object,
  // 自适应
  fit: Boolean
});

const externalParams = computed(() => {
  return props.model;
});

const defaultProps = computed(() => {
  let dp: any = {
    clearable: true
  };
  if (props.fit) {
    dp.style = {
      width: "100%"
    };
  }
  return dp;
});

const componentProps = column => {
  let props = getComponentProps(column);

  // 替换动态占位信息
  let cProps = Object.assign({}, defaultProps.value, props);
  if (!cProps.placeholder) {
    if ((column.component.type || "Input") == "Input") {
      cProps.placeholder = "请输入";
    } else {
      cProps.placeholder = "请选择";
    }
  } else {
    if (cProps.placeholder.indexOf("${name}") > -1) {
      cProps.placeholder = cProps.placeholder.replace(
        "${name}",
        column.name || "关键字"
      );
    }
  }
  return cProps;
};

const getDatePickerProps = column => {
  let props = componentProps(column);
  // if (this.autoDateToRange) {
  //   let dateType = props.type;
  //   if (!dateType.endsWith("range")) {
  //     props.type = dateType + "range";
  //   }
  // }

  let options = column.component && column.component.options;
  if (Array.isArray(options) && options.length > 0) {
    // 是否范围
    let isDaterange =
      props.type == "datetimerange" ||
      props.type == "daterange" ||
      props.type == "monthrange";
    // 构建快捷选项
    Object.assign(props, {
      pickerOptions: {
        shortcuts: options.map(option => {
          return {
            text: option.label,
            onClick: picker => {
              picker.$emit("pick", getDateRange(option.value, isDaterange));
            }
          };
        })
      }
    });
  }
  return props;
};

// 数组对象
const arrayModel = field => {
  let arr = externalParams.value[field];
  if (!arr) {
    externalParams[field] = [];
  }
  return externalParams.value;
};

/**日期对象*/
const dateModel = column => {
  if (column && column.component && column.component.props) {
    let dateType = column.component.props.type;
    if (
      dateType == "datetimerange" ||
      dateType == "daterange" ||
      dateType == "monthrange"
    ) {
      return arrayModel(column.field);
    }
  }
  return externalParams.value;
};
</script>

<style lang="scss" scoped>
.search-form-item {
  .form-item-label {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .prpendable-number {
    :deep(.el-input-group__prepend) {
      background: unset !important;
    }
  }
}
</style>
