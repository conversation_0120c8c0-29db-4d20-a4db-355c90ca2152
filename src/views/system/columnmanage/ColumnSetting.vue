<template>
  <span>
    <slot>
      <el-tooltip
        :disabled="!tooltip"
        placement="top"
        :content="tooltip as string"
      >
        <el-icon style="cursor: pointer">
          <Setting></Setting>
        </el-icon>
      </el-tooltip>
    </slot>
  </span>
</template>
<script setup lang="ts">
import { onMounted, reactive, PropType } from "vue";
import { ImTableColumnProps } from "@/components/ItsmCommon";
import { Setting } from "@element-plus/icons-vue";
import { queryTableSet } from "@/views/system/columnmanage/api";

interface ColumnState {
  // 标识
  key: string;
  // 是否隐藏
  hidden: boolean;
}

const props = defineProps({
  tooltip: {
    type: [String, Boolean] as PropType<string | false>,
    default: "字段设置"
  },
  code: {
    type: String,
    required: true
  },
  // 默认存储服务端，需要使用code去查询字段列表；
  // 如果为local，将使用属性fullColumns；
  storage: {
    type: String as PropType<"local" | "remote">,
    default: "remote"
  },
  // 完整字段列表（本地存储模式使用，将只对配置了prop的字段生效）
  fullColumns: {
    type: Array as PropType<ImTableColumnProps<any>[]>
  }
});
// 所有的改动都会派发update事件来联动表格列的变化
const emits = defineEmits(["update"]);

const state = reactive({
  // 浮窗显示状态
  visible: false,
  // 服务端模式根据编码查询的字段列表集合(全量)
  tableColumns: [],
  // 根据当前登录账号查询的自定义控制列表，如果为空将使用全量
  customColumnStates: <ColumnState[]>[]
});

const reloadTableColumns = () => {
  queryTableSet(props.code, null).then(res => {
    let { columns, states } = res.data;
    state.tableColumns = columns;
    states.customColumnStates = states;
  });
};

onMounted(() => {
  reloadTableColumns();
});
</script>
<style scoped lang="scss"></style>
