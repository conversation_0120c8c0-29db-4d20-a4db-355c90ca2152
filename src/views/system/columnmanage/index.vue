<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div class="pb-1.5 ml-1">
          <el-input
            class="mt-2 mb-1 pb-1.5 ml-1 w-[98%]"
            v-model="state.searchKey"
            @input="filterTree"
            placeholder="请输入名称或者编码过滤"
          >
            <template #append>
              <el-icon @click="addFolder">
                <Plus style="cursor: pointer"></Plus>
              </el-icon>
            </template>
          </el-input>
          <el-tree
            ref="columnDirTreeRef"
            style="max-height: 50rem"
            class="filter-tree"
            :data="state.dirTableTreeData"
            :props="defaultTreeProps"
            node-key="id"
            :expand-on-click-node="false"
            highlight-current
            :filter-node-method="handleFilterNode"
            :current-node-key="state.currentNodeKey"
            default-expand-all
            @node-click="nodeClick"
          >
            <template #default="{ node, data }">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  flex-grow: 1;
                  font-size: 13px;
                  line-height: 17px;
                "
              >
                <div style="display: flex; gap: 6px; align-items: center">
                  <el-icon v-if="!data.leaf">
                    <Folder />
                  </el-icon>
                  <el-icon v-else>
                    <Document />
                  </el-icon>
                  <span>{{ node.label }}</span>
                </div>
                <div class="flex-c">
                  <el-button
                    v-if="!node.data.leaf"
                    type="primary"
                    link
                    :icon="Plus"
                    @click.stop="openAddBusiRecord(node)"
                  ></el-button>
                  <el-button
                    type="primary"
                    link
                    :icon="Edit"
                    @click.stop="openEditRecord(node.data)"
                  ></el-button>
                  <el-button
                    :disabled="node.childNodes.length > 0"
                    :type="node.childNodes.length > 0 ? 'info' : 'danger'"
                    link
                    :icon="Remove"
                    @click.stop="handleDeleteRecord(node.data)"
                  ></el-button>
                  <!--                  <el-dropdown-->
                  <!--                    placement="bottom"-->
                  <!--                    @command="val => handleCommand(val, node)"-->
                  <!--                  >-->
                  <!--                    <el-button-->
                  <!--                      type="primary"-->
                  <!--                      link-->
                  <!--                      style="margin-right: 15px"-->
                  <!--                      :icon="useRenderIcon('EP-MoreFilled')"-->
                  <!--                    ></el-button>-->
                  <!--                    <template #dropdown>-->
                  <!--                      <el-dropdown-menu>-->
                  <!--                        <el-dropdown-item v-if="!data.leaf" command="add">-->
                  <!--                          <div style="width: 100%; text-align: center">-->
                  <!--                            添加-->
                  <!--                          </div>-->
                  <!--                        </el-dropdown-item>-->
                  <!--                        <el-dropdown-item-->
                  <!--                          :disabled="!node.isLeaf"-->
                  <!--                          command="del"-->
                  <!--                        >-->
                  <!--                          <el-button-->
                  <!--                            type="danger"-->
                  <!--                            link-->
                  <!--                            style="width: 100%; text-align: center"-->
                  <!--                            >删除-->
                  <!--                          </el-button>-->
                  <!--                        </el-dropdown-item>-->
                  <!--                      </el-dropdown-menu>-->
                  <!--                    </template>-->
                  <!--                  </el-dropdown>-->
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1 p-5">
          <template v-if="!!state.nodeData">
            <div class="flex gap-8">
              <div>
                <span style="font-size: 0.9em">名称: </span>
                <span style="font-weight: bold; margin-left: 10px">{{
                  state.nodeData.name
                }}</span>
              </div>
              <div>
                <span style="font-size: 0.9em">编码: </span>
                <span style="font-weight: bold; margin-left: 10px">{{
                  state.nodeData.code
                }}</span>
              </div>
            </div>
            <el-divider></el-divider>
          </template>
          <im-table
            v-if="state.record.leaf"
            :columns="state.definedColumns"
            :data="state.tableColumns"
            :toolbar="{ reload: false, setting: false }"
            :sortable="{
              dragIgnoreSelector: 'input'
            }"
            operator
            center
            height="calc(100vh - 210px)"
            @cell-dblclick="handleCellDblClick"
            @cell-click="handleCellClick"
          >
            <template #toolbar-left="toolbarSlot: ImToolbarSlot">
              <el-button
                :icon="Checked"
                type="success"
                @click="handleTableColumnsSave"
                >保存</el-button
              >
              <el-button
                type="primary"
                :icon="Edit"
                @click="state.editing = !state.editing"
                >批量编辑</el-button
              >
              <el-button
                :disabled="toolbarSlot.checkedRows.length == 0"
                :icon="Remove"
                type="danger"
                @click="handleTableColumnsDelete(toolbarSlot.checkedRows)"
                >批量移除</el-button
              >
              <el-button
                type="info"
                :icon="Refresh"
                @click="handleColumnsReset"
                title="还原上一次保存结果"
                >还原</el-button
              >
            </template>

            <template #toolbar-right="toolbarSlot: ImToolbarSlot">
              <el-button
                :icon="Plus"
                type="success"
                @click="handleAddTableColumn"
                >添加字段</el-button
              >
              <el-button :icon="Download" type="primary" @click="handleExport"
                >字段导出</el-button
              >
              <el-button
                :icon="UploadFilled"
                type="primary"
                @click="handleImport"
                >字段导入</el-button
              >
              <input
                ref="fileInput"
                style="display: none"
                type="file"
                accept=".json,.txt"
                @change="onFileInputChange"
              />
            </template>

            <template #label="{ row, $index }">
              <el-input
                v-if="isEditing($index)"
                v-model="row.label"
                placeholder="请输入名称"
                clearable
              ></el-input>
              <span v-else>{{ row.label }}</span>
            </template>

            <template #prop="{ row, $index }">
              <el-input
                v-if="isEditing($index)"
                v-model="row.prop"
                placeholder="请输入编码"
                clearable
              ></el-input>
              <span v-else>{{ row.prop }}</span>
            </template>

            <template #width="{ row }">
              <el-input-number
                v-if="row"
                :min="0"
                :step="10"
                v-model="row.width"
                placeholder="默认分配"
              ></el-input-number>
            </template>

            <template #hidden="{ row }">
              <el-checkbox v-if="row" v-model="row.hidden"></el-checkbox>
            </template>

            <template #filterable="{ row }">
              <el-checkbox v-if="row" v-model="row.filterable"></el-checkbox>
            </template>

            <template #sortable="{ row }">
              <el-checkbox v-if="row" v-model="row.sortable"></el-checkbox>
            </template>

            <template #showOverflowTooltip-header>
              <el-tooltip
                content="当内容过长被隐藏时显示 tooltip"
                placement="top"
              >
                <span>悬停提示</span>
              </el-tooltip>
            </template>

            <template #showOverflowTooltip="{ row }">
              <el-checkbox
                v-if="row"
                v-model="row.showOverflowTooltip"
              ></el-checkbox>
            </template>

            <template #unit="{ row, $index }">
              <div v-if="row.meta">
                <el-input
                  v-if="isEditing($index)"
                  v-model="row.meta.unit"
                  placeholder="单位"
                  clearable
                ></el-input>
                <span v-else>{{ row.meta.unit }}</span>
              </div>
            </template>

            <template #operator="{ column, row, $index }: any">
              <div>-</div>
              <!--              <el-button-->
              <!--                type="primary"-->
              <!--                link-->
              <!--                @click="ElMessage.info('开发中...')"-->
              <!--                >组件配置</el-button-->
              <!--              >-->
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>

    <el-dialog
      v-model="state.dialogVisible"
      :before-close="handleDialogClose"
      :title="state.dialogTitle"
    >
      <el-form ref="formEl" label-position="top" :model="state.record">
        <el-form-item
          label="名称"
          prop="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <el-input
            v-model="state.record.name"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          v-if="state.record.leaf"
          label="编码"
          prop="code"
          :rules="[{ required: true, message: '请输入编码' }]"
        >
          <el-input
            v-model="state.record.code"
            placeholder="请输入编码,编码不能重复"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="state.record.leaf" label="所在目录" prop="parentId">
          <el-select
            disabled
            v-model="state.record.parentId"
            placeholder="请选择目录"
          >
            <el-option
              v-for="(dirOption, index) in dirOptions"
              :key="index"
              :label="dirOption.name"
              :value="dirOption.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderVal">
          <el-input-number
            v-model="state.record.orderVal"
            placeholder="请输入排序编号"
          >
          </el-input-number>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input
            type="textarea"
            v-model="state.record.note"
            placeholder="请输入备注"
          >
          </el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button type="primary" @click="handleDirOrTableSave">确定</el-button>
        <el-button @click="handleDialogClose">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref
} from "vue";
import {
  Document,
  Edit,
  Folder,
  Plus,
  Checked,
  Remove,
  Refresh,
  UploadFilled,
  Download
} from "@element-plus/icons-vue";
import { ElForm, ElFormItem, ElMessage } from "element-plus";
import {
  delTableById,
  queryTableTree,
  saveOrEditTableTree
} from "@/views/system/columnmanage/api";
import { v4 } from "uuid";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import { ImToolbarSlot, IndexRender } from "@/components/ItsmCommon";
import { downloadText, readTextFile } from "@/utils/exportTextFile";

const { $confirm } = getCurrentInstance().appContext.config.globalProperties;
const columnDirTreeRef = ref();
const formEl = ref();
const fileInput = ref<HTMLElement>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});
const defaultTreeProps = {
  children: "children",
  label: "name"
};

const state = reactive({
  // 搜索
  searchKey: "",
  // 字段管理树结构
  dirTableTreeData: [],
  currentNodeKey: "",

  // 当前操作对象
  nodeData: null,
  record: {
    id: "",
    leaf: false,
    name: "",
    code: "",
    orderVal: 0,
    parentId: "",
    note: "",
    columnsJson: "[]"
  },
  dialogVisible: false,
  dialogTitle: "添加目录",
  // 表格字段
  definedColumns: [
    {
      label: "选择",
      type: "selection",
      width: 60
    },
    {
      label: "序号",
      render: IndexRender,
      width: 75
    },
    {
      label: "字段名称",
      prop: "label",
      align: "center"
    },
    {
      label: "字段编码",
      prop: "prop",
      align: "center"
    },
    {
      label: "宽度(px)",
      prop: "width",
      align: "center"
    },
    {
      label: "过滤",
      prop: "filterable",
      align: "center"
    },
    {
      label: "隐藏",
      prop: "hidden",
      align: "center"
    },
    {
      label: "悬停提示",
      prop: "showOverflowTooltip",
      align: "center"
    },
    {
      label: "排序",
      prop: "sortable",
      align: "center"
    },
    {
      label: "单位",
      prop: "unit",
      width: 120,
      align: "center"
    }
  ],
  // 当前操作的业务的字段列表
  tableColumns: [],
  editing: false,
  editingIndex: -1
});

/**
 * 目录下拉
 */
const dirOptions = computed(() => {
  return state.dirTableTreeData.filter(data => !data.leaf);
});

const filterTree = () => {
  columnDirTreeRef.value.filter(state.searchKey);
};

const handleFilterNode = (value: string, data) => {
  if (!value) return true;
  return data.name.indexOf(value) > -1 || data.code.indexOf(value) > -1;
};

const clearRight = () => {
  state.record = {
    leaf: false
  } as any;
};

// 添加目录
const addFolder = () => {
  state.record = {
    id: v4(),
    leaf: false,
    name: "",
    code: v4(),
    orderVal: dirOptions.value.length,
    parentId: "",
    note: "",
    columnsJson: ""
  };
  state.dialogTitle = "添加目录";
  state.dialogVisible = true;
  formEl.value.resetFields();
};

const openEditRecord = data => {
  state.record = { ...data };
  state.dialogTitle = "编辑" + (data.leaf ? "表格信息" : "目录");
  state.dialogVisible = true;
};

const openAddBusiRecord = node => {
  state.record = {
    id: v4(),
    leaf: true,
    name: "",
    code: "",
    orderVal: node.childNodes.length,
    parentId: node.data.id,
    note: "",
    columnsJson: ""
  };
  state.dialogTitle = "添加表格信息";
  state.dialogVisible = true;
  formEl.value.resetFields();
};

const handleDeleteRecord = data => {
  $confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    delTableById(data.id).then(res => {
      let { status, msg } = res;
      if (status == 0) {
        ElMessage.success("删除成功");
        queryLeftTree();
        clearRight();
      } else {
        ElMessage.error(msg);
      }
    });
  });
};

const handleColumnsReset = () => {
  // reset
  state.tableColumns = [];
  try {
    if (state.record.columnsJson) {
      state.tableColumns = JSON.parse(state.record.columnsJson);
    }
  } catch (e) {}
  state.editingIndex = -1;
  state.editing = false;
};

const nodeClick = data => {
  state.nodeData = data;
  state.record = { ...data };
  state.currentNodeKey = data.id;
  if (data.leaf) {
    handleColumnsReset();
  }
};

const handleCellClick = row => {
  let ediingIndex = state.editingIndex;
  let editRow = state.tableColumns[ediingIndex];
  if (row != editRow) {
    state.editingIndex = -1;
  }
};

const handleCellDblClick = row => {
  state.editingIndex = state.tableColumns.indexOf(row);
};

const handleDirOrTableSave = () => {
  formEl.value.validate(ok => {
    if (ok) {
      saveOrEditTableTree(state.record).then(res => {
        let { status, msg } = res;
        if (status == 0) {
          ElMessage.success("保存成功");
          if (state.nodeData && state.nodeData.id == state.record.id) {
            Object.assign(state.nodeData, state.record);
          }
          queryLeftTree(true);
          handleDialogClose();
        } else {
          ElMessage.error(msg);
        }
      });
    } else {
      ElMessage.error("校验不通过");
    }
  });
};

const handleDialogClose = () => {
  state.dialogVisible = false;
  formEl.value.resetFields();
};

// 加载左侧树
const queryLeftTree = (keepCurrentNode?: boolean) => {
  // 查询列表
  queryTableTree().then(res => {
    state.dirTableTreeData = res.data || [];
    if (!keepCurrentNode) {
      let node = state.dirTableTreeData[0];
      if (node) {
        let expandKeys = [];
        let lastLeafData = null;
        while (Array.isArray(node.children) && node.children.length > 0) {
          expandKeys.push(node.id);
          node = node.children[0];
          lastLeafData = node;
        }
        // 手动控制选择第一个节点
        nextTick(() => {
          if (lastLeafData) {
            nodeClick(lastLeafData);
          }
        });
      }
    }
  });
};

const isEditing = index => {
  return state.editing || index == state.editingIndex;
};

const handleAddTableColumn = () => {
  state.tableColumns.push({
    id: v4(),
    label: "新的字段",
    prop: "",
    hidden: false,
    meta: {
      unit: ""
    }
  });
};

const handleTableColumnsSave = () => {
  // 校验字段是否存在空
  let propList = [];
  let i = 0;
  for (let col of state.tableColumns) {
    ++i;
    let { prop, label } = col;
    if (!prop || !label) {
      ElMessage.error(`第${i}行字段名称或者编码为空！`);
      return;
    }
    if (propList.includes(prop)) {
      ElMessage.error(`第${i}行字段编码重复！`);
      return;
    }
    propList.push(prop);
  }

  state.editingIndex = -1;
  state.editing = false;
  // 设置
  state.record.columnsJson = JSON.stringify(state.tableColumns);
  // 调用后台
  saveOrEditTableTree(state.record).then(res => {
    let { status, msg } = res;
    if (status == 0) {
      ElMessage.success("保存成功!");
      queryLeftTree(true);
    } else {
      ElMessage.error(msg);
    }
  });
};

const handleTableColumnsDelete = rows => {
  state.tableColumns = state.tableColumns.filter(col => !rows.includes(col));
};

const handleExport = () => {
  if (state.tableColumns.length == 0) {
    ElMessage.warning("字段为空");
    return;
  }
  let columnsJson = JSON.stringify(state.tableColumns, null, 4);
  downloadText(columnsJson, state.record.name + ".json");
};

const onFileInputChange = evt => {
  let files = evt.target.files;
  if ((files?.length || 0) == 0) {
    ElMessage.warning("文件列表为空");
    return;
  }
  if (files && files.length > 0) {
    let file = files[0];
    readTextFile(file, json => {
      try {
        JSON.parse(json);
        $confirm("导入会覆盖当前字段列表，确定要导入吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          state.record.columnsJson = json;
          handleColumnsReset();
        });
      } catch (e) {
        console.log(e, json);
        ElMessage.error("文件内容错误");
      }
    });
  }
  // clear file area values
  evt.target.value = null;
};

const handleImport = () => {
  fileInput.value.click();
};

onMounted(() => {
  queryLeftTree();
});
</script>
<style scoped lang="scss"></style>
