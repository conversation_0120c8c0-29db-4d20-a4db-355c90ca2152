import { http } from "@/utils/http";

/**
 * 获取表格维护列表
 */
export const queryTableTree = () => {
  return http.postJson<any>(`/public-config/tableColInfo/tree`, {});
};

/**
 * 保存当前操作的目录或者表格
 */
export const saveOrEditTableTree = record => {
  console.log("saveRecord", record);
  return http.postJson<any>(`/public-config/tableColInfo/saveOrUpdate`, record);
};

/**
 * 根据编码查询表格字段信息
 */
export const queryTableSet = (code, instance) => {
  return new Promise(resolve => {
    http
      .postJson<any>(`/public-config/tableColInfoCustom/query`, {
        tableCode: code,
        instance
      })
      .then(res => {
        try {
          let { tableColInfo = {}, tableColInfoCustom = {} } = res.data || {};
          let columnsJson = tableColInfo.columnsJson;
          let { compAttrJson } = tableColInfoCustom || {};
          resolve({
            // 全量的字段
            columns: JSON.parse(columnsJson || "[]"),
            // 个性化设置字段属性信息
            customTableSet: JSON.parse(compAttrJson || "{}"),
            // 个性化设置行信息
            customTableInfo: tableColInfoCustom || {}
          });
        } catch (e) {
          console.error("返回的数据错误", res, e);
          resolve({
            columns: [],
            customSet: {},
            customInfo: null
          });
        }
      })
      .catch(err => {
        console.error("返回的数据错误", err);
        resolve({
          columns: [],
          customSet: {},
          customInfo: null
        });
      });
  });
};

/**
 * 保存个性化设置的内容
 */
export const saveTableSet = tableSet => {
  let { tableCode, instance, customTableSet, customTableInfo } = tableSet || {};
  if (!customTableInfo) {
    console.warn("customTableInfo is null while saveTableSet");
    return;
  }

  customTableInfo.compAttrJson = JSON.stringify(customTableSet);
  customTableInfo.instance = instance;
  customTableInfo.tableCode = tableCode;
  return http.postJson<any>(
    `/public-config/tableColInfoCustom/saveOrUpdate`,
    customTableInfo
  );
};

/**
 * 删除表格字段信息
 */
export const delTableById = id => {
  return http.get<any, any>(`/public-config/tableColInfo/del?id=${id || ""}`);
};
