<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3">归属租户管理 - {{ props.user?.realName }} </span>
        </template>
      </el-page-header>
    </template>
    <div class="flex-sc space-x-2">
      <el-check-tag
        v-for="(item, index) in checkerData"
        :checked="item.checked"
        @change="checkHandler(item)"
        :key="index"
      >
        {{ item.tenantName }}
      </el-check-tag>
    </div>
    <template #footer>
      <div class="flex justify-end items-center">
        <el-button
          type="primary"
          :icon="useRenderIcon('EP-CircleCheck')"
          @click="saveHandler"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch, getCurrentInstance } from "vue";
import { ResultStatus } from "@/utils/http/types";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  TenantCheckerForUser,
  getTenantCheckerData,
  saveUserTenantCheckerData
} from "@/views/system/tenantmanage/api/TenantManageApi";

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  user: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  checkerData: [] as Array<TenantCheckerForUser>
});
const { checkerData, drawerVisible } = toRefs(state);

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }

  loadCheckerData();
});

//加载租户归属信息
const loadCheckerData = () => {
  getTenantCheckerData(props.user.userName).then(res => {
    if (res.status == ResultStatus.Success) {
      state.checkerData = res.data;
    }
  });
};

//选择状态变更
const checkHandler = (checker: TenantCheckerForUser) => {
  checker.checked = !checker.checked;
};

//保存归属信息
const saveHandler = () => {
  $confirm("确定要保存当前的归属租户信息么？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const checkedTenant = state.checkerData.filter(c => c.checked);
    saveUserTenantCheckerData({
      userName: props.user.userName,
      checkers: checkedTenant
    }).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功保存归属租户信息！",
          type: "success"
        });
        cancel();
      }
    });
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
