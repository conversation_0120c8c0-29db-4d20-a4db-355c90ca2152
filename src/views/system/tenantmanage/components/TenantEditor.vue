<template>
  <div>
    <avue-form ref="editFormRef" :option="option" v-model="tForm" />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref } from "vue";
import { validSignId } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";
import {
  TenantInfo,
  save
} from "@/views/system/tenantmanage/api/TenantManageApi";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;

//avue-form Ref
const editFormRef = ref();

//租户标识校验
const validateTenantId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：hq_tenant"));
  }
};

//组件属性
const props = withDefaults(defineProps<TenantInfo>(), {
  id: "",
  tenantId: "",
  tenantName: "",
  tenantDesc: ""
});
const tForm = ref(props);

//声明事件
const emit = defineEmits(["submit"]);

//avue-form组件属性
const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "租户标识",
      prop: "tenantId",
      disabled: props.id ? true : false,
      maxlength: 32,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入租户标识",
          trigger: "blur"
        },
        { validator: validateTenantId, trigger: "blur" }
      ]
    },
    {
      label: "租户名称",
      prop: "tenantName",
      maxlength: 32,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入租户姓名",
          trigger: "blur"
        }
      ]
    },
    {
      label: "租户描述",
      prop: "tenantDesc",
      type: "textarea",
      minRows: 3,
      maxRows: 5,
      span: 24,
      maxlength: 200,
      showWordLimit: true
    }
  ]
});

function submitData() {
  editFormRef.value.validate((valid, done) => {
    if (valid) {
      save(tForm.value).then(result => {
        if (result.status === ResultStatus.Success) {
          $notify({
            title: "提示",
            message: "已成功保存租户信息！",
            type: "success"
          });
          emit("submit", true);
        }
        done();
      });
    }
  });
}

defineExpose({ submitData });
</script>
