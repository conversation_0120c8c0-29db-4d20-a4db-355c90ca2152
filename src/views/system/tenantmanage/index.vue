<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div class="pb-1.5 ml-1">
          <el-input
            placeholder="租户名称"
            clearable
            :suffix-icon="useRenderIcon('EP-Search')"
            v-model="tenantKeyWord"
          >
            <template #prepend>
              <el-button
                type="primary"
                :icon="useRenderIcon('RI-AddLine')"
                @click="addTenant"
                v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
              >
                添加
              </el-button>
            </template>
          </el-input>
        </div>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color"
        >
          <el-tree
            ref="tenantTree"
            :data="tenantData"
            node-key="tenantId"
            :expand-on-click-node="false"
            highlight-current
            :props="tenantTreeProps"
            :filter-node-method="filterTenant"
            @current-change="tenantSelectChange"
          >
            <template #default="{ node, data }">
              <div class="tree-list-content">
                <div class="tree-node-left">
                  <IconifyIconOffline icon="EP-Avatar"/>
                  <span>{{ node.label }}</span>
                </div>

                <div
                  class="space-x-2"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                >
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-EditBoxFill')"
                    @click.stop="openEditDialog(data)"
                  />
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-DeleteBin6Fill')"
                    @click.stop="deleteTenantInfo(data)"
                  />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1">
          <avue-crud
            :data="userData"
            :option="userTableOption"
            :table-loading="loading"
            v-model:page="userTablePage"
            @refresh-change="loadTenantUserData"
            @current-change="loadTenantUserData"
            @size-change="loadTenantUserData"
            @row-del="removeUser"
          >
            <template #enableSwitch="scope">
              <el-tag
                effect="dark"
                :type="scope.row.enableSwitch == '1' ? 'success' : 'danger'"
              >
                {{ scope.row.enableSwitch == "1" ? "启用" : "禁用" }}
              </el-tag>
            </template>
            <template #menu-left="{}">
              <el-button
                type="primary"
                :icon="useRenderIcon('RI-UserAddFill')"
                @click="openAddUserDialog"
                v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
              >
                添加用户
              </el-button>
            </template>
            <template #menu-right="{ size }">
              <div class="float-left pr-3">
                <el-input
                  clearable
                  placeholder="用户账号/姓名"
                  v-model="userSearchCondition.userKeyWord"
                  :size="size"
                  @keyup.enter.prevent="userSearchHandler"
                  @blur="
                    userSearchCondition.userKeyWord = $event.target.value.trim()
                  "
                >
                  <template #append>
                    <el-button
                      :icon="useRenderIcon('EP-Search')"
                      @click.stop="userSearchHandler"
                    />
                  </template>
                </el-input>
              </div>
            </template>
          </avue-crud>
        </div>
      </template>
    </splitpane>
  </div>
</template>

<script lang="ts" setup>
import splitpane, {ContextProps} from "@/components/ReSplitPane";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {ElTree} from "element-plus";
import {addDialog, closeAllDialog} from "@/components/ReDialog";
import TenantEditor from "@/views/system/tenantmanage/components/TenantEditor.vue";
import UserSearchSelector from "@/views/system/usermanage/components/UserSearchSelector.vue";
import {AdminEnum} from "@/utils/CommonTypes";
import {hasAuth} from "@/router/utils";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {computed, getCurrentInstance, h, nextTick, reactive, ref, toRefs, watch} from "vue";
import {
  addUserRelations,
  deleteTenant,
  getAllSystemTenantVoList,
  removeUserRelation,
  searchUserInfo,
  TenantInfo,
  TenantUserInfo
} from "@/views/system/tenantmanage/api/TenantManageApi";

const {$notify, $confirm, $message} =
  getCurrentInstance().appContext.config.globalProperties;

defineOptions({
  name: "SystemConfig_tenant_manage"
});

//租户列表Ref
const tenantTree = ref<InstanceType<typeof ElTree>>();

//租户信息编辑会话Ref
const editTenantRef = ref<InstanceType<typeof TenantEditor>>();

//用户选择器Ref
const userSelectRef = ref<InstanceType<typeof UserSearchSelector>>();

//页面部署配置
const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

//租户列表配置
const tenantTreeProps = {
  label: "tenantName"
};

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 200;
});
const userTableOption = reactive({
  align: "center",
  menuAlign: "center",
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  index: true,
  border: true,
  stripe: true,
  rowKey: "userId",
  height: tableHeight,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {label: "用户账号", prop: "userName"},
    {label: "姓名", prop: "realName"},
    {label: "所属组织", prop: "deptName"},
    {label: "所属角色", prop: "roleNames"},
    {label: "状态", prop: "enableSwitch"}
  ]
});

//数据对象
const state = reactive({
  tenantData: [] as Array<TenantInfo>,
  userData: [] as Array<TenantUserInfo>,
  userTablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  userSearchCondition: {
    tenantId: "",
    userKeyWord: "",
    pageNum: 1,
    pageSize: defaultPageSize
  },
  selectedTenant: {} as TenantInfo,
  tenantKeyWord: "",
  loading: false
});
const {
  tenantData,
  userData,
  userTablePage,
  loading,
  userSearchCondition,
  tenantKeyWord
} = toRefs(state);

watch(tenantKeyWord, value => tenantTree.value!.filter(value));

//加载租户数据
const loadTenantData = async () => {
  getAllSystemTenantVoList().then(result => {
    state.tenantData = result.data;

    //默认选中第一个
    if (state.tenantData && state.tenantData.length > 0) {
      const firstTenant = state.tenantData[0];
      nextTick(() => {
        tenantTree.value!.setCurrentKey(firstTenant.tenantId);
      });
    } else {
      tenantTree.value!.setCurrentKey(null);
    }
  });
};
loadTenantData();

//加载用户数据
const loadTenantUserData = async () => {
  state.userSearchCondition.pageNum = state.userTablePage.currentPage;
  state.userSearchCondition.pageSize = state.userTablePage.pageSize;
  state.loading = true;
  await searchUserInfo(state.userSearchCondition).then(result => {
    state.userData = result.data;
    state.userTablePage.total = parseInt(result.total);
  });
  state.loading = false;
};

//租户选择改变
const tenantSelectChange = (tenant: TenantInfo) => {
  state.selectedTenant = tenant;
  state.userSearchCondition.tenantId = tenant.tenantId;
  state.userTablePage.currentPage = 1;
  loadTenantUserData();
};

//新增租户
const addTenant = () => {
  openEditDialog({
    id: "",
    tenantId: "",
    tenantName: "",
    tenantDesc: ""
  } as TenantInfo);
};

//打开编辑窗口
const openEditDialog = (tenant: TenantInfo) => {
  addDialog({
    title: tenant.id ? "编辑租户" : "新增租户",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: tenant,
    contentRenderer: () =>
      h(TenantEditor, {
        ref: editTenantRef,
        onSubmit: (res: boolean) => {
          if (res) {
            closeAllDialog();
            loadTenantData();
          }
        }
      }),
    beforeSure: () => {
      editTenantRef.value.submitData();
    }
  });
};

//删除租户
const deleteTenantInfo = (tenant: TenantInfo) => {
  $confirm(`确定要删除 '${tenant.tenantName}' 么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteTenant(tenant.id).then(() => {
      $notify({
        title: "提示",
        message: "已成功删除租户信息！",
        type: "success"
      });
      loadTenantData();
    });
  });
};

//打开添加用户窗口
const openAddUserDialog = () => {
  addDialog({
    title: "添加用户",
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: () => h(UserSearchSelector, {ref: userSelectRef}),
    beforeSure: done => {
      const selUsers = userSelectRef.value.getSelectedUsers();
      if (selUsers.length > 0) {
        const userList = [];
        selUsers.forEach(u => {
          userList.push(u.userName);
        });

        addUserRelations({
          tenantId: state.userSearchCondition.tenantId,
          userNameList: userList
        }).then(() => {
          $notify({
            message: "已成功添加用户！",
            type: "success"
          });
          state.userTablePage.currentPage = 1;
          loadTenantUserData();
        });
        done();
      } else {
        $message({
          message: "请至少选择一个您要添加的用户！",
          type: "warning"
        });
      }
    }
  });
};

//移除用户
const removeUser = (row: TenantUserInfo) => {
  $confirm(
    `确定要将用户 '${row.realName}' 从 '${state.selectedTenant.tenantName}' 中删除么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    removeUserRelation(state.userSearchCondition.tenantId, row.userName).then(
      () => {
        $notify({
          message: "已成功移除用户！",
          type: "success"
        });
        loadTenantUserData();
      }
    );
  });
};

//用户搜索
const userSearchHandler = () => {
  state.userTablePage.currentPage = 1;
  loadTenantUserData();
};

//租户列表搜索
const filterTenant = (value: string, data: TenantInfo) => {
  if (!value) return true;
  return data.tenantName.includes(value);
};
</script>

<style scoped>
.h-content {
  height: calc(100% - 48px);
}
</style>
