import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage/tenant`;

//租户信息
export type TenantInfo = {
  id: string;
  tenantId: string;
  tenantName: string;
  tenantDesc: string;
};

//租户下的用户信息
export type TenantUserInfo = {
  userId: string;
  userName: string;
  realName: string;
  tenantId: string;
  deptName: string;
  roleNames: string;
  enableSwitch: string;
};

//租户选择信息-用于维护某个用户的租户归属
export type TenantCheckerForUser = {
  userName: string;
  tenantId: string;
  tenantName: string;
  checked: boolean;
};

//查询租户下的用户信息
const getTenantUserList = (tenantId: string) => {
  return http.get<any, RestResult<Array<TenantUserInfo>>>(
    `${basePath}/getTenantUserList/${tenantId}`
  );
};

//查询租户信息
const getAllSystemTenantVoList = () => {
  return http.get<any, RestResult<Array<TenantInfo>>>(
    `${basePath}/getAllSystemTenantVoList`
  );
};

//保存租户信息
const save = (tenant: TenantInfo) => {
  return http.postJson<RestResult<string>>(
    `${basePath}/saveSystemTenant`,
    tenant
  );
};

//删除租户信息
const deleteTenant = (id: string) => {
  return http.get<any, RestResult<string>>(
    `${basePath}/deleteSystemTenant?id=${id}`
  );
};

//查询用户信息
const searchUserInfo = (params: any) => {
  return http.postJson<RestPageResult<Array<TenantUserInfo>>>(
    `${basePath}/searchUserInfo`,
    params
  );
};

//添加用户到租户
const addUserRelations = (params: any) => {
  return http.postJson<RestResult<string>>(
    `${basePath}/addUserToTenant`,
    params
  );
};

//从租户下移除用户
const removeUserRelation = (tenantId: string, userName: string) => {
  return http.get<any, RestResult<string>>(
    `${basePath}/removeUserFromTenant?tenantId=${tenantId}&userName=${userName}`
  );
};

//查询指定用户的租户归属选择信息
const getTenantCheckerData = (userName: string) =>
  http.get<string, RestResult<Array<TenantCheckerForUser>>>(
    `${basePath}/getTenantCheckerData?userName=${userName}`
  );

//保存指定用户的租户归属数据
const saveUserTenantCheckerData = (params: any) =>
  http.postJson<RestResult<string>>(
    `${basePath}/saveUserTenantCheckerData`,
    params
  );

export {
  getTenantUserList,
  getAllSystemTenantVoList,
  save,
  deleteTenant,
  searchUserInfo,
  addUserRelations,
  removeUserRelation,
  getTenantCheckerData,
  saveUserTenantCheckerData
};
