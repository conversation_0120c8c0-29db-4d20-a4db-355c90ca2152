<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div class="pb-1.5 ml-1">
          <el-input
            placeholder="组织名称"
            :suffix-icon="useRenderIcon('EP-Search')"
            clearable
            v-model="deptKeyWord"
          />
        </div>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color"
        >
          <el-tree
            ref="deptTree"
            :data="deptData"
            :props="treeProps"
            :load="loadNode"
            lazy
            node-key="deptId"
            :render-after-expand="false"
            :expand-on-click-node="false"
            highlight-current
            :default-expanded-keys="['-1']"
            :filter-node-method="filterDeptNode"
            @current-change="deptSelectChange"
          />
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1">
          <avue-crud
            ref="userCrudRef"
            :data="userData"
            :option="userTableOption"
            v-model:page="userTablePage"
            v-model="user"
            :before-open="beforeOpenUser"
            :table-loading="loading"
            @refresh-change="searchUserData"
            @current-change="searchUserData"
            @size-change="searchUserData"
            @row-save="addUserSave"
            @row-update="updateUserInfo"
            @sort-change="sortChangeHandler"
          >
            <template #realName="{ row, index, size }">
              <div class="flex-sc space-x-2">
                <el-avatar
                  v-if="row.userAvatar != null && row.userAvatar.length > 0"
                  :src="'/avatar/' + row.userAvatar"
                  size="small"
                />
                <el-avatar v-else size="small">
                  {{ row.realName != null ? row.realName.split("")[0] : "" }}
                </el-avatar>
                <el-link
                  plain
                  :underline="false"
                  :size="size"
                  type="primary"
                  v-if="
                    hasAuth([
                      'system:user:detail-view',
                      AdminEnum.SuperAdmin,
                      AdminEnum.SystemAdmin
                    ])
                  "
                  @click="userCrudRef!.rowView(row, index)"
                >
                  {{ row.realName }}
                </el-link>
                <span v-else>{{ row.realName }}</span>
              </div>
            </template>
            <template #roleNames="scope">
              {{ scope.row.roleNames && scope.row.roleNames.join(",") }}
            </template>
            <template #tenantNames="scope">
              {{ scope.row.tenantNames && scope.row.tenantNames.join(",") }}
            </template>
            <template #enableSwitch="{ row }">
              <el-switch
                v-model="row.enableSwitch"
                active-value="1"
                inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
                class="enable-switch-color"
                @change="enableChangeHandler(row)"
              />
            </template>
            <template #enableSwitch-form="{ type, disable }">
              <el-switch
                v-model="user.enableSwitch"
                active-value="1"
                inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
                :disabled="disable || type === 'view'"
                class="enable-switch-color"
              />
            </template>
            <template #userTypeName-form="{ type, disable }">
              <span v-if="type === 'view'">{{ user.userTypeName }}</span>
              <el-radio-group
                v-else
                :disabled="disable"
                v-model="user.userTypeSign"
              >
                <el-radio-button :label="1">普通用户</el-radio-button>
                <el-radio-button :label="2">第三方用户</el-radio-button>
              </el-radio-group>
            </template>
            <template #gender-form="{ type, disable }">
              <span v-if="type === 'view'">{{
                  user.gender === "male" ? "男" : "女"
                }}</span>
              <el-radio-group v-else :disabled="disable" v-model="user.gender">
                <el-radio-button label="male">男</el-radio-button>
                <el-radio-button label="female">女</el-radio-button>
              </el-radio-group>
            </template>
            <template #deptId-form="{ type, disable }">
              <span v-if="type === 'view'">{{ user.deptName }}</span>
              <DeptTreeSelect
                v-else
                :disabled="disable"
                v-model:dept-id="user.deptId"
                :dept-name="user.deptName"
                class="w-full"
              />
            </template>
            <template #menu-right="{ size }">
              <div class="float-left pr-3">
                <el-input
                  clearable
                  placeholder="用户账号/姓名"
                  v-model="searchCondition.keyWord"
                  :size="size"
                  @keyup.enter.prevent="searchFirstPageData()"
                  @blur="searchCondition.keyWord = $event.target.value.trim()"
                >
                  <template #append>
                    <el-button
                      :icon="useRenderIcon('EP-Search')"
                      @click.stop="searchFirstPageData()"
                    />
                  </template>
                </el-input>
              </div>
              <el-tooltip
                content="导出用户数据"
                placement="top"
                :open-delay="1000"
              >
                <el-button
                  :icon="useRenderIcon('EP-Download')"
                  circle
                  :size="size"
                  :disabled="userData.length < 1"
                  v-auth="['system:user:export', AdminEnum.SuperAdmin]"
                  @click="exportHandler"
                />
              </el-tooltip>
            </template>
            <template #menu="{ row, size }">
              <el-dropdown
                class="pl-2"
                v-if="
                  hasAuth([
                    'system:user:del',
                    'system:user:permission',
                    AdminEnum.SuperAdmin,
                    AdminEnum.SystemAdmin
                  ])
                "
              >
                <span class="dd-dropdown-link-list">
                  更多
                  <IconifyIconOffline icon="EP-ArrowDown"/>
                </span>
                <template #dropdown>
                  <div class="pt-2 pb-2">
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-if="
                          hasAuth([
                            'system:user:permission',
                            AdminEnum.SuperAdmin,
                            AdminEnum.SystemAdmin
                          ])
                        "
                      >
                        <el-link
                          plain
                          :underline="false"
                          :size="size"
                          type="primary"
                          :icon="useRenderIcon('RI-UserSettingsFill')"
                          @click="userPermHandler(row)"
                        >
                          用户权限
                        </el-link>
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="
                          hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin])
                        "
                      >
                        <el-link
                          plain
                          :underline="false"
                          :size="size"
                          type="primary"
                          :icon="useRenderIcon('RI-LockPasswordFill')"
                          @click="resetUserPwdHandler(row)"
                        >
                          重置密码
                        </el-link>
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="
                          hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin])
                        "
                      >
                        <el-link
                          plain
                          :underline="false"
                          :size="size"
                          type="primary"
                          :icon="useRenderIcon('RI-ContactsLine')"
                          @click="tenantManageHandler(row)"
                        >
                          归属租户
                        </el-link>
                      </el-dropdown-item>
                      <el-dropdown-item
                        v-if="
                          hasAuth([
                            'system:user:del',
                            AdminEnum.SuperAdmin,
                            AdminEnum.SystemAdmin
                          ])
                        "
                      >
                        <el-link
                          plain
                          :underline="false"
                          :size="size"
                          type="danger"
                          :icon="useRenderIcon('EP-DeleteFilled')"
                          @click="deleteUserHandler(row)"
                        >
                          删除用户
                        </el-link>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </div>
                </template>
              </el-dropdown>
            </template>
          </avue-crud>
        </div>
      </template>
    </splitpane>

    <!--重置密码对话框-->
    <reset-user-pwd
      v-model:visible="resetPwdVisible"
      :user="currentOperateUser"
    />

    <!-- 用户权限抽屉 -->
    <permission-for-user
      v-model:visible="userPermissionVisible"
      :user="currentOperateUser"
      size="35%"
    />

    <!-- 归属租户管理抽屉 -->
    <tenant-manage-for-user
      v-model:visible="tenantManageVisible"
      :user="currentOperateUser"
    />
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import splitpane, {ContextProps} from "@/components/ReSplitPane";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {ElTree} from "element-plus";
import {useDeptStoreHook} from "@/store/modules/dept";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import {SimpleDeptInfo} from "@/views/system/deptmanage/api/DeptManageApi";
import {AdminEnum} from "@/utils/CommonTypes";
import {hasAuth} from "@/router/utils";
import {message} from "@/utils/message";
import {ResultStatus} from "@/utils/http/types";
import ResetUserPwd from "@/views/system/usermanage/components/ResetUserPwd.vue";
import PermissionForUser from "@/views/system/permmanage/components/PermissionForUser.vue";
import TenantManageForUser from "@/views/system/tenantmanage/components/TenantManageForUser.vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {computed, getCurrentInstance, nextTick, reactive, ref, toRefs, watch} from "vue";
import {
  add,
  deleteUser,
  exportUserData,
  searchUserInfo,
  update,
  UserManageInfo
} from "@/views/system/usermanage/api/UserManage";

defineOptions({
  name: "SystemConfig_UserMange"
});

const deptTree = ref<InstanceType<typeof ElTree>>();
const userCrudRef = ref();
const {$confirm, $notify} =
  getCurrentInstance().appContext.config.globalProperties;

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 210;
});

const userTableOption = reactive({
  align: "center",
  menuAlign: "center",
  viewBtn: hasAuth([
    "system:user:detail-view",
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin
  ]),
  index: true,
  border: true,
  stripe: true,
  addBtn: hasAuth([
    "system:user:add",
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin
  ]),
  editBtn: hasAuth([
    "system:user:edit",
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin
  ]),
  delBtn: false,
  rowKey: "userId",
  height: tableHeight,
  menuWidth: 175,
  defaultSort: {
    prop: "loginTime",
    order: "descending"
  },
  column: [
    {
      label: "姓名",
      prop: "realName",
      width: 145,
      rules: [
        {
          required: true,
          message: "请填写用户姓名",
          trigger: "blur"
        }
      ],
      sortable: true,
      maxlength: 32,
      showWordLimit: true
    },
    {
      label: "用户账号",
      prop: "userName",
      editDisabled: true,
      rules: [
        {
          required: true,
          message: "请填写用户账号",
          trigger: "blur"
        }
      ],
      sortable: true,
      maxlength: 32,
      showWordLimit: true
    },
    {
      label: "用户类型",
      prop: "userTypeName",
      sortable: true
    },
    {
      label: "性别",
      prop: "gender",
      showColumn: false,
      hide: true
    },
    {
      label: "手机号",
      prop: "cellPhone",
      sortable: true,
      maxlength: 16,
      showWordLimit: true
    },
    {
      label: "所属组织",
      prop: "deptName",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "所属组织",
      prop: "deptId",
      viewDisplay: false,
      editDisabled: true,
      showColumn: false,
      hide: true,
      rules: [
        {
          required: true,
          message: "请选择用户所属组织",
          trigger: "blur"
        }
      ]
    },
    {
      label: "所属角色",
      prop: "roleNames",
      editDisplay: false,
      addDisplay: false,
      editDisabled: true,
      showColumn: false,
      hide: true
    },
    {
      label: "所属租户",
      prop: "tenantNames",
      editDisplay: false,
      addDisplay: false,
      editDisabled: true,
      showColumn: false,
      hide: true
    },
    {
      label: "企业微信",
      prop: "wechat",
      showColumn: false,
      hide: true,
      maxlength: 200,
      showWordLimit: true
    },
    {
      label: "钉钉号",
      prop: "dingding",
      showColumn: false,
      hide: true,
      maxlength: 200,
      showWordLimit: true
    },
    {
      label: "固定电话",
      prop: "telephone",
      showColumn: false,
      hide: true,
      maxlength: 16,
      showWordLimit: true
    },
    {
      label: "电子邮件",
      prop: "email",
      showColumn: false,
      hide: true,
      maxlength: 50,
      showWordLimit: true
    },
    {
      label: "联系地址",
      prop: "address",
      showColumn: false,
      hide: true,
      maxlength: 180,
      showWordLimit: true
    },
    {
      label: "创建者",
      prop: "createUser",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "状态",
      prop: "enableSwitch",
      value: "1",
      sortable: true
    },
    {
      label: "最近登录",
      prop: "loginTime",
      width: 150,
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "出生日期",
      prop: "birthday",
      showColumn: false,
      hide: true,
      type: "date"
    },
    {
      label: "有效期至",
      prop: "expireTime",
      showColumn: false,
      hide: true,
      type: "datetime",
      disabledDate(time) {
        return time.getTime() < Date.now();
      },
      rules: [
        {
          required: true,
          message: "请设置账号有效期",
          trigger: "blur"
        }
      ]
    }
  ]
});

/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value!.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};

//首层组织数据 计算属性
const firstLevelDeptData = computed(() => {
  return parseFirstLevelDept(
    useDeptStoreHook()?.deptData.filter(
      (d: SimpleDeptInfo) => d.parentId === "-1"
    )
  );
});

//数据对象
const state = reactive({
  userData: [] as Array<UserManageInfo>,
  deptData: firstLevelDeptData,
  user: {} as UserManageInfo,
  userTablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  searchCondition: {
    deptId: "-1",
    keyWord: "",
    pageNo: 1,
    pageSize: defaultPageSize,
    sortType: "DESC",
    sortField: "loginTime"
  },
  deptKeyWord: null,
  loading: false,
  resetPwdVisible: false,
  currentOperateUser: null as UserManageInfo,
  userPermissionVisible: false,
  tenantManageVisible: false
});
const {
  userData,
  deptData,
  user,
  userTablePage,
  deptKeyWord,
  searchCondition,
  loading,
  resetPwdVisible,
  currentOperateUser,
  userPermissionVisible,
  tenantManageVisible
} = toRefs(state);

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

//加载组织树节点数据
const loadNode = (node, resolve) => {
  if (node.isLeaf) return resolve([]);
  resolve(useDeptStoreHook().getChildren(node.data.deptId));
};

//查询用户信息
const searchUserData = async () => {
  state.searchCondition.pageNo = state.userTablePage.currentPage;
  state.searchCondition.pageSize = state.userTablePage.pageSize;
  state.userData = [];
  state.loading = true;
  await searchUserInfo(state.searchCondition).then(result => {
    state.userData = result.data;
    state.userTablePage.total = parseInt(result.total);
  });
  state.loading = false;
};

//搜索用户数据
const searchFirstPageData = () => {
  state.userTablePage.currentPage = 1;
  searchUserData();
};

//组织树选择改变触发
const deptSelectChange = data => {
  state.searchCondition.deptId = data.deptId;
  searchUserData();
};

//新增用户前初始化数据
const beforeOpenUser = (done, type) => {
  if (type === "add") {
    state.user.enableSwitch = "1";
    state.user.userTypeSign = "1";
    state.user.gender = "male";
    state.user.expireTime = dayjs().add(10, "y").format("YYYY-MM-DD HH:mm:ss");
  }
  done();
};

//新增用户信息保存
const addUserSave = async (form, done, loading) => {
  await add(form).then(() => {
    loading();
    done();
    searchUserData();
    $notify({
      title: "提示",
      message: `已成功添加 ${form.userName} 用户`,
      type: "success"
    });
  });
};

//更新用户信息
const updateUserInfo = async (form, index, done, loading) => {
  await update(form).then(() => {
    loading();
    done(form);
    $notify({
      title: "提示",
      message: `已成功保存 ${form.userName} 的用户信息`,
      type: "success"
    });
  });
};

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;
  return data.deptName.includes(value);
};

//导出用户数据
const exportHandler = () => {
  message("数据正在导出中...", {type: "success"});
  exportUserData(state.searchCondition);
};

//用户状态变更 - 列表快捷
const enableChangeHandler = (val: UserManageInfo) => {
  if (val.userId != null) {
    $confirm(
      `您确认要将‘${val.realName}’用户的状态设置为 ${
        val.enableSwitch === "0" ? "禁用" : "启用"
      } 么？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      }
    )
      .then(() => {
        update(val).then(res => {
          if (res.status == ResultStatus.Success) {
            $notify({
              title: "提示",
              message: `您已成功将‘${val.realName}’用户的状态设置为 ${
                val.enableSwitch === "0" ? "禁用" : "启用"
              } ！`,
              type: "success"
            });
          }
        });
      })
      .catch(() => (val.enableSwitch = val.enableSwitch === "0" ? "1" : "0"));
  }
};

//删除用户
const deleteUserHandler = (row: UserManageInfo) => {
  console.log("用户Id", row.userId);
  $confirm(`您确认要删除‘${row.realName}’用户么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(() => {
    deleteUser(row.userId).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: `您已成功删除‘${row.realName}’用户！`,
          type: "success"
        });
        searchUserData();
      }
    });
  });
};

//重置用户密码
const resetUserPwdHandler = (row: UserManageInfo) => {
  state.currentOperateUser = row;
  state.resetPwdVisible = true;
};

//打开用户权限
const userPermHandler = (row: UserManageInfo) => {
  state.currentOperateUser = row;
  state.userPermissionVisible = true;
};

//打开用归属租户管理
const tenantManageHandler = (row: UserManageInfo) => {
  state.currentOperateUser = row;
  state.tenantManageVisible = true;
};

//排序触发
const sortChangeHandler = (val: any) => {
  state.searchCondition.sortField = val.prop;
  state.searchCondition.sortType = val.order == "ascending" ? "ASC" : "DESC";
  searchUserData();
};
</script>

<style scoped>
.h-content {
  height: calc(100% - 48px);
}
</style>
