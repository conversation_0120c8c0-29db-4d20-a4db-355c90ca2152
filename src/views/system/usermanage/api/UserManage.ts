import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage`;

//用户信息查询条件
export type UserManageQueryCondition = {
  deptId: string;
  keyWord: string;
  pageNo: number;
  pageSize: number;
  sortType: string;
};

//用户信息 - 用于用户信息管理
export type UserManageInfo = {
  userId: string;
  userName: string;
  realName: string;
  userAvatar: string;
  gender: string;
  deptId: string;
  deptName: string;
  userTypeSign: string;
  userTypeName: string;
  cellPhone: string;
  cellphone: string;
  telephone: string;
  wechat: string;
  dingding: string;
  email: string;
  address: string;
  roleNames: Array<string>;
  tenantNames: Array<string>;
  loginTime: string;
  loginIp: string;
  birthday: string;
  expireTime: string;
  createUser: string;
  enableSwitch: string;
};

//简单用户信息 - 用于用户选择
export type SimpleUserInfo = {
  userId: string;
  userName: string;
  realName: string;
  enableSwitch: string;
};

//待选择用户信息
export type UserInfoForSelect = {
  userId: string;
  userName: string;
  realName: string;
  deptId: string;
  deptPathName: string;
};

//管理员联系信息
export type AdminContactInfo = {
  userId: string;
  userName: string;
  realName: string;
  deptId: string;
  deptPathName?: string;
  email?: string;
};

//查询用户信息
const searchUserInfo = (params: UserManageQueryCondition) => {
  return http.postJson<RestPageResult<Array<UserManageInfo>>>(
    `${basePath}/users/list`,
    params
  );
};

//新增用户信息
const add = (user: UserManageInfo) => {
  //处理手机号信息
  if (user.cellPhone != null && user.cellPhone.length > 0) {
    user.cellphone = user.cellPhone;
  }
  return http.postJson<RestResult<string>>(`${basePath}/users`, user);
};

//更新用户信息
const update = (user: UserManageInfo) => {
  //处理手机号信息
  if (
    user.cellPhone != null &&
    user.cellPhone.length > 0 &&
    user.cellPhone.indexOf("****") == -1
  ) {
    user.cellphone = user.cellPhone;
  } else if (user.cellPhone == "" || user.cellPhone.length == 0) {
    user.cellphone = "clear";
  }
  return http.postJson<RestResult<UserManageInfo>>(
    `${basePath}/users/update`,
    user
  );
};

//查询待添加用户
const searchAddUser = (params: object) => {
  return http.postJson<RestResult<Array<SimpleUserInfo>>>(
    `${basePath}/tenant/searchNeedAddUser`,
    params
  );
};

//查询待选择用户数据
const queryNeedSelectUser = (params: any) =>
  http.postJson<RestPageResult<Array<UserInfoForSelect>>>(
    `${basePath}/users/queryNeedSelectUser`,
    params
  );

//导出用户数据
const exportUserData = (params: any) =>
  http.postBlobWithJson(`${basePath}/users/export`, params);

//删除用户
const deleteUser = (userId: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/users/delUser?userId=${userId}`
  );

//重置用户密码
const resetUserPwd = (params: any) =>
  http.postJson<RestResult<string>>(
    `${basePath}/eam/user/pwd/resetUserPwd`,
    params
  );

//查询管理员的联系信息
const getAdminUserForContact = () =>
  http.get<any, RestResult<Array<AdminContactInfo>>>(
    `${basePath}/eam/permission/getAdminUserForContact`
  );

export {
  searchUserInfo,
  add,
  update,
  searchAddUser,
  queryNeedSelectUser,
  exportUserData,
  deleteUser,
  resetUserPwd,
  getAdminUserForContact
};
