<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="70%"
  >
    <div class="flex justify-items-start items-center w-[500px]">
      <span class="w-[140px]">所属部门：</span>
      <dept-tree-select v-model:dept-id="search.deptId" clearable filterable />
      <el-radio-group v-model="search.deptType" class="w-[200px] ml-2">
        <el-radio-button label="0">精确</el-radio-button>
        <el-radio-button label="1">关联</el-radio-button>
      </el-radio-group>
      <el-button
        type="primary"
        :disabled="!search.deptId"
        @click="queryUserData"
      >
        查询
      </el-button>
    </div>
    <avue-crud
      ref="userTableRef"
      :data="data"
      :option="userTableOption"
      v-model:page="page"
      @on-load="loadUserPageData"
      @selection-change="userSelectionChange"
    >
      <template #menu="{ type, size, row }">
        <el-link
          :icon="useRenderIcon('EP-Plus')"
          :size="size"
          :type="type"
          @click="selectOneUser(row)"
        >
          选择
        </el-link>
      </template>
    </avue-crud>
    <template #footer>
      <el-button @click="cancel()">取 消</el-button>
      <el-button
        type="primary"
        :disabled="selectedUsers.length < 1"
        @click="batchSelectConfirm"
      >
        确认选择
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch } from "vue";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  queryNeedSelectUser,
  UserInfoForSelect
} from "@/views/system/usermanage/api/UserManage";

const userTableRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  dialogTitle: {
    type: String
  }
});

//定义事件
const emit = defineEmits(["update:visible", "user-select"]);

const userTableOption = reactive({
  selection: true,
  reserveSelection: true,
  rowKey: "userId",
  align: "center",
  menuAlign: "center",
  height: 360,
  calcHeight: 98,
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false,
  columnBtn: false,
  gridBtn: false,
  column: [
    { label: "部门", prop: "deptPathName" },
    {
      label: "用户账号",
      prop: "userName"
    },
    { label: "姓名", prop: "realName" }
  ]
});

//数据对象
const state = reactive({
  dialogVisible: false,
  title: "用户选择",
  data: [] as Array<UserInfoForSelect>,
  selectedUsers: [] as Array<UserInfoForSelect>,
  search: {
    deptType: "1",
    deptId: "",
    pageNo: 1,
    pageSize: defaultPageSize
  },
  page: {
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  }
});

const { dialogVisible, title, data, selectedUsers, search, page } =
  toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  state.title = newValue.dialogTitle;
  state.selectedUsers = [];
  state.data = [];
  if (userTableRef.value) {
    userTableRef.value.clearSelection();
  }
});

//查询待添加用户数据
const queryUserData = () => {
  state.page.currentPage = 1;
  loadUserData();
};

//分页查询数据
const loadUserPageData = (params, done) => {
  if (state.search.deptId) {
    loadUserData();
  }
  if (done != null) {
    done();
  }
};

//加载用户数据
const loadUserData = async () => {
  state.search.pageNo = state.page.currentPage;
  state.search.pageSize = state.page.pageSize;
  state.data = [];
  queryNeedSelectUser(state.search).then(result => {
    state.data = result.data;
    state.page.total = parseInt(result.total);
  });
};

//选择用户改变
const userSelectionChange = (users: Array<UserInfoForSelect>) => {
  state.selectedUsers = users;
};

//选择单个用户
const selectOneUser = (user: UserInfoForSelect) => {
  const selectedUserArray = [user];
  emit("user-select", selectedUserArray);
  cancel();
};

//批量选择确认
const batchSelectConfirm = () => {
  emit("user-select", state.selectedUsers);
  cancel();
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
