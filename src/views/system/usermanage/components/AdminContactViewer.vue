<template>
  <el-dialog
    title="管理员联系信息"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="45%"
  >
    <el-table :data="data" stripe style="width: 100%" height="260">
      <el-table-column prop="realName" label="姓名" />
      <el-table-column prop="deptPathName" label="组织" />
      <el-table-column prop="email" label="电子邮箱" />
      <template #empty>
        <el-empty description="未找到管理员的联系信息" :image-size="60" />
      </template>
    </el-table>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch } from "vue";
import { ResultStatus } from "@/utils/http/types";
import {
  AdminContactInfo,
  getAdminUserForContact
} from "@/views/system/usermanage/api/UserManage";

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  data: [] as Array<AdminContactInfo>
});
const { dialogVisible, data } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  loadAdminContactInfo();
});

//加载管理员联系信息
const loadAdminContactInfo = () => {
  getAdminUserForContact().then(res => {
    if (ResultStatus.Success == res.status) {
      state.data = res.data;
    }
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
