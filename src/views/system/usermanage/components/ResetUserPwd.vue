<template>
  <el-dialog
    :title="'重置密码 - ' + props.user?.realName"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="30%"
  >
    <avue-form ref="resetPwdFormRef" :option="option" v-model="form" />
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-CircleCheck')"
        @click="submitForm"
      >
        确定
      </el-button>
      <el-button :icon="useRenderIcon('EP-CircleClose')" @click="cancel()">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { validatePwd } from "@/utils/validator";
import { toRefs, reactive, watch, getCurrentInstance, ref } from "vue";
import { sm3 } from "sm-crypto";
import { resetUserPwd } from "@/views/system/usermanage/api/UserManage";
import { ResultStatus } from "@/utils/http/types";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;
const resetPwdFormRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  user: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//密码格式和强度校验
const validatePassword = (rule, value, callback) => {
  if (validatePwd(value)) {
    callback();
  } else {
    callback(new Error("密码必须由至少8位的字母、数字、特殊字符组成"));
  }
};

//密码一致性校验
const passwordConfirmCheck = (rule, value, callback) => {
  if (value == state.form.newPwd) {
    callback();
  } else {
    callback(new Error("两次输入密码不一致!"));
  }
};

//avue-form组件属性
const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "新密码",
      prop: "newPwd",
      span: 24,
      maxlength: 24,
      showWordLimit: true,
      type: "password",
      rules: [
        {
          required: true,
          message: "请输入新密码",
          trigger: "blur"
        },
        { validator: validatePassword, trigger: "blur" }
      ]
    },
    {
      label: "确认密码",
      prop: "checkPwd",
      span: 24,
      maxlength: 24,
      showWordLimit: true,
      type: "password",
      rules: [
        {
          required: true,
          message: "请输入确认密码",
          trigger: "blur"
        },
        { validator: validatePassword, trigger: "blur" },
        { validator: passwordConfirmCheck, trigger: "blur" }
      ]
    }
  ]
});

//数据对象
const state = reactive({
  dialogVisible: false,
  form: {
    newPwd: "",
    checkPwd: ""
  }
});
const { dialogVisible, form } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  resetPwdFormRef.value?.resetFields();
});

//提交修改密码请求
const submitForm = () => {
  resetPwdFormRef.value.validate(async (valid, done) => {
    if (valid) {
      await resetUserPwd({
        userId: props.user.userId,
        newPwd: sm3(state.form.newPwd)
      }).then(res => {
        if (res.status === ResultStatus.Success) {
          $notify({
            title: "提示",
            message: `已成功重置‘${props.user.realName}’用户的密码!`,
            type: "success"
          });
          cancel();
        }
      });
      done();
    }
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
