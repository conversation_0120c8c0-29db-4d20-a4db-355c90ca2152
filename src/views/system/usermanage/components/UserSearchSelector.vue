<template>
  <div>
    <el-row type="flex" justify="center">
      <el-col :span="8">
        <el-input
          v-model="keyWord"
          placeholder="用户账号/姓名"
          @keyup.enter="searchNeedAddUser"
          clearable
        >
          <template #append>
            <el-button
              :icon="useRenderIcon('EP-Search')"
              @click="searchNeedAddUser"
            />
          </template>
        </el-input>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table :data="userData" row-key="userId" height="50vh" stripe>
          <el-table-column prop="userName" label="用户账号" />
          <el-table-column prop="realName" label="姓名" />
          <el-table-column width="80" align="left" label="操作">
            <template #default="{ row }">
              <el-link
                type="primary"
                :icon="useRenderIcon('EP-CirclePlusFilled')"
                @click="addUserToTagGroup(row)"
              >
                添加
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col :span="24" class="p-1">
        <el-tag
          v-bind:key="item.userId"
          v-for="item in dynamicTags"
          closable
          :disable-transitions="false"
          @close="tagCloseHandler(item)"
          class="ml-1.5"
        >
          {{ item.realName }}
        </el-tag>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { message } from "@/utils/message";
import {
  searchAddUser,
  SimpleUserInfo
} from "@/views/system/usermanage/api/UserManage";

//数据对象
const state = reactive({
  keyWord: "",
  userData: [],
  dynamicTags: [] as Array<SimpleUserInfo>
});

const { keyWord, userData, dynamicTags } = toRefs(state);

//初始化用户数据
// state.userData = [];

//搜索用户数据
const searchNeedAddUser = async () => {
  await searchAddUser({ userKeyWord: state.keyWord }).then(result => {
    state.userData = result.data;
  });
};

//添加用户到标签组
const addUserToTagGroup = (user: SimpleUserInfo) => {
  if (!state.dynamicTags.includes(user)) {
    state.dynamicTags.push(user);
  } else {
    message(`${user.realName} 已添加！`, {
      type: "success"
    });
  }
};

//关闭用户标签
const tagCloseHandler = (tag: SimpleUserInfo) => {
  state.dynamicTags.splice(state.dynamicTags.indexOf(tag), 1);
};

//获取已选择用户
function getSelectedUsers() {
  return state.dynamicTags;
}

defineExpose({ getSelectedUsers });
</script>
