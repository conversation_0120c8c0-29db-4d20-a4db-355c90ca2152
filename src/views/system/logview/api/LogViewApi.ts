import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/log`;

export interface BaseLogInfo {
  logId: string;
  realName: string;
  deptName: string;
  userName: string;
  clientIp: string;
  clientOs: string;
  loadTime: string;
}

export interface AccessLogInfo extends BaseLogInfo {
  resourceName: string;
  browserName: string;
  browserVersion: string;
}

export interface LoginLogInfo extends BaseLogInfo {
  loginType: string;
  browserName: string;
  browserVersion: string;
  logInfo: string;
}

export interface OperateLogInfo extends BaseLogInfo {
  logInfo: string;
  logAction: string;
  requestParam: string;
}

//查询菜单访问日志数据
const getAccessLogData = (params: any) =>
  http.postJson<RestPageResult<Array<AccessLogInfo>>>(
    `${basePath}/access/find`,
    params
  );

//查询系统登录日志数据
const getLoginLogData = (params: any) =>
  http.postJson<RestPageResult<Array<LoginLogInfo>>>(
    `${basePath}/userLogin/find`,
    params
  );

//查询重要操作日志数据
const getOperateLogData = (params: any) =>
  http.postJson<RestPageResult<Array<OperateLogInfo>>>(
    `${basePath}/operator/find`,
    params
  );

//导出菜单访问日志
const exportAccessLogData = (params: any) =>
  http.postBlob(`${basePath}/access/export`, params);

//导出登录日志
const exportLoginLogData = (params: any) =>
  http.postBlob(`${basePath}/userLogin/export`, params);

//导出重要操作日志
const exportOperateLogData = (params: any) =>
  http.postBlob(`${basePath}/operator/export`, params);

export {
  getAccessLogData,
  getLoginLogData,
  getOperateLogData,
  exportAccessLogData,
  exportLoginLogData,
  exportOperateLogData
};
