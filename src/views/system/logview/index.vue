<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane name="loginLog">
        <template #label>
          <span class="flex-c">
            <IconifyIconOffline icon="EP-Platform" />
            <span class="ml-1">系统登录</span>
          </span>
        </template>
        <login-log-panel />
      </el-tab-pane>
      <el-tab-pane name="operateLog">
        <template #label>
          <span class="flex-c">
            <IconifyIconOffline icon="EP-WarningFilled" />
            <span class="ml-1">重要操作</span>
          </span>
        </template>
        <operate-log-panel />
      </el-tab-pane>
      <el-tab-pane name="accessLog">
        <template #label>
          <span class="flex-c">
            <IconifyIconOffline icon="EP-Menu" />
            <span class="ml-1">菜单访问</span>
          </span>
        </template>
        <access-log-panel />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from "vue";
import AccessLogPanel from "@/views/system/logview/components/AccessLogPanel.vue";
import LoginLogPanel from "@/views/system/logview/components/LoginLogPanel.vue";
import OperateLogPanel from "@/views/system/logview/components/OperateLogPanel.vue";

defineOptions({
  name: "SystemConfig_Log"
});

//数据对象
const state = reactive({
  activeName: "loginLog"
});

const { activeName } = toRefs(state);
</script>
