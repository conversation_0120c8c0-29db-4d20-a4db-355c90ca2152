<template>
  <div>
    <avue-crud
      :data="data"
      v-model:page="tablePage"
      v-model:search="searchForm"
      :option="option"
      :table-loading="loading"
      @refresh-change="refreshData"
      @on-load="loadLogData"
      @search-change="searchLogData"
      @search-reset="resetSearchForm"
    >
      <template #menu-right="{ size }">
        <el-tooltip content="导出日志数据" placement="top" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('EP-Download')"
            circle
            :size="size"
            :disabled="data.length < 1"
            @click="exportLogHandler"
            v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
          />
        </el-tooltip>
      </template>
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, computed } from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { message } from "@/utils/message";
import { AdminEnum } from "@/utils/CommonTypes";
import { pageSizeOptions, defaultPageSize } from "@/utils/page_util";
import {
  OperateLogInfo,
  getOperateLogData,
  exportOperateLogData
} from "@/views/system/logview/api/LogViewApi";

//数据对象
const state = reactive({
  data: [] as Array<OperateLogInfo>,
  tablePage: {
    total: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  searchForm: {
    userName: "",
    keyWord: "",
    loadTime: [dayjs().add(-1, "d").toDate(), dayjs().endOf("day")]
  },
  loading: false
});

const { data, tablePage, searchForm, loading } = toRefs(state);

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

const option = reactive({
  align: "center",
  menu: false,
  addBtn: false,
  index: true,
  border: true,
  stripe: true,
  rowKey: "logId",
  height: tableHeight,
  searchMenuSpan: 3,
  emptyBtnText: "重置",
  emptyBtnIcon: "el-icon-refresh-left",
  column: [
    { label: "用户名", prop: "userName", search: true },
    { label: "姓名", prop: "realName" },
    { label: "所属组织", prop: "deptName" },
    { label: "操作动作", prop: "logAction" },
    { label: "操作内容", prop: "logInfo" },
    {
      label: "关键字",
      prop: "keyWord",
      searchPlaceholder: "操作动作/操作内容",
      search: true,
      hide: true,
      showColumn: false
    },
    {
      label: "操作时间",
      prop: "loadTime",
      type: "datetime",
      searchRange: true,
      search: true,
      searchSpan: 7
    },
    { label: "IP地址", prop: "clientIp" },
    { label: "操作系统", prop: "clientOs" }
  ]
});

//查询日志数据
const searchLogData = async (params, done) => {
  state.tablePage.currentPage = 1;
  state.data = [];
  state.loading = true;
  await getOperateLogData(getSearchForm()).then(res => {
    state.data = res.data;
    state.tablePage.total = parseInt(res.total);
    done();
  });
  state.loading = false;
};

//查询日志数据 - 分页、首次加载触发
const loadLogData = async () => {
  state.loading = true;
  await getOperateLogData(getSearchForm()).then(res => {
    state.data = res.data;
    state.tablePage.total = parseInt(res.total);
  });
  state.loading = false;
};

//刷新数据
const refreshData = async () => {
  state.tablePage.currentPage = 1;
  state.data = [];
  tate.loading = true;
  await getOperateLogData(getSearchForm()).then(res => {
    state.data = res.data;
    state.tablePage.total = parseInt(res.total);
  });
  state.loading = false;
};

//准备搜索表单
const getSearchForm = () => {
  const timeRange = state.searchForm.loadTime;
  const searchTimeRange = [
    dayjs(timeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(timeRange[1]).format("YYYY-MM-DD HH:mm:ss")
  ];
  return {
    userName: state.searchForm.userName,
    keyWord: state.searchForm.keyWord,
    loadTime: searchTimeRange,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize
  };
};

//重置查询表单
const resetSearchForm = () => {
  state.searchForm.loadTime = [
    dayjs().add(-1, "d").toDate(),
    dayjs().endOf("day")
  ];
};

//导出日志数据
const exportLogHandler = () => {
  message("数据正在导出中...", { type: "success" });
  exportOperateLogData(getSearchForm());
};
</script>
