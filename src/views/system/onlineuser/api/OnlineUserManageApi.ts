import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/session`;

export type OnlineUserSessionInfo = {
  userName: string;
  realName: string;
  clientIp: string;
  clientId: string;
  clientOs: string;
  browserName: string;
  browserVersion: string;
  loginTime: string;
};

//查询全部数据
const getAllSessions = () =>
  http.get<any, RestResult<Array<OnlineUserSessionInfo>>>(
    `${basePath}/getAllSessions`
  );

//强制下线用户
const logOutUser = (userName: string, clientId: string) =>
  http.get<any, RestResult<string>>(
    `${basePath}/logOutUser?userName=${userName}&clientId=${clientId}`
  );

export { getAllSessions, logOutUser };
