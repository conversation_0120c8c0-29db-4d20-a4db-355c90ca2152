<template>
  <div>
    <el-row>
      <el-col :span="24" class="flex-c dark:bg-dark-color pt-2">
        <el-input
          clearable
          placeholder="登录ID/姓名"
          v-model="searchVal"
          :suffix-icon="useRenderIcon('EP-Search')"
          style="width: 300px"
          @blur="searchVal = $event.target.value.trim()"
        />
      </el-col>
    </el-row>
    <avue-crud
      :data="sessionData"
      :option="options"
      @refresh-change="loadAllSessionData"
    >
      <template #menu-left="{}">
        <el-tag type="success" style="margin-top: 5px">
          {{ sessionData.length }} 人在线
        </el-tag>
      </template>
      <template #menu="{ row, size }">
        <el-link
          :icon="useRenderIcon('RI-LogoutCircleRFill')"
          :size="size"
          type="primary"
          @click="logOutCurrentUser(row)"
        >
          强制下线
        </el-link>
      </template>
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, computed, getCurrentInstance } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { hasAuth } from "@/router/utils";
import { AdminEnum } from "@/utils/CommonTypes";
import {
  OnlineUserSessionInfo,
  getAllSessions,
  logOutUser
} from "@/views/system/onlineuser/api/OnlineUserManageApi";

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//数据对象
const state = reactive({
  searchVal: "",
  tableData: [] as Array<OnlineUserSessionInfo>
});

const { searchVal } = toRefs(state);

const options = reactive({
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  labelWidth: 130,
  searchLabelWidth: 120,
  menuWidth: 100,
  viewBtn: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: true,
  columnBtn: true,
  searchBtn: false,
  index: true,
  searchShow: false,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    { label: "登录ID", prop: "userName" },
    { label: "姓名", prop: "realName" },
    { label: "浏览器", prop: "browserName" },
    { label: "浏览器版本", prop: "browserVersion" },
    { label: "客户端IP", prop: "clientIp" },
    { label: "客户端ID", prop: "clientId" },
    { label: "操作系统", prop: "clientOs" },
    { label: "登录时间", prop: "loginTime", sortable: true }
  ],
  defaultSort: {
    prop: "loginTime",
    order: "descending"
  }
});

//会话信息搜索
const sessionData = computed(() => {
  if (state.searchVal != null && state.searchVal.length > 0) {
    return state.tableData.filter(item => {
      if (
        item.userName.includes(state.searchVal) ||
        item.realName.includes(state.searchVal)
      ) {
        return item;
      }
    });
  } else {
    return state.tableData;
  }
});

//加载全部会话信息
const loadAllSessionData = async () => {
  state.tableData = [];
  getAllSessions().then(result => {
    state.tableData = result.data;
  });
};
loadAllSessionData();

//强制下线用户
const logOutCurrentUser = async (row: OnlineUserSessionInfo) => {
  $confirm("您确定要强制下线 '" + row.realName + "' 用户吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(() => {
    logOutUser(row.userName, row.clientId).then(() => {
      $notify({
        title: "提示",
        message: "已成功下线 '" + row.realName + "' 用户",
        type: "success"
      });
      loadAllSessionData();
    });
  });
};
</script>
