import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";
import { UserManageInfo } from "@/views/system/usermanage/api/UserManage";

const basePath = `${ServerNames.portalServer}/framework/sysmanage`;

//获得当前用户的待管理信息
const getCurrentUserManageInfo = () =>
  http.get<any, RestResult<UserManageInfo>>(
    `${basePath}/users/getCurrentUserManageInfo`
  );

//更新当前用户头像
const updateUserAvatar = (avatarName: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/users/updateUserAvatar?avatar=${avatarName}`
  );

//变更当前用户密码
const updateUserPwd = (oldPwd: string, newPwd: string) =>
  http.postJson<RestResult<string>>(`${basePath}/users/modifypwd`, {
    oldPwd,
    newPwd
  });

//检查用户密码是否已过期
const checkUserPwdExpired = () =>
  http.get<any, RestResult<boolean>>(`${basePath}/users/checkUserPwdExpired`);

//获取密码到期提示信息
const getExpiredTipsMessage = () =>
  http.get<any, RestResult<string>>(`${basePath}/eam/user/pwd/tips`);

//用户密码过期检查-将要过期时给出提示
const pwdInvalidCheck = () =>
  http.get<any, RestResult<boolean>>(
    `${basePath}/eam/user/pwd/pwdInvalidCheck`
  );

//延迟密码有效期
const continuePwdEffect = () =>
  http.get<any, RestResult<string>>(`${basePath}/eam/user/pwd/continue`);

export {
  getCurrentUserManageInfo,
  updateUserAvatar,
  updateUserPwd,
  checkUserPwdExpired,
  getExpiredTipsMessage,
  pwdInvalidCheck,
  continuePwdEffect
};
