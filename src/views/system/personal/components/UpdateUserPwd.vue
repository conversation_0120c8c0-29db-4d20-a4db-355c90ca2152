<template>
  <el-dialog
    title="变更密码"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="30%"
  >
    <div class="space-y-5">
      <el-alert
        title="提示"
        type="warning"
        v-if="props.tips && props.tips.length > 0"
        :description="props.tips"
        show-icon
        :closable="false"
      />
      <avue-form ref="updatePwdFormRef" :option="option" v-model="form" />
    </div>
    <template #footer>
      <el-button
        type="primary"
        @click="submitForm"
        :icon="useRenderIcon('EP-CircleCheck')"
      >
        确定
      </el-button>
      <el-button
        v-if="props.cancelBtnVisible"
        :icon="useRenderIcon('EP-CircleClose')"
        @click="cancel()"
      >
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, toRefs, watch } from "vue";
import { validatePwd } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";
import { sm3 } from "sm-crypto";
import { updateUserPwd } from "@/views/system/personal/api/PersonalApi";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;
const updatePwdFormRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  cancelBtnVisible: {
    type: Boolean
  },
  tips: {
    type: String
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//密码格式和强度校验
const validatePassword = (rule, value, callback) => {
  if (validatePwd(value)) {
    callback();
  } else {
    callback(new Error("密码必须由至少8位的字母、数字、特殊字符组成"));
  }
};

//密码一致性校验
const passwordConfirmCheck = (rule, value, callback) => {
  if (value == state.form.newPwd && state.form.olpPwd != state.form.newPwd) {
    callback();
  } else if (value != state.form.newPwd) {
    callback(new Error("两次输入密码不一致!"));
  } else if (state.form.olpPwd == state.form.newPwd) {
    callback(new Error("新密码不能与原密码相同!"));
  }
};

//avue-form组件属性
const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "原密码",
      prop: "olpPwd",
      span: 24,
      maxlength: 24,
      showWordLimit: true,
      type: "password",
      rules: [
        {
          required: true,
          message: "请输入原密码",
          trigger: "blur"
        },
        { validator: validatePassword, trigger: "blur" }
      ]
    },
    {
      label: "新密码",
      prop: "newPwd",
      span: 24,
      maxlength: 24,
      showWordLimit: true,
      type: "password",
      rules: [
        {
          required: true,
          message: "请输入新密码",
          trigger: "blur"
        },
        { validator: validatePassword, trigger: "blur" }
      ]
    },
    {
      label: "确认密码",
      prop: "checkPwd",
      span: 24,
      maxlength: 24,
      showWordLimit: true,
      type: "password",
      rules: [
        {
          required: true,
          message: "请输入确认密码",
          trigger: "blur"
        },
        { validator: validatePassword, trigger: "blur" },
        { validator: passwordConfirmCheck, trigger: "blur" }
      ]
    }
  ]
});

//数据对象
const state = reactive({
  dialogVisible: false,
  form: {
    olpPwd: "",
    newPwd: "",
    checkPwd: ""
  }
});
const { dialogVisible, form } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  updatePwdFormRef.value?.resetFields();
});

//提交修改密码请求
const submitForm = () => {
  updatePwdFormRef.value.validate(async (valid, done) => {
    if (valid) {
      await updateUserPwd(sm3(state.form.olpPwd), sm3(state.form.newPwd)).then(
        res => {
          if (res.status === ResultStatus.Success) {
            $notify({
              title: "提示",
              message: "恭喜！修改密码成功，请重新登录!",
              type: "success"
            });
            useUserStoreHook().logOut();
          }
        }
      );
      done();
    }
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
