<template>
  <div>
    <avue-form ref="typeEditFormRef" v-model="userInfo" :option="formOption" />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, getCurrentInstance } from "vue";
import { ResultStatus } from "@/utils/http/types";
import {
  update,
  UserManageInfo
} from "@/views/system/usermanage/api/UserManage";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;

const formOption = reactive({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  menuPosition: "right",
  column: [
    {
      label: "出生日期",
      prop: "birthday",
      type: "date"
    },
    {
      label: "性别",
      prop: "gender",
      type: "radio",
      dicData: [
        { label: "男", value: "male" },
        { label: "女", value: "female" }
      ]
    },
    {
      label: "固定电话",
      prop: "telephone"
    },
    {
      label: "电子邮件",
      prop: "email"
    },
    {
      label: "联系地址",
      prop: "address"
    }
  ]
});

//声明事件
const emit = defineEmits(["submit"]);

//数据对象
const state = reactive({
  userInfo: {} as UserManageInfo
});
const { userInfo } = toRefs(state);

//注入用户信息
const injectUserInfo = (user: UserManageInfo) => (state.userInfo = user);

//提交表单
const submitForm = () => {
  update(state.userInfo).then(res => {
    if (res.status == ResultStatus.Success) {
      emit("submit", true);
      $notify({
        title: "提示",
        message: `已成功保存您的用户信息！`,
        type: "success"
      });
    }
  });
};

defineExpose({ injectUserInfo, submitForm });
</script>
