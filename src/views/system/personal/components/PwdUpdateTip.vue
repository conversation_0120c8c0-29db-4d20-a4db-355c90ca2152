<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    :before-close="cancel"
    width="50%"
  >
    <div class="space-y-5">
      <el-alert
        title="提示"
        type="warning"
        :description="tipsMessage"
        show-icon
        :closable="false"
      />
      <div class="flex justify-center space-x-5">
        <el-button size="large" type="primary" @click="updatePwdHandler">
          变更密码
        </el-button>
        <el-button size="large" type="success" @click="continueHandler">
          我要续期
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, reactive, toRefs, watch } from "vue";
import { ResultStatus } from "@/utils/http/types";
import {
  continuePwdEffect,
  pwdInvalidCheck
} from "@/views/system/personal/api/PersonalApi";

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  }
});

//定义事件
const emit = defineEmits(["update:visible", "modify-pwd"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  tipsMessage: ""
});
const { dialogVisible, tipsMessage } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
});

//用户密码过期检查
const checkPwdNeedInvalid = () => {
  pwdInvalidCheck().then(res => {
    if (res.status === ResultStatus.Success) {
      if (res.data) {
        state.tipsMessage = res.msg;
        state.dialogVisible = true;
        emit("update:visible", true);
      }
    }
  });
};

//更新密码
const updatePwdHandler = () => {
  emit("modify-pwd");
};

//续期
const continueHandler = () => {
  $confirm(
    "长时间使用相同密码，会增加您账号的安全风险。您确定要延迟密码有效期么？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    continuePwdEffect().then(res => {
      if (res.status === ResultStatus.Success) {
        $notify({
          title: "提示",
          message: res.msg,
          type: "success"
        });
        cancel();
      }
    });
  });
};

const cancel = () => {
  emit("update:visible", false);
};

onMounted(() => {
  checkPwdNeedInvalid();
});
</script>
