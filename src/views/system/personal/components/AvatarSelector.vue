<template>
  <el-drawer
    v-model="drawerVisible"
    size="42%"
    title="请选择您喜欢的头像"
    :before-close="cancel"
    :close-on-click-modal="true"
  >
    <div class="w-full p-3 grid grid-cols-8 gap-4">
      <el-avatar
        v-for="item in data"
        :src="'/avatar/' + item"
        :key="item"
        class="shadow-lg transform transition duration-300 ease-in-out hover:scale-150 hover:cursor-pointer"
        @click="selectAvatarHandler(item)"
      />
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import {onMounted, reactive, toRefs, watch} from "vue";
import {getConfig} from "@/config";

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  }
});

//定义事件
const emit = defineEmits(["update:visible", "select"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  data: [] as Array<string>
});
const {data, drawerVisible} = toRefs(state);

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
});

onMounted(() => {
  state.data = getConfig()?.AvatarImages;
});

//头像选择事件处理器
const selectAvatarHandler = (avatarName: string) => {
  emit("select", avatarName);
  cancel();
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
