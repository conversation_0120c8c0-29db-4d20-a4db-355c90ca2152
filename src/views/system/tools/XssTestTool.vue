<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="12">
        <h1>Html渲染验证器-防止XSS</h1>
        <div style="margin: 5px">
          <el-input
            type="textarea"
            :rows="8"
            placeholder="请输入内容"
            v-model="textarea"
          />
        </div>
        <el-button @click="renderHtmlHandler" type="success">
          安全解析渲染
        </el-button>
        <el-button @click="renderUnSateHtmlHandler" type="danger">
          常规解析渲染
        </el-button>
      </el-col>
      <el-col :span="12">
        <div v-sechtml="renderHtml" />
        <div v-html="renderUnSafeHtml" />
      </el-col>
    </el-row>

    <el-row :gutter="10" style="margin-top: 30px">
      <el-col :span="12">
        <h1>Html传输加密-防止waf扫描</h1>
        <div style="margin: 5px">
          <el-input
            type="textarea"
            :rows="8"
            placeholder="请输入内容"
            v-model="richText"
          />
        </div>
        <el-button @click="commitHtmlHandler" type="success">
          传输加密
        </el-button>
      </el-col>
      <el-col :span="12">
        <p>{{ commitHtml }}</p>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive } from "vue";
import { commitTransform } from "@/utils/security";

//数据对象
const state = reactive({
  textarea: "",
  renderHtml: "",
  renderUnSafeHtml: "",
  commitHtml: "",
  richText: ""
});
const { textarea, renderHtml, renderUnSafeHtml, commitHtml, richText } =
  toRefs(state);

const renderHtmlHandler = () => {
  state.renderHtml = state.textarea;
};

const renderUnSateHtmlHandler = () => {
  state.renderUnSafeHtml = state.textarea;
};
const commitHtmlHandler = () => {
  state.commitHtml = commitTransform(state.richText);
};
</script>
