<template>
  <div
    class="content-center border border-gray-400 border-opacity-20 rounded-lg p-2 w-[150px] h-[80px] cursor-pointer hover:bg-primary hover:text-white dark:hover:bg-primary"
    v-if="name != null"
    v-copy="props.label"
  >
    <div class="w-full flex justify-center items-center mb-3 mt-2">
      <IconifyIconOffline :icon="props.name" width="28" />
    </div>
    <div class="w-full flex justify-center items-center">
      <span class="text-xs/[11px]">{{ props.label }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
//组件属性
const props = defineProps({
  name: {
    type: String
  },
  label: {
    type: String
  }
});
</script>
