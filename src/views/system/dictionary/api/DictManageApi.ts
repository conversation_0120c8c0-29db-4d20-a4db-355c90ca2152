import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";
import { SelectOption } from "@/utils/CommonTypes";

const basePath = `${ServerNames.portalServer}/framework/dict`;

//字典类型信息
export type DictionaryTypeInfo = {
  dictTypeSign: string;
  dictTypeName: string;
  linkedTypeSign: string;
  protectLevel: number;
  firstLevel: boolean;
};

//字典类型详细信息 - 用于新增、编辑
export type DictionaryTypeDetailInfo = {
  id: string;
  dictTypeSign: string;
  dictTypeName: string;
  moduleSign: string;
  linkedTypeSign: string;
  firstLevel: boolean;
  sortIndex: number;
  remarks: string;
};

//字典配置项信息
export type DictionaryItemInfo = {
  id: string;
  dictTypeSign: string;
  dictTypeName: string;
  itemLabel: string;
  itemValue: string;
  parentItemLabel: string;
  parentItemValue: string;
  remarks: string;
  updateUid: string;
  createTime: string;
  updateTime: string;
  sortIndex: number;
};

//查询字典类型数据显示左侧列表数据
const getDictTypeList = () =>
  http.get<any, RestResult<Array<DictionaryTypeInfo>>>(
    `${basePath}/getDictTypeList`
  );

//搜索字典数据-分页
const searchDictItemData = (params: any) =>
  http.postJson<RestPageResult<Array<DictionaryItemInfo>>>(
    `${basePath}/searchDictItemData`,
    params
  );

//搜索字典数据-非分页
const searchDictItemDataNoPage = (params: any) =>
  http.postJson<RestPageResult<Array<DictionaryItemInfo>>>(
    `${basePath}/searchDictItemDataNoPage`,
    params
  );

//查询字典作为下拉框数据使用
const getDictItemForSelect = (dictTypeSign: string) =>
  http.get<String, RestResult<Array<SelectOption>>>(
    `${basePath}/getDictItemForSelect?dictTypeSign=${dictTypeSign}`
  );

//查询可关联的字典类型数据
const getCanLinkedDictTypeList = (sign: string) =>
  http.get<string, RestResult<Array<DictionaryTypeInfo>>>(
    `${basePath}/getCanLinkedDictTypeList?currentDictTypeSign=${sign}`
  );

//查询关联字典数据
const getLinkedDictionaryData = (sign: string, parentVal: string) =>
  http.get<string, RestPageResult<Array<DictionaryItemInfo>>>(
    `${basePath}/getLinkedDictionaryData?dictTypeSign=${sign}&parentItemValue=${parentVal}`
  );

//新增字典类型
const addDictionaryType = (type: DictionaryTypeDetailInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/addDictionaryType`, type);

//更新字典类型数据
const updateDictionaryType = (type: DictionaryTypeDetailInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/updateDictionaryType`, type);

//查询字典类型详情
const getDictTypeDetail = (sign: string) =>
  http.get<string, RestResult<DictionaryTypeDetailInfo>>(
    `${basePath}/getDictTypeDetail?dictTypeSign=${sign}`
  );

//删除字典类型
const deleteDictionaryType = (sign: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/deleteDictionaryType?dictTypeSign=${sign}`
  );

//新增字典项
const addDictItemData = (item: DictionaryItemInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/addDictItem`, item);

//更新字典数据
const updateDictItemData = (item: DictionaryItemInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/updateDictItem`, item);

//删除字典数据
const deleteDictItemData = (id: string) =>
  http.get<string, RestResult<string>>(`${basePath}/deleteDictItem?id=${id}`);

//导出字典数据
const exportDictItemData = (params: any) =>
  http.postBlobWithJson(`${basePath}/exportDictItemData`, params);

//导入字典数据
const importDictItemData = params =>
  http.postUpdateFile<RestResult<string>>(
    `${basePath}/importDictItemData`,
    params
  );

export {
  getDictTypeList,
  searchDictItemData,
  searchDictItemDataNoPage,
  getLinkedDictionaryData,
  getDictItemForSelect,
  getCanLinkedDictTypeList,
  addDictionaryType,
  updateDictionaryType,
  getDictTypeDetail,
  deleteDictionaryType,
  addDictItemData,
  updateDictItemData,
  deleteDictItemData,
  exportDictItemData,
  importDictItemData
};
