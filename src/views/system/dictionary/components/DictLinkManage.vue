<template>
  <el-dialog
    title="字典关联数据管理"
    v-model="dialogVisible"
    :fullscreen="true"
    :before-close="cancel"
  >
    <el-row :gutter="30">
      <el-col :span="6">
        <el-card :style="{ height: linkedManageFrameHeight() }">
          <template #header>
            <span>父字典数据</span>
          </template>
          <div class="pb-1">
            <el-input
              v-model="parentDictKey"
              style="width: 100%"
              :suffix-icon="useRenderIcon('EP-Search')"
              placeholder="请输入关键字"
              clearable
            />
          </div>
          <el-table
            v-loading="linkedTableLoading"
            :data="
              parentDictData.filter(
                data =>
                  !parentDictKey ||
                  data.itemLabel.includes(parentDictKey) ||
                  data.itemValue.includes(parentDictKey)
              )
            "
            stripe
            highlight-current-row
            :height="linkedManageTableHeight()"
            @current-change="parentDictChange"
          >
            <el-table-column label="字典名称" prop="itemLabel" sortable />
            <el-table-column label="字典标识" prop="itemValue" sortable />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card :style="{ height: linkedManageFrameHeight() }">
          <template #header>
            <span>关联字典数据</span>
          </template>
          <div class="pb-1">
            <el-input
              v-model="linkedDictKey"
              style="width: 100%"
              placeholder="请输入关键字"
              :suffix-icon="useRenderIcon('EP-Search')"
              clearable
            />
          </div>
          <el-table
            v-loading="linkedTableLoading"
            stripe
            :data="linkedDictData"
            :height="linkedManageTableHeight()"
          >
            <el-table-column label="字典名称" prop="itemLabel" sortable />
            <el-table-column label="字典标识" prop="itemValue" sortable />
            <el-table-column label="操作">
              <template v-slot="scope">
                <el-button
                  icon="el-icon-right"
                  type="text"
                  @click="removeDictLinked(scope.$index, scope.row)"
                  >移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-card :style="{ height: linkedManageFrameHeight() }">
          <template #header>
            <span>待关联字典数据</span>
          </template>
          <div class="pb-1">
            <el-input
              v-model="needLinkedKey"
              style="width: 100%"
              placeholder="请输入关键字"
              :suffix-icon="useRenderIcon('EP-Search')"
              clearable
            />
          </div>
          <el-table
            v-loading="linkedTableLoading"
            stripe
            :data="
              needLinkedDictData.filter(
                data =>
                  (!needLinkedKey ||
                    data.itemLabel.includes(needLinkedKey) ||
                    data.itemValue.includes(needLinkedKey)) &&
                  (data.parentItemValue == null ||
                    data.parentItemValue.length === 0)
              )
            "
            :height="linkedManageTableHeight()"
          >
            <el-table-column label="操作">
              <template v-slot="scope">
                <el-button
                  icon="el-icon-back"
                  type="text"
                  @click="addDictLinked(scope.$index, scope.row)"
                  :disabled="selectedParentDict == null"
                >
                  添加
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="字典名称" prop="itemLabel" sortable />
            <el-table-column label="字典标识" prop="itemValue" sortable />
            <el-table-column
              label="上联字典名称"
              prop="parentItemLabel"
              sortable
            />
            <el-table-column
              label="上联字典标识"
              prop="parentItemValue"
              sortable
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <el-button type="primary" @click="cancel()" icon="el-icon-back">
        返 回
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch, getCurrentInstance } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ResultStatus } from "@/utils/http/types";
import {
  DictionaryItemInfo,
  DictionaryTypeInfo,
  searchDictItemDataNoPage,
  getLinkedDictionaryData,
  updateDictItemData
} from "@/views/system/dictionary/api/DictManageApi";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  parentDictType: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  parentDictData: [],
  linkedDictData: [],
  needLinkedDictData: [],
  dialogVisible: false,
  linkedTableLoading: false,
  parentDictKey: "",
  linkedDictKey: "",
  needLinkedKey: "",
  selectedParentDictType: null as DictionaryTypeInfo,
  selectedParentDict: null as DictionaryItemInfo
});
const {
  parentDictData,
  linkedDictData,
  needLinkedDictData,
  dialogVisible,
  parentDictKey,
  linkedTableLoading,
  linkedDictKey,
  needLinkedKey,
  selectedParentDict
} = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  state.selectedParentDictType = newValue.parentDictType;

  //加载数据
  await loadNeedLinkedDictData();
  await loadParentDictData();
});

//选择父字典
const parentDictChange = (row: any) => {
  if (row) {
    state.selectedParentDict = row;
    refreshLinkedDictData();
  }
};

//刷新字典关联表数据
const refreshLinkedDictData = () => {
  state.linkedTableLoading = true;
  getLinkedDictionaryData(
    state.selectedParentDict.dictTypeSign,
    state.selectedParentDict.itemValue
  ).then(result => {
    state.linkedDictData = result.data;
    state.linkedTableLoading = false;
  });
};

//移除字典关联关系
const removeDictLinked = (index, row) => {
  row.parentItemLabel = null;
  row.parentItemValue = null;
  updateDictLinkedData(row, "已成功移除字典关联数据！");
};

//添加字典关联关系
const addDictLinked = (index, row) => {
  row.parentItemLabel = state.selectedParentDict.itemLabel;
  row.parentItemValue = state.selectedParentDict.itemValue;
  updateDictLinkedData(row, "已成功添加字典关联数据！");
};

//更新关系数据
const updateDictLinkedData = async (row, successMsg) => {
  updateDictItemData(row).then(result => {
    if (result.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: successMsg,
        type: "success"
      });

      //刷新数据
      refreshLinkedDictData();
      loadNeedLinkedDictData();
    }
  });
};

//加载待关联字典数据
const loadNeedLinkedDictData = async () => {
  searchDictItemDataNoPage({
    dictTypeSign: state.selectedParentDictType.linkedTypeSign
  }).then(result => {
    state.needLinkedDictData = result.data;
  });
};

//加载父字典数据
const loadParentDictData = async () => {
  searchDictItemDataNoPage({
    dictTypeSign: state.selectedParentDictType.dictTypeSign
  }).then(result => {
    state.parentDictData = result.data;
  });
};

//关联字典操作框高度
const linkedManageFrameHeight = () => `calc(100vh - 190px)`;

//表格高度
const linkedManageTableHeight = () => `calc(100vh - 310px)`;

const cancel = () => {
  emit("update:visible", false);
};
</script>
