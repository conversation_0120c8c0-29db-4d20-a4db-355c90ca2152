<template>
  <el-dialog
    title="导入字典数据"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="45%"
  >
    <div class="flex justify-center w-full">
      <span>重复数据处理模式：</span>
      <el-radio-group v-model="importForm.conflictStrategy">
        <el-radio-button :label="1">终止导入</el-radio-button>
        <el-radio-button :label="2">跳过</el-radio-button>
        <el-radio-button :label="3">覆盖</el-radio-button>
      </el-radio-group>
    </div>
    <div class="flex justify-center">
      <el-upload
        :drag="false"
        ref="menuUpload"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        :limit="1"
        :file-list="importForm.fileList"
        name="fileList"
        :auto-upload="false"
        :on-change="handleUploadChange"
      >
        <div class="w-full justify-center hover:text-blue-500">
          <iconify-icon-offline
            icon="EP-UploadFilled"
            class="w-[150px] h-[150px]"
          />
          <div class="flex justify-center">
            <span>请 <em class="font-bold">点击上传</em></span>
          </div>
        </div>
      </el-upload>
    </div>
    <div class="flex justify-center mt-2 text-orange-700">
      <span>注：只能上传Excel文件</span>
    </div>
    <template #footer>
      <el-button
        type="primary"
        v-show="importForm.fileList.length > 0"
        :icon="useRenderIcon('EP-Upload')"
        @click="importDictHandler"
      >
        导 入
      </el-button>
      <el-button @click="cancel" :icon="useRenderIcon('EP-CircleClose')">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch, getCurrentInstance } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { message } from "@/utils/message";
import { importDictItemData } from "@/views/system/dictionary/api/DictManageApi";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  typeSign: {
    type: String
  }
});

//定义事件
const emit = defineEmits(["update:visible", "import-success"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  importForm: {
    conflictStrategy: 1,
    fileList: []
  }
});
const { dialogVisible, importForm } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  state.importForm.fileList = [];
});

//上传列表改变
const handleUploadChange = (file: any) => (state.importForm.fileList = [file]);

//导入字典数据
const importDictHandler = async () => {
  const formData = new FormData();
  formData.append("file", state.importForm.fileList[0].raw);
  formData.append(
    "conflictStrategy",
    state.importForm.conflictStrategy.toString()
  );
  message("开始上传字典数据...", { type: "success" });
  await importDictItemData(formData).then(() => {
    $notify({
      title: "提示",
      message: "恭喜！已成功导入菜单数据!",
      type: "success"
    });
    cancel();
    emit("import-success");
  });
  state.importForm.fileList = [];
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
