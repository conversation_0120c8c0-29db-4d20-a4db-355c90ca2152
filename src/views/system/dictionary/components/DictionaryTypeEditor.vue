<template>
  <div>
    <avue-form ref="typeEditFormRef" v-model="editForm" :option="formOption" />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, ref, reactive, getCurrentInstance } from "vue";
import { SelectOption } from "@/utils/CommonTypes";
import { ResultStatus } from "@/utils/http/types";
import { validSignId } from "@/utils/validator";
import {
  DictionaryTypeDetailInfo,
  DictionaryTypeInfo,
  getCanLinkedDictTypeList,
  getDictItemForSelect,
  addDictionaryType,
  updateDictionaryType,
  getDictTypeDetail
} from "@/views/system/dictionary/api/DictManageApi";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;

const typeEditFormRef = ref();
const bussModuleSign = "buss_module_dict";

//字典类型标识校验
const validateTypeSign = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：buss_type"));
  }
};

const formOption = reactive({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  menuPosition: "right",
  column: [
    {
      label: "类型标识",
      prop: "dictTypeSign",
      maxlength: 50,
      showWordLimit: true,
      disabled: false,
      rules: [
        {
          required: true,
          message: "请填写字典类型标识",
          trigger: "blur"
        },
        { validator: validateTypeSign, trigger: "blur" }
      ]
    },
    {
      label: "类型名称",
      prop: "dictTypeName",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请填写字典类型名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "业务模块标识",
      prop: "moduleSign",
      type: "select",
      filterable: true,
      dicData: [],
      props: {
        label: "label",
        value: "value"
      }
    },
    {
      label: "下级关联字典",
      prop: "linkedTypeSign",
      type: "select",
      filterable: true,
      dicData: [],
      props: {
        label: "dictTypeName",
        value: "dictTypeSign"
      }
    },
    {
      label: "首层字典",
      prop: "firstLevel",
      type: "switch",
      value: false,
      dicData: [
        { label: "否", value: false },
        { label: "是", value: true }
      ]
    },
    {
      label: "排序",
      prop: "sortIndex",
      type: "number",
      min: 0,
      max: 999,
      value: 0
    },
    {
      label: "备注",
      prop: "remarks",
      maxlength: 200,
      showWordLimit: true,
      type: "textarea",
      minRows: 3,
      maxRows: 5,
      span: 24
    }
  ]
});

//声明事件
const emit = defineEmits(["submit"]);

//数据对象
const state = reactive({
  editForm: {} as DictionaryTypeDetailInfo,
  bussModuleData: [] as Array<SelectOption>,
  canLikedDictTypeData: [] as Array<DictionaryTypeInfo>
});
const { editForm } = toRefs(state);

//加载类型详细信息
const loadTypeDetailInfo = async (typeSign: string) => {
  formOption.column[0].disabled = true;
  getDictTypeDetail(typeSign).then(res => {
    state.editForm = res.data;
  });
};

//加载业务标识信息
const loadBussModuleData = async () => {
  getDictItemForSelect(bussModuleSign).then(res => {
    state.bussModuleData = res.data;
    typeEditFormRef.value.updateDic("moduleSign", state.bussModuleData);
  });
};

const loadCanLikedDictTypeData = async (typeSign: string) => {
  getCanLinkedDictTypeList(typeSign ? typeSign : bussModuleSign).then(res => {
    state.canLikedDictTypeData = res.data;
    typeEditFormRef.value.updateDic(
      "linkedTypeSign",
      state.canLikedDictTypeData
    );
  });
};

//重置表单数据
const resetEditForm = (form: DictionaryTypeDetailInfo) => {
  state.editForm = form;
  formOption.column[0].disabled = false;
};

//提交表单
const submitForm = () => {
  typeEditFormRef.value.validate((valid, done) => {
    if (valid) {
      if (!state.editForm.id) {
        addDictionaryType(state.editForm).then(res => {
          if (res.status == ResultStatus.Success) {
            $notify({
              title: "提示",
              message: "已成功添加字典类型信息！",
              type: "success"
            });
            emit("submit", true);
          }
        });
      } else {
        updateDictionaryType(state.editForm).then(res => {
          if (res.status == ResultStatus.Success) {
            $notify({
              title: "提示",
              message: "已成功保存字典类型信息！",
              type: "success"
            });
            emit("submit", true);
          }
        });
      }
      done();
    }
  });
};

defineExpose({
  resetEditForm,
  loadTypeDetailInfo,
  loadBussModuleData,
  loadCanLikedDictTypeData,
  submitForm
});
</script>
