import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/cache/manage`;

//缓存管理信息
export type CacheManageInfo = {
  cacheCode: string;
  cacheName: string;
  cacheType: string;
  cacheKey: string;
  dataNum: number;
};

//获取所有缓存数据管理信息
const getAllCacheManageInfos = () =>
  http.get<any, RestResult<CacheManageInfo[]>>(
    `${basePath}/getAllCacheManageInfos`
  );

//重新加载缓存数据
const reloadCacheData = (cacheCode: string) =>
  http.get<any, RestResult<string>>(
    `${basePath}/reloadCacheData?cacheCode=${cacheCode}`
  );

export { getAllCacheManageInfos, reloadCacheData };
