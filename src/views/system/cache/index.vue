<template>
  <el-space
    wrap
    :size="10"
    :fill="true"
    :fill-ratio="10"
    style="padding-top: 15px"
  >
    <cache-panel
      v-for="(item, index) in data"
      :key="index"
      :info="item"
      @refresh-success="refreshSuccessHandler"
    />
  </el-space>
</template>

<script lang="ts" setup>
import { toRefs, reactive, onMounted } from "vue";
import CachePanel from "@/views/system/cache/components/CachePanel.vue";
import { ResultStatus } from "@/utils/http/types";
import {
  CacheManageInfo,
  getAllCacheManageInfos
} from "@/views/system/cache/api/CacheManageApi";

//数据对象
const state = reactive({
  data: [] as Array<CacheManageInfo>
});

const { data } = toRefs(state);

const loadManageInfo = () => {
  state.data = [];
  getAllCacheManageInfos().then(res => {
    if (res.status === ResultStatus.Success) {
      state.data = res.data;
    }
  });
};

const refreshSuccessHandler = () => {
  loadManageInfo();
};

onMounted(() => {
  loadManageInfo();
});
</script>
