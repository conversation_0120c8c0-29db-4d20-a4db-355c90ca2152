<template>
  <div
    class="w-[260px] border-[2px] border-green-800 border-opacity-60 p-3 rounded-xl"
  >
    <div class="flex-bc border-b-[1px] pb-2 border-green-800 border-opacity-60">
      <span class="text-1xl font-bold">{{ props.info.cacheName }}</span>
      <span class="text-1xl font-bold text-blue-800 dark:text-blue-300">
        {{ props.info.dataNum }}
      </span>
    </div>
    <div class="grid gap-2 grid-cols-1 text-xs/[12px] pt-3">
      <p>缓存类型：{{ props.info.cacheType }}</p>
      <p>缓存Key：{{ props.info.cacheKey }}</p>
    </div>
    <div class="flex justify-end">
      <el-link
        type="primary"
        :icon="useRenderIcon('EP-RefreshLeft')"
        :underline="false"
        @click="refreshCacheDataHandler"
        v-auth="AdminEnum.SuperAdmin"
      >
        刷新
      </el-link>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getCurrentInstance } from "vue";
import { ResultStatus } from "@/utils/http/types";
import { reloadCacheData } from "@/views/system/cache/api/CacheManageApi";
import { AdminEnum } from "@/utils/CommonTypes";

const { $confirm, $notify } =
  getCurrentInstance().appContext.config.globalProperties;

//定义事件
const emit = defineEmits(["refresh-success"]);

//组件属性
const props = defineProps({
  info: {
    type: Object
  }
});

//刷新缓存处理
const refreshCacheDataHandler = () => {
  $confirm(`您确定要刷新 '${props.info.cacheName}' 的缓存吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    reloadCacheData(props.info.cacheCode).then(res => {
      if (res.status === ResultStatus.Success) {
        $notify({
          title: "提示",
          message: `${props.info.cacheName} 缓存刷新成功！`,
          type: "success"
        });
        emit("refresh-success");
      }
    });
  });
};
</script>
