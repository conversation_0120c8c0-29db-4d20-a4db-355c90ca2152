<template>
  <div class="dark:bg-dark-color">
    <el-page-header @back="jumpTo('roleManage')" class="mb-2">
      <template #content>
        <span class="mr-3"> 批量授权 - {{ selectedRole.roleName }} </span>
      </template>
    </el-page-header>
    <avue-crud
      ref="permCrudRef"
      :data="permData"
      :option="permTableOption"
      v-model:page="page"
      v-model:search="searchForm"
      @on-load="tableLoadHandler"
      @refresh-change="loadPermissionData"
      @selection-change="permSelectionChange"
      @row-del="removeUserPerm"
      @search-change="doTableSearch"
    >
      <!-- 自定义左侧操作按钮 -->
      <template #menu-left>
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="openAddPermDialog"
        >
          添加权限
        </el-button>
      </template>

      <!-- 自定义右侧操作按钮 -->
      <template #menu-right="{ size }">
        <el-tooltip
          effect="dark"
          content="批量移除"
          placement="top"
          :open-delay="300"
        >
          <el-button
            :icon="useRenderIcon('EP-DeleteFilled')"
            plain
            circle
            :size="size"
            :disabled="selectPerms.length < 1"
            @click="batchRemoveUserPerm"
          />
        </el-tooltip>
      </template>

      <!-- 自定义部门搜索条件 -->
      <template #deptPathName-search="{ disabled }">
        <div class="flex justify-items-start items-center w-full">
          <dept-tree-select
            :disabled="disabled"
            v-model:dept-id="searchForm.deptId"
            clearable
            filterable
          />
          <el-radio-group v-model="searchForm.deptType" class="w-[140px] ml-2">
            <el-radio-button label="0">精确</el-radio-button>
            <el-radio-button label="1">关联</el-radio-button>
          </el-radio-group>
        </div>
      </template>
    </avue-crud>

    <dept-user-selector
      v-model:visible="userSelectVisible"
      dialog-title="添加权限"
      @user-select="userSelectHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, ref, reactive, getCurrentInstance, computed } from "vue";
import { RoleInfo } from "@/views/system/permmanage/api/RoleManageApi";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import DeptUserSelector from "@/views/system/usermanage/components/DeptUserSelector.vue";
import { UserInfoForSelect } from "@/views/system/usermanage/api/UserManage";
import { ResultStatus } from "@/utils/http/types";
import { pageSizeOptions, defaultPageSize } from "@/utils/page_util";
import {
  UserPermissionInfo,
  getRolePermissionData,
  addUserToRolePermission,
  removePermRelations
} from "@/views/system/permmanage/api/PermissionManageApi";

const permCrudRef = ref();

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  selectedRole: {
    type: Object as PropType<RoleInfo>
  }
});

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 345;
});

const permTableOption = reactive({
  selection: true,
  reserveSelection: true,
  rowKey: "relationId",
  align: "center",
  menuAlign: "center",
  height: tableHeight,
  stripe: true,
  border: true,
  searchMenuSpan: 4,
  emptyBtnText: "重置",
  emptyBtnIcon: "el-icon-refresh-left",
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  column: [
    { label: "角色", prop: "roleName" },
    {
      label: "部门",
      prop: "deptPathName",
      search: true,
      type: "select",
      searchSpan: 6
    },
    { label: "用户账号", prop: "userName", search: true, searchSpan: 6 },
    { label: "姓名", prop: "realName", search: true, searchSpan: 6 },
    { label: "添加时间", prop: "createTime" }
  ]
});

//数据对象
const state = reactive({
  permData: [] as Array<UserPermissionInfo>,
  selectPerms: [] as Array<UserPermissionInfo>,
  userSelectVisible: false,
  searchForm: {
    roleId: props.selectedRole.roleId,
    deptType: "1",
    deptId: "",
    userName: "",
    realName: "",
    pageNo: 1,
    pageSize: defaultPageSize
  },
  page: {
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  }
});

const { permData, selectPerms, userSelectVisible, searchForm, page } =
  toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//表格搜索按钮触发
const doTableSearch = (params, done) => {
  state.page.currentPage = 1;
  permCrudRef.value.clearSelection();
  loadPermissionData();
  if (done != null) {
    done();
  }
};

//表格数据加载
const tableLoadHandler = (params, done) => {
  loadPermissionData();
  if (done != null) {
    done();
  }
};

//加载权限数据
const loadPermissionData = async () => {
  state.searchForm.pageNo = state.page.currentPage;
  state.searchForm.pageSize = state.page.pageSize;
  state.searchForm.roleId = props.selectedRole.roleId;
  state.permData = [];
  getRolePermissionData(state.searchForm).then(result => {
    state.permData = result.data;
    state.permData.forEach(row => {
      row.roleName = props.selectedRole.roleName;
    });
    state.page.total = parseInt(result.total);
  });
};

//打开添加用户权限对话框
const openAddPermDialog = () => {
  state.userSelectVisible = true;
};

//添加权限已选择用户
const userSelectHandler = (users: Array<UserInfoForSelect>) => {
  batchAddUserPermissions(users);
};

//批量添加用户权限
const batchAddUserPermissions = async (users: Array<UserInfoForSelect>) => {
  const userIdList = [];
  users.forEach(u => {
    userIdList.push(u.userId);
  });
  addUserToRolePermission({
    userIds: userIdList,
    roleId: props.selectedRole.roleId
  }).then(result => {
    if (result.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功添加" + userIdList.length + "条权限数据！",
        type: "success"
      });

      loadPermissionData();
    }
  });
};

//移除单条权限数据
const removeUserPerm = (userPerm: UserPermissionInfo) => {
  $confirm("您确定要移除 '" + userPerm.realName + "' 的权限吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const idArray = [userPerm.relationId];
    removePermRelations({ relationIds: idArray }).then(result => {
      if (result.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功移除用户权限！",
          type: "success"
        });
        loadPermissionData();
      }
    });
  });
};

//权限选择改变
const permSelectionChange = (perms: Array<UserPermissionInfo>) => {
  state.selectPerms = perms;
};

//批量移除用户权限
const batchRemoveUserPerm = () => {
  $confirm(
    "您确定要移除已选择的 '" + state.selectPerms.length + "' 条权限数据吗？",
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    const idArray = [];
    state.selectPerms.forEach(r => idArray.push(r.relationId));
    removePermRelations({ relationIds: idArray }).then(result => {
      if (result.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功移除" + idArray.length + "条权限数据！",
          type: "success"
        });
        doTableSearch(null, null);
      }
    });
  });
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
