<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3">用户权限 - {{ props.user?.realName }} </span>
        </template>
      </el-page-header>
    </template>
    <div class="flex justify-end pb-1.5">
      <el-button
        type="primary"
        :icon="useRenderIcon('RI-AddLine')"
        @click="addPermDataRow"
      >
        添加
      </el-button>
    </div>
    <el-table
      :data="permData"
      stripe
      style="width: 100%"
      class="h-full"
      :header-cell-style="{ textAlign: 'center' }"
      row-key="relationId"
    >
      <el-table-column prop="roleName" label="角色" width="180">
        <template #default="{ row }">
          <el-select
            v-model="row.roleId"
            clearable
            placeholder="请选择角色"
            v-if="row.relationId.startsWith(templateRelationSign)"
          >
            <el-option
              v-for="item in roleData"
              :key="item.roleId"
              :label="item.roleName"
              :value="item.roleId"
            />
          </el-select>
          <span v-else>{{ row.roleName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="组织" width="180">
        <template #default="{ row }">
          <DeptTreeSelect
            v-if="row.relationId.startsWith(templateRelationSign)"
            v-model:dept-id="row.deptId"
          />
          <span v-else>{{ row.deptName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{ row, $index }">
          <div class="w-full text-center">
            <el-link
              type="primary"
              :icon="useRenderIcon('RI-Save3Line')"
              v-if="row.relationId.startsWith(templateRelationSign)"
              @click="savePermData(row)"
            >
              保存
            </el-link>
            <el-link
              type="primary"
              :icon="useRenderIcon('EP-RemoveFilled')"
              class="ml-2"
              @click.prevent="removePermDataRow(row, $index)"
            >
              移除
            </el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch, getCurrentInstance } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import { ResultStatus } from "@/utils/http/types";
import { generateUniqueIdByTime } from "@/utils/IdUtil";
import {
  DataPermInfo,
  SimpleRoleInfo,
  getUserPermissionData,
  savePermissionData,
  removePermissionData,
  getCurrentUserRoleInfos
} from "@/views/system/permmanage/api/DataPermissionApi";

const templateRelationSign = "temp-";
const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  user: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  permData: [] as Array<DataPermInfo>,
  roleData: [] as Array<SimpleRoleInfo>
});
const { drawerVisible, permData, roleData } = toRefs(state);

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  await getUserPermData(newValue.user?.userId);
  loadRoleData();
});

//查询用户数据权限
const getUserPermData = async (userId: string) => {
  state.permData = [];
  await getUserPermissionData(userId).then(result => {
    if (result.status == ResultStatus.Success) {
      state.permData = result.data;
    }
  });
};

//加载角色列表数据
const loadRoleData = () => {
  state.roleData = [];
  getCurrentUserRoleInfos().then(res => (state.roleData = res.data));
};

//添加新数据权限
const addPermDataRow = () => {
  const tempKey = templateRelationSign + generateUniqueIdByTime();
  state.permData.push({
    relationId: tempKey,
    userId: props.user.userId,
    realName: props.user.realName,
    roleId: null,
    roleName: null,
    deptId: null,
    deptName: null
  });
};

//前端校验当前权限是否已存在
const validateExistPermData = (row: DataPermInfo) => {
  let valResult = true;
  state.permData.forEach(d => {
    if (
      d.relationId &&
      row.userId === d.userId &&
      row.roleId === d.roleId &&
      row.deptId === d.deptId &&
      !d.relationId.startsWith(templateRelationSign)
    ) {
      valResult = false;
      $notify({
        title: "提示",
        message: "权限已存在！",
        type: "warning"
      });
    }
  });
  return valResult;
};

//保存权限数据
const savePermData = async (row: DataPermInfo) => {
  if (validateExistPermData(row)) {
    await savePermissionData({
      userId: row.userId,
      roleId: row.roleId,
      deptId: row.deptId
    }).then(result => {
      const data = result.data;
      row.relationId = data.relationId;
      row.deptName = data.deptName;
      row.roleName = data.roleName;
      $notify({
        title: "提示",
        message: "已成功保存权限数据！",
        type: "success"
      });
    });
  }
};

//移除权限数据
const removePermDataRow = (row, index) => {
  if (row.relationId.startsWith(templateRelationSign)) {
    state.permData.splice(index, 1);
  } else {
    $confirm("确定要移除权限数据吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(() => {
      removePermissionData({
        userId: row.userId,
        roleId: row.roleId,
        deptId: row.deptId
      }).then(() => {
        $notify({
          title: "提示",
          message: "已成功移除权限数据！",
          type: "success"
        });
        state.permData.splice(index, 1);
      });
    });
  }
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
