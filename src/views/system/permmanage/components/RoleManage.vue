<template>
  <div class="p-0.5">
    <avue-crud
      :data="roleData"
      :option="option"
      @refresh-change="reloadRoleData"
      @row-update="updateRoleInfo"
      @row-save="addRoleInfo"
      @row-del="deleteRoleInfo"
    >
      <template #menu-right="{ size }">
        <div class="float-left">
          <el-input
            clearable
            :suffix-icon="useRenderIcon('EP-Search')"
            :size="size"
            placeholder="角色名称"
            v-model="keyWord"
            @blur="keyWord = $event.target.value.trim()"
          />
        </div>
        <el-divider direction="vertical" border-style="dashed" />
        <el-button
          color="#106ebe"
          :icon="useRenderIcon('RI-StackFill')"
          :size="size"
          @click="jumpTo('roleTypeManage')"
          v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
        >
          类型管理
        </el-button>
        <el-divider direction="vertical" border-style="dashed" />
        <el-button
          color="#106ebe"
          :icon="useRenderIcon('RI-SendToBack')"
          :size="size"
          @click="jumpTo('roleGroupManage')"
          v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
        >
          分组管理
        </el-button>
      </template>
      <template #menu="{ row, size }">
        <el-dropdown class="pl-2">
          <span class="dd-dropdown-link-list">
            权限
            <IconifyIconOffline icon="EP-ArrowDown" />
          </span>
          <template #dropdown>
            <div class="pt-2 pb-2">
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-link
                    plain
                    :underline="false"
                    :size="size"
                    type="primary"
                    :icon="useRenderIcon('RI-DashboardFill')"
                    @click="openOperatePermDrawer(row)"
                  >
                    功能权限
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-link
                    plain
                    :underline="false"
                    :size="size"
                    type="primary"
                    :icon="useRenderIcon('RI-ListSettingsFill')"
                    @click="openDataPermDrawer(row)"
                  >
                    数据权限
                  </el-link>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-link
                    plain
                    :underline="false"
                    :size="size"
                    type="primary"
                    :icon="useRenderIcon('RI-TeamFill')"
                    @click="jumpToBatchPermission(row)"
                  >
                    批量授权
                  </el-link>
                </el-dropdown-item>
              </el-dropdown-menu>
            </div>
          </template>
        </el-dropdown>
      </template>
    </avue-crud>

    <!-- 功能权限管理抽屉 -->
    <operate-permission-drawer
      v-model:visible="operatePermissionVisible"
      :data="resourceData"
      :role-id="selectedRoleId"
      size="30%"
    />

    <!-- 数据权限管理抽屉 -->
    <data-permission-drawer
      v-model:visible="dataPermissionVisible"
      :role-id="selectedRoleId"
      :role-name="selectedRoleName"
      size="50%"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, toRefs } from "vue";
import DataPermissionDrawer from "@/views/system/permmanage/components/DataPermissionDrawer.vue";
import OperatePermissionDrawer from "@/views/system/permmanage/components/OperatePermissionDrawer.vue";
import { ResultStatus } from "@/utils/http/types";
import { ElMessageBox, ElNotification } from "element-plus";
import { hasAuth } from "@/router/utils";
import { AdminEnum } from "@/utils/CommonTypes";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getGroupSelectData } from "@/views/system/permmanage/api/GroupManageApi";
import {
  addRole,
  deleteRole,
  getRoleData,
  RoleInfo,
  updateRole
} from "@/views/system/permmanage/api/RoleManageApi";
import {
  BaseRoleTypeInfo,
  getTypeSelectData
} from "@/views/system/permmanage/api/TypeManageApi";
import {
  getSimpleResourceData,
  SimpleResource
} from "@/views/system/menumanage/api/MenuManageApi";

const typeDictData = ref([]);
const groupDictData = ref([]);

//加载角色类型下拉框数据
getTypeSelectData().then(result => {
  const data: Array<BaseRoleTypeInfo> = result.data;
  data.forEach(d => {
    typeDictData.value.push({
      label: d.typeName,
      value: d.typeId
    });
  });
});

//加载角色分组下拉框数据
getGroupSelectData().then(result => {
  groupDictData.value = result.data;
});

//角色数据Crud配置项
const option = reactive({
  align: "center",
  menuAlign: "center",
  index: true,
  border: true,
  stripe: true,
  rowKey: "roleId",
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {
      label: "角色标识",
      prop: "roleId",
      viewDisplay: false,
      editDisabled: true,
      showColumn: false,
      hide: true,
      maxlength: 20,
      showWordLimit: true
    },
    {
      label: "角色名称",
      prop: "roleName",
      maxlength: 25,
      showWordLimit: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请填写角色名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "角色分组",
      prop: "groupName",
      addDisplay: false,
      sortable: true,
      editDisplay: false
    },
    {
      label: "角色分组",
      prop: "groupId",
      showColumn: false,
      hide: true,
      type: "select",
      dicData: groupDictData
    },
    {
      label: "角色类型",
      sortable: true,
      prop: "roleTypeName",
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "角色类型",
      prop: "roleType",
      showColumn: false,
      hide: true,
      type: "select",
      dicData: typeDictData,
      rules: [
        {
          required: true,
          message: "请选择角色类型",
          trigger: "blur"
        }
      ]
    },
    {
      label: "角色类型级别",
      prop: "roleTypeLevel",
      addDisplay: false,
      sortable: true,
      editDisplay: false
    },
    {
      label: "角色描述",
      prop: "roleInfo",
      type: "textarea",
      maxlength: 120,
      span: 24,
      showWordLimit: true
    },
    {
      label: "操作人",
      prop: "editUser",
      sortable: true,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "创建时间",
      prop: "loadTime",
      sortable: true,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "更新时间",
      prop: "editTime",
      sortable: true,
      addDisplay: false,
      editDisplay: false
    }
  ]
});

//数据对象
const state = reactive({
  operatePermissionVisible: false,
  dataPermissionVisible: false,
  selectedRoleId: "",
  selectedRoleName: "",
  data: [],
  resourceData: [],
  keyWord: ""
});

const {
  operatePermissionVisible,
  dataPermissionVisible,
  selectedRoleId,
  selectedRoleName,
  resourceData,
  keyWord
} = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to", "role-select_change"]);

//加载角色数据
const loadRoleData = async () => {
  await getRoleData().then(result => {
    state.data = result.data;
  });
};
loadRoleData();

//加载菜单数据
const loadResourceData = async () => {
  await getSimpleResourceData().then(result => {
    if (result.status == ResultStatus.Success) {
      dealResourceData(result.data);
    }
  });
};
loadResourceData();

//处理资源数据，转成成树结构数据
const dealResourceData = (resData: Array<SimpleResource>) => {
  //逻辑根节点
  const rootNode: SimpleResource = {
    resourceId: "-1",
    parentId: "",
    resourceName: "系统菜单",
    resourceIcon: "EP-Menu",
    children: []
  };

  //转换成树数据
  for (const resNode of resData) {
    resNode.children = [];
    if (resNode.parentId === "-1") {
      rootNode.children.push(resNode);
    }
    for (const childNode of resData) {
      if (childNode.parentId === resNode.resourceId) {
        resNode.children.push(childNode);
      }
    }
  }

  //push到绑定数据
  state.resourceData.push(rootNode);
};

//刷新角色数据
const reloadRoleData = async () => {
  state.data = [];
  await loadRoleData();
};

//新增角色
const addRoleInfo = async (row, done) => {
  await addRole(row as RoleInfo).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功新增角色信息！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//更新角色
const updateRoleInfo = async (row, index, done) => {
  await updateRole(row as RoleInfo).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功更新角色信息！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//删除角色
const deleteRoleInfo = async (row, index, done) => {
  await ElMessageBox.confirm(`确定要删除 '${row.roleName}' 么？`, `提示`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  await deleteRole({ roleId: row.roleId }).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: `已成功删除角色：${row.roleName}`,
        type: "success"
      });
      done(row);
    }
  });
};

//角色搜索
const roleData = computed(() => {
  if (state.keyWord != null && state.keyWord.length > 0) {
    return state.data.filter(item => {
      if (item.roleName.includes(state.keyWord)) {
        return item;
      }
    });
  } else {
    return state.data;
  }
});

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

//打开操作权限管理抽屉
const openOperatePermDrawer = (row: any) => {
  const role: RoleInfo = row as RoleInfo;
  state.selectedRoleId = role.roleId;
  state.operatePermissionVisible = true;
};

//打开数据权限管理抽屉
const openDataPermDrawer = (row: any) => {
  state.selectedRoleId = row.roleId;
  state.selectedRoleName = row.roleName;
  state.dataPermissionVisible = true;
};

//跳转到批量授权界面
const jumpToBatchPermission = (row: RoleInfo) => {
  emit("role-select_change", row);
  jumpTo("batchPermission");
};
</script>
