<template>
  <div class="dark:bg-dark-color">
    <el-page-header @back="jumpTo('roleManage')" class="mb-2">
      <template #content>
        <span class="mr-3"> 角色分组管理 </span>
      </template>
    </el-page-header>
    <avue-crud
      :data="data"
      :option="options"
      @row-save="addRoleGroup"
      @row-update="updateRoleGroup"
      @row-del="deleteRoleGroup"
      @refresh-change="reloadGroupData"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import {
  addGroup,
  deleteGroup,
  getGroupData,
  updateGroup
} from "@/views/system/permmanage/api/GroupManageApi";
import { ResultStatus } from "@/utils/http/types";
import { ElNotification, ElMessageBox } from "element-plus";
import { validSignId } from "@/utils/validator";

const validateCollectId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：business_group"));
  }
};

const options = ref(null);
options.value = {
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  rowKey: "groupId",
  viewBtn: true,
  column: [
    {
      label: "分组标识",
      prop: "groupId",
      editDisabled: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请填写分组类型标识",
          trigger: "blur"
        },
        { validator: validateCollectId, trigger: "blur" }
      ]
    },
    {
      label: "分组名称",
      prop: "groupName",
      sortable: true,
      rules: [
        {
          required: true,
          message: "请填写分组名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "创建时间",
      prop: "createTime",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    },
    {
      label: "更新时间",
      prop: "updateTime",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    },
    {
      label: "操作人",
      prop: "updateUid",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    }
  ]
};

//数据对象
const state = reactive({
  data: []
});

const { data } = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//加载分组数据
const loadGroupData = async () => {
  await getGroupData().then(result => {
    state.data = result.data;
  });
};
loadGroupData();

//刷新分组数据
const reloadGroupData = async () => {
  state.data = [];
  await loadGroupData();
};

//新增分组
const addRoleGroup = async (row, done) => {
  await addGroup(row).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功新增角色分组！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//更新类型
const updateRoleGroup = async (row, index, done) => {
  await updateGroup(row).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功更新角色分组！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//删除类型
const deleteRoleGroup = async (row, index, done) => {
  await ElMessageBox.confirm(`确定要删除 '${row.groupName}' 么？`, `提示`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  await deleteGroup({ groupId: row.groupId }).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: `已成功删除分组：${row.groupName}`,
        type: "success"
      });
      done(row);
    }
  });
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
