<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3"> 功能权限 </span>
        </template>
      </el-page-header>
    </template>
    <template #default>
      <div v-loading="commitLoading">
        <el-input
          clearable
          :suffix-icon="useRenderIcon('EP-Search')"
          placeholder="资源名称"
          v-model="resKeyWord"
        />
        <div class="pt-1.5 h-screen-gap overflow-y-auto">
          <el-tree
            ref="treeRef"
            :data="resTreeData"
            show-checkbox
            node-key="resourceId"
            :default-expand-all="true"
            :default-checked-keys="checkedKeys"
            :props="defaultProps"
            :filter-node-method="filterResource"
          >
            <template #default="{ node, data }">
              <IconifyIconOffline :icon="data.resourceIcon" />
              <span class="pl-1">{{ node.label }}</span>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          @click="saveChecked"
          :disabled="commitLoading"
        >
          保存</el-button
        >
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { toRefs, reactive, watch, ref } from "vue";
import { SimpleResource } from "@/views/system/menumanage/api/MenuManageApi";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElNotification, ElTree } from "element-plus";
import {
  getCheckedResource,
  saveCheckedResource
} from "@/views/system/permmanage/api/PermissionManageApi";
import { ResultStatus } from "@/utils/http/types";

const treeRef = ref<InstanceType<typeof ElTree>>();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  roleId: {
    type: String
  },
  data: {
    type: Array<SimpleResource>
  }
});

//资源树配置属性
const defaultProps = {
  children: "children",
  label: "resourceName"
};

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  resTreeData: [],
  checkedKeys: [],
  resKeyWord: "",
  commitLoading: false
});
const { drawerVisible, resTreeData, checkedKeys, resKeyWord, commitLoading } =
  toRefs(state);

watch(resKeyWord, value => treeRef.value!.filter(value));

//监听组件属性
watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  state.resTreeData = newValue.data;
  await loadCheckedResourceData(newValue.roleId);
});

//关闭并返回父界面
const cancel = () => {
  resetChecked();
  emit("update:visible", false);
};

//清空资源树选项
const resetChecked = () => {
  treeRef.value!.setCheckedKeys([], false);
};

//加载已选择资源数据
const loadCheckedResourceData = async (roleId: string) => {
  await getCheckedResource(roleId).then(result => {
    setCheckedKeys(result.data.resourceIdList);
  });
};

//设置资源树的选项
const setCheckedKeys = (keys: Array<string | number>) => {
  treeRef.value!.setCheckedKeys(keys, false);
};

//获取当前已选择的key
const getCheckedKeys = (): Array<string | number> => {
  return treeRef.value!.getCheckedKeys(false);
};

//保存已选择资源
const saveChecked = async () => {
  const checkedKeys = getCheckedKeys();
  state.commitLoading = true;
  await saveCheckedResource({
    roleId: props.roleId,
    resourceIdList: checkedKeys
  }).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功保存功能权限！",
        type: "success"
      });
      cancel();
    }
  });
  state.commitLoading = false;
};

//菜单搜索
const filterResource = (value: string, data: any) => {
  if (!value) return true;
  return data.resourceName.includes(value);
};
</script>

<style scoped>
.h-screen-gap {
  height: calc(100% - 28px);
}
</style>
