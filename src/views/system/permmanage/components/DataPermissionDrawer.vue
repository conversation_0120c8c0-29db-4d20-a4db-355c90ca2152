<template>
  <el-drawer
    v-model="drawerVisible"
    :before-close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3"> {{ props.roleName }} 数据权限配置 </span>
        </template>
      </el-page-header>
    </template>
    <template #default>
      <el-row :gutter="5" class="h-content">
        <el-col :span="8">
          <div class="pb-1.5">
            <el-input
              placeholder="用户名"
              clearable
              :suffix-icon="useRenderIcon('EP-Search')"
              v-model="userKeyWord"
            >
              <template #prepend>
                <el-button
                  type="primary"
                  :icon="useRenderIcon('RI-AddLine')"
                  @click="openUserSelectDialog"
                >
                  添加
                </el-button>
              </template>
            </el-input>
          </div>
          <div
            class="border border-gray-700 border-opacity-20 rounded-lg h-content overflow-y-auto"
          >
            <el-tree
              ref="userTree"
              :data="userData"
              node-key="userId"
              :props="userTreeProps"
              :expand-on-click-node="false"
              :filter-node-method="filterUser"
              highlight-current
              @current-change="getUserPermData"
            >
              <template #default="{ node }">
                <div class="tree-list-content">
                  <div class="tree-node-left">
                    <IconifyIconOffline icon="EP-Avatar" class="mr-0.5" />
                    {{ node.label }}
                  </div>

                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-DeleteBin6Fill')"
                    @click.stop="deleteUserHandler(node.data)"
                  />
                </div>
              </template>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="16">
          <div class="pb-1.5 text-right">
            <el-button
              type="primary"
              :icon="useRenderIcon('RI-AddLine')"
              @click="addPermDataRow"
              :disabled="userData.length === 0"
            >
              添加
            </el-button>
          </div>
          <el-table
            :data="permData"
            stripe
            style="width: 100%"
            class="h-content"
            :header-cell-style="{ textAlign: 'center' }"
            row-key="relationId"
          >
            <el-table-column prop="roleName" label="角色" width="180" />
            <el-table-column prop="deptName" label="组织" width="180">
              <template #default="{ row }">
                <DeptTreeSelect
                  v-if="row.relationId.startsWith(templateRelationSign)"
                  v-model:dept-id="row.deptId"
                />
                <span v-else>{{ row.deptName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row, $index }">
                <div class="w-full text-center">
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-Save3Line')"
                    v-if="row.relationId.startsWith(templateRelationSign)"
                    @click="savePermData(row)"
                  >
                    保存
                  </el-link>
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('EP-RemoveFilled')"
                    class="ml-2"
                    @click.prevent="removePermDataRow(row, $index)"
                  >
                    移除
                  </el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </template>
  </el-drawer>
</template>

<script lang="tsx" setup>
import { ElMessage, ElTree } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { generateUniqueIdByTime } from "@/utils/IdUtil";
import { addDialog } from "@/components/ReDialog";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import UserSearchSelector from "@/views/system/usermanage/components/UserSearchSelector.vue";
import { SimpleUserInfo } from "@/views/system/usermanage/api/UserManage";
import { ResultStatus } from "@/utils/http/types";
import {
  getCurrentInstance,
  h,
  nextTick,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import {
  addUserToRolePermission,
  DataPermInfo,
  getRolesPermissionUser,
  getRoleUserPermissionData,
  PermissionUserInfo,
  removePermissionData,
  removeUserPermissionFromRole,
  savePermissionData
} from "@/views/system/permmanage/api/DataPermissionApi";

//临时权限关系ID标识
const templateRelationSign = "temp-";
const { $message, $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;
const userSelectRef = ref<InstanceType<typeof UserSearchSelector>>();

//Ref声明
const userTree = ref<InstanceType<typeof ElTree>>();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  roleId: {
    type: String
  },
  roleName: {
    type: String
  }
});

//用户列表属性声明
const userTreeProps = {
  label: "realName",
  children: "children",
  isLeaf: "isLeaf"
};

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  userData: [],
  permData: [],
  userKeyWord: ""
});
const { drawerVisible, userData, permData, userKeyWord } = toRefs(state);

watch(userKeyWord, value => userTree.value!.filter(value));

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  state.userData = [];
  state.permData = [];
  await loadUsersInRole(props.roleId);
});

//加载当前角色下的用户数据
const loadUsersInRole = async (roleId: string) => {
  await getRolesPermissionUser(roleId).then(result => {
    state.userData = result.data;

    //设置用户列表默认选中第一个
    if (state.userData && state.userData.length > 0) {
      const firstUser = state.userData[0];
      nextTick(() => {
        userTree.value!.setCurrentKey(firstUser.userId);
      });
      getUserPermData(firstUser);
    } else {
      userTree.value!.setCurrentKey(null);
    }
  });
};

//查询用户数据权限
const getUserPermData = async (user: PermissionUserInfo) => {
  await getRoleUserPermissionData(props.roleId, user.userId).then(result => {
    const data = result.data;
    //添加展示需要的数据
    if (data != null && data.length > 0) {
      data.forEach(relation => {
        relation.roleName = props.roleName;
      });
    }
    state.permData = data;
  });
};

//添加新数据权限
const addPermDataRow = () => {
  const tempKey = templateRelationSign + generateUniqueIdByTime();
  const selUserNode = userTree.value!.getCurrentNode();
  if (selUserNode != null) {
    state.permData.push({
      relationId: tempKey,
      userId: selUserNode.userId,
      roleId: props.roleId,
      roleName: props.roleName,
      deptId: null,
      change: false
    });
  } else {
    ElMessage({
      showClose: true,
      message: "请选择一个用户！",
      type: "warning"
    });
  }
};

//打开用户选择窗口
const openUserSelectDialog = () => {
  addDialog({
    title: "添加用户",
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: () => h(UserSearchSelector, { ref: userSelectRef }),
    beforeSure: done => {
      const selUsers = userSelectRef.value.getSelectedUsers();
      if (selUsers.length > 0) {
        addUsersToRole(selUsers);
        done();
      } else {
        $message({
          message: "请至少选择一个您要添加的用户！",
          type: "warning"
        });
      }
    }
  });
};

//将用户添加到角色下，组织默认为所属组织
const addUsersToRole = async (users: Array<SimpleUserInfo>) => {
  const userIdList = [];
  users.forEach(u => {
    userIdList.push(u.userId);
  });
  await addUserToRolePermission({
    userIds: userIdList,
    roleId: props.roleId
  }).then(() => {
    $notify({
      title: "提示",
      message: "已成功将用户添加到角色！",
      type: "success"
    });
    loadUsersInRole(props.roleId);
  });
};

//保存权限数据
const savePermData = async (row: DataPermInfo) => {
  if (validateExistPermData(row)) {
    await savePermissionData({
      userId: row.userId,
      roleId: row.roleId,
      deptId: row.deptId
    }).then(result => {
      const data = result.data;
      row.relationId = data.relationId;
      row.deptName = data.deptName;
      $notify({
        title: "提示",
        message: "已成功保存权限数据！",
        type: "success"
      });
    });
  }
};

//前端校验当前权限是否已存在
const validateExistPermData = (row: DataPermInfo) => {
  let valResult = true;
  state.permData.forEach(d => {
    if (
      d.relationId &&
      row.userId === d.userId &&
      row.roleId === d.roleId &&
      row.deptId === d.deptId &&
      !d.relationId.startsWith(templateRelationSign)
    ) {
      valResult = false;
      $notify({
        title: "提示",
        message: "权限已存在！",
        type: "warning"
      });
    }
  });
  return valResult;
};

//移除权限数据
const removePermDataRow = (row, index) => {
  if (row.relationId.startsWith(templateRelationSign)) {
    state.permData.splice(index, 1);
  } else {
    $confirm("确定要移除权限数据吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }).then(() => {
      removePermissionData({
        userId: row.userId,
        roleId: row.roleId,
        deptId: row.deptId
      }).then(() => {
        $notify({
          title: "提示",
          message: "已成功移除权限数据！",
          type: "success"
        });
        state.permData.splice(index, 1);
        if (state.permData.length <= 0) {
          loadUsersInRole(props.roleId);
        }
      });
    });
  }
};

//移除用户
const deleteUserHandler = (data: SimpleUserInfo) => {
  $confirm(
    `确定将用户‘${data.realName}’在‘${props.roleName}’角色内的所有权限都移除吗？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    removeUserPermissionFromRole(data.userId, props.roleId).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功移除用户！",
          type: "success"
        });
        loadUsersInRole(props.roleId);
      }
    });
  });
};

//用户列表搜索
const filterUser = (value: string, data: PermissionUserInfo) => {
  if (!value) return true;
  return data.realName.includes(value);
};

const cancel = () => {
  emit("update:visible", false);
};
</script>

<style scoped>
.h-content {
  height: calc(100% - 15px);
}
</style>
