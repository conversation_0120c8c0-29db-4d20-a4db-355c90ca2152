<template>
  <div class="dark:bg-dark-color">
    <el-page-header @back="jumpTo('roleManage')" class="mb-2">
      <template #content>
        <span class="mr-3"> 角色类型管理 </span>
      </template>
    </el-page-header>
    <avue-crud
      :data="data"
      :option="options"
      @row-save="addRoleType"
      @row-update="updateRoleType"
      @row-del="deleteRoleType"
      @refresh-change="reloadTypeData"
    />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, ref } from "vue";
import {
  addType,
  deleteType,
  getTypeData,
  updateType
} from "@/views/system/permmanage/api/TypeManageApi";
import { ResultStatus } from "@/utils/http/types";
import { ElMessageBox, ElNotification } from "element-plus";
import { validSignId } from "@/utils/validator";

const validateCollectId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：network_config"));
  }
};

const options = ref(null);
options.value = {
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  rowKey: "typeId",
  viewBtn: true,
  column: [
    {
      label: "类型标识",
      prop: "typeId",
      editDisabled: true,
      sortable: true,
      rules: [
        {
          required: true,
          message: "请填写角色类型标识",
          trigger: "blur"
        },
        { validator: validateCollectId, trigger: "blur" }
      ]
    },
    {
      label: "类型名称",
      prop: "typeName",
      sortable: true,
      rules: [
        {
          required: true,
          message: "请填写角色类型名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "级别",
      prop: "level",
      type: "number",
      sortable: true,
      value: 2,
      min: 2,
      max: 999
    },
    {
      label: "创建时间",
      prop: "createTime",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    },
    {
      label: "更新时间",
      prop: "updateTime",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    },
    {
      label: "操作人",
      prop: "updateUid",
      editDisabled: true,
      editDisplay: false,
      sortable: true,
      addDisabled: true,
      addDisplay: false
    }
  ]
};

//数据对象
const state = reactive({
  data: []
});

const { data } = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//加载类型数据
const loadTypeData = async () => {
  await getTypeData().then(result => {
    state.data = result.data;
  });
};
loadTypeData();

//刷新类型数据
const reloadTypeData = async () => {
  state.data = [];
  await loadTypeData();
};

//新增类型
const addRoleType = async (row, done) => {
  addType(row).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功新增角色类型！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//更新类型
const updateRoleType = async (row, index, done) => {
  await updateType(row).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功更新角色类型！",
        type: "success"
      });
      done(result.data);
    }
  });
};

//删除类型
const deleteRoleType = async (row, index, done) => {
  await ElMessageBox.confirm(`确定要删除 '${row.typeName}' 么？`, `提示`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  await deleteType({ typeId: row.typeId }).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: `已成功删除类型：${row.typeName}`,
        type: "success"
      });
      done(row);
    }
  });
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
