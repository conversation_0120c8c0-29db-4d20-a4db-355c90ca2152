<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <component
        :is="currentComponent"
        :selected-role="selectedRole"
        @jump-to="comChange"
        @role-select_change="roleSelectChangeHandler"
      />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, shallowRef, toRefs } from "vue";
import RoleManage from "@/views/system/permmanage/components/RoleManage.vue";
import RoleGroupManage from "@/views/system/permmanage/components/RoleGroupManage.vue";
import RoleTypeManage from "@/views/system/permmanage/components/RoleTypeManage.vue";
import BatchPermission from "@/views/system/permmanage/components/BatchPermission.vue";
import { RoleInfo } from "@/views/system/permmanage/api/RoleManageApi";

defineOptions({
  name: "SystemConfig_PermManage"
});

const roleManage = shallowRef(RoleManage);
const roleGroupManage = shallowRef(RoleGroupManage);
const roleTypeManage = shallowRef(RoleTypeManage);
const batchPermission = shallowRef(BatchPermission);

//数据对象
const state = reactive({
  currentComponent: null,
  selectedRole: null as RoleInfo
});

const { currentComponent, selectedRole } = toRefs(state);

state.currentComponent = roleManage;

const comChange = (val: string) => {
  if (val == "roleManage") {
    state.currentComponent = roleManage;
  }

  if (val == "roleTypeManage") {
    state.currentComponent = roleTypeManage;
  }

  if (val == "roleGroupManage") {
    state.currentComponent = roleGroupManage;
  }

  if (val == "batchPermission") {
    state.currentComponent = batchPermission;
  }
};

const roleSelectChangeHandler = (role: RoleInfo) => {
  state.selectedRole = role;
};
</script>
