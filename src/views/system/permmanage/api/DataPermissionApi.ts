import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage/eam/permission`;

// 数据权限信息
export type DataPermInfo = {
  relationId: string;
  userId: string;
  realName: string;
  roleId: string;
  roleName: string;
  deptId: string;
  deptName: string;
};

//角色下的用户信息
export type PermissionUserInfo = {
  userId: string;
  userName: string;
  realName: string;
  deptId: string;
};

//简单角色信息
export type SimpleRoleInfo = {
  roleId: string;
  roleName: string;
};

//查询某个角色下的用户信息
const getRolesPermissionUser = (roleId: string) => {
  return http.get<any, RestResult<Array<PermissionUserInfo>>>(
    `${basePath}/getRolesPermissionUser?roleId=${roleId}`
  );
};

//查询指定角色、用户的权限数据
const getRoleUserPermissionData = (roleId: string, userId: string) => {
  return http.get<any, RestResult<Array<DataPermInfo>>>(
    `${basePath}/getRoleUserPermissionData?roleId=${roleId}&userId=${userId}`
  );
};

//添加用户到角色
const addUserToRolePermission = (params: any) => {
  return http.postJson<RestResult<String>>(
    `${basePath}/addUserToRolePermission`,
    params
  );
};

//保存单条权限数据
const savePermissionData = (params: any) => {
  return http.postJson<RestResult<DataPermInfo>>(
    `${basePath}/addSingleUserPermission`,
    params
  );
};

//移除权限数据
const removePermissionData = (params: any) => {
  return http.postJson<RestResult<String>>(
    `${basePath}/unBindUserFromRole`,
    params
  );
};

//将用户权限从角色中移除
const removeUserPermissionFromRole = (userId: string, roleId: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/removeUserPermissionFromRole?userId=${userId}&roleId=${roleId}`
  );

//查询某个用户的权限数据列表
const getUserPermissionData = (userId: string) =>
  http.get<string, RestResult<Array<DataPermInfo>>>(
    `${basePath}/getUserPermissionData?userId=${userId}`
  );

//查询当前用户具有权限的待选择角色数据-只包含Id和Name，通常用于下拉列表
const getCurrentUserRoleInfos = () =>
  http.get<any, RestResult<Array<SimpleRoleInfo>>>(
    `${basePath}/getCurrentUserRoleInfos`
  );

export {
  getRolesPermissionUser,
  getRoleUserPermissionData,
  addUserToRolePermission,
  savePermissionData,
  removePermissionData,
  removeUserPermissionFromRole,
  getUserPermissionData,
  getCurrentUserRoleInfos
};
