import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";
import { SelectOption } from "@/utils/CommonTypes";

export type RoleGroupInfo = {
  groupId: string;
  groupName: string;
  createTime: string;
  updateTime: string;
  updateUid: string;
  sortIndex: number;
};

//查询类型数据
const getGroupData = () => {
  return http.get<any, RestResult<Array<RoleGroupInfo>>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/role/group/getAllGroupData`
  );
};

//查询角色分组下拉框数据
const getGroupSelectData = () => {
  return http.get<any, RestResult<Array<SelectOption>>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/role/group/getAllGroupSelectOptions`
  );
};

//新增分组
const addGroup = (group: RoleGroupInfo) => {
  return http.postJson<RestResult<RoleGroupInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/role/group/addRoleGroup`,
    group
  );
};

//更新分组
const updateGroup = (group: RoleGroupInfo) => {
  return http.postJson<RestResult<RoleGroupInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/role/group/updateRoleGroup`,
    group
  );
};

//删除分组
const deleteGroup = (params: Object) => {
  return http.get<any, RestResult<string>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/role/group/deleteRoleGroup`,
    { params }
  );
};

export { getGroupData, getGroupSelectData, addGroup, updateGroup, deleteGroup };
