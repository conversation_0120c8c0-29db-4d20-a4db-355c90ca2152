import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

export interface BaseRoleTypeInfo {
  typeId: string;
  typeName: string;
}

export interface RoleTypeInfo extends BaseRoleTypeInfo {
  level: number;
  createTime: string;
  updateTime: string;
  updateUid: string;
  sortIndex: number;
}

//查询类型数据
export const getTypeData = () => {
  return http.get<any, RestResult<Array<RoleTypeInfo>>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/getAllTypeData`
  );
};

//查询类型选择数据
export const getTypeSelectData = () => {
  return http.get<any, RestResult<Array<BaseRoleTypeInfo>>>(
    `${ServerNames.portalServer}/framework/sysmanage/eam/permission/getCurrentUserRoleTypeInfos`
  );
};

//新增类型
export const addType = (type: RoleTypeInfo) => {
  return http.postJson<RestResult<RoleTypeInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/addRoleType`,
    type
  );
};

//更新类型
export const updateType = (type: RoleTypeInfo) => {
  return http.postJson<RestResult<RoleTypeInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/updateRoleType`,
    type
  );
};

//删除类型
export const deleteType = (params: Object) => {
  return http.get<any, RestResult<string>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/deleteRoleType`,
    { params }
  );
};
