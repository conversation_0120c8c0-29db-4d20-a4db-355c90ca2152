import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage`;

//某个角色的已选择资源信息
export type RoleResourceCheckedInfo = {
  roleId: string;
  resourceIdList: Array<string | number>;
};

//权限数据信息
export type UserPermissionInfo = {
  relationId: string;
  roleId: string;
  roleName: string;
  userId: string;
  userName: string;
  realName: string;
  deptId: string;
  deptPath: string;
  deptPathName: string;
  createTime: string;
};

//查询某个角色的已选择资源信息
const getCheckedResource = (roleId: string) => {
  return http.get<string, RestResult<RoleResourceCheckedInfo>>(
    `${basePath}/roles/resource/${roleId}`
  );
};

//保存已选择的操作权限
const saveCheckedResource = (checkedRes: RoleResourceCheckedInfo) => {
  return http.postJson<RestResult<string>>(
    `${basePath}/roles/resource`,
    checkedRes
  );
};

//查询权限数据 - 分页
const getRolePermissionData = (params: any) =>
  http.postJson<RestPageResult<Array<UserPermissionInfo>>>(
    `${basePath}/eam/permission/getRolePermissionData`,
    params
  );

//添加用户到角色
const addUserToRolePermission = (params: any) =>
  http.postJson<RestResult<string>>(
    `${basePath}/eam/permission/addUserToRolePermission`,
    params
  );

//根据权限数据ID移除权限数据
const removePermRelations = (params: any) =>
  http.postJson<RestResult<string>>(
    `${basePath}/eam/permission/removePermRelations`,
    params
  );

export {
  getCheckedResource,
  saveCheckedResource,
  getRolePermissionData,
  addUserToRolePermission,
  removePermRelations
};
