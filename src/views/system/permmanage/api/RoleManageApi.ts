import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

export type RoleInfo = {
  roleId: string;
  roleName: string;
  roleInfo: string;
  roleType: string;
  roleTypeName: string;
  roleTypeLevel: number;
  groupId: string;
  groupName: string;
  editUser: string;
  editTime: string;
  loadTime: string;
};

//查询角色数据
const getRoleData = () => {
  return http.get<any, RestResult<Array<RoleInfo>>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles`
  );
};

//新增角色
const addRole = (role: RoleInfo) => {
  return http.postJson<RestResult<RoleInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles`,
    role
  );
};

//更新角色
const updateRole = (role: RoleInfo) => {
  return http.postJson<RestResult<RoleInfo>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/update`,
    role
  );
};

//删除角色
const deleteRole = (params: any) => {
  return http.get<any, RestResult<string>>(
    `${ServerNames.portalServer}/framework/sysmanage/roles/deleteRole`,
    { params }
  );
};

export { getRoleData, addRole, updateRole, deleteRole };
