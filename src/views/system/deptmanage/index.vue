<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <component :is="currentComponent" @jump-to="comChange" />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, shallowRef, toRefs } from "vue";
import DeptInfoManage from "@/views/system/deptmanage/components/DeptInfoManage.vue";
import DeptTypeManage from "@/views/system/deptmanage/components/DeptTypeManage.vue";

defineOptions({
  name: "SystemConfig_DepartmentManage"
});

const deptInfoManage = shallowRef(DeptInfoManage);
const deptTypeManage = shallowRef(DeptTypeManage);

//数据对象
const state = reactive({
  currentComponent: null
});
const { currentComponent } = toRefs(state);

state.currentComponent = deptInfoManage;

const comChange = (val: string) => {
  if (val === "deptInfoManage") {
    state.currentComponent = deptInfoManage;
  }

  if (val === "deptTypeManage") {
    state.currentComponent = deptTypeManage;
  }
};
</script>
