<template>
  <div>
    <avue-crud
      ref="deptCrud"
      v-model="deptForm"
      :data="data"
      :option="option"
      :before-open="beforeFormOpenHandler"
      @refresh-change="loadRootNodes"
      @tree-load="treeLoadHandler"
      @row-save="addRootDept"
      @row-update="updateDept"
      @row-del="deleteDeptHandler"
    >
      <template #deptName="{ row }">
        <span
          v-html="highLightText(row.deptName, keyWord)"
          class="text-primary font-bold"
        />
        <el-button
          v-if="row.hasChildren"
          type="primary"
          text
          :icon="useRenderIcon('RI-ArrowUpDownLine')"
          @click="sortDeptHandler(row)"
        ></el-button>
      </template>
      <template #deptPathName="{ row }">
        <span v-html="highLightText(row.deptPathName, keyWord)" />
      </template>
      <template #menu-left="{ size }">
        <div class="flex items-center float-right space-x-3 ml-3 pt-0.5">
          <el-button
            type="primary"
            plain
            :size="size"
            :icon="
              isExpand
                ? useRenderIcon('EP-CaretBottom')
                : useRenderIcon('EP-CaretRight')
            "
            @click="handleExpand"
          >
            {{ isExpand ? "折叠" : "展开" }}
          </el-button>
          <el-tooltip
            class="item"
            effect="light"
            content="提示：折叠/展开功能 仅对已加载的数据有效！"
            placement="right"
            :open-delay="500"
          >
            <iconify-icon-offline icon="EP-InfoFilled" />
          </el-tooltip>
        </div>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left">
          <el-input
            clearable
            placeholder="组织名称"
            v-model="keyWord"
            :size="size"
            :suffix-icon="useRenderIcon('EP-Search')"
            @blur="keyWord = ($event.target as HTMLInputElement).value.trim()"
          />
        </div>
        <el-divider direction="vertical" border-style="dashed" />
        <el-button
          color="#106ebe"
          :icon="useRenderIcon('RI-StackFill')"
          :size="size"
          @click="jumpTo('deptTypeManage')"
          v-auth="'system:dept:type:manage'"
        >
          类型管理
        </el-button>
        <el-tooltip content="排序首层组织" placement="top" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('RI-ArrowUpDownLine')"
            circle
            :size="size"
            @click="
              sortDeptHandler({
                deptId: '-1',
                deptName: '首层组织'
              } as DeptManageInfo)
            "
          />
        </el-tooltip>
        <el-tooltip
          content="导出全部组织数据"
          placement="top"
          :open-delay="1000"
        >
          <el-button
            :icon="useRenderIcon('EP-Download')"
            circle
            :size="size"
            :disabled="data.length < 1"
            @click="exportDataHandler"
            v-auth="AdminEnum.SuperAdmin"
          />
        </el-tooltip>
        <el-tooltip placement="top" content="同步组织数据" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('RI-ArrowLeftRightFill')"
            circle
            :size="size"
            @click="asyncDeptData"
            v-auth="[
              'system:dept:async:data',
              AdminEnum.SuperAdmin,
              AdminEnum.SystemAdmin
            ]"
          />
        </el-tooltip>
        <el-tooltip placement="top" content="重算路径" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('RI-GuideLine')"
            circle
            :size="size"
            @click="rebuildDeptPathHandler"
            v-auth="[
              'system:dept:rebuild:path',
              AdminEnum.SuperAdmin,
              AdminEnum.SystemAdmin
            ]"
          />
        </el-tooltip>
      </template>
      <template #menu="{ row, size, type }">
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('RI-AddBoxFill')"
          @click="addSubDept(row)"
          v-auth="[
            'system:dept:add',
            AdminEnum.SuperAdmin,
            AdminEnum.SystemAdmin
          ]"
        >
          新增
        </el-button>
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('EP-Promotion')"
          @click="moveDeptHandler(row)"
          v-auth="[
            'system:dept:move',
            AdminEnum.SuperAdmin,
            AdminEnum.SystemAdmin
          ]"
        >
          移动
        </el-button>
      </template>
    </avue-crud>

    <!-- 移动组织会话框 -->
    <move-dept-node
      v-model:visible="moveNodeVisible"
      :dept="selectedDept"
      @move-success="moveSuccessHandler"
    />

    <!-- 组织排序抽屉 -->
    <dept-sort-drawer
      v-model:visible="sortDrawerVisible"
      :parent-dept="selectedDept"
      @success="sortSuccessHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, reactive, ref, toRefs } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElNotification } from "element-plus";
import { highLightText } from "@/utils/view";
import { message } from "@/utils/message";
import { ResultStatus } from "@/utils/http/types";
import MoveDeptNode from "@/views/system/deptmanage/components/MoveDeptNode.vue";
import DeptSortDrawer from "@/views/system/deptmanage/components/DeptSortDrawer.vue";
import { IconifyIconOffline } from "@/components/ReIcon";
import {
  DeptTypeInfo,
  getAllDeptTypeData
} from "@/views/system/deptmanage/api/DeptTypeManageApi";
import {
  asyncWholeDeptData,
  deleteDept,
  DeptManageInfo,
  exportAllDeptData,
  getNodesByParentId,
  rebuildDeptPath,
  save
} from "@/views/system/deptmanage/api/DeptManageApi";
import { AdminEnum } from "@/utils/CommonTypes";
import { hasAuth } from "@/router/utils";

const { $loading, $Log, $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

const rootParentId = "-1";
const deptCrud = ref();
const typeData = ref([]);

//数据对象
const state = reactive({
  moveNodeVisible: false,
  sortDrawerVisible: false,
  selectedDept: null as DeptManageInfo,
  data: [],
  deptForm: {} as DeptManageInfo,
  keyWord: "",
  isExpand: false
});

const {
  moveNodeVisible,
  sortDrawerVisible,
  selectedDept,
  data,
  deptForm,
  keyWord,
  isExpand
} = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//Crud配置项
const option = reactive({
  index: true,
  border: true,
  rowKey: "deptId",
  rowParentKey: "parentId",
  lazy: true,
  menuWidth: 235,
  labelWidth: 120,
  addBtn: hasAuth([
    "system:dept:add",
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin
  ]),
  editBtn: hasAuth([
    "system:dept:edit",
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin
  ]),
  delBtn: hasAuth([
    AdminEnum.SuperAdmin,
    AdminEnum.SystemAdmin,
    "system:dept:delete"
  ]),
  gridBtn: false,
  column: [
    {
      label: "组织名称",
      prop: "deptName",
      maxlength: 35,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请填写组织名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "主管单位",
      prop: "competentDeptName",
      hide: true,
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "组织类型",
      prop: "deptTypeName",
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "组织类型",
      prop: "deptType",
      showColumn: false,
      hide: true,
      type: "select",
      dicData: typeData
    },
    {
      label: "全路径信息",
      prop: "deptPathName",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "统一社会信用代码",
      prop: "deptUscc",
      maxlength: 60,
      showWordLimit: true,
      showColumn: false,
      hide: true
    },
    {
      label: "排序标识",
      prop: "sortIndex",
      type: "number",
      min: 0,
      max: 999,
      width: 70
    }
  ]
});

//加载组织跟节点数据
const loadRootNodes = async () => {
  state.data = [];
  getNodesByParentId(rootParentId).then(result => {
    state.data = result.data;
  });
};
loadRootNodes();

//加载组织类型数据
getAllDeptTypeData().then(result => {
  const sourceData: Array<DeptTypeInfo> = result.data;
  sourceData.forEach(d =>
    typeData.value.push({
      label: d.deptTypeName,
      value: d.deptType
    })
  );
});

//加载子节点
const treeLoadHandler = async (tree, treeNode, resolve) => {
  await getNodesByParentId(tree.deptId).then(result => {
    resolve(result.data);
  });
};

//新增组织触发
const beforeFormOpenHandler = (done, type) => {
  if (type === "add") {
    state.deptForm.sortIndex = 0;
  }
  done();
};

//添加根组织信息
const addRootDept = async (row, done) => {
  if (!row.parentId) {
    row.parentId = rootParentId;
  }

  await save(row).then(result => {
    ElNotification({
      title: "提示",
      message: "已成功添加组织信息！",
      type: "success"
    });
    done(result.data);
  });
};

//新增子组织
const addSubDept = async row => {
  deptCrud.value.rowAdd();
  state.deptForm.parentId = row.deptId;
};

//更新组织信息
const updateDept = async (row, index, done) => {
  await save(row).then(result => {
    ElNotification({
      title: "提示",
      message: "已成功保存组织信息！",
      type: "success"
    });
    done(result.data);
  });
};

//导出组织数据
const exportDataHandler = async () => {
  message("数据正在导出中...", { type: "success" });
  await exportAllDeptData();
};

//手动同步部门数据
const asyncDeptData = () => {
  $confirm(`同步组织数据可能耗费较长的时间，您确认要进行此操作么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    const loading = $loading({
      lock: true,
      text: "正努力同步部门数据中......",
      background: "rgba(0, 0, 0, 0.7)"
    });
    await asyncWholeDeptData()
      .then(res => {
        loading.close();
        if (ResultStatus.Success === res.status) {
          $notify({
            title: "提示",
            message: "已成功同步所有部门数据！",
            type: "success"
          });
        }
      })
      .catch(error => {
        loading.close();
        $Log.danger(error);
      });
    await loadRootNodes();
  });
};

//重新计算路径信息
const rebuildDeptPathHandler = () => {
  $confirm(`重算路径可能耗费较长的时间，您确认要进行此操作么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    const loading = $loading({
      lock: true,
      text: "正在卖力计算 组织路径信息 中......",
      background: "rgba(0, 0, 0, 0.7)"
    });

    await rebuildDeptPath()
      .then(res => {
        loading.close();
        if (ResultStatus.Success === res.status) {
          $notify({
            title: "提示",
            message: "已成功计算所有组织的路径信息！",
            type: "success"
          });
        }
      })
      .catch(error => {
        loading.close();
        $Log.danger(error);
      });
    // await loadRootNodes();
  });
};

//移动组织节点
const moveDeptHandler = (dept: DeptManageInfo) => {
  state.selectedDept = dept;
  state.moveNodeVisible = true;
};

//移动成功触发
const moveSuccessHandler = () => {
  loadRootNodes();
};

// 是否展开table(展开与折叠切换)
const handleExpand = () => {
  state.isExpand = !state.isExpand;
  nextTick(() => {
    forArr(state.data, state.isExpand);
  });
};

// 递归组织树
const forArr = (arr: Array<any>, isExpand: boolean) => {
  arr.forEach(i => {
    deptCrud.value.toggleRowExpansion(i, isExpand);
    if (i.children) {
      forArr(i.children, isExpand);
    }
  });
};

//删除组织信息触发
const deleteDeptHandler = (dept: DeptManageInfo) => {
  $confirm("您确定要删除 '" + dept.deptName + "' 吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteDept(dept.deptId).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除组织信息！",
          type: "success"
        });
        loadRootNodes();
      }
    });
  });
};

//组织排序触发
const sortDeptHandler = (d: DeptManageInfo) => {
  state.selectedDept = d;
  state.sortDrawerVisible = true;
};

//组织排序成功触发
const sortSuccessHandler = (data: Array<DeptManageInfo>) => {
  if (state.selectedDept.deptId === "-1") {
    loadRootNodes();
  } else {
    deptCrud.value.updateKeyChildren(state.selectedDept.deptId, data);
  }
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
