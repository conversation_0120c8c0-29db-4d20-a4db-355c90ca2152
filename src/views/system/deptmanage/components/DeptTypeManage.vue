<template>
  <div class="dark:bg-dark-color">
    <el-page-header @back="jumpTo('deptInfoManage')" class="mb-2">
      <template #content>
        <span class="mr-3"> 组织类型管理 </span>
      </template>
    </el-page-header>
    <avue-crud
      :data="typeData"
      :option="crudOption"
      @refresh-change="loadDeptTypeData"
      @row-save="addDeptTypeHandler"
      @row-update="updateDeptTypeHandler"
      @row-del="deleteDeptTypeHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, getCurrentInstance } from "vue";
import { validSignId } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";
import {
  DeptTypeInfo,
  getDepartmentTypeForManage,
  addDeptType,
  updateDeptType,
  deleteDeptType
} from "@/views/system/deptmanage/api/DeptTypeManageApi";

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

const validateTypeId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：unit_dept"));
  }
};

//类型管理crud配置项
const crudOption = reactive({
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  labelWidth: 130,
  searchLabelWidth: 120,
  menuWidth: 300,
  viewBtn: false,
  column: [
    {
      label: "类型标识",
      prop: "deptType",
      editDisabled: true,
      rules: [
        {
          required: true,
          message: "请填写组织类型标识",
          trigger: "blur"
        },
        { validator: validateTypeId, trigger: "blur" }
      ],
      maxlength: 28,
      showWordLimit: true
    },
    {
      label: "类型名称",
      prop: "deptTypeName",
      rules: [
        {
          required: true,
          message: "请填写角色类型名称",
          trigger: "blur"
        }
      ],
      maxlength: 60,
      showWordLimit: true
    },
    {
      label: "排序编号",
      prop: "sortIndex",
      type: "number",
      sortable: true,
      value: 0,
      minRows: 0,
      maxRows: 999
    },
    {
      label: "创建时间",
      prop: "loadTime",
      editDisabled: true,
      editDisplay: false,
      addDisabled: true,
      addDisplay: false
    }
  ]
});

//数据对象
const state = reactive({
  typeData: [] as Array<DeptTypeInfo>
});

const { typeData } = toRefs(state);

//定义事件
const emit = defineEmits(["jump-to"]);

//加载组织类型数据
const loadDeptTypeData = () => {
  state.typeData = [];
  getDepartmentTypeForManage().then(result => {
    if (result.status == ResultStatus.Success) {
      state.typeData = result.data;
    }
  });
};
loadDeptTypeData();

//添加组织类型
const addDeptTypeHandler = (form, done) => {
  addDeptType(form).then(result => {
    if (result.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功添加组织类型！",
        type: "success"
      });
      loadDeptTypeData();
    }
  });
  done();
};

//更新组织类型
const updateDeptTypeHandler = (form, index, done) => {
  updateDeptType(form).then(result => {
    if (result.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功更新组织类型信息！",
        type: "success"
      });
      loadDeptTypeData();
    }
  });
  done();
};

//删除组织类型
const deleteDeptTypeHandler = (form: DeptTypeInfo) => {
  $confirm("您确定要删除 '" + form.deptTypeName + "' 吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteDeptType(form.deptType).then(result => {
      if (result.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除组织类型！",
          type: "success"
        });
        loadDeptTypeData();
      }
    });
  });
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
