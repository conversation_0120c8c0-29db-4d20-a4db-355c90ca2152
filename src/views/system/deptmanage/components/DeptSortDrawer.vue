<template>
  <el-drawer
    v-model="drawerVisible"
    size="450"
    :before-close="cancel"
    :table-loading="loading"
    :close-on-click-modal="false"
  >
    <template #header>
      <el-page-header @back="cancel">
        <template #content>
          <span class="mr-3">
            {{ `组织排序 - ${parentDept?.deptName}` }}
          </span>
        </template>
      </el-page-header>
    </template>
    <el-alert
      title="请拖拽下方的组织数据行，以完成排序！"
      type="warning"
      show-icon
      :closable="false"
      class="mb-2"
    />
    <avue-crud
      ref="tableRef"
      :data="data"
      :option="tableOption"
      @sortable-change="sortChangeHandler"
    />
    <template #footer>
      <div style="flex: auto">
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          :disabled="loading || !sortChanged"
          @click="saveChangeHandler"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch } from "vue";
import {
  DeptManageInfo,
  getNodesByParentId,
  updateSortIndex
} from "@/views/system/deptmanage/api/DeptManageApi";
import { moveArrayItemInPlace } from "@/utils/array";
import { ResultStatus } from "@/utils/http/types";

const tableRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  parentDept: {
    type: Object,
    default: () => {
      return {} as DeptManageInfo;
    }
  }
});
//定义事件
const emit = defineEmits(["update:visible", "success"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  sortChanged: false,
  loading: false,
  data: []
});
const { drawerVisible, sortChanged, loading, data } = toRefs(state);

watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  state.sortChanged = false;
  await loadDeptData();
});

const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: false,
  sortable: true,
  stripe: true,
  menu: false,
  showHeader: false,
  header: false,
  rowKey: "resourceId",
  column: [
    {
      label: "名称",
      prop: "deptName"
    }
  ]
});

//加载组织数据
const loadDeptData = async () => {
  state.loading = true;
  const { data } = await getNodesByParentId(props.parentDept.deptId);
  state.data = data;
  state.loading = false;
};

//排序改变触发
const sortChangeHandler = (oldIndex: number, newIndex: number) => {
  state.data = moveArrayItemInPlace(state.data, oldIndex, newIndex);
  state.sortChanged = true;
  tableRef.value.refreshTable();
};

//保存排序触发
const saveChangeHandler = async () => {
  state.data.forEach((item, index) => (item.sortIndex = index));
  const ids = [];
  state.data.forEach(item => ids.push(item.deptId));
  const { status } = await updateSortIndex(ids);
  if (status == ResultStatus.Success) {
    emit("success", state.data);
    cancel();
  }
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
