<template>
  <el-dialog
    :title="'移动组织 - ' + currentDept.deptName"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="35%"
  >
    <div class="space-x-2">
      <span>当前上级组织：</span>
      <span class="font-bold">{{ parentDeptName }}</span>
    </div>
    <div class="flex space-x-2 mt-2">
      <span class="w-[123px]">目标上级组织：</span>
      <div
        class="border border-gray-400 border-opacity-20 rounded-lg h-content w-full h-96 overflow-y-auto ml-1 p-1 dark:bg-dark-color"
      >
        <el-tree
          ref="deptTreeRef"
          :data="deptData"
          node-key="deptId"
          :expand-on-click-node="false"
          highlight-current
          :props="{ label: 'deptName', isLeaf: 'hasChildren' }"
          :default-expanded-keys="['-1']"
          lazy
          :load="loadNode"
          @current-change="treeSelectHandler"
        />
      </div>
    </div>
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-Promotion')"
        :disabled="
          currentDept.parentId == selectedDept.deptId ||
          selectedDept.deptPath?.indexOf(currentDept.deptId) > -1
        "
        @click="moveDeptNode"
      >
        移 动
      </el-button>
      <el-button @click="cancel" :icon="useRenderIcon('EP-CircleClose')">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { toRefs, reactive, getCurrentInstance, ref, watch } from "vue";
import { ElTree } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import Node from "element-plus/es/components/tree/src/model/node";
import { ResultStatus } from "@/utils/http/types";
import {
  DeptManageInfo,
  getNodesByParentId,
  getNodeById,
  save
} from "@/views/system/deptmanage/api/DeptManageApi";

const { $confirm, $notify } =
  getCurrentInstance().appContext.config.globalProperties;
const deptTreeRef = ref<InstanceType<typeof ElTree>>();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  dept: {
    type: Object
  }
});

//定义事件
const emit = defineEmits(["update:visible", "move-success"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  parentDeptName: "",
  deptData: [],
  currentDept: {} as DeptManageInfo,
  selectedDept: {} as DeptManageInfo
});
const { dialogVisible, parentDeptName, deptData, currentDept, selectedDept } =
  toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }

  state.currentDept = newValue.dept;
  loadRootDeptNodes();
  loadParentDeptName(state.currentDept.parentId);
});

const rootNode = {
  deptId: "-1",
  deptName: "顶级组织",
  children: [],
  hasChildren: false
};

//加载首层组织数据
const loadRootDeptNodes = () => {
  state.deptData = [];
  state.deptData = [rootNode];
};

//加载树节点
const loadNode = (node: Node, resolve: (data: DeptManageInfo[]) => void) => {
  if (node.data.deptId != null) {
    getNodesByParentId(node.data.deptId).then(res => {
      res.data.forEach(d => (d.hasChildren = !d.hasChildren));
      resolve(res.data);
    });
  } else {
    state.deptData = [rootNode];
  }
};

//加载原父组织名称
const loadParentDeptName = (parentId: string) => {
  if (parentId === "-1") {
    state.parentDeptName = "顶级组织";
  } else {
    getNodeById(parentId).then(res => {
      if (res.data != null) {
        state.parentDeptName = res.data.deptName;
      }
    });
  }
};

//选择树节点触发
const treeSelectHandler = (node: DeptManageInfo) => {
  state.selectedDept = node;
};

//移动菜单树节点
const moveDeptNode = () => {
  $confirm(
    `您确认要将 '${state.currentDept.deptName}' 移动到 '${state.selectedDept.deptName}' 下么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  ).then(() => {
    state.currentDept.parentId = state.selectedDept.deptId;
    const successMsg = `已成功将组织移动到 ${state.selectedDept.deptName}！`;
    save(state.currentDept).then(res => {
      if (ResultStatus.Success === res.status) {
        $notify({
          title: "提示",
          message: successMsg,
          type: "success"
        });
        emit("move-success");
        cancel();
      }
    });
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
