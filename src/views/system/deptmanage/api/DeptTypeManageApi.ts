import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage/depts`;

//组织类型信息
export type DeptTypeInfo = {
  deptType: string;
  deptTypeName: string;
  sortIndex: number;
  loadTime: string;
};

//组织类型管理-查询全部类型-此方法为直接从数据库查询，不会查缓存数据，仅限于数据管理使用
const getDepartmentTypeForManage = () =>
  http.get<string, RestResult<Array<DeptTypeInfo>>>(
    `${basePath}/getDepartmentTypeForManage`
  );

//查询组织类型数据
const getAllDeptTypeData = () => {
  return http.get<any, RestResult<Array<DeptTypeInfo>>>(
    `${basePath}/getDeptTypeData`
  );
};

//添加组织类型
const addDeptType = (params: any) =>
  http.postJson<RestResult<string>>(`${basePath}/addDeptType`, params);

//更新组织类型
const updateDeptType = (params: any) =>
  http.postJson<RestResult<string>>(basePath + "/updateDeptType", params);

//删除组织类型
const deleteDeptType = (deptType: string) =>
  http.get<string, RestResult<string>>(
    basePath + "/deleteDeptType?deptType=" + deptType
  );

export {
  getDepartmentTypeForManage,
  getAllDeptTypeData,
  addDeptType,
  updateDeptType,
  deleteDeptType
};
