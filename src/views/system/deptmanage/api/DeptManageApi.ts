import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage`;

//简单组织信息
export type SimpleDeptInfo = {
  deptId: string;
  parentId: string;
  deptName: string;
  deptType: string;
  deptPath: string;
  deptPathName: string;
  leaf: boolean;
};

//组织信息 - 用于组织管理
export type DeptManageInfo = {
  deptId: string;
  parentId: string;
  deptName: string;
  deptType: string;
  deptTypeName: string;
  deptPath: string;
  deptPathName: string;
  competentDeptId: string;
  competentDeptName: string;
  sortIndex: number;
  hasChildren: boolean;
};

//查询待缓存的组织数据
const getNeedCacheData = () => {
  return http.get<any, RestResult<Array<SimpleDeptInfo>>>(
    `${basePath}/dept/getAllSimpleDeptData`
  );
};

//根据父Id查询组织子节点信息
const getNodesByParentId = (parentId: string) => {
  return http.get<any, RestResult<Array<DeptManageInfo>>>(
    `${basePath}/deptmanage/findByParentId?parentId=${parentId}`
  );
};

//根据Id查询组织信息-不会查询缓存，大量调用禁止使用
const getNodeById = (deptId: string) =>
  http.get<string, RestResult<DeptManageInfo>>(
    `${basePath}/deptmanage/findById?deptId=${deptId}`
  );

//保存组织信息
const save = (dept: DeptManageInfo) => {
  return http.postJson<RestResult<DeptManageInfo>>(
    `${basePath}/deptmanage/save`,
    dept
  );
};

//删除组织信息
const deleteDept = (deptId: string) =>
  http.get<any, RestResult<string>>(
    `${basePath}/deptmanage/deleteDept?deptId=${deptId}`
  );

//导出全部组织数据
const exportAllDeptData = () => http.postBlob(`${basePath}/dept/export`);

//一键重新计算部门路径
const rebuildDeptPath = () =>
  http.get<any, RestResult<string>>(`${basePath}/dept/rebuildAllDeptPath`);

//全量同步部门数据 asyncWholeDeptData
const asyncWholeDeptData = () =>
  http.get<any, RestResult<string>>(`${basePath}/dept/asyncWholeDeptData`);

//更新组织顺序
const updateSortIndex = (params: any) =>
  http.postJson<RestResult<string>>(
    `${basePath}/deptmanage/updateSortIndex`,
    params
  );

export {
  getNeedCacheData,
  getNodesByParentId,
  getNodeById,
  save,
  deleteDept,
  exportAllDeptData,
  rebuildDeptPath,
  asyncWholeDeptData,
  updateSortIndex
};
