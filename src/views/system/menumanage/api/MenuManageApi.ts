import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/framework/sysmanage/resources`;

//基础资源信息
export interface BaseResource {
  resourceId: string;
  parentId: string;
  resourceCode: string;
  resourceName: string;
  resourceIcon: string;
  resourceUrl: string;
  resourceType: string;
  isHidden: boolean;
  hidden: boolean;
  openTarget: string;
  sortIndex: number;
  resourceInfo: string;
}

//资源数据信息 - 管理时使用
export interface ManageResource extends BaseResource {
  permCode: string;
  children?: Array<ManageResource>;
  hasChildren: boolean;
}

//资源树信息 - 用于资源选择
export type SimpleResource = {
  resourceId: string;
  parentId: string;
  resourceName: string;
  resourceIcon: string;
  permCode: string;
  children?: Array<SimpleResource>;
};

//查询菜单数据
const getMenuData = (params?: Object) => {
  return http.post<any, RestResult<Array<ManageResource>>>(
    `${basePath}/find-by-parent-id`,
    {
      params
    }
  );
};

//根据菜单Id查询菜单数据
const getMenuById = (id: string) =>
  http.get<string, RestResult<ManageResource>>(`${basePath}/${id}`);

//添加菜单
const addMenu = (params?: ManageResource) => {
  return http.postJson<RestResult<ManageResource>>(`${basePath}`, params);
};

//更新菜单
const updateMenu = (params?: ManageResource) => {
  return http.postJson<RestResult<ManageResource>>(
    `${basePath}/update-menu`,
    params
  );
};

//删除菜单
const deleteMenu = (resourceId: string) => {
  return http.post<any, RestResult<string>>(
    `${basePath}/delete-resource/${resourceId}`
  );
};

//查询待选择资源信息
const getSimpleResourceData = () => {
  return http.get<any, RestResult<Array<SimpleResource>>>(
    `${basePath}/getAllSimpleResource`
  );
};

//导出菜单数据
const exportMenuData = (params: any) =>
  http.postBlobWithJson(`${basePath}/export`, params);

//上传导入菜单数据
const uploadMenuData = (params: any) =>
  http.postUpdateFile<RestResult<string>>(`${basePath}/import`, params);

//更新菜单顺序
const updateSortIndex = (params: any) =>
  http.postJson<RestResult<string>>(`${basePath}/updateSortIndex`, params);

export {
  getMenuData,
  getMenuById,
  addMenu,
  updateMenu,
  deleteMenu,
  getSimpleResourceData,
  exportMenuData,
  uploadMenuData,
  updateSortIndex
};
