import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/message/manage`;

//消息信息-用于在表格中展示
export type MessageTableInfo = {
  relationId: string;
  messageId: string;
  messageTitle: string;
  status: boolean;
  moduleId: string;
  moduleName: string;
  typeId: string;
  typeName: string;
  priority: number;
  createTime: string;
};

//消息信息 - 用于查看详细信息
export type MessageInfo = {
  messageId: string;
  messageTitle: string;
  moduleName: string;
  typeName: string;
  priority: number;
  messageContent: string;
  contentType: number;
  createTime: string;
  linkData: Array<MessageLinkData>;
};

//消息链接信息
export type MessageLinkData = {
  linkText: string;
  routeName: string;
  outLinkUrl: string;
  params: object;
};

//消息模块信息
export type MessageModuleInfo = {
  moduleId: string;
  moduleName: string;
  createTime: string;
  updateTime: string;
  updateUid: string;
  sortIndex: number;
};

//消息类型信息
export type MessageTypeInfo = {
  typeId: string;
  moduleId: string;
  typeName: string;
  createTime: string;
  updateTime: string;
  updateUid: string;
  sortIndex: number;
};

//模块菜单数据
export type ModuleMenuData = {
  moduleId: string;
  moduleName: string;
  unReadNum: number;
};

//消息查询提交
export type MessageQueryCondition = {
  subject: string;
  moduleId: string;
  status: string;
  priority: string;
  typeId: string;
  typeName?: string;
  timeRange: Array<string>;
  createTime: Array<any>;
  pageNo: number;
  pageSize: number;
};

//按模块未读消息统计数据 - 当前用户
export type ModuleUnReadData = {
  moduleId: string;
  unReadNum: string;
};

//查询消息详细信息
const getMessageInfo = (messageId: string) =>
  http.get<string, RestResult<MessageInfo>>(
    `${basePath}/getMessageInfo?messageId=${messageId}`
  );

//查询所有消息数据
const getAllModuleData = () =>
  http.get<any, RestResult<Array<MessageModuleInfo>>>(
    `${basePath}/getAllModuleData`
  );

//查询消息类型数据
const getMessageTypeData = (moduleId: string) =>
  http.get<string, RestResult<Array<MessageTypeInfo>>>(
    `${basePath}/getMessageTypeData?moduleId=${moduleId}`
  );

//分页查询消息数据
const getMessagePageData = (condition: MessageQueryCondition) =>
  http.postJson<RestPageResult<Array<MessageTableInfo>>>(
    `${basePath}/getMessagePageData`,
    condition
  );

//查询近期消息
const getRecentMessage = (subject: number, limit: number) =>
  http.get<any, RestResult<Array<MessageTableInfo>>>(
    `${basePath}/getRecentMessage?subject=${subject}&limit=${limit}`
  );

//按模块计算未读消息数量
const countUnReadData = () =>
  http.get<any, RestResult<Array<ModuleUnReadData>>>(
    `${basePath}/countUnReadData`
  );

//更新消息状态为已读
const updateReadStatus = (relationIds: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/updateReadStatus?relationIds=${relationIds}`
  );

//新增消息模块
const addModule = (module: MessageModuleInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/addModule`, module);

//更新消息模块
const updateModule = (module: MessageModuleInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/updateModule`, module);

//删除消息模块
const deleteModule = (moduleId: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/deleteModule?moduleId=${moduleId}`
  );

//新增消息类型
const addMessageType = (type: MessageTypeInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/addMessageType`, type);

//删除消息类型
const deleteMessageType = (typeId: string) =>
  http.get<string, RestResult<string>>(
    `${basePath}/deleteMessageType?typeId=${typeId}`
  );

//更新消息类型
const updateMessageType = (type: MessageTypeInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/updateMessageType`, type);

export {
  getMessageInfo,
  getAllModuleData,
  getMessageTypeData,
  addModule,
  updateModule,
  deleteModule,
  addMessageType,
  deleteMessageType,
  updateMessageType,
  getMessagePageData,
  countUnReadData,
  updateReadStatus,
  getRecentMessage
};
