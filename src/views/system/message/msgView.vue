<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color"
        >
          <el-tree
            ref="moduleTreeRef"
            :data="moduleData"
            node-key="moduleId"
            :expand-on-click-node="false"
            highlight-current
            :props="moduleTreeProps"
            @current-change="moduleChangeHandler"
          >
            <template #default="{ node, data }">
              <div class="tree-list-content">
                <div class="tree-node-left">
                  <IconifyIconOffline icon="EP-Document"/>
                  <span>{{ node.label }}</span>
                </div>

                <div class="space-x-2">
                  <el-badge
                    v-if="data.unReadNum > 0"
                    :value="data.unReadNum"
                    class="pt-3"
                  />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1">
          <avue-crud
            ref="messageCrud"
            :data="messageData"
            :option="messageTableOption"
            :table-loading="loading"
            v-model:search="checkForm"
            v-model:page="page"
            @refresh-change="refreshChangeHandler"
            @current-change="loadMessageData(true)"
            @size-change="loadMessageData(true)"
            @search-change="searchChangeHandler"
            @search-reset="resetSearchForm"
            @selection-change="selectionChange"
          >
            <template #menu-left>
              <el-radio-group
                v-model="checkForm.subject"
                @change="refreshChangeHandler"
              >
                <el-radio-button label="0">待办</el-radio-button>
                <el-radio-button label="1">通知</el-radio-button>
              </el-radio-group>
            </template>

            <template #menu-right="{ size }">
              <el-tooltip
                effect="dark"
                content="标记为已读"
                placement="top"
                :open-delay="300"
              >
                <el-button
                  icon="el-icon-finished"
                  plain
                  circle
                  :size="size"
                  :disabled="selectedMessages.length === 0"
                  @click="batchUpdateReadStatus"
                />
              </el-tooltip>
            </template>

            <template #status="scope">
              <span v-if="scope.row.status">已读</span>
              <el-badge
                v-else
                is-dot
                style="margin-top: 5px; margin-right: 5px"
              >
                未读
              </el-badge>
            </template>

            <template #priority="scope">
              <el-tag v-if="scope.row.priority === 0" type="danger">高</el-tag>
              <el-tag v-if="scope.row.priority === 1" type="warning">中</el-tag>
              <el-tag v-if="scope.row.priority === 2">低</el-tag>
            </template>

            <template #messageTitle="{ size, row }">
              <el-link
                :size="size"
                type="primary"
                @click="viewMessageDetail(row)"
              >
                {{ row.messageTitle }}
              </el-link>
            </template>

            <template #menu="{ type, size, row }">
              <el-button
                icon="el-icon-finished"
                :size="size"
                :type="type"
                @click="viewMessageDetail(row)"
              >
                查看
              </el-button>
            </template>
          </avue-crud>
        </div>
      </template>
    </splitpane>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import splitpane, {ContextProps} from "@/components/ReSplitPane";
import {addDialog} from "@/components/ReDialog/index";
import {useUnReadMessageHook} from "@/store/modules/message";
import MessageDetailInfo from "@/views/system/message/components/MessageDetailInfo.vue";
import {ElTree} from "element-plus";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {computed, getCurrentInstance, h, nextTick, reactive, ref, toRefs} from "vue";
import {
  countUnReadData,
  getAllModuleData,
  getMessagePageData,
  getMessageTypeData,
  MessageQueryCondition,
  MessageTableInfo,
  MessageTypeInfo,
  ModuleMenuData,
  updateReadStatus
} from "@/views/system/message/api/MessageManageApi";

defineOptions({
  name: "SystemConfig_Message_Center"
});

const {$confirm, $notify} =
  getCurrentInstance().appContext.config.globalProperties;
const messageCrud = ref();
const moduleTreeRef = ref<InstanceType<typeof ElTree>>();
const messageDetailRef = ref<InstanceType<typeof MessageDetailInfo>>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const moduleTreeProps = {
  label: "moduleName"
};

const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 300;
});
const minMsgTypeData = {typeId: "", typeName: "全部"} as MessageTypeInfo;
const messageTableOption = reactive({
  align: "center",
  menuAlign: "center",
  labelWidth: 130,
  searchLabelWidth: 70,
  menuWidth: 100,
  height: tableHeight,
  stripe: true,
  border: true,
  selection: true,
  reserveSelection: true,
  rowKey: "relationId",
  selectable: row => {
    return !row.status;
  },
  tip: true,
  addBtn: false,
  refreshBtn: true,
  columnBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: true,
  searchShow: true,
  searchMenuSpan: 4,
  emptyBtnText: "重置",
  emptyBtnIcon: "el-icon-refresh-left",
  column: [
    {
      label: "消息标题",
      prop: "messageTitle",
      slot: true,
      overHidden: true
    },
    {label: "模块", prop: "moduleName", width: 200},
    {
      label: "类型",
      prop: "typeName",
      width: 200,
      search: true,
      searchSpan: 5,
      type: "select",
      dicData: [minMsgTypeData],
      props: {
        label: "typeName",
        value: "typeId"
      }
    },
    {
      label: "状态",
      prop: "status",
      width: 80,
      search: true,
      type: "select",
      searchType: "radio",
      button: true,
      searchSpan: 5,
      dicData: [
        {label: "全部", value: ""},
        {label: "未读", value: "0"},
        {label: "已读", value: "1"}
      ],
      slot: true
    },
    {
      label: "优先级",
      prop: "priority",
      width: 80,
      search: true,
      type: "select",
      searchType: "radio",
      button: true,
      searchSpan: 5,
      dicData: [
        {label: "全部", value: ""},
        {label: "高", value: "0"},
        {label: "中", value: "1"},
        {label: "低", value: "2"}
      ],
      slot: true
    },
    {
      label: "到达时间",
      prop: "createTime",
      width: 160,
      search: true,
      type: "date",
      searchSpan: 5,
      searchRange: true,
      startPlaceholder: "开始日期",
      endPlaceholder: "结束日期",
      searchClearable: false
    }
  ]
});

//数据对象
const state = reactive({
  moduleData: [] as Array<ModuleMenuData>,
  messageData: [] as Array<MessageTableInfo>,
  selectedMessages: [] as Array<string>,
  checkForm: {
    subject: "0",
    moduleId: "-1",
    status: "",
    priority: "",
    typeId: "",
    typeName: "",
    createTime: [dayjs().add(-7, "d"), dayjs().endOf("day")],
    timeRange: [
      dayjs().add(-7, "d").format("YYYY-MM-DD HH:mm:ss"),
      dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss")
    ],
    pageNo: 1,
    pageSize: defaultPageSize
  } as MessageQueryCondition,
  page: {
    currentPage: 1,
    total: 0,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  loading: false
});

const {moduleData, messageData, selectedMessages, checkForm, page, loading} =
  toRefs(state);

//查询左侧菜单数据
const loadModuleData = async () => {
  getAllModuleData().then(result => {
    const data = result.data;

    //转换成menu数据
    const menuData = [] as Array<ModuleMenuData>;
    data.forEach(m =>
      menuData.push({
        moduleId: m.moduleId,
        moduleName: m.moduleName,
        unReadNum: 0
      })
    );
    menuData.unshift({moduleId: "-1", moduleName: "全部消息", unReadNum: 0});
    state.moduleData = menuData;

    nextTick(() => {
      moduleTreeRef.value!.setCurrentKey("-1");
    });
  });
};
loadModuleData();

//模块选择改变触发
const moduleChangeHandler = (module: ModuleMenuData) => {
  state.checkForm.moduleId = module.moduleId;
  state.page.currentPage = 1;
  loadMessageData(true);
  loadMessageTypeData(module.moduleId);
};

//查询按钮触发
const searchChangeHandler = async (params, done) => {
  await loadMessageData(true);
  done();
};

//刷新按钮触发
const refreshChangeHandler = async () => {
  state.page.currentPage = 1;
  await loadMessageData(true);
};

//查询消息数据
const loadMessageData = async (asyncToStore: boolean) => {
  handleCheckForm();
  state.messageData = [];
  state.loading = true;
  await getMessagePageData(state.checkForm).then(res => {
    state.messageData = res.data != null ? res.data : [];
    state.page.total = parseInt(res.total != null ? res.total : "0");
    countUnReadMessageData(asyncToStore);
  });
  state.loading = false;
};

//加载消息类型数据
const loadMessageTypeData = async (moduleId: string) => {
  getMessageTypeData(moduleId).then(res => {
    state.checkForm.typeName = "";
    if (res.data != null) {
      messageTableOption.column[2].dicData = [minMsgTypeData, ...res.data];
    } else {
      messageTableOption.column[2].dicData = [minMsgTypeData];
    }
  });
};

//处理查询表单
const handleCheckForm = () => {
  state.checkForm.pageNo = state.page.currentPage;
  state.checkForm.pageSize = state.page.pageSize;
  state.checkForm.typeId = state.checkForm.typeName;
  state.checkForm.timeRange = [
    dayjs(state.checkForm.createTime[0]).format("YYYY-MM-DD HH:mm:ss"),
    dayjs(state.checkForm.createTime[1]).format("YYYY-MM-DD HH:mm:ss")
  ];
};

//按模块计算未读消息
const countUnReadMessageData = async (asyncToStore: boolean) => {
  countUnReadData().then(result => {
    const countData = result.data;
    if (countData != null && countData.length > 0) {
      //计算总数并更新各个模块的未读消息数量
      let total = 0;
      state.moduleData.forEach(menu => {
        const unReadCountObj = countData.filter(d => {
          return d.moduleId === menu.moduleId;
        });
        if (unReadCountObj != null && unReadCountObj.length > 0) {
          menu.unReadNum = parseInt(unReadCountObj[0].unReadNum);
          total = total + menu.unReadNum;
        } else {
          menu.unReadNum = 0;
        }
      });

      //总数大于0时 更新左侧菜单的全部消息数量
      if (total > 0) {
        const allModule = state.moduleData.filter(menu => {
          return menu.moduleId === "-1";
        });
        allModule[0].unReadNum = total;
      }
    } else {
      state.moduleData.forEach(menu => (menu.unReadNum = 0));
    }

    if (asyncToStore) {
      useUnReadMessageHook()?.loadUnReadCountData();
    }
  });
};

//重置查询表单
const resetSearchForm = () => {
  state.checkForm.createTime = [dayjs().add(-7, "d"), dayjs().endOf("day")];
};

//查看消息详细信息
const viewMessageDetail = (msg: MessageTableInfo) => {
  addDialog({
    title: msg.messageTitle,
    fullscreenIcon: true,
    closeOnClickModal: true,
    hideFooter: true,
    contentRenderer: () => h(MessageDetailInfo, {ref: messageDetailRef}),
    open: () => {
      messageDetailRef.value.loadMessageInfo(msg.messageId);
    },
    closeCallBack: () => {
      updateReadStatus(msg.relationId).then(() => {
        if (!msg.status) {
          loadMessageData(true);
        }
      });
    }
  });
};

//监听消息铃铛统计数据的变化
useUnReadMessageHook()?.$subscribe((mutation, storeState) => {
  const countData = storeState.unReadCountData;
  if (state.moduleData != null && state.moduleData.length > 0) {
    const allModule = state.moduleData.filter(menu => menu.moduleId === "-1");
    if (allModule[0].unReadNum != countData.total) {
      loadMessageData(false);
    }
  }
});

//消息多选
const selectionChange = (selRows: Array<MessageTableInfo>) => {
  const selectedRelations = [];
  selRows.forEach(row => selectedRelations.push(row.relationId));
  state.selectedMessages = selectedRelations;
};

//批量标记已读
const batchUpdateReadStatus = async () => {
  const msgNum = state.selectedMessages.length;
  $confirm(`您确认要将已选择的 ${msgNum} 条消息设置为已读么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(() => {
    updateReadStatus(state.selectedMessages.join()).then(() => {
      messageCrud.value.clearSelection();
      loadMessageData(true);
      $notify({
        title: "提示",
        message: `已成功将${msgNum}条消息设置为已读！`,
        type: "success"
      });
    });
  });
};
</script>

<style scoped>
.h-content {
  height: calc(100% - 20px);
}
</style>
