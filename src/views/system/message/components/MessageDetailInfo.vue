<template>
  <div>
    <div class="flex-bc">
      <div>
        <el-tag v-if="message.priority === 0" type="danger">高</el-tag>
        <el-tag v-if="message.priority === 1" type="warning">中</el-tag>
        <el-tag v-if="message.priority === 2">低</el-tag>
        <span class="text-xs font-semibold pl-1">
          {{ message.moduleName }}
        </span>
      </div>
      <div>
        <span class="text-xs">{{ message.createTime }}</span>
      </div>
    </div>
    <div
      class="border border-gray-400 border-opacity-20 rounded-lg overflow-y-auto my-3 p-2 dark:bg-dark-color"
    >
      <p class="w-full h-48">{{ message.messageContent }}</p>
    </div>
    <el-row
      v-if="message.linkData != null && message.linkData.length > 0"
      :gutter="10"
    >
      <el-col v-for="link in message.linkData" :key="link.linkText" :span="12">
        <el-link
          :icon="useRenderIcon('EP-Guide')"
          type="primary"
          @click="linkHandler(link)"
        >
          {{ link.linkText }}
        </el-link>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, getCurrentInstance } from "vue";
import {
  MessageInfo,
  getMessageInfo,
  MessageLinkData
} from "@/views/system/message/api/MessageManageApi";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { LocationQuery } from "vue-router";

const { $router } = getCurrentInstance().appContext.config.globalProperties;

//数据对象
const state = reactive({
  message: {} as MessageInfo
});

const { message } = toRefs(state);

//加载消息数据
const loadMessageInfo = (id: string) => {
  getMessageInfo(id).then(result => {
    state.message = result.data;
  });
};

//链接跳转
const linkHandler = (link: MessageLinkData) => {
  if (link.routeName) {
    $router.push({
      name: link.routeName,
      query: link.params as LocationQuery
    });
  } else if (link.outLinkUrl) {
    let outLinkUrl = link.outLinkUrl;
    if (link.params != null) {
      const paramArray = [];
      for (const key in link.params) {
        paramArray.push(key + "=" + link.params[key]);
      }
      if (paramArray.length > 0) {
        outLinkUrl = outLinkUrl + "?" + paramArray.join("&");
      }
    }
    window.open(outLinkUrl, "_blank");
  }
};

defineExpose({ loadMessageInfo });
</script>
