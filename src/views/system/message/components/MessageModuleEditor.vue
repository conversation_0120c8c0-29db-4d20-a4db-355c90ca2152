<template>
  <div>
    <avue-form ref="editFormRef" :option="option" v-model="tForm" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, getCurrentInstance } from "vue";
import { validSignId } from "@/utils/validator";
import {
  MessageModuleInfo,
  addModule,
  updateModule
} from "@/views/system/message/api/MessageManageApi";
import { ResultStatus } from "@/utils/http/types";

const { $notify } = getCurrentInstance().appContext.config.globalProperties;
const editFormRef = ref();

const props = withDefaults(defineProps<MessageModuleInfo>(), {
  moduleId: "",
  moduleName: "",
  createTime: "",
  updateTime: "",
  updateUid: "",
  sortIndex: 0
});
const tForm = ref(props);

//声明事件
const emit = defineEmits(["submit"]);

//模块标识校验
const validateTenantId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：portal_msg"));
  }
};

//avue-form组件属性
const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "模块标识",
      prop: "moduleId",
      disabled: props.moduleId ? true : false,
      maxlength: 32,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入模块标识",
          trigger: "blur"
        },
        { validator: validateTenantId, trigger: "blur" }
      ]
    },
    {
      label: "模块名称",
      prop: "moduleName",
      maxlength: 100,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输入模块名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "序号",
      prop: "sortIndex",
      type: "number",
      value: 0,
      min: 0,
      max: 999
    }
  ]
});

function submitData() {
  editFormRef.value.validate((valid, done) => {
    if (valid) {
      if (props.moduleId) {
        //更新
        updateModule(tForm.value).then(res => {
          if (res.status === ResultStatus.Success) {
            $notify({
              title: "提示",
              message: "已成功保存模块信息！",
              type: "success"
            });
            emit("submit", true);
          }
          done();
        });
      } else {
        //新增
        addModule(tForm.value).then(res => {
          if (res.status === ResultStatus.Success) {
            $notify({
              title: "提示",
              message: "已成功添加模块信息！",
              type: "success"
            });
            emit("submit", true);
          }
          done();
        });
      }
    }
  });
}
defineExpose({ submitData });
</script>
