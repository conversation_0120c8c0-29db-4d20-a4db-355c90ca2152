<template>
  <el-dialog
    :title="'路由参数 - ' + props.entryName"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    width="50%"
  >
    <avue-crud
      ref="paramsCrudRef"
      :option="option"
      :data="data"
      @row-update="addUpdate"
      @row-save="rowSave"
      @row-del="deleteParams"
    />
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-CircleCheck')"
        @click="saveDataHandler"
      >
        保 存
      </el-button>
      <el-button @click="cancel" :icon="useRenderIcon('EP-CircleClose')">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
//组件属性
import { computed, reactive, ref, toRefs, watch } from "vue";
import { message } from "@/utils/message";
import { ResultStatus } from "@/utils/http/types";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  RouteParams,
  updateQuickEntryRouteParams
} from "@/views/system/quickentry/api/QuickEntryManageApi";

const maxParamsLimit = 5;
const paramsCrudRef = ref();

const props = defineProps({
  visible: {
    type: Boolean
  },
  params: {
    type: Array<RouteParams>
  },
  entryId: {
    type: String
  },
  entryName: {
    type: String
  }
});

//数据对象
const state = reactive({
  dialogVisible: false,
  data: [] as Array<RouteParams>
});
const { data, dialogVisible } = toRefs(state);

watch(props, async (newValue: any) => {
  state.dialogVisible = newValue.visible;
  if (!state.dialogVisible) {
    return;
  }
  state.data = newValue.params;
});

//表格配置
const option = reactive({
  addBtn: false,
  addRowBtn: true,
  cellBtn: true,
  stripe: true,
  index: true,
  refreshBtn: false,
  columnBtn: false,
  menuWidth: 160,
  height: 380,
  rowKey: "$index",
  column: [
    {
      label: "参数Key",
      prop: "key",
      cell: true,
      maxlength: 20,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输参数Key",
          trigger: "blur"
        }
      ]
    },
    {
      label: "参数值",
      prop: "value",
      cell: true,
      maxlength: 30,
      showWordLimit: true,
      rules: [
        {
          required: true,
          message: "请输参数值",
          trigger: "blur"
        }
      ]
    }
  ]
});

//定义事件
const emit = defineEmits(["update:visible", "save-success"]);

//新增数据
const rowSave = (row, done) => {
  done(row);
};

//更新数据
const addUpdate = (row, index, done) => {
  done(row);
};

//计算待保存的有效数据的数量
const needSaveParamLength = computed(() => {
  return state.data.filter(r => r.key != null && r.key.length > 0).length;
});

watch(needSaveParamLength, newValue => {
  if (newValue > maxParamsLimit) {
    message(`最多只能定义${maxParamsLimit}个参数`, { type: "warning" });
    option.addRowBtn = false;
  } else {
    option.addRowBtn = true;
  }
});

//删除参数
const deleteParams = async (row, index, done) => {
  done(row);
};

//整体保存到服务端
const saveDataHandler = () => {
  if (needSaveParamLength.value > maxParamsLimit) {
    message(`最多只能定义${maxParamsLimit}个参数,请删除多余的参数！`, {
      type: "warning"
    });
  } else {
    updateQuickEntryRouteParams({
      id: props.entryId,
      routeParams: state.data
    }).then(res => {
      if (res.status == ResultStatus.Success) {
        message("快捷入口路由参数已成功保存！");
        emit("save-success");
        cancel();
      }
    });
  }
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
