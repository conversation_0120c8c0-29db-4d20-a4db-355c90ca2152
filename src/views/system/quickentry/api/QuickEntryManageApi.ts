import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/quick-entry`;

//路由参数
export type RouteParams = {
  key: string;
  value: string;
};

//快捷入口信息
export type QuickEntryInfo = {
  resourceName: string;
  resourceIcon: string;
  resourceCode: string;
  resourceUrl: string;
  customName: string;
  customIcon: string;
  routeParams: Array<RouteParams>;
};

//快捷入口管理对象
export type QuickEntryManageInfo = {
  id: string;
  roleTypeId: string;
  roleTypeName: string;
  resourceId: string;
  resourceName: string;
  permCode: string;
  permCodeName: string;
  customName: string;
  customIcon: string;
  routeParams: Array<RouteParams>;
  updateUid: string;
  createTime: string;
  updateTime: string;
  sortIndex: number;
};

//新增快捷入口
export const addQuickEntryInfo = (params: QuickEntryManageInfo) =>
  http.postJson<RestResult<QuickEntryManageInfo>>(
    `${basePath}/add-entry`,
    params
  );

//更新快捷入口
export const updateQuickEntryInfo = (params: QuickEntryManageInfo) =>
  http.postJson<RestResult<QuickEntryManageInfo>>(
    `${basePath}/update-entry`,
    params
  );

//删除快捷入口
export const deleteQuickEntryInfo = (id: string) =>
  http.get<any, RestResult<string>>(`${basePath}/delete-entry?id=${id}`);

//查询快捷入口管理数据
export const getQuickEntryManageData = (roleTypeId: string) =>
  http.get<string, RestResult<Array<QuickEntryManageInfo>>>(
    `${basePath}/get-entry-list?roleTypeId=${roleTypeId}`
  );

//查询快捷入口显示数据
export const getQuickEntryViewData = () =>
  http.get<any, RestResult<Array<QuickEntryInfo>>>(
    `${basePath}/get-current-user-quick-entry`
  );

//更新、保存快捷入口路由参数
export const updateQuickEntryRouteParams = (params: Object) =>
  http.postJson<RestResult<string>>(`${basePath}/update-entry-params`, params);
