<template>
  <div class="ml-1.5 mr-1">
    <avue-crud
      ref="entryCrudRef"
      :data="entryData"
      v-model="entry"
      :option="entryTableOption"
      @refresh-change="loadEntryData(state.selectedRoleType)"
      @row-save="addEntryInfo"
      @row-update="updateEntryInfo"
      @row-del="deleteEntryInfo"
    >
      <template #menu-right="{ size }">
        <div class="float-left flex-c">
          <el-text>角色类型：</el-text>
          <el-select
            v-model="selectedRoleType"
            filterable
            style="width: 200px"
            @change="loadEntryData"
          >
            <el-option
              v-for="item in roleTypeData"
              :key="item.typeId"
              :label="item.typeName"
              :value="item.typeId"
            />
          </el-select>
          <el-divider direction="vertical" border-style="dashed" />
          <el-input
            clearable
            v-model="searchVal"
            :suffix-icon="useRenderIcon('EP-Search')"
            :size="size"
            style="width: 200px"
            placeholder="入口名称"
            @blur="searchVal = $event.target.value.trim()"
          />
        </div>
        <el-divider direction="vertical" border-style="dashed" />
      </template>
      <template #menu="{ row, size }">
        <el-link
          plain
          :underline="false"
          :size="size"
          type="primary"
          :icon="useRenderIcon('RI-ShareLine')"
          @click="editParamsHandler(row)"
          v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
        >
          参数
        </el-link>
      </template>
    </avue-crud>

    <!-- 路由参数编辑 -->
    <entry-params-editor
      v-model:visible="paramsEdit.visible"
      :params="paramsEdit.params"
      :entry-id="paramsEdit.entryId"
      :entry-name="paramsEdit.entryName"
      @save-success="loadEntryData(state.selectedRoleType)"
    />
  </div>
</template>

<script lang="ts" setup>
import { toRefs, reactive, computed, ref, getCurrentInstance } from "vue";
import { ResultStatus } from "@/utils/http/types";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getSimpleResourceData,
  SimpleResource
} from "@/views/system/menumanage/api/MenuManageApi";
import {
  BaseRoleTypeInfo,
  getTypeSelectData
} from "@/views/system/permmanage/api/TypeManageApi";
import {
  QuickEntryManageInfo,
  RouteParams,
  addQuickEntryInfo,
  getQuickEntryManageData,
  updateQuickEntryInfo,
  deleteQuickEntryInfo
} from "@/views/system/quickentry/api/QuickEntryManageApi";
import EntryParamsEditor from "@/views/system/quickentry/components/EntryParamsEditor.vue";
import { AdminEnum } from "@/utils/CommonTypes";
import { hasAuth } from "@/router/utils";

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

const entryCrudRef = ref();
const menuData = ref([] as Array<SimpleResource>);
const permData = ref([] as Array<SimpleResource>);

//处理资源数据，转成成树结构数据
const dealResourceData = (resData: Array<SimpleResource>) => {
  const firstLevelNode = [] as Array<SimpleResource>;
  //转换成树数据
  for (const resNode of resData) {
    resNode.children = [];
    if (resNode.parentId === "-1") {
      firstLevelNode.push(resNode);
    }
    for (const childNode of resData) {
      if (
        childNode.parentId === resNode.resourceId &&
        childNode.permCode.length == 0
      ) {
        resNode.children.push(childNode);
      }
    }

    //存储权限编码到permCodeData
    if (resNode.permCode.length > 0) {
      state.permCodeData.push(resNode);
    }
  }

  //push到绑定数据
  menuData.value = firstLevelNode;
};

//加载菜单数据
getSimpleResourceData().then(res => {
  if (res.status == ResultStatus.Success) {
    dealResourceData(res.data);
  }
});

//加载角色类型下拉框数据
getTypeSelectData().then(result => {
  state.roleTypeData = result.data;
  state.selectedRoleType = state.roleTypeData[0].typeId;
  loadEntryData(state.selectedRoleType);
});

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 150;
});

const resourceChangeHandler = ({ value }) => {
  permData.value = [];
  for (const perm of state.permCodeData) {
    if (perm.parentId == value) {
      permData.value.push(perm);
    }
  }

  //判断是否要清空permCode值
  const permArray: Array<SimpleResource> = permData.value.filter(
    item => item.resourceId == state.entry.permCode
  );
  if (permArray.length == 0) {
    state.entry.permCode = "";
  }
};

const entryTableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  rowKey: "id",
  height: tableHeight,
  menuWidth: 175,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {
      label: "排序",
      prop: "sortIndex",
      sortable: true,
      width: 70,
      type: "number",
      value: 0,
      min: 0,
      max: 999
    },
    {
      label: "角色类型",
      prop: "roleTypeName",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "对应菜单",
      prop: "resourceId",
      hide: true,
      type: "tree",
      dicData: menuData,
      parent: false,
      filterable: true,
      props: {
        label: "resourceName",
        value: "resourceId"
      },
      showColumn: false,
      order: 2,
      rules: [
        {
          required: true,
          message: "请选择对应菜单",
          trigger: "blur"
        }
      ],
      change: resourceChangeHandler
    },
    {
      label: "对应菜单",
      prop: "resourceName",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "对应权限码",
      prop: "permCodeName",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "对应权限码",
      prop: "permCode",
      hide: true,
      type: "select",
      dicData: permData,
      filterable: true,
      props: {
        label: "resourceName",
        value: "resourceId"
      },
      showColumn: false,
      order: 2
    },
    {
      label: "自定义名称",
      prop: "customName",
      order: 1,
      maxlength: 15,
      showWordLimit: true
    },
    {
      label: "自定义图标",
      prop: "customIcon",
      order: 1,
      maxlength: 200,
      showWordLimit: true
    },
    {
      label: "创建时间",
      prop: "createTime",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "更新时间",
      prop: "updateTime",
      editDisplay: false,
      addDisplay: false,
      sortable: true
    },
    {
      label: "更新人",
      prop: "updateUid",
      editDisplay: false,
      addDisplay: false
    }
  ]
});

//数据对象
const state = reactive({
  data: [] as Array<QuickEntryManageInfo>,
  permCodeData: [] as Array<SimpleResource>,
  roleTypeData: [] as Array<BaseRoleTypeInfo>,
  selectedRoleType: "",
  entry: {} as QuickEntryManageInfo,
  searchVal: "",
  paramsEdit: {
    visible: false,
    entryId: null,
    entryName: null,
    params: [] as Array<RouteParams>
  }
});

const { entry, selectedRoleType, roleTypeData, searchVal, paramsEdit } =
  toRefs(state);

const entryData = computed(() => {
  if (state.searchVal != null && state.searchVal.length > 0) {
    return state.data.filter(item => {
      return (
        item.resourceName.includes(searchVal.value) ||
        item.customName.includes(searchVal.value)
      );
    });
  } else {
    return state.data;
  }
});

//加载入口数据
const loadEntryData = (roleTypeId: string) => {
  state.data = [];
  getQuickEntryManageData(roleTypeId).then(res => (state.data = res.data));
};

//新增入口
const addEntryInfo = async (row, done) => {
  row.roleTypeId = state.selectedRoleType;
  await addQuickEntryInfo(row as QuickEntryManageInfo).then(res => {
    if (res.status == ResultStatus.Success) {
      done(res.data);
      $notify({
        title: "提示",
        message: "已成功添加快捷入口信息！",
        type: "success"
      });
      loadEntryData(state.selectedRoleType);
    }
  });
};

//更新快捷入口
const updateEntryInfo = async (row, index, done) => {
  await updateQuickEntryInfo(row as QuickEntryManageInfo).then(res => {
    if (res.status == ResultStatus.Success) {
      done(res.data);
      $notify({
        title: "提示",
        message: "已成功保存快捷入口信息！",
        type: "success"
      });
      loadEntryData(state.selectedRoleType);
    }
  });
};

//删除快捷入口
const deleteEntryInfo = async (row, index, done) => {
  await $confirm(
    `您确定要删除 '${
      row.customName != null && row.customName.length > 0
        ? row.customName
        : row.resourceName
    }' 快捷入口么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  );
  deleteQuickEntryInfo(row.id).then(res => {
    if (res.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功删除快捷入口信息！",
        type: "success"
      });
      done(row);
    }
  });
};

//编辑路由参数
const editParamsHandler = (row: any) => {
  state.paramsEdit.params = row.routeParams != null ? row.routeParams : [];
  state.paramsEdit.entryId = row.id;
  state.paramsEdit.entryName =
    row.customName.length > 0 ? row.customName : row.resourceName;
  state.paramsEdit.visible = true;
};
</script>
