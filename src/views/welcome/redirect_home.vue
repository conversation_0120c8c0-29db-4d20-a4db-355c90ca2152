<template>
  <div></div>
</template>

<script lang="ts" setup>
import {getCurrentInstance, onMounted} from "vue";
import {getTopMenu} from "@/router/utils";

const {$router} = getCurrentInstance().appContext.config.globalProperties;

//跳转到第一个有效菜单
const toHomePage = () => {
  const topMenu = getTopMenu();
  $router.push(topMenu.path);
};

onMounted(() => {
  setTimeout(() => {
    toHomePage();
  }, 300);
});
</script>
