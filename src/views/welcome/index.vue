<script setup lang="ts">
import UserStatistics from "@/views/welcome/components/UserStatistics.vue";
import UserTypePie from "@/views/welcome/components/UserTypePie.vue";
import LoginStatistics from "@/views/welcome/components/LoginStatistics.vue";
import MenuAccessStatistics from "@/views/welcome/components/MenuAccessStatistics.vue";
import ImportantOperateStatistics from "@/views/welcome/components/ImportantOperateStatistics.vue";
import QuickEntry from "@/views/welcome/components/QuickEntry.vue";

defineOptions({
  name: "Welcome"
});
</script>

<template>
  <div
    class="h-full w-full justify-center items-center pr-6 pt-2 pb-2 space-y-4"
  >
    <el-row :gutter="15">
      <el-col :span="17" class="flex space-y-4">
        <user-statistics />
        <login-statistics />
      </el-col>
      <el-col :span="7">
        <quick-entry />
      </el-col>
    </el-row>
    <el-row :gutter="15">
      <el-col :span="8">
        <menu-access-statistics />
      </el-col>
      <el-col :span="9">
        <important-operate-statistics />
      </el-col>
      <el-col :span="7">
        <user-type-pie />
      </el-col>
    </el-row>
  </div>
</template>
