<template>
  <el-card shadow="hover" header="快捷入口">
    <template #header>
      <div class="flex-bc">
        <div class="flex-sc space-x-1">
          <iconify-icon-offline
            icon="RI-BookmarkFill"
            :style="{ color: '#409EFF' }"
          />
          <span class="font-bold">快捷入口</span>
        </div>
      </div>
    </template>
    <div class="overflow-y-auto overflow-x-hidden w-full h-[428px]">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(item, index) in data" :key="index">
          <entry-icon :entry="item" :key="index" />
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { toRefs, reactive } from "vue";
import { ResultStatus } from "@/utils/http/types";
import {
  QuickEntryInfo,
  getQuickEntryViewData
} from "@/views/system/quickentry/api/QuickEntryManageApi";
import EntryIcon from "@/views/welcome/components/EntryIcon.vue";

//数据对象
const state = reactive({
  data: [] as Array<QuickEntryInfo>
});

const { data } = toRefs(state);

//查询快捷入口数据
getQuickEntryViewData().then(res => {
  if (res.status == ResultStatus.Success) {
    state.data = res.data;
  }
});
</script>
