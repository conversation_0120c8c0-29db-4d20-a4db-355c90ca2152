<template>
  <el-card shadow="hover">
    <template #header>
      <div class="flex-bc">
        <div class="flex-sc space-x-1">
          <iconify-icon-offline
            icon="RI-BookmarkFill"
            :style="{ color: '#409EFF' }"
          />
          <span class="font-bold">重要操作数排行</span>
        </div>
        <div class="flex items-center">
          <el-radio-group v-model="timeBelong" @change="loadChartData()">
            <el-radio-button label="7">7天</el-radio-button>
            <el-radio-button label="15">15天</el-radio-button>
            <el-radio-button label="30">30天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>
    <div ref="barChartRef" style="width: 100%; height: 500px" />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch, type Ref, onMounted } from "vue";
import {
  delay,
  EchartOptions,
  useDark,
  useECharts,
  UtilsEChartsOption
} from "@pureadmin/utils";
import { useAppStoreHook } from "@/store/modules/app";
import {
  countOperateActionTimes,
  ColumnChartData
} from "@/views/welcome/api/StatisticApi";

const { isDark } = useDark();
const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});
const sideBarStatus = computed(() => {
  return useAppStoreHook().getSidebarStatus;
});
const layout = computed(() => {
  return useAppStoreHook().getLayout;
});
const timeBelong = ref("7");

const chartOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    top: "5%",
    containLabel: true
  },
  xAxis: {
    type: "value",
    boundaryGap: [0, 0.01]
  },
  yAxis: {
    type: "category",
    data: []
  },
  series: [
    {
      name: "操作次数：",
      type: "bar",
      data: [],
      color: ["#f5a023"]
    }
  ]
};

const barChartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize, getInstance } = useECharts(
  barChartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);
setOptions(chartOption as UtilsEChartsOption);

//监听界面和主题变化
watch([sideBarStatus, layout], () => {
  delay(600).then(() => resize());
});
watch(theme, () => {
  delay(300).then(() => loadChartData());
});

const loadChartData = () => {
  countOperateActionTimes(timeBelong.value).then(res => updateData(res.data));
};

const updateData = (data: ColumnChartData) => {
  const chart = getInstance();
  const option = chart!.getOption();
  if (option) {
    option.yAxis[0].data = data.yaxisData;
    option.series[0].data = data.seriesData;
    chart!.setOption(option);
  }
};

onMounted(() => {
  delay(300).then(() => loadChartData());
});
</script>
