<template>
  <el-card shadow="hover">
    <template #header>
      <div class="flex-bc">
        <div class="flex-sc space-x-1">
          <iconify-icon-offline
            icon="RI-BookmarkFill"
            :style="{ color: '#409EFF' }"
          />
          <span class="font-bold">用户统计</span>
        </div>
        <div class="flex items-center">
          <el-radio-group v-model="timeBelong" @change="timeChangeHandler">
            <el-radio-button label="0">本月</el-radio-button>
            <el-radio-button label="1">上月</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>
    <div class="flex-bc px-2">
      <avue-data-box ref="boxRef" :option="option" class="w-full" />
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { toRefs, reactive, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  UserCountInfo,
  countUserInfo,
  countOlineUserNumber
} from "@/views/welcome/api/StatisticApi";

const boxRef = ref();

//数据对象
const state = reactive({
  timeBelong: "0",
  option: {
    span: 6,
    data: [
      {
        title: "总用户数",
        count: 0,
        icon: useRenderIcon("EP-User"),
        color: "rgb(245,160,35)"
      },
      {
        title: "月活用户数",
        count: 0,
        icon: useRenderIcon("EP-Promotion"),
        color: "rgb(196,11,11)"
      },
      {
        title: "月登录次数",
        count: 0,
        icon: useRenderIcon("EP-SwitchButton"),
        color: "rgb(5,162,55)"
      },
      {
        title: "在线用户数",
        count: 0,
        icon: useRenderIcon("EP-Connection"),
        color: "rgb(4,103,36)"
      }
    ]
  },
  countData: {} as UserCountInfo
});
const { timeBelong, option } = toRefs(state);

//统计时间变更
const timeChangeHandler = () => {
  loadCountData();
};

//加载统计数据
const loadCountData = async () => {
  await countUserInfo(parseInt(state.timeBelong)).then(
    res => (state.countData = res.data)
  );
  await countOlineUserNumber().then(
    res => (state.countData.onlineNum = res.data)
  );
  dealCountData();
};

const dealCountData = () => {
  state.option.data[0].count = state.countData.total;
  state.option.data[1].count = state.countData.activeNum;
  state.option.data[2].count = state.countData.loginNum;
  state.option.data[3].count = state.countData.onlineNum;
};

loadCountData();
</script>
