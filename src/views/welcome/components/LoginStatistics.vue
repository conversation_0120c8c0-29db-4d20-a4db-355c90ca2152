<template>
  <el-card shadow="hover">
    <template #header>
      <div class="flex-bc">
        <div class="flex-sc space-x-1">
          <iconify-icon-offline
            icon="RI-BookmarkFill"
            :style="{ color: '#409EFF' }"
          />
          <span class="font-bold">登录统计</span>
        </div>
        <div class="flex items-center">
          <el-radio-group v-model="timeBelong" @change="loadChartData()">
            <el-radio-button label="7">7天</el-radio-button>
            <el-radio-button label="15">15天</el-radio-button>
            <el-radio-button label="30">30天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </template>
    <div ref="lineChartRef" style="width: 100%; height: 200px" />
  </el-card>
</template>

<script lang="ts" setup>
import { ref, computed, watch, type Ref, onMounted } from "vue";
import {
  delay,
  useDark,
  useECharts,
  EchartOptions,
  UtilsEChartsOption
} from "@pureadmin/utils";
import { useAppStoreHook } from "@/store/modules/app";
import {
  countLoginTimes,
  ColumnChartData
} from "@/views/welcome/api/StatisticApi";

const { isDark } = useDark();
const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});
const sideBarStatus = computed(() => {
  return useAppStoreHook().getSidebarStatus;
});
const layout = computed(() => {
  return useAppStoreHook().getLayout;
});
const timeBelong = ref("7");

const chartOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  xAxis: [
    {
      type: "category",
      data: []
    }
  ],
  yAxis: [
    {
      type: "value"
    }
  ],
  series: [
    {
      data: [],
      type: "line"
    }
  ],
  grid: {
    left: "5%",
    right: "2%",
    top: "5%",
    bottom: "10%"
  }
};

const lineChartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize, getInstance } = useECharts(
  lineChartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);
setOptions(chartOption as UtilsEChartsOption);

//监听界面和主题变化
watch([sideBarStatus, layout], () => {
  delay(600).then(() => resize());
});
watch(theme, () => {
  delay(300).then(() => loadChartData());
});

const loadChartData = () => {
  countLoginTimes(timeBelong.value).then(res => updateData(res.data));
};

const updateData = (data: ColumnChartData) => {
  const chart = getInstance();
  const option = chart!.getOption();
  if (option) {
    option.xAxis[0].data = data.xaxisData;
    option.series[0].data = data.seriesData;
    chart!.setOption(option);
  }
};

onMounted(() => {
  delay(300).then(() => loadChartData());
});
</script>
