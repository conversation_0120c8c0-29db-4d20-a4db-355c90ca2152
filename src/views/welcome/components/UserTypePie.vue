<template>
  <el-card shadow="hover" header="用户类型">
    <template #header>
      <div class="flex-bc">
        <div class="flex-sc space-x-1">
          <iconify-icon-offline
            icon="RI-BookmarkFill"
            :style="{ color: '#409EFF' }"
          />
          <span class="font-bold">用户类型占比</span>
        </div>
      </div>
    </template>
    <div ref="pieChartRef" style="width: 100%; height: 500px" />
  </el-card>
</template>

<script lang="ts" setup>
import { computed, onMounted, type Ref, ref, watch } from "vue";
import { useAppStoreHook } from "@/store/modules/app";
import {
  delay,
  type EchartOptions,
  useDark,
  useECharts
} from "@pureadmin/utils";
import { countUserTypeData } from "@/views/welcome/api/StatisticApi";

const { isDark } = useDark();

const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

const sideBarStatus = computed(() => {
  return useAppStoreHook().getSidebarStatus;
});

const layout = computed(() => {
  return useAppStoreHook().getLayout;
});

const pieChartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize, getInstance } = useECharts(
  pieChartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);
setOptions({
  tooltip: {
    trigger: "item"
  },
  legend: {
    icon: "circle",
    bottom: 1
  },
  series: [
    {
      name: "用户数量",
      type: "pie",
      bottom: "20%",
      top: "15%",
      radius: "80%",
      center: ["50%", "50%"],
      color: ["#05a237", "#026086", "#696868"],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)"
        }
      }
    }
  ]
});

//监听界面和主题变化
watch([sideBarStatus, layout], () => {
  delay(600).then(() => resize());
});
watch(theme, () => {
  delay(300).then(() => loadChartData());
});

const loadChartData = () => {
  countUserTypeData().then(res => updateData(res.data));
};

const updateData = (data: Array<any>) => {
  const chart = getInstance();
  const option = chart!.getOption();
  if (option) {
    option.series[0].data = data;
    chart!.setOption(option);
  }
};

onMounted(() => {
  delay(300).then(() => loadChartData());
});
</script>
