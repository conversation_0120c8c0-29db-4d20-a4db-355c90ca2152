<template>
  <div
    class="content-center cursor-pointer text-primary hover:bg-primary hover:text-white rounded-lg hover:bg-opacity-50 pb-3"
    @click="jumpTo"
  >
    <div class="w-full flex justify-center items-center pt-3 pb-2">
      <IconifyIconOffline :icon="props.entry.resourceIcon" width="60" />
    </div>
    <div class="w-full flex justify-center items-center">
      <span class="text-xs/[12px] font-bold">
        {{ props.entry.resourceName }}
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from "vue";

const { $router } = getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  entry: {
    type: Object
  }
});

const jumpTo = () => {
  $router.push({
    name: props.entry.resourceCode,
    query: props.entry.routeParams
  });
};
</script>
