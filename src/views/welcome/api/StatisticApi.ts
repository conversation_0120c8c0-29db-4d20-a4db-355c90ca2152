import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";

const statisticBasePath = `${ServerNames.portalServer}/statistic`;
const sessionBasePath = `${ServerNames.portalServer}/session`;

//用户统计响应数据
export type UserCountInfo = {
  total: number;
  activeNum: number;
  loginNum: number;
  onlineNum: number;
};

export type ColumnChartData = {
  xaxisData: Array<string>;
  yaxisData: Array<string>;
  seriesData: Array<number>;
};

//用户数据统计
const countUserInfo = (time: number) =>
  http.get<number, RestResult<UserCountInfo>>(
    `${statisticBasePath}/countUserInfo?time=${time}`
  );

//获取在线用户数
const countOlineUserNumber = () =>
  http.get<any, RestResult<number>>(`${sessionBasePath}/countOlineUserNumber`);

//统计用户类型占比数据
const countUserTypeData = () =>
  http.get<any, RestResult<any>>(`${statisticBasePath}/countUserTypeData`);

//按天计算某段时间内的系统登录次数
const countLoginTimes = (dayLong: string) =>
  http.get<number, RestResult<ColumnChartData>>(
    `${statisticBasePath}/countLoginTimes?dayLong=${dayLong}`
  );

//统计某个时间段内的菜单点击次数排行
const countMenuAccessTimes = (dayLong: string) =>
  http.get<number, RestResult<ColumnChartData>>(
    `${statisticBasePath}/countMenuAccessTimes?dayLong=${dayLong}`
  );

//统计某个时间段内的操作行为次数排行
const countOperateActionTimes = (dayLong: string) =>
  http.get<number, RestResult<ColumnChartData>>(
    `${statisticBasePath}/countOperateActionTimes?dayLong=${dayLong}`
  );

export {
  countUserInfo,
  countOlineUserNumber,
  countUserTypeData,
  countLoginTimes,
  countMenuAccessTimes,
  countOperateActionTimes
};
