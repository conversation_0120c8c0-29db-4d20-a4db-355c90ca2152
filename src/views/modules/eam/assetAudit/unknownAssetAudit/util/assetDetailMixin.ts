import {
  reactive,
  ref,
  toRefs,
  watch,
  onMounted,
  nextTick,
} from "vue";
import {
  ElMessage,
  ElMessageBox
} from 'element-plus';
import {
  http
} from "@/utils/http";
/***
 * 获取按小类分组的表单字段集合（资产首页表单布局使用）
 * 全局只查询一遍（刷新页面重新查询）
 *
 */
const basePath = "rest-proxy";
let mockMode = false;
const queryAllPositionSet = () => {
  if (!mockMode) {
    return axios.get(`${basePath}/assetPosition/queryAllPositionSet`);
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          14: {
            category_name: "摄像机",
            category_id: "14",
            columns: [{
              field: "name",
              name: "资产名称",
            },
            {
              field: "enableTime",
              name: "启用时间",
            },
            {
              field: "remarks",
              name: "备注",
            },
            {
              field: "dataSource",
              name: "数据来源",
            },
            {
              field: "tenant_code__label",
              name: "租户",
            },
            {
              field: "ipAddress",
              name: "IP地址",
            },
            {
              field: "state",
              name: "状态",
            },
            {
              field: "createTime",
              name: "创建时间",
            },
            ],
            position_image: "el-icon-platform-eleme",
            topo_image: "el-icon-eleme",
            portType: "1",
          },
          25: {
            category_name: "交换机",
            category_id: "25",
            columns: [{
              field: "name",
              name: "资产名称",
            },
            {
              field: "ipAddress",
              name: "IP地址",
            },
            {
              field: "macAddress",
              name: "MAC地址",
            },
            {
              field: "zoneName__label",
              name: "行政区划",
            },
            {
              field: "assetCompany",
              name: "资产厂商",
            },
            {
              field: "assetSpec",
              name: "资产型号",
            },
            {
              field: "assetVersion",
              name: "资产版本",
            },
            {
              field: "osType",
              name: "操作系统",
            },
            ],
            position_image: "el-icon-platform-eleme",
            topo_image: "el-icon-eleme",
            isShowPort: true,
            /** portType = 1 服务端口；portType = 2 网络端口；portType = 3 不显示   */
            portType: "1",
          },
        },
      });
    }, 200);
  });
};

const assetDetailMixin = () => {
  /**
   * {name, type, model}
   * */
  const detailAssetItem = ref({});
  const categoryColumns = ref({});
  const assetDetailVisible = ref(false);

  /** 查询*/
  const queryForAssetDetail = () => {
    queryCategoryColumns();
  }

  /** 根据ip查询详情资产信息 */
  const queryDetailByIpAddress = async (ipAddress) => {
    let viewLoading = ElMessage({
      message: "正在查询资产实例...",
      // iconClass: "el-message__icon  el-icon-loading",
      duration: 0,
    });
    queryAssetModel(ipAddress, assetItem => {
      viewLoading.close();
      if (assetItem) {
        assetDetailVisible.value = true;
      } else {
        ElMessage.error("未查询到资产信息");
      }
    });
  }

  /** 根据id查询详情资产信息 */
  const queryDetailById = async (instanceId, es_index) => {
    let viewLoading = ElMessage({
      message: "正在查询资产实例...",
      // iconClass: "el-message__icon  el-icon-loading",
      duration: 0,
    });
    queryAssetModelById(instanceId, es_index, assetItem => {
      viewLoading.close();
      if (assetItem) {
        assetDetailVisible.value = true;
      } else {
        ElMessage.error("未查询到资产信息");
      }
    });
  }

  /** 根据id查询资产模型 */
  const queryAssetModelById = (instanceId, es_index, callback) => {
    http
      .post(
        `rest-proxy/assetPosition/queryAssetPositionData`, {
        "_id": instanceId,
        "es_index": es_index
      }, {
        headers: {
          "Content-Type": "application/json",
        },
      }
      )
      .then(res => {
        let assets = res.data?.rows || [];
        let instance = assets[0];
        detailAssetItem.value = instance;
        if (callback && typeof callback == "function") {
          callback(detailAssetItem.value);
        }
      })
      .catch(err => {
        console.error(err);
        callback(null);
      });
  }

  /** 根据ip查询资产模型 */
  const queryAssetModel = (ipAddress, callback) => {
    http
      .get(
        "security-event/sem/assetInfoDetail?ipAddress=" + ipAddress
      )
      .then(res => {
        let assets = res['data'] || [];
        let instance = assets[0];
        detailAssetItem.value = instance;
        console.info("xxxxx", detailAssetItem.value)
        if (callback && typeof callback == "function") {
          callback(detailAssetItem.value);
        }
      })
      .catch(err => {
        console.error(err);
        callback(null);
      });
  }


  /** 所有类别字段(只查询一次),关系拓扑会使用 */
  const queryCategoryColumns = () => {
    queryAllPositionSet().then(res => {
      categoryColumns.value = res.data;
    });
  }
};
export default assetDetailMixin;