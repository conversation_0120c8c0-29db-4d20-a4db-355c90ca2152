let defaultImage = {
  label: "默认",
  value: "default",
  // 默认用中间件的图标
  categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/middleware.svg",
  topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/middleware.png",
  topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/middleware-select.png",
};

let images = [
  {
    label: "业务系统",
    value: "bus-system",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/bus-system.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/bus-system.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/bus-system-select.png",
  },
  {
    label: "信息系统",
    value: "info-system",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/info-system.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/bus-system.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/bus-system-select.png",
  },
  {
    label: "中间件",
    value: "middleware",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/middleware.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/middleware.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/middleware-select.png",
  },
  {
    label: "交换机",
    value: "switch",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/switch.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/switch.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/switch-select.png",
  },
  {
    label: "交换机2",
    value: "switch2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/switch2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/switch.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/switch-select.png",
  },
  {
    label: "其它办公设备终端",
    value: "other-office-terminal",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/other-office-terminal.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-office-terminal.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-office-terminal-select.png",
  },
  {
    label: "笔记本",
    value: "notebook",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/notebook.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-office-terminal.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-office-terminal-select.png",
  },
  {
    label: "其它用户终端",
    value: "other-user-terminal",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/other-user-terminal.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-user-terminal.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/other-user-terminal-select.png",
  },
  {
    label: "网络设备",
    value: "network-equipment",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/network-equipment.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-equipment-select.png",
  },
  {
    label: "办公设备",
    value: "office-equipment",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/office-equipment.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment-select.png",
  },
  {
    label: "办公设备2",
    value: "office-equipment2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/office-equipment2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment-select.png",
  },
  {
    label: "台式机",
    value: "office-equipment3",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/office-equipment3.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment-select.png",
  },
  {
    label: "安全设备",
    value: "safety-equipment",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/safety-equipment.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/safety-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/safety-equipment-select.png",
  },
  {
    label: "安全设备2",
    value: "safety-equipment2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/safety-equipment2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/safety-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/safety-equipment-select.png",
  },
  {
    label: "打印机",
    value: "printer",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/printer.svg",
    // 暂时和办公设备图标使用同一组
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/printer.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/printer.png",
  },
  {
    label: "打印机2",
    value: "printer2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/printer2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/office-equipment-select.png",
  },
  {
    label: "数据库",
    value: "database",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/database.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/database.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/database-select.png",
  },
  {
    label: "服务端口",
    value: "service-port",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/service-port.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/service-port.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/service-port-select.png",
  },
  {
    label: "物理服务器",
    value: "physical-server",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/physical-server.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/physical-server.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/physical-server-select.png",
  },
  {
    label: "虚拟服务器",
    value: "virtual-server",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/virtual-server.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/virtual-server.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/virtual-server-select.png",
  },
  {
    label: "虚拟服务器2",
    value: "virtual-server2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/virtual-server2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/virtual-server.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/virtual-server-select.png",
  },
  {
    label: "线路",
    value: "line",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/line.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/line.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/line-select.png",
  },
  {
    label: "链路",
    value: "link",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/link.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/link.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/link-select.png",
  },
  {
    label: "网络端口",
    value: "network-port",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/network-port.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-port.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-port-select.png",
  },
  {
    label: "路由器",
    value: "router",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/router.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router-select.png",
  },
  {
    label: "路由器2",
    value: "router2",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/router2.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router-select.png",
  },
  {
    label: "无线设备",
    value: "router3",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/router3.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/router-select.png",
  },
  {
    label: "通用软件",
    value: "software",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/software.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/software.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/software-select.png",
  },
  {
    label: "平板PAD",
    value: "pad",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/pad.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/pad.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/pad-select.png",
  },
  {
    label: "手机",
    value: "telphone",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/telphone.svg",
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/telphone.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/telphone-select.png",
  },
  {
    label: "wifi",
    value: "wifi",
    categoryImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/small/wifi.svg",
    // 使用网络设备topo图
    topoImage: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-equipment.png",
    topoImageSelect: "/src/views/modules/eam/asseAudit/unknownAssetAudit/images/topo/network-equipment-select.png",
  },
];

const imagesMap = {};
for (let image of images) {
  let { value } = image;
  imagesMap[value] = image;
}

export default images;

/** 获取图片信息 */
export const getImageInfo = value => {
  let imageInfo = imagesMap[value];
  if (!imageInfo) {
    console.warn(`type of value ${value} is not exist `);
  }
  return imageInfo || defaultImage;
};

/** 获取小图标 */
export const getCategoryImage = value => {
  console.log("export const getCategoryImage = value =>", value);
  return getImageInfo(value).categoryImage;
};

/** 获取拓扑图标 */
export const getTopoImage = value => {
  return getImageInfo(value).topoImage;
};

/** 获取拓扑图标选中 */
export const getTopoImageSelect = value => {
  return getImageInfo(value).topoImageSelect;
};
