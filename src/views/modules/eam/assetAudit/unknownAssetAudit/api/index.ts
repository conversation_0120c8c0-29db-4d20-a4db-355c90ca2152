import { http as query } from "@/utils/http";
const basePath = "/ipplan/";
const ipPlanBase = "/rest-proxy/";
const apiList = {
  /**
   * 查询条件
   * */
  conditionList(params) {
    return query.get("/eam-core/zcgl-common/" + params, {});
  },
  /**
   * 表头
   * */
  tableTable(type, params) {
    return query.postJson(ipPlanBase + type, params);
  },
  /**
   * 表格信息
   * */
  tableList(params) {
    return query.postJson(ipPlanBase + params, {});
  },
  /**
   * 未知资产
   * */
  //未知资产发现
  queryUnknowAssetDisposeCount() {
    return query.get(ipPlanBase + "unknow/queryUnknowAssetDisposeCount");
  },
  //未知资产归属单位统计
  queryUnknowAssetZoneGroup() {
    return query.get(ipPlanBase + "unknow/queryUnknowAssetZoneGroup");
  },
  //未知资产发现来源统计
  queryUnknowAssetSourceGroup() {
    return query.get(ipPlanBase + "unknow/queryUnknowAssetSourceGroup");
  },
  //导出
  exportUnknowAssetTable(params) {
    return query.postBlobWithJson("/eam-core/zcgl-common/unknow/exportUnknowAssetTable", params);
  },
  exportUnknowAssetImportResult(saveUrl) {
    return query.postBlobWithJson("/eam-core/zcgl-common" + saveUrl);
  },
  //  立即扫描 /eam-core/zcgl-common/runSopUnknowColl
  runSopUnknowColl() {
    return query.get("/eam-core/zcgl-common/runSopUnknowColl");
  },


  /**
   * 僵尸资产
   * */
  //僵尸资产发现
  queryCorpseCount() {
    return query.get(ipPlanBase + "corpse/queryCorpseCount");
  },
  //僵尸资产类别corpse/queryCorpsePie
  queryCorpsePie() {
    return query.get(ipPlanBase + "corpse/queryCorpsePie");
  },
  //  TOP3
  queryCorpseLastOutlineTopThree() {
    return query.get(ipPlanBase + "corpse/queryCorpseLastOutlineTopThree");
  },
  //  删除

  clearCorpseById(params) {
    return query.postJson(ipPlanBase + "corpse/clearCorpseById", params);
  },
  //导出
  exportCorpse(params) {
    return query.postBlobWithJson("/eam-core/zcgl-common/corpse/exportCorpse", params);
  },


  // 入库保存
  saveIns(params: any){
    return query.postJson<any>(`eam-core/zcgl-common/zsjUnknow/saveDataSopUnknow`, params);
  }
};
export default apiList;
