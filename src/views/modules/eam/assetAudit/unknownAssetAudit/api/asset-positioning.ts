import { http as axios } from "@/utils/http";

const basePath = "rest-proxy";
const corePath = "eam-core/zcgl-common"

let mockMode = false;
let mockMode1 = false;

/**
 * 1.获取资产大类列表
 * @returns {*}
 */
export const queryLabelByUseModel = () => {
  if (!mockMode) {
    return axios.get(`${basePath}/assetPosition/label/queryLabelByUseModel`);
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: [
          { label: "办公电脑", value: "1", groupId: "", groupName: "" },
          { label: "服务器", value: "2" },
          { label: "网络设备", value: "3" },
          { label: "安全设备", value: "4" },
          { label: "外网办公电脑", value: "5" },
          { label: "外网服务器", value: "6" },
          { label: "核心网络设备", value: "7" },
          { label: "纳入监控设备", value: "8" },
          { label: "更多1", value: "9" },
          { label: "更多2", value: "10" },
          { label: "更多3", value: "11" },
          { label: "更多4", value: "12" },
          { label: "更多5", value: "13" },
          { label: "更多6", value: "14" },
        ],
      });
    }, 200);
  });
};

/***
 * 2.获取按小类分组的表单字段集合（资产首页表单布局使用）
 * 全局只查询一遍（刷新页面重新查询）
 *
 */
export const queryAllPositionSet = () => {
  if (!mockMode) {
    return axios.get(`${basePath}/assetPosition/queryAllPositionSet`);
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          14: {
            category_name: "摄像机",
            category_id: "14",
            columns: [
              {
                field: "name",
                name: "资产名称",
              },
              {
                field: "enableTime",
                name: "启用时间",
              },
              {
                field: "remarks",
                name: "备注",
              },
              {
                field: "dataSource",
                name: "数据来源",
              },
              {
                field: "tenant_code__label",
                name: "租户",
              },
              {
                field: "ipAddress",
                name: "IP地址",
              },
              {
                field: "state",
                name: "状态",
              },
              {
                field: "createTime",
                name: "创建时间",
              },
            ],
            position_image: "el-icon-platform-eleme",
            topo_image: "el-icon-eleme",
            portType: "1",
          },
          25: {
            category_name: "交换机",
            category_id: "25",
            columns: [
              {
                field: "name",
                name: "资产名称",
              },
              {
                field: "ipAddress",
                name: "IP地址",
              },
              {
                field: "macAddress",
                name: "MAC地址",
              },
              {
                field: "zoneName__label",
                name: "行政区划",
              },
              {
                field: "assetCompany",
                name: "资产厂商",
              },
              {
                field: "assetSpec",
                name: "资产型号",
              },
              {
                field: "assetVersion",
                name: "资产版本",
              },
              {
                field: "osType",
                name: "操作系统",
              },
            ],
            position_image: "el-icon-platform-eleme",
            topo_image: "el-icon-eleme",
            isShowPort: true,
            /** portType = 1 服务端口；portType = 2 网络端口；portType = 3 不显示   */
            portType: "1",
          },
        },
      });
    }, 200);
  });
};

/***
 * 3.查询资产列表接口
 *
 */
export const queryAssetPositionData = condition => {
  if (!mockMode) {
    return axios.post(
      `${basePath}/assetPosition/queryAssetPositionData`,
      condition,
      {
        headers: { "Content-Type": "application/json" },
      }
    );
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          total: 10000,
          use: 490,
          rows: Array.from({ length: 14 }, (_, index) => {
            return {
              id: index + 1,
              name: "住院楼检验科 - 检验设备（10.2.3.45）",
              category_id: "25",
              icon: "el-icon-apple",
              model: {},
            };
          }),
        },
      });
    }, 200);
  });
};

/**
 * 3.查询资产详情-tab页基本信息
 *
 * @param params
 * @returns {Promise<unknown>|*}
 */
export const queryDetailBase = params => {
  if (!mockMode) {
    return axios.postJson(
      `${basePath}/assetPosition/queryInstancePropData`,
      params
    );
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: Array.from({ length: 5 }, () => {
          return {
            groupName: "公共属性",
            children: [
              {
                code: "remarks",
                name: "备注",
                value: "1",
              },
              {
                code: "assetSpec",
                name: "资产型号",
                value: "2",
              },
              {
                code: "deviceName",
                name: "设备名称",
                value: "3",
              },
              {
                code: "userName",
                name: "用户名",
                value: "4",
              },
            ],
          };
        }),
      });
    }, 200);
  });
};

/**
 * 4、查询资产详情-tab页变更追踪
 *
 * @param params
 * @returns {Promise<unknown>|*}
 */
export const queryDetailChangeTrack = params => {
  if (!mockMode) {
    return axios.postJson(
      `${basePath}/assetPosition/queryAssetChangeByDate`,
      params
    );
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: [
          {
            date: "2022-1-18",
            data: [
              {
                data: [
                  {
                    type: "1",
                    title: "使用人【admin】将资产厂商由【华为】变更成【华为】",
                  },
                  {
                    type: "1",
                    title:
                      "使用人【admin】将资产责任人联系电话由【15715484584】变更成【15715484584】",
                  },
                  {
                    type: "1",
                    title:
                      "使用人【admin】将资产CUID由【64AB79F05FF329752558EF293DE16CBA】变更成【64AB79F05FF329752558EF293DE16CBA】",
                  },
                  {
                    type: "2",
                    title: "xxxx变更了关系",
                  },
                ],
                time: "12:00:33",
              },
              {
                data: [
                  {
                    type: "1",
                    title:
                      "使用人【admin】将IP地址由【*************】变更成【*************】",
                  },
                  {
                    type: "1",
                    title:
                      "使用人【admin】将MAC地址由【BC-CC-FC-AB-TE-03】变更成【BC-CC-FC-AB-TE-03】",
                  },
                  {
                    type: "1",
                    title:
                      "使用人【admin】将行政区划由【邯郸,】变更成【邯郸,】",
                  },
                ],
                time: "12:00:31",
              },
            ],
          },
        ],
      });
    }, 200);
  });
};

/**
 * 5、查询资产详情-tab页拓扑关系
 *
 * @param params
 * @returns {Promise<unknown>|*}
 */
export const queryDetailRelations = params => {
  if (!mockMode) {
    return axios.postJson(
      `${basePath}/assetPosition/queryTopoRelationData`,
      params
    );
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: Array.from({ length: 10 }, (_, index) => {
          return {
            icon: "",
            name: "**********",
            groupLabel: "交换机",
            groupValue: 25,
            relation: "fatherAndSon",
            children: [
              {
                assetImportantLevel__label: "高",
                updated_time: "2021-12-21 16:42:22",
                assetImportantLevel: "1",
                created_user_id: 1,
                zoneName__tree_url: "西藏",
                deviceDesc: "",
                deviceUsage: "画像选中",
                portIndex: "",
                instanceState: "new",
                major: "9wko4ZlkAp0jxqu7Tcz",
                fromAssetID: "",
                assetDutyPhone: "15222522352",
                confirmStatus: "unconfirm",
                id: 4217,
                zoneName__label: "西藏",
                state: "新建",
                assetCompany: "SEI有哪啦",
                onlineEndTime: "2021-10-18 07:12:00",
                created_time: "2021-11-08 01:53:49",
                assetBelong: "2",
                updatedTime: null,
                cuid: "57CB2591134C4747D86FF767311C0DE0",
                ipAddress: "**********",
                enableTime: null,
                refRack: "",
                refRoom: "IDC专业",
                data_source: "人工录入",
                assetSpec: "V255-D3",
                macAddress: "AA-BB-CC-DD-EE-FF",
                instance_id: "4217",
                refVlan: "",
                scrapped_ins_time: "2021-11-08 01:50:54",
                name: "**********",
                assetDuty: "zdz",
                ipLong: "19148617",
                dataSource: "人工录入",
                code: "RESOURCE_INSTANCE_s4b6_1636336254841",
                fun_area: "",
                user_name: "",
                refRoom__label: "IDC专业",
                confirmStatus__label: "未确认",
                category_id: "25",
                deleted_flag: 0,
                ipV6Address: "",
                osType: "",
                refRack__label: "",
                zoneName: "GDYPrp4yRQ1iIJlOyg0",
                enable_time: null,
                colTime: null,
                creator: "dev",
                phyPosition: "华夏东大洋孤单北纬053",
                manager: "",
                onlineStartTime: "2021-10-17 15:42:27",
                refDept: "",
                disable_ins_time: "2021-11-08 01:50:54",
                major__label: "顺德分局",
                created_user_name: "dev",
                assetBelong__label: "网管",
                confirmDate: null,
                createTime: "2021-11-08 09:53:51",
                manage_area: "",
                assetVersion: "version3.0",
                assetDuty__label: "张大嘴",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "浙江",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "3WPBoI9irjhYbGDrUdM",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 271,
                zoneName__label: "浙江",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:31",
                cuid: "CF325D355D803D940BED5DC13C2FB986",
                ipAddress: "*************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "dd",
                macAddress: "AD-CD-CA-BB-BA-ED",
                instance_id: "271",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232248852",
                name: "地瓜地瓜WHSAEGW39BHW",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_cf9ab64a-26e3-45c6-92bf-0aca6623a18f",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "LvUa1G39SEz1j7xHW5u",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "三水分局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:33",
                assetImportantLevel: "2",
                zoneName__tree_url: "市局",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "gNiucvzpoCD2P1U4Qcn",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 272,
                zoneName__label: "市局",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:32",
                cuid: "9AA2D6C935E9227B9069A4F8E01A8752",
                ipAddress: "***************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "ee",
                macAddress: "AD-CD-CA-BB-BA-EA",
                instance_id: "272",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232290283",
                name: "地瓜WH-PS-SW-0032-ER",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_fa4770b3-fb59-4591-8d04-9623932a9b57",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "gNiucvzpoCD2P1U4Qcn",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "市局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "西藏",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "2pNTfbe6NTAwbNjwmmV",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 273,
                zoneName__label: "西藏",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:31",
                cuid: "AF966B5756559B1461602B120A80E153",
                ipAddress: "*************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "ff",
                macAddress: "AD-CD-CA-BB-BA-EB",
                instance_id: "273",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232248851",
                name: "地瓜地瓜WHSAEGW32BHW",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_5f8e8c39-04a1-4995-bbbd-8dbdb63d0042",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "GDYPrp4yRQ1iIJlOyg0",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "高明分局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "市局",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "gNiucvzpoCD2P1U4Qcn",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 274,
                zoneName__label: "市局",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:30",
                cuid: "1062374AEC3A738C74AEC7BDB3C69606",
                ipAddress: "***************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "gg",
                macAddress: "BA-CC-FC-AB-TE-DC",
                instance_id: "274",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232295082",
                name: "地瓜地瓜地瓜GSM小基站网关-FW04",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_1201de09-757e-42ac-a4d3-8e5591a3f420",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "gNiucvzpoCD2P1U4Qcn",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "市局",
                assetBelong__label: "",
                confirmDate: "2021-08-20 20:57:00",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:37",
                assetImportantLevel: "2",
                zoneName__tree_url: "市局",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "gNiucvzpoCD2P1U4Qcn",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 278,
                zoneName__label: "市局",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:37",
                cuid: "615B0EDFED5E0B261B20B76B1E2B8F46",
                ipAddress: "*************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "hh",
                macAddress: "AD-CD-CA-BB-BA-DE",
                instance_id: "278",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232246330",
                name: "dt-lte-s5500-2",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_855ebfc2-649f-4a26-9d2c-d0fcee164547",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "gNiucvzpoCD2P1U4Qcn",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "市局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "市局",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "gNiucvzpoCD2P1U4Qcn",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 279,
                zoneName__label: "市局",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:31",
                cuid: "8ACB76273851590CEC2B7544E667E60F",
                ipAddress: "***************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "aa",
                macAddress: "AD-CD-CA-BB-BA-DA",
                instance_id: "279",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232292492",
                name: "WH-PS-SW-0003-ZX",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_1f4a99f6-eef9-4707-97c4-5ddd0ba39f5f",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "gNiucvzpoCD2P1U4Qcn",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "市局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "市局",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "gNiucvzpoCD2P1U4Qcn",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 281,
                zoneName__label: "市局",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:31",
                cuid: "7488AB3878B99CA4336352E8DFBAB715",
                ipAddress: "**************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "bb",
                macAddress: "AD-CD-CA-BB-BA-DD",
                instance_id: "281",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232249249",
                name: "WHSAEGW81BHW-TOR",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_8d839015-92cf-4322-b737-d83c4b7e2f6c",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "gNiucvzpoCD2P1U4Qcn",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "市局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
              {
                assetImportantLevel__label: "中",
                updated_time: "2022-01-13 00:01:31",
                assetImportantLevel: "2",
                zoneName__tree_url: "贵州",
                deviceDesc: "",
                deviceUsage: "",
                portIndex: "",
                major: "2pNTfbe6NTAwbNjwmmV",
                fromAssetID: "",
                assetDutyPhone: "",
                confirmStatus: "confirmd",
                id: 267,
                zoneName__label: "贵州",
                state: "启用",
                assetCompany: "华为",
                onlineEndTime: "2021-10-05 00:00:00",
                assetBelong: "",
                updatedTime: "2022-01-13 00:01:31",
                cuid: "C7B51EF6EC1AA9EEDEC511AD569B2592",
                ipAddress: "*************",
                enableTime: "2021-08-05 14:51:16",
                refRack: "",
                refRoom: "",
                assetSpec: "bb",
                macAddress: "AA-CD-BA-CA-AE-CD",
                instance_id: "267",
                refVlan: "",
                assetDuty: "jkong",
                ipLong: "3232236491",
                name: "WLAN-荆州-接入交换机Quidway- S2403H",
                dataSource: "IP登记,人工录入,自动生成",
                code: "RESOURCE_INSTANCE_a2e4e646-6239-4a68-8b99-555b02b4fe70",
                refRoom__label: "",
                confirmStatus__label: "已确认",
                category_id: 25,
                ipV6Address: "",
                osType: "windows",
                refRack__label: "",
                newIp: "",
                zoneName: "DojKglvaDA8ir6pVguF",
                colTime: null,
                creator: "admin",
                phyPosition: "",
                onlineStartTime: "2021-10-14 15:42:27",
                refDept: "",
                major__label: "高明分局",
                assetBelong__label: "",
                createTime: "2021-08-05 14:51:16",
                assetVersion: "",
                assetDuty__label: "净空",
                tenant_code__label: "",
                remarks: "",
                tenant_code: "",
              },
            ],
          };
        }),
      });
    }, 200);
  });
};

/**
 * 6、查询资产详情-端口信息
 *
 * @param params
 * @returns {Promise<unknown>|*}
 */
export const queryDetailPort = params => {
  if (!mockMode) {
    return axios.postJson(
      `${basePath}/assetPosition/queryServicePortData`,
      params
    );
  }

  /** 模拟接口 */
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: [
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "22／tcp",
            portId: "5226",
            serviceName: null,
            highRisk: "是",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "80／tcp",
            portId: "5227",
            serviceName: null,
            highRisk: "是",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "3306／tcp",
            portId: "5231",
            serviceName: null,
            highRisk: "是",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "111／tcp",
            portId: "5228",
            serviceName: null,
            highRisk: "是",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "10051／tcp",
            portId: "5232",
            serviceName: null,
            highRisk: "否",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "10050／tcp",
            portId: "5229",
            serviceName: null,
            highRisk: "否",
          },
          {
            serviceVersion: null,
            changeList: [],
            portStatus: "未知",
            portName: "10052／tcp",
            portId: "5230",
            serviceName: null,
            highRisk: "否",
          },
        ],
      });
    }, 200);
  });
};
/**
 * 7、查询资产单位，责任人和告警标签列表
 *
 * @param params
 * @return {Promise<unknow>|*}
 */
export const queryAssetZoneDutyTags = params =>{
  if (!mockMode1) {
    return axios.postJson(
      `${basePath}/assetPosition/queryEventTagsByIp`,
      params
    );
  }
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data: {
          zoneName: '东莞市中医院',
          assetDuty: '张三',
          eventTags: ['恶意软件', '弱口令', '远程攻击', '网络攻击']
        }
      });
    },200);
  });
}

