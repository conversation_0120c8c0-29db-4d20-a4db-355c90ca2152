<template>
    <div>
        <div style="width: 100%;" class="colBetween">
            <el-card class="cardHeight" style="width: 28%;">
                <div slot="header" class="slotHeader">
                    <img style="display: inline-block;" class="titleIcon" src="./assets/images/未知资产.png" alt="">
                    <span class="titleSize">未知资产发现</span>
                </div>
                <div style="width: 100%;height: 265px;display: flex;flex-direction: column;justify-content: center;">
                    <div>
                        <div style="font-size: 18px;font-weight: 600">累计已发现未知资产数</div>
                        <span style="font-size: 32px;font-weight: bold">{{ state.UnknowAssetDisposeCount['totalCount']
                            }}</span>
                    </div>
                    <div style="margin: 30px 0;width: 100%;">
                        <div style="width: 100%;display: flex">
                            <div style="width: 50%;height: 16px;margin-right: 2px" class="green"></div>
                            <div style="width: 50%;height: 16px" class="blue"></div>
                        </div>
                    </div>
                    <div>
                        <div style="width: 100%;display: flex">
                            <div style="width: 50%;height: 16px;margin-right: 2px">
                                <div>
                                    <span
                                        style="display: inline-block;width: 8px;height: 8px;background:#55C8AB "></span>
                                    <span style="padding-left: 5px">已处置</span>
                                </div>
                                <div style="font-size: 18px;font-weight: bold;color: #55C8AB;padding-left: 12px">
                                    {{ state.UnknowAssetDisposeCount['disposeCount'] }}
                                </div>
                            </div>
                            <div style="width: 50%;height: 16px">
                                <div>
                                    <span
                                        style="display: inline-block;width: 8px;height: 8px;background:#1890FF "></span>
                                    <span style="padding-left: 5px">未处置</span>
                                </div>
                                <div style="font-size: 18px;font-weight: bold;color: #1890FF;padding-left: 12px">
                                    {{ state.UnknowAssetDisposeCount['nodisposeCount'] }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-card>
            <el-card class="cardHeight" style="width: 42%;">
                <div slot="header" class="slotHeader">
                    <img style="display: inline-block;" class="titleIcon" src="./assets/images/未知资产发现来源.png" alt="">
                    <span class="titleSize">未知资产归属单位统计</span>
                </div>
                <div style="width: 100%;height: 265px;display: flex;position: relative">
                    <div style="width: 55%;height: 100%;position: absolute;">
                        <compCharts :option="circleImportantChart" :height-charts="'100%'"></compCharts>
                    </div>
                    <div style="width: 65%;height: 100%;position: absolute;transform: translateX(200px)">
                        <compCharts :option="barImportantChart" :height-charts="'100%'"></compCharts>
                    </div>
                </div>
            </el-card>
            <el-card class="cardHeight" style="width: 28%;">
                <div slot="header" class="slotHeader">
                    <img style="display: inline-block;" class="titleIcon" src="./assets/images/资产归属.png" alt="">
                    <span class="titleSize">未知资产发现来源统计</span>
                </div>
                <div style="width: 100%;height: 265px;">
                    <compCharts :option="barTypeChart" :height-charts="'100%'"></compCharts>
                </div>
            </el-card>
        </div>
        <el-card style="margin-top: 12px">
            <el-row>
                <el-col>
                    <im-search-form :fuzzyable="true" class="search-form" select-width="210px"
                        :search-style="{ width: '70%' }" :columns="columns" :options="{ placeholder: '请输入 ${name}' }"
                        fit :col-num-per-row="4" label-width="120px" @on-update-row-num="val => (conditionRowNum = val)"
                        @on-expanded-change="val => (expandConditions = val)" @on-search="onSearch">
                    </im-search-form>
                    <!-- <im-search-form class="search-form" select-width="210px" :search-style="{ width: '100%' }"
                        :columns="columns" :options="{ placeholder: '请输入 ${name}' }" fit :col-num-per-row="3"
                        label-width="120px" @on-update-row-num="val => (conditionRowNum = val)"
                        @on-expanded-change="val => (expandConditions = val)" @on-search="onSearch">
                    </im-search-form> -->
                </el-col>
                <el-col style="padding: 10px 0">
                    <el-radio-group v-model="isDisposal" size="mini" @change="changeRadio">
                        <el-radio-button :label="0">未处置</el-radio-button>
                        <el-radio-button :label="1">已处置</el-radio-button>
                    </el-radio-group>
                    <div style="float: right;padding-top:5px;">
                        <el-button type="text" style="float: right;padding: 0 10px;transform: translateY(10px)"
                            icon="el-icon-download" @click="exportTable">
                            导出
                        </el-button>
                        <el-button v-show="isDisposal == 0" type="text"
                            style="float: right;padding: 0 10px;transform: translateY(10px)" icon="el-icon-upload"
                            @click="importTable">批量导入
                        </el-button>

                        <el-button v-if="userInfo.maxRoleLevel == '0' || userInfo.maxRoleLevel == '1'" title="入库过滤器配置"
                            type="text" icon="el-icon-s-tools"
                            style="float: right;padding: 0 10px;transform: translateY(10px)"
                            @click="setUnknowFilter">入库过滤器配置</el-button>

                        <el-button v-show="isDisposal == 0" :disabled="loading" type="text"
                            style="float: right;padding: 0 10px;transform: translateY(10px)" icon="el-icon-data-line"
                            @click="verification">立即验证
                        </el-button>
                        <el-button v-show="isDisposal == 0" :disabled="loading" type="text"
                            style="float: right;padding-top:0px;padding-right:10px;transform: translateY(10px)"
                            icon="el-icon-data-line" @click="yctc" v-loading="tcLoading">远程探测
                        </el-button>
                    </div>
                </el-col>
                <el-col>
                    <im-table :loading="homeLoading" @on-reload="homeHandlePageCurrentChange"
                        @on-page-current-change="homeHandlePageCurrentChange"
                        @on-pagesize-change="homeHandlePageCurrentChange" :data="tableData" ref="columnTable"
                        method="POST" request-data-root="data" read-property="rows" :pagination="homePagination"
                        :columns="tableColums" :url="tableUrl" id-key="ipAddress" @on-load-before="onLoadBefore"
                        enable-hide-columns columns-sortable element-loading-text="资产扫描中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.8)">
                        <template v-slot:operation="scope">
                            <el-button v-show="isDisposal == 0" :disabled="isDisposal != 0" type="text"
                                @click="warehousing(scope.row)">
                                入库
                            </el-button>
                            <el-button v-show="isDisposal != 0" type="text" @click="seewarehousing(scope.row)">
                                查看
                            </el-button>
                        </template>
                        <template v-slot:ipAddress="scope">
                            <el-button type="text" @click="showDrawer(scope.row)">{{ scope.row.ipAddress }}</el-button>
                        </template>
                    </im-table>
                </el-col>
            </el-row>
        </el-card>

        <!--入库    -->
        <el-drawer v-model="warehousingdrawer" :modal="true" direction="rtl" class="customStyleDialog"
            :wrapperClosable="false" size="66%">
            <div slot="title">
                <div>
                    <span>未知资产详情</span>
                    <el-button style="float: right;margin-right: 10px" @click="warehousingdrawer = false">关闭</el-button>
                    <el-button style="float: right;margin-right: 10px" @click="save">确认入库</el-button>
                </div>
            </div>
            <div>
                <el-form style="padding-left: 20px;" v-if="warehousingdrawer">
                    <el-form-item style="margin-top: 16px;" label="资产类型">
                        <el-tree-select v-model="state.categoryId" :data="categoryTreeList" node-key="id"
                            @current-change="treeChange" title-key="name" check-strictly :render-after-expand="false"
                            style="width: 240px" />
                        <!-- <el-select-tree :data="categoryTreeList" style="width: 300px" fixed-position only-leaf-select
                            :maxContentHeight="265" v-model="categoryId" parent-key="parentId" @on-change="treeChange"
                            id-key="id" title-key="name">
                        </el-select-tree> -->
                    </el-form-item>
                </el-form>
            </div>
            <assetBaseInfo ref="assetBaseInfoRef" :editType="'add'" :categoryId="categoryId"
                :baseInfoAllEdit="baseInfoAllEditData" :allBaseInfoAllEdit="allBaseInfoAllEdit" />
            <!-- <assetBaseInfo v-model:saveButtonLoading="saveButtonLoading" :infoTitle="props.sourceInfo.infoTitle"
                @init-instance-data="initInstanceData" :categoryId="props.sourceInfo.categoryId"
                :isLoading="assetBaseInfoLoading" :instanceId="props.sourceInfo.instanceId" ref="baseInfoRef"
                :editType="props.sourceInfo.editType" :baseInfoAllEdit="baseInfoAllEditData"
                :allBaseInfoAllEdit="allBaseInfoAllEdit" :tabLeftNameArray="tableleftTree"
                @updateInstance="updateInstance" :select-cate="props.sourceInfo.selectCate" @jump-to="jumpTo"
                @source-select="sourceSelect" @query-left-tree="getLeftTree" /> -->
            <!-- {{ (warehousingdrawer && isShow && categoryId)+ '' }}
            <add-property-edit ref="add" v-if="warehousingdrawer && isShow && categoryId" :instanceIdP="0" :row="row"
                type="add" @close="close" @saveSuccess="saveSuccess" :categoryIdP="categoryId"></add-property-edit> -->
        </el-drawer>
        <!--导入-->
        <el-drawer size="45%" v-model="importDrawer" modal
            :before-close="() => { importDrawer = false; showResult = false; resultMsg = ''; resultRule = '' }">

            <div class="title" slot="title">
                <!--            <img class="icon" :src="getCategoryImage(category.categoryIcon)"/>-->
                <span class="label" style="margin-left: 8px;">批量导入</span>
            </div>

            <div class="asset-import" style="padding: 0 20px; height: 99%; position: relative; overflow: hidden;">
                <el-upload ref="uploader" class="asset-upload" :headers="uploadHeaders" :data="extraParams"
                    :on-error="onUploadError" :on-success="onUploadSuccess" :on-progress="onFileProgress" drag
                    :limit="1" accept=".xlsx" action="//" :on-change="uploadTemplate">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text"><em>点击上传文件</em> 或者拖拽上传</div>
                    <div class="upload-desc">只能上传excel文件</div>
                </el-upload>
                <div v-if="showResult">
                    {{ resultMsg }}
                    <el-button type="text" @click="exportResult">导出结果</el-button>
                </div>
            </div>
        </el-drawer>
        <!--    查看-->
        <el-drawer :destroy-on-close="true" size="70%" v-model="assetDetailVisible">
            <asset-detail :visible="assetDetailVisible" :assetItem="detailAssetItem"
                :categoryColumns="categoryColumns"></asset-detail>
        </el-drawer>



        <el-dialog title="未知资产归属单位统计" width="1300px" v-model="showZoneDialog" v-if="showZoneDialog">
            <div style="position:relative">
                <span style="display: inline-block;position:absolute;top:-10px;right:20px;z-index:100">
                    <el-button type="primary" @click="exportZoneUnknow" v-loading="exportLoading">导出</el-button>
                    <el-button type="primary" @click="closeZoneDialog">关闭</el-button>
                </span>

                <el-tabs v-model="selectActive">
                    <el-tab-pane v-for="item in zoneTabs" :label="item.label" :name="item.value">
                        <im-table :ref="'zoneTable' + item.value" method="post" v-if="item.value == selectActive"
                            request-data-root="data" read-property="rows"
                            :pagination="{ total: 'total', currentPage: 'pageNum', pageSize: 'pageSize' }"
                            :columns="zoneUnknowColumns" :url="zoneTableUrls" id-key="id" @sort-change="sortChange"
                            @on-load-before="zoneOnLoadBefore" enable-hide-columns is-save-hidden-state
                            v-loading="false" element-loading-text="资产扫描中" element-loading-spinner="el-icon-loading"
                            element-loading-background="rgba(0, 0, 0, 0.8)">

                        </im-table>
                    </el-tab-pane>
                </el-tabs>
            </div>

        </el-dialog>

        <el-drawer title="入库过滤配置" v-model="unknowFilter" v-if="unknowFilter" :modal="true" direction="rtl"
            :wrapperClosable="false" size="70%">
            <div style="width:90%;margin:0 auto;">
                <el-tabs v-model="unknowTab" @tab-click="changeTabs">
                    <el-tab-pane label="IP范围配置" name="ip">
                        <div style="margin-bottom:10px;">
                            <el-popover placement="left-start" width="300" trigger="hover">
                                <p>
                                    支持单IP，同一网段IP范围、同一网段IP和子网；
                                </p>
                                <p>格式如下</p>
                                <p>单个IP：***********</p>
                                <p>IP范围：***********-*************</p>
                                <p>IP范围：***********-255</p>
                                <!--                <p>IP/子网掩码：***********/*************</p>-->
                                <p>IP/子网掩码：***********/24</p>
                                <template #reference>
                                    <el-button style="margin-left: 5px;margin-right: 6px;margin-bottom: 1px;"
                                        slot="reference" icon="el-icon-question" circle>
                                        <svg style="
                                            width: 16px;
                                            height: 16px;
                                            margin-left: -4px !important;
                                            color: #3e3e3e;
                                        " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                            <path fill="currentColor"
                                                d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592 0-42.944-14.08-76.736-42.24-101.376-28.16-25.344-65.472-37.312-111.232-37.312zm-12.672 406.208a54.272 54.272 0 0 0-38.72 14.784 49.408 49.408 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.848 54.848 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.968 51.968 0 0 0-15.488-38.016 55.936 55.936 0 0 0-39.424-14.784z">
                                            </path>
                                        </svg>
                                    </el-button>
                                </template>
                            </el-popover>
                            <span style="color:red;font-size: 13px;">入库范围配置（只有在配置的范围内的数据才可以入库）</span>
                        </div>
                        <el-row style="margin-bottom: 6px;">
                            <el-button type="primary" @click="addUnknowFilter">新增</el-button>
                            <el-button type="primary" @click="deleteUnknowFilterAll">删除</el-button>
                            <el-button type="primary" @click="exportFilterTemp('1')"
                                v-loading="tempFilterLoading">下载模板</el-button>
                            <el-button type="primary" @click="exportFilterTemp('2')"
                                v-loading="exportFilterLoading">导出</el-button>
                            <div style="display: inline-block;margin:  0 10px">
                                <el-upload ref="uploader" :on-error="onFilterUploadError"
                                    :on-success="onFilterUploadSuccess" :show-file-list="false" :limit="1" :max="1"
                                    accept=".xlsx, .xls" action="/rest/eam-core/zcgl-common/un/importUnknowFilter">
                                    <el-button type="primary">导入</el-button>
                                </el-upload>
                            </div>
                        </el-row>
                        <im-table ref="unknowIpTable" :data="unknowFilterData" read-property="rows"
                            :columns="unknowIpColumn" id-key="id">
                            <template v-slot:ipRange="scope">
                                <el-input type="text" v-model="scope.row.ipRange"
                                    :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true"></el-input>
                            </template>
                            <template v-slot:operator="scope">
                                <el-input type="text" v-model="scope.row.operator"
                                    :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true"></el-input>
                            </template>
                            <template v-slot:state="scope">
                                <el-switch v-model="scope.row.state" active-color="#13ce66" inactive-color="#ff4949"
                                    active-value="1" inactive-value="2"
                                    :disabled="scope.row.edit && scope.row.edit == 'edit' ? false : true">
                                </el-switch>
                            </template>
                            <template v-slot:oper="scope">
                                <el-button type="text" @click="editUnknowFilter(scope.row)">
                                    <span v-if="scope.row.edit && scope.row.edit == 'edit'">保存</span>
                                    <span v-else>编辑</span>
                                </el-button>
                                <el-button type="text" @click="deleteUnknowFilter(scope.row)">
                                    删除
                                </el-button>
                            </template>
                        </im-table>
                    </el-tab-pane>
                    <el-tab-pane label="白名单配置" name="white">
                        <el-row>
                            <el-form style="width: 100%;" :model="state.whiteForm" ref="whiteFormRef" label-width="0px">
                                <el-form-item style="width: 100%;" prop="ranges" :rules="[
                                    {
                                        validator: validateKeyRule,
                                        required: true,
                                        trigger: ['blur', 'change'],
                                    }
                                ]" :class="{ range: iprange1 }">
                                    <el-row style="width: 100%;">
                                        <el-col :span="23">
                                            <el-input style="width: 100%;" type="textarea" :rows="10"
                                                v-model="state.whiteForm.ranges"></el-input>
                                            <span style="color: red;font-size: 12px">{{ iptooltip1 }}</span>
                                        </el-col>
                                        <el-col :span="1">
                                            <el-popover placement="left-start" width="300" trigger="hover">
                                                <p>
                                                    支持单IP，同一网段IP范围、同一网段IP和子网；
                                                </p>
                                                <p>格式如下（一行一个）</p>
                                                <p>单个IP：***********</p>
                                                <p>IP范围：***********-*************</p>
                                                <p>IP范围：***********-255</p>
                                                <!--                <p>IP/子网掩码：***********/*************</p>-->
                                                <p>IP/子网掩码：***********/24</p>
                                                <template #reference>
                                                    <el-button
                                                        style="margin-left: 5px;margin-right: 6px;margin-bottom: 1px;"
                                                        slot="reference" icon="el-icon-question" circle>
                                                        <svg style="
                                                            width: 16px;
                                                            height: 16px;
                                                            margin-left: -4px !important;
                                                            color: #3e3e3e;
                                                        " xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path fill="currentColor"
                                                                d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592 0-42.944-14.08-76.736-42.24-101.376-28.16-25.344-65.472-37.312-111.232-37.312zm-12.672 406.208a54.272 54.272 0 0 0-38.72 14.784 49.408 49.408 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.848 54.848 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.968 51.968 0 0 0-15.488-38.016 55.936 55.936 0 0 0-39.424-14.784z">
                                                            </path>
                                                        </svg>
                                                    </el-button>
                                                </template>
                                            </el-popover>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-form>
                        </el-row>
                        <el-row style="margin-top:10px;">
                            <el-button type="primary" @click="saveUnknowWhite">保存</el-button>
                            <el-button @click="closeDwaver">取消</el-button>
                        </el-row>
                    </el-tab-pane>

                    <el-tab-pane label="数据源配置" name="source">
                        <div v-if="unknowTab == 'source'">
                            <div style="margin-bottom:10px;">
                                <span style="color:red; font-size: 13px;">入库数据源配置（配置数据源下该出现多少次之后才会入库，添加周期配置，
                                    超过本周期的数据将自动清除，如无配置或配置为0，默认一次就会入库，永不会清除）</span>
                            </div>
                            <el-row style="margin-bottom: 6px;">
                                <el-button type="primary" @click="addUnknowSource">新增</el-button>
                                <el-button type="primary" @click="deleteUnknowSourceAll">删除</el-button>
                                <el-button type="primary" @click="exportFilterSourceTemp('1')"
                                    v-loading="tempFilterLoading">下载模板</el-button>
                                <el-button type="primary" @click="exportFilterSourceTemp('2')"
                                    v-loading="exportFilterLoading">导出</el-button>
                                <div style="display: inline-block;margin:  0 10px">
                                    <el-upload ref="sourceUploader" :on-error="onFilterUploadError"
                                        :on-success="onFilterSourceUploadSuccess" :show-file-list="false" :limit="1"
                                        :max="1" accept=".xlsx, .xls"
                                        action="/rest/eam-core/zcgl-common/un/importUnknowFilterSource">
                                        <el-button type="primary">导入</el-button>
                                    </el-upload>
                                </div>
                            </el-row>
                            <im-table ref="unknowSourceTable" :data="unknowSourceData" read-property="rows"
                                :columns="unknowSourceColumn" id-key="id">
                                <template v-slot:dataSource="scope">
                                    <el-input type="text" v-model="scope.row.dataSource"
                                        :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true"></el-input>
                                </template>
                                <template v-slot:sourceCnt="scope">
                                    <el-input-number v-model="scope.row.sourceCnt"
                                        :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true" :min="0"
                                        :step="1"></el-input-number>
                                </template>
                                <template v-slot:sourceTime="scope">
                                    <el-input-number v-model="scope.row.sourceTime"
                                        :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true" :min="0"
                                        :step="1"></el-input-number>
                                </template>
                                <template v-slot:sourceState="scope">
                                    <el-switch v-model="scope.row.sourceState" active-color="#13ce66"
                                        inactive-color="#ff4949" active-value="1" inactive-value="2"
                                        :readonly="scope.row.edit && scope.row.edit == 'edit' ? false : true">
                                    </el-switch>
                                </template>
                                <template v-slot:oper="scope">
                                    <el-button type="text" @click="editUnknowSourceFilter(scope.row)">
                                        <span v-if="scope.row.edit && scope.row.edit == 'edit'">保存</span>
                                        <span v-else>编辑</span>
                                    </el-button>
                                    <el-button type="text" @click="deleteUnknowSourceFilter(scope.row)">
                                        删除
                                    </el-button>
                                </template>
                            </im-table>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </el-drawer>
    </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount } from "vue";
import assetDetailMixin from '@/views/modules/eam/assetAudit/unknownAssetAudit/util/assetDetailMixin';
import apiList from '@/views/modules/eam/assetAudit/unknownAssetAudit/api';
import { http as query } from "@/utils/http";
import { http as axios } from "@/utils/http";
import { http } from "@/utils/http";
import compCharts from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/compCharts.vue";
import assetBaseInfo from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/assetBaseInfo.vue";
import discoveryScopeCom from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/discoveryScope/index.vue";
import AssetDetail from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/AssetDetail.vue";
import { v1 } from 'uuid';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getInstanceByIdApi, queryPropertyEnumByInstanceAxios } from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";

/***
 * 获取按小类分组的表单字段集合（资产首页表单布局使用）
 * 全局只查询一遍（刷新页面重新查询）
 *
 */
const basePath = "rest-proxy";
let mockMode = false;
const queryAllPositionSet = () => {
    if (!mockMode) {
        return axios.get(`${basePath}/assetPosition/queryAllPositionSet`);
    }

    /** 模拟接口 */
    return new Promise(resolve => {
        setTimeout(() => {
            resolve({
                data: {
                    14: {
                        category_name: "摄像机",
                        category_id: "14",
                        columns: [{
                            field: "name",
                            name: "资产名称",
                        },
                        {
                            field: "enableTime",
                            name: "启用时间",
                        },
                        {
                            field: "remarks",
                            name: "备注",
                        },
                        {
                            field: "dataSource",
                            name: "数据来源",
                        },
                        {
                            field: "tenant_code__label",
                            name: "租户",
                        },
                        {
                            field: "ipAddress",
                            name: "IP地址",
                        },
                        {
                            field: "state",
                            name: "状态",
                        },
                        {
                            field: "createTime",
                            name: "创建时间",
                        },
                        ],
                        position_image: "el-icon-platform-eleme",
                        topo_image: "el-icon-eleme",
                        portType: "1",
                    },
                    25: {
                        category_name: "交换机",
                        category_id: "25",
                        columns: [{
                            field: "name",
                            name: "资产名称",
                        },
                        {
                            field: "ipAddress",
                            name: "IP地址",
                        },
                        {
                            field: "macAddress",
                            name: "MAC地址",
                        },
                        {
                            field: "zoneName__label",
                            name: "行政区划",
                        },
                        {
                            field: "assetCompany",
                            name: "资产厂商",
                        },
                        {
                            field: "assetSpec",
                            name: "资产型号",
                        },
                        {
                            field: "assetVersion",
                            name: "资产版本",
                        },
                        {
                            field: "osType",
                            name: "操作系统",
                        },
                        ],
                        position_image: "el-icon-platform-eleme",
                        topo_image: "el-icon-eleme",
                        isShowPort: true,
                        /** portType = 1 服务端口；portType = 2 网络端口；portType = 3 不显示   */
                        portType: "1",
                    },
                },
            });
        }, 200);
    });
};
/**
  * {name, type, model}
  * */
const detailAssetItem = ref({});
const categoryColumns = ref({});
const assetDetailVisible = ref(false);
const assetDetailClose = (val) => {
    assetDetailVisible.value = val;
}
/** 查询*/
const queryForAssetDetail = () => {
    queryCategoryColumns();
}
/** 根据ip查询详情资产信息 */
const queryDetailByIpAddress = async (ipAddress) => {
    let viewLoading = ElMessage({
        message: "正在查询资产实例...",
        // iconClass: "el-message__icon  el-icon-loading",
        duration: 0,
    });
    queryAssetModel(ipAddress, assetItem => {
        viewLoading.close();
        if (assetItem) {
            assetDetailVisible.value = true;
        } else {
            ElMessage.error("未查询到资产信息");
        }
    });
}

/** 根据id查询详情资产信息 */
const queryDetailById = async (instanceId, es_index) => {
    let viewLoading = ElMessage({
        message: "正在查询资产实例...",
        // iconClass: "el-message__icon  el-icon-loading",
        duration: 0,
    });
    queryAssetModelById(instanceId, es_index, assetItem => {
        viewLoading.close();
        if (assetItem) {
            assetDetailVisible.value = true;
        } else {
            ElMessage.error("未查询到资产信息");
        }
    });
}

/** 根据id查询资产模型 */
const queryAssetModelById = (instanceId, es_index, callback) => {
    http
        .post(
            `rest-proxy/assetPosition/queryAssetPositionData`, {
            "_id": instanceId,
            "es_index": es_index
        }, {
            headers: {
                "Content-Type": "application/json",
            },
        }
        )
        .then(res => {
            let assets = res.data?.rows || [];
            let instance = assets[0];
            detailAssetItem.value = instance;
            if (callback && typeof callback == "function") {
                callback(detailAssetItem.value);
            }
        })
        .catch(err => {
            console.error(err);
            callback(null);
        });
}

/** 根据ip查询资产模型 */
const queryAssetModel = (ipAddress, callback) => {
    http
        .get(
            "security-event/sem/assetInfoDetail?ipAddress=" + ipAddress
        )
        .then(res => {
            let assets = res['data'] || [];
            let instance = assets[0];
            detailAssetItem.value = instance;
            console.info("xxxxx", detailAssetItem.value)
            if (callback && typeof callback == "function") {
                callback(detailAssetItem.value);
            }
        })
        .catch(err => {
            console.error(err);
            callback(null);
        });
}


/** 所有类别字段(只查询一次),关系拓扑会使用 */
const queryCategoryColumns = () => {
    queryAllPositionSet().then(res => {
        categoryColumns.value = res['data'];
    });
}
assetDetailMixin();

let validatePass = (rule, value, callback) => {
    let validateRule = /^(([1-9]?\d|1\d{2}|2[0-4]\d|25[0-5])\.){3}([1-9]?\d|1\d{2}|2[0-4]\d|25[0-5])$/
    if (value === '') {
        callback(new Error('请输入ip地址'));
    } else if (!validateRule.test(value)) {
        callback(new Error('请输入正确格式的ip地址!'));
    } else {
        let valid = false
        axios.get(`eam-core/zcgl-common/un/validataIpAddress?ipAddress=${value}`)
            .then(res => {
                valid = res['data'].result == 'error' ? true : false
                if (valid) {
                    callback(new Error('ip地址已存在!'));
                } else {
                    callback();
                }
            })

    }
};
let validatePass1 = (rule, value, callback) => {
    let validateRule = /[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}/
    if (value === '') {
        callback();
    } else {
        if (validateRule.test(value)) {
            callback(new Error('mac地址格式不正确！mac地址格式为00:24:21:19:BD:E4!'));
        } else {
            callback();
        }
    }
};

const userInfo = JSON.parse(sessionStorage.getItem('user-info'));

const state = reactive({
    a: "",
    whiteForm: {
        ranges: ""
    },
    iprange1: false,
    iptooltip1: "",
    areaData: "",
    unknowSourceData: [],
    unknowSourceColumn: [
        { type: "selection" },
        { prop: "dataSource", label: "数据来源", slot: "dataSource", align: "center" },
        { prop: "sourceCnt", label: "重复次数", slot: "sourceCnt", align: "center" },
        { prop: "sourceTime", label: "存活周期（天）", slot: "sourceTime", align: "center" },
        { prop: "sourceState", label: "启用状态", align: "center", slot: "sourceState" },
        { label: "操作", slot: "oper", align: "center" }
    ],
    importFilterLoading: false,
    unknowFilterData: [],
    unknowTab: "ip",
    unknowFilter: false,
    exportLoading: false,
    zoneTabs: [],
    zoneTableUrls: "eam-core/zcgl-common/unknow/queryUnknowZoneDataByTab",
    zoneUnknowColumns: [
        { label: "序号", type: "index" },
        { prop: "zoneName", label: "单位名称" },
        { prop: "assetCount", label: "登记资产数量", sortable: true },
        { prop: "unknowCount", label: "未知资产数量", sortable: true },
        { prop: "noDisUnknowCount", label: "未处理数量", sortable: true },
        { prop: "rate", label: "未知资产占比", sortable: true },
    ],
    unknowIpColumn: [
        { type: "selection" },
        { prop: "ipRange", label: "IP范围", slot: "ipRange", align: "center" },
        { prop: "operator", label: "运营商", slot: "operator", align: "center" },
        { prop: "startIp", label: "起始IP", align: "center" },
        { prop: "endIp", label: "结束IP", align: "center" },
        { prop: "state", label: "启用状态", slot: "state", align: "center" },
        { label: "操作", slot: "oper", align: "center" }
    ],
    selectActive: "",
    showZoneDialog: false,
    filterData: [],
    tcLoading: false,
    showPort: false,
    selectIds: "",
    portNum: 4899,
    discoveryScopeShow: false,
    ipScopeList: "",
    textAreaInput: "",
    showResult: false,
    resultMsg: "",
    resultUrl: "",
    columnsPopoverVisible: false,
    columns: [
        {
            field: "k1",
            name: "名称1",
            component: {
                fuzzyable: true,
                disableSearch: false,
                operator: "",
                type: "Input"
            }
        },
        {
            field: "k2",
            name: "类型",
            component: {
                type: "Select",
                operator: "",
                options: [
                    { label: "石景山", value: "sjs" },
                    { label: "海淀", value: "hd" }
                ],
                props: {
                    multiple: true
                }
            }
        },
        {
            field: "k3",
            name: "时间",
            component: {
                type: "DatePicker",
                options: [],
                operator: "",
                props: {
                    type: "datetimerange",
                    valueFormat: "yyyy-MM-dd HH:mm:dd"
                }
            }
        }
    ],
    selectType: "unknow/queryUnknowAssetTable",
    tableColums: [],
    tableUrl: "",
    circleNotplanChart: null,
    barTypeChart: null,
    circleImportantChart: null,
    barImportantChart: null,
    UnknowAssetDisposeCount: {},
    UnknowAssetZoneGroup: [],
    conditions: [],
    isDisposal: 0,
    params: {},
    row: {},
    warehousingdrawer: false,
    categoryId: null,
    categoryTreeList: [],
    isShow: true,
    uploadHeaders: {},
    extraParams: "",
    importDrawer: false,
    loading: false,
    intervalTime: null,
    filterNumber: 0,
    exportFilterLoading: false,
    tempFilterLoading: false
})

const {
    whiteForm, iprange1, iptooltip1, areaData, unknowSourceData, unknowSourceColumn,
    importFilterLoading, unknowFilterData, unknowTab, unknowFilter, exportLoading,
    zoneTabs, zoneTableUrls, zoneUnknowColumns, unknowIpColumn, selectActive,
    showZoneDialog, filterData, tcLoading, showPort, selectIds, portNum, discoveryScopeShow,
    ipScopeList, textAreaInput, showResult, resultMsg, resultUrl, columnsPopoverVisible,
    columns, selectType, tableColums, tableUrl, circleNotplanChart, barTypeChart,
    circleImportantChart, barImportantChart, UnknowAssetDisposeCount, UnknowAssetZoneGroup,
    conditions, isDisposal, params, row, warehousingdrawer, categoryId, categoryTreeList,
    isShow, uploadHeaders, extraParams, importDrawer, loading, intervalTime, filterNumber,
    exportFilterLoading, tempFilterLoading
} = toRefs(state);

// 动态表需要的数据和请求方法
const baseInfoAllEditData = ref([]);
const allBaseInfoAllEdit = ref([]);
const queryPropertyEnumByInstanceMethod = (params) => {
    const tmpParams = {
        categoryId: categoryId.value,
        isAuth: true,
        property: params
    };
    try {
        queryPropertyEnumByInstanceAxios(tmpParams).then(res => {
            if (params.showType == "comboTree") {
                params.enumArray.push(...buildTree(res?.data?.enumArray, "id", "parentId"));
            } else {
                params.enumArray = res?.data?.enumArray;
            }
        })
    } catch (error) {
    }
}
const getInstanceByIdMethod = async (query) => {
    try {
        // assetBaseInfoLoading.value = true;
        getInstanceByIdApi(query).then(res => {
            const rowsObj = res?.["data"]?.["rowsObj"];
            allBaseInfoAllEdit.value = res?.["data"]
            baseInfoAllEditData.value = rowsObj;
            if (baseInfoAllEditData.value.length > 0) {
                baseInfoAllEditData.value.forEach((item: Array<any>) => {
                    item?.['rows'].forEach((item: Array<any>) => {
                        item?.['colList'].forEach(item => {
                            if (item?.field?.showType == "combobox" || item?.field?.showType == "comboTree") {
                                queryPropertyEnumByInstanceMethod(item.field);
                            }
                            if (item?.field?.showType == "objectTable") {
                                // item.field.column = [];
                                Reflect.set(item.field, 'column', []);
                                Reflect.set(item.field, 'tmpValue', [
                                    {
                                        "macAddress": "AA-BB-BA-BC-CA-DC",
                                        "sortId": 1,
                                        "ipname": "***********49",
                                        "Nameofnetworkcard": "echo33",
                                        "remarks": ""
                                    }
                                ]);
                                Reflect.set(item.field, 'operator', {
                                    label: "操作",
                                    fixed: "right",
                                    width: "166"
                                });
                                if (item.field['value'].length == 0) {
                                    item.field['value'].push({
                                        "macAddress": "",
                                        "sortId": 1,
                                        "ipname": "",
                                        "Nameofnetworkcard": "",
                                        "remarks": ""
                                    })
                                }
                                item.field?.tableList.forEach((item02, index) => {
                                    let isTree = false;
                                    if (item02.showType == 'comboTree') {
                                        isTree = true;
                                    }

                                    item.field.column.push({
                                        // ...item02,
                                        prop: item02.code,
                                        label: item02.name,
                                        // item02.showType
                                        showType: item02.showType,
                                        isAuto: item02.isAuto,
                                        dataType: item02.dataType,
                                        code: item02.code,
                                        property: item02.property,
                                        dataLength: item02.dataLength,
                                        isReEdit: item02.isReEdit,
                                        tableList: isTree ? [...buildTree(item02.enumArray, "id", "parentId")] : item02.enumArray,
                                        name: item02.name,
                                        enumArray: item02.enumArray,
                                        titleDesc: item02.titleDesc,
                                        width: item02.width,
                                        align: item02.align,
                                        ruleData: item02.rule
                                    })
                                    if (index + 1 == item.field.tableList.length) {
                                        item.field.column.push({

                                            prop: "operator",
                                            label: "操作"
                                        })
                                    }

                                })
                            }
                        })
                    })
                })
            }
            //   assetBaseInfoLoading.value = false;
        });
    } catch (error) {
        // assetBaseInfoLoading.value = false;
    }
};







const validateKeyRule = (list, val, callback) => {
    // debugger;
    if (val) {
        //reg1 单个IP
        //reg2 ***********-*************
        //reg3 ***********-255
        //reg5 ***********/24
        let enterReg = /[\n]/g;
        //ipList判断行数
        let ipList = enterReg.exec(val)?.input?.split("\n");
        if (ipList) {
            //多行
            if (ipList.length < 17) {
                for (let i in ipList) {
                    regRuleKey(ipList[i], i);
                }
            } else {
                return;
            }
        } else {
            //单行范围ip类型
            regRuleKey(val);
        }
    } else {
        iprange1.value = false;
        iptooltip1.value = "";
    }
}
const regRuleKey = (val, index?) => {
    //第一位1-254
    let ipSpotRegOne = "(?:25[0-4]|2[0-4]\\d|1?[1-9]\\d?|1[0-9]\\d?)\\.";
    //0-255.
    let ipSpotReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.";
    //0-255
    let ipNoSoptReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)";
    //0-254.
    let ipLessReg = "(?:25[0-4]|2[0-4]\\d|1?\\d\\d?)\\.";
    //链接符为-
    let currentRangeReg = `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\-(?:\\1\\2(?<endThree>${ipSpotReg}))?(?<end>${ipNoSoptReg}))?$`;
    //连接符为/
    let currentMaskReg =
        `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\/(?<end>(?:(?:(?:1[6-9])|(?:2[0-9])|(?:3[0-2])))))?$`;
    // console.log(currentRangeReg);
    let checkRangeReg = new RegExp(currentRangeReg, "g");
    let checkMaskReg = new RegExp(currentMaskReg, "g");
    if (checkRangeReg.exec(val)) {
        // debugger;
        checkRangeReg.lastIndex = 0;
        let data = checkRangeReg.exec(val);
        if (!data.groups.endThree) {
            //没有第endThree时
            iprange1.value = false;
            iptooltip1.value = "";
            return;
        } else {
            if (data.groups.three < data.groups.endThree) {
                iprange1.value = false;
                iptooltip1.value = "";
                return;
            } else if (data.groups.three == data.groups.endThree) {
                if (data.groups.four < data.groups.end) {
                    iprange1.value = false;
                    iptooltip1.value = "";
                    return;
                } else {
                    return;
                }
            } else {
                return;
            }
        }
    } else if (checkMaskReg.exec(val)) {
        //  规则4、5
        checkMaskReg.lastIndex = 0;
        let data = checkMaskReg.exec(val);
        if (data.groups.end <= 32 && data.groups.end >= 0) {
            iptooltip1.value = "";
            iprange1.value = false;
        } else {
            return;
        }

    } else {
        // 没有匹配上
        iprange1.value = true;
        iptooltip1.value = index ? `请检查第${index / 1 + 1}行IP范围` : `请输入正确IP范围`;
    }
}
const closeDwaver = () => {
    unknowFilter.value = false;
}

const saveUnknowWhite = () => {
    let params = {};
    if (!iptooltip1.value) {
        params['ranges'] = state.whiteForm.ranges.split("\n");
        axios
            .postJson(
                "eam-core/zcgl-common/un/saveUnknowFilterWhite",
                params
            ).then(res => {
                if (res['data'].result == 'success') {
                    ElMessage.success("保存成功");
                } else {
                    ElMessage.error("保存失败");
                }
            }).catch(exp => {
                console.log(exp);
            })
    } else {
        ElMessage.error("校验未通过");
    }
}
const changeTabs = (node) => {
    console.log(node);
    if (node.props.name == 'ip') {
        queryUnknowFilter();
    } else if (node.props.name == 'source') {
        queryUnknowSourceFilter();
    } else if (node.props.name == 'white') {
        queryUnknowWhiteFilter();
    }
}
const exportFilterSourceTemp = (type) => {
    if (type == '1') {
        tempFilterLoading.value = true;
    } else {
        exportFilterLoading.value = true;
    }
    axios.get(
        "eam-core/zcgl-common/un/exportUnknowFilterSource?type=" + type, {}, {
        responseType: 'blob'
    }

    ).then(res => {
        if (type == 1) {
            tempFilterLoading.value = false;
            ElMessage.success("模板下载成功");
        } else {
            exportFilterLoading.value = false;
            ElMessage.success("导出成功");
        }

    }).catch(exp => {
        console.log(exp);
        exportFilterLoading.value = false;
        tempFilterLoading.value = false;
    })
}

const deleteUnknowSourceFilter = (row) => {

    ElMessageBox.alert("确认删除吗?", "是否删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            if (row.id) {
                let ids = [];
                ids.push(row.id);
                let params = {};
                params['ids'] = ids;
                axios.postJson(
                    "eam-core/zcgl-common/un/deleteUnknowFilterSource",
                    params
                ).then(res => {
                    if (res['data'].result == 'success') {
                        let tt = -1;
                        for (let i = 0; i < state.unknowSourceData.length; i++) {
                            if (state.unknowSourceData[i].id == row.id) {
                                tt = i;
                                break;
                            }
                        }
                        if (tt >= 0) {
                            state.unknowSourceData.splice(tt, 1);
                        }

                        ElMessage.success("删除成功");
                    } else {
                        ElMessage.error(res['data'].msg);
                    }
                }).catch(exp => {

                })
            } else {
                let tt = -1;
                for (let i = 0; i < state.unknowSourceData.length; i++) {
                    if (state.unknowSourceData[i].cid == row.cid) {
                        tt = i;
                        break;
                    }
                }
                if (tt >= 0) {
                    state.unknowSourceData.splice(tt, 1);
                }
                ElMessage.success("删除成功");
            }
        })

}
const unknowTabClick = (tab, event) => {
    console.log(tab, event);
}
const exportZoneUnknow = () => {
    exportLoading.value = true;
    axios.postBlobWithJson("eam-core/zcgl-common/unknow/exportUnknowZoneDataAllTabs"
    ).then(res => {
        exportLoading.value = false;
        ElMessage.success("导出成功");
    })
}

const editUnknowSourceFilter = (row) => {
    if (!row.dataSource) {
        ElMessage.error("数据来源不能为空");
        return;
    }
    if (row.edit && row.edit == 'edit') {
        let flag = false;
        for (let i = 0; i < unknowSourceData.value.length; i++) {
            let dd = unknowSourceData.value[i];
            if (dd.dataSource == row.dataSource) {
                if (row.id) {
                    if (!dd.id || dd.id != row.id) {
                        ElMessage.error("该数据来源已存在，请修改");
                        flag = true;
                        break;
                    }
                } else {
                    if (row.cid) {
                        if (!dd.cid || dd.cid != row.cid) {
                            ElMessage.error("该数据来源已存在，请修改");
                            flag = true;
                            break;
                        }
                    }
                }
            }
        }
        if (flag) {
            return;
        }
        let params = {};
        if (row.id) {
            params['id'] = row.id;
        }
        params['dataSource'] = row.dataSource;
        if (row.sourceCnt) {
            params['sourceCnt'] = row.sourceCnt;
        }
        if (row.sourceTime) {
            params['sourceTime'] = row.sourceTime;
        }
        params['sourceState'] = row.sourceState;
        axios.postJson("eam-core/zcgl-common/un/saveUnknowFilterSource", params
        ).then(res => {
            if (res['data'].result == 'success') {
                ElMessage.success("保存成功");
                row.edit = '';
                row.id = res['data'].data.id;
                row.sourceCnt = res['data'].data.sourceCnt;
                row.sourceTime = res['data'].data.sourceTime;
            } else {
                ElMessage.error(res['data'].msg);
            }
        }).catch(exp => {
            console.log(exp);
        })
    } else {
        row.edit = "edit";
    }
}
const queryUnknowWhiteFilter = () => {
    axios.get("eam-core/zcgl-common/un/queryUnknowFilterWhiteList").then(res => {
        let tt = [];
        if (res['data']) {
            for (let i = 0; i < res['data'].length; i++) {
                tt.push(res['data'][i].ipRange);
            }
            state.whiteForm.ranges = tt.join("\n");
            console.log(state.whiteForm.ranges);
        }
    }).catch(exp => {
        console.log(exp);
    })
}
const queryUnknowSourceFilter = () => {
    axios.get("eam-core/zcgl-common/un/queryUnknowFilterSources").then(res => {
        unknowSourceData.value = res['data'];
    }).catch(exp => {
        console.log(exp);
    })
}

const addUnknowSource = () => {
    let row = {};
    row['dataSource'] = "";
    row['sourceCnt'] = 0;
    row['sourceTime'] = 0;
    row['sourceState'] = "1";
    row['edit'] = "edit";
    row['cid'] = v1();
    state.unknowSourceData.push(row);

}
const deleteSource = (keys) => {
    ElMessageBox.alert("确认删除吗?", "是否删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            let params = {};
            params['ids'] = keys;
            axios.postJson("eam-core/zcgl-common/un/deleteUnknowFilterSource", params
            ).then(res => {
                if (res['data'].result == 'success') {
                    ElMessage.success("删除成功");
                    queryUnknowSourceFilter();
                } else {
                    ElMessage.error(res['data'].msg);
                }
            }).catch(exp => {
                console.log(exp);
            })
        })
}
const unknowSourceTable = ref(null);
const deleteUnknowSourceAll = () => {
    let keys = unknowSourceTable.value.getSelectionKeys();
    if (keys && keys.length > 0) {
        deleteSource(keys);
    } else {
        ElMessage.error("请选择要删除的数据");
    }
}

const onFilterUploadError = (err, file, fileList) => {
    console.log(err);
    if (err) {
        if (err.message == '服务异常，请重试或联系开发人员!') {
            ElMessage.error("服务连接超时");
        } else {
            let message = err.message;
            try {
                let errData = JSON.parse(message);
                ElMessage.error(errData && errData.msg);
            } catch (error) {
            }
        }
    }
}
const uploader = ref(null);
const onFilterUploadSuccess = (response, file, fileList) => {
    console.log(response);
    if (response.data.result == "success") {
        ElMessage.success("导入成功");
    } else {
        ElMessage.error(response.data.message);
    }
    queryUnknowFilter();
    uploader.value.clearFiles();
}

const sourceUploader = ref(null);
const onFilterSourceUploadSuccess = (response, file, fileList) => {
    console.log(response);
    if (response.data.result == "success") {
        ElMessage.success("导入成功");
    } else {
        ElMessage.error(response.data.message);
    }
    queryUnknowSourceFilter();
    sourceUploader.value.clearFiles();
}

const exportFilterTemp = (type) => {
    if (type == '1') {
        tempFilterLoading.value = true;
    } else {
        exportFilterLoading.value = true;
    }
    axios.get("eam-core/zcgl-common/un/exportUnknowFilterIps?type=" + type,
        {},
        {
            responseType: 'blob',
        }
    ).then(res => {
        if (type == 1) {
            tempFilterLoading.value = false;
            ElMessage.success("模板下载成功");
        } else {
            exportFilterLoading.value = false;
            ElMessage.success("导出成功");
        }

    }).catch(exp => {
        console.log(exp);
        exportFilterLoading.value = false;
        tempFilterLoading.value = false;
    })
}
const queryUnknowFilter = () => {
    axios.get("eam-core/zcgl-common/un/queryUnknowFilterIps",).then(res => {
        unknowFilterData.value = res['data'];
    }).catch(exp => {
        console.log(exp);
    })
}
const setUnknowFilter = () => {
    unknowTab.value = "ip";
    unknowFilter.value = true;
    queryUnknowFilter();
}

const addUnknowFilter = () => {
    let tt = {};
    tt['ipRange'] = "";
    tt['operator'] = "";
    tt['state'] = "1";
    tt['edit'] = "edit";
    tt['cid'] = v1();
    state.unknowFilterData.push(tt);
}
const compareIp = (start, end) => {
    let status = false;
    let startArr = start.split(".");
    let endArr = end.split(".");
    for (let i = 0; i < startArr.length; i++) {
        const startItem = startArr[i];
        const endItem = endArr[i];
        if (parseInt(endItem) > parseInt(startItem)) {
            status = true;
            break;
        } else if (parseInt(endItem) < parseInt(startItem)) {
            status = false;
            break;
        }
    }
    return status;
}
const regRule = (val) => {
    //第一位1-254
    let ipSpotRegOne = "(?:25[0-4]|2[0-4]\\d|1?[1-9]\\d?|1[0-9]\\d?)\\.";
    //0-255.
    let ipSpotReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.";
    //0-255
    let ipNoSoptReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)";
    //0-254.
    let ipLessReg = "(?:25[0-4]|2[0-4]\\d|1?\\d\\d?)\\.";
    //链接符为-
    let reg = /^(?:(?:2[0-4][0-9]\.)|(?:25[0-5]\.)|(?:1[0-9][0-9]\.)|(?:[1-9][0-9]\.)|(?:[0-9]\.)){3}(?:(?:2[0-4][0-9])|(?:25[0-5])|(?:1[0-9][0-9])|(?:[1-9][0-9])|(?:[0-9]))$/;
    let re = new RegExp(reg);
    let currentRangeReg = `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\-(?:\\1\\2(?<endThree>${ipSpotReg}))?(?<end>${ipNoSoptReg}))?$`;

    //连接符为/
    let currentMaskReg =
        `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\/(?<end>(?:(?:(?:1[6-9])|(?:2[0-9])|(?:3[0-2])))))?$`;
    // console.log(currentRangeReg);
    let checkRangeReg = new RegExp(currentRangeReg, "g");
    let checkMaskReg = new RegExp(currentMaskReg, "g");
    let arr = val.split("-");
    if (arr.length == 2 && re.test(arr[0]) && re.test(arr[1])) {
        if (!compareIp(arr[0], arr[1])) {
            return false;
        }
        return true;
    }
    else if (checkRangeReg.exec(val)) {
        //规则2、3
        //判断 后面C段 > 前面C段
        //判断 后面C段 = 前面C段 =>后面D段 > 前面D段
        checkRangeReg.lastIndex = 0;
        let data = checkRangeReg.exec(val);

        if (!data.groups.endThree) {
            //没有第endThree时
            return true;
        } else {
            if (data.groups.three < data.groups.endThree) {
                return true;
            } else if (data.groups.three == data.groups.endThree) {
                if (data.groups.four < data.groups.end) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    } else if (checkMaskReg.exec(val)) {
        //  规则4、5
        checkMaskReg.lastIndex = 0;
        let data = checkMaskReg.exec(val);
        if (data.groups.end <= 32) {
            return true;
        } else {
            return false;
        }
    } else {
        // 没有匹配上
        return false;
    }
}

const editUnknowFilter = (row) => {
    console.log(row);
    if (row.edit && row.edit == 'edit') {
        if (row.ipRange) {
            if (regRule(row.ipRange)) {
                let params = {};
                if (row.id) {
                    params['id'] = row.id;
                }
                params['ipRange'] = row.ipRange;
                params['operator'] = row.operator;
                params['state'] = row.state;
                axios.postJson("eam-core/zcgl-common/un/saveUnknowFilterIp", params
                ).then(res => {
                    if (res['data'].result == 'success') {
                        ElMessage.success("保存成功");
                        row.edit = "";
                        row.id = res['data'].id;
                        row.startIp = res['data'].startIp;
                        row.endIp = res['data'].endIp;
                    }
                }).catch(exp => {
                    console.log(exp);
                })
            } else {
                ElMessage.error("IP范围不满足校验规则")
            }
        } else {
            ElMessage.error("IP范围不能为空");
        }
    } else {
        row.edit = "edit";
    }
}

const unknowIpTable = ref(null);
const deleteUnknowFilterAll = () => {
    let keys = unknowIpTable.value.getSelectionKeys();
    console.log(keys);
    if (!keys || keys.length == 0) {
        ElMessage.error("请选择要删除的数据");
        return;
    }
    ElMessageBox.alert("确认删除吗?", "是否删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            let params = {};
            params['ids'] = keys;
            axios.postJson("eam-core/zcgl-common/un/deleteUnknowFilterIps", params).then(res => {
                if (res['data'].result == 'success') {
                    ElMessage.success("删除成功");
                    queryUnknowFilter();
                } else {
                    ElMessage.error(res['data'].msg);
                }
            }).catch(exp => {
                console.log(exp);
            })
        })
}
const deleteUnknowFilter = (row) => {
    ElMessageBox
    ElMessageBox.alert("确认删除吗?", "是否删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            if (row.id) {
                let ids = [];
                ids.push(row.id);
                let params = {};
                params['ids'] = ids;
                axios.postJson("eam-core/zcgl-common/un/deleteUnknowFilterIps", params).then(res => {
                    if (res['data'].result == 'success') {
                        let tt = -1;
                        for (let i = 0; i < state.unknowFilterData.length; i++) {
                            if (state.unknowFilterData[i].id == row.id) {
                                tt = i;
                                break;
                            }
                        }
                        if (tt >= 0) {
                            state.unknowFilterData.splice(tt, 1);
                        }
                        ElMessage.success("删除成功");
                    } else {
                        ElMessage.error(res['data'].msg);
                    }
                }).catch(exp => {

                })
            } else {
                let tt = -1;
                for (let i = 0; i < state.unknowFilterData.length; i++) {
                    if (state.unknowFilterData[i].cid == row.cid) {
                        tt = i;
                        break;
                    }
                }
                if (tt >= 0) {
                    state.unknowFilterData.splice(tt, 1);
                }
                ElMessage.success("删除成功");
            }
        })

}

// 首页的table
const tableData = ref([]);
const homePagination = reactive({ total: 0, currentPage: 1, pageSize: 10 });
const columnTable = ref(null);
const homeLoading = ref(false);
const columnTableRef = ref({
    load: () => {
        console.log("const columnTableRef = ref")
        homeLoading.value = true;
        apiList.tableTable(state.selectType, { conditions: state.conditions, isImport: state.isDisposal, pageSize: homePagination.pageSize, pageNum: homePagination.currentPage }).then(res => {
            tableColums.value = [
                { type: "selection" },
                { type: "index", width: 50, label: "序号", align: "center" }
            ];
            let resData = res['data'].columns;
            for (let i in resData) {
                if (resData[i].isShow) {
                    let json = {
                        label: resData[i].title,
                        slot: resData[i].key == "ip" ? "ip" : null,
                        prop: resData[i].key,
                        align: "center",
                        showOverflowTooltip: true
                    };
                    tableColums.value.push(json);
                }

            }
            tableColums.value.push(
                { label: "操作", slot: "operation", align: "center" }
            );
            tableData.value = [];
            tableData.value = res['data']['rows'];
            homeLoading.value = false;
            homePagination.total = res['data']['total'];
        });
    }
});
const homeHandlePageCurrentChange = (page: number, pageSize: number) => {
    console.log("page pageSize", page, pageSize);
    homePagination.currentPage = page;
    homePagination.pageSize = pageSize;
    columnTableRef.value.load();
};


// home表格选中行数据

//远程探测
const yctc = () => {
    ElMessageBox.prompt('请输入端口号', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'number',
        inputValue: '4899',
        inputValidator: function (value) {
            console.log(value);
            if (value == undefined || value == null || value == '') {
                return "请输入端口号";
            } else {
                return true;
            }
        }
    }).then(({ value }) => {
        console.log(value);
        tcLoading.value = true;
        let params = {};
        params['port'] = value;
        console.log(...columnTable.value.checkedRows);
        let tt = [];
        const arr = [...columnTable.value.checkedRows];
        arr.forEach(item => {
            tt.push(item['ipAddress'])
        })
        if (tt != undefined && tt != null && tt.length > 0) {
            params['ids'] = tt.join(",");
        }
        query.postJson("/eam-core/zcgl-common/ua/telnetUnknow", params).then(res => {
            tcLoading.value = false;
            showPort.value = false;
            if (res['data'].result == 'success') {

                ElMessage.success("执行成功");
                columnTableRef.value.load();
            } else {
                ElMessage.error(res['data'].msg);
            }
        }).catch(exp => {
            showPort.value = false;
            tcLoading.value = false;
            ElMessage.error("执行失败");
            console.log("执行失败，" + exp.message);
        })
    }).catch((err) => {
        console.log(err);
    });

}

const discoveryScope = ref(null);
//立即扫描
const scanning = () => {
    let ipList = [];
    //扫描范围校验
    let flag = !discoveryScope.value.iprange;
    if (discoveryScope.value.dataForm.ipAddress && flag) {
        loading.value = true;
        for (let item of discoveryScope.value.dataForm.ipAddress.split("\n")) {
            let json = {
                "ipAddress": item
            };
            ipList.push(json);
        }
        console.log(ipList);
        let json = {
            policyName: "未知资产",
            isHistory: "unknownAssets"
        };
        json['operationAssetIpDataVos'] = ipList;
        query.postJson("/eam-collection/operation/probe/cmdb/insertOrUpdateCron", json).then(res => {
            ElMessage.success("开始扫描");
            //this.intervalIPscope();
        });
    }
}

//初始化扫描范围+定时查看扫描状态
const initIpScope = (type) => {
    let json = {
        cronId: userInfo.userName + "-" + "unknownAssets",
        isHistory: "unknownAssets"
    };
    query.postJson("/eam-collection/operation/probe/conf/getOperationAssetCronListByConditionGroupCron", json).then(res => {
        let data = res['data'].rows;
        let flag = true;
        for (let i in data) {
            if (data[i].executionStatus === 1) {
                flag = false;
            } else {
                console.log(flag);
            }
        }
        if (flag) {
            clearInterval(intervalTime.value);
            intervalTime.value = null;
            loading.value = false;
            columnTable.value.loadPage(1);
            if (type !== 'init') {

                ElMessage.success("扫描完成");
            }
        } else {
            loading.value = true;
        }
        let currentipScopeList = data[0]?.ipAddress.replace(/,/g, "\n")
        ipScopeList.value = currentipScopeList ? currentipScopeList : '';
        discoveryScopeShow.value = true;

    }).catch(err => {
        console.log(err);
    });
}
const intervalIPscope = () => {
    intervalTime.value = setInterval(() => {
        initIpScope();
    }, 2500);
}
//立即验证
const verification = () => {
    loading.value = true;
    ElMessage.success("开始验证");
    query.get("eam-core/zcgl-common/nowValidata").then(res => {
        if (res['data'].result == "success") {
            loading.value = false;
        } else {
            ElMessage.success("验证失败");
        }
    });
}

const seewarehousing = (row) => {
    console.log(row);
    queryDetailByIpAddress(row.ipAddress);
}
const warehousing = (row) => {
    row.value = row;
    warehousingdrawer.value = true;
    nextTick(() => {
        categoryId.value = null;
    });
}
const exportResult = () => {
    apiList.exportUnknowAssetImportResult(state.resultUrl).then(() => {
        ElMessage.success("导出成功");
    }).catch(exp => {
        console.log(exp);
    });
}

const treeChange = (val) => {
    console.log(val);
    isShow.value = false;
    nextTick(() => {
        isShow.value = true;
    });
    const tmpObj = {
        id: 0,
        categoryId: val.id,
        isAuth: "true"
    };
    getInstanceByIdMethod(tmpObj);
}
const add = ref(null);
const assetBaseInfoRef = ref(null);
const save = () => {
    assetBaseInfoRef.value.saveInstance();
    // add.value.save("propertyForm");
}
const ipAddress = ref('');
const close = () => {
    warehousingdrawer.value = false;
    ipAddress.value = "";
    // this.query();
    setTimeout(() => {
        columnTableRef.value.load();
        init();
    }, 700);
}



const showDrawer = (row) => {
    console.log(row);
    // this.$refs.drawer.dialog = true;
}
const onSearch = (conditionModels, queryModel) => {
    conditions.value = conditionModels;
    columnTableRef.value.load();
}
const tableParams = ref();
const onLoadBefore = (params, requestConfig) => {
    tableParams.value = {
        conditions: conditions.value,
        isImport: state.isDisposal
    };
    Object.assign(requestConfig, {
        headers: {
            "Content-Type": "application/json"
        }
    });
    Object.assign(params, tableParams.value);
}

const exportTable = () => {
    let json = {
        isImport: state.isDisposal,
        conditions: state.conditions,
        pageNum: homePagination.currentPage,
        pageSize: homePagination.pageSize
    };
    apiList.exportUnknowAssetTable(json).then(
        () => {
            ElMessage.success("开始导出")
        }
    ).catch(err => {
        console.log(err);
    });
}
const importTable = () => {
    importDrawer.value = true;
}
const initBarTypeChart = (x, y) => {
    barTypeChart.value = {
        title: [
            {
                show: x.length > 0 ? false : true,
                text: "暂无数据",
                x: "center",
                y: "center",
                textStyle: {
                    color: "rgba(0,0,0,0.5)",
                    fontWeight: "400"
                }
            }
        ],
        color: ["#0188FB"],
        grid: {
            left: 10,
            right: 40,
            bottom: "10%",
            top: 20,
            containLabel: true
        },
        tooltip: {},
        xAxis: {
            type: "value",
            show: false
        },
        yAxis: {
            type: "category",
            axisLabel: {
                show: true,
                textStyle: {
                    // color: "red"
                }
            },
            splitLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            axisLine: {
                show: false
            },
            data: x
        },
        series: [
            {
                name: "未知资产归属单位统计",
                data: y,
                type: "bar",
                barWidth: 20,
                label: {
                    show: true,
                    position: "right"
                }
            }
        ]
    };
}

const initImportantChart = (data, max, count) => {
    let colors = ["#DD1515", "#F98C3E", "#0188FB", "#17CAE2", "#25C691"].reverse(),
        datas = data,
        maxArr = max
        ;
    console.log(datas);
    circleImportantChart.value = {
        title: [
            {
                show: datas.length > 0 ? false : true,
                text: "暂无数据",
                x: "center",
                y: "center",
                textStyle: {
                    color: "rgba(0,0,0,0.5)",
                    fontWeight: "400"
                }
            }
        ],
        color: colors,
        // tooltip: {},
        legend: {
            show: false,
            orient: "vertical",
            right: "40",
            top: "center"
        },
        series: [
            {
                name: "按资产重要性分布",
                type: "pie",
                radius: ["40%", "60%"],
                center: ["30%", "50%"],
                avoidLabelOverlap: false,

                // roseType: "area",
                itemStyle: {
                    normal: {
                        borderWidth: 2,
                        borderRadius: 180,
                        shadowBlur: 10,
                        shadowColor: "#fff",
                        borderColor: "#fff"
                    }
                },
                labelLine: {
                    show: false
                },
                label: {
                    normal: {
                        rich: {
                            a: {
                                color: "#3C4A54",
                                align: "center",
                                fontSize: 18,
                                fontWeight: "bold",
                            },
                            b: {
                                color: "#aaa",
                                align: "center",
                                fontSize: 12,
                                fontWeight: "200",
                            },
                            c: {
                                fontSize: 12,
                                color: "#303133",
                                align: "center",
                                fontWeight: 100
                            }
                        },
                        formatter: function (params) {
                            return (
                                "{a|" +
                                count +
                                "}" + "{c|}" +
                                "\n\n{b|总数}"
                            );
                        },
                        position: "center",
                        show: true,
                    },
                },
                data: datas
            }
        ]
    };
    barImportantChart.value = {
        title: [
            {
                show: datas.length > 0 ? false : true,
                text: "暂无数据",
                x: "center",
                y: "center",
                textStyle: {
                    color: "rgba(0,0,0,0.5)",
                    fontWeight: "400"
                }
            }
        ],
        // tooltip: {
        //   trigger: "axis",
        //   axisPointer: {
        //     type: "shadow"
        //   }
        // },
        legend: {
            show: false
        },
        grid: {
            left: 20,
            right: "5%",
            bottom: 40,
            top: 40,
            containLabel: true
        },
        xAxis: {
            show: false,
            type: "value"
        },
        yAxis: [
            {
                type: "category",
                inverse: true,
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisPointer: {
                    label: {
                        show: true,
                        margin: 30
                    }
                },
                data: datas.map((item) => item.name),
                axisLabel: {
                    fontSize: 14
                }
            },
            {
                type: "category",
                inverse: true,
                axisTick: "none",
                axisLine: "none",
                show: true,
                data: datas.map((item) => item.value),
                axisLabel: {
                    show: true,
                    fontSize: 14,
                    color: "#333",
                    formatter: function (value, i) {
                        return `\r\r${datas[i].value}%\r\r\r\r${datas[i].count}`;
                    }
                }
            }
        ],
        series: [
            {
                z: 2,
                name: "value",
                type: "bar",
                showBackground: true,
                barWidth: 15,
                zlevel: 1,
                data: datas.map((item, i) => {
                    let itemStyle = {
                        color: colors[i]
                    };
                    return {
                        value: item.value,
                        itemStyle: itemStyle
                    };
                }),
                label: {
                    show: false,
                    position: "right",
                    color: "#333333",
                    fontSize: 14,
                    offset: [10, 0]
                }
            },
            {
                name: "背景",
                type: "bar",
                barWidth: 20,
                barGap: "-100%",
                itemStyle: {
                    normal: {
                        color: "rgba(255, 111, 111, 0.55)"
                    }
                },
                data: maxArr
            }
        ]
    };
}

const changeRadio = () => {
    apiList.tableTable(state.selectType, { conditions: state.conditions, isImport: state.isDisposal, pageSize: homePagination.pageSize, pageNum: 1 }).then(res => {
        tableColums.value = [
            { type: "selection" },
            { type: "index", width: 50, label: "序号", align: "center" }
        ];
        let resData = res['data'].columns;
        for (let i in resData) {
            if (resData[i].isShow) {
                let json = {
                    label: resData[i].title,
                    slot: resData[i].key == "ip" ? "ip" : null,
                    prop: resData[i].key,
                    align: "center",
                    showOverflowTooltip: true
                };
                tableColums.value.push(json);
            }

        }
        tableColums.value.push(
            { label: "操作", slot: "operation", align: "center" }
        );
        tableData.value = [];
        tableData.value = res['data']['rows'];

    });
    columnTableRef.value.load();
}

// 处理树形选择器数据
const buildTree = (data: any[], deptId: string, parentId: string) => {
    const map = new Map();
    const rootNodes = [];
    // 首先将所有节点添加到映射中
    data.forEach(item => {
        map.set(item[deptId], { ...item, label: item['name'], children: [] });
    });
    // 然后根据parentId构建树形结构
    data.forEach(item => {
        if (item[parentId] == "-1" || item[parentId] == null) {
            rootNodes.push(map.get(item[deptId]));
        } else {
            if (map.has(item[parentId])) {
                map.get(item[parentId]).children.push(map.get(item[deptId]));
            } else {
                // 如果找不到parentId，则将该节点作为顶级父节点
                rootNodes.push(map.get(item[deptId]));
            }
        }
    });

    return rootNodes;
}
const init = () => {
    axios.get("eam-core/zcgl-common/category/getAllCategory").then(res => {
        categoryTreeList.value = buildTree(res['data'], 'id', 'parentId');
    }).catch(err => {
        console.info(err);
    });
    //未知资产发现
    apiList.queryUnknowAssetDisposeCount().then(res => {
        UnknowAssetDisposeCount.value = res['data'];
    });
    // 按资产类别分布
    apiList.queryUnknowAssetZoneGroup().then(res => {
        let data = res['data'].list;
        let currentData = [], max = 0, count = 0;
        for (let i in data) {
            const json = {
                name: data[i]['zoneName'],
                value: data[i].rate,
                count: data[i]['count']
            };
            // json.name = data[i]['zoneName'];
            // json.value = data[i].rate;
            currentData.push(json);
            count += data[i].count;

            if (max < json.count) {
                max = json.count;
            }
        }
        initImportantChart(currentData, max, count);
    });
    // 未知资产归属单位统计
    apiList.queryUnknowAssetSourceGroup().then(res => {
        let data = res['data'].list,
            x = [],
            y = [];
        for (let i in data) {
            x.push(data[i].name);
            y.push(data[i].count);
        }
        initBarTypeChart(x, y);
    });
}

//导入
const clearFormValidates = () => {
}
/** 导出模板 */
const exportTemplate = () => {
    let params = {
        categoryId: state.categoryId
    };
    axios.get(`${baseContextPath}/asset/assetResourceExcel/exportExcelTemplate`, {
        params,
        responseType: "arraybuffer"
    }).then((res) => {
        let blob = new Blob([res['data']]);
        // downloadFromBlob(blob, null, res['headers']);
    }).catch(err => {
        ElMessage.error("导出失败");
    });
}

const onUploadError = (err, file, fileList) => {
    console.log(err);
    let message = err.message;
    try {
        let errData = JSON.parse(message);
        ElMessage.error(errData && errData.msg);
    } catch (error) {
    }
    uploader.value.clearFiles();
}

const emits = defineEmits(['refresh'])
const onUploadSuccess = (response, file, fileList) => {
    console.log(response);
    ElMessage.info("导入成功");
    emits("refresh");
    uploader.value.clearFiles();
}
/**钩子函数： 进度*/
const onFileProgress = (event, file, fileList) => {
}
const uploadTemplate = (file) => {
    if (file) {
        showResult.value = false;
        let param = new FormData();
        param.append("file", file.raw);
        param.append("isImport", 0);
        let r = axios.postUpdateFile("/eam-core/zcgl-common/unknow/importSopUnknowAsset", param).then(res => {
            showResult.value = true;
            resultMsg.value = res['data'].message;
            resultUrl.value = res['data'].saveUrl;
            columnTableRef.value.load();
            uploader.value.clearFiles();
        }).catch(err => {
            ElMessage({
                showClose: true,
                message: err,
                type: "error"
            });
        });
    }

}

onMounted(() => {
    initIpScope('init');
    columnTableRef.value.load();
    queryForAssetDetail();
})

apiList.conditionList("unknow/queryUnknowCondition?isAuth=false&isImport=0").then(res => {
    columns.value = res['data'].columns;
});
apiList.tableTable(state.selectType, { conditions: state.conditions, isImport: state.isDisposal, pageSize: homePagination.pageSize, pageNum: 1 }).then(res => {
    tableColums.value = [
        { type: "selection" },
        { type: "index", width: 50, label: "序号", align: "center" }
    ];
    let resData = res['data'].columns;
    for (let i in resData) {
        if (resData[i].isShow) {
            let json = {
                label: resData[i].title,
                slot: resData[i].key == "ip" ? "ip" : null,
                prop: resData[i].key,
                align: "center",
                showOverflowTooltip: true
            };
            tableColums.value.push(json);
        }
    }
    tableColums.value.push(
        { label: "操作", slot: "operation", align: "center" }
    );
    tableUrl.value = "/rest-proxy/unknow/queryUnknowAssetTable";
});
init();

watch(importDrawer, (val) => {
    if (!val) {
        init();
    }
}, { deep: true })

onBeforeUnmount(() => {
    clearInterval(state.intervalTime);
    intervalTime.value = null;
})

</script>

<style scoped lang="scss">
@import '@/views/modules/eam/assetAudit/unknownAssetAudit/css/table.scss';

.green {
    background: linear-gradient(90deg, #72E6D4 0%, #55C8AB 100%);
}

.blue {
    background: linear-gradient(90deg, #79BFFF 0%, #1890FF 100%);
}

.cardHeight {
    height: $cardHeight;
}

.titleSize {
    font-size: $titleSize;
    color: $titleColor;
    font-weight: $titleFontWeight;
    padding: 0 5px;
}

.titleIcon {
    font-size: $titleIcon;
    vertical-align: bottom;
}

::v-deep .el-upload {
    width: 100%;

    .el-upload-dragger {
        width: 100% !important;
        height: 250px;
    }

    .upload-desc {
        height: 18px;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #6E7493;
        line-height: 18px;
        margin-top: 20px;
    }
}

.import-progress {
    margin-top: 20px;

    .complete-percent {
        font-size: 16px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: #3C4A54;
    }

    .filename {
        float: right;
        font-size: 14px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: #3C4A54;
    }

    .upload-progress {
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #6E7493;
    }
}

.import-bz {
    margin-top: 8px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #3C4A54;
}

.ip-alloc-main {
    height: calc(100vh - 130px);
    overflow: hidden;
}

.ip-alloc-left {
    padding: 5px;
    /* background-color: #FFF;*/
    height: calc(100vh - 130px);
    overflow: auto;
    border: 1px solid #e1e8f3;
}

.ip-alloc-right {
    height: calc(100vh - 130px);
    /* background-color: #FFF;*/
    border: 1px solid #e1e8f3;
}

.ip-form-inline {
    padding: 4px;
}

.ip-form-inline .ip-form-item-val {
    font-size: 16px;
    font-weight: 500;
}

.outline-card-container {
    padding: 10px 20px;
    overflow: auto;
    max-height: 600px;
}

.ip-alloc-right .outline-card {
    height: 220px;
    border: 1px dashed rgb(170, 170, 170);
    width: 33%;
    float: left;
    padding: 5px;
    margin: 2px;
}

.form-inner-label {
    text-align: right;
    font-size: 16px;
    padding: 5px;
}

.form-inner-value {
    text-align: left;
    padding: 5px;
    font-size: 16px;
}

.ip-b-title {
    margin: 10px 20px;
    width: 120px;
    height: 30px;
    background: rgb(64, 158, 255);
    color: rgb(255, 255, 255);
    text-align: center;
    padding: 5px;
    font-size: 16px;
}

.ip-use-block {
    /*margin: 5 px 0 px;*/
    width: 120px;
    height: 30px;
    color: rgb(255, 255, 255);
    text-align: center;
    padding: 5px;
    font-size: 16px;
}

.ip-c-d-table {
    border-collapse: collapse;
    font-size: 13px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    width: 100%;
}

.ip-c-d-table tr td {
    border: 1px solid #00a0e9;
    padding: 5px;
}


.myInput ::v-deep .el-select .el-input {
    /*width: 58px;*/
}

.input-with-select ::v-deep .el-input-group__prepend {
    background-color: #fff;
}

::v-deep .el-upload-dragger {
    width: 500px;
}
</style>
