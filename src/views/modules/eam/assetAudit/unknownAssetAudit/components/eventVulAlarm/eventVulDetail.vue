<template>
  <el-main>
    <el-row style="height:200px;">
      <el-col :span="12">
        <el-row style="margin-top:15px;border-right:1px solid #3c4a54">
          <el-col :span="8">
            <el-row style="font-size:12px;font-weight: bold;padding-left:30px;">安全事件</el-row>
            <el-row style="text-align:center;font-weight:bold;font-size:30px;margin-top:50px;color: #3c4a54;">
              <el-col :span="12">
                {{ noDisEventCount }}
              </el-col>
              <el-col :span="12">
                {{ disEventCount }}
              </el-col>
            </el-row>
            <el-row style="text-align:center;
          font-weight: 500;
          font-size: 14px;
          color: #6f6e89;">
              <el-col :span="12">
                未处置
              </el-col>
              <el-col :span="12">
                已处置
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="16">
            <!-- <chart-dom :option="eventOptions" style="height: 200px;width:100%;"></chart-dom> -->
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="12">
        <el-row style="margin-top:15px;">
          <el-col :span="8">
            <el-row style="font-size:12px;font-weight: bold;padding-left:30px;">安全漏洞</el-row>
            <el-row style="text-align:center;font-weight:bold;font-size:30px;margin-top:50px;color: #3c4a54;">
              <el-col :span="12">
                {{ noDisVulCount }}
              </el-col>
              <el-col :span="12">
                {{ disVulCount }}
              </el-col>
            </el-row>
            <el-row style="text-align:center;font-weight: 500;
          font-size: 14px;
          color: #6f6e89;">
              <el-col :span="12">
                未处置
              </el-col>
              <el-col :span="12">
                已处置
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="16">
            <!-- <chart-dom :option="vulOptions" style="height: 200px;width:100%;"></chart-dom> -->
          </el-col>
        </el-row>
      </el-col>
    </el-row>



    <el-row>
      <div style="width: 100%;">
        <p style="font-weight: bold;margin-top:20px;margin-bottom:20px;"><span
            style="padding-right:10px;font-weight:bolder;color:cornflowerblue;font-size:20px;">丨</span>关联端口信息</p>
        <imTableNew :ipList="[assetItem.model.ipAddress]" :title="'port'">
        </imTableNew>
      </div>
    </el-row>

    <el-row>
      <div style="width: 100%;">
        <p style="font-weight: bold;margin-top:20px;margin-bottom:20px;"><span
            style="padding-right:10px;font-weight:bolder;color:cornflowerblue;font-size:20px;">丨</span>关联事件告警信息（近三月）</p>
        <imTableNew :ipList="[assetItem.model.ipAddress]" :title="'event'">
        </imTableNew>
      </div>

    </el-row>

    <el-row>
      <div style="width: 100%;">
        <p style="font-weight: bold;margin-top:20px;margin-bottom:20px;"><span
            style="padding-right:10px;font-weight:bolder;color:cornflowerblue;font-size:20px;">丨</span>关联漏洞数据信息（近三月）</p>
        <imTableNew :ipList="[assetItem.model.ipAddress]" :title="'vul'">
        </imTableNew>
      </div>

    </el-row>
  </el-main>
</template>
<script setup lang="ts">
// import ChartDom from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/chartTool/chartDom.vue";
import imTableNew from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/eventVulAlarm/imTableNew.vue";
import { http as axios } from "@/utils/http";
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";

const props = defineProps({
  assetItem: Object
})

const state = reactive({
  eventOptions: {},
  vulOptions: {},
  disEventCount: 0,
  noDisEventCount: 0,
  disVulCount: 0,
  noDisVulCount: 0,
  ipList: [],
  portUrl: "rest-proxy/assetPosition/queryEventPortDetail",
  eventUrl: "rest-proxy/assetPosition/queryEventTableData",
  vulUrl: "rest-proxy/assetPosition/queryVulTableData",
  portEventColumns: [
    { type: 'index', prop: "index", align: "center" },
    { label: "端口号", prop: "portName", align: "center", showOverflowTooltip: true },
    { label: "关联应用", prop: "webName", align: "center", showOverflowTooltip: true },
    { label: "告警对象", prop: "eventObject", align: "center", showOverflowTooltip: true },
    { label: "端口状态", prop: "portStatus", align: "center", slot: "portStatus" },
    { label: "是否高危端口", prop: "isHigh", align: "center", slot: "isHigh" },
    { label: "风险标签", prop: "eventTags", align: "center", slot: "eventTags" },
    { label: "操作", prop: "oper", align: "center", slot: "portOper" },
  ],
  eventColumns: [
    { type: 'index', prop: "index", align: "center" },
    { label: "事件名称", prop: "event_name", align: "center", showOverflowTooltip: true },
    { label: "告警次数", prop: "event_cnt", align: "center", showOverflowTooltip: true },
    { label: "处置状态", prop: "deal_status", align: "center", showOverflowTooltip: true, slot: "disStatus" },
    { label: "告警来源", prop: "event_agent", align: "center", showOverflowTooltip: true },
    { label: "最新发现时间", prop: "last_time", align: "center", showOverflowTooltip: true }
  ],
  vulColumns: [
    { type: 'index', prop: "index", align: "center" },
    { label: "漏洞名称", prop: "vulName", align: "center", showOverflowTooltip: true },
    { label: "风险等级", prop: "vullevel", align: "center", showOverflowTooltip: true, slot: "vullevel" },
    { label: "处置状态", prop: "status", align: "center", showOverflowTooltip: true, slot: "status" },
    { label: "漏洞来源", prop: "syscode", align: "center", showOverflowTooltip: true },
    { label: "最新发现时间", prop: "lastUpdateTime", align: "center", showOverflowTooltip: true }
  ],
})

const {
  eventOptions, vulOptions, disEventCount, noDisEventCount, disVulCount,
  noDisVulCount, ipList, portUrl, eventUrl, vulUrl, portEventColumns, eventColumns,
  vulColumns
} = toRefs(state)

const initCount = () => {
  let params = {};
  params['ipList'] = ipList.value;
  axios
    .postJson("rest-proxy/assetPosition/queryRelationEventVulCount", params).then(res => {
      disEventCount.value = res['data'].disEventCount;
      noDisEventCount.value = res['data'].noDisEventCount;
      disVulCount.value = res['data'].disVulCount;
      noDisVulCount.value = res['data'].noDisVulCount;
    }).catch(exp => {
      console.log(exp);
    })
}
const initChart = () => {
  let params = {};
  params['ipList'] = ipList.value;
  axios
    .postJson("rest-proxy/assetPosition/queryRelationEventVulChart", params).then(res => {
      eventOptions.value = {
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: '50%',
          top: 'center'
        },
        series: [
          {
            name: '安全事件',
            type: 'pie',
            label: {
              show: false,
              position: 'center'
            },
            radius: '80%',
            center: ['20%', '55%'],
            data: res['data'].eventChart
          }
        ]
      };
      vulOptions.value = {
        title: {
          text: '',
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: '50%',
          top: 'center'
        },
        series: [
          {
            name: '安全漏洞',
            type: 'pie',
            label: {
              show: false,
              position: 'center'
            },
            radius: '80%',
            center: ['20%', '55%'],
            data: res['data'].vulChart
          }
        ]
      };
    }).catch(exp => {
      console.log(exp);
    })
}
const tableParams = ref({});
const loadPortBefore = (params, requestConfig) => {
  tableParams.value = {
    ipList: ipList.value
  };
  Object.assign(requestConfig, {
    headers: {
      "Content-Type": "application/json"
    }
  });
  Object.assign(params, tableParams.value);
}



ipList.value = [];
if (props.assetItem && props.assetItem.model && props.assetItem.model.ipAddress) {
  ipList.value.push(props.assetItem.model.ipAddress);
} else {
  ipList.value = props.assetItem.ipList;
}
initCount();
initChart();


</script>
