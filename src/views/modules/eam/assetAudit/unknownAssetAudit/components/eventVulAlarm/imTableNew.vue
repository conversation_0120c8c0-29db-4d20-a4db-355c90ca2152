<template>
    <div>
        <im-table style="width: 100%;" ref="portEventTable" height="230px" stripes border request-data-root="data"
            read-property="list" :pagination="pagination" is-save-hidden-state :columns="Columns" id-key="portName"
            @on-load-before="loadPortBefore" @on-page-current-change="handlePageChange"
            @on-pagesize-change="handlePageChange">
            <!-- <div v-if="props.title == 'port'"> -->
            <!-- <template scope="scope">
                    <span>
                        {{(page - 1) * pageSize + scope.$index + 1}}
                    </span>
                </template> -->
            <template v-if="props.title == 'port'" v-slot:portStauts="scope">
                <div>
                    <span v-if="scope.row.portStatus == 'open'">开启</span>
                    <span v-else-if="scope.row.portStatus == 'close'">关闭</span>
                    <span v-else>未知</span>
                </div>
            </template>
            <template v-if="props.title == 'port'" v-slot:isHigh="scope">
                <div>
                    <span v-if="scope.row.isHigh == '1'">是</span>
                    <span v-else>否</span>
                </div>
            </template>
            <template v-if="props.title == 'port'" v-slot:eventTags="scope">
                <div v-if="scope.row.eventTags">
                    <el-tag v-for="item in scope.row.eventTags"
                        style="height: 24px; line-height: 24px;background-color:#FF5D61;color:#fff;" type="danger">
                        {{ item }}
                    </el-tag>
                </div>
            </template>
            <template v-if="props.title == 'port'" v-slot:portOper="scope">
                <el-button type="text">端口探测</el-button>
            </template>
            <!-- </div> -->
            <template v-if="props.title == 'event'" v-slot:disStatus="scope">
                <div>
                    <span v-if="scope.row.deal_status == '1'">
                        <div>
                            <div
                                style="height:6px;width:6px;background-color:green;border-radius:6px;display: inline-block;margin-right:5px;">
                            </div>未处置
                        </div>
                    </span>
                    <span v-else>
                        <div
                            style="height:6px;width:6px;background-color:deepskyblue;border-radius:6px;display: inline-block;margin-right:5px;">
                        </div>已处置
                    </span>
                </div>
            </template>




            <template v-if="props.title == 'vul'" v-slot:disStatus="scope">
                <div>
                    <span v-if="scope.row.status == '1'">
                        <div>
                            <div
                                style="height:6px;width:6px;background-color:green;border-radius:6px;display: inline-block;margin-right:5px;">
                            </div>未处置
                        </div>
                    </span>
                    <span v-else>
                        <div
                            style="height:6px;width:6px;background-color:deepskyblue;border-radius:6px;display: inline-block;margin-right:5px;">
                        </div>已处置
                    </span>
                </div>
            </template>
            <template v-if="props.title == 'vul'" v-slot:vullevel="scope">
                <div v-if="scope.row.vullevel">
                    <el-tag v-if="scope.row.vullevel == '危急'"
                        style="height: 24px; line-height: 24px;background-color:rebeccapurple;color:#fff;"
                        type="danger">{{
                            scope.row.vullevel }}</el-tag>
                    <el-tag v-if="scope.row.vullevel == '高危'"
                        style="height: 24px; line-height: 24px;background-color:#FF5D61;color:#fff;" type="danger">{{
                            scope.row.vullevel }}</el-tag>
                    <el-tag v-else-if="scope.row.vullevel == '中危'"
                        style="height: 24px; line-height: 24px;background-color:darkorange;color:#fff;">{{
                            scope.row.vullevel
                        }}</el-tag>
                    <el-tag v-else-if="scope.row.vullevel == '低危'"
                        style="height: 24px; line-height: 24px;background-color:lightgreen;color:#fff;">{{
                            scope.row.vullevel
                        }}</el-tag>
                    <el-tag v-else-if="scope.row.vullevel == '信息'"
                        style="height: 24px; line-height: 24px;background-color:darkgreen;color:#fff;">{{
                            scope.row.vullevel
                        }}</el-tag>
                    <span v-else>{{ scope.row.reSeverity }}</span>
                </div>
            </template>









            <template v-if="props.title == 'alarm'" v-slot:reSeverity="scope">
                <div v-if="scope.row.reSeverity">
                    <el-tag v-if="scope.row.reSeverity == '严重'"
                        style="height: 24px; line-height: 24px;background-color:#FF5D61;color:#fff;" type="danger">{{
                            scope.row.reSeverity }}</el-tag>
                    <el-tag v-else-if="scope.row.reSeverity == '重要'"
                        style="height: 24px; line-height: 24px;background-color:darkorange;color:#fff;">{{
                            scope.row.reSeverity
                        }}</el-tag>
                    <el-tag v-else-if="scope.row.reSeverity == '一般'"
                        style="height: 24px; line-height: 24px;background-color:lightgreen;color:#fff;">{{
                            scope.row.reSeverity
                        }}</el-tag>
                    <el-tag v-else-if="scope.row.reSeverity == '提示'"
                        style="height: 24px; line-height: 24px;background-color:darkgreen;color:#fff;">{{
                            scope.row.reSeverity
                        }}</el-tag>
                    <span v-else>{{ scope.row.reSeverity }}</span>
                </div>
            </template>
        </im-table>
    </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs } from 'vue';
import { http as axios } from "@/utils/http";
const props = defineProps({
    portEventColumns: Array,
    portUrl: String,
    method: String,
    ipList: Array,
    title: String
})
const pagination = reactive({
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [5, 10, 20, 30, 40, 50, 100],
})
const state = reactive({
    portUrl: "rest-proxy/assetPosition/queryEventPortDetail",
    eventUrl: "rest-proxy/assetPosition/queryEventTableData",
    vulUrl: "rest-proxy/assetPosition/queryVulTableData",
    portEventColumns: [
        { type: 'index', prop: "index", align: "center" },
        { label: "端口号", prop: "portName", align: "center", showOverflowTooltip: true },
        { label: "关联应用", prop: "webName", align: "center", showOverflowTooltip: true },
        { label: "告警对象", prop: "eventObject", align: "center", showOverflowTooltip: true },
        { label: "端口状态", prop: "portStatus", align: "center", slot: "portStatus" },
        { label: "是否高危端口", prop: "isHigh", align: "center", slot: "isHigh" },
        { label: "风险标签", prop: "eventTags", align: "center", slot: "eventTags" },
        { label: "操作", prop: "oper", align: "center", slot: "portOper" },
    ],
    eventColumns: [
        { type: 'index', prop: "index", align: "center" },
        { label: "事件名称", prop: "event_name", align: "center", showOverflowTooltip: true },
        { label: "告警次数", prop: "event_cnt", align: "center", showOverflowTooltip: true },
        { label: "处置状态", prop: "deal_status", align: "center", showOverflowTooltip: true, slot: "disStatus" },
        { label: "告警来源", prop: "event_agent", align: "center", showOverflowTooltip: true },
        { label: "最新发现时间", prop: "last_time", align: "center", showOverflowTooltip: true }
    ],
    vulColumns: [
        { type: 'index', prop: "index", align: "center" },
        { label: "漏洞名称", prop: "vulName", align: "center", showOverflowTooltip: true },
        { label: "风险等级", prop: "vullevel", align: "center", showOverflowTooltip: true, slot: "vullevel" },
        { label: "处置状态", prop: "status", align: "center", showOverflowTooltip: true, slot: "status" },
        { label: "漏洞来源", prop: "syscode", align: "center", showOverflowTooltip: true },
        { label: "最新发现时间", prop: "lastUpdateTime", align: "center", showOverflowTooltip: true }
    ],
    alarmUrl: "/alarm-query-server/alarm/queryAll",
    alarmColumns: [
        { label: "告警等级", prop: "reSeverity", align: "center", showOverflowTooltip: true, slot: "reSeverity" },
        { label: "告警标题", prop: "title", align: "center", showOverflowTooltip: true },
        { label: "告警正文", prop: "description", align: "center", showOverflowTooltip: true },
        { label: "告警次数", prop: "reSeverityNum", align: "center", showOverflowTooltip: true },
        { label: "持续时长", prop: "duration", align: "center", showOverflowTooltip: true },
        { label: "最新发现时间", prop: "lastOccurrence", align: "center", showOverflowTooltip: true },
    ],
})
const {
    portUrl, vulUrl, eventUrl, portEventColumns, eventColumns, vulColumns
} = toRefs(state)
const Columns = ref([]);
const url = ref('');
const loadPortBefore = () => { }
const tableData = ref([]);
const requestTable = () => {
    const params = {
        ipList: props.ipList,
        pageNum: pagination.currentPage,
        pageSize: pagination.pageSize
    }
    if (props.method == 'GET') {

    } {
        console.log("props.portUrl", url.value)
        axios.postJson(url.value, params).then(res => {
            pagination.total = res['data']['total'];
            tableData.value = res['data']['list'];
        })
    }
}
const handlePageChange = (page: number, pageSize: number) => {
    pagination.currentPage = page;
    pagination.pageSize = pageSize;
    requestTable();
}
onMounted(() => {
    console.log(props.title, props.title, props.title, props.title, props.title, props.title)
    if (props.title == 'port') {
        Columns.value = state.portEventColumns;
        url.value = state.portUrl;
        requestTable();
    } else if (props.title == 'event') {
        Columns.value = state.eventColumns;
        url.value = state.eventUrl;
        requestTable();
    } else if (props.title == 'vul') {
        Columns.value = state.vulColumns;
        url.value = state.vulUrl;
        requestTable();
    } else if (props.title == 'alarm') {
        Columns.value = state.alarmColumns;
        url.value = state.alarmUrl;
        requestTable();
    }

})
</script>

<style lang="scss" scoped></style>