<template>
  <el-main>
    <el-row>
      <el-col :span="6">
        <div style="padding:5px;">
          <el-row style="border:1px solid lightgray;border-radius: 10px;">
            <el-col :span="8" style="padding:25px;">
              <img src="../../images/alarmCount.png" style="width:100%;" />
            </el-col>
            <el-col :span="16" style="padding-left:10px;">
              <p style="padding-left:0;padding-right:0;padding-bottom:0;margin:0;padding-top:23px;color:#3c4a54">实时告警数
              </p>
              <p style="padding:0;margin:0;font-size:30px;">{{ nowAlarmCount }}</p>
            </el-col>
          </el-row>
          <el-row style="border:1px solid lightgray;border-radius: 15px;margin-top:10px;">
            <el-col :span="8" style="padding:25px;">
              <img src="../../images/alarmCount.png" style="width:100%;" />
            </el-col>
            <el-col :span="16" style="padding-left:10px;">
              <p style="padding-left:0;padding-right:0;padding-bottom:0;margin:0;padding-top:23px;color:#3c4a54">近七天累计告警
              </p>
              <p style="padding:0;margin:0;font-size:30px;">{{ sevenDayAlarmCount }}</p>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :span="6">
        <div style="padding:5px;">
          <div style="height:218px;border:1px solid lightgray;border-radius: 10px;">
            <!-- <chart-dom :option="sysMemOptions" style="height:218px;width:100%"></chart-dom> -->
          </div>
        </div>

      </el-col>
      <el-col :span="6">
        <div style="padding:5px;">
          <div style="height:218px;border:1px solid lightgray;border-radius: 10px;">
            <!-- <chart-dom :option="cpuOptions" style="height:218px;width:100%"></chart-dom> -->
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div style="padding:5px;">
          <div style="height:218px;border:1px solid lightgray;border-radius: 10px;">
            <!-- <chart-dom :option="losePackageOptions" style="height:218px;width:100%"></chart-dom> -->
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row>
      <div style="width: 100%;">
        <p style="font-weight: bold;margin-top:20px;margin-bottom:20px;"><span
            style="padding-right:10px;font-weight:bolder;color:cornflowerblue;font-size:20px;">丨</span>实时告警信息（近24小时）</p>

        <imTableNew :ipList="[assetItem.model.ipAddress]" :title="'alarm'">
        </imTableNew>
        <!-- <im-table ref="alarmTable" height="230px" method="POST" stripes border request-data-root="data"
          read-property="data" :pagination="{
            total: 'total',
            currentPage: 'page',
            pageSize: 'size',
            options: {
              pageSizes: [5, 10, 20, 30, 40, 50, 100],
              pageSize: 5
            }
          }" is-save-hidden-state :columns="alarmColumns" :url="alarmUrl" id-key="alarmName"
          @on-load-before="loadAlarmBefore">
          <template v-slot:reSeverity="scope">
            <div v-if="scope.row.reSeverity">
              <el-tag v-if="scope.row.reSeverity == '严重'"
                style="height: 24px; line-height: 24px;background-color:#FF5D61;color:#fff;" type="danger">{{
                  scope.row.reSeverity }}</el-tag>
              <el-tag v-else-if="scope.row.reSeverity == '重要'"
                style="height: 24px; line-height: 24px;background-color:darkorange;color:#fff;">{{ scope.row.reSeverity
                }}</el-tag>
              <el-tag v-else-if="scope.row.reSeverity == '一般'"
                style="height: 24px; line-height: 24px;background-color:lightgreen;color:#fff;">{{ scope.row.reSeverity
                }}</el-tag>
              <el-tag v-else-if="scope.row.reSeverity == '提示'"
                style="height: 24px; line-height: 24px;background-color:darkgreen;color:#fff;">{{ scope.row.reSeverity
                }}</el-tag>
              <span v-else>{{ scope.row.reSeverity }}</span>
            </div>
          </template>
</im-table> -->
      </div>
    </el-row>
  </el-main>
</template>
<script setup lang="ts">
// import ChartDom from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/chartTool/chartDom.vue";
import { http as axios } from "@/utils/http";
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";
import imTableNew from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/eventVulAlarm/imTableNew.vue";
const props = defineProps({
  assetItem: Object
})

const state = reactive({
  ipList: [],
  cuidList: [],
  alarmUrl: "/alarm-query-server/alarm/queryAll",
  alarmColumns: [
    { label: "告警等级", prop: "reSeverity", align: "center", showOverflowTooltip: true, slot: "reSeverity" },
    { label: "告警标题", prop: "title", align: "center", showOverflowTooltip: true },
    { label: "告警正文", prop: "description", align: "center", showOverflowTooltip: true },
    { label: "告警次数", prop: "reSeverityNum", align: "center", showOverflowTooltip: true },
    { label: "持续时长", prop: "duration", align: "center", showOverflowTooltip: true },
    { label: "最新发现时间", prop: "lastOccurrence", align: "center", showOverflowTooltip: true },
  ],
  memRate: 25,
  cpuRate: 30,
  lossPackRate: 22.5,
  sysMemOptions: {},
  cpuOptions: {},
  losePackageOptions: {},
  nowAlarmCount: 0,
  sevenDayAlarmCount: 0
})
const {
  ipList, cuidList, alarmUrl, alarmColumns, memRate, cpuRate, lossPackRate, sysMemOptions, cpuOptions,
  losePackageOptions, nowAlarmCount, sevenDayAlarmCount
} = toRefs(state);

const initData = () => {
  // state.sysMemOptions.series[0].data[0].value = state.memRate;
  // state.sysMemOptions.series[0].data[1].value = 100 - state.memRate;
  // state.cpuOptions.series[0].data[0].value = state.cpuRate;
  // state.cpuOptions.series[0].data[1].value = 100 - state.cpuRate;
  // state.losePackageOptions.series[0].data[0].value = state.lossPackRate;
  // state.losePackageOptions.series[0].data[1].value = 100 - state.lossPackRate;
}
const getDateTimeByFomatter = (date) => {
  let myyear = date.getFullYear();
  let mymonth = date.getMonth() + 1;
  let myweekday = date.getDate();
  let myhour = date.getHours();
  let mymin = date.getMinutes();
  let mysec = date.getSeconds();
  if (mymonth < 10) {
    mymonth = '0' + mymonth;
  }
  if (myweekday < 10) {
    myweekday = '0' + myweekday;
  }
  if (myhour < 10) {
    myhour = '0' + myhour;
  }
  if (mymin < 10) {
    mymin = '0' + mymin;
  }
  if (mysec < 10) {
    mysec = '0' + mysec;
  }
  let fdate = myyear + "-" + mymonth + "-" + myweekday + " " + myhour + ":"
    + mymin + ":" + mysec;
  return fdate;
}
const tableParams = ref({});
const loadAlarmBefore = (params, requestConfig) => {
  let date = new Date();
  let sdate = new Date(date);
  sdate.setDate(date.getDate() - 1);
  let startDate = getDateTimeByFomatter(sdate);
  let endDate = getDateTimeByFomatter(date);
  tableParams.value = {
    code: "alarmQuery",
    user: "dev",
    extendParam: [
      {
        type: "enums",
        fieldName: "devIp",
        value: state.ipList
      },
      {
        type: "enums",
        fieldName: "status",
        value: [
          "OPEN",
          "CONFIRMED"
        ]
      },
      {
        type: "time",
        fieldName: "lastOccurrence",
        value: [
          startDate,
          endDate
        ]
      }
    ]
  };
  Object.assign(requestConfig, {
    headers: {
      "Content-Type": "application/json"
    }
  });
  Object.assign(params, tableParams.value);
}
const initAlarmCount = () => {
  let params = {};
  params['devIp'] = state.ipList;
  params['status'] = ["OPEN", "CONFIRMED"];
  axios
    .postJson("monitor-web-proxy/alarmCountController/getAlarmCount", params).then(res => {
      nowAlarmCount.value = res['data'];
    }).catch(exp => {
      console.log(exp);
    })

  params = {};
  params['devIp'] = state.ipList;
  params['time'] = "7d";
  axios
    .postJson("monitor-web-proxy/alarmCountController/getAlarmCount", params).then(res => {
      sevenDayAlarmCount.value = res['data'];
    }).catch(exp => {
      console.log(exp);
    })
}
const initCpuMemPackageRate = () => {
  let params = {};
  params['cuidList'] = cuidList.value;
  params['ipList'] = ipList.value;
  axios
    .postJson("rest-proxy/assetPosition/queryCpuMemDisByIps", params).then(res => {
      if (res['data']) {
        if (!res['data'].cpuRate) {
          res['data'].cpuRate = 0;
        }
        if (!res['data'].memRate) {
          res['data'].memRate = 0;
        }
        if (!res['data'].packageRate) {
          res['data'].packageRate = 0;
        }
      } else {
        res['data'] = {
          "cpuRate": 0,
          "memRate": 0,
          "packageRate": 0
        }
      }
      cpuOptions.value = {
        title: {
          text: 'CPU使用率',
          textStyle: {
            fontSize: 14,
            color: "#3c4a54"
          }
        },
        hoverAnimation: false,
        series: [
          {
            name: 'CPU使用率',
            type: 'pie',
            color: ["#4AFE84", "#f1f1f1"],
            selectedOffset: 0,
            label: {
              show: false,
              position: 'center',
              formatter: 'CPU利用率\n\n{d}%'
            },
            emphasis: {
              disabled: true
            },
            avoidLabelOverlap: false,
            labelLine: {
              show: false
            },
            silent: true,
            itemStyle: {
              //设置的是每项之间的留白
              borderWidth: 7,
              borderColor: '#fff'
            },
            radius: ['60', '80%'],
            center: ['50%', '50%'],
            data: [
              {
                value: res['data'].cpuRate, name: '系统内存', selected: true,
                label: {
                  show: true,
                  fontSize: 14,
                  color: "#3c4a54"
                }
              },
              { value: 100 - res['data'].cpuRate, name: '空白' },
            ],
          }
        ]
      };
      sysMemOptions.value = {
        title: {
          text: '系统内存',
          textStyle: {
            fontSize: 14,
            color: "#3c4a54"
          }
        },
        hoverAnimation: false,
        series: [
          {
            name: '系统内存',
            type: 'pie',
            color: ["#1890fe", "#f1f1f1"],
            selectedOffset: 0,
            label: {
              show: false,
              position: 'center',
              formatter: '内存利用率\n\n{d}%'
            },
            emphasis: {
              disabled: true
            },
            avoidLabelOverlap: false,
            labelLine: {
              show: false
            },
            silent: true,
            itemStyle: {
              //设置的是每项之间的留白
              borderWidth: 7,
              borderColor: '#fff'
            },
            radius: ['60', '80%'],
            center: ['50%', '50%'],
            data: [
              {
                value: res.data.memRate, name: '系统内存', selected: true,
                label: {
                  show: true,
                  fontSize: 14,
                  color: "#3c4a54"
                }
              },
              { value: 100 - res.data.memRate, name: '空白' },
            ],
          }
        ]
      };
      losePackageOptions.value = {
        title: {
          text: '丢包率',
          textStyle: {
            fontSize: 14,
            color: "#3c4a54"
          }
        },
        hoverAnimation: false,
        series: [
          {
            name: '丢包率',
            type: 'pie',
            color: ["#FED46F", "#f1f1f1"],
            selectedOffset: 0,
            label: {
              show: false,
              position: 'center',
              formatter: '丢包率\n\n{d}%'
            },
            emphasis: {
              disabled: true
            },
            avoidLabelOverlap: false,
            labelLine: {
              show: false
            },
            silent: true,
            itemStyle: {
              //设置的是每项之间的留白
              borderWidth: 7,
              borderColor: '#fff'
            },
            radius: ['60', '80%'],
            center: ['50%', '50%'],
            data: [
              {
                value: res.data.packageRate, name: '丢包率', selected: true,
                label: {
                  show: true,
                  fontSize: 14,
                  color: "#3c4a54"
                }
              },
              { value: 100 - res.data.packageRate, name: '空白' },
            ],
          }
        ]
      };
    }).catch(exp => {
      console.log(exp);
    })
}


initData();

ipList.value = [];
cuidList.value = [];
ipList.value.push(props.assetItem.model.ipAddress);
cuidList.value.push(props.assetItem.model.cuid);
initAlarmCount();
initCpuMemPackageRate();
</script>
