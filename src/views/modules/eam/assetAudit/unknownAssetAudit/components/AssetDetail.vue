<template>
  <!-- <el-drawer class="asset-detail" v-model="drawer" :direction="direction" append-to-body size="70%"
    :before-close="handleClose"> -->
  <div class="asset-detail">
    <div style="margin-bottom: 1rem;" class="title" slot="title">
      <!--      <i class="icon" :class="icon"></i>-->
      <!-- <img class="icon" :src="'@/views/modules/eam/assetAudit/unknownAssetAudit/images/small/physical-server.svg'" width="36" /> -->
      <span class="label">{{ title }}</span>
      <span v-if="props.assetItem && props.assetItem.model && props.assetItem.model.onlineStatus"
        style="margin-left:10px;">
        <el-tag v-if="props.assetItem.model.onlineStatus == 'online'" style="height: 24px; line-height: 24px;"
          type="success">在线</el-tag>
        <el-tag v-if="props.assetItem.model.onlineStatus == 'outline'" style="height: 24px; line-height: 24px;"
          type="danger">离线</el-tag>
      </span>
    </div>
    <div class="zoneDuty" style="margin-left:30px;">
      <el-form :model="infoForm" ref="infoFormRef" label-width="120px" label-position="right" label-suffix="：">
        <el-row>
          <el-form-item prop="zoneName" label="所属单位" style="position: relative">
            <span>{{ infoForm.zoneName }}</span>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="assetDuty" label="责任人" style="position: relative">
            <span>{{ infoForm.assetDuty }}</span>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="eventTabs" label="告警标签" style="position: relative">
            <div>
              <el-tag v-for="item in infoForm.eventTags"
                style="height: 20px; line-height: 20px;margin-right:10px;background-color:#FF5D61;color:#fff;"
                type="danger">
                {{ item }}
              </el-tag>
            </div>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <el-tabs class="asset-detail-tabs" v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="基本信息" name="base">
        <div class="asset-tab-item">
          <template v-if="groupAssetInfos.length > 0">
            <template v-for="(groupAssetInfo, index) in groupAssetInfos" :key="index">
              <div>
                <div v-if="groupAssetInfos.length > 1" class="detail-title">
                  <img src="../images/group.svg" />
                  <span>{{ groupAssetInfo.groupName }}</span>
                </div>
                <el-form class="detail-form" style="padding: 0 10px" label-width="120px" label-position="right"
                  label-suffix="：">
                  <el-row>
                    <el-col v-for="(column, j) in groupAssetInfo.children" :span="column.showType == 'objectTable' ||
                      column.showType == 'table'
                      ? 24
                      : 12
                      " :key="index + '-' + j">


                      <el-form-item style="position: relative;">
                        <div slot="label" :title="column.propertyUnit != null &&
                          column.propertyUnit != ''
                          ? column.name + '(' + column.propertyUnit + ')'
                          : column.name
                          " style="
                            /* width: 100%; */
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            color: #6f6e89;
                          ">
                          {{
                            column.propertyUnit != null &&
                              column.propertyUnit != ""
                              ? column.name + "(" + column.propertyUnit + ")"
                              : column.name
                          }}：
                        </div>
                        <span v-if="
                          column.code == 'ipAddress' || column.code == 'cuid'
                        ">
                          <el-tooltip :content="column.value">
                            <span v-if="
                              column.code == 'ipAddress' ||
                              column.code == 'cuid'
                            ">{{
                              !column ||
                                column.value == null ||
                                column.value == ""
                                ? ""
                                : column.value.indexOf(",") > 0
                                  ? column.value.split(",")[0] + "..."
                                  : column.value
                            }}</span>
                            <span v-else>{{ column.value }}</span>
                          </el-tooltip>
                        </span>
                        <el-tooltip :content="column.tooltipValue" v-else-if="
                          column.tooltipType == '1' ||
                          column.tooltipType == '3'
                        ">
                          <div v-if="column.showType == 'objectTable'">
                            <el-collapse accordion v-model="ots[column.code]">
                              <el-collapse-item :name="column.code">
                                <template slot="title">
                                  {{
                                    column.value == undefined ||
                                      column.value == null ||
                                      column.value == ""
                                      ? "0"
                                      : column.value
                                  }}
                                </template>
                                <object-table-property :ref="column.code" :categoryIdP="categoryId"
                                  :proOcode="column.ocode" :type="'view'" :showOper="'show'" :tableData="column.oovalue"
                                  :tableColumn="column.tableList"></object-table-property>
                              </el-collapse-item>
                            </el-collapse>
                          </div>
                          <div style="width: 100%;" v-else-if="column.showType == 'table'">
                            <div style="overflow: hidden;margin-left: 3.6rem;">
                              <svg v-if="!column.hide" style="
                                  width: 33px;
                                  height: 33px;
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1024 1024">
                                <path fill="currentColor" d="M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64"></path>
                                <path fill="currentColor" d="M480 672V352a32 32 0 1 1 64 0v320a32 32 0 0 1-64 0"></path>
                                <path fill="currentColor"
                                  d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896">
                                </path>
                              </svg>
                              <svg v-else style="
                                  width: 33px;
                                  height: 33px;
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)" xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 1024 1024">
                                <path fill="currentColor"
                                  d="m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248z">
                                </path>
                                <path fill="currentColor"
                                  d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896">
                                </path>
                              </svg>
                              <!-- <i class="el-icon-circle-plus-outline" v-if="column.hide" style="
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)"></i> -->
                              <!-- <i class="el-icon-remove-outline" v-else style="
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)"></i> -->
                            </div>
                            <div v-if="column.hide" style="margin-top: 10px; position: relative;width: 100%;">
                              <el-table :data="column.tableList">
                                <el-table-column type="index" label="序号" width="80px" align="center">
                                </el-table-column>
                                <el-table-column label="文件大小" align="center" prop="fileSize">
                                </el-table-column>
                                <el-table-column label="附件名称" align="center" prop="fileName">
                                </el-table-column>
                                <el-table-column label="操作" align="center" prop="action" width="150">
                                  <template slot-scope="scope">
                                    <el-button size="mini" @click="
                                      download(
                                        scope.row.fullPath,
                                        scope.row.fileName
                                      )
                                      ">下载</el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </div>
                          <span v-else>{{ column.value }}</span>
                        </el-tooltip>
                        <div v-else-if="column.tooltipType == '2'">
                          <div v-if="
                            column.value != undefined && column.value != ''
                          ">
                            <el-popover v-for="(item2, index2) in column.value.split(',')" :key="item2" placement="top"
                              trigger="hover">
                              <el-form label-width="160px" size="mini">
                                <el-form-item v-for="item1 in column.tooltipValue.columns ||
                                  []" :label="item1.title + '：'" :title="item1.title + '：'"
                                  style="border-bottom: 2px solid #eee" :key="item1.key" :prop="item1.key">
                                  <div style="text-align: center; font-size: 12px">
                                    {{
                                      column.tooltipValue.rows[index2][
                                      item1.key
                                      ]
                                    }}
                                  </div>
                                </el-form-item>
                              </el-form>
                              <span slot="reference" v-if="
                                index2 == column.value.split(',').length - 1
                              ">{{ item2 }}</span>
                              <span slot="reference" v-else>{{ item2 }},</span>
                            </el-popover>
                          </div>
                          <div v-else></div>
                        </div>

                        <div v-else>

                          <div v-if="column.showType == 'objectTable'">
                            <el-collapse v-model="ots[column.code]" accordion>
                              <el-collapse-item :name="column.code">
                                <template slot="title">
                                  {{
                                    column.value == undefined ||
                                      column.value == null ||
                                      column.value == ""
                                      ? "0"
                                      : column.value
                                  }}
                                </template>
                                <object-table-property :ref="column.code" :categoryIdP="categoryId"
                                  :proOcode="column.ocode" :type="'view'" :showOper="'show'" :tableData="column.oovalue"
                                  :tableColumn="column.tableList"></object-table-property>
                              </el-collapse-item>
                            </el-collapse>
                          </div>
                          <!-- <div v-else-if="column.showType == 'table'">
                            <div style="overflow: hidden">
                              <i class="el-icon-circle-plus-outline" v-if="column.hide" style="
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)"></i>
                              <i class="el-icon-remove-outline" v-else style="
                                  font-size: 30px;
                                  float: left;
                                  margin-left: 10px;
                                  cursor: pointer;
                                  color: #66b1ff;
                                " @click="openUpload(column, column.code)"></i>
                            </div>
                            <div v-if="!column.hide" style="margin-top: 10px; position: relative">

                              <el-table :data="column.tableList">
                                <el-table-column type="index" label="序号" width="80px" align="center">
                                </el-table-column>
                                <el-table-column label="文件大小" align="center" prop="fileSize">
                                </el-table-column>
                                <el-table-column label="附件名称" align="center" prop="fileName">
                                </el-table-column>
                                <el-table-column label="操作" align="center" prop="action" width="150">
                                  <template slot-scope="scope">
                                    <el-button size="mini" @click="
                                      download(
                                        scope.row.fullPath,
                                        scope.row.fileName
                                      )
                                      ">下载</el-button>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </div> -->
                          <span v-else>{{ column.value }}</span>
                        </div>
                        <span v-if="
                          column.titleDesc != null &&
                          column.titleDesc != undefined &&
                          column.titleDesc != '' &&
                          column.titleDesc != 'null'
                        " style="
                            position: absolute;
                            top: 0px;
                            left: -18px;
                            z-index: 1000;
                          " :title="column.titleDesc">
                          <i class="el-icon-question" style="opacity: 0.7" />
                        </span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </template>
          </template>
          <el-empty v-else></el-empty>
        </div>
      </el-tab-pane>
      <el-tab-pane label="变更追踪" name="change-tracking">
        <div class="asset-tab-item">
          <!-- {{ 'dhfhsdhfjsdhjfhjdshjfhsdhfjjfgjdfjhgdfhgjhjfdhjghjfdhgjhdfjg' + state.changeTracks }} -->
          <el-timeline v-if="(changeTracks.length > 0)" ref="trackingTimeline" class="tracking-timeline">
            <el-timeline-item v-for="(changeTrack, index) in changeTracks" :key="'ct-' + index"
              class="primary-timeline-item" :timestamp="changeTrack.date" icon="el-icon-timer" placement="top">
              <div>
                <div class="change-time-item" v-for="(changeOfTime, j) in changeTrack.data" :key="'time-' + j">
                  <div class="time-label">{{ changeOfTime.time }}</div>

                  <div style="display: flex; margin-bottom: 8px" v-if="hasPropertyChanges(changeOfTime.data)">
                    <el-tag>属性变更</el-tag>
                    <div class="text-items">
                      <div class="text-item" v-for="(item, i) in getPropertyChanges(
                        changeOfTime.data
                      )" :key="i">
                        {{ item.title }}
                      </div>
                    </div>
                  </div>

                  <div style="display: flex" v-if="hasRelationChanges(changeOfTime.data)">
                    <el-tag type="warning">关系变更</el-tag>
                    <div class="text-items">
                      <div class="text-item" v-for="(item, i) in getRelationChanges(
                        changeOfTime.data
                      )" :key="i">
                        {{ item.title }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>

          <el-empty v-else></el-empty>

          <div v-if="changeTracks.length > 0" class="change-more" @click="queryChangeTracks">
            <span>查看更多变更记录</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="关系拓扑" name="topo">
        <div class="asset-tab-item">
          <AssetTopo v-if="showTopo" ref="assetTopo" :asset-item="props.assetItem" style="height: 100%"
            :group-types="groupTypes" :category-columns="props.categoryColumns"></AssetTopo>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="showPort" label="端口" name="port">
        <div class="asset-tab-item">
          <asset-port v-if="isServerPort" style="height: 100%" :port-list="portList"></asset-port>
          <asset-port-network v-else style="height: 100%" :port-list="portList"></asset-port-network>
        </div>
      </el-tab-pane>
      <el-tab-pane label="风险信息" name="event">
        <event-vul-detail :assetItem="props.assetItem" v-if="activeName == 'event'"></event-vul-detail>
      </el-tab-pane>
      <el-tab-pane label="告警信息" name="alarm">
        <alarm-info :assetItem="props.assetItem" v-if="activeName == 'alarm'"></alarm-info>
      </el-tab-pane>
    </el-tabs>
  </div>
  <!-- </el-drawer> -->
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";
import {
  queryDetailBase as queryDetailBaseApi,
  queryDetailChangeTrack,
  queryDetailRelations as queryDetailRelationsApi,
  queryDetailPort as queryDetailPortApi, queryAssetZoneDutyTags as queryAssetZoneDutyTagsApi,
} from "@/views/modules/eam/assetAudit/unknownAssetAudit/api/asset-positioning";
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCategoryInfo } from '@/views/modules/eam/assetAudit/unknownAssetAudit/mixin/asset'
import AssetPort from '@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/AssetPort.vue'
import AssetTopo from '@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/AssetTopo.vue'
import AssetPortNetwork from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/AssetPortNetwork.vue";
import AlarmInfo from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/eventVulAlarm/alarmInfo.vue";
import EventVulDetail from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/eventVulAlarm/eventVulDetail.vue"
import objectTableProperty from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/objectTableProperty.vue";
const props = defineProps({
  visible: Boolean,
  assetItem: Object,
  assetTypeInfo: Object,
  categoryColumns: Object,
});

const {
  getCategoryImage,
  getCategoryName,
  getTopoImage,
  getTopoImageSelect,
  getColumns,
} = useCategoryInfo(props);

const getPortType = (item) => {
  let category_id = item?.category_id;
  let categoryColumns = props.categoryColumns || {};
  let portType = categoryColumns[category_id]?.portType;
  return portType;
};

const state = reactive({
  infoForm: {
    zoneName: '',
    assetDuty: '',
    eventTags: []
  },
  queryParams: {},
  ipList: [],
  collapseName: "1",
  ots: {},
  direction: "rtl",
  drawer: false,
  activeName: "base",
  /** 基本信息分组数据 */
  groupAssetInfos: [],
  /** 变更追踪 */
  changeTracks: [],
  pageNum: 0,
  endChangeFlag: true,
  /** 拓扑关系 */
  groupTypes: [],
  showTopo: false,
  /** 端口信息 */
  portList: [],
});
const {
  infoForm, queryParams, ipList, collapseName,
  ots, direction, drawer, activeName, groupAssetInfos, changeTracks,
  pageNum, endChangeFlag, groupTypes, showTopo, portList
} = toRefs(state);

const categoryId = computed(() => {
  if (props.assetItem) {
    if (props.assetItem.category_id) {
      return props.assetItem.category_id;
    } else if (props.assetItem.model) {
      if (props.assetItem.model.category_id) {
        return props.assetItem.model.category_id;
      }
    }
  }
  return props.assetItem?.category_id;
});
const assetId = computed(() => {
  if (props.assetItem) {
    if (props.assetItem._id) {
      return props.assetItem._id;
    } else if (props.assetItem.model) {
      if (props.assetItem.model._id) {
        return props.assetItem.model._id;
      }
    }
  }
  return props.assetItem?._id;
});
const title = computed(() => {
  if (props.assetItem) {
    if (props.assetItem.name) {
      return props.assetItem.name;
    } else if (props.assetItem.model) {
      if (props.assetItem.model.name) {
        return props.assetItem.model.name;
      }
    }
  }
  return props.assetItem?.name;
});
const icon = computed(() => {
  return props.assetItem?.icon;
});
const detailParams = computed(() => {
  return {
    instance_id: props.assetItem?.model?._id,
    category_id: props.assetItem?.model?.category_id,
  };
});
const portType = computed(() => {
  return getPortType(props.assetItem?.model);
});
const showPort = computed(() => {
  return portType.value != "3";
});
const isServerPort = computed(() => {
  return portType.value == "1";
});



const getGlobalParams = () => {
  return state.queryParams;
}
const handleOnShow = () => {
  // 重置信息
  changeTracks.value = [];
  pageNum.value = 0;
  activeName.value = "base";

  nextTick(() => {
    /** 查询所属单位，责任人，告警标签信息 */
    queryAssetZoneDutyTags();
    /** 查询基本信息 */
    queryDetailBase();
    /** 查询变更追踪 */
    queryChangeTracks();
    /** 查询拓扑关系 */
    queryDetailRelations();
    /** 查询端口信息 */
    queryDetailPort();
  });
}
const download = (fullPath, fileName) => {
  window.location.href =
    "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
    fullPath +
    "&fileName=" +
    fileName;
}
const openUpload = (item, id) => {
  item.hide = !item.hide;
  // this.buttonIcon=item.field.hide?"el-icon-remove-outline": "el-icon-circle-plus-outline";
  // this.$set(item, "hide", item.hide);
}
/** 查询所属单位，责任人，告警标签信息 */
const queryAssetZoneDutyTags = () => {
  state.infoForm.zoneName = props.assetItem.model.zoneName__label;
  state.infoForm.assetDuty = props.assetItem.model.assetDuty;
  console.log("state.infoForm.zoneName", state.infoForm.zoneName)
  let params = {};
  params['ipAddress'] = props.assetItem.model.ipAddress;
  console.log('queryAssetZoneDutyTags', params);
  queryAssetZoneDutyTagsApi(params).then(res => {
    state.infoForm.eventTags = res['data'];
  })
}
/** 查询资产详情-基本信息 */
const queryDetailBase = () => {
  state.groupAssetInfos.splice(0, state.groupAssetInfos.length);
  queryDetailBaseApi(detailParams.value).then(res => {
    let assetInfos = res.data || [];
    state.groupAssetInfos.push(...assetInfos);
    for (var i = 0; i < state.groupAssetInfos.length; i++) {
      if (!state.groupAssetInfos[i].children) {
        continue;
      } else {
        for (var ii = 0; ii < state.groupAssetInfos[i].children.length; ii++) {
          var item = state.groupAssetInfos[i].children[ii];
          if (item.showType == 'objectTable') {
            state.ots[item.code] = item.code;
          }
        }
      }
    }
  });
}

const trackingTimeline = ref(null);
/** 查询资产详情-变更追踪 */
const queryChangeTracks = () => {
  if (pageNum.value == -1) {
    ElMessage.info("已经没有数据了");
    return;
  }
  // if (pageNum.value == 1) {
  //   state.changeTracks.splice(0, state.changeTracks.length);
  //   console.log("state.changeTracks.length",state.changeTracks.length,state.changeTracks)
  // }
  // let height = 0;
  // if (trackingTimeline.value?.$el) {
  //   let clientRect = trackingTimeline.value.$el.getBoundingClientRect();
  //   height = clientRect.height;
  // }
  pageNum.value = pageNum.value + 1;
  // console.log("queryDetailChangeTrack", pageNum.value);
  queryDetailChangeTrack({
    ...detailParams.value,
    pageNum: state.pageNum,
    pageSize: 10,
  }).then(res => {
    let changeTracksC = res.data || [];

    // if (height > 0 || true) {
    //   nextTick(() => {
    //     trackingTimeline.value.$el.scrollTop = height;
    //     console.log("trackingTimeline.value.$el", trackingTimeline.value.$el)
    //   });
    // }
    console.log("changeTracksC.length == 0", changeTracksC.length == 0)
    if (changeTracksC.length == 0) {
      if (pageNum.value >= 2) {
        ElMessage.info("已经没有数据了");
      }
      pageNum.value = -1;
      return;
    } else {
      state.changeTracks.push(...changeTracksC);
    }
  }).catch((err) => {
    console.log(err);
  });
}
/** 查询拓扑关系 */
const queryDetailRelations = () => {
  queryDetailRelationsApi(detailParams.value).then(res => {
    groupTypes.value = res.data || [];
  });
}
/** 查询详情的端口 */
const queryDetailPort = () => {
  queryDetailPortApi(detailParams.value).then(res => {
    portList.value = res.data;
  });
}

/** 是否存在属性变更 */
const hasPropertyChanges = (items) => {
  return (items || []).find(item => item.type == "1");
}
/** 是否存在属性变更 */
const hasRelationChanges = (items) => {
  return (items || []).find(item => item.type == "2");
}
/** 合并属性变更，如果没有返回空 */
const getPropertyChanges = (items) => {
  return (items || []).filter(item => item.type == "1");
}

/** 合并关系变更，如果没有返回空 */
const getRelationChanges = (items) => {
  return (items || []).filter(item => item.type == "2");
}
/** tab切换 */
const handleTabClick = (tab) => {
  if (tab.props.name == "topo") {
    showTopo.value = true;
  }
}

const emits = defineEmits(['update:visible'])
const handleClose = () => {
  drawer.value = false;
  emits("update:visible", false);
}


provide('getGlobalParams', getGlobalParams);
provide('globalParams', queryParams);



watch(() => props.visible, (val => {
  drawer.value = val;
  if (val) {
    console.log("() => props.visible", val)
    handleOnShow();
  }
}), { deep: true, immediate: true })
watch(() => props.assetItem, (val => {
  console.log(props.assetItem);
  let ipAddress = val?.model?.ipAddress;
  console.log(ipAddress);
  ipList.value = [];
  ipList.value.push(ipAddress);
}), { deep: true, immediate: true })

</script>

<style lang="scss" scoped>
.asset-detail {
  ::v-deep(.el-drawer__header) {
    margin-bottom: 10px;
  }

  .zoneDuty {
    ::v-deep(.el-form-item) {
      margin-bottom: 12px !important;

      .el-form-item__label {
        line-height: 14px !important;
        font-weight: 500;
        font-size: 14px;
        color: #6f6e89;
      }

      .el-form-item__content {
        line-height: 14px !important;
        font-weight: bold;
        font-size: 14px;
        color: #3c4a54;
      }
    }
  }

  .title {
    font-size: 18px;
    font-family: PingFang SC-Bold, PingFang SC;
    font-weight: bold;
    line-height: 18px;
    display: flex;
    align-items: center;

    .icon {
      margin-right: 5px;
    }

    .label {
      color: #3c4a54;
    }
  }

  .asset-detail-tabs {
    margin: 10px 20px;
  }

  .asset-tab-item {
    height: calc(100vh - 158px);
    overflow: auto;
    overflow-x: hidden;

    .detail-title {
      font-size: 16px;
      font-weight: bold;
      margin-top: 20px;
      display: flex;

      span {
        margin-left: 6px;
      }
    }

    .detail-form {
      margin-top: 20px;

      ::v-deep(.el-form-item) {
        margin-bottom: 12px !important;

        .el-form-item__label {
          line-height: 14px !important;
          font-weight: 500;
          font-size: 14px;
          color: #6f6e89;
        }

        .el-form-item__content {
          line-height: 14px !important;
          font-weight: bold;
          font-size: 14px;
          color: #3c4a54;
        }
      }
    }

    .tracking-timeline {
      padding-left: 100px;
      max-height: calc(100% - 120px);
      overflow: auto;
      scroll-behavior: smooth;

      ::v-deep(.el-timeline-item__icon) {
        background: unset !important;
        color: unset !important;
        font-size: 15px !important;
        margin-left: -2px;
      }

      ::v-deep(.el-timeline-item__wrapper) {
        .el-timeline-item__timestamp {
          margin-left: -120px;
          font-size: 14px;
          font-family: PingFang SC-Bold, PingFang SC;
          font-weight: bold;
          color: #3c4a54;
          line-height: 14px;
        }

        .el-timeline-item__content {
          flex: 2;
          margin-top: -20px;

          .change-time-item {
            margin-bottom: 15px;

            .time-label {
              font-size: 14px;
              font-family: DIN-Medium, DIN;
              font-weight: 500;
              color: #6f6e89;
              line-height: 14px;
              margin: 8px 0;
            }

            .text-items {
              margin-left: 10px;

              .text-item {
                font-size: 14px;
                font-family: PingFang SC-Bold, PingFang SC;
                line-height: 18px;
                word-break: break-all;
                padding-bottom: 4px;
              }
            }
          }
        }
      }
    }

    .tracking-timeline2 {
      padding-left: 100px;
      max-height: calc(100% - 80px);
      overflow: auto;

      .primary-timeline-item {
        ::v-deep(.el-timeline-item__wrapper) {
          display: flex;
          flex-direction: row-reverse;
          //align-items: center;

          .el-timeline-item__timestamp {
            margin-left: -120px;
            font-size: 14px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #3c4a54;
            line-height: 14px;
          }

          .el-timeline-item__content {
            flex: 2;
            margin-top: -20px;

            .timeline-text {
              padding: 5px;
              display: inline-block;
              font-size: 14px;

              .text-items {
                .text-item {
                  word-break: break-all;
                }
              }
            }
          }
        }
      }
    }

    .change-more {
      font-size: 14px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      color: #1890ff;
      line-height: 14px;
      margin: 20px;
      cursor: pointer;
    }
  }
}
</style>
