<template>
  <el-form class="asset-property-form detail-form" label-width="125px" label-position="right" label-suffix="：">
    <el-row v-if="props.columns.length > 0">
      <el-col v-for="(column, j) in props.columns" :span="12" :key="j" v-show="j < 8">
        <el-form-item :label="column['name']">
          <div slot="label" :title="column['name']" style="
              width: 100%;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            ">
            {{ column['name'] }}：
          </div>
          <template v-if="$scopedSlots.item">
            <slot name="item" v-bind="{ item, column }"></slot>
          </template>
          <span v-else class="item-value" :style="getPropertyStyle(column)">{{
            item[column['field']]
            }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-tree class="empty-desc" v-else empty-text="字段信息没有维护"></el-tree>
  </el-form>
</template>

<script setup lang="ts">

const props = defineProps({
  columns: Array,
  item: Object,
  propertyStyleFn: Function,
})

const getPropertyStyle = (column) => {
  if (typeof props.propertyStyleFn == "function") {
    return props.propertyStyleFn(props.item, column);
  }
  return {};
}


</script>

<style lang="scss" scoped>
.asset-property-form {
  .empty-desc {
    :deep(.el-tree__empty-text) {
      font-size: 15px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 10px !important;

    .el-form-item__label {
      line-height: 14px !important;
      font-weight: 500;
      font-size: 14px;
      color: #6f6e89;
    }

    .el-form-item__content {
      line-height: 14px !important;
      font-weight: bold;
      font-size: 14px;
      color: #3c4a54;

      .item-value {
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
</style>
