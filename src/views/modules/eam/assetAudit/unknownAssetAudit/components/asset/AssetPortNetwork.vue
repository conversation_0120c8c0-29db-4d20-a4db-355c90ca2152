<template>
  <div class="asset-port-network">
    <template v-if="groupPorts.length > 0">
      <!-- 端口图片排列 -->
      <div class="asset-port-groups" style="max-height: 498px; overflow: auto" @click="selectPortIndex = -1">
        <div class="port-group" v-for="(g, k) in groupPorts" :key="`group-${k}`">
          <div class="port-group-body">
            <el-row>
              <el-col class="port-item" v-for="(odd, index) in 6" :key="index" :span="4"
                v-show="k * 12 + odd * 2 - 1 <= ports.length" @click.native.stop="selectPort(k * 12 + odd * 2 - 1)">
                <el-tooltip effect="dark" placement="top" :enterable="false">
                  <div slot="content" style="padding: 4px; width: 180px">
                    <el-row style="margin-top: 5px">
                      <el-col :span="9">名称: </el-col>
                      <el-col :span="15">{{
                        getPort(k * 12 + odd * 2 - 1).portName
                        }}</el-col>
                    </el-row>
                    <el-row style="margin-top: 5px">
                      <el-col :span="9">带宽: </el-col>
                      <el-col :span="15">{{
                        getPort(k * 12 + odd * 2 - 1).portSpeed || 0
                      }}(Mbps)</el-col>
                    </el-row>
                  </div>
                  <div class="port-item">
                    <span class="seq-odd" :class="{
                      'seq-odd-open': isOpenPort(k * 12 + odd * 2 - 1),
                    }">{{ k * 12 + odd * 2 - 1 }}</span>
                    <img :src="getImgSrc(k * 12 + odd * 2 - 1)" />
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
            <el-row style="margin-top: 20px">
              <el-col class="port-item" v-for="(even, index) in 6" :key="index" :span="4"
                v-show="k * 12 + even * 2 <= ports.length" @click.native.stop="selectPort(k * 12 + even * 2)">
                <el-tooltip effect="dark" placement="bottom" :enterable="false">
                  <div slot="content" style="padding: 4px; width: 180px">
                    <el-row style="margin-top: 5px">
                      <el-col :span="9">名称: </el-col>
                      <el-col :span="15">{{
                        getPort(k * 12 + even * 2).portName
                        }}</el-col>
                    </el-row>
                    <el-row style="margin-top: 5px">
                      <el-col :span="9">带宽: </el-col>
                      <el-col :span="15">{{
                        getPort(k * 12 + even * 2).portSpeed || 0
                      }}(Mbps)</el-col>
                    </el-row>
                  </div>
                  <div class="port-item">
                    <img :src="getImgSrc(k * 12 + even * 2)" />
                    <span class="seq-even" :class="{
                      'seq-even-open': isOpenPort(k * 12 + even * 2),
                    }">{{ k * 12 + even * 2 }}</span>
                  </div>
                </el-tooltip>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 端口表格数据 -->
      <div class="asset-port-table" style="margin-top: 10px" :style="fitTableStyle">
        <im-table style="padding: 0 10px; height: 100%" :columns="portColumns" :model="tablePorts"
          fit-height></im-table>
      </div>
    </template>
    <el-empty v-else></el-empty>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed } from "vue";

const props = defineProps({
  type: String,
  portList: Array,
})

const portColumns = reactive([
  { prop: "portName", label: "端口名称" },
  { prop: "portIndex", label: "端口索引" },
  { prop: "portAlias", label: "端口别名" },
  { prop: "portDesc", label: "端口描述" },
  { prop: "portSpeed", label: "端口带宽(Mbps)" },
  { prop: "portDux", label: "双工模式" },
  { prop: "portStatus", label: "端口状态" },
])
const selectPortIndex = ref(-1);

const ports = computed(() => {
  let portListValue = props.portList || [];
  return portListValue;
});
const groupPorts = computed(() => {
  let len = ports.value.length;
  // 按12个端口分组
  let lastGroup = len % 12;
  let lastGroupNum = lastGroup == 0 ? 12 : lastGroup;
  let groupCount = lastGroup == 0 ? len / 12 : (len - lastGroup) / 12 + 1;
  return Array.from({ length: groupCount }, (_, index) => {
    return {
      groupIndex: index,
      portNum: index == groupCount - 1 ? lastGroupNum : 12,
    };
  });
});
const tablePorts = computed(() => {
  if (selectPortIndex.value == -1) return ports.value;
  return ports.value.filter((port, index) => selectPortIndex.value == index);
});
const fitTableStyle = computed(() => {
  let len = ports.value.length;
  let rows = len % 24 == 0 ? len / 24 : (len - (len % 24)) / 24;
  rows = Math.min(rows, 3);
  return {
    height: `calc(100% - ${rows * 166 + 10}px`,
  };
});


const portItemStyle = (index) => {
  return {};
}
const isOpenPort = (seq) => {
  let index = seq - 1;
  let port = ports.value[index];
  return port && port['portStatus'] == "开启";
}
const getPort = (seq) => {
  let index = seq - 1;
  return ports.value[index] || {};
}
const getImgSrc = (seq) => {
  let isOpenPortC = isOpenPort(seq);
  if (isOpenPortC) {
    return import("@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/images/network-port-open.png");
  } else {
    return import("@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/images/network-port.png");
  }
}
const selectPort = (seq) => {
  selectPortIndex.value = seq - 1;
}





</script>

<style lang="scss" scoped>
.asset-port-network {
  position: relative;
  overflow: hidden;
  // margin: 0 -20px;
  // background: #F5F5F5;

  .asset-port-groups {
    .port-group {
      width: 50%;
      height: 166px;
      padding: 10px;
      float: left;

      .port-group-body {
        width: 100%;
        height: 100%;
        padding: 10px 40px;
        background: #3c4a54;

        .port-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #babdcb;
          cursor: pointer;

          .seq-odd {
            margin-bottom: 5px;
          }

          .seq-odd {
            margin-bottom: 5px;

            &.seq-odd-open {
              color: #25c691;
            }
          }

          .seq-even {
            margin-top: 5px;

            &.seq-even-open {
              color: #25c691;
            }
          }
        }
      }
    }
  }
}
</style>
