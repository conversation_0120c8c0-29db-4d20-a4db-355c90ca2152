<template>
  <div class="asset-topo">
    <div class="topo-legend">
      <div class="legend" v-for="(relation, i) in relations" :key="i">
        <div :style="{ background: relation.color }"></div>
        <span>{{ relation.label }}</span>
      </div>
    </div>
    <!-- <img src="../../images/topo/physical-server-select.png" alt=""> -->
    <div class="topo-container">
      <div class="wrapper" ref="wrapper">
        <!-- 连线使用svg 生成 -->
        <svg class="topo-svg" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <template v-for="(pathData, index) in pathDataList" :key="index">
            <path  :stroke="pathData.color" fill="none" stroke-width="1.5" :d="pathData.d"></path>
          </template>
        </svg>

        <div class="center node" :style="centerStyle" @click="currentNode = null">
          <img :src="currentNode == null
            ? getTopoImageSelect(props.assetItem.model)
            : getTopoImage(props.assetItem.model)
            " style="width: 100px; height: 100px" />
          <div>{{ centerLabel }}</div>
        </div>
        <div :id="'node-' + index" class="node" v-for="(node, index) in nodes" :key="'node-' + index"
          :style="nodeStyle(index)" @click="currentNode = node">
          <img :src="currentNode == node
            ? getTopoImageSelect(node)
            : getTopoImage(node)
            " />
          <div class="node-label">{{ node.label }}</div>
        </div>
      </div>
    </div>

    <div class="asset-topo-list">
      <div v-if="showList" style="height: 100%; width: 100%; overflow: auto">
        <template v-if="children.length > 0">
          <el-card class="asset-topo-card" :header="item.name" v-for="(item, i) in children" :key="i"
            style="margin-bottom: 10px; box-shadow: unset">
            <div slot="header" class="clearfix title">
              <div style="display: flex; align-items: center">
                <!--                <span class="icon el-icon-s-data"></span>-->
                <img class="icon" :src="getCategoryImage(item)" style="width: 16px; transform: scale(1.5)" />
                <span class="label">{{ item.name || item.ipAddress }}</span>
              </div>
            </div>
            <asset-property-form style="margin: 10px" :item="item" :columns="getColumns(item)"></asset-property-form>
          </el-card>
        </template>
        <el-empty v-else></el-empty>
      </div>
      <el-empty v-else description="正在渲染视图，请稍后..."></el-empty>
    </div>

  </div>
</template>

<script setup lang="ts">
// import resizeObserver from "itsm-common/src/components/event/resizeObserver";
import AssetPropertyForm from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/asset/AssetPropertyForm.vue";
import { useCategoryInfo } from '@/views/modules/eam/assetAudit/unknownAssetAudit/mixin/asset'
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";

const props = defineProps({
  assetItem: Object,
  groupTypes: Array,
  categoryColumns: Object,
});


const {
  getCategoryInfo,
  getCategoryImage,
  getCategoryName,
  getTopoImage,
  getTopoImageSelect,
  // getColumns,
  getPortType,
} = useCategoryInfo(props);
let relations01 = [
  {
    value: "connection",
    label: "连接关系",
    color: "#1890FF",
  },
  {
    value: "fatherAndSon",
    label: "父子关系",
    color: "#FFB72B",
  },
  {
    value: "constitute",
    label: "构成关系",
    color: "#0FBE55",
  },
  {
    value: "other",
    label: "其他关系",
    color: "gray",
  },
];
let relationColors01 = {};
relations01.forEach(relation => {
  relationColors01[relation.value] = relation.color;
});


const state = reactive({
  containerOffset: {
    left: 0,
    top: 0,
  },
  centerX: 0,
  centerY: 0,
  rx: 100,
  ry: 100,
  relations01,
  relationColors01,
  layouts: [],
  pathDataList: [],
  currentNode: null,
  showList: false,
})
const {
  containerOffset, centerX, centerY, rx, ry,
  relations01: relations, relationColors01: relationColors,
  layouts, pathDataList, currentNode, showList
} = toRefs(state);


const centerLabel = computed(() => {
  const { name } = props.assetItem;
  return `${name}`;
});
const centerStyle = computed(() => {
  return {
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
  };
});

const nodes = computed(() => {
  let groupTypes = props.groupTypes;
  groupTypes = groupTypes.filter(gt =>
    gt.children != null && gt.children.length > 0
  );
  return groupTypes.map(groupType => {
    const { children, groupValue, groupLabel, relation } = groupType;
    const { name, ipAddress } = (children && children[0]) || {};
    return {
      label:
        children?.length == 1
          ? `${name}(${ipAddress})`
          : `${groupLabel}(${children.length})`,
      groupValue,
      children,
      relation,
      category_id: groupValue,
    };
  });
});

const children = computed(() => {
  if (state.currentNode) {
    return state.currentNode.children;
  } else {
    let children = [];
    nodes.value.forEach(node => {
      children.push(...node.children);
    });
    return children;
  }
});


const wrapper = ref(null);
onMounted(() => {
  nextTick(init);
  // resizeObserver.observe(wrapper.value, initLayoutSize);
})

const init = () => {
  let { width, height } = wrapper.value.getBoundingClientRect();
  rx.value = width / 2;
  ry.value = height / 2;
  centerX.value = rx.value;
  centerY.value = ry.value;
  updateLayouts();
}
/** 解决体验问题 */
const initLayoutSize = () => {
  let wrapperC = wrapper.value;
  if (!wrapperC) return;
  let { width, height, left, top } = wrapperC.getBoundingClientRect();
  if (width == 0 || height == 0) {
    showList.value = false;
    return;
  }
  nextTick(() => {
    setTimeout(() => {
      showList.value = true;
    }, 0);
  });
  // 如果没有布局数据
  state.pathDataList.splice(0, state.pathDataList.length);
  if (state.layouts.length == 0) return;
  init();
  Object.assign(state.containerOffset, {
    left,
    top,
  });
  updatePathDataList();
}
const updateLayouts = () => {
  let n = nodes.value.length;
  let step = 1;
  let increment = (step * (2 * Math.PI)) / n;
  let angle = 0;
  layouts.value.splice(0, layouts.value.length);
  for (let i = 0; i < n; i++) {
    let x = 50 + 50 * Math.cos(angle);
    let y = 50 - 50 * Math.sin(angle);
    layouts.value.push({
      x,
      y,
      angle,
      node: nodes.value[i],
    });
    angle += increment;
  }
}
const updatePathDataList = () => {
  let wrapperC = wrapper.value;
  if (!wrapperC) return;
  let { width, height } = wrapperC.getBoundingClientRect();
  if (width > 0 || height > 0) {
    // 计算各个节点实际坐标，绘制连线
    layouts.value.forEach(layout => {
      // 中心百分比位置
      let { x, y, node } = layout;
      let X = ((width * x) / 100).toFixed(4);
      let Y = ((height * y) / 100).toFixed(4);
      state.pathDataList.push({
        d: `M${centerX.value}, ${centerY.value}L${X},${Y}`,
        color: state.relationColors01[node.relation],
      });
    });
  }
}

const nodeStyle = (index) => {
  let nodeStyle = {};
  if (layouts.value[index]) {
    Object.assign(nodeStyle, {
      left: `${layouts.value[index].x}%`,
      top: `${layouts.value[index].y}%`,
    });
  }
  return nodeStyle;
}

const getColumns = (item) => {
  let category_id = item.category_id;
  let categoryColumns = props.categoryColumns || {};
  let columns = categoryColumns[category_id]?.columns || [];
  return columns;
}

const getCategoryIcon = (model) => {
  let { category_id } = model;
  let category = props.categoryColumns[category_id];
  let { position_image } = category || {};
  return position_image;
}

onBeforeUnmount(() => {
  // resizeObserver.unobserve(wrapper.value, initLayoutSize);
})

watch(() => props.groupTypes, (val) => {
  updateLayouts();
  setTimeout(() => {
    initLayoutSize();
  }, 500);
}, { deep: true })



</script>

<style lang="scss" scoped>
.asset-topo {
  position: relative;

  .topo-legend {
    position: absolute;
    left: 0;
    top: 0;
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .legend {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      div {
        flex: 1;
        height: 3px;
        margin-right: 10px;
      }

      .connect {
        background: #1890ff;
      }

      .parent {
        background: #ffb72b;
      }

      .create {
        background: #0fbe55;
      }
    }
  }

  .topo-container {
    position: absolute;
    left: 150px;
    right: 80px;
    padding: 40px;
    top: 0;
    bottom: 55%;

    .wrapper {
      position: relative;
      width: 100%;
      height: 100%;

      .topo-svg {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .node {
        position: absolute;
        text-align: center;
        cursor: pointer;
        width: 75px;
        z-index: 200;
        transform: translate(-50%, -50%);

        .node-label {
          margin-top: 2px;
        }
      }

      .path {
        position: absolute;
        background: #0a79a1;
        height: 1px;
      }
    }
  }

  .asset-topo-list {
    position: absolute;
    background: #f5f5f5;
    padding: 10px;
    left: 0;
    right: 0;
    top: 45%;
    bottom: 0;

    .asset-topo-card {
      border: unset;
      box-shadow: unset;

      :deep(.el-card__header) {
        padding: 15px;

        .title {
          font-size: 18px;
          font-family: PingFang SC-Bold, PingFang SC;
          font-weight: bold;
          line-height: 18px;

          .icon {
            margin-right: 10px;
          }

          .label {
            //color: #1890FF;
          }
        }
      }
    }
  }
}
</style>
