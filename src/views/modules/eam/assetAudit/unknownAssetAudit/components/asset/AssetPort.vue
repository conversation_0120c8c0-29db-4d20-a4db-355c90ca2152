<template>
  <div class="asset-port">
    <div v-if="(props.portList || []).length > 0" style="text-align: right; font-size: 14px; padding: 10px 0">
      <label>只看高危：</label>
      <el-switch v-model="showHightLevel" active-color="#FF4D4F"> </el-switch>
    </div>
    <el-row class="port-row" v-if="ports.length > 0" :gutter="20" style="height: calc(100% - 42px); overflow: auto">
      <el-col class="port-item" :span="fitSpan" v-for="(port, index) in ports" :key="index"
        :style="portItemStyle(index)">
        <div class="port-item-body">
          <div class="port-header">
            <img src="./images/port.png" />
            <div>{{ port['portName'] }}</div>
          </div>

          <div class="port-center" :title="port['portSafeDes']">
            <div class="level">
              <div class="level-value" :class="{ 'level-value-high': port['highRisk'] == '是' }">
                {{ port['highRisk'] || "否" }}
              </div>
              <div class="level-label">是否高危</div>
            </div>
            <div class="status">
              <el-popover :disabled="(port['changeList'] || []).length == 0" placement="right" width="200" trigger="hover">
                <div class="port-history-changes" style="text-align: center; max-height: 400px; overflow: auto">
                  <div class="port-history-change" v-for="(changeItem, i) in port['changeList'] || []" :key="i">
                    <span>{{ changeItem.CREATED_TIME }}</span>
                    <span :style="{
                      color:
                        changeItem.VALUE_AFTER == '开启'
                          ? '#0fbe55'
                          : '#d3d3e0',
                    }">{{ changeItem.VALUE_AFTER }}</span>
                  </div>
                </div>
                <div class="status-value" slot="reference" :class="{
                  'status-value-open': port['portStatus'] == '开启',
                  'status-value-unknown':
                    (port['portStatus'] || '未知') == '未知',
                }">
                  {{ port['portStatus'] || "未知" }}
                </div>
              </el-popover>

              <div class="status-label">端口状态</div>
            </div>
          </div>

          <el-row :gutter="8" style="margin-bottom: 20px">
            <el-col class="label" :span="8">服务名称：</el-col>
            <el-col class="value" :span="16">{{
              port['serviceName'] || "-"
              }}</el-col>
          </el-row>

          <el-row :gutter="10" style="margin-bottom: 20px">
            <el-col class="label" :span="8">服务版本：</el-col>
            <el-col class="value" :span="16">{{
              port['serviceVersion'] || "-"
              }}</el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-empty v-else></el-empty>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed } from "vue";


const props = defineProps({
  type: String,
  portList: Array,
});

const showHightLevel = ref(false);

const ports = computed(() => {
  let portList = props.portList || [];
  if (!showHightLevel.value) {
    return portList;
  }
  return portList.filter(port => port['highRisk'] === "是");
});
const fitSpan = computed(() => {
  let len = ports.value.length || 0;
  if (len < 3 && len > 0) {
    return 24 / len;
  }
  return 8;
});

const portItemStyle = (index) => {
  return {};
}
</script>

<style lang="scss" scoped>
.asset-port {
  position: relative;
  overflow-x: hidden;
  padding: 0 20px;
  // background: #F5F5F5;

  .port-row {
    padding-top: 10px;
    background: #f5f5f5;
  }

  .port-item {
    height: 226px;
    margin-bottom: 20px;

    .port-item-body {
      width: 100%;
      height: 100%;
      padding: 20px;
      background: #fff;

      .port-header {
        display: flex;
        align-items: center;

        img {
          margin-right: 10px;
        }

        div {
          font-size: 16px;
          font-family: PingFang SC-Bold, PingFang SC;
          font-weight: bold;
          color: #3c4a54;
          line-height: 16px;
        }
      }

      .port-center {
        height: 82px;
        margin: 20px 0;
        background: #3c4a54;
        color: #f3f3f9;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .level {
          display: flex;
          align-items: center;
          flex-direction: column;

          &-value {
            font-size: 18px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #d3d3e0;
            line-height: 18px;
            margin-bottom: 10px;

            &-high {
              color: #ff4d4f;
            }
          }

          &-label {
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #f3f3f9;
            line-height: 14px;
          }
        }

        .status {
          display: flex;
          align-items: center;
          flex-direction: column;

          &-value {
            font-size: 18px;
            font-family: PingFang SC-Bold, PingFang SC;
            font-weight: bold;
            color: #d3d3e0;
            line-height: 18px;
            margin-bottom: 10px;

            &-open {
              color: #0fbe55;
            }

            &-unknown {
              color: #ba36d6;
            }
          }

          &-label {
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #f3f3f9;
            line-height: 14px;
          }
        }
      }
    }
  }

  .label {
    font-size: 14px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #a8a8be;
    line-height: 14px;
  }

  .value {
    font-size: 14px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #3c4a54;
    line-height: 14px;
  }
}

.port-history-changes {
  .port-history-change {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 3px 2px;
  }
}
</style>
