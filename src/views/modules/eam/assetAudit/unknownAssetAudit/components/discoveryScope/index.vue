<template>
  <el-form :model="state.dataForm" :rules="rules" ref="dataForm" label-width="100px">
    <el-form-item label="扫描范围" prop="ipAddress" :rules="[
      {
        validator: validateKey,
        required: true,
        trigger: ['blur', 'change'],
      }
    ]" :class="{ range: iprange }">

      <el-row>
        <el-col :span="22">
          <el-input :disabled="props.disabled" type="textarea" :rows="2" v-model="state.dataForm.ipAddress"></el-input>
          <!--          <span style="color: red;font-size: 12px">{{ iptooltip }}</span>-->
        </el-col>
        <el-col :span="2">
          <el-popover placement="bottom-end" width="300" trigger="hover">
            <p>
              支持单IP，同一网段IP范围、同一网段IP和子网；
            </p>
            <p>格式如下（一行一个）</p>
            <p>单个IP：***********</p>
            <p>IP范围：***********-*************</p>
            <p>IP范围：***********-255</p>
            <!--                <p>IP/子网掩码：***********/*************</p>-->
            <p>IP/子网掩码：***********/24</p>
            <p>说明：为保证扫描效率，子网掩码需大于等于16</p>
            <el-button style="margin-left: 5px" slot="reference" icon="el-icon-question" circle></el-button>
          </el-popover>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onActivated, onDeactivated } from "vue";
const props = defineProps({
  ipScopeList: {
    type: String,
    default: ""
  },
  disabled: {
    type: Boolean,
  }
});

const state = reactive({
  dataForm: {
    ipAddress: "",
    policyName: {}
  },
  rules: {},
  iprange: false,
  iptooltip: "",
});
const {
  dataForm,
  rules,
  iprange,
  iptooltip
} = toRefs(state);

onMounted(() => {
  state.dataForm.ipAddress = props.ipScopeList;
})

const validateKey = (list, val, callback) => {
  let enterReg = /[\n]/g;
  //ipList判断行数
  let ipList = enterReg.exec(val)?.input?.split("\n");
  if (ipList) {
    //多行
    if (ipList.length < 17) {
      for (let i in ipList) {
        regRule(callback, ipList[i], i);
      }
    } else {
      return;
    }
  } else {
    //单行范围ip类型
    regRule(callback, val);
  }
}

const regRule = (callback, val, index?) => {
  //第一位1-254
  let ipSpotRegOne = "(?:25[0-4]|2[0-4]\\d|1?[1-9]\\d?|1[0-9]\\d?)\\.";
  //0-255.
  let ipSpotReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.";
  //0-255
  let ipNoSoptReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)";
  //0-254.
  let ipLessReg = "(?:25[0-4]|2[0-4]\\d|1?\\d\\d?)\\.";
  //链接符为-
  let currentRangeReg = `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\-(?:\\1\\2(?<endThree>${ipSpotReg}))?(?<end>${ipNoSoptReg}))?$`;
  //连接符为/
  let currentMaskReg =
    `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\/(?<end>(?:(?:(?:1[6-9])|(?:2[0-9])|(?:3[0-2])))))?$`;
  let checkRangeReg = new RegExp(currentRangeReg, "g");
  let checkMaskReg = new RegExp(currentMaskReg, "g");
  if (checkRangeReg.exec(val)) {
    //规则2、3
    //判断 后面C段 > 前面C段
    //判断 后面C段 = 前面C段 =>后面D段 > 前面D段
    checkRangeReg.lastIndex = 0;
    let data = checkRangeReg.exec(val);
    if (!data.groups.endThree) {
      //没有第endThree时
      iprange.value = false;
      iptooltip.value = "";
      return;
    } else {
      if (data.groups.three < data.groups.endThree) {
        iprange.value = false;
        iptooltip.value = "";
        return;
      } else if (data.groups.three == data.groups.endThree) {
        if (data.groups.four < data.groups.end) {
          iprange.value = false;
          iptooltip.value = "";
          callback()
          return;
        } else {
          callback()
          return;
        }
      } else {
        callback()
        return;
      }
    }
  } else if (checkMaskReg.exec(val)) {
    //  规则4、5
    checkMaskReg.lastIndex = 0;
    let data = checkMaskReg.exec(val);
    if (data.groups.one <= 191 && data.groups.end <= 32 && data.groups.end >= 16) {
      iptooltip.value = "";
      iprange.value = false;
    } else if (data.groups.one <= 223 && data.groups.end <= 32 && data.groups.end >= 24) {
      iprange.value = false;
      iptooltip.value = "";
      callback()
      return;
    } else {
      callback()
      return;
    }
  } else {
    // 没有匹配上
    iprange.value = true;
    iptooltip.value = index ? `请检查第${index / 1 + 1}行IP范围` : `请输入正确IP范围`;
    callback(iptooltip.value)
  }
}

onActivated(() => {
  state.dataForm.ipAddress = props.ipScopeList;
})
const policyName = ref();
const policyNameTooltip = ref();
watch(state.dataForm.policyName, (newV: String) => {
  if (newV.length > 0) {
    policyName.value = true
    policyNameTooltip.value = ""
  } else {
    policyName.value = false
    policyNameTooltip.value = "请输入策略名称"
  }
}, { deep: true })

// watch(moveDeptFormData.destinationParentId, (newV) => {
//   if (newV) {
//     iptooltip.value = "";
//     iprange.value = false;
//   }
// }, { deep: true })

watch(iprange, (val) => {
  if (val) {
    document.onkeydown = function (e) {
      //捕捉回车事件
      var ev = (typeof event != "undefined") ? window.event : e;
      if (ev['keyCode'] == 13 || event['which'] == 13) {
        return false;
      }
    };
  } else {
    document.onkeydown = function (e) {
      //捕捉回车事件
      var ev = (typeof event != "undefined") ? window.event : e;
      if (ev['keyCode'] == 13 || event['which'] == 13) {
        return true;
      }
    };
  }
}, { deep: true })


</script>

<style lang="scss" scoped>
.el-radio {
  margin: 0;
  padding: 0;
}

::v-deep.range {

  .el-textarea__inner {
    background: rgba(255, 0, 0, 0.2) !important;
  }

}
</style>
