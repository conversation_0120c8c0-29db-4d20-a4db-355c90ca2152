<template>
  <div v-loading="loading" ref="charts" :style="{ width: '100%', height: props.heightCharts }"></div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick } from "vue";
import * as echarts from "echarts"
import { debounce } from "lodash";

const props = defineProps({
  heightCharts: {
    type: String,
    default: '300px'
  },
  option: Object
});

const chartsInit = ref(null);
const charts = ref(null);
const loading = ref(false);
const ro = ref(null);


const domChange = () => {
  chartsInit.value?.resize();
}
const resizeHandler = debounce(domChange, 200);
onMounted(() => {
  console.log('init');
  init();
})

watch(()=> props.option, (val) => {
  console.log(val);
  init();
}, { deep: true })

const init = () => {
  loading.value = true;
  if (chartsInit.value) {
    chartsInit.value.dispose();
  }
  chartsInit.value = echarts.init(
    charts.value,
    '',
  );
  // resizeOn();
  console.log(props.option)
  if (props.option) {
    chartsInit.value.setOption(props.option);
  }
  loading.value = false;
}

const resizeOn = () => {
  // ro.value = new ResizeObserver(() => {
  //   resizeHandler();
  // });
  // ro.value.observe(charts.value);
  // window.addEventListener("resize", this.resizeHandler);
}

</script>
<style lang="scss" scoped>
.CompCharts {
  .el-main {
    padding: 0;
    margin-left: 6px;
  }
}
</style>
