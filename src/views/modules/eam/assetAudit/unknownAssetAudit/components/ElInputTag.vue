<template>
  <div class="el-input-tag input-tag-wrapper" :class="[props.size ? 'el-input-tag--' + props.size : '']"
    @click="focusTagInput">
    <el-tag v-for="(tag, idx) in innerTags" v-bind="$attrs" :key="tag" :size="props.size" :closable="!readOnly"
      :disable-transitions="false" @close="remove(idx)">
      {{ tag }}
    </el-tag>
    <input ref="tagInputRef" v-if="!props.readOnly" class="tag-input" @input="inputTag" :value="newTag"
      @keydown.delete.stop="removeLastTag" @keydown="addNew" @blur="addNew" />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";
const props = defineProps({
  value: {
    type: Array,
    default: () => [],
  },
  addTagOnKeys: {
    type: Array,
    default: () => [13, 188, 9],
  },
  readOnly: {
    type: Boolean,
    default: false,
  },
  size: String,
})
const state = reactive({
  newTag: "",
  innerTags: [...props.value],
})
watch(() => props.value, (val) => {
  state.innerTags = [...props.value];
}, { deep: true });


const tagInputRef = ref(null);
const focusTagInput = () => {
  if (props.readOnly || !tagInputRef.value) {
    return;
  } else {
    tagInputRef.value.focus();
  }
}
const inputTag = (ev) => {
  state.newTag = ev.target.value;
}
const addNew = (e) => {
  if (e && !props.addTagOnKeys.includes(e.keyCode) && e.type !== "blur") {
    return;
  }
  if (e) {
    e.stopPropagation();
    e.preventDefault();
  }
  let addSuccess = false;
  if (state.newTag.includes(",")) {
    state.newTag.split(",").forEach(item => {
      if (addTag(item.trim())) {
        addSuccess = true;
      }
    });
  } else {
    if (addTag(state.newTag.trim())) {
      addSuccess = true;
    }
  }
  if (addSuccess) {
    tagChange();
    state.newTag = "";
  }
}
const addTag = (tag) => {
  tag = tag.trim();
  if (tag && !state.innerTags.includes(tag)) {
    state.innerTags.push(tag);
    return true;
  }
  return false;
}
const remove = (index) => {
  state.innerTags.splice(index, 1);
  tagChange();
}
const removeLastTag = () => {
  if (state.newTag) {
    return;
  }
  state.innerTags.pop();
  tagChange();
}
const emits = defineEmits(['input', 'rulesInput'])
const tagChange = () => {
  emits("input", state.innerTags);
  emits("rulesInput");
}
</script>

<style scoped>
.el-form-item.is-error .el-input-tag {
  border-color: #f56c6c;
}

.input-tag-wrapper {
  position: relative;
  font-size: 14px;
  background-color: #fff;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  outline: none;
  padding: 0 10px 0 5px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}

.el-tag {
  margin-right: 4px;
  color: #909399;
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}

::v-deep .el-tag .el-tag__close {
  color: #909399;
}

.tag-input {
  background: transparent;
  border: 0;
  font-size: inherit;
  outline: none;
  padding-left: 0;
  width: 150px;
  color: #000;
}

.el-input-tag {
  min-height: 28px;
}

.el-input-tag--mini {
  min-height: 28px;
  line-height: 28px;
  font-size: 12px;
}

.el-input-tag--small {
  min-height: 32px;
  line-height: 32px;
}

.el-input-tag--medium {
  min-height: 36px;
  line-height: 36px;
}

::v-deep input:not([type]),
::v-deep input[type=""],
::v-deep input[type="text"],
::v-deep input[type="password"],
::v-deep input[type="textarea"] {
  border: 0px solid #dcdfe6;
}
</style>
