<template>
  <div class="objectTableProperty">
    <el-form :model="tableForm" ref="tableForm" label-width="10px">
      <im-table ref="propTable" :data="tableForm.tableData" :columns="dataColumn" border id-key="sortId" stripe>
        <template v-slot:[item.code]="scope" v-for="(item, index) in props.tableColumn">
          <el-form-item :prop="'tableData.' + (scope.row.sortId - 1) + '.' + item.code" :rules="{
            required: props.type != 'add' && props.type != 'edit' ? false : item.rule.request, type: item.rule.type, name: item.rule.name,
            message: item.rule.message, trigger: item.rule.trigger,
            validator: (rule, value, callback) => { constMust(rule, scope.row[item.code], callback, item) }
          }">
            <div style="position:relative;">
              <span v-if="item.property == 'must' && (props.type == 'add' || props.type == 'edit')"
                style="color:red;position:absolute;top:3px;left:-10px;">*</span>
              <span v-if="item.showType == 'number' && item.isAuto != 1">
                <span v-if="item.dataLength != null">
                  <el-input-number v-model="scope.row[item.code]"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"
                    :max="item.dataLength"></el-input-number>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input-number v-model="scope.row[item.code]"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input-number>
                </span>
              </span>
              <span v-if="item.isAuto == 1">
                <span v-if="item.dataLength != null">
                  <el-input type="text" v-model="scope.row[item.code]"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"
                    :max="item.dataLength"></el-input>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input type="text" v-model="scope.row[item.code]"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input>
                </span>
              </span>
              <span v-if="item.showType == 'inputSelect'">
                <el-input-tag :model-value="scope.row[item.code] ? scope.row[item.code].split(',') : []"
                  @update:model-value="val => scope.row[item.code] = val.join(',')"
                  @rulesInput="rulesInput(item, index)"
                  v-if="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1"></el-input-tag>
                <!-- <el-input disabled v-else v-model="scope.row[item.code] != undefined && scope.row[item.code] != null
                  && scope.row[item.code] != '' ? scope.row[item.code].join(',') : scope.row[item.code]"></el-input> -->
              </span>
              <span v-if="item['showType'] == 'moreInput'">
                <!-- <input-multiple v-model="scope.row[item['code']]" :name="item['name']" :category-id="props.categoryIdP"
                  :is-rule="item['isRule']" :ref="setItemRef(item['code'])" @rulesInput="rulesInput(item, index)"
                  v-if="(props.type == 'add' || props.type == 'edit') && item.field.isEdit != 1"></input-multiple> -->
                <!-- <el-input disabled v-else v-model="(scope.row[item.code] != undefined && scope.row[item.code] != null
                  && scope.row[item.code] != '') ? scope.row[item.code].join(',') : scope.row[item.code]"></el-input> -->
              </span>
              <span v-if="item.showType == 'windowboxFilter'">
                <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                  <el-button icon="el-icon-search" @click="openFilterSelect(item)"
                    v-if="item.isEdit != 1 ? true : false"></el-button>
                </div>
                <div style="display: table-cell;">
                  <el-select style="width:100%" v-model="scope.row[item.code]" multiple filterable remote
                    reserve-keyword placeholder="请输入关键词" @focus="(val) => clearTableList(val, item)"
                    :disabled="item.isEdit != 1 ? false : true" :remote-method="(vul) => remoteMethod(vul, item)">
                    <el-option v-for="item1 in item.tableList" :key="item1.value" :label="item1.label"
                      :value="item1.value">
                    </el-option>
                  </el-select>
                </div>
                <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                  <el-button icon="el-icon-close" v-if="item.isEdit != 1 && item.isShowClear == '1' ? true : false"
                    @click="clearSelect(item)"></el-button>
                </div>
              </span>
              <span v-if="item.showType == 'objectTable'">
                <object-table-property :categoryIdP="props.categoryIdP" :proOcode="item.ocode" :type="type"
                  :tableData="scope.row[item.code]" :tableColumn="item.tableList"></object-table-property>
              </span>
              <span v-if="item.showType == 'windowboxTreeFilter'">

                <div v-if="item.isEdit != 1 ? true : false"
                  style="display: table-cell;    width: 44px;    vertical-align: top;">
                  <el-button icon="el-icon-search" @click="openWindowTree(item)"></el-button>
                </div>
                <div style="display: table-cell;">
                  <el-select v-model="scope.row[item.code]" multiple filterable remote reserve-keyword
                    placeholder="请输入关键词" @focus="(val) => clearTreeTableList(val, item)"
                    :disabled="item.isEdit != 1 ? false : true" :remote-method="(vul) => remoteTreeMethod(vul, item)">
                    <el-option v-for="item1 in item.tableList" :key="item1.value" :label="item1.label"
                      :value="item1.value">
                    </el-option>
                  </el-select>
                </div>
                <div v-if="item.isEdit != 1 && item.isShowClear == '1' ? true : false"
                  style="display: table-cell;    width: 44px;    vertical-align: top;">
                  <el-button icon="el-icon-close" @click="clearWindowTree(item)"></el-button>
                </div>

              </span>
              <span v-if="item.showType == 'input' && item.isAuto != 1">
                <!--数据类型 input、number、text、select、checkbox、radio-->
                <span v-if="item.dataType == 'string'">
                  <span v-if="item.dataLength != null">
                    <el-input v-model="scope.row[item.code]" :maxlength="item.dataLength"
                      :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? item.name : ''"
                      :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input>
                  </span>
                  <span v-if="item.dataLength == null">
                    <el-input v-model="scope.row[item.code]"
                      :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? item.name : ''"
                      :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input>
                  </span>
                </span>
                <span v-if="item.dataType == 'long'">
                  <span v-if="item.dataLength != null">
                    <el-input-number v-model="scope.row[item.code]" :max="item.dataLength"
                      :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input-number>
                  </span>
                  <span v-if="item.dataLength == null">
                    <el-input-number v-model="scope.row[item.code]"
                      :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input-number>
                  </span>
                </span>

              </span>

              <span v-if="item.showType == 'textarea'">
                <!--数据类型 input、number、text、select、checkbox、radio-->
                <span v-if="item.dataLength != null">
                  <el-input v-model="scope.row[item.code]" type="textarea" :maxlength="item.dataLength"
                    :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? item.name : ''"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input v-model="scope.row[item.code]" type="textarea"
                    :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? item.name : ''"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-input>
                </span>
              </span>
              <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
              <span v-if="item.showType == 'dateTime'">
                <el-date-picker v-model="scope.row[item.code]" type="datetime"
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-date-picker>
              </span>
              <!--日期输入框 yyyy-MM-dd-->
              <span v-if="item.showType == 'date'">
                <el-date-picker v-model="scope.row[item.code]" type="date"
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-date-picker>
              </span>

              <span v-if="item.showType == 'checkbox'">
                <el-checkbox-group v-model="scope.row[item.code]">
                  <el-checkbox :label="cn.value" v-for="cn in item.enumArray"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true">{{
                      cn.label
                    }} </el-checkbox>

                </el-checkbox-group>
              </span>

              <span v-if="item.showType == 'mul_combobox'">
                <el-select placement="top" filterable multiple="true"
                  :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? '请选择' : '  '"
                  v-model="scope.row[item.code]"
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true">
                  <el-option v-for="en in item.enumArray" :value="en.value" :label="en.label"></el-option>
                </el-select>
              </span>
              <span v-if="item.showType == 'comboTree'">

                <im-select-tree
                  :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? '请选择' : '  '"
                  clearable filter-on-input :model="item.enumArray" children-key="children"
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"
                  fixed-position :maxContentHeight=265 v-model="scope.row[item.code]" id-key="id" style="width: 100%"
                  title-key="title">
                </im-select-tree>
              </span>
              <!--单选-->
              <span v-if="item.showType == 'radio'">
                <el-radio-group v-model="scope.row[item.code]">
                  <el-radio :value="cn.value" :label="cn.value" v-for="cn in item.enumArray"
                    :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true">
                    {{ cn.label }}
                  </el-radio>
                </el-radio-group>
              </span>
              <span v-if="item.showType == 'combobox'">
                <el-select placement="top" filterable
                  :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? '请选择' : '  '"
                  v-model="scope.row[item.code]" clearable
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true">
                  <el-option v-for="en in item.enumArray" :value="en.value" :label="en.label"></el-option>
                </el-select>
              </span>
              <span v-if="item.showType == 'cascader'">
                <el-cascader :options="item.enumArray"
                  :placeholder="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? '请选择' : '  '"
                  v-model="scope.row[item.code]" clearable
                  :disabled="(props.type == 'add' || props.type == 'edit') && item.isEdit != 1 ? false : true"></el-cascader>
              </span>
              <span
                v-if="item.titleDesc != null && item.titleDesc != undefined && item.titleDesc != '' && item.titleDesc != 'null'"
                style="position: absolute;top:0px;left:-18px;" :title="item.titleDesc">
                <i type="ios-help-circle-outline" size="15" />
              </span>
            </div>
          </el-form-item>
        </template>
        <template v-slot:oper="scope">
          <el-button type="primary" icon="el-icon-circle-plus-outline" v-if="type == 'add' || type == 'edit'"
            @click="addRow()"></el-button>
          <el-button type="primary" icon="el-icon-remove-outline" v-if="type == 'add' || type == 'edit'"
            @click="deleteRow(scope.row)"></el-button>
        </template>
      </im-table>
    </el-form>
  </div>

</template>
<script setup lang="ts">
import ElInputTag from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/ElInputTag.vue";
import InputMultiple from "@/views/modules/eam/assetAudit/unknownAssetAudit/components/inputMultiple.vue";
import { http as axios } from "@/utils/http";
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";




const basePath = "eam-core/zcgl-common";
const checkObjectPropertyByCode = (url, parameter) => {
  return axios.postJson(basePath + url, parameter);
};
const checkObjectPropertyByCodeAxios = (params) => checkObjectPropertyByCode("/objectTable/checkObjectPropertyByCode", params);
const state = reactive({
  dataColumn: [],
  tableForm: {
    tableData: [],
  },
})
const props = defineProps({
  showOper: String,
  sss: String,
  proOcode: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  tableData: {
    type: Array,
    default: []
  },
  tableColumn: {
    type: Array,
    default: []
  },
  categoryIdP: {
    type: Number || String,
    default: 0,
  },
  proCode: {
    type: String
  }
})
const {
  dataColumn, tableForm
} = toRefs(state);
onMounted(() => {
  state.tableForm['tableData'] = props.tableData
  init();
})
const emits = defineEmits(['update:tableData', 'validateObjectFiled'])
watch(() => state.tableForm.tableData, (val, oldval) => {
  emits('update:tableData', state.tableForm.tableData);
  emits("validateObjectFiled", props.proCode);
}, { deep: true })


const itemRefs = ref({});
const setItemRef = (code) => (el) => {
  if (el) {
    itemRefs.value[code] = el;
  } else {
    delete itemRefs.value[code];
  }
};
const constMust = async (rule, value, callback, item) => {
  var checkParams = {};
  var requreid = rule.required;
  if (requreid) {
    if (item.showType == 'moreInput') {
      itemRefs.value[item.code][0].cyclicCheck().then(() => {
        callback();
      }).catch(() => {
        callback(new Error(" "));
      })
    } else {
      checkParams['ocode'] = props.proOcode;
      checkParams['code'] = item.rule.field;
      checkParams['value'] = value;
      checkObjectPropertyByCodeAxios(checkParams)
        .then(res => {
          if (res['data'].state == "success") {
            callback();
          } else if (res['data'].state == "error1") {
            callback(new Error(rule.name + "不满足校验规则"));
          } else {
            callback(new Error(rule.name + "不能为空"));
          }
        })
        .catch(exp => {
          callback(new Error(rule.name + "不满足校验规则"));
        });
    }

  } else {
    callback();
  }
}
const addRow = () => {
  var cc = {};
  for (var i = 0; i < props.tableColumn.length; i++) {
    var showType = props.tableColumn[i]['showType'];
    if (showType == 'number' || showType == 'comboTree') {
      cc[props.tableColumn[i]['code']] = null;
    } else if (showType == "windowboxFilter" || showType == "windowboxTreeFilter"
      || showType == "inputSelect" || showType == "moreInput"
      || showType == "objectTable") {
      cc[props.tableColumn[i]['code']] = [];
    } else {
      cc[props.tableColumn[i]['code']] = '';
    }
  }
  cc['sortId'] = state.tableForm.tableData.length + 1;
  state.tableForm.tableData.push(cc);
  state.tableForm.tableData = props.tableData;
}
const deleteRow = (row) => {
  console.log(row);
  if (state.tableForm.tableData.length == 1) {
    ElMessage.error("最少保留一行");
  } else {
    let tt = 0;
    for (let i = 0; i < state.tableForm.tableData.length; i++) {
      if (state.tableForm.tableData[i].sortId == row.sortId) {
        tt = i;
        break;
      }
    }
    state.tableForm.tableData.splice(tt, 1);
    for (let i = 0; i < state.tableForm.tableData.length; i++) {
      state.tableForm.tableData[i].sortId = i + 1;
    }
  }
  //state.tableForm.tableData = props.tableData;
}
const init = () => {
  for (let i = 0; i < props.tableData.length; i++) {
    props.tableData[i]['sortId'] = i + 1;
  }
  if (props.type == 'add' || props.type == 'edit') {
    if (props.tableData.length == 0) {
      var cc = {};
      for (var i = 0; i < props.tableColumn.length; i++) {
        var showType = props.tableColumn[i]['showType'];
        if (showType == 'number' || showType == 'comboTree') {
          cc[props.tableColumn[i]['code']] = null;
        } else if (showType == "windowboxFilter" || showType == "windowboxTreeFilter"
          || showType == "inputSelect" || showType == "moreInput"
          || showType == "objectTable") {
          cc[props.tableColumn[i]['code']] = [];
        } else {
          cc[props.tableColumn[i]['code']] = '';
        }
      }
      cc['sortId'] = 1;
      props.tableData.push(cc);
    }
  }
  var mm = props.tableColumn;
  for (let i = 0; i < mm.length; i++) {
    mm[i]['label'] = mm[i]['name'];
    mm[i]['prop'] = mm[i]['code'];
    mm[i]['slot'] = mm[i]['code'];
    //if(!props.showOper||props.showOper==null||props.showOper=="") {
    mm[i]['width'] = mm[i]['width'] + "px";
    // }
    mm[i]['hidden'] = mm[i]['hidden'] == '0' ? true : false
    state.dataColumn.push(mm[i]);
  }
  if (!props.showOper || props.showOper == null || props.showOper == "") {
    var mm = {};
    mm['label'] = '操作';
    mm['align'] = "center";
    mm['slot'] = "oper";
    state.dataColumn.push(mm);
  }
  if (state.dataColumn.length == 1) {
    var mm = state.dataColumn[0];
    mm['width'] = undefined;
    state.dataColumn[0] = mm;
  }
  state.tableForm.tableData = props.tableData;
}
// const objRules = (required) => {
//   let _this = this;
//   if (required) {
//     return new Promise((resolve, reject) => {
//       var flag = false;
//       if (_this.tableForm.tableData.length == 1) {
//         for (var i = 0; i < _this.tableColumn.length; i++) {
//           if (_this.tableForm.tableData[0][_this.tableColumn[i].code] != undefined
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != null
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != ""
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != 0) {
//             flag = true;
//           }
//         }
//       } else {
//         flag = true;
//       }
//       if (!flag) {
//         reject(false);
//       } else {
//         resolve(true);
//       }
//     });
//   } else {
//     return new Promise((resolve, reject) => {
//       var flag = false;
//       if (_this.tableForm.tableData.length == 1) {
//         for (var i = 0; i < _this.tableColumn.length; i++) {
//           if (_this.tableForm.tableData[0][_this.tableColumn[i].code] != undefined
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != null
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != ""
//             && _this.tableForm.tableData[0][_this.tableColumn[i].code] != 0) {
//             flag = true;
//           }
//         }
//       } else {
//         flag = true;
//       }
//       if (!flag) {
//         resolve(true);
//       } else {
//         _this.$refs["tableForm"].validate((valid) => {
//           if (valid) {
//             if (_this.proCode == 'wangka') {
//               let ipnames = [];
//               let mes = [];
//               for (var i = 0; i < _this.tableData.length; i++) {
//                 var ipname = _this.tableData[i].ipname;
//                 if (ipnames.indexOf(ipname) > -1) {
//                   mes.push("网卡第" + (ipnames.indexOf(ipname) + 1) + "行与第" + (i + 1) + "行ip地址冲突，请检查修改");
//                 }
//                 ipnames.push(ipname);
//               }
//               if (mes.length > 0) {
//                 var message = mes.join('<br/>');
//                 reject(false);
//                 _this.$alert(message, '提示信息', { dangerouslyUseHTMLString: true });
//               } else {
//                 resolve(true);
//               }
//             } else {
//               resolve(true);
//             }

//           } else {
//             reject(false);
//           }
//         });
//       }
//     });
//   }
// }


</script>
<style lang="scss" scoped>
.objectTableProperty {
  ::v-deep .el-input.is-disabled .el-input__inner {
    color: #262626;
  }

  ::v-deep .cell .el-input__inner {
    border-color: #e5e5e5;
  }
}
</style>
