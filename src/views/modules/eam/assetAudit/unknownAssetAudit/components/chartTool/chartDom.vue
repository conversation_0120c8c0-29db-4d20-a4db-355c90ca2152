<template>
  <div ref="chartDom"></div>
</template>

<script setup lang='ts'>
import * as echarts from 'echarts';
import debounce from "lodash/debounce";
// import { addListener, removeListener } from "resize-detector";
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";

// const props = defineProps({
//   option: {
//     type: Object,
//     default: () => { }
//   }
// })
// let resize = () => {
//   this.chart.resize();
// }
// resize = debounce(resize, 300);
// watch(() => props.option, (val) => {
//   this.chart.setOption(val);
// })
// const chartDom = ref(null);
// onMounted(() => {
//   renderChart();
//   // addListener(chartDom.value, resize);
// })
// onBeforeUnmount(() => {
//   // removeListener(chartDom.value, resize);
//   this.chart.dispose();
//   this.chart = null;
// })
// const renderChart = () => {
//   this.chart = echarts.init(chartDom.value);
//   this.chart.setOption(props.option);
// }




</script>
<style></style>
