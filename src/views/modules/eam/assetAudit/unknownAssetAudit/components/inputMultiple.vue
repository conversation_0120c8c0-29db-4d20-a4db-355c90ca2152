<template>
  <div class="inputMultiple">
    <el-form :model="dynamicValidateForm" ref="ruleFormRef" label-width="100px" class="demo-ruleForm">
      <el-form-item v-for="(domain, index) in dynamicValidateForm.domains" :label="'域名' + index" :key="domain.key"
        :prop="'domains.' + index + '.value'" :rules="rulesFrom">
        <el-input v-model="domain.value" class="inputButton" @change="inputChange" :style="{
          width: ` calc(100% - 150px) `
        }"></el-input>
        <div style="display: inline-block;width: 150px;text-align: center">
          <el-button icon="el-icon-plus" size="mini" title="添加" @click.prevent="addDomain(index)"></el-button>
          <el-button style="display: inline-block;" @click.prevent="removeDomain(domain)" size="mini"
            icon="el-icon-delete" title="删除"></el-button>
        </div>
      </el-form-item>
    </el-form>
  </div>

</template>

<script setup lang="ts">
import { http as axios } from "@/utils/http";
import { reactive, ref, toRefs, watch, onMounted, nextTick, onBeforeUnmount, computed, provide } from "vue";
const basePath = "eam-core/zcgl-common";
const checkPropertyByCode = (url, params) => {
  return axios.postJson(basePath + url, params);
};
const checkPropertyByCodeAxios = (params) => checkPropertyByCode("/instance/checkPropertyByCode", params);

const props = defineProps({
  value: {
    type: Array || String || Number,
    default: []
  },
  name: {
    type: String
  },
  categoryId: {
    type: String || Number
  },
  isRule: {
    type: String,
  },
})
const validatePass = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error(props.name + "不满足校验规则"));
  } else {
    checkPropertyByCodeAxios({ name: props.name, categoryId: props.categoryId, value: value }).then(res => {
      if (!(res.data == 'success')) {
        callback(new Error(props.name + "不满足校验规则"));
      } else {
        callback()
      }
    }).catch(err => {
    })
  }
};

const state = reactive({
  dynamicValidateForm: {
    domains: [{
      value: ''
    }],
  },
  rules: {
    required: true, validator: validatePass, trigger: 'blur'
  }
})
const {
  dynamicValidateForm, rules
} = toRefs(state)

const rulesFrom = computed(() => {
  return props.isRule != "1" ? [] : state.rules
})


watch(() => props.value, val => {
  init();
}, { deep: true, immediate: true })

const init = () => {
  state.dynamicValidateForm.domains = [];

  for (let item of props.value) {
    state.dynamicValidateForm.domains.push({
      value: item,
    });
  }
  if (state.dynamicValidateForm.domains.length == 0) {
    state.dynamicValidateForm.domains.push({
      value: "",
    });
  }
}
const emits = defineEmits(['input'])
const inputChange = () => {
  let values = [];
  for (let item of state.dynamicValidateForm.domains) {
    if (item.value) {
      values.push(item.value);
    }
  }
  emits("input", values);
}
const ruleFormRef = ref(null);
const cyclicCheck = () => {
  return new Promise((resolve, reject) => {
    ruleFormRef.value.validate((valid) => {
      if (valid) {
        resolve(true);
      } else {
        reject(false);
      }
    });
  });
}

const removeDomain = (item) => {
  if (state.dynamicValidateForm.domains.length == 1) {
    return;
  }
  let index = state.dynamicValidateForm.domains.indexOf(item)
  if (index !== -1) {
    state.dynamicValidateForm.domains.splice(index, 1)
  }
  inputChange();
}
const addDomain = (index) => {
  state.dynamicValidateForm.domains.splice(index + 1, 0, { value: '' })
}




init();

</script>
<style lang="scss" scoped>
.inputMultiple {
  .el-main {
    padding: 0;
    margin-left: 6px;
  }

  ::v-deep .el-form-item__label {
    display: none;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
  }

  .inputButton {
    display: inline-block;
  }

  ::v-deep .is-success .el-input__inner,
  ::v-deep .is-success .el-input__inner :hover {
    border-color: #dcdfe6 !important;
  }
}
</style>
