import {
  getCategoryImage as getCategoryImageBringIn,
  getTopoImage as getTopoImageBringIn,
  getTopoImageSelect as getTopoImageSelectBringIn,
} from "@/views/modules/eam/assetAudit/unknownAssetAudit/images/image";
import { computed } from 'vue';

export function useCategoryInfo(props) {
  const getCategoryInfo = (item) => {
    let category_id = item.category_id;
    let categoryColumns = props.categoryColumns || {};
    return categoryColumns[category_id];
  };

  const getCategoryImage = (item) => {
    console.log("const getCategoryImage = (item) =>",item);
    let { position_image } = getCategoryInfo(item) || {};
    let categoryImage = getCategoryImageBringIn(position_image);
    return categoryImage;
  };

  const getCategoryName = (item) => {
    let { category_id, categoryName } = item || {};
    if (categoryName) {
      return categoryName;
    }
    let categoryColumns = props.categoryColumns || {};
    categoryName = categoryColumns[category_id]?.category_name;
    return categoryName || "未知类别";
  };

  const getTopoImage = (item) => {
    let { position_image } = getCategoryInfo(item) || {};
    let topoImage = getTopoImageBringIn(position_image);
    return topoImage;
  };

  const getTopoImageSelect = (item) => {
    let { position_image } = getCategoryInfo(item) || {};
    let topoImageSelect = getTopoImageSelectBringIn(position_image);
    return topoImageSelect;
  };

  const getColumns = (item) => {
    let category_id = item.category_id;
    let categoryColumns = props.categoryColumns || {};
    let columns = categoryColumns[category_id]?.columns || [];
    return columns;
  };

  const getPortType = (item) => {
    let category_id = item?.category_id;
    let categoryColumns = props.categoryColumns || {};
    let portType = categoryColumns[category_id]?.portType;
    console.log(category_id, categoryColumns[category_id], item);
    return portType;
  };

  return {
    getCategoryInfo,
    getCategoryImage,
    getCategoryName,
    getTopoImage,
    getTopoImageSelect,
    getColumns,
    getPortType,
  };
}
