<template>
  <div class="flex justify-start items-end gap-1">
    <el-input
      v-model="customText"
      show-word-limit
      :rows="10"
      type="textarea"
      placeholder="请输入IP地址表达式，具体格式请参考右下方说明！"
      @input="inputChangeHandler"
    />
    <el-tooltip
      placement="left-end"
      effect="light"
      :open-delay="500"
    >
      <iconify-icon-offline icon="EP-InfoFilled"/>
      <template #content>
        <p>
          支持单IP，同一网段IP范围、同一网段IP和子网；
        </p>
        <p>格式如下（一行一个）</p>
        <p>单个IP：***********</p>
        <p>IP范围：***********-*************</p>
        <p>IP范围：***********-255</p>
        <p>IP/子网掩码：***********/24</p>
        <p>说明：为保证扫描效率，子网掩码需大于等于16</p>
      </template>
    </el-tooltip>
  </div>
  <el-text v-if="errorMsg&&errorMsg.length>0" type="danger">{{ errorMsg }}</el-text>
</template>

<script lang="ts" setup>
import {getCurrentInstance, reactive, toRefs} from 'vue';
import {IconifyIconOffline} from "@/components/ReIcon";

const {$message} = getCurrentInstance().appContext.config.globalProperties;

//组件属性
const props = defineProps({
  list: {
    type: Array<String>,
    default: []
  }
});

//声明事件
const emit = defineEmits(["update:list"]);

//数据对象
const state = reactive({
  customText: '',
  errorMsg: null as string
})
const {
  customText,
  errorMsg
} = toRefs(state)

//输入改变触发
const inputChangeHandler = (val: string) => {
  const errorArray = validateIpData(val.split('\n'));
  if (errorArray && errorArray.length > 0) {
    state.errorMsg = errorArray.join();
  } else {
    state.errorMsg = null;
  }
}

//校验IP表达式数据
function validateIpData(ipData: string[]): string[] {
  const errors: string[] = [];

  ipData.forEach((data, index) => {
    let isValid = true;
    let errorMessage = `第 ${index + 1} 行: `;

    // 检查是否为单个 IP
    if (/^\d+\.\d+\.\d+\.\d+$/.test(data)) {
      if (!validateSingleIp(data)) {
        isValid = false;
        errorMessage += '无效的单个 IP 格式。';
      }
    }
    // 检查是否为 IP 范围
    else if (/^\d+\.\d+\.\d+\.\d+-(\d+\.\d+\.\d+\.\d+|\d+)$/.test(data)) {
      const [startIp, end] = data.split('-');
      if (!validateSingleIp(startIp) || !validateEndOfRange(end, startIp)) {
        isValid = false;
        errorMessage += 'IP 范围格式无效或超出子网。';
      }
    }
    // 检查是否为带子网掩码的 IP
    else if (/^\d+\.\d+\.\d+\.\d+\/(\d{1,2})$/.test(data)) {
      const [ip, mask] = data.split('/');
      if (!validateSingleIp(ip) || parseInt(mask) < 16) {
        isValid = false;
        errorMessage += '子网掩码无效（必须为 >= 16）。';
      }
    } else {
      isValid = false;
      errorMessage += '无法识别的格式.';
    }

    if (!isValid) {
      errors.push(errorMessage);
    }
  });

  return errors;
}

//校验单个IP位置的数字
function validateSingleIp(ip: string): boolean {
  const parts = ip.split('.').map(Number);
  for (const part of parts) {
    if (part < 0 || part > 255) {
      return false;
    }
  }
  return true;
}

//校验IP范围
function validateEndOfRange(end: string, startIp: string): boolean {
  const startParts = startIp.split('.').map(Number);
  let endParts: number[];

  if (end.includes('.')) {
    endParts = end.split('.').map(Number);
    for (let i = 0; i < 4; i++) {
      if (endParts[i] < startParts[i] || endParts[i] > 255) {
        return false;
      }
    }
  } else {
    endParts = [...startParts];
    endParts[3] = parseInt(end);
    if (endParts[3] < startParts[3] || endParts[3] > 255) {
      return false;
    }
  }

  // 确保范围不超过子网
  const subnetMask = getSubnetMaskFromIpRange(startParts, endParts);
  return subnetMask >= 16;
}

//获取子网掩码
function getSubnetMaskFromIpRange(start: number[], end: number[]): number {
  let mask = 0;
  for (let i = 0; i < 4; i++) {
    if (start[i] !== end[i]) break;
    mask += 8;
  }
  return mask;
}

//校验IP地址是否合法
const validateIpText = (): boolean => {
  if (state.customText != null && state.customText.trim().length > 0) {
    const ipData = state.customText.split("\n");
    const errors = validateIpData(ipData);
    if (errors.length > 0) {
      $message({
        type: 'error',
        message: errors.join('\n')
      });
      return false;
    }
    emit("update:list", ipData);
    return true;
  } else {
    $message({
      type: 'error',
      message: '请输入IP地址表达式！'
    });
  }
  return false;
}

//初始化输入内容
const initText = (ipData: Array<string>) => {
  if (ipData && ipData.length > 0) {
    state.customText = ipData.join("\n");
  }
}

//对外暴露可调用方法
defineExpose({validateIpText, initText});

</script>
