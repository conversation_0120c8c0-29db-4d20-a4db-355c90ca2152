<template>
  <div
    class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color h-48 p-2"
  >
    <el-tree
      ref="treeRef"
      :data="data"
      :props=" {
        label: 'name',
        isLeaf: 'leaf'
      }"
      show-checkbox
      node-key="id"
      default-expand-all
    />
  </div>
</template>

<script lang="ts" setup>
import {getCurrentInstance, reactive, ref, toRefs} from 'vue';
import {getIpPoolTreeData} from "@/views/modules/eam/assetMap/taskManage/api/eamCollectServerApi";
import {ResultStatus} from "@/utils/http/types";
import {ElTree} from "element-plus";

const {$message} = getCurrentInstance().appContext.config.globalProperties;
const treeRef = ref<InstanceType<typeof ElTree>>();

//组件属性
const props = defineProps({
  checkedKeys: {
    type: Array<String>,
    default: []
  }
});

//定义事件
const emit = defineEmits(["update:checked-keys"]);

//数据对象
const state = reactive({
  data: [],
})
const {
  data,
} = toRefs(state)

//加载资源池Tree数据
const loadTreeData = async () => {
  const res = await getIpPoolTreeData();
  if (res.status === ResultStatus.Success) {
    state.data = res.data;
    //设置已选择的key
    if (props.checkedKeys && props.checkedKeys.length > 0) {
      setCheckedKeys(props.checkedKeys as any);
    }
  }
}
loadTreeData();

//获取当前已选择的key
const getCheckedKeys = (): Array<string | number> => {
  return treeRef.value!.getCheckedKeys(false);
};

//设置资源树的选项
const setCheckedKeys = (keys: Array<string | number>) => {
  treeRef.value!.setCheckedKeys(keys, false);
};

//校验选择项
const validateChecks = (): boolean => {
  const checks = getCheckedKeys();
  if (checks && checks.length > 0) {
    emit("update:checked-keys", checks)
    return true;
  }
  $message({
    type: 'error',
    message: '请选择IP资源池！'
  });
  return false;
}

//对外暴露可调用方法
defineExpose({validateChecks});

</script>
