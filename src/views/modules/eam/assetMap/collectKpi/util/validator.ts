//校验采集指标表单
const validateKpiForm = (form: any): string => {
  if (!(form.glueName && form.glueName?.trim().length > 0)) {
    return '请输入指标名称';
  }

  if (!(form.glueRemark && form.glueRemark?.trim().length > 0)) {
    return '请输入指标描述';
  }

  if (!(form.glueSource && form.glueSource?.trim().length > 0)) {
    return '请输入采集脚本';
  }

  if (!(form.handleGroovy && form.handleGroovy?.trim().length > 0)) {
    return '请输入解析脚本';
  }

  return null;
}

export {
  validateKpiForm
}
