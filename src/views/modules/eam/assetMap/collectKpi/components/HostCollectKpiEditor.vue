<template>
  <div class="p-3">
    <el-divider content-position="left">
      <sign-title title="指标信息"/>
    </el-divider>
    <div class="flex-sc p-2 gap-1 pt-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20">指标名称：</el-text>
      <el-input v-model="form.glueName" placeholder="请输入指标名称" clearable maxlength="64" show-word-limit/>
    </div>
    <div class="flex-bc p-2 w-full">
      <div class="flex-sc gap-1">
        <span class="text-red-600">*</span>
        <el-text class="w-20">适配系统：</el-text>
        <el-radio-group v-model="form.glueType">
          <el-radio-button v-for="item in collectTypeData" :label="item.label" :value="item.value"/>
        </el-radio-group>
      </div>
      <div class="flex-sc gap-1">
        <el-text>执行超时时间：</el-text>
        <el-input-number
          v-model="form.glueTimeout"
          :min="15"
          :max="300"
          :step="5"
          class="w-32"
        >
          <template #suffix>
            <span>秒</span>
          </template>
        </el-input-number>
        <el-tooltip
          placement="top"
          effect="light"
          :open-delay="500"
        >
          <iconify-icon-offline icon="EP-InfoFilled"/>
          <template #content>
            <p>脚本执行大于等于此时间，则视为超时，不再等待执行结果。</p>
          </template>
        </el-tooltip>
      </div>
      <div class="flex-sc gap-1 pr-5">
        <el-text>是否启用：</el-text>
        <el-switch
          v-model="form.glueEnable"
          :active-value="1"
          :inactive-value="0"
          active-text="启用"
          inactive-text="禁用"
          inline-prompt
          class="enable-switch-color"
        />
      </div>

    </div>
    <div class="flex-sc p-2 gap-1 pb-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20">指标描述：</el-text>
      <el-input
        v-model="form.glueRemark"
        :rows="2"
        type="textarea"
        placeholder="请输入指标描述"
        maxlength="200"
        show-word-limit
      />
    </div>
    <el-divider content-position="left">
      <sign-title title="指标脚本"/>
    </el-divider>
    <el-tabs v-model="activeName" class="mt-5">
      <el-tab-pane label="采集脚本" name="collect">
        <monaco-editor ref="shellSourceRef" v-model="form.glueSource" language="shell"
                       height="260px" :options="{ wordWrap: 'on' }"/>
      </el-tab-pane>
      <el-tab-pane label="解析脚本" name="parse">
        <monaco-editor ref="shellSourceRef" v-model="form.handleGroovy" language="java"
                       height="260px" :options="{ wordWrap: 'on' }"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue';
import SignTitle from "@/components/SignTitle/SignTitle.vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import {collectTypeData} from "@/views/modules/eam/assetMap/collectKpi/util/collect_data";
import {validateKpiForm} from "@/views/modules/eam/assetMap/collectKpi/util/validator";
import {addKpi, updateKpi} from "@/views/modules/eam/assetMap/collectKpi/api/kpiCollectApi";
import {IconifyIconOffline} from "@/components/ReIcon";

const {$message} = getCurrentInstance().appContext.config.globalProperties;
const shellSourceRef = ref();

//组件属性
const props = defineProps({
  kpiInfo: {
    type: Object,
    default: () => {
    }
  }
});

//声明事件
const emit = defineEmits(["success"]);

//数据对象
const state = reactive({
  activeName: 'collect',
  form: {
    id: null,
    glueName: "",
    glueType: 1,
    glueRemark: "",
    glueSource: "",
    handleGroovy: "",
    glueTimeout: 15,
    glueEnable: 1
  },
})
const {
  activeName,
  form,
} = toRefs(state)

//提交表单数据
const submitData = async () => {
  const validateMsg = validateKpiForm(state.form);
  if (validateMsg) {
    $message({
      type: "error",
      message: validateMsg
    })
  } else {
    //通过校验进行提交操作
    if (state.form.id) {
      //更新
      if(state.form.glueType == 1) {
        state.form.glueSource = state.form.glueSource.replace(/\r\n/g,'\n');
      }
      const res = await updateKpi(state.form);
      if (res.status === "0") {
        $message({
          type: "success",
          message: "已成功更新采集指标！"
        });
        emit("success");
      }
    } else {
      if(state.form.glueType == 1) {
        state.form.glueSource = state.form.glueSource.replace(/\r\n/g,'\n');
      }
      //新增
      const res = await addKpi(state.form);
      if (res.status === "0") {
        $message({
          type: "success",
          message: "已成功新增采集指标！"
        });
        emit("success");
      }
    }
  }
}

//组件挂载初始化
onMounted(() => {
  if (props.kpiInfo.glueName && props.kpiInfo.glueName.length > 0) {
    state.form = props.kpiInfo as any;
  }
});

//对外暴露可调用方法
defineExpose({submitData});

</script>
