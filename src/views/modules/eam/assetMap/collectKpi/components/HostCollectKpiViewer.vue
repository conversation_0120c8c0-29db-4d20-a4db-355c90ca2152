<template>
  <div>
    <div class="flex-bc  mb-2">
      <el-tag type="primary">{{ getCollectTypeLabel(kpiInfo.glueType) }}</el-tag>
      <el-tooltip
        class="item"
        effect="light"
        :content="`指标描述：${kpiInfo.glueRemark}`"
        placement="left"
        :open-delay="500"
      >
        <iconify-icon-offline icon="EP-InfoFilled"/>
      </el-tooltip>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="采集脚本" name="collect">
        <monaco-editor ref="shellSourceRef" v-model="form.glueSource" language="shell"
                       height="360px" :options="{ wordWrap: 'on',readOnly: true }"/>
      </el-tab-pane>
      <el-tab-pane label="解析脚本" name="parse">
        <monaco-editor ref="shellSourceRef" v-model="form.handleGroovy" language="java"
                       height="360px" :options="{ wordWrap: 'on',readOnly: true }"/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, toRefs} from 'vue';
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import {IconifyIconOffline} from "@/components/ReIcon";
import {getCollectTypeLabel} from "@/views/modules/eam/assetMap/collectKpi/util/collect_data";

//组件属性
const props = defineProps({
  kpiInfo: {
    type: Object,
    default: () => {
    }
  }
});

//数据对象
const state = reactive({
  activeName: 'collect',
  form: {
    glueSource: "",
    handleGroovy: ""
  }
})
const {
  form,
  activeName,
} = toRefs(state)

onMounted(() => {
  state.form.glueSource = props.kpiInfo.glueSource;
  state.form.handleGroovy = props.kpiInfo.handleGroovy;
});

</script>
