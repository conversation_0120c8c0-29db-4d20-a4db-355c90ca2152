import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.eamCollectServer}/`;

//指标数据分页查询
const getKpiData = (params: any) =>
  http.postJson<any>(`${basePath}ibex/job/glue/pageList`, params);

//新增采集指标
const addKpi = (params: any) =>
  http.postJson<any>(`${basePath}ibex/job/glue/add`, params);

//更新采集指标
const updateKpi = (params: any) =>
  http.postJson<any>(`${basePath}ibex/job/glue/update`, params);

//删除采集指标
const deleteKpi = (ids: string) =>
  http.get<string, any>(`${basePath}ibex/job/glue/del?ids=${ids}`);

export {
  getKpiData,
  addKpi,
  updateKpi,
  deleteKpi
}
