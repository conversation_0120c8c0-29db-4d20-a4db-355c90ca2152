<template>
  <div>
    <avue-crud
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadKpiData"
      @current-change="loadKpiData"
      @selection-change="selectionChangeHandler"
    >
      <!-- 表格左侧菜单 -->
      <template #menu-left>
        <el-button type="primary" :icon="useRenderIcon('EP-Plus')" @click="openEditKpiDialog({})" v-auth="'kpi:add'">
          新建指标
        </el-button>
      </template>
      <!-- 表格右侧菜单 -->
      <template #menu-right="{ size }">
        <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
          <el-input
            v-model="searchCondition.glueName"
            clearable
            placeholder="指标名称"
            :size="size"
          >
            <template #append>
              <el-button
                :icon="useRenderIcon('EP-Search')"
                @click="resetTablePageAndQuery"
              />
            </template>
          </el-input>
          <el-select
            v-model="searchCondition.glueTypes"
            placeholder="适配系统"
            clearable
            multiple
            collapse-tags
            filterable
            style="width: 280px"
            class="ml-3"
            @change="resetTablePageAndQuery"
          >
            <el-option v-for="item in collectTypeData" :key="item.value" :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
          <el-tooltip
            content="批量删除指标"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="useRenderIcon('EP-DeleteFilled')"
              circle
              :size="size"
              :disabled="selectedKpis.length==0"
              @click="batchDeleteKpiHandler"
            />
          </el-tooltip>
        </div>
      </template>
      <!-- 表格操作按钮 -->
      <template #menu="{ row, size, type }">
        <el-button
          :size="size"
          :type="type"
          :icon="useRenderIcon('EP-View')"
          text
          @click="openViewKpiDialog(row)"
        >
          查看
        </el-button>
        <el-button
          :size="size"
          :type="type"
          :icon="useRenderIcon('EP-Edit')"
          text
          @click="openEditKpiDialog(row)"
          v-auth="'kpi:edit'"
        >
          编辑
        </el-button>
        <el-button
          :size="size"
          :type="type"
          :icon="useRenderIcon('EP-Delete')"
          text
          @click="deleteKpiHandler(row)"
          v-auth="'kpi:delete'"
        >
          删除
        </el-button>
      </template>
      <template #glueType="{row}">
        <el-tag type="primary" effect="plain">
          {{ getCollectTypeLabel(row.glueType) }}
        </el-tag>
      </template>
      <template #glueEnable="{row}">
        <el-tag :type="row.glueEnable == 1?'success':'danger'" effect="plain">
          {{ row.glueEnable == 1 ? '启用' : '禁用' }}
        </el-tag>

      </template>
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, h, reactive, ref, toRefs} from 'vue';
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {addDialog, closeAllDialog} from "@/components/ReDialog/index";
import HostCollectKpiEditor from "@/views/modules/eam/assetMap/collectKpi/components/HostCollectKpiEditor.vue";
import HostCollectKpiViewer from "@/views/modules/eam/assetMap/collectKpi/components/HostCollectKpiViewer.vue";
import {deleteKpi, getKpiData} from "@/views/modules/eam/assetMap/collectKpi/api/kpiCollectApi";
import {collectTypeData, getCollectTypeLabel} from "@/views/modules/eam/assetMap/collectKpi/util/collect_data";
import {ElOption} from "element-plus";

const {$message, $confirm} = getCurrentInstance().appContext.config.globalProperties;

//编辑采集指标Ref
const editCollectKpiRef = ref<InstanceType<typeof HostCollectKpiEditor>>();
//查看采集指标Ref
const viewCollectKpiRef = ref<InstanceType<typeof HostCollectKpiViewer>>();

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    glueName: "",
    glueTypes: []
  },
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  tableData: [],
  selectedKpis: [] as Array<string>,
})

const {
  tableLoading,
  searchCondition,
  tablePage,
  tableData,
  selectedKpis
} = toRefs(state)

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 260;
});

//表格配置项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 180,
  height: tableHeight,
  rowKey: "id",
  column: [
    {
      label: "指标名称",
      prop: "glueName",
    },
    {
      label: "适配系统",
      prop: "glueType",
    },
    {
      label: "指标描述",
      prop: "glueRemark",
    },
    {
      label: "是否启用",
      prop: "glueEnable",
      width: 100,
    },
    {
      label: "创建时间",
      prop: "addTime",
    }, {
      label: "更新时间",
      prop: "updateTime",
    }
  ]
});

//重置分页后查询数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  loadKpiData();
}

// 加载指标数据
const loadKpiData = async () => {
  state.tableLoading = true;
  state.tableData = [];
  const res = await getKpiData({
    ...state.searchCondition,
    pageIndex: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize
  });
  state.tableData = res.data;
  state.tablePage.total = res.total;
  state.tableLoading = false;
}
loadKpiData();

// 打开编辑指标窗口
const openEditKpiDialog = (k: any) => {
  addDialog({
    title: k.glueName ? `编辑指标 - ${k.glueName}` : "新建指标",
    width: "80%",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: {kpiInfo: k},
    contentRenderer: () => h(HostCollectKpiEditor, {
      ref: editCollectKpiRef,
      onSuccess: () => {
        closeAllDialog();
        loadKpiData();
      }
    }),
    beforeSure: (done: Function) => {
      editCollectKpiRef.value.submitData();
    }
  });
}

// 打开查看指标窗口
const openViewKpiDialog = (k: any) => {
  addDialog({
    title: `查看指标 - ${k.glueName}`,
    width: "80%",
    fullscreenIcon: true,
    closeOnClickModal: false,
    hideFooter: true,
    props: {kpiInfo: k},
    contentRenderer: () => h(HostCollectKpiViewer, {ref: viewCollectKpiRef})
  });
}

//删除采集指标触发
const deleteKpiHandler = async (k: any) => {
  $confirm(
    `删除指标其关联的采集任务也会失效。您确定要删除 '${k.glueName}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const res = await deleteKpi(k.id);
    if (res.status === "0") {
      $message({
        type: "success",
        message: "已成功删除采集指标！"
      });
      await loadKpiData();
    }
  });
}

//批量删除采集指标触发
const batchDeleteKpiHandler = async () => {
  const selectedSize = state.selectedKpis.length;
  $confirm(
    `删除指标其关联的采集任务也会失效。您确定要删除已选择的 ${selectedSize} 条指标么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const res = await deleteKpi(state.selectedKpis.join(","));
    if (res.status === "0") {
      $message({
        type: "success",
        message: "已成功删除 ${selectedSize} 条采集指标！"
      });
      await loadKpiData();
    }
  });
}

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.id));
  state.selectedKpis = selectIds;
}

</script>
