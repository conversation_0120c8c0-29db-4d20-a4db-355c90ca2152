<template>
  <div style="margin-top:20px;">
    <avue-crud
      ref="tableRef"
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadTableData"
      @current-change="loadTableData"
    >
      <template #menu-left>
        <div class="float-left pr-3">
          <search-with-type-column v-model="searchCondition.value"
                                   v-model:searchCondition="searchCondition"
                                   :column-options="columnSearchOptions"
                                   :column-select-width="120"
                                   class="flex-sc w-[800px]"
                                   @search="resetTablePageAndQuery"
                                   @reset="resetCondition"
                                   input-class-name="w-1/2"/>
        </div>
      </template>
      <template #oper="{ row }">
        <el-button link type="primary" @click="showLinkInfo(row)">查看详情</el-button>
      </template>
    </avue-crud>
  </div>
</template>
<script setup lang="ts">
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import SearchWithTypeColumn from "@/components/Search/SearchWithTypeColumn.vue";
import {ref, reactive, computed, toRefs, onMounted} from "vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {queryHostAssetRelationTable} from "@/views/modules/eam/assetMap/hostAssetRelationship/api/hostVistRelation"

const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 260;
});
const tableRef = ref(null);
const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "keyId",
  column: [
    {
      label: "源端IP地址",
      prop: "srcIp"
    },
    {
      label: "源端MAC地址",
      prop: "srcMac"
    },
    {
      label: "对端IP地址",
      prop: "destIp"
    },
    {
      label: "对端MAC地址",
      prop: "destMac"
    },
    {
      label: "最后采集时间",
      prop: "createTime"
    },
    {
      label: "操作",
      prop: "oper",
      width: 150
    }
  ]
})
const emit = defineEmits(["jump-to", "source-select"]);
//数据对象
const state = reactive({
  tableLoading: false,
  columnSearchOptions: [
    {
      component:{
        type: "Input"
      },
      name: "源端IP地址",
      field: "srcIp",
      value: null,
      fuzzy: true,

    },
    {
      component:{
        type: "Input"
      },
      name: "源端MAC地址",
      field: "srcMac",
      value: null,
      fuzzy: true,
    },
    {
      component:{
        type: "Input"
      },
      name: "对端IP地址",
      field: "destIp",
      value: null,
      fuzzy: true,
    },
    {
      component:{
        type: "Input"
      },
      name: "对端MAC地址",
      field: "destMac",
      value: null,
      fuzzy: true
    },
  ],
  searchCondition: {
    field: "srcIp",
    value: null,
    fuzzy: true,
    type: "Input"
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  }
})

const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}

const {
  tableData,tablePage,columnSearchOptions,searchCondition,tableLoading
} = toRefs(state)



const resetCondition = () =>{
  if(state.columnSearchOptions&&state.columnSearchOptions.length>0) {
    state.searchCondition.field = state.columnSearchOptions[0].field;
    state.searchCondition.fuzzy = true;
    state.searchCondition["props"] = state.columnSearchOptions[0].component["props"];
    state.searchCondition.type = state.columnSearchOptions[0].component.type;
    state.searchCondition["options"] = state.columnSearchOptions[0].component["options"];
    state.searchCondition.value = null;
  }else{
    state.searchCondition = {
      field: "ipAddress",
      value: null,
      fuzzy: true,
      type: "Input"
    };
  }
  state.tablePage.currentPage = 1;
  loadTableData();
}


//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  let params = {
    conditions: [],
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
  }
  if(state.searchCondition.value){
    let cm = {
      field: state.searchCondition.field,
      value: state.searchCondition.value,
      operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
    }
    params.conditions.push(cm);
  }
  queryHostAssetRelationTable(params).then(res=>{
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
    state.tableLoading = false;
  }).catch(err=>{
    console.log(err);
    state.tableLoading = false;
  })

}
onMounted(()=>{
  loadTableData();
})
const showLinkInfo = (row) => {
  emit("source-select", row);
  emit('jump-to',"hostRelationTableInfo");
}
</script>
