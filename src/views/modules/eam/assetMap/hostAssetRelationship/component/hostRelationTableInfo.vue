<template>
  <div class="flex-bc">
    <el-page-header @back="jumpTo('hostRelationTableData')" class="mb-2">
      <template #content>
        <span class="mr-3 font-bold"> 访问信息详情 </span>
      </template>
    </el-page-header>
  </div>
  <avue-data-display :option="panalOptions"></avue-data-display>
  <el-tabs v-model="tabPosition" type="border-card" class="demo-tabs" style="margin-top: 10px;">
    <el-tab-pane label="拓扑图" name="topo">
      <div ref="chartsContainer" :style="{ width: '100%', height: '600px',backgroundColor:'#F0F8FA' }"></div>
    </el-tab-pane>
    <el-tab-pane label="列表" name="table">
      <avue-crud
        ref="tableRef"
        :data="tableData"
        :option="tableOption"
        v-model:page="tablePage"
        :table-loading="tableLoading"
        @refresh-change="resetTablePageAndQuery"
        @size-change="loadTableData"
        @current-change="loadTableData"
      >
        <template #menu-left>
          <div class="float-left pr-3">
            <search-with-type-column v-model="searchCondition.value"
                                     v-model:searchCondition="searchCondition"
                                     :column-options="columnSearchOptions"
                                     :column-select-width="120"
                                     class="flex-sc w-[800px]"
                                     @search="resetTablePageAndQuery"
                                     @reset="resetCondition"
                                     input-class-name="w-1/2"/>
          </div>
        </template>
      </avue-crud>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup lang="ts">
import * as echarts from "echarts";
import {computed, onMounted, reactive, ref, toRefs} from "vue";
import {
  queryVistInfoData,
  queryVistCount,
  queryVistInfoTopo
} from "@/views/modules/eam/assetMap/hostAssetRelationship/api/hostVistRelation";
import SearchWithTypeColumn from "@/components/Search/SearchWithTypeColumn.vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
type EChartsOption = echarts.EChartsOption;

const emit = defineEmits(["jump-to"]);
const props = defineProps({
  sourceInfo : {
    type: Object
  }
});
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 260;
});
const tableRef = ref(null);
const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "keyId",
  column: [
    {
      label: "源端IP地址",
      prop: "srcIp"
    },
    {
      label: "源端MAC地址",
      prop: "srcMac"
    },
    {
      label: "源端端口",
      prop: "srcPort"
    },
    {
      label: "对端IP地址",
      prop: "destIp"
    },
    {
      label: "对端MAC地址",
      prop: "destMac"
    },
    {
      label: "对端端口",
      prop: "destPort"
    },
    {
      label: "服务进程",
      prop: "pidInfo"
    },
    {
      label: "最后采集时间",
      prop: "createTime"
    }
  ]
})
const state = reactive({
  panalOptions: {
    span:4,
    data: [
      {
        title: '累计发现访问关系',
        count: '0',
        fontColor: '#7c7e7e',
        color: '#7c7e7e'
      }
    ]
  },
  tabPosition:"topo",
  tableData: [],
  tableLoading: false,
  columnSearchOptions: [
    {
      component:{
        type: "Input"
      },
      name: "源端端口",
      field: "srcPort",
      value: null,
      fuzzy: true,

    },
    {
      component:{
        type: "Input"
      },
      name: "对端端口",
      field: "destPort",
      value: null,
      fuzzy: true,
    }
  ],
  searchCondition: {
    field: "srcPort",
    value: null,
    fuzzy: true,
    type: "Input"
  },
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  topoData:{
    node:{
      srcIp:[],
      srcPort:[],
      destIp:[],
      destPort:[]
    },
    link:[]
  }
})

const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}
const {panalOptions,tableData,tabPosition,tableLoading,columnSearchOptions,searchCondition,tablePage,topoData} = toRefs(state)


const resetCondition = () =>{
  if(state.columnSearchOptions&&state.columnSearchOptions.length>0) {
    state.searchCondition.field = state.columnSearchOptions[0].field;
    state.searchCondition.fuzzy = true;
    state.searchCondition["props"] = state.columnSearchOptions[0].component["props"];
    state.searchCondition.type = state.columnSearchOptions[0].component.type;
    state.searchCondition["options"] = state.columnSearchOptions[0].component["options"];
    state.searchCondition.value = null;
  }else{
    state.searchCondition = {
      field: "ipAddress",
      value: null,
      fuzzy: true,
      type: "Input"
    };
  }
  state.tablePage.currentPage = 1;
  loadTableData();
}
//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  let params = {
    conditions: [],
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    srcIp: props.sourceInfo.srcIp,
    destIp: props.sourceInfo.destIp
  }
  if(state.searchCondition.value){
    let cm = {
      field: state.searchCondition.field,
      value: state.searchCondition.value,
      operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
    }
    params.conditions.push(cm);
  }
  queryVistInfoData(params).then(res=>{
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
    state.tableLoading = false;
  }).catch(err=>{
    console.log(err);
    state.tableLoading = false;
  })

}
const chartsContainer = ref(null);
const queryCount = () =>{
  let row = {
    "srcIp": props.sourceInfo.srcIp,
    "destIp": props.sourceInfo.destIp
  };
  queryVistCount(row).then((res) => {
    state.panalOptions.data[0].count = res.data.totalCount;
  })
}
const queryVistInfoTopoData = () =>{
  let params = {
    srcIp: props.sourceInfo.srcIp,
    destIp: props.sourceInfo.destIp
  }
  queryVistInfoTopo(params).then(res=>{
    const chartDom = chartsContainer.value;
    const myChart = echarts.init(chartDom);
    let cakeShapeOption: EChartsOption;
    cakeShapeOption = {
      title: {
        text: ''
      },
      tooltip: {},
      animationDurationUpdate: 1500,
      animationEasingUpdate: 'quinticInOut',
      series: [
        {
          type: 'graph',
          layout: 'none',
          roam: true,
          label: {
            show: true
          },
          symbolSize: 60,
          symbol:'roundRect',
          edgeSymbol: ['circle', 'arrow'],
          edgeSymbolSize: [1, 10],
          symbolOffset: [8, 0],
          edgeLabel: {
            fontSize: 20
          },

          data: res.data.data,
          itemStyle: {//设置节点样式
            normal: {
              //函数接收params参数，params就是当前data数组的每一项，把颜色return出来就可以啦~
              color: function (params) {
                return params.data.color;
              },
              borderColor:"#C0C8CB",
              borderWidth: 2,
            },

          },
          // links: [],
          links: res.data.links,
          lineStyle: {
            opacity: 0.9,
            width: 2,
            curveness: 0
          },
          roam: false,  // 禁用地图漫游功能，对于非地图图表也有效，但主要是针对地图的缩放和平移功能
          draggable: false
        },
      ],

    };
    cakeShapeOption && myChart.setOption(cakeShapeOption);
  })
}
const jumpTo = (url: any) => {
  emit("jump-to", url);
}
onMounted(()=>{
  queryCount();
  loadTableData();
  queryVistInfoTopoData();
})

</script>
<style lang="scss" scoped>
:deep(.item) {
  .count{
    margin:0 auto;
  }
  .splitLine{
    display: none;
  }
}
</style>
