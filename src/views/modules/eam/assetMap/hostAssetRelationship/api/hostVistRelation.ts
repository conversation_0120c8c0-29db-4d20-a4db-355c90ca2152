import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.eamCollectServer}`;

const queryHostAssetRelationTable = (params: any) =>
  http.postJson<any>(`${basePath}/hostAsset/queryHostAssetRelationTable`, params);
const queryVistCount = (params: any) =>
  http.postJson<any>(`${basePath}/hostAsset/queryVistCount`, params);
const queryVistInfoData = (params: any) =>
  http.postJson<any>(`${basePath}/hostAsset/queryVistInfoData`, params);
const queryVistInfoTopo = (params: any) =>
  http.postJson<any>(`${basePath}/hostAsset/queryVistInfoTopo`, params);

export {
  queryHostAssetRelationTable,
  queryVistCount,
  queryVistInfoData,
  queryVistInfoTopo
}
