//采集Agent状态
const agentStatusData = [
  {
    label: "正常",
    value: 1,
  },
  {
    label: "异常",
    value: 2,
  },
  {
    label: "未安装",
    value: 3,
  },
]

//获取采集Agent状态Label
const getAgentStatusLabel = (status: number) => {
  const match = agentStatusData.find(item => item.value === status);
  return match ? match.label : '-';
}

//漏洞扫描任务类型
const vulTaskTypeData = [
  // {
  //   label: "内置漏扫",
  //   value: "Nessus"
  // },
  {
    label: "山石网科漏扫",
    value: "hillStone"
  }
];

//获取漏扫类型Label
const getVulTaskTypeLabel = (type: string) => {
  const match = vulTaskTypeData.find(item => item.value === type);
  return match ? match.label : '-';
}

//漏洞扫描任务进度
const vulTaskProcessData = [
  {
    label: "未执行",
    value: "notExecuted"
  },
  {
    label: "执行中",
    value: "executing"
  },
  {
    label: "执行完成",
    value: "executed"
  }
];

//获取漏扫进度Label
const getVulTaskProcessLabel = (value: string) => {
  const match = vulTaskProcessData.find(item => item.value === value);
  return match ? match.label : '-';
}

//获取漏扫进度tag标签类型
const getVulTaskProcessTagType = (value: string) => {
  switch (value) {
    case "notExecuted":
      return "info";
    case "executing":
      return "primary";
    case "executed":
      return "success";
    default:
      return "info";
  }
}

//山石支持的漏扫类型
const hillStoneVulTypeData = [
  {
    label: "系统扫描",
    value: "0"
  },
  {
    label: "弱口令扫描",
    value: "1"
  },
  {
    label: "WEB扫描",
    value: "3"
  }
];

//资产探测任务类型
const assetTaskTypeData = [
  {
    label: "全面扫描",
    value: "0"
  },
  {
    label: "存活资产扫描",
    value: "1"
  },
  {
    label: "端口扫描",
    value: "3"
  },
  {
    label: "软件扫描",
    value: "5"
  },
  {
    label: "WEB应用扫描",
    value: "4"
  }
];

//获取资产探测任务类型Label
const getAssetTaskTypeLabel = (type: string) => {
  const match = assetTaskTypeData.find(item => item.value === type);
  return match ? match.label : '-';
}

//获取资产探测界面界面标识
const getAssetTaskType = (typeVal: string) => {
  switch (typeVal) {
    case "全面扫描":
      return "fullScanResult";
    case "存活资产扫描":
      return "assetAliveScanResult";
    case "端口扫描":
      return "portScanResult";
    case "WEB应用扫描":
      return "webScanResult";
    case "软件扫描":
      return "softwareScanResult";
    default:
      return "fullScan";
  }
}


export {
  agentStatusData,
  getAgentStatusLabel,
  vulTaskTypeData,
  getVulTaskTypeLabel,
  vulTaskProcessData,
  getVulTaskProcessLabel,
  getVulTaskProcessTagType,
  hillStoneVulTypeData,
  assetTaskTypeData,
  getAssetTaskTypeLabel,
  getAssetTaskType
}
