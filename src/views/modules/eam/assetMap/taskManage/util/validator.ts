//校验主机探测任务表单
import {HostTaskFormData} from "@/views/modules/eam/assetMap/taskManage/api/customTaskApi";

const validateTaskForm = (form: HostTaskFormData): string => {
  if (!(form.jobName && form.jobName?.trim().length > 0)) {
    return '请输入任务名称！';
  }

  if (!(form.glueIds && form.glueIds?.length > 0)) {
    return '请选择采集指标！';
  }

  if (!(form.addressList && form.addressList.length > 0)) {
    return '请添加要执行任务的主机！';
  }

  return null;
}

export {
  validateTaskForm
}

