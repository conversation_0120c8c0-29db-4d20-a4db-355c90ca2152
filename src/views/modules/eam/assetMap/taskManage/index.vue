<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane name="assetTask">
        <template #label>
                <span class="flex-c">
                  <IconifyIconOffline icon="RI-DatabaseFill"/>
                  <span class="ml-1">资产探测</span>
                </span>
        </template>
        <asset-task-index/>
      </el-tab-pane>
      <el-tab-pane name="switchTask">
        <template #label>
                <span class="flex-c">
                  <IconifyIconOffline icon="RI-DeviceFill"/>
                  <span class="ml-1">交换机采集</span>
                </span>
        </template>
        <switch-task-index/>
      </el-tab-pane>
      <el-tab-pane name="vulTask" lazy>
        <template #label>
          <span class="flex-c">
            <IconifyIconOffline icon="RI-Bug2Fill"/>
            <span class="ml-1">漏洞探测</span>
          </span>
        </template>
        <vul-task-index/>
      </el-tab-pane>
      <el-tab-pane name="customTask" lazy>
        <template #label>
          <span class="flex-c">
            <IconifyIconOffline icon="RI-GitRepositoryCommitsFill"/>
            <span class="ml-1">自定义任务探测</span>
          </span>
        </template>
        <custom-task-index/>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import {reactive, toRefs} from 'vue';
import CustomTaskIndex from "@/views/modules/eam/assetMap/taskManage/components/CustomTaskIndex.vue";
import VulTaskIndex from "@/views/modules/eam/assetMap/taskManage/components/VulTaskIndex.vue";
import AssetTaskIndex from "@/views/modules/eam/assetMap/taskManage/components/AssetTaskIndex.vue";
import SwitchTaskIndex from "@/views/modules/eam/assetMap/taskManage/components/SwitchTaskIndex.vue";

//数据对象
const state = reactive({
  activeName: "assetTask",
})

const {
  activeName,
} = toRefs(state)

</script>
