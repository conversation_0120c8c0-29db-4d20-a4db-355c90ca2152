<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <KeepAlive include="AssetTaskMapping">
        <component
          :is="currentComponent"
          :taskInfo="selectedTask"
          @jump-to="comChange"
          @select-task="selectTaskHandler"
        />
      </KeepAlive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {reactive, shallowRef, toRefs} from 'vue';
import SwitchTaskMapping from "@/views/modules/eam/assetMap/taskManage/components/SwitchTaskMapping.vue";
import SwitchScanResult from "@/views/modules/eam/assetMap/taskManage/components/SwitchScanResult.vue";

const switchTaskMain = shallowRef(SwitchTaskMapping);
const switchScanResult = shallowRef(SwitchScanResult);


//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 已选择的任务
  selectedTask: null,
})
const {
  currentComponent,
  selectedTask,
} = toRefs(state)

state.currentComponent = switchTaskMain;

// 根据传入的值切换组件
const comChange = (val: string) => {
  let nextComponent: any;
  switch (val) {
    case "switchTaskMain":
      nextComponent = switchTaskMain;
      break;
    case "switchScanResult":
      nextComponent = switchScanResult;
      break;
  }
  state.currentComponent = nextComponent;
};

const selectTaskHandler = (task: any) => {
  state.selectedTask = task;
}


</script>
