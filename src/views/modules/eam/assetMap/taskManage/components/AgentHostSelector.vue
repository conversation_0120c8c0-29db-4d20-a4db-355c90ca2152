<template>
  <div>
    <div class="flex-c w-full gap-2 mb-3">
      <el-input v-model="searchCondition.ip" class="w-52" clearable/>
      <el-button type="primary" @click="resetTablePageAndQuery">查询</el-button>
    </div>

    <avue-crud
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @size-change="loadHostData"
      @current-change="loadHostData"
      @selection-change="selectionChangeHandler"
    >
      <template #agentState="{row}">
        <el-tag type="primary" effect="plain">{{ getAgentStatusLabel(row.agentState) }}</el-tag>
      </template>
    </avue-crud>

  </div>
</template>

<script lang="ts" setup>
import {reactive, toRefs} from 'vue';
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {getHostList} from "@/views/modules/eam/assetMap/taskManage/api/customTaskApi";
import {getAgentStatusLabel} from "@/views/modules/eam/assetMap/taskManage/util/task_data";

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    ip: ''
  },
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  tableData: [],
  selectedHosts: []
})
const {
  tableLoading,
  searchCondition,
  tablePage,
  tableData,
  selectedHosts
} = toRefs(state)

//表格选项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: false,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  header: false,
  menu: false,
  height: 300,
  rowKey: "ipAddress",
  column: [
    {
      label: "资产名称",
      prop: "name",
    },
    {
      label: "IP地址",
      prop: "ipAddress",
    },
    {
      label: "所属业务系统",
      prop: "opSystemLabel",
    },
    {
      label: "Agent状态",
      prop: "agentState",
      width: 120
    },
    {
      label: "授权用户",
      prop: "agentUser",
    }
  ]
});

//重置分页后查询数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  loadHostData();
}

//加载主机数据
const loadHostData = async () => {
  state.tableLoading = true;
  const res = await getHostList({
    ...state.searchCondition,
    pageIndex: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize
  });
  if (res.status === "0") {
    state.tableData = res.data;
    state.tablePage.total = res.total;
  }
  state.tableLoading = false;
}
loadHostData();

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  state.selectedHosts = selRows;
}

//获取已选择的主机数据
const getSelectedHosts = () => {
  return state.selectedHosts;
}

//对外暴露可调用方法
defineExpose({getSelectedHosts});

</script>
