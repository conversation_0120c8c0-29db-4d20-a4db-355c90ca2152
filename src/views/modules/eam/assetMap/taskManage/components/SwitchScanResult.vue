<template>
  <div>
    <div class="flex-bc w-full mb-2">
      <el-page-header @back="jumpTo('switchTaskMain')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> {{ taskInfo.policyName }} </span>
        </template>
      </el-page-header>
    </div>
    <avue-crud
      ref="tableRef"
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadTableData"
      @current-change="loadTableData"
    >
      <template #menu-left>
        <div class="float-left pr-3" style="vertical-align: top;margin-right:30px;">
          <el-checkbox v-model="checked" @change="changeCheckBox">显示空IP</el-checkbox>
        </div>
        <div class="float-left pr-3">
          <search-with-type-column v-model="searchCondition.value"
                              v-model:searchCondition="searchCondition"
                              :column-options="columnSearchOptions"
                              :column-select-width="120"
                              class="flex-sc w-[800px]"
                              @search="resetTablePageAndQuery"
                              @reset="resetCondition"
                              input-class-name="w-1/2"/>
        </div>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left pr-3">
          <el-tooltip
            content="导出扫描数据"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="useRenderIcon('EP-Download')"
              circle
              :size="size"
              :disabled="tableData==null||tableData.length==0"
              @click="exportOnlineData"
            />
          </el-tooltip>
        </div>
      </template>
      <template #onlineStatus="{row}">
          <div v-if="row['onlineStatus'] == '离线'">
            <div style="display:inline-block;width:12px;height:12px;border:1px solid grey;background-color: grey;border-radius: 6px;margin-right:5px;overflow:hidden;">
              <el-icon style="color:white;font-size:8px;vertical-align: 7px;"><CloseBold /></el-icon>
            </div>
            <span style="vertical-align: 2px;">离线</span>
          </div>
          <div v-if="row['onlineStatus'] == '在线'">
            <div style="display:inline-block;width:12px;height:12px;border:1px solid green;background-color: green;border-radius: 6px;margin-right:5px;overflow:hidden;">
              <el-icon style="color:white;font-size:8px;vertical-align: 6px;"><Select /></el-icon>
            </div>
            <span style="vertical-align: 2px;">在线</span>
          </div>
      </template>

    </avue-crud>
  </div>
</template>
<style scoped>
.numZcgl {
  color: #418AEC;
  width: 100px;
  margin-left: 10px;
  font-size: 16px;
  font-weight: bold
}
</style>
<script lang="ts" setup>
import {computed, onMounted, reactive, ref, toRefs} from 'vue';
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import SearchWithTypeColumn from "@/components/Search/SearchWithTypeColumn.vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {
  queryCollAccess,
  queryCollCondition,
  queryCollColumn,
  queryCollResult,
  exportCollResult, exportCollResultByWord
} from "@/views/modules/eam/assetMap/taskManage/api/assetMapping";
import {CloseBold, Select} from "@element-plus/icons-vue";


// 组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 360;
});
const exportVisible = ref(false);
const tableRef = ref(null);
const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "keyId",
  column: [
    {
      label: "资源IP地址",
      prop: "ip"
    },
    {
      label: "在线状态",
      prop: "onlineStatus"
    },
    {
      label: "采集时间",
      prop: "collectTime"
    }
  ]
})

// 定义事件
const emit = defineEmits(["jump-to"]);

//数据对象
const state = reactive({
  checked:false,
  tableLoading: false,
  columnSearchOptions: [],
  searchCondition: {
    field: "ipAddress",
    value: null,
    fuzzy: true,
    type: "Input"
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
})
const {
  tableLoading,
  columnSearchOptions,
  searchCondition,
  tableData,
  tablePage,
  checked
} = toRefs(state)

//重置分页后查询数据
const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}
const loadOnlineCount = () =>{
  queryCollAccess(props.taskInfo.cronId).then(res=>{
    state.ipCount = res.data.ipCount;
  })
}
const loadConditions = (params) =>{
  queryCollCondition(params).then(res=>{
    state.columnSearchOptions = res.data.columns;
    if(state.columnSearchOptions&&state.columnSearchOptions.length>0){
      state.searchCondition.field = state.columnSearchOptions[0].field;
      state.searchCondition.fuzzy = true;
      state.searchCondition.props = state.columnSearchOptions[0].component.props;
      state.searchCondition.type = state.columnSearchOptions[0].component.type;
      state.searchCondition.options = state.columnSearchOptions[0].component.options;
      state.searchCondition.value = null;
    }
  })
}
const loadColumns = (params) =>{
  queryCollColumn(params).then(res=>{
    let tt = [];
    if(res.data&&res.data.columns&&res.data.columns.length>0){
      for(let i = 0; i<res.data.columns.length; i++) {
        let mm = {
          prop: res.data.columns[i].key,
          label: res.data.columns[i].title
        }
        tt.push(mm);
      }

    }
    tableOption.value.column = tt;
  })
}

//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  let checkStatus = "";
  if(!state.checked){
    checkStatus = "1";
  }
  let params = {
    collType: "2",
    cronId: props.taskInfo.cronId,
    policyType:"2",
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    checkStatus:checkStatus,
    conditions: [],
  }
  if(state.searchCondition.value){
    let cm = {
      field: state.searchCondition.field,
      value: state.searchCondition.value,
      operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
    }
    params.conditions.push(cm);
  }
  queryCollResult(params).then(res=>{
    state.tableLoading = false;
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
  })
}
const resetCondition = () =>{
  if(state.columnSearchOptions&&state.columnSearchOptions.length>0) {
    state.searchCondition.field = state.columnSearchOptions[0].field;
    state.searchCondition.fuzzy = true;
    state.searchCondition.props = state.columnSearchOptions[0].component.props;
    state.searchCondition.type = state.columnSearchOptions[0].component.type;
    state.searchCondition.options = state.columnSearchOptions[0].component.options;
    state.searchCondition.value = null;
  }else{
    state.searchCondition = {
      field: "ipAddress",
        value: null,
        fuzzy: true,
        type: "Input"
    };
  }
  state.tablePage.currentPage = 1;
  loadTableData();
}
// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
const exportOnlineData = () =>{
  let checkStatus = "";
  if(!state.checked){
    checkStatus = "1";
  }
  let params = {
    collType: "2",
    cronId: props.taskInfo.cronId,
    policyType: "2",
    checkStatus: checkStatus,
    conditions: [],
  }
  if(state.searchCondition.value){
    let cm = {
      field: state.searchCondition.field,
      value: state.searchCondition.value,
      operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
    }
    params.conditions.push(cm);
  }
  exportCollResult(params).then(()=>{

  })
}
const changeCheckBox = (val) =>{
  state.checked = val;
  loadTableData();
}
onMounted(()=>{
  loadOnlineCount();
  let checkStatus = "";
  if(!state.checked){
    checkStatus = "1";
  }
  let params = {
    collType: "2",
    cronId: props.taskInfo.cronId,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    checkStatus: checkStatus,
    conditions: []
  }
  loadConditions(params);
  loadColumns(params);
  loadTableData();
})
</script>
