<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <KeepAlive include="AssetTaskMapping">
        <component
          :is="currentComponent"
          :taskInfo="selectedTask"
          @jump-to="comChange"
          @select-task="selectTaskHandler"
        />
      </KeepAlive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {reactive, shallowRef, toRefs} from 'vue';
import AssetTaskMapping from "@/views/modules/eam/assetMap/taskManage/components/AssetTaskMapping.vue";
import AssetFullScanResult from "@/views/modules/eam/assetMap/taskManage/components/AssetFullScanResult.vue";
import AssetPortScanResult from "@/views/modules/eam/assetMap/taskManage/components/AssetPortScanResult.vue";
import AssetSoftwareScanResult from "@/views/modules/eam/assetMap/taskManage/components/AssetSoftwareScanResult.vue";
import AssetAliveScanResult from "@/views/modules/eam/assetMap/taskManage/components/AssetAliveScanResult.vue";
import AssetWebScanResult from "@/views/modules/eam/assetMap/taskManage/components/AssetWebScanResult.vue";

const assetTaskMain = shallowRef(AssetTaskMapping);
const fullScanResult = shallowRef(AssetFullScanResult);
const portScanResult = shallowRef(AssetPortScanResult);
const softwareScanResult = shallowRef(AssetSoftwareScanResult);
const assetAliveScanResult = shallowRef(AssetAliveScanResult);
const webScanResult = shallowRef(AssetWebScanResult)

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 已选择的任务
  selectedTask: null,
})
const {
  currentComponent,
  selectedTask,
} = toRefs(state)

state.currentComponent = assetTaskMain;

// 根据传入的值切换组件
const comChange = (val: string) => {
  let nextComponent: any;
  switch (val) {
    case "assetTaskMain":
      nextComponent = assetTaskMain;
      break;
    case "fullScanResult":
      nextComponent = fullScanResult;
      break;
    case "portScanResult":
      nextComponent = portScanResult;
      break;
    case "softwareScanResult":
      nextComponent = softwareScanResult;
      break;
    case "assetAliveScanResult":
      nextComponent = assetAliveScanResult;
      break;
    case "webScanResult":
      nextComponent = webScanResult;
      break;
  }
  state.currentComponent = nextComponent;
};

const selectTaskHandler = (task: any) => {
  state.selectedTask = task;
}


</script>
