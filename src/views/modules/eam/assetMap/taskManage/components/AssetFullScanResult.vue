<template>
  <div>
    <div class="flex-bc w-full mb-2">
      <el-page-header @back="jumpTo('assetTaskMain')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> {{ taskInfo.name }}</span>
        </template>
      </el-page-header>
      <div class="flex-bc w-1/3 rounded pl-3 pr-3 bg-blue-500 bg-opacity-15">
        <div class="flex-sc gap-2">
          <el-text>存活IP:</el-text>
          <el-text class="text-primary font-bold">{{ ipCount }}</el-text>
          <el-text>存活端口:</el-text>
          <el-text class="text-primary font-bold">{{ portCount }}</el-text>
          <el-text>软件服务:</el-text>
          <el-text class="text-primary font-bold">{{ serverCount }}</el-text>
          <el-text>Web应用:</el-text>
          <el-text class="text-primary font-bold">{{ webCount }}</el-text>
        </div>
        <div>
          <el-text type="danger" class="font-bold">{{ `用时: ${collTime ? collTime : '未知'}` }}</el-text>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName" class="w-full" @tab-click="changeTabs">
      <el-tab-pane label="基本信息" name="base"></el-tab-pane>
      <el-tab-pane label="存活端口" name="port"></el-tab-pane>
      <el-tab-pane label="软件服务" name="server"></el-tab-pane>
      <el-tab-pane label="Web应用" name="web"></el-tab-pane>
    </el-tabs>
    <avue-crud
      ref="tableRef"
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadTableData"
      @current-change="loadTableData"
    >
      <template #menu-left>
        <div class="float-left pr-3" style="vertical-align: top;margin-right:30px;" v-if="activeName == 'base'">
          <el-radio-group v-model="onlineStatus" size="small" @change="changeOnlineStatus" style="vertical-align: 11px;">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="online" >在线</el-radio-button>
            <el-radio-button label="outline">离线</el-radio-button>
          </el-radio-group>
        </div>
        <div class="float-left pr-3">
          <search-with-type-column v-model="searchCondition.value"
                                   v-model:searchCondition="searchCondition"
                                   :column-options="columnSearchOptions"
                                   :column-select-width="120"
                                   class="flex-sc w-[800px]"
                                   @search="resetTablePageAndQuery"
                                   @reset="resetCondition"
                                   input-class-name="w-1/2"/>
        </div>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left pr-3">
          <el-tooltip
            content="导出扫描数据"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="useRenderIcon('EP-Download')"
              circle
              :size="size"
              :disabled="tableData==null||tableData.length==0"
              @click="exportCollData"
            />
          </el-tooltip>
        </div>
      </template>
      <template #portImage="{ row }">
        <el-button
          type="text"
          @click="viewPicture(row.portImage)"
        >
          查看图片
        </el-button>
      </template>
    </avue-crud>
    <el-dialog v-model="showImageDialog" title="图片" :close-on-click-modal="false">
      <div style="overflow: auto;height: 500px">
        <img   v-if="imageUrl"  :src="imageUrl"  style="width: 100%" />
      </div>
    </el-dialog>
    <el-dialog
      title="导出"
      width="500px"
      v-model="exportVisible"
      :close-on-click-modal="false"
    >
      <div style="text-align: center;height:100px;margin-top:50px;">
        <el-checkbox-group v-model="checkExport">
          <el-checkbox label="EXCEL导出" border></el-checkbox>
          <el-checkbox label="PDF导出" border></el-checkbox>
          <el-checkbox label="WORD导出" border></el-checkbox>
        </el-checkbox-group>
      </div>
      <div style="text-align: right;">
        <el-button type="primary" @click="exportCheck">确定</el-button>
        <el-button type="primary" @click="closeCheck">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {computed, onMounted, reactive, ref, toRefs} from 'vue';
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import SearchWithTypeColumn from "@/components/Search/SearchWithTypeColumn.vue";
import {
  exportCollResult, exportCollResultByWord,
  queryAllCollAccess,
  queryCollColumn,
  queryCollCondition, queryCollResult
} from "@/views/modules/eam/assetMap/taskManage/api/assetMapping";

// 组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 360;
});
const exportVisible = ref(false);
const tableRef = ref(null);
const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "keyId",
  column: []
})

// 定义事件
const emit = defineEmits(["jump-to"]);

//数据对象
const state = reactive({
  activeName: "base",
  tableLoading: false,
  columnSearchOptions: [],
  searchCondition: {
    field: "ipAddress",
    value: null,
    fuzzy: true,
    type: "Input"
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  ipCount:0,
  portCount:0,
  serverCount:0,
  webCount:0,
  collTime:'',
  onlineStatus:'online',
  imageUrl:"",
  checkExport:["EXCEL导出"],
})
const {
  activeName,
  tableLoading,
  columnSearchOptions,
  searchCondition,
  tableData,
  tablePage,
  ipCount,
  portCount,
  serverCount,
  webCount,
  collTime,
  onlineStatus,
  imageUrl,
  checkExport
} = toRefs(state)

const onlineStatusOptions = [
  {
    label: "全部",
    value: "0"
  },
  {
    label: "在线",
    value: "1"
  },
  {
    label: "离线",
    value: "2"
  }
]

//重置分页后查询数据
const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}
const showImageDialog = ref(false);
//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  let params = {
    collType: "1",
    cronId: props.taskInfo.cronId,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    onlineStatus: state.onlineStatus,
    tabType: state.activeName,
    conditions: [],
  }
  if(state.searchCondition.value){
    let cm = {
      field: state.searchCondition.field,
      value: state.searchCondition.value,
      operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
    }
    params.conditions.push(cm);
  }
  queryCollResult(params).then(res=>{
    state.tableLoading = false;
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
  })
}

const resetCondition = () =>{
  if(state.columnSearchOptions&&state.columnSearchOptions.length>0) {
    state.searchCondition.field = state.columnSearchOptions[0].field;
    state.searchCondition.fuzzy = true;
    state.searchCondition.props = state.columnSearchOptions[0].component.props;
    state.searchCondition.type = state.columnSearchOptions[0].component.type;
    state.searchCondition.options = state.columnSearchOptions[0].component.options;
    state.searchCondition.value = null;
  }else{
    state.searchCondition = {
      field: "ipAddress",
      value: null,
      fuzzy: true,
      type: "Input"
    };
  }
  state.tablePage.currentPage = 1;
  loadTableData();
}
const changeOnlineStatus = (value) => {
  state.onlineStatus = value;
  state.tablePage.currentPage = 1;
  loadTableData();
}
// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
const initCount = () => {
  queryAllCollAccess(props.taskInfo.cronId).then(res=>{
    state.ipCount = res.data.ipCount;
    state.portCount = res.data.portCount;
    state.serverCount = res.data.serverCount;
    state.webCount = res.data.webCount;
    state.collTime = res.data.collTime;
  })
}
const exportCheck = () =>{
    let params = {
      collType: "1",
      cronId: props.taskInfo.cronId,
      conditions: [],
      tabType: state.activeName,
    }
    if(state.searchCondition.value){
      let cm = {
        field: state.searchCondition.field,
        value: state.searchCondition.value,
        operator: state.searchCondition.type=='DatePicker'?'datetimerange':state.searchCondition.fuzzy?"fuzzy":null
      }
      params.conditions.push(cm);
    }
  if(state.checkExport.indexOf("EXCEL导出")>=0){
    exportCollResult(params).then(()=>{
    })
  }
  if(state.checkExport.indexOf("WORD导出")>=0){
    params['exportType'] = '1';
    exportCollResultByWord(params).then(()=>{
    })
  }
  if(state.checkExport.indexOf("PDF导出")>=0){
    params['exportType'] = '2';
    exportCollResultByWord(params).then(()=>{
    })
  }
}
const changeTabs = (value) =>{
  state.activeName = value.props.name;
  initConditions();
  initColumn();
  state.tablePage.currentPage = 1;
  loadTableData();
}
const initConditions = () =>{
  let params = {
    collType: "1",
    cronId: props.taskInfo.cronId,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    onlineStatus: state.onlineStatus,
    tabType: state.activeName,
    conditions: [],
  }
  queryCollCondition(params).then(res=>{
    state.columnSearchOptions = res.data.columns;
    if(state.columnSearchOptions&&state.columnSearchOptions.length>0){
      state.searchCondition.field = state.columnSearchOptions[0].field;
      state.searchCondition.fuzzy = true;
      state.searchCondition.props = state.columnSearchOptions[0].component.props;
      state.searchCondition.type = state.columnSearchOptions[0].component.type;
      state.searchCondition.options = state.columnSearchOptions[0].component.options;
      state.searchCondition.value = null;
    }
  })
}
const initColumn = () =>{
  let params = {
    collType: "1",
    cronId: props.taskInfo.cronId,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
    onlineStatus: state.onlineStatus,
    tabType: state.activeName,
    conditions: [],
  }
  queryCollColumn(params).then(res=>{
    let tt = [];
    if(res.data&&res.data.columns&&res.data.columns.length>0){
      for(let i = 0; i<res.data.columns.length; i++) {
        let mm = {
          prop: res.data.columns[i].key,
          label: res.data.columns[i].title,
          showOverflowTooltip:true
        }
        tt.push(mm);
      }
    }
    tableOption.value.column = tt;
  })
}
const viewPicture = (src) =>{
  state.imageUrl = src;
  showImageDialog.value = true;
}
const exportCollData = () =>{
  exportVisible.value = true;
}
const closeCheck = () =>{
  exportVisible.value = false;
}
onMounted(()=>{
  initCount();
  initConditions();
  initColumn();
  loadTableData();
})
</script>
