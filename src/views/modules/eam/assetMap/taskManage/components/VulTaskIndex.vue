<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <component
        :is="currentComponent"
        :taskInfo="selectedTask"
        :hostInfo="selectedHostInfo"
        @jump-to="comChange"
        @select-task="selectTaskHandler"
        @select-host="selectHostHandler"
      />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {reactive, shallowRef, toRefs} from 'vue';
import VulTaskMapping from "@/views/modules/eam/assetMap/taskManage/components/VulTaskMapping.vue";
import VulTaskResult from "@/views/modules/eam/assetMap/taskManage/components/VulTaskResult.vue";
import VulInfoView from "@/views/modules/eam/assetMap/taskManage/components/VulInfoView.vue";

const vulTaskMain = shallowRef(VulTaskMapping);
const vulTaskResult = shallowRef(VulTaskResult);
const vulInfoView = shallowRef(VulInfoView);

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 已选择的任务
  selectedTask: null,
  // 已选择的主机信息
  selectedHostInfo: null,
})
const {
  currentComponent,
  selectedTask,
  selectedHostInfo,
} = toRefs(state)

state.currentComponent = vulTaskMain;

// 根据传入的值切换组件
const comChange = (val: string) => {
  let nextComponent: any;
  switch (val) {
    case "vulTaskMain":
      nextComponent = vulTaskMain;
      break;
    case "vulTaskResult":
      nextComponent = vulTaskResult;
      break;
    case "vulInfoView":
      nextComponent = vulInfoView;
      break;
  }
  state.currentComponent = nextComponent;
};

const selectTaskHandler = (task: any) => {
  state.selectedTask = task;
}

const selectHostHandler = (host: any) => {
  state.selectedHostInfo = host;
}

</script>
