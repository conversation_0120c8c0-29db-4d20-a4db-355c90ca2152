<template>
  <div class="p-3" v-loading="importLoading">
    <el-divider content-position="left">
      <sign-title title="任务信息" />
    </el-divider>
    <div class="flex-sc p-2 gap-1 pt-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20 text-right pr-2">任务名称：</el-text>
      <el-input
        v-model="form.policyName"
        placeholder="请输入任务名称"
        clearable
        maxlength="64"
        show-word-limit
      />
    </div>
    <div
      class="text-red-600"
      style="padding-left: 6rem; font-size: 12px"
      v-if="policyNameValidata"
    >
      请输入任务名称
    </div>
    <div class="flex w-full">
      <div class="flex-sc gap-1 p-2 w-1/2">
        <span class="text-red-600">*</span>
        <el-text class="w-20 mr-1 text-right">任务类型：</el-text>
        <el-select
          v-model="form.policyType"
          placeholder="请选择任务类型"
          filterable
          :disabled="taskInfo.cronId"
        >
          <el-option :key="'2'" label="交换机采集" :value="'2'" />
        </el-select>
      </div>
    </div>
    <div class="flex-sc gap-1 p-2">
      <el-text class="w-20 text-right mr-1">交换机扫描：</el-text>
      <el-button type="primary" @click="selectSwitch">选择交换机</el-button>
    </div>
    <div>
      <el-text style="margin-left: 6rem; color: #409eff"
        >已选择交换机<span style="padding: 0 3px; font-weight: bold">{{
          !currSwitchList ? 0 : currSwitchList.length
        }}</span
        >台,共有交换机<span style="padding: 0 3px; font-weight: bold">{{
          switchTableData.length
        }}</span
        >台</el-text
      >
    </div>
    <div class="flex-sc gap-1 p-2 mb-5">
      <el-text class="w-20 text-right mr-1">执行周期：</el-text>
      <simple-schedule
        ref="simpleScheduleRef"
        v-model:exp="form.schedulingCycle"
        :enable-minute="true"
        :minMinute="5"
        :enable-hour="true"
        action-type="H"
      />
    </div>
  </div>
  <el-dialog
    title="选择交换机设备"
    v-model="switchDialogShow"
    width="1200"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <el-row>
          <el-col :span="12" style="text-align: left">
            <h4 :id="titleId" :class="titleClass">选择交换机设备</h4>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button type="primary" @click="saveSwitch"> 保存 </el-button>
            <el-button type="primary" @click="closeSwitch"> 关闭 </el-button>
          </el-col>
        </el-row>
      </div>
    </template>
    <div>
      <el-button type="primary" @click="addSwitch">新增</el-button>
              <el-upload
                style="display: inline-block; padding: 0 10px; vertical-align: -1px"
                class="upload-demo"
                action="/rest/eam-collection/operation/probe3/conf/importSwitchData"
                :on-success="uploadSwitchSuccess"
                :on-error="uploadSwitchError"
                :show-file-list="false"
                :before-upload="beforeUploadSwtich"
              >
                <el-button type="primary">导入</el-button>
              </el-upload>
              <el-button type="primary" @click="exportSwitchExcelTemp"
                >导出模板</el-button
              >
      <el-input v-model="selectInput" style="width: 300px;float:right;">
        <template #append>
          <el-button type="primary" @click="querySwitchTableData"
          >查询</el-button
          >
          <el-button
            style="margin-left: 10px"
            type="primary"
            @click="resetSwitchTableData"
          >重置</el-button
          >
        </template>
      </el-input>
    </div>
      <div style="height: 500px">

        <el-auto-resizer>
          <template #default="{ height, width }">
            <el-table-v2
              :columns="switchTableColumn"
              :data="switchTableData"
              :width="width"
              :height="height"
              fixed
              border
            />
          </template>
        </el-auto-resizer>
      </div>


    <!--    <avue-crud-->
    <!--      ref="switchTableRef"-->
    <!--      :data="switchTableData"-->
    <!--      :option="switchTableOption"-->
    <!--      :table-loading="tableLoading"-->
    <!--      @selection-change="switchSelectChange"-->
    <!--    >-->
    <!--      <template #menu-left>-->
    <!--        <el-button type="primary" @click="addSwitch">新增</el-button>-->
    <!--        <el-upload-->
    <!--          style="display: inline-block; padding: 0 10px; vertical-align: -1px"-->
    <!--          class="upload-demo"-->
    <!--          action="/rest/eam-collection/operation/probe3/conf/importSwitchData"-->
    <!--          :on-success="uploadSwitchSuccess"-->
    <!--          :on-error="uploadSwitchError"-->
    <!--          :show-file-list="false"-->
    <!--          :before-upload="beforeUploadSwtich"-->
    <!--        >-->
    <!--          <el-button type="primary">导入</el-button>-->
    <!--        </el-upload>-->
    <!--        <el-button type="primary" @click="exportSwitchExcelTemp"-->
    <!--          >导出模板</el-button-->
    <!--        >-->
    <!--      </template>-->
    <!--      <template #menu-right="{ size }">-->
    <!--        <div class="float-left pr-3">-->
    <!--          <el-input v-model="selectInput">-->
    <!--            <template #append>-->
    <!--              <el-button type="primary" @click="querySwitchTableData"-->
    <!--                >查询</el-button-->
    <!--              >-->
    <!--              <el-button-->
    <!--                style="margin-left: 10px"-->
    <!--                type="primary"-->
    <!--                @click="resetSwitchTableData"-->
    <!--                >重置</el-button-->
    <!--              >-->
    <!--            </template>-->
    <!--          </el-input>-->
    <!--        </div>-->
    <!--      </template>-->
    <!--      <template #menu="{ row, size, type }">-->
    <!--        <el-button-->
    <!--          type="text"-->
    <!--          v-if="!row.editType || row.editType != 'edit'"-->
    <!--          style="margin: 0; padding: 0"-->
    <!--          @click="editRow(row)"-->
    <!--          >编辑</el-button-->
    <!--        >-->
    <!--        <el-button-->
    <!--          type="text"-->
    <!--          v-else-->
    <!--          style="margin: 0; padding: 0"-->
    <!--          @click="editRow(row)"-->
    <!--          >保存</el-button-->
    <!--        >-->
    <!--        <el-button-->
    <!--          type="text"-->
    <!--          style="margin: 0; padding: 0; margin-left: 5px"-->
    <!--          @click="deleteRow(row)"-->
    <!--          >删除</el-button-->
    <!--        >-->
    <!--      </template>-->
    <!--      <template #ipAddress="{ row }">-->
    <!--        <el-input-->
    <!--          type="text"-->
    <!--          v-model="row.ipAddress"-->
    <!--          :disabled="!(row.editType && row.editType == 'edit' && row.editIp)"-->
    <!--        ></el-input>-->
    <!--      </template>-->
    <!--      <template #connectType="{ row }">-->
    <!--        <el-select-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--          size="small"-->
    <!--          v-model="row.connectType"-->
    <!--        >-->
    <!--          <el-option label="telnet" value="telnet"></el-option>-->
    <!--          <el-option label="ssh" value="ssh"></el-option>-->
    <!--        </el-select>-->
    <!--      </template>-->
    <!--      <template #connectPort="{ row }">-->
    <!--        <el-input-number-->
    <!--          v-model="row.connectPort"-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--        ></el-input-number>-->
    <!--      </template>-->
    <!--      <template #userName="{ row }">-->
    <!--        <el-input-->
    <!--          type="text"-->
    <!--          v-model="row.userName"-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--        ></el-input>-->
    <!--      </template>-->
    <!--      <template #password="{ row }">-->
    <!--        <el-input-->
    <!--          type="password"-->
    <!--          v-model="row.password"-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--        ></el-input>-->
    <!--      </template>-->
    <!--      <template #switchType="{ row }">-->
    <!--        <el-select-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--          size="small"-->
    <!--          v-model="row.switchType"-->
    <!--        >-->
    <!--          <el-option label="核心" value="1"></el-option>-->
    <!--          <el-option label="接入" value="2"></el-option>-->
    <!--          <el-option label="汇聚" value="3"></el-option>-->
    <!--        </el-select>-->
    <!--      </template>-->
    <!--      <template #manufacturer="{ row }">-->
    <!--        <el-select-->
    <!--          :disabled="!(row.editType && row.editType == 'edit')"-->
    <!--          size="small"-->
    <!--          v-model="row.manufacturer"-->
    <!--        >-->
    <!--          <el-option-->
    <!--            v-for="(item, index) in productList"-->
    <!--            :label="item.productName"-->
    <!--            :value="item.productCode"-->
    <!--            :key="index"-->
    <!--          ></el-option>-->
    <!--        </el-select>-->
    <!--      </template>-->
    <!--    </avue-crud>-->
  </el-dialog>
  <el-dialog v-model="importResultShow" title="导入结果" width="600">
    <el-result :icon="importIcon" :title="importResult" :sub-title="importMsg">
      <template #extra>
        <div
          v-if="importErrors && importErrors.length > 0"
          style="text-align: left; max-height: 200px; overflow: auto"
        >
          <div style="font-weight: bold">失败信息：</div>
          <div
            v-for="item in importErrors"
            style="padding-left: 10px; color: gray"
          >
            {{ item }}
          </div>
        </div>
      </template>
    </el-result>
  </el-dialog>
</template>

<script lang="tsx" setup>
import {reactive, toRefs, ref, onMounted, nextTick, unref, type FunctionalComponent} from 'vue';
import SignTitle from "@/components/SignTitle/SignTitle.vue";
import SimpleSchedule from "@/components/SimpleSchedule/SimpleSchedule.vue";
import {
  ElMessage,
  ElMessageBox,
  ElCheckbox,
  ElInput,
  ElSelect,
  ElOption,
  ElInputNumber,
  ElButton,
  type Column
} from "element-plus";
import {v4 as uuidv4 } from "uuid";
import {
  deleteSwitchTableInfo, exportSwitchTemp,
  insertOrUpdateAllCron, insertSwitchTableInfo,
  queryCollPolicyExists,
  queryCronByCronId, queryProductInfo, querySwitchTableInfo
} from "@/views/modules/eam/assetMap/taskManage/api/assetMapping";
import type { CheckboxValueType } from 'element-plus'
import {sm4} from "sm-crypto";

const sm4Key = "1B6E03BAE001B71D1AC6377806E2FF63";
//组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});
const importLoading = ref(false);
const importResultShow = ref(false);
const switchTableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  columnBtn:false,
  gridBtn:false,
  refreshBtn:false,
  menu: true,
  menuWidth: "100px",
  height:"500px",
  selection: true,
  selectionFixed: true,
  reserveSelection: true,
  rowKey: "id",
  column: [
    {
      label: "IP地址",
      prop: "ipAddress"
    },
    {
      label: "链接协议",
      prop: "connectType"
    },
    {
      label: "端口",
      prop: "connectPort"
    },
    {
      label: "账号",
      prop: "userName"
    },
    {
      label: "口令",
      prop: "password",
    },
    {
      label: "交换机类型",
      prop: "switchType",
    },
    {
      label: "厂商",
      prop: "manufacturer"
    }
  ]
})

const generateColumns = (length = 7, prefix = 'column-', props?: any) =>
  switchTableOption.value.column.map((column, columnIndex) => ({
    key: column.prop,
    dataKey: column.prop,
    title: column.label,
    width:150,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: string) => {
        rowData[column.prop!] = value
      }
      if(column.prop=='ipAddress'||column.prop=='userName'){
        return (
          <ElInput
            disabled={!(rowData.editType&&rowData.editType=='edit'&& rowData.editIp)}
            modelValue={rowData[column.prop!]}
            onInput={onChange}
          />
        )
      }else if(column.prop=='connectType'){
        const onChange = (value: string) => {
          rowData[column.prop!] = value
        }
        return  (
          <ElSelect
            size={"small"}
            disabled={!(rowData.editType&&rowData.editType=='edit')}
            modelValue={rowData[column.prop!]}
            onChange={onChange}
          >
            <ElOption value="telnet" label="telnet"/>
            <ElOption value="ssh" label="ssh"/>
          </ElSelect>
        )
      }else if(column.prop=='connectPort'){
        const onChange = (value: string) => {
          rowData[column.prop!] = value
        }
        return (
          <ElInputNumber
            disabled={!(rowData.editType&&rowData.editType=='edit')}
            modelValue={rowData[column.prop!]}
            onInput={onChange}
            max={65535}
            min={0}
          />
        )
      }else if(column.prop=='password'){
        const onChange = (value: string) => {
          rowData[column.prop!] = value
        }
        return (
          <ElInput
            type={"password"}
            disabled={!(rowData.editType&&rowData.editType=='edit')}
            modelValue={rowData[column.prop!]}
            onInput={onChange}
          />
        )
      }else if(column.prop=='switchType'){
        const onChange = (value: string) => {
          rowData[column.prop!] = value
        }
        return (
          <ElSelect
            size={"small"}
            disabled={!(rowData.editType && rowData.editType == "edit")}
            modelValue={rowData[column.prop!]}
            onChange={onChange}
          >
            <ElOption label="核心" value="1"/>
            <ElOption label="接入" value="2"/>
            <ElOption label="汇聚" value="3"/>
          </ElSelect>
        );
      }else if(column.prop=='manufacturer'){
        const onChange = (value: string) => {
          rowData[column.prop!] = value
        }
        return (
          <ElSelect
            size={"small"}
            disabled={!(rowData.editType && rowData.editType == "edit")}
            modelValue={rowData[column.prop!]}
            onChange={onChange}
          >
            {state.productList.map((item) => {
              return <ElOption
                label={item.productName}
                value={item.productCode}
                key={item.productCode}/>
            })}
          </ElSelect>
        );
      }
    }
  }))

const switchTableColumn: Column<any>[] = generateColumns(7)
//声明事件
const emit = defineEmits(["success"]);
type SelectionCellProps = {
  value: boolean
  intermediate?: boolean
  onChange: (value: CheckboxValueType) => void
}
const SelectionCell: FunctionalComponent<SelectionCellProps> = ({
                                                                  value,
                                                                  intermediate = false,
                                                                  onChange,
                                                                }) => {
  return (
    <ElCheckbox
      onChange={onChange}
      modelValue={value}
      indeterminate={intermediate}
    />
  )
}
switchTableColumn.unshift({
  key: 'selection',
  width: 20,
  cellRenderer: ({rowData}) => {
    const onChange = (value: CheckboxValueType) => {
      return (rowData.checked = value);
    };
    // changeFilterCheckd();
    return <SelectionCell value={rowData.checked} onChange={onChange}/>
  },

  headerCellRenderer: () => {
    const _data = unref(switchTableData)
    const onChange = (value: CheckboxValueType) =>
      (state.switchTableData = _data.map((row) => {
        row.checked = value;
        return row
      }))
    const allSelected = _data.every((row) => row.checked)
    const containsChecked = _data.some((row) => row.checked)

    return (
      <SelectionCell
        value={allSelected}
        intermediate={containsChecked && !allSelected}
        onChange={onChange}
      />
    )
  }
})

switchTableColumn.push({
  title: "操作",
  width:110,
  align:"left",
  cellRenderer: ({rowData}) => {
    const editRow = () =>{
      if(rowData.editType&&rowData.editType=="edit"){
        if(!rowData.ipAddress){
          ElMessage.error("IP地址不能为空");
          return;
        }
        if(!rowData.connectType){
          ElMessage.error("链接协议不能为空");
          return;
        }
        if(!rowData.connectPort){
          ElMessage.error("端口不能为空");
          return;
        }
        if(!rowData.switchType){
          ElMessage.error("交换机类型不能为空");
          return;
        }
        if(!rowData.manufacturer){
          ElMessage.error("厂商不能为空");
          return;
        }
        if(!ipReg(rowData.ipAddress)){
          ElMessage.error("IP地址格式不正确，请修改IP地址");
          return;
        }
        let flag = false;
        for(let i in state.switchTableData){
          if(rowData!=state.switchTableData[i]&&rowData.ipAddress == state.switchTableData[i].ipAddress){
            flag = true;
            break;
          }
        }
        if(flag){
          ElMessage.error("当前IP地址已存在，请修改");
          return;
        }
        rowData.editIp = false;
        rowData.password = sm4.encrypt(rowData.password, sm4Key);
        let saveParams = {
          ipAddress:rowData.ipAddress,
          connectType:rowData.connectType,
          connectPort:rowData.connectPort,
          userName:rowData.userName,
          password:rowData.password,
          switchType:rowData.switchType,
          manufacturer:rowData.manufacturer
        }
        insertSwitchTableInfo(saveParams).then(res=>{
          if(res.data.result=='success'){
            ElMessage.success("保存成功");
            rowData.editType = null;
          }else{
            ElMessage.error("保存失败");
          }
        })
      }else{
        rowData.password = sm4.decrypt(rowData.password, sm4Key);
        rowData.editType = 'edit';
      }
      for(let i in state.switchAllData){
        if(state.switchAllData[i].id == rowData.id){
          state.switchAllData[i] = rowData;
        }
      }
    }
    const deleteRow = () =>{
      ElMessageBox.confirm('确认要删除吗？','提示',{
        confirmButtonText: '确认',
        cancelButtonText: '取消',
      }).then(()=>{
        let deleteId = -1;
        for(let i in state.switchTableData){
          if(rowData.id==state.switchTableData[i].id){
            deleteId = i;
          }
        }
        state.switchTableData.splice(deleteId,1);
        state.switchAllData.splice(deleteId,1);
        if(rowData.ipAddress){
          let ips = [];
          ips.push(rowData.ipAddress);
          deleteSwitchTableInfo({
            "ips":ips
          }).then(res1=>{
            if(res1.data.result=='success'){
              ElMessage.success("删除成功");
            }
          })
        }
      }).catch(()=>{

      })
    }
    return (
      <div>
        <ElButton
          type="primary"
          link
          size={"small"}
          style = {"margin: 0; padding: 0; margin-left: 2px"}
          v-show={!(rowData.editType && rowData.editType == "edit")}
          onClick={() => {
            editRow();
          }}
        > 编辑 </ElButton>
        < ElButton
          type="primary"
          size={"small"}
          link
          style = {"margin: 0; padding: 0; margin-left: 2px"}
          v-show={(rowData.editType && rowData.editType == "edit")}
          onClick={() => {
            editRow();
          }}
        >保存
        </ElButton>
          <ElButton
            type="primary"
            size={"small"}
            link
            style = {"margin: 0; padding: 0; margin-left: 2px"}
            onClick={() => {
              deleteRow();
            }}
          > 删除 </ElButton>
      </div>
    )
  }
})
//数据对象
const state = reactive({
  form: {
    cronId: null,
    policyName:'',
    policyType:'2',
    schedulingCycle:"1H"
  } as any,
  switchTableData:[],
  policyNameValidata:false,
  currSwitchList: [],
  tableLoading:false,
  selectInput:'',
  productList:[],
  selectIps:[],
  selectSwitchData:[],
  switchAllData:[],
  importIcon:'success',
  importResult:'成功',
  importMsg:'',
  importErrors:[]
})
const {
  form,
  policyNameValidata,
  switchTableData,
  tableLoading,
  selectInput,
  productList,
  currSwitchList,
  importIcon,
  importResult,
  importMsg,
  importErrors
} = toRefs(state)
const switchDialogShow = ref(false);
const simpleScheduleRef = ref(SimpleSchedule);

onMounted(()=>{
  simpleScheduleRef.value.initExp(state.form.schedulingCycle);
})
//提交表单数据
const submitData = async () => {
  simpleScheduleRef.value.generateExp();
  let operationAssetIpDataVos = [];
  let flag = false;
  if(!state.form.policyName){
    flag = true;
    state.policyNameValidata = true;
  }
  if(flag){
    ElMessage.error("表单校验不通过");
    return;
  }
  if(state.currSwitchList&&state.currSwitchList.length>0){
    operationAssetIpDataVos.push(...state.currSwitchList);
  }
  let params = {
    cronId: props.taskInfo?.cronId,
    collType: '2',
    policyType: state.form.policyType,
    policyName: state.form.policyName,
    schedulingCycle: state.form.schedulingCycle,
    operationAssetCronDataVo: operationAssetIpDataVos,
    operationAssetIpDataVos: operationAssetIpDataVos
  };
  queryCollPolicyExists({policyName: state.form.policyName, cronId: props.taskInfo?.cronId}).then(res=>{
    if (res.data == "success") {
      insertOrUpdateAllCron(params).then(()=>{
        ElMessage.success("保存成功");
        emit("success");
      })
    } else {
      ElMessage.error("任务名称已重复，请修改任务名称！");
    }
  })
}

onMounted(()=>{
  if(props.taskInfo&&props.taskInfo.cronId){
    queryCronByCronId(props.taskInfo.cronId).then(res=>{
      state.form =  {
          cronId: res.data.cronId,
          policyName:res.data.policyName,
          policyType:res.data.policyType+"",
          schedulingCycle:res.data.schedulingCycle,
      }
      state.currSwitchList=res.data.operationAssetSwitchDataVos;
    })
  }
  querySwitchTableCount();
})
const querySwitchTableData = () =>{
  state.tableLoading = true;
  state.switchTableData = state.switchAllData.filter(row=>{
    return row.ipAddress.indexOf(state.selectInput)>=0;
  })
  // state.switchTableData = state.switchTableData.filter(row=>{
  //   return row.ipAddress.indexOf(state.selectInput)>=0;
  // })
  // querySwitchTableInfo(state.selectInput).then(res=> {
  //   state.switchTableData = res.data.list;
  //   if (state.switchTableData && state.switchTableData.length > 0) {
  //     for (let i in state.switchTableData) {
  //       state.switchTableData[i].id = uuidv4();
  //     }
  //   }
    state.tableLoading = false;
  // });
}

const querySwitchTableDataByUrl = () =>{
  state.selectInput='';
  state.tableLoading = true;
  querySwitchTableInfo('').then(res=> {
    if(res.data&&res.data.list){
      state.switchTableData = res.data.list;
      state.switchAllData = JSON.parse(JSON.stringify(res.data.list));
      if(state.switchTableData&&state.switchTableData.length>0){
        for(let i in state.switchTableData){
          state.switchTableData[i].id = uuidv4();
          state.switchAllData[i].id = state.switchTableData[i].id;
        }
      }
      if(state.switchTableData!=null&&state.switchTableData.length>0&&state.currSwitchList&&state.currSwitchList.length>0){
        nextTick(()=>{
          for(let i in state.switchTableData){
            for(let k in state.currSwitchList){
              if(state.switchTableData[i].ipAddress==state.currSwitchList[k].ipAddress){
                // switchTableRef.value.toggleRowSelection(state.switchTableData[i],true);
                state.switchTableData[i]['checked'] = true;
              }
            }
          }
        })
      }
    }
    state.tableLoading = false;
  });
}
const switchTableRef = ref(null);

const selectSwitch = () =>{
  queryProductInfo({}).then(res=>{
    state.productList = res["data"].list;
  })
  switchDialogShow.value = true;
  state.tableLoading = true;
  querySwitchTableInfo("").then(res=>{
    if(res.data&&res.data.list){
      state.switchTableData = res.data.list;
      state.switchAllData = JSON.parse(JSON.stringify(res.data.list));
      if(state.switchTableData&&state.switchTableData.length>0){
        for(let i in state.switchTableData){
          state.switchTableData[i].id = uuidv4();
          state.switchAllData[i].id = state.switchTableData[i].id;
        }
      }
      if(state.switchTableData!=null&&state.switchTableData.length>0&&state.currSwitchList&&state.currSwitchList.length>0){
        nextTick(()=>{
          for(let i in state.switchTableData){
            for(let k in state.currSwitchList){
              if(state.switchTableData[i].ipAddress==state.currSwitchList[k].ipAddress){
                state.switchTableData[i]['checked'] = true;
                // switchTableRef.value.toggleRowSelection(state.switchTableData[i],true);
              }
            }
          }
        })
      }
    }
    state.tableLoading = false;
  })
}
const querySwitchTableCount = () =>{
  querySwitchTableInfo("").then(res=>{
    state.switchTableData = res.data.list;
    switchDialogShow.value = false;
  });
}
const saveSwitch = () =>{
  switchSelectChange(state.switchTableData);
  state.currSwitchList = state.selectSwitchData;
  clearSwitchSelect();
  // switchTableRef.value.clearSelection();
  state.selectInput = '';
  querySwitchTableCount();
}
const addSwitch = () =>{
  let id = uuidv4();
  let row = {
    id: id,
    ipAddress: "",
    connectType:'telnet',
    connectPort:23,
    userName:'',
    password:'',
    switchType:null,
    manufacturer:null,
    editType: 'edit',
    editIp:true
  }
  state.switchTableData.unshift(row);
  state.switchAllData.unshift(row);
}
const clearSwitchSelect = () =>{
  for(let i in state.switchTableData){
    state.switchTableData[i].checked = false;
  }
}
const closeSwitch = () =>{
  state.selectInput = '';
  clearSwitchSelect();
  // switchTableRef.value.clearSelection();
  switchDialogShow.value = false;
}
const ipReg = (ip) =>{
  var re =  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return re.test(ip);
}


const switchSelectChange = (value) =>{
  state.selectIps = [];
  state.selectSwitchData = [];
  if(value&&value.length>0){
    for(let i in value){
      if(value[i].ipAddress&&value[i].checked){
        state.selectIps.push(value[i].ipAddress);
        let s = {
          ipAddress:value[i].ipAddress,
          connectType:value[i].connectType,
          connectPort:value[i].connectPort,
          userName:value[i].userName,
          password:value[i].password,
          switchType:value[i].switchType,
          manufacturer:value[i].manufacturer
        }
        state.selectSwitchData.push(s);
      }
    }
  }
}
const resetSwitchTableData = () =>{
  state.selectInput = '';
  querySwitchTableData();
}
const exportSwitchExcelTemp = () =>{
  exportSwitchTemp().then(()=>{

  })
}
const uploadSwitchSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) =>{
    importResultShow.value = true;
    if(response.data.result=='success'){
      state.importIcon = "success";
      state.importResult = '导入成功';
      state.importErrors = response.data.errorList;
    }else{
      state.importIcon = "error";
      state.importResult = '导入失败';
    }
    state.importMsg = response.data.msg;
    importLoading.value = false;
    querySwitchTableDataByUrl();
}
const uploadSwitchError = (error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) =>{
  console.log(error);
  ElMessage.error("文件上传失败");
  importLoading.value = false;
}
const beforeUploadSwtich = (rawFile: UploadRawFile) =>{
  console.log(rawFile.name);
  if(rawFile.name.indexOf(".")>=0){
    if(rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xlsx'||rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xls'){
      importLoading.value = true;
      return true;
    }else{
      ElMessage.error("只支持识别Excel文件");
      return false;
    }
  }else{
    ElMessage.error("只支持识别Excel文件");
    return false;
  }
}
//对外暴露可调用方法
defineExpose({submitData});
</script>
