<template>
  <div>
    <div class="flex-bc w-full mb-2">
      <el-page-header @back="jumpTo('vulTaskResult')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> 漏洞信息 - {{ hostInfo.ip }}</span>
        </template>
      </el-page-header>
    </div>
    <avue-crud
      ref="tableRef"
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadTableData"
      @current-change="loadTableData"
    >
      <template #menu-left="{ size }">
        <div class="flex-sc w-full h-full">
          <search-with-column v-model="searchCondition.value"
                              v-model:fuzzy-enable="searchCondition.fuzzy"
                              v-model:column-val="searchCondition.field"
                              :column-options="columnSearchOptions"
                              :column-select-width="90"
                              class="flex-sc w-[800px]"
                              @search="resetTablePageAndQuery"
                              @reset="searchCondition.value=null"
                              input-class-name="w-1/2"/>
        </div>
      </template>
      <template #vul_level="{ row }">
        <el-text type="danger">{{ row.vul_level }}</el-text>
      </template>
      <template #vul_name="{ row }">
        <el-link type="primary" @click="openDetailViewDialog(row)">{{ row.vul_name }}</el-link>
      </template>
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import {computed, h, onMounted, reactive, ref, toRefs} from 'vue';
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {queryVulInfo} from "@/views/modules/eam/assetMap/taskManage/api/vulTaskApi";
import {ResultStatus} from "@/utils/http/types";
import VulDetailView from "@/views/modules/eam/assetMap/taskManage/components/VulDetailView.vue";
import {addDialog} from "@/components/ReDialog/index";

//编辑任务 Ref
const detailViewRef = ref<InstanceType<typeof VulDetailView>>();

// 组件属性
const props = defineProps({
  hostInfo: {
    type: Object,
    default: () => {
    }
  }
});

// 定义事件
const emit = defineEmits(["jump-to"]);

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    field: "vulname",
    value: '',
    fuzzy: true
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
})
const {
  tableLoading,
  searchCondition,
  tableData,
  tablePage
} = toRefs(state)

const columnSearchOptions = [
  {
    label: "漏洞名称",
    value: "vulname"
  },
  {
    label: "CVE编号",
    value: "vulNumberCve"
  }
]

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "ip_address",
  column: [
    {
      label: "漏洞名称",
      prop: "vul_name",
    },
    {
      label: "CVE编号",
      prop: "cve",
    },
    {
      label: "CNVD编号",
      prop: "db_vul_num_cnvd",
    },
    {
      label: "CNNVD编号",
      prop: "db_vul_num_cnnvd",
    },
    {
      label: "漏洞等级",
      prop: "vul_level",
    },
    {
      label: "漏洞类型",
      prop: "db_vul_type",
    },
    {
      label: "漏洞描述",
      prop: "vul_desc",
      overHidden: true
    },
    {
      label: "影响产品",
      prop: "db_product",
    }
  ]
})

//重置分页后查询数据
const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}

//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  const params = {
    conditions: [
      {
        ...state.searchCondition,
        operator: state.searchCondition.fuzzy ? "fuzzy" : "exact"
      }
    ],
    ...props.hostInfo,
    pageSize: state.tablePage.pageSize,
    pageNum: state.tablePage.currentPage
  }
  const {status, data} = await queryVulInfo(params);
  if (status == ResultStatus.Success) {
    state.tableData = data.list;
    state.tablePage.total = data.total;
  }
  state.tableLoading = false;
}

//打开查看详细信息窗口
const openDetailViewDialog = (v: any) => {
  addDialog({
    title: "漏洞详情",
    width: "50%",
    fullscreenIcon: true,
    hideFooter: true,
    closeOnClickModal: true,
    props: {id: v.ID},
    contentRenderer: () => h(VulDetailView, {ref: detailViewRef})
  });
}

//挂载后初始化
onMounted(() => {
  loadTableData();
});

// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

</script>
