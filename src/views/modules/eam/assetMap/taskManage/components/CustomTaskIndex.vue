<template>
  <div>
    <transition mode="out-in" name="fade-transform">
      <KeepAlive include="CustomTaskMapping">
        <component
          :is="currentComponent"
          :taskInfo="selectedTask"
          @jump-to="comChange"
          @select-task="selectTaskHandler"
        />
      </KeepAlive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {reactive, shallowRef, toRefs} from 'vue';
import CustomTaskMapping from "@/views/modules/eam/assetMap/taskManage/components/CustomTaskMapping.vue";
import CustomTaskExecuteResult from "@/views/modules/eam/assetMap/taskManage/components/CustomTaskExecuteResult.vue";

const customTaskMain = shallowRef(CustomTaskMapping);
const executeResult = shallowRef(CustomTaskExecuteResult);

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 已选择的任务
  selectedTask: null,
})
const {
  currentComponent,
  selectedTask
} = toRefs(state)

state.currentComponent = customTaskMain;

// 根据传入的值切换组件
const comChange = (val: string) => {
  let nextComponent: any;
  switch (val) {
    case "customTaskMain":
      nextComponent = customTaskMain;
      break;
    case "executeResult":
      nextComponent = executeResult;
      break;
  }
  state.currentComponent = nextComponent;
};

const selectTaskHandler = (task: any) => {
  state.selectedTask = task;
}

</script>
