<template>
  <div class="p-3" v-loading="importLoading">
    <el-divider content-position="left">
      <sign-title title="任务信息"/>
    </el-divider>
    <div class="flex-sc p-2 gap-1 pt-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20 text-right pr-2">任务名称：</el-text>
      <el-input v-model="form.policyName" placeholder="请输入任务名称" clearable maxlength="64" show-word-limit/>
    </div>
    <div class="text-red-600" style="padding-left:6rem;font-size:12px;" v-if="policyNameValidata">请输入任务名称</div>
    <div class="flex w-full">
      <div class="flex-sc gap-1 p-2 w-1/2">
        <span class="text-red-600">*</span>
        <el-text class="w-20 mr-1 text-right">任务类型：</el-text>
        <el-select v-model="form.policyType" placeholder="请选择任务类型" filterable
                   :disabled="taskInfo.cronId">
          <el-option
            v-for="item in assetTaskTypeData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="text-red-600" style="padding-left:6rem;font-size:12px;" v-if="policyTypeValidata">请选择任务类型</div>
      <div class="flex-sc gap-1 p-2 w-1/2">
        <el-text class="w-24 mr-1 text-right">IP类型：</el-text>
        <el-radio-group v-model="form.ipType">
          <el-radio-button label="IPV4" value="ipv4"/>
          <el-radio-button label="IPV6" value="ipv6"/>
        </el-radio-group>
      </div>
    </div>
    <div class="flex w-full" v-if="form.policyType!='1'">
      <div class="flex-sc gap-1 p-2 w-1/2">
        <el-text class="w-20 text-right mr-1">探测速度：</el-text>
        <el-radio-group v-model="form.collectSpeed">
          <el-radio-button label="快速" value="1"/>
          <el-radio-button label="普通" value="2"/>
        </el-radio-group>
      </div>
      <div class="flex-sc gap-1 p-2 w-1/2">
        <el-text class="w-24 mr-1 text-right">端口扫描方式：</el-text>
        <el-radio-group v-model="form.portCollectType">
          <el-radio-button label="TCP" value="tcp"/>
          <el-radio-button label="UDP" value="upd"/>
        </el-radio-group>
      </div>
    </div>
    <div class="flex-sc gap-1 p-2">
      <el-text class="w-20 text-right mr-1">扫描途径：</el-text>
      <el-checkbox style="margin-right:5px;"  v-if="form.policyType=='0'" v-model="form.isSwitch" label="交换机扫描" value="1" false-label="0"
                    true-label="1"/>
      <el-button type="primary"  v-if="form.policyType=='0'&&form.isSwitch=='1'" @click="selectSwitch">选择交换机</el-button>
      <el-checkbox v-model="form.isNmapScan" label="NMAP扫描" false-label="0"
                   true-label="1"/>
    </div>
    <div v-if="form.policyType=='0'&&form.isSwitch=='1'" >
      <el-text style="margin-left:6rem;color:#409EFF;">已选择交换机<span style="padding:0 3px;font-weight: bold;">{{!currSwitchList?0:currSwitchList.length}}</span>台,共有交换机<span style="padding:0 3px;font-weight: bold;">{{switchTableData.length}}</span>台</el-text>
    </div>
    <div class="flex-sc gap-1 p-2 mb-5">
      <el-text class="w-20 text-right mr-1">执行周期：</el-text>
      <simple-schedule ref="simpleScheduleRef"
                       v-model:exp="form.schedulingCycle"
                       :enable-minute="true" :minMinute="5"
                       :enable-hour="true" action-type="H"/>
    </div>
    <el-divider content-position="left">
      <sign-title title="执行范围"/>
    </el-divider>
    <div class="flex-bc w-full pl-2 pr-2 mt-5 mb-2">
      <el-radio-group v-model="form.operationType">
        <el-radio label="自定义" value="1"/>
        <el-radio label="资产类型" value="3"/>
        <el-radio label="IP资源池" value="2"/>
      </el-radio-group>
      <div class="flex-sc gap-1" v-if="form.policyType!='1'">
        <el-text class="w-18 pr-2">端口范围：</el-text>
        <el-radio-group v-model="form.portCollectRange">
          <el-radio value="default">常用端口（1000个）</el-radio>
          <el-radio value="1-65535">全量端口（1-65535）</el-radio>
        </el-radio-group>
      </div>
    </div>
    <ip-express-input ref="ipExpInputRef" v-model:list="customIpData" v-if="form.operationType=='1'"/>
    <asset-type-selector ref="assetTypeRef" v-model:checked-keys="checkedAssetTypes" v-if="form.operationType=='3'"/>
    <ip-pool-selector ref="ipPoolRef" v-model:checked-keys="checkedIpPools" v-if="form.operationType=='2'"/>
  </div>
  <el-dialog title="选择交换机设备" v-model="switchDialogShow" width="1200" :close-on-click-modal="false" :show-close="false">
    <template #header="{ close, titleId, titleClass }">
      <div class="my-header">
        <el-row>
          <el-col :span="12" style="text-align:left;">
            <h4 :id="titleId" :class="titleClass">选择交换机设备</h4>
          </el-col>
          <el-col :span="12" style="text-align:right">
            <el-button type="primary" @click="saveSwitch">
              保存
            </el-button>
            <el-button type="primary" @click="closeSwitch">
              关闭
            </el-button>
          </el-col>
        </el-row>
      </div>
    </template>
    <avue-crud
      ref="switchTableRef"
      :data="switchTableData"
      :option="switchTableOption"
      :table-loading="tableLoading"
      @selection-change="switchSelectChange"
    >
      <template #menu-left>
         <el-button type="primary" @click="addSwitch"
                     >新增</el-button>
        <el-upload style="display:inline-block;padding:0 10px;vertical-align: -1px;"
          class="upload-demo"
          action="/rest/eam-collection/operation/probe3/conf/importSwitchData"
                   :on-success="uploadSwitchSuccess"
                   :on-error="uploadSwitchError"
                   :show-file-list="false"
                   :before-upload="beforeUploadSwtich"
        >
          <el-button type="primary">导入</el-button>
        </el-upload>
        <el-button type="primary" @click="exportSwitchExcelTemp"
        >导出模板</el-button>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left pr-3">
          <el-input v-model="selectInput">
            <template #append>
              <el-button type="primary" @click="querySwitchTableData">查询</el-button>
              <el-button style="margin-left:10px;" type="primary" @click="resetSwitchTableData">重置</el-button>
            </template>
          </el-input>
        </div>
      </template>
      <template #menu="{ row, size, type }">
        <el-button type="text" v-if="!row.editType||row.editType!='edit'" style="margin:0;padding:0;" @click="editRow(row)">编辑</el-button>
        <el-button type="text" v-else style="margin:0;padding:0;" @click="editRow(row)">保存</el-button>
        <el-button type="text" style="margin:0;padding:0;margin-left:5px;" @click="deleteRow(row)">删除</el-button>
      </template>
      <template #ipAddress="{ row }">
        <el-input type="text" v-model="row.ipAddress" :disabled="!(row.editType&&row.editType=='edit'&&row.editIp)"></el-input>
      </template>
      <template #connectType="{ row }">
        <el-select
          :disabled="!(row.editType&&row.editType=='edit')"
          size="small"
          v-model="row.connectType"
        >
          <el-option label="telnet" value="telnet"></el-option>
          <el-option label="ssh" value="ssh"></el-option>
        </el-select>
      </template>
      <template #connectPort="{ row }">
        <el-input-number v-model="row.connectPort" :disabled="!(row.editType&&row.editType=='edit')"></el-input-number>
      </template>
      <template #userName="{ row }">
        <el-input type="text" v-model="row.userName" :disabled="!(row.editType&&row.editType=='edit')"></el-input>
      </template>
      <template #password="{ row }">
        <el-input type="password" v-model="row.password" :disabled="!(row.editType&&row.editType=='edit')"></el-input>
      </template>
      <template #switchType="{ row }">
        <el-select
          :disabled="!(row.editType&&row.editType=='edit')"
          size="small"
          v-model="row.switchType"
        >
          <el-option label="核心" value="1"></el-option>
          <el-option label="接入" value="2"></el-option>
          <el-option label="汇聚" value="3"></el-option>
        </el-select>
      </template>
      <template #manufacturer="{ row }">
        <el-select
          :disabled="!(row.editType&&row.editType=='edit')"
          size="small"
          v-model="row.manufacturer"
        >
          <el-option v-for="(item,index) in productList" :label="item.productName" :value="item.productCode" :key="index"></el-option>
        </el-select>
      </template>
    </avue-crud>
  </el-dialog>
  <el-dialog v-model="importResultShow" title="导入结果" width="600">
    <el-result
      :icon="importIcon"
      :title="importResult"
      :sub-title="importMsg"
    >
      <template #extra>
        <div v-if="importErrors&&importErrors.length>0" style="text-align:left;max-height:200px;overflow:auto;">
          <div style="font-weight: bold;">失败信息：</div>
          <div v-for="item in importErrors" style="padding-left:10px;color:gray">{{item}}</div>
        </div>
      </template>
    </el-result>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, toRefs, ref, onMounted, nextTick} from 'vue';
import SignTitle from "@/components/SignTitle/SignTitle.vue";
import {assetTaskTypeData} from "@/views/modules/eam/assetMap/taskManage/util/task_data";
import IpExpressInput from "@/views/modules/eam/assetMap/components/IpExpressInput.vue";
import AssetTypeSelector from "@/views/modules/eam/assetMap/components/AssetTypeSelector.vue";
import IpPoolSelector from "@/views/modules/eam/assetMap/components/IpPoolSelector.vue";
import SimpleSchedule from "@/components/SimpleSchedule/SimpleSchedule.vue";
import {ElMessage,ElMessageBox} from "element-plus";
import {v4 as uuidv4 } from "uuid";
import {
  deleteSwitchTableInfo, exportSwitchTemp,
  insertOrUpdateAllCron, insertSwitchTableInfo,
  queryCollPolicyExists,
  queryCronByCronId, queryProductInfo, querySwitchTableInfo
} from "@/views/modules/eam/assetMap/taskManage/api/assetMapping";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import SearchWithTypeColumn from "@/components/Search/SearchWithTypeColumn.vue";
import {sm4} from "sm-crypto";

// const sm4 = require("sm-crypto").sm4;
const sm4Key = "1B6E03BAE001B71D1AC6377806E2FF63";
//组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});
const importLoading = ref(false);
const importResultShow = ref(false);
const switchTableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  columnBtn:false,
  gridBtn:false,
  refreshBtn:false,
  menu: true,
  menuWidth: "100px",
  height:"500px",
  selection: true,
  selectionFixed: true,
  reserveSelection: true,
  rowKey: "id",
  column: [
    {
      label: "IP地址",
      prop: "ipAddress"
    },
    {
      label: "链接协议",
      prop: "connectType"
    },
    {
      label: "端口",
      prop: "connectPort"
    },
    {
      label: "账号",
      prop: "userName"
    },
    {
      label: "口令",
      prop: "password",
    },
    {
      label: "交换机类型",
      prop: "switchType",
    },
    {
      label: "厂商",
      prop: "manufacturer"
    }
  ]
})

//声明事件
const emit = defineEmits(["success"]);

//数据对象
const state = reactive({
  form: {
    cronId: null,
    policyName:'',
    policyType:'1',
    ipType: 'ipv4',
    collectSpeed: '1',
    portCollectType: 'tcp',
    isSwitch: '0',
    isNmapScan: "1",
    schedulingCycle:"1H",
    operationType:"1",
    portCollectRange:"default",
  } as any,
  switchTableData:[],
  customIpData: [] as Array<string>,
  checkedAssetTypes: [],
  checkedIpPools: [],
  policyNameValidata:false,
  policyTypeValidata:false,
  currSwitchList: [],
  tableLoading:false,
  selectInput:'',
  productList:[],
  selectIps:[],
  selectSwitchData:[],
  switchAllData:[],
  importIcon:'success',
  importResult:'成功',
  importMsg:'',
  importErrors:[]
})
const {
  form,
  customIpData,
  checkedAssetTypes,
  checkedIpPools,
  policyNameValidata,
  policyTypeValidata,
  switchTableData,
  tableLoading,
  selectInput,
  productList,
  currSwitchList,
  importIcon,
  importResult,
  importMsg,
  importErrors
} = toRefs(state)
const switchDialogShow = ref(false);
const simpleScheduleRef = ref(SimpleSchedule);
const ipExpInputRef = ref(IpExpressInput);
const assetTypeRef = ref(AssetTypeSelector);
const ipPoolRef = ref(IpPoolSelector);

//提交表单数据
const submitData = async () => {
  simpleScheduleRef.value.generateExp();
  let flag = false;
  if(!state.form.policyName){
    flag = true;
    state.policyNameValidata = true;
  }
  if(!state.form.policyType){
    flag = true;
    state.policyTypeValidata = true;
  }
  if(state.form.operationType=='1'){
    if(!ipExpInputRef.value.validateIpText()){
      flag = true;
    }
  }else if(state.form.operationType=='3'){
    if(!assetTypeRef.value.validateChecks()){
      flag = true;
    }
  }else{
    if(!ipPoolRef.value.validateChecks()){
      flag = true;
    }
  }
  if(flag){
    ElMessage.error("表单校验不通过");
    return;
  }
  let operationAssetIpDataVos = [];
  if (state.form.operationType == 1) {
    let ipList = state.customIpData;
    for (let i in ipList) {
      let json = {
        ipAddress: ipList[i],
      };
      operationAssetIpDataVos.push(json);
    }
  } else if (state.form.operationType == 2) {
    let data = state.checkedIpPools;
    for (let i in data) {
      let json = {
        ipAddress: data[i],
      };
      operationAssetIpDataVos.push(json);
    }
  } else if (state.form.operationType == 3) {
    let data = state.checkedAssetTypes;
    for (let i in data) {
      let json = {
        ipAddress: data[i],
      };
      operationAssetIpDataVos.push(json);
    }
  }
  if(state.form.policyType==0&&state.form.isSwitch=='1'){
    if(state.currSwitchList&&state.currSwitchList.length>0){
      operationAssetIpDataVos.push(...state.currSwitchList);
    }
  }else{
    state.form.isSwitch = '0';
  }
  let params = {
    cronId: props.taskInfo?.cronId,
    collType: state.form.policyType=='0'?'1':'2',
    policyType: state.form.policyType,
    policyName: state.form.policyName,
    collectionType: state.form.collectionType,
    operationType: state.form.operationType,
    portCollectType: state.form.portCollectType,
    collectSpeed: state.form.collectSpeed,
    portCollectRange: state.form.portCollectRange,
    isNmapScan: state.form.isNmapScan,
    isSwitch: state.form.isSwitch,
    schedulingCycle: state.form.schedulingCycle,
    ipType: state.form.ipType,
    operationAssetCronDataVo: operationAssetIpDataVos,
    operationAssetIpDataVos: operationAssetIpDataVos
  };
  queryCollPolicyExists({policyName: state.form.policyName, cronId: props.taskInfo?.cronId}).then(res=>{
    if (res.data == "success") {
      insertOrUpdateAllCron(params).then(()=>{
        ElMessage.success("保存成功");
        emit("success");
      })
    } else {
      ElMessage.error("任务名称已重复，请修改任务名称！");
    }
  })
}

onMounted(()=>{
  if(props.taskInfo&&props.taskInfo.cronId){
    queryCronByCronId(props.taskInfo.cronId).then(res=>{
      state.form =  {
          cronId: res.data.cronId,
          policyName:res.data.policyName,
          policyType:res.data.policyType+"",
          ipType: res.data.ipType,
          collectSpeed: res.data.collectSpeed,
          portCollectType: res.data.portCollectType,
          isSwitch: res.data.isSwitch+"",
          isNmapScan: res.data.isNmapScan+"",
          schedulingCycle:res.data.schedulingCycle,
          operationType:res.data.operationType+"",
          portCollectRange:res.data.portCollectRange
      }
      if(res.data.policyType!='0'){
        state.form.isSwitch = '0';
      }else{
        if(state.form.isSwitch=='1'){
          state.currSwitchList=res.data.operationAssetSwitchDataVos;
        }
      }
      if(res.data.operationType=="1"){
        let ips = [];
        if(res.data.operationAssetIpDataVos&&res.data.operationAssetIpDataVos.length>0){
          for(let m in res.data.operationAssetIpDataVos){
            ips.push(res.data.operationAssetIpDataVos[m].ipAddress);
          }
        }
        state.customIpData = ips;
        ipExpInputRef.value.initText(state.customIpData);
      }else if(res.data.operationType=='2'){
        let ips = [];
        if(res.data.operationAssetIpDataVos&&res.data.operationAssetIpDataVos.length>0){
          for(let m in res.data.operationAssetIpDataVos){
            ips.push(res.data.operationAssetIpDataVos[m].ipAddress);
          }
        }
        state.checkedIpPools = ips;
      }else{
        let ips = [];
        if(res.data.operationAssetIpDataVos&&res.data.operationAssetIpDataVos.length>0){
          for(let m in res.data.operationAssetIpDataVos){
            ips.push(res.data.operationAssetIpDataVos[m].ipAddress);
          }
        }
        state.checkedAssetTypes = ips;
      }
      simpleScheduleRef.value.initExp(state.form.schedulingCycle);
    })
  }else{
    simpleScheduleRef.value.initExp(state.form.schedulingCycle);
  }
  querySwitchTableCount();
})
const querySwitchTableData = () =>{
  state.tableLoading = true;
  state.switchTableData = state.switchAllData.filter(row=>{
    return row.ipAddress.indexOf(state.selectInput)>=0;
  })
  // state.switchTableData = state.switchTableData.filter(row=>{
  //   return row.ipAddress.indexOf(state.selectInput)>=0;
  // })
  // querySwitchTableInfo(state.selectInput).then(res=> {
  //   state.switchTableData = res.data.list;
  //   if (state.switchTableData && state.switchTableData.length > 0) {
  //     for (let i in state.switchTableData) {
  //       state.switchTableData[i].id = uuidv4();
  //     }
  //   }
    state.tableLoading = false;
  // });
}

const querySwitchTableDataByUrl = () =>{
  state.selectInput='';
  state.tableLoading = true;
  querySwitchTableInfo('').then(res=> {
    if(res.data&&res.data.list){
      state.switchTableData = res.data.list;
      state.switchAllData = JSON.parse(JSON.stringify(res.data.list));
      if(state.switchTableData&&state.switchTableData.length>0){
        for(let i in state.switchTableData){
          state.switchTableData[i].id = uuidv4();
          state.switchAllData[i].id = state.switchTableData[i].id;
        }
      }
      if(state.switchTableData!=null&&state.switchTableData.length>0&&state.currSwitchList&&state.currSwitchList.length>0){
        nextTick(()=>{
          for(let i in state.switchTableData){
            for(let k in state.currSwitchList){
              if(state.switchTableData[i].ipAddress==state.currSwitchList[k].ipAddress){
                switchTableRef.value.toggleRowSelection(state.switchTableData[i],true);
              }
            }
          }
        })
      }
    }
    state.tableLoading = false;
  });
}
const switchTableRef = ref(null);

const selectSwitch = () =>{
  queryProductInfo({}).then(res=>{
    state.productList = res["data"].list;
  })
  switchDialogShow.value = true;
  state.tableLoading = true;
  querySwitchTableInfo("").then(res=>{
    if(res.data&&res.data.list){
      state.switchTableData = res.data.list;
      state.switchAllData = JSON.parse(JSON.stringify(res.data.list));
      if(state.switchTableData&&state.switchTableData.length>0){
        for(let i in state.switchTableData){
          state.switchTableData[i].id = uuidv4();
          state.switchAllData[i].id = state.switchTableData[i].id;
        }
      }
      if(state.switchTableData!=null&&state.switchTableData.length>0&&state.currSwitchList&&state.currSwitchList.length>0){
        nextTick(()=>{
          for(let i in state.switchTableData){
            for(let k in state.currSwitchList){
              if(state.switchTableData[i].ipAddress==state.currSwitchList[k].ipAddress){
                switchTableRef.value.toggleRowSelection(state.switchTableData[i],true);
              }
            }
          }
        })
      }
    }
    state.tableLoading = false;
  })
}
const querySwitchTableCount = () =>{
  querySwitchTableInfo("").then(res=>{
    state.switchTableData = res.data.list;
    switchDialogShow.value = false;
  });
}
const saveSwitch = () =>{
  state.currSwitchList = state.selectSwitchData;
  switchTableRef.value.clearSelection();
  state.selectInput = '';
  querySwitchTableCount();
}
const addSwitch = () =>{
  let id = uuidv4();
  let row = {
    id: id,
    ipAddress: "",
    connectType:'telnet',
    connectPort:23,
    userName:'',
    password:'',
    switchType:null,
    manufacturer:null,
    editType: 'edit',
    editIp:true
  }
  state.switchTableData.unshift(row);
  state.switchAllData.unshift(row);
}
const closeSwitch = () =>{
  state.selectInput = '';
  switchTableRef.value.clearSelection();
  switchDialogShow.value = false;
}
const ipReg = (ip) =>{
  var re =  /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return re.test(ip);
}
const editRow = (row) =>{
  if(row.editType&&row.editType=="edit"){
    if(!row.ipAddress){
      ElMessage.error("IP地址不能为空");
      return;
    }
    if(!row.connectType){
      ElMessage.error("链接协议不能为空");
      return;
    }
    if(!row.connectPort){
      ElMessage.error("端口不能为空");
      return;
    }
    if(!row.userName){
      ElMessage.error("账号不能为空");
      return;
    }
    if(!row.switchType){
      ElMessage.error("交换机类型不能为空");
      return;
    }
    if(!row.manufacturer){
      ElMessage.error("厂商不能为空");
      return;
    }
    if(!ipReg(row.ipAddress)){
      ElMessage.error("IP地址格式不正确，请修改IP地址");
      return;
    }
    let flag = false;
    for(let i in state.switchTableData){
      if(row!=state.switchTableData[i]&&row.ipAddress == state.switchTableData[i].ipAddress){
        flag = true;
        break;
      }
    }
    if(flag){
      ElMessage.error("当前IP地址已存在，请修改");
      return;
    }
    row.editIp = false;
    row.password = sm4.encrypt(row.password, sm4Key);
    let saveParams = {
      ipAddress:row.ipAddress,
      connectType:row.connectType,
      connectPort:row.connectPort,
      userName:row.userName,
      password:row.password,
      switchType:row.switchType,
      manufacturer:row.manufacturer
    }
    insertSwitchTableInfo(saveParams).then(res=>{
      if(res.data.result=='success'){
        ElMessage.success("保存成功");
        row.editType = null;
      }else{
        ElMessage.error("保存失败");
      }
    })
  }else{
    row.password = sm4.decrypt(row.password, sm4Key);
    row.editType = 'edit';
  }
  for(let i in state.switchAllData){
    if(state.switchAllData[i].id == row.id){
      state.switchAllData[i] = row;
    }
  }
}
const deleteRow = (row) =>{
  ElMessageBox.confirm('确认要删除吗？','提示',{
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  }).then(()=>{
    let deleteId = -1;
    for(let i in state.switchTableData){
      if(row.id==state.switchTableData[i].id){
        deleteId = i;
      }
    }
    state.switchTableData.splice(deleteId,1);
    state.switchAllData.splice(deleteId,1);
    if(row.ipAddress){
      let ips = [];
      ips.push(row.ipAddress);
      deleteSwitchTableInfo({
        "ips":ips
      }).then(res1=>{
        if(res1.data.result=='success'){
          ElMessage.success("删除成功");
        }
      })
    }
  }).catch(()=>{

  })
}

const switchSelectChange = (value) =>{
  state.selectIps = [];
  state.selectSwitchData = [];
  if(value&&value.length>0){
    for(let i in value){
      if(value[i].ipAddress){
        state.selectIps.push(value[i].ipAddress);
        let s = {
          ipAddress:value[i].ipAddress,
          connectType:value[i].connectType,
          connectPort:value[i].connectPort,
          userName:value[i].userName,
          password:value[i].password,
          switchType:value[i].switchType,
          manufacturer:value[i].manufacturer
        }
        state.selectSwitchData.push(s);
      }
    }
  }
}
const resetSwitchTableData = () =>{
  state.selectInput = '';
  querySwitchTableData();
}
const exportSwitchExcelTemp = () =>{
  exportSwitchTemp().then(()=>{

  })
}
const uploadSwitchSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) =>{
    importResultShow.value = true;
    if(response.data.result=='success'){
      state.importIcon = "success";
      state.importResult = '导入成功';
      state.importErrors = response.data.errorList;
    }else{
      state.importIcon = "error";
      state.importResult = '导入失败';
    }
    state.importMsg = response.data.msg;
    importLoading.value = false;
    querySwitchTableDataByUrl();
}
const uploadSwitchError = (error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) =>{
  console.log(error);
  ElMessage.error("文件上传失败");
  importLoading.value = false;
}
const beforeUploadSwtich = (rawFile: UploadRawFile) =>{
  console.log(rawFile.name);
  if(rawFile.name.indexOf(".")>=0){
    if(rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xlsx'||rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xls'){
      importLoading.value = true;
      return true;
    }else{
      ElMessage.error("只支持识别Excel文件");
      return false;
    }
  }else{
    ElMessage.error("只支持识别Excel文件");
    return false;
  }
}
//对外暴露可调用方法
defineExpose({submitData});

</script>
