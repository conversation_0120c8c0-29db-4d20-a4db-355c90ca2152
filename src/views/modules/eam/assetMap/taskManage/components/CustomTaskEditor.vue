<template>
  <div class="p-3">
    <el-divider content-position="left">
      <sign-title title="任务信息"/>
    </el-divider>
    <div class="flex-sc p-3 gap-1 pt-5">
      <span class="text-red-600">*</span>
      <el-text class="w-16">任务名称：</el-text>
      <el-input v-model="form.jobName" placeholder="请输入任务名称" clearable maxlength="64" show-word-limit/>
    </div>
    <div class="flex-sc p-3 gap-1">
      <span class="text-red-600">*</span>
      <el-text class="w-16">采集指标：</el-text>
      <el-select v-model="form.glueIds" placeholder="请选择采集指标" multiple clearable filterable>
        <el-option
          v-for="item in kpiList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="flex-sc p-3 gap-1 pb-5">
      <span class="text-red-600">*</span>
      <el-text class="w-16">执行规则：</el-text>
      <el-radio-group v-model="form.scheduleType" class="mr-5">
        <el-radio-button label="手动执行" value="NONE"/>
        <el-radio-button label="定期执行" value="CRON"/>
      </el-radio-group>
      <cron-element-plus v-model="form.scheduleConf"
                         v-if="form.scheduleType=== 'CRON'"
                         :button-props="{ type: 'primary',plain:true }"
                         locale="zh-cn"
                         format="spring"/>
      <el-text type="primary" class="ml-5" v-if="form.scheduleType=== 'CRON'">
        {{
          `表达式:  ${form.scheduleConf}`
        }}
      </el-text>
    </div>
    <el-divider content-position="left">
      <sign-title title="执行范围"/>
    </el-divider>
    <div class="mt-5 p-3">
      <avue-crud :data="hostData"
                 :option="tableOption"
                 @row-del="hostDeleteHandler">
        <template #menu-left>
          <el-button type="primary" plain :icon="useRenderIcon('EP-Plus')" @click="openHostSelector">
            添加主机
          </el-button>
        </template>
        <template #agentState="{row}">
          <el-tag type="primary" effect="plain">{{ getAgentStatusLabel(row.agentState) }}</el-tag>
        </template>
      </avue-crud>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {getCurrentInstance, h, onMounted, reactive, ref, toRefs} from 'vue';
import SignTitle from "@/components/SignTitle/SignTitle.vue";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import AgentHostSelector from "@/views/modules/eam/assetMap/taskManage/components/AgentHostSelector.vue";
import {addDialog} from "@/components/ReDialog/index";
import {getAgentStatusLabel} from "@/views/modules/eam/assetMap/taskManage/util/task_data";
import {
  createHostTask,
  getTaskExtraInfo,
  HostTaskFormData,
  updateHostTask
} from "@/views/modules/eam/assetMap/taskManage/api/customTaskApi";
import {validateTaskForm} from "@/views/modules/eam/assetMap/taskManage/util/validator";

const {$message, $confirm} = getCurrentInstance().appContext.config.globalProperties;

//添加主机
const hostSelectorRef = ref<InstanceType<typeof AgentHostSelector>>();

//组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  },
  kpiList: {
    type: Array<any>,
    default: () => []
  }
});

//声明事件
const emit = defineEmits(["success"]);

//数据对象
const state = reactive({
  form: {} as HostTaskFormData,
  hostData: []
})

const {
  form,
  hostData
} = toRefs(state)

//表格选项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  stripe: true,
  index: true,
  addBtn: false,
  editBtn: false,
  delBtn: true,
  refreshBtn: false,
  gridBtn: false,
  columnBtn: false,
  menuWidth: 200,
  height: 260,
  rowKey: "ipAddress",
  column: [
    {
      label: "IP地址",
      prop: "ipAddress",
    },
    {
      label: "资产名称",
      prop: "name",
    },
    {
      label: "所属业务系统",
      prop: "opSystemLabel",
    },
    {
      label: "Agent状态",
      prop: "agentState",
      width: 120
    },
    {
      label: "授权用户",
      prop: "agentUser",
    }
  ]
});

//打开添加主机窗口
const openHostSelector = () => {
  addDialog({
    title: "选择主机",
    width: 860,
    fullscreenIcon: false,
    closeOnClickModal: false,
    contentRenderer: () => h(AgentHostSelector, {ref: hostSelectorRef}),
    beforeSure: (done: Function) => {
      const selectedHosts = hostSelectorRef.value?.getSelectedHosts();
      if (selectedHosts && selectedHosts.length > 0) {
        addHostToState(selectedHosts);
        done();
      } else {
        $message({
          type: "error",
          message: "请选择主机后，再点击确认按钮！"
        });
      }
    }
  });
}

//将主机信息添加到state中
const addHostToState = (hosts: Array<any>) => {
  if (state.hostData && state.hostData.length > 0) {
    hosts.forEach((item) => {
      const exist = state.hostData.find((host) => host.ipAddress === item.ipAddress);
      if (!exist) {
        state.hostData.push(item);
      }
    })
  } else {
    state.hostData = hosts;
  }
}

//移除主机触发
const hostDeleteHandler = (form, index, done) => {
  $confirm(`您确要移除 ${form.ipAddress} 么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: 'warning'
  }).then(() => {
    done(form);
  });
}

//提交表单数据
const submitData = async () => {
  collectHostAddressList();
  const validateMsg = validateTaskForm(state.form);
  if (validateMsg) {
    $message({
      type: "error",
      message: validateMsg
    })
  } else {
    //通过校验进行提交操作
    if (state.form.id) {
      //更新
      const res = await updateHostTask(state.form);
      if (res.status === "0") {
        $message({
          type: "success",
          message: "已成功更新任务！"
        });
        emit("success");
      }
    } else {
      //新增
      const res = await createHostTask(state.form);
      if (res.status === "0") {
        $message({
          type: "success",
          message: "已成功创建任务！"
        });
        emit("success");
      }
    }
  }
}

//收集已选择的主机地址列表
const collectHostAddressList = () => {
  const addressList = [];
  state.hostData.forEach((item) => addressList.push(item.ipAddress));
  state.form.addressList = addressList;
}

//获取待编辑的额外信息
const getExtraInfoForEdit = async () => {
  const res = await getTaskExtraInfo(state.form.id);
  if (res.status === "0") {
    state.hostData = res.data.addressList;
    const glues = res.data.glues;
    if (glues && glues.length > 0) {
      const glueIds = [];
      glues.forEach((item: any) => glueIds.push(item.id));
      state.form.glueIds = glueIds;
    }
  }
}

onMounted(() => {
  state.form = props.taskInfo as HostTaskFormData;
  if (state.form.id && state.form.id > 0) {
    getExtraInfoForEdit();
  }
})

//对外暴露可调用方法
defineExpose({submitData});

</script>

