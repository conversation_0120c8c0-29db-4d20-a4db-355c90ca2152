<template>
  <avue-crud
    :data="tableData"
    :option="tableOption"
    v-model:page="tablePage"
    :table-loading="tableLoading"
    @refresh-change="resetTablePageAndQuery"
    @size-change="loadTaskData"
    @current-change="loadTaskData"
    @selection-change="selectionChangeHandler"
  >
    <!-- 表格左侧菜单 -->
    <template #menu-left>
      <el-button type="primary" :icon="useRenderIcon('EP-Plus')" @click="openEditTaskDialog({})">
        新建任务
      </el-button>
    </template>
    <!-- 表格右侧菜单 -->
    <template #menu-right="{ size }">
      <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
        <el-input
          clearable
          placeholder="任务名称"
          v-model="searchCondition.name"
          :size="size"
          @blur="searchCondition.name = $event.target.value.trim()"
        >
          <template #append>
            <el-button
              :icon="useRenderIcon('EP-Search')"
              @click="resetTablePageAndQuery"
            />
          </template>
        </el-input>
        <el-select
          v-model="searchCondition.taskType"
          placeholder="任务类型"
          clearable
          multiple
          collapse-tags
          filterable
          style="width: 280px"
          class="ml-3"
          @change="resetTablePageAndQuery"
        >
          <el-option
            v-for="item in assetTaskTypeData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-tooltip
          content="批量删除任务"
          placement="top"
          :open-delay="1000"
        >
          <el-button
            :icon="useRenderIcon('EP-DeleteFilled')"
            circle
            :disabled="selectedTasks?.length==0"
            :size="size"
            @click="batchDeleteTaskHandler"
          />
        </el-tooltip>
      </div>
    </template>
    <!-- 表格操作按钮 -->
    <template #menu="{ row, size, type }">
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-Compass')"
        text
        :disabled="row.executeStatus == 1"
        @click="executeCron(row)"
      >
        重新执行
      </el-button>
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-View')"
        text
        :disabled="row.executeStatus == 1"
        @click="viewResultHandler(row)"
      >
        查看结果
      </el-button>
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-Edit')"
        text
        :disabled="row.executeStatus == 1"
        @click="openEditTaskDialog(row)"
      >
        编辑
      </el-button>
      <el-button
        :size="size"
        type="danger"
        :icon="useRenderIcon('EP-Delete')"
        text
        :disabled="row.executeStatus == 1"
        @click="deleteTaskHandler(row)"
      >
        删除
      </el-button>
    </template>
    <template #policyType="{row}">
      <el-tag type="primary">{{ row.policyType }}</el-tag>
    </template>
    <template #isState="{row}">
      <el-switch
        v-model="row.isState"
        :active-value=1
        :inactive-value=0
        active-text="启用"
        inactive-text="禁用"
        inline-prompt
        class="enable-switch-color"
        :disabled="row.executeStatus =='执行中'"
        @change="changeOnOrDown(row)"
      />

    </template>
    <template #runProcess="{row}">
      <el-tag v-if="row.executeStatus=='执行成功'" type="success">执行成功</el-tag>
      <el-tag v-else-if="row.executeStatus=='执行失败'" type="danger">执行失败</el-tag>
      <el-tag v-else-if="row.executeStatus=='未执行'">未执行</el-tag>
      <el-progress v-else :text-inside="true" :stroke-width="15" :percentage="Math.round(row.runProcess*100)/100" status="success"></el-progress>
    </template>
  </avue-crud>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, h, onMounted, reactive, ref, toRefs} from 'vue';
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import AssetTaskEditor from "@/views/modules/eam/assetMap/taskManage/components/AssetTaskEditor.vue";
import {addDialog, closeAllDialog} from "@/components/ReDialog/index";
import {
  closeProcessByCronId,
  deleteOperationAllCronIds,
  executeOperationAllTask, insertOrUpdateAllCron,
  queryCollTableData
} from "@/views/modules/eam/assetMap/taskManage/api/assetMapping";
import {
  assetTaskTypeData,
  getAssetTaskType
} from "@/views/modules/eam/assetMap/taskManage/util/task_data";
import {ElMessage} from "element-plus";

const {$message, $confirm} = getCurrentInstance().appContext.config.globalProperties;

//编辑任务 Ref
const editTaskRef = ref<InstanceType<typeof AssetTaskEditor>>();

// 定义事件
const emit = defineEmits(["jump-to", "select-task"]);

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    name: "",
    taskType: null
  },
  tableData: [
  ],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  selectedTasks: [] as Array<string>,
  eventSources:[],
  processMap:[],
})
const {
  tableLoading,
  searchCondition,
  tableData,
  tablePage,
  selectedTasks
} = toRefs(state)

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

//表格选项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  selectable: (row: any) => row.runningStatus != 1,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 280,
  height: tableHeight,
  rowKey: "cronId",
  column: [
    {
      label: "任务名称",
      prop: "policyName",
    },
    {
      label: "任务类型",
      prop: "policyType",
    },
    {
      label: "执行周期",
      prop: "schedulingCycle",
    },
    {
      label: "进度",
      prop: "runProcess",
    },
    {
      label: "存活IP数",
      prop: "onlineCount",
    },
    {
      label: "端口数",
      prop: "portCount",
    },
    {
      label: "服务数",
      prop: "serverCount",
    },
    {
      label: "Web页面数",
      prop: "webCount",
    },
    {
      label: "最近运行时间",
      prop: "executeTime",
    },{
      label: "启用状态",
      prop: "isState"
    }
  ]
});

//重置分页后查询数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  loadTaskData();
}

const deleteProcessEvent = (cronId,threadId) =>{
  closeProcessByCronId({
    cronId: cronId,
    id: threadId
  }).then(res=>{
    if(res.data=='success'){
    }
  })
}

//加载任务数据
const loadTaskData = async () => {
    let params = {
      pageNum: state.tablePage.currentPage,
      pageSize: state.tablePage.pageSize,
      policyType: state.searchCondition.taskType?state.searchCondition.taskType.join(","):'',
      policyName: state.searchCondition.name
    }
  if(state.eventSources&&state.eventSources.length>0){
    for(let i =0;i<state.eventSources.length;i++){
      if(state.eventSources[i]){
        state.eventSources[i].close();
      }
    }
    state.eventSources = [];
  }
  if(state.processMap&&state.processMap.length>0){
    for(let i=0;i<state.processMap.length;i++){
      if(state.processMap[i]){
        deleteProcessEvent(state.processMap[i].split("_")[0],state.processMap[i].split("_")[1]);
      }
    }
    state.processMap = [];
  }
  queryCollTableData(params).then(res=>{
    state.tableData = res.data.rows;
    state.tablePage.total = res.data.total;
    let flag = false;
    let cronIds = [];
    for (let i in res.data.rows) {
      if (res.data.rows[i].executeStatus == '执行中') {
        flag = true;
        cronIds.push(res.data.rows[i].cronId);
      }
    }
    if(flag){
      let source = new EventSource(
        "/rest/eam-collection/operation/probe3/conf/queryProcessByCronIds?cronIds=" + cronIds.join(","),
      );
      state.eventSources.push(source);
      source.addEventListener("data", data => {
        let result = JSON.parse(data.data).list;
        for (let i in res.data.rows) {
          for(let j in result){
            if (res.data.rows[i].cronId == result[j].cronId) {
              state.tableData[i].runProcess=Math.round(result[j].data*100)/100;
            }
          }
        }
        if(state.processMap.indexOf(cronIds.join(",")+"_"+JSON.parse(data.data).id)<0){
          state.processMap.push(cronIds.join(",")+"_"+JSON.parse(data.data).id);
        }
      });

      source.addEventListener("close", data => {
        let result = JSON.parse(data.data).list;
        for (let i in res.data.rows) {
          for(let j in result){
            if (res.data.rows[i].cronId == result[j].cronId) {
              state.tableData[i].runProcess=Math.round(result[j].data*100)/100;
            }
          }
        }
        if(state.processMap.indexOf(cronIds.join(",")+"_"+JSON.parse(data.data).id)<0){
          state.processMap.push(cronIds.join(",")+"_"+JSON.parse(data.data).id);
        }
        source.close();
        deleteProcessEvent(cronIds.join(","),JSON.parse(data.data).id);
        loadTaskData();
      });
    }
  })
}

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.cronId));
  state.selectedTasks = selectIds;
}

// 打开编辑任务窗口
const openEditTaskDialog = (t: any) => {
  addDialog({
    title: t.policyName ? `编辑任务 - ${t.policyName}` : "新建任务",
    width: "80%",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: {taskInfo: t},
    contentRenderer: () => h(AssetTaskEditor, {
      ref: editTaskRef, onSuccess: () => {
        closeAllDialog();
        loadTaskData();
      }
    }),
    beforeSure(done) {
      editTaskRef.value.submitData();
    },
  });
}

//删除任务触发
const deleteTaskHandler = async (t: any) => {
  $confirm(
    `您确定要删除 '${t.policyName}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    let cronIds = [t.cronId];
    let params = {
      cronIds: cronIds
    };
    deleteOperationAllCronIds(params).then(res=>{
      if(res.data == 'success'){
        ElMessage.success("删除成功");
        loadTaskData();
      }else{
        ElMessage.error(res.data);
      }
    })
  });
}

// 批量删除任务触发
const batchDeleteTaskHandler = async () => {
  console.log(state.selectedTasks);
  const selectedSize = state.selectedTasks.length;
  $confirm(
    `您确定要删除已选择的 ${selectedSize} 项任务么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    let params = {
      cronIds: state.selectedTasks
    };
    deleteOperationAllCronIds(params).then(res=>{
      if(res.data == 'success'){
        ElMessage.success("删除成功");
        loadTaskData();
      }else{
        ElMessage.error(res.data);
      }
    })
    // $message({
    //   type: "success",
    //   message: `已成功删除 ${selectedSize} 项任务！`
    // });
  });
}

//查看结果触发
const viewResultHandler = (t: any) => {
  emit("select-task", t);
  jumpTo(getAssetTaskType(t.policyType));
}

// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

const executeCron = (row) =>{
  let collType=1;
  if(row.policyType=='全面扫描'){
    collType=1
  }else{
    collType=2;
  }
  executeOperationAllTask({ cronId: row.cronId,collType:collType}).then(()=>{
    ElMessage.success("执行成功");
    loadTaskData();
  })
}
const changeOnOrDown = (row) => {
  console.log(row);
  if(!row.cronId) return;
  let json = {
    cronId: row.cronId,
    isState: row.isState
  };

  if(row.policyType&&row.policyType=='全面扫描'){
    json["collType"]='1';
  }else {
    json["collType"]='2';
  }
  console.log(json);
  insertOrUpdateAllCron(json).then(() => {
    ElMessage.success("启用状态修改成功");
    loadTaskData();
  }).catch(err => {
    console.log(err);
  });
}

onMounted(()=>{
  loadTaskData();
})

</script>
