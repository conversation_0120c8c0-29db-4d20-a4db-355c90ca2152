<template>
  <div class="p-2">
    <el-descriptions
      title="Vertical list with border"
      direction="horizontal"
      :column="1"
      border
    >
      <template #title>
        <el-text class="font-bold text-lg">{{ data.vul_name }}</el-text>
      </template>
      <el-descriptions-item label="CNVD-ID">{{ data.db_vul_num_cnvd }}</el-descriptions-item>
      <el-descriptions-item label="CNNVD-ID">{{ data.db_vul_num_cnnvd }}</el-descriptions-item>
      <el-descriptions-item label="公开日期">{{ data.db_publish_time }}</el-descriptions-item>
      <el-descriptions-item label="危害级别">{{ data.vul_level }}</el-descriptions-item>
      <el-descriptions-item label="影响产品">{{ data.db_product }}</el-descriptions-item>
      <el-descriptions-item label="CVE-ID">{{ data.cve }}</el-descriptions-item>
      <el-descriptions-item label="漏洞类型">{{ data.db_vul_type }}</el-descriptions-item>
      <el-descriptions-item label="参考链接">{{ data.db_ref_link }}</el-descriptions-item>
      <el-descriptions-item label="漏洞解决方案">{{ data.vul_solution }}</el-descriptions-item>
      <el-descriptions-item label="厂商补丁">{{ data.db_manufacturer_patch }}</el-descriptions-item>
      <el-descriptions-item label="漏洞描述">{{ data.vul_desc }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import {reactive, toRefs} from 'vue';
import {queryVulDetail} from "@/views/modules/eam/assetMap/taskManage/api/vulTaskApi";

// 组件属性
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});

//数据对象
const state = reactive({
  data: {} as any,
})
const {
  data,
} = toRefs(state)

//查询详细信息
const loadData = async () => {
  const {data} = await queryVulDetail(props.id);
  state.data = data;
}
loadData();

</script>
<style scoped lang="scss">
:deep(tbody) {
  /* 允许文本换行 */
  white-space: normal;
  /* 强制长单词换行 */
  word-break: break-all;
  .el-descriptions__label{
    white-space: nowrap;
  }
}
</style>