<template>
  <avue-crud
    :data="tableData"
    :option="tableOption"
    v-model:page="tablePage"
    :table-loading="tableLoading"
    @refresh-change="resetTablePageAndQuery"
    @size-change="loadTaskData"
    @current-change="loadTaskData"
    @selection-change="selectionChangeHandler"
  >
    <!-- 表格左侧菜单 -->
    <template #menu-left>
      <el-button type="primary" :icon="useRenderIcon('EP-Plus')"
                 v-auth="'host:map:add'"
                 @click="openEditTaskDialog({ scheduleType:'CRON',scheduleConf:'0 0/5 * * * ?'})">新建任务
      </el-button>
    </template>
    <!-- 表格右侧菜单 -->
    <template #menu-right="{ size }">
      <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
        <el-input
          clearable
          placeholder="任务名称"
          v-model="searchCondition.jobName"
          :size="size"
          @blur="searchCondition.jobName = $event.target.value.trim()"
        >
          <template #append>
            <el-button
              :icon="useRenderIcon('EP-Search')"
              @click="resetTablePageAndQuery"
            />
          </template>
        </el-input>
        <el-select
          v-model="searchCondition.scheduleTypes"
          placeholder="调度类型"
          clearable
          multiple
          collapse-tags
          filterable
          style="width: 280px"
          class="ml-3"
          @change="resetTablePageAndQuery"
        >
          <el-option
            v-for="item in typeOptionData"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-tooltip
          content="批量删除任务"
          placement="top"
          :open-delay="1000"
        >
          <el-button
            :icon="useRenderIcon('EP-DeleteFilled')"
            circle
            :size="size"
            :disabled="selectedTasks.length==0"
            @click="batchDeleteTaskHandler"
          />
        </el-tooltip>
      </div>
    </template>
    <!-- 表格操作按钮 -->
    <template #menu="{ row, size, type }">
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-Open')"
        text
        v-auth="'host:map:do'"
        @click="executeNowHandler(row)"
        :disabled="row.runningStatus == 1|| row.triggerStatus == 0"
      >
        立即执行
      </el-button>

      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-View')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'host:map:result'"
        @click="viewResultHandler(row)"
      >
        查看结果
      </el-button>
      <el-button
        :size="size"
        :type="type"
        :icon="useRenderIcon('EP-Edit')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'host:map:edit'"
        @click="openEditTaskDialog(row)"
      >
        编辑
      </el-button>
      <el-button
        :size="size"
        type="danger"
        :icon="useRenderIcon('EP-Delete')"
        text
        :disabled="row.runningStatus == 1"
        v-auth="'host:map:delete'"
        @click="deleteTaskHandler(row)"
      >
        删除
      </el-button>
    </template>
    <template #runningStatus="{row}">
      <el-tag :type="row.runningStatus == 1?'success':'info'" effect="plain">
        {{ row.runningStatus == 1 ? '运行中' : '未运行' }}
      </el-tag>
    </template>
    <template #scheduleType="{row}">
      <el-tag type="primary" effect="plain">
        {{ row.scheduleType == 'CRON' ? '定期执行' : '手动执行' }}
      </el-tag>
    </template>
    <template #triggerStatus="{row}">
      <el-switch
        v-model="row.triggerStatus"
        :active-value="1"
        :inactive-value="0"
        active-text="启用"
        inactive-text="禁用"
        inline-prompt
        class="enable-switch-color"
        :disabled="row.runningStatus == 1"
        @change="enableChangeHandler(row)"
      />
    </template>
    <template #triggerLastTime="{row}">
      {{ row.triggerLastTime > 0 ? useDateFormat(row.triggerLastTime, 'YYYY-MM-DD HH:mm:ss') : '-' }}
    </template>
  </avue-crud>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, h, reactive, ref, toRefs} from 'vue';
import {useDateFormat} from '@vueuse/core'
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {addDialog, closeAllDialog} from "@/components/ReDialog/index";
import CustomTaskEditor from "@/views/modules/eam/assetMap/taskManage/components/CustomTaskEditor.vue";
import {
  deleteTask,
  disableTask,
  enableTask,
  executeTask,
  getKpiList,
  getTaskData
} from "@/views/modules/eam/assetMap/taskManage/api/customTaskApi";

const {$message, $confirm} = getCurrentInstance().appContext.config.globalProperties;

//编辑任务 Ref
const editTaskRef = ref<InstanceType<typeof CustomTaskEditor>>();

//定义事件
const emit = defineEmits(["jump-to", "select-task"]);

//数据对象
const state = reactive({
  tableLoading: false,
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  searchCondition: {
    jobName: "",
    scheduleTypes: []
  },
  typeOptionData: [
    {
      label: "定期执行",
      value: "CRON"
    },
    {
      label: "手动执行",
      value: "NONE"
    }
  ],
  selectedTasks: [] as Array<string>,
  kpiList: []
})
const {
  tableLoading,
  tableData,
  tablePage,
  searchCondition,
  typeOptionData,
  selectedTasks,
  kpiList
} = toRefs(state)

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

//表格选项
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  selectable: (row: any) => row.runningStatus != 1,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 300,
  height: tableHeight,
  rowKey: "id",
  column: [
    {
      label: "任务名称",
      prop: "jobName",
      sortable: true,
    },
    {
      label: "调度类型",
      prop: "scheduleType",
    },
    {
      label: "运行状态",
      prop: "runningStatus",
    },
    {
      label: "是否启用",
      prop: "triggerStatus",
      width: 120
    },
    {
      label: "最近运行时间",
      prop: "triggerLastTime",
    }
  ]
});

//重置分页后查询数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  loadTaskData();
}

//加载任务数据
const loadTaskData = async () => {
  state.tableLoading = true;
  state.tableData = [];
  const res = await getTaskData({
    ...state.searchCondition,
    pageIndex: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize,
  });
  if (res.status === "0") {
    state.tableData = res.data;
    state.tablePage.total = res.total;
  }
  state.tableLoading = false;
}
loadTaskData();

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.id));
  state.selectedTasks = selectIds;
}

// 查看执行结果触发
const viewResultHandler = (t: any) => {
  emit("select-task", t);
  jumpTo("executeResult");
}

// 打开编辑任务窗口
const openEditTaskDialog = (t: any) => {
  addDialog({
    title: t.jobName ? `编辑任务 - ${t.jobName}` : "新建任务",
    width: "80%",
    fullscreenIcon: false,
    closeOnClickModal: false,
    props: {taskInfo: t, kpiList: state.kpiList},
    contentRenderer: () => h(CustomTaskEditor, {
      ref: editTaskRef, onSuccess: () => {
        closeAllDialog();
        loadTaskData();
      }
    }),
    beforeSure(done) {
      editTaskRef.value.submitData();
    },
  });
}

// 删除任务触发
const deleteTaskHandler = async (t: any) => {
  $confirm(
    `您确定要删除 '${t.jobName}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const res = await deleteTask(t.id);
    if (res.status === "0") {
      $message({
        type: "success",
        message: "已成功删除任务！"
      });
      await loadTaskData();
    }
  });
}

// 批量删除任务触发
const batchDeleteTaskHandler = async () => {
  const selectedSize = state.selectedTasks.length;
  $confirm(
    `您确定要删除已选择的 ${selectedSize} 项任务么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const res = await deleteTask(state.selectedTasks.join(","));
    if (res.status === "0") {
      $message({
        type: "success",
        message: `已成功删除 ${selectedSize} 项任务！`
      });
      await loadTaskData();
    }
  });
}

// 立即执行触发
const executeNowHandler = async (t: any) => {
  $confirm(
    `您确定要执行 '${t.jobName}' 么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(async () => {
    const res = await executeTask(t.id);
    if (res.status === "0") {
      $message({
        type: "success",
        message: "已成功执行任务！"
      });
      await loadTaskData();
    }
  })
}

//任务启用、禁用变更 - 列表快捷
const enableChangeHandler = (val: any) => {
  if (val.id) {
    $confirm(
      `您确认要将'${val.jobName}'设置为 ${
        val.triggerStatus == 0 ? "禁用" : "启用"
      } 么？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      }
    )
      .then(async () => {
        if (val.triggerStatus == 1) {
          //启用任务
          const res = await enableTask(val.id);
          if (res.status === "0") {
            $message({
              type: "success",
              message: `您已成功启用'${val.jobName}'！`
            });
          }

        } else if (val.triggerStatus == 0) {
          //禁用任务
          const res = await disableTask(val.id);
          if (res.status === "0") {
            $message({
              type: "success",
              message: `您已成功禁用'${val.jobName}'！`
            });
          }
        }
      })
      .catch(() => (val.triggerStatus = val.triggerStatus == 0 ? 1 : 0));
  }
};

//加载指标列表数据
const loadKpiData = async () => {
  const res = await getKpiList();
  if (res.status === "0") {
    const list = [];
    res.data.forEach((item: any) => {
      list.push({
        label: item.glueName,
        value: item.id
      });
    });
    state.kpiList = list;
  }
}
loadKpiData();

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

</script>
