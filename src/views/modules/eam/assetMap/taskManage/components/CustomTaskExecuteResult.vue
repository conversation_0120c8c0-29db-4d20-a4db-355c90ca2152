<template>
  <div>
    <div class="flex-bc w-full">
      <el-page-header @back="jumpTo('customTaskMain')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> 任务执行结果 - {{ taskInfo.jobName }}</span>
        </template>
      </el-page-header>
    </div>
    <div class="flex justify-start gap-2">
      <div class="w-52 pt-2">
        <el-input placeholder="IP地址"
                  v-model="ipKeyWord"
                  :suffix-icon="useRenderIcon('EP-Search')"
                  class="mb-1 w-52"
                  clearable/>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto dark:bg-dark-color p-1"
        >
          <el-tree
            ref="ipTreeRef"
            :data="addressList"
            node-key="ipAddress"
            :expand-on-click-node="false"
            :props="{
                  label:'ipAddress',
                }"
            highlight-current
            :filter-node-method="filterAddressNode"
            @current-change="addressChangeHandler"
          />
        </div>
      </div>
      <div :style="{width: 'calc(100% - 213px)'}" class="pr-1">
        <el-tabs v-model="activeName" class="w-full" @tab-change="tabChangeHandler">
          <el-tab-pane v-for="item in glues" :label="item.glueName" :name="item.id"></el-tab-pane>
        </el-tabs>
        <avue-crud
          ref="tableRef"
          :data="tableData"
          :option="tableOption"
          v-model:page="tablePage"
          :table-loading="tableLoading"
          @refresh-change="resetTablePageAndQuery"
          @size-change="loadTableData"
          @current-change="loadTableData"
        >
          <template #menu-left="{ size }">
            <div class="flex-sc w-full h-full">
              <search-with-column v-model="searchCondition.fieldValue"
                                  v-model:fuzzy-enable="searchCondition.fuzzy"
                                  v-model:column-val="searchCondition.fieldName"
                                  :column-options="columnSearchOptions"
                                  :column-select-width="90"
                                  class="flex-sc w-[800px]"
                                  @search="resetTablePageAndQuery"
                                  @reset="searchCondition.fieldValue=null"
                                  input-class-name="w-1/2"/>
            </div>
          </template>
          <template #menu-right="{ size }">
            <div class="float-left pr-3">
              <el-tooltip
                content="导出采集数据"
                placement="top"
                :open-delay="1000"
              >
                <el-button
                  :icon="useRenderIcon('EP-Download')"
                  circle
                  :size="size"
                  :disabled="tableData==null||tableData.length==0"
                  @click="exportDataHandler"
                  v-auth="'host:map:export'"
                />
              </el-tooltip>
            </div>
          </template>
        </avue-crud>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch} from 'vue';
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {ElTree} from "element-plus";
import {
  exportTaskResultData,
  getTaskExtraInfo,
  getTaskResultColumns,
  getTaskResultData
} from "@/views/modules/eam/assetMap/taskManage/api/customTaskApi";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";

const {$message} = getCurrentInstance().appContext.config.globalProperties;

const ipTreeRef = ref<InstanceType<typeof ElTree>>();

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return window.innerHeight - 360;
});

const tableRef = ref(null);
const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "keyId",
  column: []
})

// 组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});

// 定义事件
const emit = defineEmits(["jump-to"]);

// 数据对象
const state = reactive({
  ipKeyWord: null,
  activeName: null,
  addressList: [],
  glues: [],
  columnSearchOptions: [],
  searchCondition: {
    fieldName: null,
    fieldValue: null,
    gatherIp: null,
    fuzzy: true
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  tableLoading: false
})
const {
  ipKeyWord,
  activeName,
  addressList,
  glues,
  columnSearchOptions,
  searchCondition,
  tableData,
  tablePage,
  tableLoading
} = toRefs(state)

watch(ipKeyWord, val => {
  ipTreeRef.value!.filter(val);
});

// 加载扩展信息
const loadExtendInfo = async () => {
  const res = await getTaskExtraInfo(props.taskInfo.id);
  if (res.status === "0") {
    //IP地址列表
    state.addressList = res.data.addressList;
    if (state.addressList && state.addressList.length > 0) {
      await nextTick(() => {
        ipTreeRef.value!.setCurrentKey(state.addressList[0].ipAddress);
      });
    }

    //指标Tab
    state.glues = res.data.glues;
    if (state.glues && state.glues.length > 0) {
      state.activeName = state.glues[0].id;
    }
  }
}

// 加载表格列信息
const loadTableColumnOptions = async (glueId: number) => {
  const res = await getTaskResultColumns(glueId);
  if (res.status === "0") {
    pushColumnDataToState(res.data);
    tableRef.value.refreshTable();
  }
}

// 将列信息解析并推送到State
const pushColumnDataToState = (columnData: Array<any>) => {
  if (columnData && columnData.length > 0) {
    //解析列数据
    const columns = [];
    columnData.forEach(item => {
      if (item.hide == 0) {
        columns.push({
          label: item.name,
          prop: item.field,
          overHidden: true
        });
      }
    });
    tableOption.value.column = columns;

    //解析搜索列选项
    const searchColumns = [];
    columnData.forEach(item => {
      if (item.filterEnable == 1) {
        searchColumns.push({
          label: item.name,
          value: item.field
        });
      }
    });
    state.columnSearchOptions = searchColumns;
    if (state.columnSearchOptions && state.columnSearchOptions.length > 0) {
      state.searchCondition.fieldName = state.columnSearchOptions[0].value;
    }
    state.searchCondition.fieldValue = null;
  } else {
    tableOption.value.column = [];
    state.columnSearchOptions = [];
  }
}

// Tab选择改变触发
const tabChangeHandler = async (val: number) => {
  await loadTableColumnOptions(val);
  tableRef.value.refreshTable();
  await resetTablePageAndQuery();
}

// IP地址选择触发
const addressChangeHandler = async (val: any) => {
  state.searchCondition.gatherIp = val.ipAddress;
  await resetTablePageAndQuery();
}

//重置分页后查询数据
const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}

//加载表格数据
const loadTableData = async () => {
  if (state.activeName != null) {
    state.tableLoading = true;
    state.tableData = [];
    const res = await getTaskResultData({
      pageIndex: state.tablePage.currentPage,
      pageSize: state.tablePage.pageSize,
      jobId: props.taskInfo.id,
      glueId: state.activeName,
      indexName: getEsIndex(state.activeName),
      ...state.searchCondition
    });
    if (res.status === "0") {
      state.tableData = res.data;
      state.tablePage.total = res.total;
    }
    state.tableLoading = false;
  }
}

//获取指标的ES索引
const getEsIndex = (glueId: number) => {
  const glue = state.glues.find(item => item.id == glueId);
  return glue ? glue.esIndex : "";
}

// IP地址搜索
const filterAddressNode = (value: string, data: any) => {
  if (!value) return true;
  return data.ipAddress.includes(value);
}

// 数据导出触发
const exportDataHandler = async () => {
  $message({
    message: "数据正在导出中...",
    type: "success"
  });
  await exportTaskResultData({
    jobId: props.taskInfo.id,
    glueId: state.activeName,
    indexName: getEsIndex(state.activeName),
    ...state.searchCondition
  });
}

// 界面初始化
onMounted(async () => {
  await loadExtendInfo();
  if (state.glues && state.glues.length > 0) {
    await loadTableColumnOptions(state.glues[0].id);
    tableRef.value.refreshTable();
  }
  await loadTableData();
});

// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

</script>
