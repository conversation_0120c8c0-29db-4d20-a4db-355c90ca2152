<template>
  <div>
    <div class="flex-bc w-full mb-2">
      <el-page-header @back="jumpTo('vulTaskMain')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> 漏洞扫描结果 - {{ taskInfo.name }}</span>
        </template>
      </el-page-header>
      <div class="flex-bc w-1/3 rounded pl-3 pr-3 bg-blue-500 bg-opacity-15">
        <div class="flex-sc gap-2">
          <el-text>危急:</el-text>
          <el-text class="text-primary font-bold">{{ statisticsData.seriousCount }}</el-text>
          <el-text>高危:</el-text>
          <el-text class="text-primary font-bold">{{ statisticsData.highCount }}</el-text>
          <el-text>中危:</el-text>
          <el-text class="text-primary font-bold">{{ statisticsData.mediumCount }}</el-text>
          <el-text>低危:</el-text>
          <el-text class="text-primary font-bold">{{ statisticsData.lowCount }}</el-text>
        </div>
        <div>
          <el-text type="danger" class="font-bold">{{ `用时: ${taskInfo.time ? taskInfo.time : '未知'}` }}</el-text>
        </div>
      </div>
    </div>
    <avue-crud
      ref="tableRef"
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="loadTableData"
      @current-change="loadTableData"
    >
      <template #menu-left="{ size }">
        <div class="flex-sc w-full h-full">
          <search-with-column v-model="searchCondition.value"
                              v-model:fuzzy-enable="searchCondition.fuzzy"
                              v-model:column-val="searchCondition.field"
                              :column-options="columnSearchOptions"
                              :column-select-width="90"
                              class="flex-sc w-[800px]"
                              @search="resetTablePageAndQuery"
                              @reset="searchCondition.value=null"
                              input-class-name="w-1/2"/>
        </div>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left pr-3">
          <el-tooltip
            content="导出数据"
            placement="top"
            :open-delay="1000"
          >
            <el-button
              :icon="useRenderIcon('EP-Download')"
              circle
              :size="size"
              :disabled="tableData==null||tableData.length==0"
              v-auth="'vul:task:export'"
              @click="exportDataHandler"
            />
          </el-tooltip>
        </div>
      </template>
      <template #seriousCount="{ row }">
        <el-link :type="row.seriousCount>0?'danger':'success'" @click="jumpToVulInfoHandler(row,'危急')"
                 :disabled="row.seriousCount==0" class="font-bold">
          {{ row.seriousCount }}
        </el-link>
      </template>
      <template #highCount="{ row }">
        <el-link :type="row.highCount>0?'danger':'success'" @click="jumpToVulInfoHandler(row,'高危')"
                 :disabled="row.highCount==0" class="font-bold">
          {{ row.highCount }}
        </el-link>
      </template>
      <template #mediumCount="{ row }">
        <el-link :type="row.mediumCount>0?'danger':'success'" @click="jumpToVulInfoHandler(row,'中危')"
                 :disabled="row.mediumCount==0" class="font-bold">
          {{ row.mediumCount }}
        </el-link>
      </template>
      <template #lowCount="{ row }">
        <el-link :type="row.lowCount>0?'danger':'success'" @click="jumpToVulInfoHandler(row,'低危')"
                 :disabled="row.lowCount==0" class="font-bold">
          {{ row.lowCount }}
        </el-link>
      </template>
      <template #totalCount="{ row }">
        <el-link :type="row.totalCount>0?'danger':'success'" @click="jumpToVulInfoHandler(row,'')"
                 :disabled="row.totalCount==0" class="font-bold">
          {{ row.totalCount }}
        </el-link>
      </template>
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import {computed, getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue';
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {
  exportTaskResultData,
  queryTaskResult,
  queryTaskStatistics
} from "@/views/modules/eam/assetMap/taskManage/api/vulTaskApi";
import {ResultStatus} from "@/utils/http/types";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";

const {$message} = getCurrentInstance().appContext.config.globalProperties;

// 组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});

// 定义事件
const emit = defineEmits(["jump-to", "select-host"]);

//数据对象
const state = reactive({
  tableLoading: false,
  searchCondition: {
    field: "ip_address",
    value: '',
    fuzzy: true
  },
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  statisticsData: {
    seriousCount: 0,
    highCount: 0,
    mediumCount: 0,
    lowCount: 0,
    infoCount: 0
  }
})
const {
  tableLoading,
  searchCondition,
  tableData,
  tablePage,
  statisticsData
} = toRefs(state)

const columnSearchOptions = [
  {
    label: "IP地址",
    value: "ip_address"
  }
]

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 320;
});

const tableOption = ref({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  height: tableHeight,
  rowKey: "ip_address",
  column: [
    {
      label: "IP地址",
      prop: "ip_address",
    },
    {
      label: "危急漏洞数",
      prop: "seriousCount",
    },
    {
      label: "高危漏洞数",
      prop: "highCount",
    },
    {
      label: "中危漏洞数",
      prop: "mediumCount",
    },
    {
      label: "低危漏洞数",
      prop: "lowCount",
    },
    {
      label: "合计",
      prop: "totalCount",
    }
  ]
})

//重置分页后查询数据
const resetTablePageAndQuery = async () => {
  state.tablePage.currentPage = 1;
  await loadTableData();
}

//加载表格数据
const loadTableData = async () => {
  state.tableLoading = true;
  state.tableData = [];

  //组装查询条件
  const conditions = {
    taskallid: props.taskInfo.taskallid,
    conditions: [{
      ...state.searchCondition,
      operator: state.searchCondition.fuzzy ? "fuzzy" : "exact"
    }],
    pageSize: state.tablePage.pageSize,
    pageNum: state.tablePage.currentPage
  }

  //查询数据
  const res = await queryTaskResult(conditions);
  if (res.status == ResultStatus.Success) {
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
  }
  state.tableLoading = false;

}

//加载统计数据
const loadStatisticsData = async () => {
  const res = await queryTaskStatistics(props.taskInfo.taskallid);
  if (res.status == ResultStatus.Success) {
    state.statisticsData = res.data;
  }
}

//跳转到漏洞信息页面除非
const jumpToVulInfoHandler = (h: any, level: string) => {
  emit("select-host", {
    taskallid: props.taskInfo.taskallid,
    ip: h.ip_address,
    vullevel: level,
  });
  jumpTo("vulInfoView");
}

//数据导出触发
const exportDataHandler = async () => {
  $message({
    message: "数据正在导出中...",
    type: "success"
  });
  await exportTaskResultData({
      taskallid: props.taskInfo.taskallid,
      conditions:
        [{
          ...state.searchCondition,
          operator: state.searchCondition.fuzzy ? "fuzzy" : "exact"
        }],
    }
  );
}

//挂载后初始化
onMounted(() => {
  loadTableData();
  loadStatisticsData();
});

// 跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};

</script>
