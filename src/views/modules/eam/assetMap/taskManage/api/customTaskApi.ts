import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.eamCollectServer}/ibex/job`;

export type HostTaskFormData = {
  id?: number;
  jobName: string;
  scheduleType: string;
  scheduleConf: string;
  addressList: Array<string>;//IP地址列表
  glueIds: Array<number>//指标ID列表
}

//查询任务数据
const getTaskData = (params: any) =>
  http.postJson<any>(`${basePath}/pageList`, params);

//查询指标数据列表-用于指标选择
const getKpiList = () =>
  http.get<any, any>(`${basePath}/glue/findKV`);

//查询主机数据列表
const getHostList = (params: any) =>
  http.postJson<any>(`${basePath}/address`, params);

//新建主机探测任务
const createHostTask = (params: HostTaskFormData) =>
  http.postJson<any>(`${basePath}/add`, params);

//更新主机探测任务
const updateHostTask = (params: HostTaskFormData) =>
  http.postJson<any>(`${basePath}/update`, params);

//查询任务关联的额外信息
const getTaskExtraInfo = (id: number) =>
  http.get<any, any>(`${basePath}/detail/glue/address?id=${id}`);

//删除任务
const deleteTask = (ids: string) =>
  http.get<any, any>(`${basePath}/del?ids=${ids}`);

//手动执行任务
const executeTask = (id: number) =>
  http.get<any, any>(`${basePath}/trigger?id=${id}`);

//启用任务
const enableTask = (id: number) =>
  http.get<any, any>(`${basePath}/start?id=${id}`);

//禁用任务
const disableTask = (id: number) =>
  http.get<any, any>(`${basePath}/stop?id=${id}`);

//获取任务执行结果的表格列数据
const getTaskResultColumns = (glueId: number) =>
  http.get<any, any>(`${basePath}/result/glue/field?glueId=${glueId}`);

//获取任务执行结果数据
const getTaskResultData = (params: any) =>
  http.postJson<any>(`${basePath}/result/page`, params);

//导出任务执行结果数据
const exportTaskResultData = (params: any) =>
  http.postBlobWithJson<any>(`${basePath}/result/export`, params);

export {
  getTaskData,
  getKpiList,
  getHostList,
  createHostTask,
  updateHostTask,
  getTaskExtraInfo,
  deleteTask,
  executeTask,
  enableTask,
  disableTask,
  getTaskResultColumns,
  getTaskResultData,
  exportTaskResultData
}
