import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.eamCollectServer}/operation/probe/conf`;

//查询资产类型数据
const getAssetTypeList = (code: string) =>
  http.get<any, any>(`${basePath}/getOperationAssetCategory?code=${code}`);

//查询IP资源池Tree数据
const getIpPoolTreeData = () =>
  http.get<any, any>(`${basePath}/getIpIstrationTree`);

export {
  getAssetTypeList,
  getIpPoolTreeData
}
