import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.securityVulServer}/v3/vul`;

export type VulTaskFormData = {
  taskallid?: string;
  taskType?: string; //漏扫执行类型
  relatedVulIds: string;
  policyName: string; //任务名称
  operationType: string; //执行范围类型标识  1 自定义 2 资源管理 3 IP管理
  operationDataList: Array<string>; //执行范围标识数据
  vulScanTool: string; //漏扫工具：系统内置： Nessus 山石网科：hillStone
  scheduleType?: string;
  schedule: string; //任务执行周期标识  0=立即执行，1=定时执行一次；2=每天；3=每周；4=每月；
  schedulingCycle: string; //漏扫定期执行表达式
}

//查询任务数据
const queryTaskData = (params: any) =>
  http.postJson<any>(`${basePath}/queryTasklist`, params);

//下发任务
const addVulTask = (params: any) =>
  http.postJson<any>(`${basePath}/addNewTask`, params);

//更新任务
const updateVulTask = (params: any) => {
  params.target = [];
  return http.postJson<any>(`${basePath}/updateTask`, params);
}

//删除任务
const deleteVulTask = (params: any) =>
  http.postJson<any>(`${basePath}/deleteTaskBatch`, params);

//查询任务执行结果
const queryTaskResult = (params: any) =>
  http.postJson<any>(`${basePath}/queryVulCountScanResult`, params);

//查询任务执行统计数据
const queryTaskStatistics = (taskallid: string) =>
  http.get<any, any>(`${basePath}/queryCountByVulScan?taskallid=${taskallid}`);

//导出任务结果数据
const exportTaskResultData = (params: any) =>
  http.postBlobWithJson<any>(`${basePath}/import/vulCountScanResult`, params);

//查询漏洞信息-表格数据
const queryVulInfo = (params: any) =>
  http.postJson<any>(`${basePath}/queryVulCveInfoScanResult`, params);

//查询某个漏洞的详细信息
const queryVulDetail = (id: string) =>
  http.postJson<any>(`${basePath}/queryVulDetailInfoScanResult`, {id});

//查询漏洞任务类型
const queryVulTaskType = () =>
  http.get<any, any>(`${basePath}/queryVulScanToolList`);

//查询漏洞扫描类型
const queryVulScanType = (code: string) =>
  http.get<any, any>(`${basePath}/queryTaskTypeList?vulScanTool=${code}`);

export {
  queryTaskData,
  addVulTask,
  updateVulTask,
  deleteVulTask,
  queryTaskResult,
  queryTaskStatistics,
  exportTaskResultData,
  queryVulInfo,
  queryVulDetail,
  queryVulTaskType,
  queryVulScanType,
}
