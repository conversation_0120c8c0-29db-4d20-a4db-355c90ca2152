import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.eamCollectServer}`;

const queryCollTableData = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe3/conf/queryCollTableData`, params);
const querySwitchTableData = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe3/conf/querySwitchTableData`, params);
const queryAllCollAccess = (params: string) =>
  http.get<any, any>(`${basePath}/operation/probe/conf/queryAllCollAccess?cronId=${params}`);
const queryCollAccess = (params: string) =>
  http.get<any, any>(`${basePath}/operation/probe/conf/queryCollAccess?cronId=${params}`);
const queryCollCondition = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe/conf/queryCollCondition`, params);
const queryCollColumn = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe/conf/queryCollColumn`, params);
const queryCollResult = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe/conf/queryCollResult`, params);
const exportCollResult = (params: any) =>
  http.postBlobWithJson<any>(`${basePath}/operation/probe/conf/exportCollResult`, params)
const exportCollResultByWord = (params: any) =>
  http.postBlobWithJson<any>(`${basePath}/operation/data/conf/exportCollResultByWord`, params)
const deleteOperationAllCronIds = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe3/conf/deleteOperationAllCronIds`, params);
const executeOperationAllTask = (params: any) =>
  http.get<any, any>(`${basePath}/collection/executeOperationAllTask?cronId=${params.cronId}&collType=${params.collType}`);
const closeProcessByCronId = (params: any) =>
  http.get<any, any>(`${basePath}/operation/probe/conf/closeProcessByCronId?cronId=${params.cronId}&id=${params.id}`);
const insertOrUpdateAllCron = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe/cmdb/insertOrUpdateAllCron`, params);
const queryCollPolicyExists = (params: any) =>
  http.get<any, any>(`${basePath}/operation/probe/cmdb/queryCollPolicyExists?policyName=${params.policyName}&cronId=${params.cronId}`);
const queryCronByCronId = (params: string) =>
  http.get<any, any>(`${basePath}/operation/probe3/conf/queryCronByCronId?cronId=${params}`);
const querySwitchTableInfo = (params: any) =>
  http.get<any, any>(`${basePath}/operation/probe3/conf/querySwitchTableInfo?queryCode=${params}`);
const validateSwitchIp = (params: any) =>
  http.get<any, any>(`${basePath}/operation/probe3/conf/validateSwitchIp?ipAddress=${params}`);
const insertSwitchTableInfo = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe3/conf/insertSwitchTableInfo`, params);
const deleteSwitchTableInfo = (params: any) =>
  http.postJson<any>(`${basePath}/operation/probe3/conf/deleteSwitchTableInfo`, params);
const exportSwitchTemp = () =>
  http.postBlob<any>(`${basePath}/operation/probe3/conf/exportSwitchTemp`)
const queryProductInfo = (params: any) =>
  http.postJson<any>(`${basePath}/operation/data/conf/queryProductInfo`, params)

export {
  queryCollTableData,
  querySwitchTableData,
  queryAllCollAccess,
  queryCollCondition,
  queryCollColumn,
  queryCollResult,
  queryCollAccess,
  exportCollResult,
  exportCollResultByWord,
  deleteOperationAllCronIds,
  executeOperationAllTask,
  closeProcessByCronId,
  insertOrUpdateAllCron,
  queryCollPolicyExists,
  queryCronByCronId,
  querySwitchTableInfo,
  validateSwitchIp,
  insertSwitchTableInfo,
  deleteSwitchTableInfo,
  exportSwitchTemp,
  queryProductInfo
}
