<template>
  <el-dialog v-model="drawerVisible" title="属性选择" @close="handleClose" append-to-body v-if="drawerVisible" :close-on-click-modal="false"
  >
    <div>
      <el-input v-model="queryParam.keyword" placeholder="请输入属性编码或名称" class="w-1/2">
        <template #append>
          <el-checkbox v-model="queryParam.keywordVague" label="模糊" :value="true"/>
        </template>
      </el-input>
      <el-button type="primary" class="ml-3" @click="sizeChange">查询</el-button>
      <el-button type="primary" @click="resetImportPros">重置</el-button>
    </div>
    <avue-crud :data="propertyData" ref="propertyTable" :option="tableOption"  v-model:page="tablePage"
               @size-change="sizeChange"
               @current-change="initImportPros"
               @selection-change="selectionChange"
               >

    </avue-crud>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="savePros">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref, toRefs,watch,onMounted} from 'vue'
import {
  initImportPropData
} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";

const emit = defineEmits(["update:visible","insertPros"]);

const state = reactive({
  drawerVisible: false,
  queryForm:{
    code:"",
    name:"",
  },
  queryParam:{
    keyword:"",
    keywordVague:true
  },
  propertyData:[],
  selectPros:[],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: 10,
    pageSizes: pageSizeOptions
  },
})
const props = defineProps({
  visible: {
    type: Boolean
  },
  categoryId : {
    type: String
  },
  importPros : {
    type: Array
  }
});
const {drawerVisible,queryParam,selectPros,propertyData,tablePage} = toRefs(state);
const handleClose = () => {
  emit("update:visible", false);
};
watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    state.queryParam.keywordVague=true;
    state.queryParam.keyword='';
    return;
  }
  if(state.drawerVisible){
    initImportPros();
  }
});
const savePros = () =>{
  console.log(state.selectPros);
  emit('insertPros',state.selectPros);
  sizeChange();
}
const resetImportPros = () =>{
  state.queryParam.keyword = "";
  state.queryParam.keywordVague = true;
  sizeChange();
}
const selectionChange = (val) =>{
  state.selectPros = [];
  if(val&&val.length>0){
    for(let i = 0;i<val.length;i++){
      state.selectPros.push(val[i].code);
    }
  }

}

const initImportPros = () =>{
  let params = {
    categoryId:"",
    selectProps:[],
    queryCode:"",
    queryType:"1",
    pageNum:1,
    pageSize:10
  };
  params.categoryId = props.categoryId;
  params.selectProps = props.importPros;
  params.queryCode = state.queryParam.keyword;
  if(state.queryParam.keywordVague){
    params.queryType = "0";
  }else{
    params.queryType = "1";
  }
  params.pageNum = state.tablePage.currentPage;
  params.pageSize = state.tablePage.pageSize;
  initImportPropData(params).then(res=>{
    state.propertyData = res.data.rows;
    state.tablePage.total = res.data.total;
  })
}
const sizeChange = () =>{
  let params = {
    categoryId:"",
    selectProps:[],
    queryCode:"",
    queryType:"1",
    pageNum:1,
    pageSize:10
  };
  params.categoryId = props.categoryId;
  params.selectProps = props.importPros;
  params.queryCode = state.queryParam.keyword;
  if(state.queryParam.keywordVague){
    params.queryType = "1";
  }else{
    params.queryType = "0";
  }
  params.pageNum = 1;
  params.pageSize = state.tablePage.pageSize;
  initImportPropData(params).then(res=>{
    state.propertyData = res.data.rows;
    state.tablePage.total = res.data.total;
  })
}
//挂载后初始化
onMounted(() => {
});
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  refreshBtn: false, //表格上面小的 刷新按钮
  columnBtn: false,  //表格上面小的 列表按钮列动态显隐按钮
  searchBtn: false,  //表格上面小的 搜索按钮
  menuWidth: 130,
  menu: false,
  selection:true,
  rowKey:"name",
  column: [
    {
      label: "属性名称",
      prop: "name"
    },
    {
      label: "属性编码",
      prop: "code"
    }
  ]
});
</script>
