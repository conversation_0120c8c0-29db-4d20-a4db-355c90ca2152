<template>
  <el-dialog v-model="biduiVisible" title="资产数据比对" @close="biduiClose" append-to-body v-if="biduiVisible">
    <avue-crud :data="formList" :option="tableOption"></avue-crud>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, toRefs,watch} from 'vue'
const state = {
  biduiVisible:false,
  formList:[]
}
const props = defineProps({
  visible: {
    type: Boolean
  },
  dataList : {
    type: Array
  }
});
//监听组件属性
watch(props, async (newValue: any) => {
  state.biduiVisible = newValue.visible;
  state.formList = newValue.dataList;
});

const {biduiVisible,formList} = toRefs(state);

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  maxHeight:600,
  updateBtn:false,
  saveBtn:false,
  cancelBtn:false,
  searchBtn:false,
  emptyBtn:false,
  refreshBtn:false,
  columnBtn:false,
  searchShowBtn:false,
  gridBtn:false,
  menu:false,
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  column: [
    {
      display:false,
      label: "数据源",
      children:[
        {
          prop:"srcKey",
          label:"字段",
          html: true,
          formatter: (val) => {
            return `<b>${val.srcKey}</>`;
          }
        },
        {
          prop:"srcValue",
          label:"值"
        }
      ]
    },
    {
      display:false,
      label:"资产",
      children:[
        {
          prop:"destKey",
          label:"字段",
          html: true,
          formatter: (val) => {
            return `<b>${val.destKey}</b>`;
          }
        },
        {
          prop:"destValue",
          label:"值"
        }
      ]
    }
  ]
});
const emit = defineEmits(["update:visible"]);
const biduiClose = () =>{
  emit("update:visible", false);
}
</script>
