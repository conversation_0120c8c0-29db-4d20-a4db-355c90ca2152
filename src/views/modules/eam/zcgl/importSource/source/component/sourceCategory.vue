<template>
  <div>
    <div class="flex-bc">
      <el-page-header @back="jumpTo('sourceImport')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> 入库规则维护 </span>
        </template>
      </el-page-header>
    </div>
    <div style="font-size:14px">
      <el-row class="mt-5 mb-5 pl-10">
        <el-col :span="8">
          <span class="mr-3 font-bold">数据源ID:</span>&nbsp<span>{{sourceData.sourceId}}</span>
        </el-col>
        <el-col :span="8">
          <span class="mr-3  font-bold">数据源名称:</span>&nbsp<span>{{sourceData.sourceName}}</span>
        </el-col>
        <el-col :span="8">
          <span class="mr-3 font-bold">入库方式:</span>&nbsp<span>{{sourceData.sourceType}}</span>
        </el-col>
      </el-row>
    </div>
    <div>
      <el-tabs v-model="cateTabName" editable type="card" class="demo-tabs" @edit="editCateTab" @tab-click="clickTab">
        <el-tab-pane  v-for="(tab, index) in cateTabs" :key="index" :name="tab.importCategoryId" :label="tab.categoryName">
          <template #label>
            <span @dblclick="editTab(tab)">{{ tab.categoryName }}</span>
          </template>
          <el-card style="font-size:14px;position:relative;" class="mb-3">
            <div style="position:absolute;top:10px;right:10px;z-index:1;">
              <el-button @click="editTab(tab)">编辑</el-button>
            </div>
            <el-row class="mb-2">
              <el-col :span="8">
                <span class="mr-3 font-bold size-8">是否启用导入主键:</span>&nbsp
                <span>{{tab.isImportUnique==='0'?'是':'否'}}</span>
              </el-col>
              <el-col :span="8" v-if="tab.isImportUnique==='0'">
                <span class="mr-3 font-bold size-8">主键:</span>&nbsp
                <span>{{tab.importPrimaryName}}</span>
              </el-col>
              <el-col :span="8">
                <span class="mr-3 font-bold size-8">导入规则:</span>&nbsp
                <span>{{tab.importType=='0'?'新增并更新':tab.importType=='1'?'仅新增':'仅更新'}}</span>
              </el-col>
              <el-col :span="8" v-if="tab.isImportUnique!='0'">
                <span class="mr-3 font-bold small">类别映射字段:</span>&nbsp
                <span>{{tab.categoryMappingField}}</span>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" v-if="tab.isImportUnique!='0'">
                <span class="mr-3 font-bold small">类别映射值:</span>&nbsp
                <span>{{tab.categoryMappingValue}}</span>
              </el-col>
              <el-col :span="8" v-if="tab.isImportUnique=='0'">
                <span class="mr-3 font-bold small">类别映射字段:</span>&nbsp
                <span>{{tab.categoryMappingField}}</span>
              </el-col>
              <el-col :span="8" v-if="tab.isImportUnique=='0'">
                <span class="mr-3 font-bold small">类别映射值:</span>&nbsp
                <span>{{tab.categoryMappingValue}}</span>
              </el-col>
            </el-row>
          </el-card>
          <avue-crud :data="fieldData" :option="tableOption" @row-del="deleteProps">
            <template #menu-left="{ size }">
              <el-button
                :icon="useRenderIcon('EP-Plus')"
                @click="insertProps"
              >添加属性</el-button>
              <el-button
                @click="saveProps"
                :icon="useRenderIcon('EP-Select')"
              >保存</el-button>
              <el-upload
                style="display:inline-block;height:24px;vertical-align:-1.5px;"
                v-if="sourceInfo.sourceType==='2'"
                ref="upload"
                class="upload-demo ml-2.5"
                action="rest/eam-core/zcgl-common/importBySource/queryImportExcelTitle"
                :show-file-list="false"
                :on-success="importSuccess"
                :on-error="importError"
                :auto-upload="true"
                         :before-upload="beforeUpload"
              >
                <el-button :icon="useRenderIcon('EP-Select')">字段识别</el-button>
              </el-upload>
            </template>

            <template #srcField="scope">
              <el-input type="text" v-model = "scope.row.srcField" :rules="{ required: true, message: '请输入源字段', trigger: 'blur' }"></el-input>
            </template>
            <template #targetField="scope">
              <el-select v-model="scope.row.targetField" filterable clearable>
                <el-option v-for="item in proComList" :label="item.name" :value="item.code" :key="item.code" :rules="{ required: true, message: '请选择资产属性', trigger: 'change' }"></el-option>
              </el-select>
            </template>
            <template #isTableShow="scope">
              <el-select v-model="scope.row.isTableShow" filterable clearable :rules="{ required: true, message: '请选择是否列表展示', trigger: 'change' }">
                <el-option label="是" :value="'0'" :key="'0'"></el-option>
                <el-option label="否" :value="'1'" :key="'1'"></el-option>
              </el-select>
            </template>
            <template #updateType="scope">
              <el-select v-model="scope.row.updateType" filterable clearable :rules="{ required: true, message: '请选择入库方式', trigger: 'change' }">
                <el-option label="全量更新" :value="'0'" :key="'0'"></el-option>
                <el-option label="仅更新空值" :value="'1'" :key="'1'"></el-option>
                <el-option label="空值覆盖" :value="'2'" :key="'2'"></el-option>
              </el-select>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </div>
    <source-category v-model:visible="isCategoryVisiable" :type="editType" :category-form="categoryForm" :source-type="sourceInfo.sourceType" @refreshTab="refreshCateTab"></source-category>
    <add-import-property v-model:visible="isShowProperty" :category-id="selectCate" :import-pros="selectPros" @insert-pros="insertPros"></add-import-property>
  </div>
</template>
<script setup lang="ts">
import {onMounted, reactive, toRefs,ref} from "vue";
import {genFileId, TabsPaneContext} from 'element-plus'
import {
  deleteImportSourceCategory, queryImportField, queryImportProperty,
  queryImportSourceCategory,saveImportField,queryImportExcelTitle
} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import SourceCategory from "@/views/modules/eam/zcgl/importSource/source/component/CategoryEdit.vue";
import AddImportProperty from "@/views/modules/eam/zcgl/importSource/source/component/addImportProperty.vue";
import {ElMessageBox,ElMessage,UploadInstance,UploadProps,UploadRawFile} from "element-plus";
const state = reactive({
  sourceData:{
    sourceId:"",
    sourceName:"",
    sourceType:""
  },
  fieldData:[],
  cateProList:[],
  cateTabName:'',
  cateTabs:[],
  categoryForm:{
    categoryId:"",
    categoryName:'',
    isImportUnique:"0",
    importPrimary:[],
    importType:"0",
    categoryMappingField:"",
    categoryMappingValue:"",
    importCategoryId:"",
    sourceId:""
  },
  selectPros:[],
  isCategoryVisiable:false,
  isShowProperty:false,
  selectCate:"",
  selectImportCate:"",
  proComList:[],
  editType:'add',
  saveType:'save'
})
const props = defineProps({
  sourceInfo : {
    type: Object
  }
});
const editCateTab = (tabName,action) =>{
  if(action === 'add'){
    state.editType = 'add';
    state.categoryForm.importCategoryId = '';
    state.categoryForm.categoryId = '';
    state.categoryForm.isImportUnique = '0';
    state.categoryForm.importPrimary = [];
    state.categoryForm.importType = '0'
    state.categoryForm.categoryMappingField = '';
    state.categoryForm.categoryMappingValue = '';
    state.categoryForm.sourceId = state.sourceData.sourceId;
    state.isCategoryVisiable = true;
  }
  if(action === 'remove'){
    deleteTab(tabName);
  }
}
const initTableDataByTab = (tabName) =>{
  state.selectPros = [];
  state.cateTabName = tabName.importCategoryId;
  state.selectCate = tabName.categoryId;
  queryProByCate();
  queryImportField(tabName.importCategoryId).then(res=>{
    state.fieldData = res.data;
    if(state.fieldData&&state.fieldData.length>0){
      for(let i = 0;i<state.fieldData.length;i++){
        state.selectPros.push(state.fieldData[i].targetField);

      }
    }
  })
}
const initPropertyList = (value) =>{
  queryImportProperty(value).then(res=>{
    state.cateProList = res.data;
  })
}
const clickTab = (tab: TabsPaneContext, event: Event) =>{
  console.log(state.cateTabs[tab.index]);
  initPropertyList(state.cateTabs[tab.index]);
  initTableDataByTab(state.cateTabs[tab.index]);
}
const deleteTab = (tname) =>{
  ElMessageBox.confirm('如果删除，该数据源分类下的所有配置将被删除，是否确认删除？','warning',{
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(()=>{
    deleteImportSourceCategory(tname).then(res=>{
      if(res.data!='error'){
        refreshCateTab(null);
      }
    })
  }).catch(()=>{

  })

}
const insertProps = () =>{
  state.isShowProperty = true;
}

const insertPros = (proList) =>{
  state.selectPros.push(...proList);
  let addData = [];
  for(let i=0;i<proList.length;i++){
    let dd = {
      fieldId:"",
      importCategoryId:"",
      sourceId:"",
      srcField:"",
      targetField:"",
      isTableShow:"",
      updateType:"",
      sortId:0
    };
    dd.targetField = proList[i];
    dd.sortId = state.fieldData.length+1+i;
    dd.importCategoryId = state.cateTabName;
    dd.sourceId = state.sourceData.sourceId;
    dd.isTableShow = "1";
    dd.updateType = "0";
    addData.push(dd);
  }
  state.fieldData.push(...addData);
}

const refreshCateTab = (data) =>{
  queryImportSourceCategory(props.sourceInfo.sourceId).then(res=>{
    state.cateTabs = res.data;
    if(data){
      state.cateTabName = data;
      let tab = {
        importCategoryId:data
      };
      if(state.cateTabs&&state.cateTabs.length>0) {
        for (let i = 0; i < state.cateTabs.length; i++) {
          if(state.cateTabs[i].importCategoryId===data){
             tab = state.cateTabs[i];
          }
        }
      }
      initPropertyList(tab.importCategoryId);
      initTableDataByTab(tab);
    }else{
      if(state.cateTabs&&state.cateTabs.length>0){
        state.cateTabName = state.cateTabs[0].importCategoryId;
        state.selectCate = state.cateTabs[0].categoryId;
        initPropertyList(state.cateTabs[0].importCategoryId);
        initTableDataByTab(state.cateTabs[0]);
      }
    }
  })
}
const {sourceData,cateTabName,cateTabs,fieldData,saveType,cateProList,categoryForm,isCategoryVisiable,isShowProperty,selectPros,selectCate,proComList,editType} = toRefs(state);
onMounted(() => {
  refreshCateTab(null);
  state.sourceData.sourceId = props.sourceInfo.sourceId;
  state.sourceData.sourceName = props.sourceInfo.sourceName;
  if(props.sourceInfo.sourceType==='1'){
    state.sourceData.sourceType = '接口';
  }else{
    state.sourceData.sourceType = '人工导入';
  }

})
const tableOption = reactive({
  index: true,
  indexLabel: '序号',
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: true,
  menuWidth: 130,
  maxHeight:475,
  column: [
    {
      label: "源字段",
      prop: "srcField"
    },
    {
      label: "资产属性",
      prop: "targetField"
    },
    {
      label: "是否列表显示",
      prop: "isTableShow"
    },
    {
      label: "更新方式",
      prop: "updateType"
    }
  ]
});
const emit = defineEmits(["jump-to","source-select"]);
const queryProByCate = () =>{
  queryImportProperty(state.selectCate).then(res=>{
    state.proComList = res.data;
  })
}
const editTab = (tab) =>{
  state.editType = 'edit';
  state.categoryForm.categoryId = tab.categoryId;
  state.categoryForm.sourceId = tab.sourceId;
  state.categoryForm.isImportUnique = tab.isImportUnique;
  if(tab.importPrimary){
    state.categoryForm.importPrimary = tab.importPrimary.split(',');
  }
  state.categoryForm.importType = tab.importType;
  state.categoryForm.categoryMappingField = tab.categoryMappingField;
  state.categoryForm.categoryMappingValue = tab.categoryMappingValue;
  state.categoryForm.importCategoryId = tab.importCategoryId;
  state.isCategoryVisiable = true;
}
const deleteProps = (row,index) =>{
  console.log(row,index);
  state.fieldData.splice(index,1);
  if(state.fieldData.length>0){
    for(let i=0;i<state.fieldData.length;i++){
      state.fieldData[i].sortId = i;
    }
  }
  if(row.targetField){
    let deleteId = -1;
    if(state.selectPros.length>0){
      for(let i=0;i<state.selectPros.length;i++){
        if(state.selectPros[i]==row.targetField){
          deleteId = i;
        }
      }
    }
    state.selectPros.splice(deleteId,1);
  }
}
const saveProps = () =>{
  console.log(state.fieldData);
  let flag = true;
  let flag1 = true;
  if(state.fieldData.length>0){
    for(let i = 0;i<state.fieldData.length;i++){
      let row = state.fieldData[i];
      if(!row.srcField||!row.targetField||!row.isTableShow||!row.updateType){
        flag = false;
        break;
      }
    }
  }
  if(!flag){
    ElMessage.error('表格内所有属性不可为空');
    return;
  }
  let msg = [];
  if(state.fieldData.length>0){
    for(let i =0;i<state.fieldData.length;i++){
      if(state.fieldData[i].targetField.indexOf(".")>0){
        continue;
      }
      for(let t =0;t<state.fieldData.length;t++){
        if(state.fieldData[t].targetField.indexOf(".")>0){
          continue;
        }
        if(i<t){
          if(state.fieldData[i].srcField==state.fieldData[t].srcField){
            flag1 = false;
            msg.push("第"+(i+1)+"行的源字段和第"+(t+1)+"行的源字段存在重复");
          }
          if(state.fieldData[i].targetField==state.fieldData[t].targetField){
            flag1 = false;
            msg.push("第"+(i+1)+"行的资产属性和第"+(t+1)+"行的资产属性存在重复");
          }
        }
      }
    }
  }
  if(!flag1){
    ElMessageBox.alert(
      msg.join("<br/>"),
      '错误信息',
      {
        dangerouslyUseHTMLString: true,
      }
    )
    return;
  }
  let params = {
    importSourceFieldList:[],
    importCategoryId:"",
    sourceId:""
  };
  params.importCategoryId = state.cateTabName;
  params.sourceId = state.sourceData.sourceId;
  params.importSourceFieldList = state.fieldData;
  saveImportField(params).then(res=>{
    if(res.data === 'error'){
      ElMessage.error('保存失败，请联系管理员');
    }else{
      ElMessage({
        message: '保存成功',
        type: 'success',
      })
    }
  })
}

const importSuccess = (result) => {
  if(state.fieldData.length>0){
    ElMessageBox.confirm('是否确认识别，如果识别，已识别属性将被全部覆盖','warning',{
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(()=>{
      state.fieldData = [];
      if(result.data && result.data.length>0){
        let addData = [];
        for(let i=0;i<result.data.length;i++){
          let dd = {
            fieldId:"",
            importCategoryId:"",
            sourceId:"",
            srcField:"",
            targetField:"",
            isTableShow:"",
            updateType:"",
            sortId:0
          };
          dd.srcField = result.data[i];
          dd.sortId = i;
          dd.importCategoryId = state.cateTabName;
          dd.sourceId = state.sourceData.sourceId;
          dd.isTableShow = "1";
          dd.updateType = "0";
          addData.push(dd);
        }
        state.fieldData.push(...addData);
      }else{
        ElMessage.error("识别失败，请检查EXCEL文件");
      }
    }).catch(()=>{

    })
  }else{
    state.fieldData = [];
    if(result.data && result.data.length>0){
      let addData = [];
      for(let i=0;i<result.data.length;i++){
        let dd = {
          fieldId:"",
          importCategoryId:"",
          sourceId:"",
          srcField:"",
          targetField:"",
          isTableShow:"",
          updateType:"",
          sortId:0
        };
        dd.srcField = result.data[i];
        dd.sortId = i;
        dd.importCategoryId = state.cateTabName;
        dd.sourceId = state.sourceData.sourceId;
        dd.isTableShow = "1";
        dd.updateType = "0";
        addData.push(dd);
      }
      state.fieldData.push(...addData);
    }else{
      ElMessage.error("识别失败，请检查EXCEL文件");
    }
  }
}
const upload = ref<UploadInstance>();
const importError = (error) =>{
  console.log(error);
  ElMessage.error("文件识别失败，请检查EXCEL文件");
}
const beforeUpload = (rawFile: UploadRawFile) =>{
  if(rawFile.name.indexOf(".")>=0){
    if(rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xlsx'||rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xls'){
      return true;
    }else{
      ElMessage.error("只支持识别Excel文件");
      return false;
    }
  }else{
    ElMessage.error("只支持识别Excel文件");
    return false;
  }
}
const importDataSuccess = (result) => {
  console.log(result);
}
const importDataError = (error) =>{
  console.log(error);
  ElMessage.error("文件上传失败，请联系管理员");
}
const jumpTo = (sign: string) => {
  emit("source-select",props.sourceInfo.sourceId);
  emit("jump-to", sign);
};
const updateTab = (tab) =>{
  state.saveType = 'edit';
}
const saveTab = (tab) =>{
  state.saveType = 'save';
}
</script>
