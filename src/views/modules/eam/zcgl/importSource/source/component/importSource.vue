<template>
  <div>
    <el-drawer v-model="drawer" title="Excel数据导入" :with-header="true"
               size="45%"
    :close-on-click-modal="false"
               @close="closeDrawer"
    >
      <el-upload
        ref="uploader"
        class="asset-upload"
        :data="param"
        :on-error="onUploadError"
        :on-success="onUploadSuccess"
        :on-progress="onFileProgress"
        :before-upload="beforeUpload"
        drag
        :limit="1"
        accept=".xlsx"
        action="rest/eam-core/zcgl-common/importSourceData/importExcelSourceData"
        >
        <el-icon class="el-icon-upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传文件</em> 或者拖拽上传</div>
        <div class="upload-desc" style="padding-top:5px">只能上传excel文件</div>
      </el-upload>
      <el-col :sm="12" :lg="24" v-show="successResult">
        <el-result
          icon="success"
          title="导入成功"
          :sub-title="importSuccessData"
        >
        </el-result>
      </el-col>
      <el-col :sm="12" :lg="24" v-show="importingResult">
        <el-result
          icon="success"
          title="数据导入中,请稍候...."
          :sub-title="importSuccessData"
        >
        </el-result>
      </el-col>
      <el-col :sm="12" :lg="24" v-show="errorResult">
        <el-result
          icon="error"
          title="导入失败"
          :sub-title="importErrorData"
        >
        </el-result>
      </el-col>
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
import {onUnmounted, ref, toRefs, watch} from 'vue';
import { UploadFilled } from '@element-plus/icons-vue';
import type { UploadProps,UploadInstance } from 'element-plus'
import {queryImportSourceResult} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";

const state = {
  drawer:false,
  param:{},
  isShowImportResult:false,
  importErrorData:"",
  importSuccessData:"",
  importingMsgData:"",
  percent:0
}
const successResult = ref(false);
const errorResult = ref(false);
const importingResult = ref(false);
const {
  drawer,
  param,
  isShowImportResult,
  importErrorData,
  importSuccessData,
  percent,
  importingMsgData
} = toRefs(state);
const props = defineProps({
  sourceInfo : {
    type: Object
  },
  visible: {
    type: Boolean
  },
});
const emit = defineEmits(["closeImportSource","refreshTable"]);
const closeDrawer = () =>{
  emit("closeImportSource");
  emit("refreshTable");
}
//监听组件属性
watch(props, async (newValue: any) => {
  state.drawer = newValue.visible;
  if (!state.drawer) {
    return;
  }
});
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  console.log("进入beforeUpload")
  state.isShowImportResult = false;
  errorResult.value = false;
  successResult.value = false;
  state.importErrorData = "";
  state.importSuccessData = "";
  state.importingMsgData = "";
  if(rawFile.name.indexOf(".")>=0){
    if(rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xlsx'||rawFile.name.split(".")[rawFile.name.split(".").length-1]=='xls'){
      state.param.sourceId = props.sourceInfo.sourceId;
      return true;
    }else{
      state.isShowImportResult = true;
      errorResult.value = true;
      state.importErrorData = "只支持识别Excel文件";
      return false;
    }
  }else{
    state.isShowImportResult = true;
    errorResult.value = true;
    state.importErrorData = "只支持识别Excel文件";
    return false;
  }
}
const uploader = ref(null)
const onUploadError = (err, file, fileList) => {
  state.isShowImportResult = true;
  errorResult.value = true;
  state.importErrorData = err.msg;
  uploader.value.clearFiles();
}
const ttInterval = ref();
const onUploadSuccess:UploadProps['onSuccess'] = (response,uploadFile) => {
  console.log(response);
  if(response.data.result=='success') {
    console.log("导入成功");
    state.isShowImportResult = true;
    successResult.value = true;
    state.importSuccessData = response.data.data;
  }else if(response.data.result=='importing'){
    state.isShowImportResult = true;
    importingResult.value = true;
    state.importingMsgData = '';
    ttInterval.value = setInterval(() => {
      queryImportSourceResult(props.sourceInfo.sourceId).then(res=>{
        if(res.data['importResult']=='success'){
          importingResult.value = false;
          state.isShowImportResult = true;
          successResult.value = true;
          state.importSuccessData = "共计处理"+res.data.sourceCount+"条数据，新增"+res.data.insertCount+"条，更新"+res.data.updateCount+"条，过滤"+res.data.errorCount+"条";
          clearIntervals();
        }else if(res.data['importResult']=='error'){
          importingResult.value = false;
          state.isShowImportResult = true;
          errorResult.value = true;
          state.importErrorData = response.data.msg;
          clearIntervals();
        }
      })
    },2000)
  }else{
    state.isShowImportResult = true;
    errorResult.value = true;
    state.importErrorData = response.data.data;
  }
  uploader.value.clearFiles();
}
const clearIntervals = () => {
  console.log("清除定时器")
  clearInterval(ttInterval.value);
}
onUnmounted(()=>{
  clearInterval(ttInterval.value);
})
/**钩子函数： 进度*/
const onFileProgress = (event, file, fileList) => {
  state.percent = Number(event.percent.toFixed(2));
}

</script>
