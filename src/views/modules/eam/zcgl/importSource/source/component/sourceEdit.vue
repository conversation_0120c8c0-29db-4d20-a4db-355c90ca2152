<template>
  <el-dialog v-model="drawerVisible" title="数据源维护" @close="handleClose" append-to-body v-if="drawerVisible" :close-on-click-modal="false">
    <el-form :model="sourceForm" ref="ruleFormRef">
      <el-form-item label="数据源名称" :label-width="120" prop="sourceName" :rules="{ required: true, message: '请输入数据源名称', trigger: 'blur' }">
        <el-input v-model="sourceForm.sourceName" autocomplete="off" />
      </el-form-item>
      <el-form-item label="入库方式" :label-width="120" required prop="sourceType" :rules="{ required: true, message: '请选择入库方式', trigger: 'blur' }">
        <el-select v-model="sourceForm.sourceType" placeholder="请选择入库方式">
          <el-option label="接口" :value="'1'" />
          <el-option label="人工导入" :value="'2'" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSource(ruleFormRef)">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref, toRefs,watch} from 'vue'
import {
  saveImportSource
} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'

const emit = defineEmits(["update:visible","refreshTab"]);
const state = reactive({
  drawerVisible: false,
  sourceForm:{
    sourceId:"",
    sourceName:"",
    sourceType:"1"
  }
})
const props = defineProps({
  visible: {
    type: Boolean
  },
  sourceForm : {
    type: Object
  }
});
const {drawerVisible,sourceForm} = toRefs(state);
const ruleFormRef = ref<FormInstance>()
const handleClose = () => {
  emit("update:visible", false);
};

//监听组件属性
watch(props, async (newValue: any) => {
  state.sourceForm = newValue.sourceForm;
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
});
const saveSource = (formEl: FormInstance | undefined) =>{
  formEl.validate((valid,fields) =>{
    if(valid){
      saveImportSource(sourceForm).then(res=>{
        if(res.data!='error'){
          handleClose();
          emit("refreshTab",res.data);
        }else{
          ElMessage('保存失败,请联系管理员');
        }
      })
    }
  })
}

</script>
