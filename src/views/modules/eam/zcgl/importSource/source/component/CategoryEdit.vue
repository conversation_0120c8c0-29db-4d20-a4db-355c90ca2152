<template>
  <el-dialog v-model="drawerVisible" title="导入类别维护" @close="handleClose" append-to-body v-if="drawerVisible" :close-on-click-modal="false">
    <el-form :model="categoryForm" ref="cateFormRef" :label-width="120">
      <el-form-item label="资产类别"  prop="categoryId" :rules="{ required: true, message: '请选择资产类别', trigger: 'change' }">
        <el-select v-model="categoryForm.categoryId" placeholder="请选择资产类别" @change="initPropertyList" filterable clearable :disabled="type=='edit'">
          <el-option v-for="item in categoryList" :label="item.label" :value="item.value" :key="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用导入主键" required prop="isImportUnique" :rules="{ required: true, message: '请选择是否启用导入主键', trigger: 'change' }">
        <el-select v-model="categoryForm.isImportUnique" placeholder="请选择是否启用导入主键"  filterable clearable>
          <el-option label="是" :value="'0'" />
          <el-option label="否" :value="'1'" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="categoryForm.isImportUnique==='0'" label="主键" prop="importPrimary" :rules="{ required: true, message: '请选择主键', trigger: 'change' }">
        <el-select v-model="categoryForm.importPrimary" placeholder="请选择主键" multiple clearable filterable>
          <el-option v-for="item in propertyList" :label="item.name" :value="item.code" :key="item.code" />
        </el-select>
      </el-form-item>
      <el-form-item label="导入规则" required prop="importType" :rules="{ required: true, message: '请选择导入规则', trigger: 'change' }">
        <el-select v-model="categoryForm.importType" placeholder="请选择导入规则"  filterable clearable>
          <el-option label="新增并更新" :value="'0'" />
          <el-option label="仅新增" :value="'1'" />
          <el-option label="仅更新" :value="'2'" />
        </el-select>
      </el-form-item>
      <el-form-item label="类别映射字段" required prop="categoryMappingField" :rules="{ required: true, message: '请输入类别映射字段', trigger: 'blur' }">
        <el-input type="text" v-model="categoryForm.categoryMappingField"></el-input>
      </el-form-item>
      <el-form-item label="类别映射值" required prop="categoryMappingValue" :rules="{ required: true, message: '请输入类别映射值', trigger: 'blur' }">
        <el-input type="text" v-model="categoryForm.categoryMappingValue" placeholder="多值使用英文逗号分隔"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSourceCategory(cateFormRef)">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref, toRefs,watch,onMounted} from 'vue'
import {
  saveImportSourceCategory,queryImportCategory,queryImportProperty
} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'

const emit = defineEmits(["update:visible","refreshTab"]);
const state = reactive({
  drawerVisible: false,
  categoryForm:{
    categoryId:"",
    isImportUnique:"0",
    importPrimary:[],
    importType:"",
    categoryMappingField:"",
    categoryMappingValue:"",
    importCategoryId:"",
    sourceId:""
  },
  categoryList:[],
  propertyList:[],
})
const props = defineProps({
  visible: {
    type: Boolean
  },
  categoryForm : {
    type: Object
  },
  sourceType:{
    type: String
  },
  type:{
    type: String
  },
});
const {drawerVisible,categoryForm,categoryList,propertyList} = toRefs(state);
const cateFormRef = ref<FormInstance>()
const handleClose = () => {
  emit("update:visible", false);
};

//监听组件属性
watch(props, async (newValue: any) => {
  state.categoryForm = newValue.categoryForm;
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  if (state.drawerVisible&&state.categoryForm.categoryId){
    initPropertyList(state.categoryForm.categoryId);
  }
});
const saveSourceCategory = (formEl: FormInstance | undefined) =>{
  let paramForm = {
    categoryId:"",
    isImportUnique:"",
    importType:"",
    categoryMappingField:"",
    categoryMappingValue:"",
    importCategoryId:"",
    sourceId:"",
    importPrimary:""
  };
  /**
   * categoryId:"",
   *     isImportUnique:"0",
   *     importPrimary:[],
   *     importType:"",
   *     categoryMappingField:"",
   *     categoryMappingValue:"",
   *     importCategoryId:"",
   *     sourceId:""
   */
  paramForm.categoryId = state.categoryForm.categoryId;
  paramForm.isImportUnique = state.categoryForm.isImportUnique;
  paramForm.importType = state.categoryForm.importType;
  paramForm.categoryMappingField = state.categoryForm.categoryMappingField;
  paramForm.categoryMappingValue = state.categoryForm.categoryMappingValue;
  paramForm.importCategoryId = state.categoryForm.importCategoryId;
  paramForm.sourceId = state.categoryForm.sourceId;
  if(paramForm.isImportUnique==='1'){
    paramForm.importPrimary = '';
  }else{
    if(state.categoryForm.importPrimary){
      paramForm.importPrimary = state.categoryForm.importPrimary.join(",");
    }
  }
  formEl.validate((valid,fields) =>{
    if(valid){
      saveImportSourceCategory(paramForm).then(res=>{
        if(res.data!='error'){
          handleClose();
          emit("refreshTab",res.data);
        }else{
          ElMessage('保存失败,请联系管理员');
        }
      })
    }
  })
}
const initPropertyList = (value) =>{
  queryImportProperty(value).then(res=>{
    state.propertyList = res.data;
  })
}
const  initCateList = () =>{
  queryImportCategory().then(res=>{
    state.categoryList = res.data;
  })
}
//挂载后初始化
onMounted(() => {
  console.log("执行tab加载")
  initCateList();
});
</script>
