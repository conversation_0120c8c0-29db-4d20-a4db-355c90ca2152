<template>
  <div>
    <div class="flex-c">
      <search-with-column
        v-model="columnCondition.value"
        v-model:fuzzy-enable="columnCondition.fuzzy"
        v-model:column-val="columnCondition.field"
        :column-options="columnSearchOptions"
        :column-select-width="90"
        @search="resetTablePageAndQuery"
        @reset="resetSearchHandler"
        class="flex-c w-full"
        input-class-name="w-1/2"
      />
    </div>
    <div style="margin-top:20px;">
      <el-tabs v-model="tabName" editable type="card" class="demo-tabs" @edit="handleTabsEdit" @tab-change="changeTab">
        <el-tab-pane  v-for="(tab, index) in editableTabs" :key="index" :name="tab.sourceId" :label="tab.sourceName">
          <template #label>
            <span @dblclick="editTab(tab)">{{ tab.sourceName }}</span>
          </template>
          <!-- 表格 -->
          <avue-crud :data="tableData" :option="tableOption" v-model:page="tablePage">
            <template #menu-left="{ size }">
                <el-button
                  :icon="useRenderIcon('EP-HelpFilled')" @click="importRuleSet(tab)"
                >入库规则</el-button>
                <el-button v-if="tab.sourceType=='2'"
                  :icon="useRenderIcon('EP-Upload')" @click="importSourceData(tab)"
                >导入</el-button>
                <el-button v-if="tableOption.column.length>0" @click="exportSourceData(tab)"
                  :icon="useRenderIcon('EP-Download')"
                >导出</el-button>
              <el-button v-if="tableOption.column.length>0" @click="showSourceResult(tab)"
                         :icon="useRenderIcon('EP-View')"
              >查看导入结果</el-button>
            </template>
            <template #oper__button="{ row }">
              <el-button type="primary" link @click="sourceBiDui(row)">数据资产比对</el-button>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </div>
    <source-edit v-model:visible="isSourceVisiable" :sourceForm="sourceForm" @refreshTab="refreshTab"></source-edit>
    <import-source v-model:visible="importSourceVisiable" :sourceInfo="sourceForm" @closeImportSource="closeImportSource" @refreshTable="importRefresh"></import-source>
    <source-info-after-before v-model:visible="showBiduiVisible" :dataList="biduiList"></source-info-after-before>
    <el-dialog title="导入结果" append-to-body v-model="resultVisible" width="500" @close="closeResult">
      <el-col :sm="12" :lg="24" v-show="successShow">
        <el-result
          icon="success"
          title="导入成功"
          :sub-title="successTitle"
        >
        </el-result>
      </el-col>
      <el-col :sm="12" :lg="24" v-show="importingResult">
        <el-result
          icon="success"
          title="数据导入中,请稍候...."
        >
        </el-result>
      </el-col>
      <el-col :sm="12" :lg="24" v-show="errorShow">
        <el-result
          icon="error"
          title="导入失败"
          :sub-title="errorTitle"
        >
        </el-result>
      </el-col>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>

import {onMounted, reactive, shallowRef, toRefs,ref} from 'vue';
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import SourceEdit from "@/views/modules/eam/zcgl/importSource/source/component/sourceEdit.vue";
import ImportSource from "@/views/modules/eam/zcgl/importSource/source/component/importSource.vue";
import {
  deleteImportSource,
  queryImportSourceList,queryImportSourceDataConditions,queryImportSourceData,exportImportSourceData,queryImportSourceResult,
  querySourceBeforeAfter
} from "@/views/modules/eam/zcgl/importSource/source/api/ZcglSourceApi";
import { ElMessage, ElMessageBox } from 'element-plus';
import SourceInfoAfterBefore from "@/views/modules/eam/zcgl/importSource/source/component/sourceInfoAfterBefore.vue";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
const state = reactive({
  tabName:"",
  searchCondition:{
    keyword:null,
    keywordVague:true
  },
  columnCondition: {
    value: null,
    field: "ipAddress",
    fuzzy: true,
    operator: "fuzzy"
  },
  editableTabs:[],
  editableTabsValue: '',
  tabIndex:0,
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  columnSearchOptions:[],
  tableData:[],
  conditionList:[],
  isSourceVisiable:false,
  importSourceVisiable:false,
  sourceForm:{
    sourceId:"",
    sourceName:"",
    sourceType:"1"
  },
  showBiduiVisible:false,
  biduiList:[]
})
const {
  tabName,
  searchCondition,
  editableTabs,
  tablePage,
  tableData,
  isSourceVisiable,
  importSourceVisiable,
  sourceForm,
  conditionList,showBiduiVisible,
  columnCondition,columnSearchOptions,
  biduiList
} = toRefs(state)
const emit = defineEmits(["jump-to", "source-select"]);
const props = defineProps({
  sourceInfo:{
    type:String,
    default:''
  }
});
const resultVisible = ref(false);
const successShow = ref(false);
const errorShow = ref(false);
const importingResult = ref(false);
const successTitle = ref('');
const errorTitle = ref('');
const closeResult = () =>{
  resultVisible.value = false;
  successShow.value = false;
  errorShow.value = false;
  successTitle.value = '';
  errorTitle.value = '';
}
const initTabColumns = () =>{
  queryImportSourceDataConditions(state.tabName).then(res=>{
    let columns = res["data"]["columns"];
    let tt = [];
    let conditions = res["data"]["conditions"];
    if(columns&&columns.length>0){
      for(let i = 0;i<columns.length;i++){
        let row = columns[i];
        row["showOverflowTooltip"] = true;
        row["width"] = "150px";
        tt.push(row);
      }
      let c = {
        prop:"oper__button",
        label:"操作",
        width:"110px",
        fixed:"right"
      };
      tt.push(c);
    }
    let cs = [];
    if(conditions&&conditions.length>0){
      for(let i = 0;i<conditions.length;i++) {
        let c = conditions[i];
        let t = {
          "label":c.name,
          "value":c.field
        }
        cs.push(t);
      }
    }
    state.columnSearchOptions = cs;
    if(cs.length>0){
      state.columnCondition.value = "";
      state.columnCondition.field = cs[0].value;
    }else{
      state.columnCondition.value = "";
      state.columnCondition.field = "ipAddress";
    }
    tableOption.column = tt;
    state.conditionList = res.data["conditions"];
  })
}
const showSourceResult = () =>{
  queryImportSourceResult(state.tabName).then(res=>{
    resultVisible.value = true;
    if(res.data.importResult=='success'){
      successShow.value = true;
      successTitle.value = "共计处理"+res.data.sourceCount+"条数据，新增"+res.data.insertCount+"条，更新"+res.data.updateCount+"条，过滤"+res.data.errorCount+"条";
    }else if(res.data.importResult=='importing'){
      importingResult.value = true;
    }else{
      errorShow.value = true;
      errorTitle.value = res.data.msg;
    }
  })
}
const exportSourceData = (tab) =>{
  let params = {};
  params.sourceId = tab.sourceId;
  params.conditions = null;
  exportImportSourceData(params);
}
const sourceBiDui = (row) =>{
  let params = {};
  params.sourceId = state.tabName;
  params.row = row;
  querySourceBeforeAfter(params).then(res=>{
    if(res.data.result=='success'){
      state.biduiList = res.data.list;
      state.showBiduiVisible = true;
    }else{
      ElMessage.error(res.data.msg);
    }

  })
}
const refreshTable = () =>{
  let params = {};
  params.sourceId = state.tabName;
  params.conditions = null;
  params.pageNum = state.tablePage.currentPage;
  params.pageSize = state.tablePage.pageSize;
  params.conditions = state.columnCondition.value ? [state.columnCondition] : [],
  queryImportSourceData(params).then(res=>{
    state.tableData = res.data.list;
    state.tablePage.total = res.data.total;
  })
}
const changeTab = (name) =>{
  state.tabName = name;
  initTabColumns();
  refreshTable();
}
const handleTabsEdit = (tabName,action) =>{
  if (action === 'add') {
    state.sourceForm.sourceId = '';
    state.sourceForm.sourceName = '';
    state.sourceForm.sourceType = '1';
    state.isSourceVisiable = true;
  }
  if (action === 'remove') {
    deleteTab(tabName);
  }
}
const importSourceData=(tab) =>{
  state.sourceForm.sourceId = tab.sourceId;
  state.sourceForm.sourceName = tab.sourceName;
  state.sourceForm.sourceType = tab.sourceType;
  state.importSourceVisiable = true;
}
const deleteTab = (tname) =>{
  ElMessageBox.confirm('如果删除，该数据源下的所有配置将被删除，是否确认删除？','提示',{
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(()=>{
    deleteImportSource(tname).then(res=>{
      if(res.data!='error'){
        refreshTab(null);
      }
    })
  }).catch(()=>{

  })

}
const tableOption = reactive({

  index:"true",
  indexLabel:"序号",
  align: "center",
  menuAlign: "center",
  maxHeight:600,
  menu:false,
  columnBtn:false,
  gridBtn:false,
  refreshBtn:false,
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  column: []
});
const refreshTab = (data) =>{
  queryImportSourceList().then(res=>{
    console.log("返回结果",res);
    state.editableTabs = res.data;
    if(data){
      state.tabName = data;
    }else{
      if(state.editableTabs.length>0){
        state.tabName = state.editableTabs[0].sourceId;
        state.sourceForm.sourceName = state.editableTabs[0].sourceName;
      }
    }
    initTabColumns();
    refreshTable();
  });
}
const closeImportSource=()=>{
  state.importSourceVisiable = false;
}
const importRuleSet = (tab) =>{
  emit("source-select", tab);
  jumpTo('sourceCategory');
}
const editTab = (tab) =>{
  state.sourceForm.sourceId = tab.sourceId;
  state.sourceForm.sourceName = tab.sourceName;
  state.sourceForm.sourceType = tab.sourceType;
  state.tabName = tab.sourceId;
  state.isSourceVisiable = true;
}
//挂载后初始化
onMounted(() => {
  if(props.sourceInfo){
    state.tabName = props.sourceInfo;
  }
  console.log("执行tab加载")
  refreshTab(state.tabName);
});
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
//重置分页后查询事件数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  refreshTable();
};
//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "",
    fuzzy: true,
    operator: "fuzzy"
  };
  state.columnCondition.field = state.columnSearchOptions[0].value;
  resetTablePageAndQuery();
};
const importRefresh = () =>{
  initTabColumns();
  refreshTable();
}
</script>
