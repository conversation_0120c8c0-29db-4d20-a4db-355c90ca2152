<template>
  <div>
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition mode="out-in" name="fade-transform">
      <!-- 动态组件，根据currentComponent的值来决定渲染哪个组件 -->
      <component
        :is="currentComponent"
        :source-info="selectedSource"
        @jump-to="comChange"
        @source-select="sourceSelectHandler"
      />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {reactive, shallowRef, toRefs} from "vue";
import ZcglSourceImportData from "@/views/modules/eam/zcgl/importSource/source/component/zcglSourceImportData.vue";
import SourceCategory from "@/views/modules/eam/zcgl/importSource/source/component/sourceCategory.vue";

// 创建两个组件的引用
const sourceImport = shallowRef(ZcglSourceImportData)
const sourceCategory = shallowRef(SourceCategory)


//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 选中的事件
  selectedSource: null,
})
const {
  currentComponent,
  selectedSource
} = toRefs(state)

// 默认显示EventDealManage组件
state.currentComponent = sourceImport;

// 根据传入的值切换组件
const comChange = (val: string) => {
  if (val == "sourceImport") {
    state.currentComponent = sourceImport;
  }

  if (val == "sourceCategory") {
    console.log(val)
    state.currentComponent = sourceCategory;
  }
};

// 处理事件选择
const sourceSelectHandler = (evt: any) => {
  state.selectedSource = evt;
}

</script>


