import {http} from "@/utils/http";
import {ServerNames} from "@/utils/http/serverNames";


const basePath = `${ServerNames.eamCoreServer}/`;

//导入数据源 - 用于新增、编辑
export type ImportSource = {
  sourceId: string;
  sourceName: string;
  sourceType: string;
};

//导入数据源类别 - 用于新增、编辑
export type ImportSourceCategory = {
  importCategoryId: string;
  sourceId: string;
  categoryId: string;
  isImportUnique: string;
  importPrimary: string;
  importType: string;
  categoryMappingField: string;
  categoryMappingValue: string;
};

//导入数据源字段 - 用于新增、编辑
export type ImportSourceField = {
  fieldId: string;
  sourceId: string;
  importCategoryId: string;
  srcField: string;
  targetField: string;
  isTableShow: string;
  sortId: string;
  updateType: string;
};
const saveImportSource = (params: any) =>{
  return http.postJson<any>(`${basePath}importBySource/saveImportSource`, params._value);
}

const deleteImportSource = (params: any) =>{
  return http.get<any,RestResult<string>>(`${basePath}importBySource/deleteImportSource?sourceId=`+params);
}

const queryImportSourceList = () =>{
  return http.get<any,RestResult<Array<ImportSource>>>(`${basePath}importBySource/queryImportSourceList`);
}

const saveImportSourceCategory = (params: any) =>{
  return http.postJson<any>(`${basePath}importBySource/saveImportSourceCategory`, params);
}

const deleteImportSourceCategory = (params: any) =>{
  return http.get<any,RestResult<string>>(`${basePath}importBySource/deleteImportSourceCategory?importCategoryId=`+params);
}

const queryImportSourceCategory = (params: any) =>{
  return http.get<any,RestResult<Array<ImportSourceCategory>>>(`${basePath}importBySource/queryImportSourceCategory?sourceId=`+params);
}

const saveImportField = (params: any) =>{
  return http.postJson<any>(`${basePath}importBySource/saveImportField`, params);
}

const queryImportField = (params) =>{
  return http.get<any,RestResult<Array<ImportSourceField>>>(`${basePath}importBySource/queryImportField?importCategoryId=`+params);
}

const queryImportCategory = () =>{
  return http.get<any,RestResult<Array<Object>>>(`${basePath}category/queryChildCategorySelect`)
}

const queryImportProperty = (param) =>{
  return http.get<any,RestResult<Array<Object>>>(`${basePath}importBySource/queryPropertyListByCategory?categoryId=`+param);
}
const initImportPropData = (params) =>{
  return http.postJson<any>(`${basePath}importBySource/initImportPropData`, params);
}
const queryImportExcelTitle = (params) =>{
  return http.post<any,RestResult<Array<String>>>(`${basePath}importBySource/queryImportExcelTitle`,params);
}
const queryImportSourceDataConditions = (params) =>{
  return http.get<any,RestResult<Object>>(`${basePath}importSourceData/queryImportSourceDataConditions?sourceId=`+params);
}
const queryImportSourceData = (params) =>{
  return http.postJson<any>(`${basePath}importSourceData/queryImportSourceData`,params);
}
const exportImportSourceData = (params) =>{
  return http.postBlobWithJson(`${basePath}importSourceData/exportImportSourceData`,params)
}
const queryImportSourceResult = (params) =>{
  return http.get<any,RestResult<Object>>(`${basePath}importSourceData/queryImportSourceResult?sourceId=`+params);
}

const querySourceBeforeAfter = (params) =>{
  return http.postJson<any>(`${basePath}importSourceData/querySourceBeforeAfter`,params);
}
export {saveImportSource,deleteImportSource,queryImportSourceList,saveImportSourceCategory,deleteImportSourceCategory,queryImportSourceCategory,saveImportField,queryImportField,
  queryImportCategory,queryImportProperty,initImportPropData,queryImportExcelTitle,queryImportSourceDataConditions,queryImportSourceData,exportImportSourceData,
  queryImportSourceResult,querySourceBeforeAfter}
