.eam-instance-asset-info {
  .group-divider {
    height: 1px;
    background-color: var(--el-border-color);
    margin: 10px 10px 18px 10px;
  }
  .property-group-card {
    margin: 0.8rem 0 1.2rem 0;
    &.edit {
      margin: 0.8rem 0 1.2rem 16px;
    }
    .property-group {
      display: flex;
      align-items: center;
      gap: 4px;
      .group-img {
        width: 14px;
        height: 12px;
      }
      .group-name {
        font-family: PingFang SC, PingFang SC;
        font-weight: bold;
        font-size: 16px;
        color: #3C4A54;
        line-height: 16px;
      }
    }
  }
  .group-items-wrap {
    &.view {
      padding: 0 10px;
    }
     .el-form-item {
       .el-form-item__label {
         width: fit-content !important;
         padding: 0!important;
         > div {
           font-family: PingFang SC, PingFang SC;
           font-weight: 400 !important;
           color: #6F6E89;
           font-size: 14px;
         }
       }
       .el-form-item__content {
         font-family: PingFang SC, PingFang SC;
         font-weight: 500;
         font-size: 14px;
         color: #3C4A54;
         line-height: 14px;
         padding-right: 20px;
         > span {
           white-space: nowrap;
           overflow: hidden;
           text-overflow: ellipsis;
         }
         > div {
           white-space: nowrap;
           overflow: hidden;
           text-overflow: ellipsis;
         }
       }
     }
  }
  .change-time-item {
    .text-item {
      line-height: 22px !important;
    }
  }

  .base-tabs {
    .el-tabs__item.is-active {
      background-color: var(--el-color-primary-light-9) !important;
    }
  }
}
.dark {
  .eam-instance-asset-info {
    .group-name {
      color: #fff !important;
    }
    .group-items-wrap.view {
      .el-form-item {
        .el-form-item__label {
          > div {
            color: rgba(255, 255, 255, 0.8);
          }
        }
        .el-form-item__content {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #fff;
          line-height: 14px;
        }
      }
    }
  }
}
