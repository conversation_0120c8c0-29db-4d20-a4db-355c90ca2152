<template>
  <el-main>
    <div class="flex-c">
      <el-input v-model="filterTemplateColumn" placeholder="过滤值" class="w-1/2"></el-input>
      <el-button type="primary" class="ml-3" @click="changeFilter(filterTemplateColumn)">查询</el-button>
      <el-button type="primary" @click="resetFilter">重置</el-button>
    </div>
    <el-row style="margin-top:10px;">
      <div  style="text-align: center;width:50%;margin:10px auto;">
        <el-row>
          <el-col :span="11">
            是否开启IP掩码查询：
            <el-switch v-model="ip_mask_state_save"
                       active-text="是"
                       inactive-text="否"
                       active-value="open"
                       inactive-value="close"
                       @change="changeSwitch"></el-switch>
          </el-col>
          <el-col :span="11">
            是否开启IP地址段查询：
            <el-switch v-model="ip_seg_state_save"
                       active-text="是"
                       inactive-text="否"
                       active-value="open"
                       inactive-value="close"
                       @change="changeSwitch1"></el-switch>
          </el-col>
        </el-row>
      </div>
    </el-row>
    <div>
      <el-checkbox-group v-model="checkCondition"
                         style="overflow:hidden;"
                         :min='1'>
          <el-checkbox v-for="item in conditionList1" :label="item.value"
                       :key="item.value"><span style="width:20px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;" :title="item.label">{{ item.label }}</span></el-checkbox>
      </el-checkbox-group>
    </div>
  </el-main>
</template>

<script setup lang="ts">
  import {
    getIsQueryIpAxios,
    propertyExistsAxios
  } from "@/views/modules/eam/zcgl/instance/source/api/attrsortgroupModelInterface";
  import {
    queryConditionByCategoryUserAxios,
    queryPropByCateAxios,
    saveConditionByCategoryUserAxios
  } from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
  import {onMounted, reactive, toRefs} from "vue";
  import {ElMessage} from "element-plus";

  const state = reactive({
    checkCondition:[],
    filterTemplateColumn:'',
    conditionList:[],
    conditionList1:[],
    ip_mask_state_save:"",
    ip_seg_state_save:"",
    conditionSelect:[]
  })
  const {filterTemplateColumn,ip_mask_state_save,ip_seg_state_save,checkCondition,conditionList1} = toRefs(state);
  const props = defineProps({
    categoryId:{
      type:String||Number
    }
  })
  const init = () =>{
    let params = {
      categoryId: props.categoryId,
      isAuth: true
    };
    queryPropByCateAxios(params).then(res => {
      state.conditionList = res["data"];
      state.conditionList1 = res["data"];
      state.checkCondition = [];
      queryConditionByCategoryUserAxios(params).then(res1 => {
        if (res1["data"]) {
          for (let i = 0; i < res1["data"].length; i++) {
            state.checkCondition.push(parseInt(res1["data"][i]));
          }
        }
        //current.checkCondition=res1.data
      });
    });
    getIsQueryIpAxios(params).then(res => {
      state.ip_mask_state_save = res["data"].ip_mask_state;
      state.ip_seg_state_save = res["data"].ip_seg_state;
    });
  }
  const changeSwitch = (value) => {
    if (value == "open") {
      propertyExistsAxios(props.categoryId).then(res => {
        if (res.data == "success") {
          state.ip_mask_state_save = "open";
        } else {
          ElMessage.error("未添加IP值属性，无法开启");
          changeSwitch("close");
          state.ip_mask_state_save = "close";
        }
      });
    }
  }
  const changeSwitch1 = (value) =>{
    if (value == "open") {
      propertyExistsAxios(props.categoryId).then(res => {
        if (res.data == "success") {
          state.ip_seg_state_save = "open";
        } else {
          ElMessage.error("未添加IP值属性，无法开启");
          changeSwitch1("close");
          state.ip_seg_state_save = "close";
        }
      });
    }
  }
  const emit = defineEmits(["cancalCondition"]);
  const saveCondition = () =>{
    state.conditionSelect = [];
    let resultList = [];
    if (state.checkCondition.length == 0) {
      ElMessage.error("请最少勾选一个属性作为查询条件");
      return;
    }
    for (let i = 0; i < state.conditionList.length; i++) {
      let pp = {
        property_id: state.conditionList[i].value,
        category_id: props.categoryId
      };
      if (state.checkCondition.indexOf(state.conditionList[i].value) >= 0) {
        pp["conditions"] = 1;
      } else {
        pp["conditions"] = 0;
      }
      resultList.push(pp);
    }
    var params = {
      data: resultList,
      categoryId: props.categoryId,
      ip_seg_state: state.ip_seg_state_save,
      ip_mask_state: state.ip_mask_state_save
    };
    saveConditionByCategoryUserAxios(params)
      .then(res => {
        if (res["data"] == "success") {
          ElMessage.success("保存成功");
          emit("cancalCondition");
        } else {
          ElMessage.error(res["data"]);
        }
      })
      .catch(exp => {
        ElMessage.error(exp.message);
      });
  }
  const cancalCondition = () =>{
    emit("cancalCondition");
  }
  const changeFilter = (value) =>{
    console.log(value);
    state.conditionList1= state.conditionList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      },
    );
  }
  const resetFilter = () =>{
    state.filterTemplateColumn = '';
    changeFilter(state.filterTemplateColumn);
  }
  defineExpose({
    saveCondition,cancalCondition
  })
  onMounted(()=>{
    init();
  })
</script>
<style lang="scss" scoped>
:deep(.el-checkbox){
  width: 200px; /* 定义checkbox的宽度 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
