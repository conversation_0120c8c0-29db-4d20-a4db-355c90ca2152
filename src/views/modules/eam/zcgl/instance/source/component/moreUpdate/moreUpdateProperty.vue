<template>
  <el-main v-loading="saveLoading" class="moreUpdateProperty">
    <div style="max-height:600px;overflow:scroll">
      <el-card>

        <el-form :model="propertyForm" label-width="120px" ref="propertyForm">
          <el-row>
            <template v-for="item in updateList">
              <el-col v-if="item.field.isEdit == 1" :span="12" >
                <el-form-item :prop="item.field.code"
                  :label="item.field.propertyUnit != null && item.field.propertyUnit != '' ? item.field.name + '(' + item.field.propertyUnit + ')：' : item.field.name"
                  :title="item.field.propertyUnit != null && item.field.propertyUnit != '' ? item.field.name + '(' + item.field.propertyUnit + ')：' : item.field.name">
                  <!--数字-->
                  <span style="width: 100%;" v-if="item.field.showType == 'number' && item.field.isAuto != 1">
                    <span v-if="item.field.dataLength != null">
                      <el-input-number v-model="item.field.value" :max="item.field.dataLength"></el-input-number>
                    </span>
                    <span v-if="item.field.dataLength == null">
                      <el-input-number v-model="item.field.value"></el-input-number>
                    </span>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'inputSelect'">
                    <el-input-tag v-model="item.field.value" @rulesInput="rulesInput(item, item.code)"></el-input-tag>
                  </span>
                  <!--普通输入框 input （string，number）-->
                  <span style="width: 100%;" v-if="item.field.showType == 'input' && item.field.isAuto != 1">
                    <!--数据类型 input、number、text、select、checkbox、radio-->
                    <span v-if="item.field.dataType == 'string'">
                      <span v-if="item.field.dataLength != null">
                        <el-input v-model="item.field.value" :maxlength="item.field.dataLength"
                          :placeholder="item.field.name"></el-input>
                      </span>
                      <span v-if="item.field.dataLength == null">
                        <el-input v-model="item.field.value" :placeholder="item.field.name"></el-input>
                      </span>
                    </span>
                    <span v-if="item.field.dataType == 'long'">
                      <span v-if="item.field.dataLength != null">
                        <el-input-number v-model="item.field.value" :max="item.field.dataLength"></el-input-number>
                      </span>
                      <span v-if="item.field.dataLength == null">
                        <el-input-number v-model="item.field.value"></el-input-number>
                      </span>
                    </span>

                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'windowboxTree'">
                    <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                      class="ellipsis" :placeholder="item.field.name">
                      <el-button slot="prepend" icon="el-icon-search" @click="openWindowTree(item)"></el-button>
                      <el-button slot="append" icon="el-icon-close" @click="clearWindowTree(item)"></el-button>
                    </el-input>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'windowboxFilter'">
                    <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-search" @click="openFilterSelect(item)"></el-button>
                      </div>
                      <div style="display: table-cell;">
                        <el-select style="width:100%" v-model="item.field.value" multiple filterable remote
                          reserve-keyword placeholder="请输入关键词" @focus="(val) => clearTableList(val, item)"
                          :remote-method="(vul) => remoteMethod(vul, item)">
                          <el-option v-for="item1 in item.field.tableList" :key="item1.value" :label="item1.label"
                            :value="item1.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-close" v-if="item.field.isShowClear == '1' ? true : false"
                          @click="clearSelect(item)"></el-button>
                      </div>
                    </div>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'windowboxTreeFilter'">
                    <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-search" @click="openWindowTree(item)"></el-button>
                      </div>
                      <div style="display: table-cell;">
                        <el-select v-model="item.field.value" multiple filterable remote reserve-keyword
                          placeholder="请输入关键词" @focus="(val) => clearTreeTableList(val, item)"
                          :remote-method="(vul) => remoteTreeMethod(vul, item)">
                          <el-option v-for="item1 in item.field.tableList" :key="item1.value" :label="item1.label"
                            :value="item1.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div v-if="item.field.isShowClear == '1' ? true : false"
                        style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-close" @click="clearWindowTree(item)"></el-button>
                      </div>

                    </div>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'email'
                    || item.field.showType == 'telephone' || item.field.showType == 'phone'
                    || item.field.showType == 'snmp_addr'">
                    <span v-if="item.field.dataLength != null">
                      <el-input v-model="item.field.value" :maxlength="item.field.dataLength"
                        :placeholder="item.field.name"></el-input>
                    </span>
                    <span v-if="item.field.dataLength == null">
                      <el-input v-model="item.field.value" :placeholder="item.field.name"></el-input>
                    </span>
                  </span>
                  <!--富文本-->
                  <span style="width: 100%;" v-if="item.field.showType == 'textarea'">
                    <!--数据类型 input、number、text、select、checkbox、radio-->
                    <span v-if="item.field.dataLength != null">
                      <el-input v-model="item.field.value" type="textarea" :maxlength="item.field.dataLength"
                        :placeholder="item.field.name"></el-input>
                    </span>
                    <span v-if="item.field.dataLength == null">
                      <el-input v-model="item.field.value" type="textarea" :placeholder="item.field.name"></el-input>
                    </span>
                  </span>
                  <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                  <span style="width: 100%;" v-if="item.field.showType == 'dateTime'">
                    <el-date-picker v-model="item.field.value" type="datetime" format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
                  </span>
                  <!--日期输入框 yyyy-MM-dd-->
                  <span style="width: 100%;" v-if="item.field.showType == 'date'">
                    <el-date-picker v-model="item.field.value" type="date" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"></el-date-picker>
                  </span>

                  <span v-if="item.field.showType == 'checkbox'">
                    <el-checkbox-group v-model="item.field.value">
                      <el-checkbox :label="cn.value" v-for="cn in item.field.enumArray">{{ cn.label
                        }} </el-checkbox>

                    </el-checkbox-group>
                  </span>
                  <!-- 复选下拉-->
                  <span style="width: 100%;" v-if="item.field.showType == 'mul_combobox'">
                    <el-select placement="top" filterable clearable multiple="true" :placeholder="'请选择'"
                      v-model="item.field.value">
                      <el-option v-for="en in item.field.enumArray" :value="en.value" :label="en.label"></el-option>
                    </el-select>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'comboTree'">
                    <span>
                      <!-- <im-select-tree :placeholder="'请选择'" clearable enable-scroll :scroll-count="20" filter-on-input
                      :model="item.field.enumArray" children-key="children" fixed-position parent-key="parentId"
                      v-model="item.field.value" :collapse-level="1" id-key="id" style="width: 100%" title-key="title">
                    </im-select-tree> -->
                      <el-tree-select :props="defaultProps" children-key="children" node-key="id" node-value="id"
                        id-key="id" title-key="title" clearable :placeholder="'请选择'" v-model="item.field.value"
                        :data="item.field.enumArray" check-strictly :render-after-expand="false" style="width: 90%" />
                    </span>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'mul_windowbox'">
                    <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                      class="ellipsis" :placeholder="item.field.name">
                      <el-button slot="prepend" icon="el-icon-search" @click="openMulSelect(item)"></el-button>
                      <el-button slot="append" v-if="item.field.isShowClear == '1'" icon="el-icon-close"
                        @click="clearMulSelect(item)"></el-button>
                    </el-input>
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'objectTable'">
                    <span>
                      <object-table-property v-if="otpShow" :ref="item.field.code" :categoryIdP="categoryIdP"
                        :proOcode="item.field.ocode" :type="type" :tableData.sync="item.field.value"
                        :tableColumn="item.field.tableList"></object-table-property>
                    </span>
                  </span>
                  <!--单选-->
                  <span style="width: 100%;" v-if="item.field.showType == 'radio'">
                    <el-radio-group v-model="item.field.value">
                      <el-radio :value="cn.value" :key="cn.value" v-for="cn in item.field.enumArray">
                        {{ cn.label }}
                      </el-radio>
                    </el-radio-group>
                  </span>

                  <span style="width: 100%;" v-if="item.field.isCascade == 1 && item.field.activeProp == ''">
                    <!--主动级联，且不被别的属性关联-->
                    <!-- 下拉选 -->
                    <span style="width: 100% !important;" v-if="item.field.showType == 'combobox'">
                      <el-select style="width: 100% !important;" placement="top" filterable v-model="item.field.value"
                        :placeholder="'请选择'" @change="selectActiveCase(item)" clearable>
                        <el-option v-for="en in item.field.enumArray" :value="en.value" :label="en.label"></el-option>
                      </el-select>
                    </span>
                    <span v-if="item.field.showType == 'windowbox'">
                      <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                        class="ellipsis" :placeholder="item.field.name">
                        <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button>
                        <el-button slot="append" v-if="item.field.isShowClear == '1'" icon="el-icon-close"
                          @click="clearSelect(item)"></el-button>
                      </el-input>

                    </span>
                    <span v-if="item.field.showType == 'cascader'">
                      <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="'请选择'"
                        @change="selectActiveCase(item)" clearable></el-cascader>
                    </span>
                  </span>

                  <span style="width: 100%;" v-if="item.field.isCascade == 1 && item.field.activeProp != ''">
                    <!--主动级联，且被别的属性关联-->
                    <!-- 下拉选 -->
                    <span v-if="item.field.showType == 'combobox'">
                      <el-select placement="top" filterable v-model="item.field.value" :placeholder="'请选择'"
                        @change="selectActiveCase(item)" @visible-change="val => { openBeSelect(val, item) }" clearable>
                        <el-option v-for="en in item.field.enumArray" :value="en.value" :label="en.label"></el-option>
                      </el-select>
                    </span>
                    <span v-if="item.field.showType == 'windowbox'">
                      <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                        class="ellipsis" :placeholder="item.field.name">
                        <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button>
                        <el-button slot="append" v-if="item.field.isShowClear == '1'" icon="el-icon-close"
                          @click="clearSelect(item)"></el-button>
                      </el-input>
                    </span>
                    <span v-if="item.field.showType == 'cascader'">
                      <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="'请选择'"
                        @change="selectActiveCase(item)" @visible-change="val => { openBeSelect(val, item) }"
                        clearable></el-cascader>
                    </span>
                  </span>

                  <span style="width: 100%;" v-if="item.field.isCascade == 0 && item.field.activeProp == ''">
                    <!--不是主动级联,也不被级联的-->
                    <!-- 下拉选 -->
                    <span v-if="item.field.showType == 'combobox'">
                      <el-select placement="top" filterable :placeholder="'请选择'" v-model="item.field.value" clearable>
                        <el-option v-for="en in item.field.enumArray" :value="en.value" :label="en.label"></el-option>
                      </el-select>
                    </span>
                    <span v-if="item.field.showType == 'windowbox'">
                      <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                        class="ellipsis" :placeholder="item.field.name">
                        <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button>
                        <el-button slot="append" v-if="item.field.isShowClear == '1'" icon="el-icon-close"
                          @click="clearSelect(item)"></el-button>
                      </el-input>

                    </span>
                    <span v-if="item.field.showType == 'cascader'">
                      <el-cascader :data="item.field.enumArray" :placeholder="'请选择'" v-model="item.field.value"
                        clearable></el-cascader>
                    </span>
                  </span>
                  <span style="width: 100%;" v-if="item.field.isCascade == 0 && item.field.activeProp != ''">
                    <!--不是主动级联,但是被级联的-->
                    <!-- 下拉选 -->
                    <span v-if="item.field.showType == 'combobox'">
                      <el-select placement="top" filterable v-model="item.field.value" :placeholder="'请选择'"
                        @visible-change="val => { openBeSelect(val, item) }" clearable>
                        <el-option v-for="en in item.field.enumArray" :value="en.value" :label="en.label"></el-option>
                      </el-select>
                    </span>
                    <span v-if="item.field.showType == 'windowbox'">
                      <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                        class="ellipsis" :placeholder="item.field.name">
                        <el-button slot="prepend" icon="el-icon-search" @click="openBeCaseWindow(item)"></el-button>
                        <el-button slot="append" v-if="item.field.isShowClear == '1'" icon="el-icon-close"
                          @click="clearSelect(item)"></el-button>
                      </el-input>
                    </span>
                    <span v-if="item.field.showType == 'cascader'">
                      <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="'请选择'"
                        @visible-change="val => { openBeSelect(val, item) }" clearable></el-cascader>
                    </span>
                  </span>
                  <span
                    v-if="item.field.titleDesc != null && item.field.titleDesc != undefined && item.field.titleDesc != '' && item.field.titleDesc != 'null'"
                    style="position: absolute;top:0px;left:-18px;" :title="item.field.titleDesc">
                    <i type="ios-help-circle-outline" size="15" />
                  </span>
                  <span style="width: 100%;" v-if="item.field.showType == 'table'">
                    <div style="
                              overflow: hidden;
                              display: flex;
                              align-items: center;
                            ">
                      <el-upload action="/rest/eam-core/zcgl-common/upload/upload" ref="upload" :disabled="type == 'add' || type == 'edit' ? false : true
                        " :on-success="(res, file) => {
                        handleSuccess(res, file, item);
                      }
                        " :show-file-list="false" :format="[
                          'pdf',
                          'txt',
                          'doc',
                          'docx',
                          'xls',
                          'xlsx',
                          'jpeg',
                          'jpg',
                          'png'
                        ]" :on-format-error="handleFormatError" :max-size="50000" style="float: left"
                        :on-exceeded-size="handleMaxSize">
                        <el-button @click="() => {
                          item.span = 24;
                          item.field.hide = true;
                        }
                          " size="small" type="primary">附件上传</el-button>
                      </el-upload>
                      <i class="el-icon-circle-plus-outline" v-if="!item.field.hide" style="
                                font-size: 24px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 24;
                                item.field.hide = !item.field.hide;
                              }
                                ">展开</i>
                      <i class="el-icon-remove-outline" v-else style="
                                font-size: 24px;
                                line-height: 24px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 12;
                                item.field.hide = !item.field.hide;
                              }
                                ">收起</i>
                    </div>
                    <div v-if="item.field.hide" style="margin-top: 10px; position: relative">
                      <el-table border max-height="200" :data="item.field.tableList">
                        <el-table-column type="index" label="序号" width="80px" align="center">
                        </el-table-column>
                        <el-table-column label="文件大小" align="center" prop="fileSize">
                        </el-table-column>
                        <el-table-column label="附件名称" align="center" prop="fileName">
                        </el-table-column>
                        <el-table-column label="操作" align="center" prop="action" width="150">
                          <template #default="scope">
                            <el-button size="small" @click="
                              download(
                                row.fullPath,
                                row.fileName
                              )
                              ">下载</el-button>
                            <el-popconfirm @confirm="deleteUploadData(item, row)" title="确认要删除吗?">
                              <template #reference>
                                <el-button size="small" :disabled="type == 'add' || type == 'edit'
                                  ? false
                                  : true
                                  ">删除</el-button>
                              </template>
                            </el-popconfirm>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </span>
                </el-form-item>
              </el-col>
            </template>

          </el-row>
        </el-form>

      </el-card>
      <el-card style="width: 100%;">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="选择资产详情" name="1">
            <im-table ref="propTable" max-height="400px" sortable :columns="tableColumns" :data="tableData" border
              id-key="_id" stripe>
            </im-table>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>
    <div style="display: flex;justify-content: center; margin-top:20px;">
      <el-button type="primary" @click="saveMoreUpdate">保存</el-button>
      <el-button type="primary" @click="closeMoreUpdate">取消</el-button>
    </div>
    <el-dialog :visible.sync="windowTreeDialog" :modal="false" fullscreen append-to-body destroy-on-close
      style="z-index:99999;">
      <windowBoxTree :item="treeItem" v-if="windowTreeDialog" @closeWindowTree="closeWindowTree"></windowBoxTree>
    </el-dialog>
  </el-main>
</template>
<script lang="ts" setup>
import { initMoreUpdatePropertyAxios, saveMoreUpdatePropertyAxios } from "@/views/modules/eam/zcgl/instance/source/api/batchModificationInterface";
// import windowBoxTree from "../windowBoxTree";
import { reactive, ref, toRefs, watch, onMounted, onUnmounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const updateList = ref([]);
const tableColumns = ref([]);
const tableData = ref([]);
const propertyForm = ref([]);
const activeNames = ref(['1']);
const treeItem = ref({});
const windowTreeDialog = ref(false);
const windoxboxSearch = reactive({});
const currentItem = reactive({});
const saveLoading = ref(false);

const props = defineProps({
  ids: {
    type: Array || String
  }
})

function buildTree(
  data: any[],
  deptId: string | number,
  parentId: string | number
) {
  // 创建一个映射，用于快速查找节点
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, children: [] });
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
}

const defaultProps = {
  children: "children",
  label: "title"
};

const init = () => {
  const params = {};
  if (typeof props.ids != 'string') {
    params['ids'] = props.ids.join(",");
  } else {
    params['ids'] = props.ids;
  }

  initMoreUpdatePropertyAxios(params).then(res => {
    updateList.value = res.data.updateList;
    updateList.value.forEach(item => {
      if (item['field']['showType'] == 'comboTree') {
        item['field']['enumArray'] = [...buildTree(item['field']['enumArray'], "id", "parentId")]
      }
    })
    tableData.value = res.data.tableData;
    tableColumns.value = res.data.tableColumns;
  }).catch(exp => {
    ElMessage.error("初始化属性失败，" + exp.message);
  })
}

const emits = defineEmits(['closeMoreUpdate', 'clearSelection', 'refreshTable']);
const closeMoreUpdate = () => {
  emits("closeMoreUpdate");
}

const saveMoreUpdate = () => {
  const params = {};
  if (typeof props.ids != 'string') {
    params['ids'] = props.ids.join(",");
  } else {
    params['ids'] = props.ids;
  }
  params['dataForm'] = propertyForm.value;
  params['updateList'] = updateList.value;
  saveLoading.value = true;

  saveMoreUpdatePropertyAxios(params).then(res => {
    if (res.data.result == 'success') {
      ElMessage.success("保存成功");
      closeMoreUpdate();
      emits("clearSelection");
      emits("refreshTable");
      saveLoading.value = false;
    } else {
      saveLoading.value = false;
      ElMessage.error(res.data.message);
    }
  }).catch(exp => {
    saveLoading.value = false;
    ElMessage.error("保存失败，" + exp.message);
  })
}

const openWindowTree = (item) => {
  let tt = "";
  if (item.field.showType == "windowboxTreeFilter") {
    if (item.field.value != null && item.field.value.length > 0) {
      for (var i = 0; i < item.field.value.length; i++) {
        if (i == item.field.value.length - 1) {
          tt += item.field.value[i];
        } else {
          tt += item.field.value[i];
        }
      }
      item.field.showValue = tt;
    }
  }
  treeItem.value = {};
  treeItem.value = item;
  windowTreeDialog.value = true;
}

const closeWindowTree = (item) => {
  windowTreeDialog.value = false;
  treeItem.value = item;
}

const clearWindowTree = (item) => {
  item.field.showValue = "";
  item.field.value = "";
  windowTreeDialog.value = true;
  windowTreeDialog.value = false;
}

const clearTreeTableList = (event, item) => {
  let query = "";
  if (
    windoxboxSearch[item.field.code + "windowboxTree"] != undefined &&
    windoxboxSearch[item.field.code + "windowboxTree"] != null
  ) {
    query = windoxboxSearch[item.field.code + "windowboxTree"];
  }
  item.field.tableList = [];
  remoteTreeMethod(query, item);
}

const clearTableList = (event, item) => {
  let query = "";
  if (
    windoxboxSearch[item.field.code + "windowbox"] != undefined &&
    windoxboxSearch[item.field.code + "windowbox"] != null
  ) {
    query = windoxboxSearch[item.field.code + "windowbox"];
  }
  item.field.tableList = [];
  remoteMethod(query, item);
}

const remoteTreeMethod = (query, item) => {
  windoxboxSearch[item.field.code + "windowboxTree"] = query;
  if (query !== "") {
    item.field.tableList = item.field.enumArray[0].enumList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
      },
    );
  } else {
    item.field.tableList = [];
  }
}

const remoteMethod = (query, item) => {
  windoxboxSearch[item.field.code + "windowbox"] = query;
  if (query !== "") {
    item.field.tableList = item.field.enumArray.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    item.field.tableList = [];
  }
}

const openSelect = (item) => {
  if (item.field.activeProp != "") {
    //如果是被级联的的
    // openBeCaseWindow(item);
  } else {
    // getObjectPage(item);
  }
}

const clearSelect = (item) => {
  item.field.value = "";
  item.field.showValue = "";
  currentItem['field'].value = "";
  currentItem['field'].showValue = "";
  console.info("value " + currentItem['field'].value);
  if (currentItem['field'].isCascade === 1) {
    //如果是主动发起的需要更新缓存
    // updateActiveCase(currentItem);
  }
  // closePropSelect();
}




onMounted(() => {
  init();
})

</script>
<style lang="scss" scoped>
.moreUpdateProperty {
  :deep(.el-loading-mask) {
    z-index: 3 !important;
  }
}
</style>
