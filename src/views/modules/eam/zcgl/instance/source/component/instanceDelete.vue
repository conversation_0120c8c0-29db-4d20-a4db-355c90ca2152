<template>
  <div class="instance">
    <div class="flex-bc">
      <el-col :span="22">
        <el-page-header @back="jumpTo('assetTableInfoPage')" class="mb-2">
          <template #content>
            <span class="mr-3 font-bold"> 资产数据回收 </span>
          </template>
        </el-page-header>
      </el-col>
    </div>

        <div ref="homeBottomLeftRef">
          <splitpane :splitSet="settingLR">
            <template #paneL>
              <div style="padding-left:5px;">
                <el-input clearable v-model="filterText" style="
              width:100%;
              height: 1.5rem;
              line-height: 2rem;
              margin-top: 0.5rem;
              margin-bottom: 0.5rem;
              font-size: 14px;
            " placeholder="请输入关键字" :suffix-icon="Search"/>
                <!-- default-expand-all -->
                <div style="overflow:auto;" v-loading="treeLoading">
                  <el-tree ref="treeRef" style="
              max-height: 50rem;
            " class="filter-tree" :data="treeData" :props="defaultProps" node-key="id" label="title"
                           :expand-on-click-node="false" :filter-node-method="filterNode" highlight-current
                           :current-node-key="currentNodeKey" :default-expanded-keys="defaultExpandedKeys"
                           @node-click="nodeClick">
                    <template #default="{ node, data }">
                      <div class="custom-tree-node" style="
                  display: flex;
                  justify-content: space-between;
                  flex-grow: 1;
                  font-size: 13px;
                  line-height: 17px;
                ">
                        <div style="display: inline-block">{{ node.label }}</div>
                      </div>
                    </template>
                  </el-tree>
                </div>
              </div>
            </template>
            <template #paneR>
              <div style="height:100%;overflow-y: auto;">
              <div>
                <el-form :inline="true" :model="searchFormInline" class="instance-form-inline" style="padding-bottom: 1rem">
                  <el-row style="padding-bottom: 1rem">
                    <el-col :span="24">
                      <div>
                        <el-row>
                          <el-col :span="17">
                            <el-form
                              ref="queryCondition"
                              :model="queryConditionForm"
                              label-width="90px"
                              @submit.native.prevent
                            >
                              <div class="flex-c">
                                <el-input v-model="queryConditionForm['allName']" :disabled="isShow" placeholder="IP地址、资产名称" class="w-1/2">
                                  <template #append>
                                    <el-button v-if="!isShow" @click="clickShow" style="font-size:12px;height:1.6rem;line-height:1.5rem;padding-top:1px" :icon="useRenderIcon('EP-CirclePlus')">展开</el-button>
                                    <el-button v-else @click="clickShow" style="font-size:12px;height:1.6rem;line-height:1.5rem;padding-top:1px" :icon="useRenderIcon('EP-Remove')">收起</el-button>
                                  </template>
                                </el-input>
                                <el-button type="primary" class="ml-3" @click="queryFunction">查询</el-button>
                                <el-button type="primary" class="ml-3" @click="resetFunction">重置</el-button>
                              </div>
                            </el-form>
                          </el-col>
                          <el-col :span="6">
                            <el-dropdown placement="bottom" @command="clickCommand" style="vertical-align: center;">
                              <el-button>更多操作</el-button>
                              <template #dropdown>
                              <el-dropdown-menu>
                                <el-dropdown-item style="padding: 0.3rem 1rem;" :disabled="selectedTableData.length == 0" command="reduInstances">
                                  批量还原
                                </el-dropdown-item>
                                <el-dropdown-item style="padding: 0.3rem 1rem;" :disabled="selectedTableData.length == 0" command="deleteInstances">
                                  批量删除
                                </el-dropdown-item>
                              </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </el-col>
                        </el-row>
                        <div v-show="isShow" style="margin-top:15px;max-height: 210px;overflow-y:auto;" class="expandDiv">
                          <el-form
                            ref="querySwithForm"
                            :model="querySwithForm"
                            label-width="120px"
                            @submit.native.prevent
                          >
                            <el-row>
                              <el-col :span="12" v-show="ip_mask_state == 'open'">
                                <el-form-item label="IP掩码">
                                  <el-input
                                    v-model.trim="querySwithForm.ipaddr"
                                    placeholder="IP地址"
                                    style="width: 16rem;"
                                  ></el-input>
                                  <span
                                    style="
                            width: 1rem;
                            display: inline-block;
                            font-weight: bolder;
                            text-align: center;
                            color: black;
                          "
                                  >/</span
                                  >
                                  <el-input
                                    v-model.trim="querySwithForm.yanma"
                                    placeholder="掩码"
                                    style="width: 6rem;"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                              <el-col :span="12" v-show="ip_seg_state == 'open'">
                                <el-form-item label="IP地址段">
                                  <el-input
                                    v-model.trim="querySwithForm.startIp"
                                    placeholder="起始IP"
                                    style="width: 10rem;"
                                  ></el-input>
                                  <span
                                    style="
                            width:3rem;
                            display: inline-block;
                            font-weight: bolder;
                            text-align: center;
                            color: black;
                          "
                                  >---</span
                                  >
                                  <el-input
                                    v-model.trim="querySwithForm.endIp"
                                    placeholder="结束IP"
                                    style="width: 10rem;"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                            </el-row>
                          </el-form>
                          <el-form
                            ref="queryCondition"
                            :model="queryConditionForm"
                            label-width="120px"
                          >
                            <el-row
                              v-for="(col, index0) in propertyForm['rows']"
                              :key="index0"
                            >
                              <el-col
                                :span="item.field.showType == 'objectTable' ? 24 : 12"
                                v-for="(item, index1) in col.colList"
                                :key="index1"
                              >
                                <!--可编辑，并且没有单位的属性-->
                                <span v-if="item.field != null && item.rule != null">
                        <el-form-item :label="item.field.name" :key="index0">
                          <span v-if="item.field.showType == 'objectTable'">
                            <query-object-table
                              :ref="(el) => getObjRef(el,item.field.code)"
                              :queryData.sync="item.field.value"
                              :queryList="item.field.tableList"
                              :ocode="item.field.ocode"
                            ></query-object-table>
                          </span>
                          <!--普通输入框 input （string，number）-->
                          <span
                            v-if="
                              item.field.showType == 'input' ||
                              item.field.showType == 'telephone' ||
                              item.field.showType == 'textarea' ||
                              item.field.showType == 'email' ||
                              item.field.showType == 'phone' ||
                              item.field.showType == 'snmp_addr'
                            "
                          >
                            <span>
                              <el-input
                                v-model.trim="item.field.value"
                                :placeholder="item.field.name"
                                :precise.sync="item.field.hide"
                              >
                              </el-input>
                            </span>
                          </span>
                          <span
                            v-if="
                              item.field.showType == 'dateTime' ||
                              item.field.showType == 'date'
                            "
                          >
                            <el-date-picker clearable v-model="item.field.value" type="datetimerange" range-separator="到" value-format="YYYY-MM-DD HH:mm:ss"
                                            format="YYYY-MM-DD HH:mm:ss"
                                            start-placeholder="开始日期" end-placeholder="结束日期" />
                          </span>
                          <span v-if="item.field.showType == 'number'">
                            <el-input-number
                              v-model.trim="item.field.value"
                            ></el-input-number>
                          </span>
                          <span v-if="item.field.showType == 'checkbox'">
                            <el-checkbox-group v-model="item.field.value">
                              <el-checkbox
                                :label="cn.value"
                                v-for="(cn, index) in item.field.enumArray"
                                :key="index"
                              >{{ cn.label }}
                              </el-checkbox>
                            </el-checkbox-group>
                          </span>
                          <span v-if="item.field.showType == 'windowboxTree'">
                            <el-input
                              v-model.trim="item.field.showValue"
                              readonly
                              clearable
                              :title="item.field.showValue"
                              class="ellipsis"
                              :placeholder="item.field.name"
                            >
                              <el-button
                                slot="prepend"
                                icon="el-icon-search"
                                @click="openWindowTree(item)"
                              ></el-button>
                              <el-button
                                slot="append"
                                icon="el-icon-close"
                                @click="clearWindowTree(item)"
                              ></el-button>
                            </el-input>
                          </span>

                          <span v-if="item.field.showType == 'windowboxFilter'">
                            <div
                              style="
                                overflow: hidden;
                                display: inline-table;
                                vertical-align: top;
                                width: 100%;
                              "
                            >
                              <div
                                style="
                                  display: table-cell;
                                  width: 44px;
                                  vertical-align: top;
                                "
                              >
                                <el-button
                                  icon="el-icon-search"
                                  @click="openFilterSelect(item)"
                                ></el-button>
                              </div>
                              <div style="display: table-cell">
                                <el-select
                                  style="width: 100%"
                                  v-model="item.field.value"
                                  popper-append-to-body
                                  multiple
                                  filterable
                                  remote
                                  reserve-keyword
                                  placeholder="请输入关键词"
                                  @focus="val => clearTableList(val, item)"
                                  :remote-method="
                                    vul => remoteMethod(vul, item)
                                  "
                                >
                                  <el-option
                                    v-for="item1 in item.field.tableList"
                                    :key="item1.value"
                                    :label="item1.label"
                                    :value="item1.value"
                                  >
                                  </el-option>
                                </el-select>
                              </div>
                              <div
                                style="
                                  display: table-cell;
                                  width: 44px;
                                  vertical-align: top;
                                "
                              >
                                <el-button
                                  icon="el-icon-close"
                                  @click="clearSelect(item)"
                                ></el-button>
                              </div>
                            </div>
                          </span>
                          <span
                            v-if="item.field.showType == 'windowboxTreeFilter'"
                          >
                            <div
                              style="
                                overflow: hidden;
                                display: inline-table;
                                vertical-align: top;
                                width: 100%;
                              "
                            >
                              <div
                                style="
                                  display: table-cell;
                                  width: 44px;
                                  vertical-align: top;
                                "
                              >
                                <el-button
                                  icon="el-icon-search"
                                  @click="openWindowTree(item)"
                                ></el-button>
                              </div>
                              <div style="display: table-cell">
                                <select-popover
                                  v-model="item.field.value"
                                  :options="item.field.tableList"
                                  @focus="val => clearTreeTableList(val, item)"
                                  @remote-method="
                                    vul => remoteTreeMethod(vul, item)
                                  "
                                ></select-popover>
                              </div>
                              <div
                                style="
                                  display: table-cell;
                                  width: 44px;
                                  vertical-align: top;
                                "
                              >
                                <el-button
                                  icon="el-icon-close"
                                  @click="clearWindowTree(item)"
                                ></el-button>
                              </div>
                            </div>
                          </span>
                          <span v-if="item.field.showType == 'inputSelect'">
                            <el-input
                              v-model="item.field.value"
                              :placeholder="item.field.name"
                              :precise.sync="item.field.hide"
                            >
                            </el-input>
                          </span>
                          <span v-if="item.field.showType == 'moreInput'">
                            <el-input
                              v-model="item.field.value"
                              :placeholder="item.field.name"
                              :precise.sync="item.field.hide"
                            >
                            </el-input>
                          </span>
                          <!--单选-->
                          <span v-if="item.field.showType == 'radio'">
                            <el-radio-group v-model="item.field.value">
                              <template
                                v-for="(cn, index) in item.field.enumArray"
                              >
                                <el-radio :value="cn.value">{{
                                    cn.label
                                  }}</el-radio>
                              </template>
                            </el-radio-group>
                          </span>
                          <span v-if="item.field.showType == 'cascader'">
                            <el-cascader
                              :options="item.field.enumArray"
                              v-model="item.field.value"
                            ></el-cascader>
                          </span>
                          <span v-if="item.field.showType == 'windowbox'">
                            <el-input
                              v-model.trim="item.field.value"
                              icon="ios-search"
                              :placeholder="item.field.name"
                            />
                          </span>
                          <span v-if="item.field.showType == 'mul_windowbox'">
                            <el-input
                              v-model.trim="item.field.value"
                              icon="ios-search"
                              :placeholder="item.field.name"
                            />
                          </span>
                          <!-- 复选下拉-->

                          <span v-if="item.field.showType == 'mul_combobox'">
                            <el-select
                              placement="top"
                              filterable
                              :multiple="true"
                              v-model="item.field.value"
                            >
                              <el-option
                                v-for="(en, index) in item.field.enumArray"
                                :value="en.value"
                                :label="en.label"
                                :key="index"
                              ></el-option>
                            </el-select>
                          </span>
                          <span v-if="item.field.showType == 'comboTree'">
                            <el-tree-select
                              v-model="item.field.value"
                              node-value="id"
                              node-key="id"
                              :data="item.field.enumArray"
                              :props="defaultProps"
                            ></el-tree-select>
                          </span>
                          <span
                            v-if="
                              item.field.isCascade == 1 &&
                              item.field.activeProp != ''
                            "
                          >
                            <!--主动级联，且被别的属性关联-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                placement="top"
                                filterable
                                :multiple="true"
                                :clearable="true"
                                style="width: 100%"
                                v-model="item.field.value"
                              >
                                <el-option
                                  v-for="(en, index) in item.field.enumArray"
                                  :key="index"
                                  :value="en.value"
                                  :label="en.label"
                                >
                                </el-option>
                              </el-select>
                            </span>
                          </span>

                          <span
                            v-if="
                              item.field.isCascade == 0 &&
                              item.field.activeProp != ''
                            "
                          >
                            <!--不是主动级联,但是被级联的-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                placement="top"
                                filterable
                                :multiple="true"
                                style="width: 100%"
                                v-model="item.field.value"
                                clearable
                              >
                                <el-option
                                  v-for="(en, index) in item.field.enumArray"
                                  :key="index"
                                  :value="en.value"
                                  :label="en.label"
                                ></el-option>
                              </el-select>
                            </span>
                          </span>

                          <span
                            v-if="
                              item.field.isCascade == 0 &&
                              item.field.activeProp == ''
                            "
                          >
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                placement="top"
                                filterable
                                :multiple="true"
                                :clearable="true"
                                style="width: 100%"
                                v-model="item.field.value"
                              >
                                <el-option
                                  v-for="(en, index) in item.field.enumArray"
                                  :value="en.value"
                                  :key="index"
                                  :label="en.label"
                                >
                                </el-option>
                              </el-select>
                            </span>
                          </span>
                        </el-form-item>
                      </span>
                              </el-col>
                            </el-row>
                          </el-form>
                        </div>
                      </div>
                    </el-col>
                    <!-- 多功能区域 -->
                  </el-row>
                </el-form>
              </div>
              <el-row style="padding-bottom: 1rem" class="centerCard">
                <span style="width: 6em;font-size: 0.9em;line-height: 1.9em;margin-left: 0.6rem;">资产标签：</span>
                <!-- {{ selectedCard + '' }} -->
                <el-check-tag :class="{ selected: selectedCard == 'all' || !selectedCard }"
                              style="cursor: pointer; display: flex;width: auto;justify-content: center;margin-right: 0.6rem;color: #fff;"
                              @click="selectCard({ value: 'all' })"><span>全部</span></el-check-tag>
                <span v-for="(card, index) in cards" :key="index">
            <el-check-tag :class="{ selected: selectedCard == card.value }"
                          style="cursor: pointer; display: flex;width: auto;justify-content: center;margin-right: 0.6rem;"
                          @click="selectCard(card)">
              <span style="white-space: nowrap;">{{ card.label }}</span>
            </el-check-tag>
        </span>
              </el-row>
              <div>
                <avue-crud :data="dataTable" :option="tableOption" v-model:page="state.pagination"
                           @size-change="handlePageSizeChange" :table-loading="tableLoading" @selection-change="chagenSelection"
                           @row-dblclick="showInfo"
                           @current-change="handlePageCurrentChange">
                  <template #operator="{ row, $index }">
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="reduInstance(row)"
                    >还原
                    </el-button>
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="deleteInstance(row)"
                    >删除
                    </el-button>
                  </template>
                </avue-crud>
              </div>
              </div>
            </template>
          </splitpane>
        </div>
    <el-dialog v-model="windowTreeDialog"
               :modal="false"
               fullscreen
               append-to-body
               destroy-on-close
               style="z-index:99999;">
      <windowBoxTree :item="treeItem"
                     v-if="windowTreeDialog"
                     @closeWindowTree="closeWindowTree"></windowBoxTree>
    </el-dialog>
    <div class="re-drawer">
      <el-drawer  v-model="dialogVisible"
                  size="50%"
                  v-if="dialogVisible"
                  @close="dialogVisible = false">
        <template #header="{ close, titleId, titleClass }">
          <div class="flex-bc">
            <el-col :span="22">
              <el-page-header @back="dialogVisible = false" class="mb-2">
                <template #content>
                  <span class="mr-3 font-bold"> 资产详情 </span>
                </template>
              </el-page-header>
            </el-col>
          </div>
        </template>
        <show-property show-type="delete" :instance-id="itemInstanceId" :category-id="itemCategoryId"></show-property>
      </el-drawer>
    </div>

  </div>
</template>
<script setup lang="ts">
import {
  initConditionMethod,
  initDeleteTableDataMethod,
  initDeleteTreeDataMethod, initTableColumsMethod, verifyReduInstanceAxios, reduInstanceAxios,
  deleteInstanceDeleteAxios, initQueryLabelMethod, queryLabelCountAxios,
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {Search} from "@element-plus/icons-vue";
import {ElMessage, ElMessageBox, ElTree} from "element-plus";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import {nextTick, onMounted, reactive, ref, toRefs} from "vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import queryObjectTable from "@/views/modules/eam/zcgl/instance/source/component/queryObjectTable.vue";
import {queryEnumList} from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
import windowBoxTree from "@/views/modules/eam/zcgl/instance/source/component/windowBoxTree.vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import ShowProperty from "@/views/modules/eam/zcgl/instance/source/component/showProperty.vue";
const settingLR: ContextProps = reactive({
  minPercent: 12,
  defaultPercent: 16,
  split: "vertical"
});
const state = reactive({
  filterText: "",
  treeData: [],
  defaultProps: {
    children: "children",
    label: "title",
  },
  currentNodeKey:'',
  defaultExpandedKeys: [],
  selectedCard: "all",
  cards: [],
  colorMap:{},
  page:{
    currentNodeId:'0',
    currentNodeName:'全部'
  },
  pagination: {
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions,
  },
  dataTable:[],
  tableLoading:false,
  treeLoading:false,
  tableOption:{
    index: false,
    indexLabel: '序号',
    align: "center",
    menuAlign: "center",
    maxHeight:500,
    selection:true,
    selectionFixed:true,
    refreshBtn:false,
    columnBtn:false,
    gridBtn:false,
    menu: false,
    border: true,
    stripe: true,
    addBtn: false,
    editBtn: false,
    delBtn: false,
    menuWidth: 130,
    column: []
  },
  isShow:false,
  loading:false,
  pageshow:false,
  queryIsShow:false,
  queryConditionForm:{},
  propertyForm:{},
  querySwithForm: {
    ipaddr: "",
    yanma: "",
    startIp: "",
    endIp: "",
  },
  fieldSort:"",
  order:"",
  searchFormInline: {
    value1: [],
    input: "",
    value2: ""
  },
  queryLoading:false,
  ip_mask_state: "close",
  ip_seg_state: "close",
  ip_mask_state_save: false,
  ip_seg_state_save: false,
  confirmStatusCombobox: null,
  individualization_gradStatus: null,
  initAssetDuty: null,
  buttonLoading: false,
  treeItem: {},
  windowTreeDialog:false,
  windowboxloading:false,
  mulData:[],
  mulCurrentItem:{},
  mulShow_type:"",
  mulOcode:"",
  currentMulItem:{},
  currentMulId:"",
  totalMulW:"",
  selectMulDataModal:false,
  propValue:"",
  object:{
    page:1,
    size:10
  },
  caseObject:[],
  currentItem:{},
  windoxboxSearch:{},
  dialogVisible:false,
  itemInstanceId: "0",
  itemCategoryId: "0",
})
const {filterText,treeLoading,treeData,defaultProps,currentNodeKey,defaultExpandedKeys,selectedCard,cards,
  dataTable,tableLoading,tableOption,isShow,queryConditionForm,propertyForm,querySwithForm,searchFormInline,
  ip_mask_state,ip_seg_state,buttonLoading,windowTreeDialog,treeItem,dialogVisible,itemInstanceId,itemCategoryId} = toRefs(state);
const treeRef = ref<InstanceType<typeof ElTree>>();
interface Tree {
  [key: string]: any;

  id: number;
  label: string;
  children?: Tree[];
}
const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

function selectCard(card) {
  state.selectedCard = card.value;
  let pararms = {
    "categoryId": state.page.currentNodeId,
    "pageNum": state.pagination.currentPage,
    "pageSize": state.pagination.pageSize,
    "isAuth": "true",
    "filterColumnMap":state.colorMap,
  }
  if(state.selectedCard != 'all'){
    pararms["scriptLabel"] = state.selectedCard;
  }
  initTableData(pararms);
}

const initTableData = (param) =>{
  param.pageNum = state.pagination.currentPage;
  param.pageSize = state.pagination.pageSize;
  if (state.isShow == false) {
    state.queryIsShow = false;
    param.all = "all";
    param.allName = state.queryConditionForm["allName"];
  } else {
    state.queryIsShow = true;
    let propertyForm = state.propertyForm["rows"];
    if (propertyForm) {
      //动态条件
      for (let x = 0; x < propertyForm.length; x++) {
        let colList = propertyForm[x].colList;
        for (let y = 0; y < colList.length; y++) {
          let col = colList[y];
          if (null != col.field) {
            let code = col.field.code;
            let value = col.field.value;
            let showType = col.field.showType;

            let code_id = code + "---" + showType;
            if(showType=='input'||showType=='inputSelect'||showType=='moreInput'){
              code_id = code + "---" + showType +"---" +col.field.hide;
            }
            if (
              null != value && code
            ) {
              if (col.field.value instanceof Array) {
                if (value[0] != "" || value[1] != "") {
                  for (let i = 0; i < value.length; i++) {
                    if (!value[i]) {
                      value[i] = "null";
                    }
                  }
                  param[code_id] = value.join(",");
                }
              } else {
                param[code_id] = value;
              }
            }
          }
        }
      }
    } else {
      param.name = state.queryConditionForm["name"];
      param.code = state.queryConditionForm["code"];
      param.manager = state.queryConditionForm["manager"];
      param.status = state.queryConditionForm["status"];
    }
    let ipaddr = state.querySwithForm.ipaddr;
    let yanma = state.querySwithForm.yanma;
    let startIp = state.querySwithForm.startIp;
    let endIp = state.querySwithForm.endIp;
    if (ipaddr != "") {
      param.query_yanma_startIp = iptolong(ipaddr);
      if (yanma != "") {
        let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
        param.query_yanma_startIp = cc.split("#")[0];
        param.query_yanma_endIp = cc.split("#")[1];
      }
    }
    if (startIp != "") {
      param.query_startIp = iptolong(startIp);
    }
    if (endIp != "") {
      param.query_endIp = iptolong(endIp);
    }
  }
  state.tableLoading = true;
  state.treeLoading = true;
  // const loading = this.$loading({
  //   lock: true
  // });
  if (state.order) {
    param.fieldSort = state.fieldSort;
    param.order = state.order;
  }
  if(state.colorMap!=null){
    param.filterColumnMap = JSON.stringify(state.colorMap);
  }
  state.pageshow=false;
  param.showLabel="2";
  initDeleteTableDataMethod(param)
    .then(res => {
      if (res["data"].pageNum) {
        state.pagination.currentPage = res["data"].pageNum;
      }

      nextTick(()=>{
        state.pageshow=true;
      })
      setTimeout(() => {
        state.dataTable = res["data"].list;
        state.pagination.total = res["data"].total;
        nextTick(() => {
          state.tableLoading = false;
          state.treeLoading = false;
        });
      }, 1500);
    })
}
const iptolong = (ip) =>{
  let num = 0;
  ip = ip.split(".");
  num =
    Number(ip[0]) * 256 * 256 * 256 +
    Number(ip[1]) * 256 * 256 +
    Number(ip[2]) * 256 +
    Number(ip[3]);
  num = num >>> 0;
  return num;
}
const subnet_mask_change_ip_segment = (ip_str,mask) =>{
  let mark_len = 32;
  mark_len = mask;
  let nextBit = Math.min(mark_len, 8);
  let ips = ip_str.split(".");
  let maskIp = {};
  maskIp["a"] = ((1 << nextBit) - 1) << (8 - nextBit);
  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["b"] = ((1 << nextBit) - 1) << (8 - nextBit);

  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["c"] = ((1 << nextBit) - 1) << (8 - nextBit);

  mark_len -= 8;
  nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
  maskIp["d"] = ((1 << nextBit) - 1) << (8 - nextBit);
  // 开始IP各个位置的值
  let a = ips[0] & maskIp["a"];
  let b = ips[1] & maskIp["b"];
  let c = ips[2] & maskIp["c"];
  let d = ips[3] & maskIp["d"];

  // 开始IP
  let startIp = a + "." + b + "." + c + "." + d;
  // 结束IP各个位置的值
  a = (maskIp["a"] ^ 255) | ips[0];
  b = (maskIp["b"] ^ 255) | ips[1];
  c = (maskIp["c"] ^ 255) | ips[2];
  d = (maskIp["d"] ^ 255) | ips[3];

  // 结束IP
  let endIp = a + "." + b + "." + c + "." + d;
  return iptolong(startIp) + "#" + iptolong(endIp);
}

const handlePageCurrentChange = (page: number) => {
  state.pagination.currentPage = page;
  initTable();
}
const handlePageSizeChange = (page: number) => {
  state.pagination.pageSize = page;
  state.pagination.currentPage = 1;
  initTable();
}
const initTable = () => {
  const json = {
    categoryId: state.page.currentNodeId,
    pageNum: state.pagination.currentPage,
    pageSize: state.pagination.pageSize,
    isAuth: "true",
  };
  if(state.selectedCard!='all'){
    json["scriptLabel"]=state.selectedCard;
  }
  initTableData(json);
}
const isChildNodes = ref(true);
const nodeClick = node => {
  // console.log(node.id);
  initQueryLabelData();
  isChildNodes.value = node.children&&node.children.length > 0;
  state.isShow = false;
  state.selectedCard = "all";
  state.page.currentNodeId = node.id;
  state.page.currentNodeName = node.name;
  state.pagination.currentPage = 1;
  state.pagination.pageSize = 20;
  let params = {
    "categoryId": state.page.currentNodeId,
    "pageNum": state.pagination.currentPage,
    "pageSize": state.pagination.pageSize,
    "isAuth": "true",
    "filterColumnMap":state.colorMap,
  }
  if(state.selectedCard!='all'){
    params["scriptLabel"] = state.selectedCard;
  }
  initTableColums(params);
};
const initTableColums = async(params) => {
  state.loading = true;
  try {
    params["showLabel"] = "2";
    initTableColumsMethod(params).then((res: any) => {
      const tmpData = [];
      state.tableOption.column.length = 0;
      res.data.forEach(item => {
        if(!item.hide){
          tmpData.push({
            resizable: item["resizable"],
            // filters: true,
            filterable: true,
            tip: item["tooltip"],
            align: item["align"],
            width: item["width"],
            disabled: item["hide"],
            label: item["title"],
            prop: item["key"],
            ellipsis: item["ellipsis"],
            overHidden: item["tooltip"],
            showOverflowTooltip:true,
            headerSlot:item['key']+"-header"
          });
        }
      });
      state.tableOption.column.push(
        ...tmpData,
        {
          label: "删除时间",
          prop: 'oper__time',
          align: 'center',
          showOverflowTooltip:true,
        },{
          label: "操作人",
          prop: 'oper__realname',
          align: 'center',
          showOverflowTooltip:true,
        },
        {
          label: "操作",
          prop: 'operator',
          align: 'center',
          width: "100px",
          fixed: "right"
        },
      );
      state.loading = false;
      initTableData(params);
    });
  } catch (error) {
    console.log(error);
    state.loading = false;
  }
};
const selectedTableData = ref([]);
const chagenSelection = (selection) =>{
  selectedTableData.value = [];
  if(selection&&selection.length>0){
    for(let i=0;i<selection.length;i++){
      selectedTableData.value.push(selection[i]["_id"]);
    }
  }
}
const clickShow = () =>{
  state.isShow = !state.isShow;
  const json = {
    categoryId: state.page.currentNodeId,
    isAuth: "true",
  };
  if (state.isShow) {
    if(!state.querySwithForm.ipaddr){
      state.querySwithForm.ipaddr = '';
    }
    if(!state.querySwithForm.yanma){
      state.querySwithForm.yanma = '';
    }
    if(!state.querySwithForm.endIp){
      state.querySwithForm.endIp = '';
    }
    if(!state.querySwithForm.startIp){
      state.querySwithForm.startIp = '';
    }
    initCondition(json);
  }
};
const buildTree = (data: any[],
                   deptId: string | number,
                   parentId: string | number) => {
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], {...item, children: []});
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
}
const initCondition = async(json) => {
  state.queryLoading = true;
  initConditionMethod(json)
      .then(res => {
        let  newProperty = []
        if(res["data"]){
          newProperty = res["data"];
          let oldProperty = state.propertyForm["rows"];
          let old = [];
          if(newProperty["rows"]){
            for(let i =0;i<newProperty["rows"].length;i++) {
              for(let ii=0;ii<newProperty["rows"][i].colList.length;ii++) {
                if (newProperty["rows"][i].colList[ii].field.showType == "comboTree") {
                  let tt = [];
                  tt.push(...buildTree(newProperty["rows"][i].colList[ii].field.enumArray, "id", "parentId"));
                  newProperty["rows"][i].colList[ii].field.enumArray = JSON.parse(JSON.stringify(tt));
                }
              }
            }
          }
          if(newProperty["rows"]&&oldProperty){
            for(let k=0;k<oldProperty.length;k++){
              for(let kk=0;kk<oldProperty[k]?.colList.length;kk++){
                if(oldProperty[k]?.colList[kk].field.value){
                  old.push(oldProperty[k]?.colList[kk]);
                }
              }
            }
            for(let i =0;i<newProperty["rows"].length;i++) {
              for(let ii=0;ii<newProperty["rows"][i].colList.length;ii++){
                for(let j=0;j<old.length;j++){
                  if(newProperty["rows"][i].colList[ii].field.code==old[j].field.code){
                    newProperty["rows"][i].colList[ii].field.value=old[j].field.value;
                    break;
                  }
                }
              }
            }
          }

        }
        state.queryLoading = false;
        state.propertyForm = newProperty;
        state.ip_mask_state = res["data"].ip_mask_state;
        state.ip_seg_state = res["data"].ip_seg_state;
      })
      .catch(exp => {
        ElMessage.error("查询条件加载失败！",exp);
        state.queryLoading = false;
      });
}
const queryFunction = () =>{
  let param = {
    categoryId: state.page.currentNodeId,
    pageNum: 1,
    pageSize: state.pagination.pageSize,
    isAuth: "true",
  };
  state.pagination.currentPage = 1;
  //this.pageSize=10;
  if (state.isShow == false) {
    state.queryIsShow = false;
    param["all"] = "all";
    param["allName"] = state.queryConditionForm["allName"];
  } else {
    state.queryIsShow = true;
    let propertyForm = state.propertyForm["rows"];
    if (null != propertyForm) {
      //动态条件
      for (let x = 0; x < propertyForm.length; x++) {
        let colList = propertyForm[x].colList;
        for (let y = 0; y < colList.length; y++) {
          let col = colList[y];
          if (null != col.field) {
            let code = col.field.code;
            let value = col.field.value;
            let showType = col.field.showType;
            let code_id = code + "---" + showType;
            if(showType=='input'||showType=='inputSelect'||showType=='moreInput'||showType=='textarea'){
              code_id = code + "---" + showType +"---" +col.field.hide;
            }
            if (
                value != undefined && null != code &&
                code != ""
            ) {
              if (col.field.value instanceof Array) {
                if (col.field.value[0] != "") {
                  param[code_id] = value.join(",");
                }
              } else {
                param[code_id] = value;
              }
            }
          }
        }
      }
    } else {
      param["name"] = state.queryConditionForm["name"];
      param["code"] = state.queryConditionForm["code"];
      param["manager"] = state.queryConditionForm["manager"];
      param["status"] = state.queryConditionForm["status"];
    }
    let ipaddr = state.querySwithForm.ipaddr;
    let yanma = state.querySwithForm.yanma;
    let startIp = state.querySwithForm.startIp;
    let endIp = state.querySwithForm.endIp;
    if (ipaddr != "") {
      param["query_yanma_startIp"] = iptolong(ipaddr);
      if (yanma != "") {
        let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
        param["query_yanma_startIp"] = cc.split("#")[0];
        param["query_yanma_endIp"] = cc.split("#")[1];
      }
    }
    if (startIp != "") {
      param["query_startIp"] = iptolong(startIp);
    }
    if (endIp != "") {
      param["query_endIp"] = iptolong(endIp);
    }
  }
  initTableData(param);
};
const reset = (json) => {
  for (let key in json) {
    json[key] = "";
  }
}
const objTableRefs = ref(new Map);
const getObjRef = (el:any,code:String) =>{
  objTableRefs.value.set("obj_"+code,el);
}
const resetFunction = () =>{
  state.confirmStatusCombobox = null;
  state.individualization_gradStatus = null;
  state.initAssetDuty = null;
  reset(state.queryConditionForm);
  state.querySwithForm.ipaddr = "";
  state.querySwithForm.yanma = "";
  state.querySwithForm.startIp = "";
  state.querySwithForm.endIp = "";
  state.colorMap = {};
  let propertyForm = state.propertyForm["rows"];
  if (null != propertyForm) {
    //动态条件
    for (let x = 0; x < propertyForm.length; x++) {
      let colList = propertyForm[x].colList;
      for (let y = 0; y < colList.length; y++) {
        let col = colList[y];
        if (null != col.field) {
          if (col.field.showType == "comboTree") {
            col.field.value = null;
          } else if (col.field.showType == "number") {
            col.field.value = undefined;
          } else if (col.field.showType=='objectTable'){
            col.field.value = "";
            let formCode = 'obj_'+col.field.code;
            objTableRefs.value.get(formCode).reset();
          }else {
            col.field.value = "";
          }
        }
      }
    }
  }
  state.querySwithForm["zoneName"] = null;
  queryFunction();
};
const reduInstances = () => {
  let params = {
    instanceIds: selectedTableData.value,
    categoryId: state.page.currentNodeId,
  };
  state.tableLoading = true;
  state.buttonLoading = true;
  verifyReduInstanceAxios(params)
      .then(res => {
        if (res["data"].result == "success") {
          reduInstanceAxios(params)
              .then(res1 => {
                if(res1["data"]=='success'){
                  ElMessage.success("还原成功");
                  requestTreeDataNewMethod();
                }else{
                  ElMessage.error(res1["msg"]);
                }
              })
              .catch(exp => {
                state.tableLoading = false;
                state.buttonLoading = false;
                ElMessage.error(exp.message);
              });
        } else {
          state.tableLoading = false;
          ElMessage.error("还原失败，" + res["data"].result);
        }
      })
      .catch(exp => {
        state.tableLoading = false;
        ElMessage.error(exp.message);
      });
}

const deleteInstances = () => {
  let params = {
    instanceIds: selectedTableData.value,
    categoryId: state.page.currentNodeId
  };
  ElMessageBox.confirm(
      "此次删除后资产数据将彻底删除无法找回，是否确认删除？",
      "是否删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
  )
      .then(() => {
        deleteInstanceDeleteAxios(params)
            .then(res1 => {
              if(res1["data"]=="success"){
                ElMessage.success("删除成功");
                requestTreeDataNewMethod();
              }else{
                ElMessage.error(res1["msg"]);
              }
            })
            .catch(exp => {
              ElMessage.error(exp.message);
            });
      })
      .catch(() => {});
}
const openWindowTree = (item) =>{
  let tt = "";
  if (item.field.showType == "windowboxTreeFilter") {
    if (item.field.value != null && item.field.value.length > 0) {
      for (let i = 0; i < item.field.value.length; i++) {
        if (i == item.field.value.length - 1) {
          tt += item.field.value[i];
        } else {
          tt += item.field.value[i];
        }
      }
      item.field.showValue = tt;
    }
  }
  state.treeItem = {};
  state.treeItem = item;
  state.windowTreeDialog = true;
}
const clearWindowTree = (item) => {
  item.field.showValue = "";
  item.field.value = "";
  state.windowTreeDialog = true;
  state.windowTreeDialog = false;
}
const windowMulTable = ref();
const openFilterSelect = (item) => {
  item.field.showValue = "";
  if (item.field.value != null && item.field.value.length > 0) {
    for (let i = 0; i < item.field.value.length; i++) {
      if (i == item.field.value.length - 1) {
        item.field.showValue += item.field.value[i];
      } else {
        item.field.showValue += item.field.value[i] + ",";
      }
    }
  }
  state.windowboxloading = true;
  state.mulCurrentItem = {};
  state.mulCurrentItem["value"] = JSON.parse(
    JSON.stringify(item.field.value),
  );
  state.mulCurrentItem["label"] = JSON.parse(
    JSON.stringify(item.field.showValue),
  );
  state.currentMulItem = item;
  state.currentMulId = item.field.value;
  state.mulOcode = item.field.ocode;
  state.mulShow_type = item.field.showType;
  let params = {
    show_type: "windowbox",
    ocode: item.field.ocode,
    pageSize: state.object.size,
    pageNum: state.object.page,
    value: state.propValue,
  }
  queryEnumList(params).then(res => {
    state.selectMulDataModal = true;
    state.mulData = res.data.data;
    state.totalMulW = res.data.total;
    state.windowboxloading = false;
    let windowMulSelects = [];

    let ttValue = null;
    let ttLabel = null;
    if (state.mulCurrentItem["value"] != null) {
      if (typeof state.mulCurrentItem["value"] == "string") {
        ttValue = state.mulCurrentItem["value"].split(",");
      } else {
        ttValue = state.mulCurrentItem["value"];
      }
      ttLabel = state.mulCurrentItem["label"].split(",");
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        let cc = {
          value: ttValue[i],
          label: ttLabel[i]
        };
        windowMulSelects.push(cc);
      }
    }
    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < state.mulData.length; i++) {
            if (state.mulData[i].value == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(
                state.mulData[i],
                true,
              );
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        state.mulCurrentItem["value"] = ttValue.join(",");
        state.mulCurrentItem["label"] = ttLabel.join(",");
      }
    });

  });
}
const clearTableList = (event, item) => {
  let query = "";
  if (
    state.windoxboxSearch[item.field.code + "windowbox"] != undefined &&
    state.windoxboxSearch[item.field.code + "windowbox"] != null
  ) {
    query = state.windoxboxSearch[item.field.code + "windowbox"];
  }
  item.field.tableList = [];
  remoteMethod(query, item);
}
const remoteMethod = (query, item)=> {
  state.windoxboxSearch[item.field.code + "windowbox"] = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    item.field.tableList = item.field.enumArray.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    item.field.tableList = [];
  }
}
const clearSelect = (item) => {
  item.field.value = "";
  item.field.showValue = "";
  state.currentItem["field"].value = "";
  state.currentItem["field"].showValue = "";
  console.info("value " + state.currentItem["field"].value);
  if (state.currentItem["field"].isCascade === 1) {
    //如果是主动发起的需要更新缓存
    updateActiveCase(state.currentItem);
  }
}
const updateActiveCase = (item) => {
  if (state.caseObject.length > 0) {
    let obj = state.caseObject.filter(p => {
      //根据当前变动的主动发起关联的属性，遍历已缓存的所有主动关联的key和value
      return p.key == item.field.code;
    });
    if (obj != null && obj.length > 0 && undefined != item.field.value) {
      //如果此属性存在，更新值
      obj[0].value = item.field.value;
    } else {
      //如果不存在，放入主动发起的缓存中
      let json = {
        key: item.field.code,
        value: item.field.value,
      };
      state.caseObject.push(json);
    }
  } else {
    let json = {
      key: item.field.code,
      value: item.field.value,
    };
    state.caseObject.push(json);
  }
}
const  clearTreeTableList = (event, item) => {
  let query = "";
  if (
    state.windoxboxSearch[item.field.code + "windowboxTree"] != undefined &&
    state.windoxboxSearch[item.field.code + "windowboxTree"] != null
  ) {
    query = state.windoxboxSearch[item.field.code + "windowboxTree"];
  }
  item.field.tableList = [];
  remoteTreeMethod(query, item);
}
const remoteTreeMethod = (query, item) => {
  state.windoxboxSearch[item.field.code + "windowboxTree"] = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    item.field.tableList = item.field.enumArray[0].enumList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
      },
    );
  } else {
    item.field.tableList = [];
  }
}
const  closeWindowTree = (item) => {
  state.windowTreeDialog = false;
  state.treeItem = item;
}
const props = defineProps({
  sourceInfo:String
})

const requestTreeDataNewMethod = (id = "") => {
  initDeleteTreeDataMethod({
    code: "category_id",
    isAuth: "true"
  }).then(res => {
    state.treeData = res["data"];
    // state.treeData.length = 0;
    // state.treeData.push(...buildTree(res["data"], "id", "parentId"));
    if(state.page.currentNodeId){
      state.currentNodeKey = state.page.currentNodeId;
    }else{
      state.currentNodeKey = state.treeData[0]["id"];
    }
    state.defaultExpandedKeys.push(state.page.currentNodeId ? state.page.currentNodeId : state.treeData[0]["id"]);
    state.page.currentNodeId = state.page.currentNodeId ? state.page.currentNodeId : state.treeData[0]["id"];
    let params = {
      "categoryId": state.page.currentNodeId,
      "pageNum": state.pagination.currentPage,
      "pageSize": state.pagination.pageSize,
      "isAuth": "true",
      "filterColumnMap":state.colorMap,
    }
    if(state.selectedCard!='all'){
      params["scriptLabel"] = state.selectedCard;
    }
    initQueryLabelData();
    initTableColums(params);
  });
};
const emit = defineEmits(["jump-to", "source-select"]);
const jumpTo = (page) =>{
  emit("source-select",state.page.currentNodeId);
  emit("jump-to",page);
}
const initQueryLabelData = () => {
  try {
    initQueryLabelMethod({
      categoryId: state.page.currentNodeId,
      isAuth: "true"
    }).then(res => {
      state.cards = res.data;
    });
  } catch (error) {
    console.log(error);
  }
};
const reduInstance = (row) =>{
  let selectIds = [];
  selectIds.push(row.instance_id + "");
  let params = {
    instanceIds: selectIds,
    categoryId: state.page.currentNodeId,
  };
  verifyReduInstanceAxios(params)
    .then(res => {
      if (res["data"].result == "success") {
        reduInstanceAxios(params)
          .then(res1 => {
            if(res1["data"]=='success'){
              ElMessage.success("还原成功");
              requestTreeDataNewMethod();
            }
          })
          .catch(exp => {
            ElMessage.error(exp.message);
          });
      } else {
        ElMessage.error("还原失败，" + res["data"].result);
      }
    })
    .catch(exp => {
      ElMessage.error(exp.message);
    });
}
const deleteInstance = (row) => {
  let selectIds = [];
  selectIds.push(row.instance_id + "");
  let params = {
    instanceIds: selectIds,
    categoryId: state.page.currentNodeId
  }
  ElMessageBox.confirm(
    "此次删除后资产数据将彻底删除无法找回，是否确认删除？",
    "是否删除",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      deleteInstanceDeleteAxios(params)
        .then(res1 => {
          if(res1["data"]=='success'){
            ElMessage.success("删除成功");
            requestTreeDataNewMethod();
          }else{
            ElMessage.error(res1["data"]);
          }
        })
        .catch(exp => {
          ElMessage.error(exp.message);
        });
    })
    .catch(() => {});
}
const showInfo = (row) =>{
  state.itemInstanceId = row["_id"];
  state.itemCategoryId = row["category_id"];
  state.dialogVisible = true;
}
const clickCommand = (command) =>{
  if(command=='reduInstances'){
    reduInstances();
  }else if(command=='deleteInstances'){
    deleteInstances();
  }
}
onMounted(() => {
  console.log(props.sourceInfo);
  if(props.sourceInfo){
    state.page.currentNodeId = props.sourceInfo;
  }
  requestTreeDataNewMethod();
});
</script>

<style lang="scss" scoped>
.re-drawer{
  :deep(.el-drawer__header){
    margin-bottom: 0 !important;
  }
  :deep(.el-drawer__body){
    margin:0;
    padding:0 20px;
  }
}
.instance {
  padding-top:10px;
  :deep(.avue-crud__header){
    display: none;
  }
  .expandDiv{
    :deep(.el-form-item){
      margin-bottom: 5px;
    }
    :deep(.el-select){
      width:23rem;
    }
    :deep(.el-input--small){
      width:23rem;
    }
    :deep(.el-select__selected-item){
      width:21rem;
    }
  }

  :deep(.instance-form-inline) {
    .el-select__selection {
      min-height: 1.5rem;
    }

    .el-form-item__label {
      height: 1.9rem;
      line-height: 2rem;
      color: #999;
      font-size: 14px;
    }

    .el-input__inner {
      height: 1.5rem;
      line-height: 1.5rem;
    }
    .el-input-group__append{
      background-color:#409EFF;
      color:#fff;
      height: 1.6rem;
      line-height: 1.6rem;
      font-size:12px;
    }

    .el-button {
      height: 1.5rem;
      line-height: 1.5rem;
    }

    .dropdown-menu {
      .el-dropdown__list {
        padding: 0.2rem;
      }
    }

    .el-row {
      justify-content: center;
      // flex-wrap: nowrap
    }

    .secondRow {
      margin-left: -6rem;

      .el-form-item__label {
        font-size: 14px;
        font-weight: 700;
      }

      .el-range-editor--small.el-input__wrapper {
        height: 100%;
      }
    }
  }

  :deep(.centerCard) {

    > div {
      // box-shadow: 1px 1px 6px 0px #eee;
      text-align: center;
      // line-height: 1.9rem;

      span {
        font-size: 13px;
        font-weight: 600;
        // color: rgb(153, 153, 153);
      }

      div {
        font-weight: 500;
        // color: rgb(67, 67, 67);
        // font-size: 20px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .selected {
      box-sizing: border-box;
      // border: 1px solid #409eff;
      background: #409eff;

      span,
      div {
        color: #fff;
        // color: #fff;
      }
    }
  }

  :deep(.bottomLR) {
    justify-content: start;
    margin-left: 0.55em;

    .el-select__wrapper {
      height: 1.5rem;
      line-height: 1.5rem;
      width: 20rem;
      font-size: 14px;
    }

    .bottomRTable .el-select__wrapper {
      width: auto;
    }

    .bottomRTable .el-table__row {
      height: 3rem;
    }

    .bottomRTable .el-checkbox-group .el-row {
      height: 29rem;
    }
  }
}
</style>
