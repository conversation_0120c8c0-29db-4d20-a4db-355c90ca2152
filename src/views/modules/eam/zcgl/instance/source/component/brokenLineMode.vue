<template>
  <div ref="chartsContainer" :style="{ width: requestName=='风险告警'?'50rem':'33rem', height: '16rem' }"></div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
import { vulnerabilityOccurrenceTrendMethod } from "@/views/modules/eam/zcgl/instance/source/api/vulnerabilityInformation";
import { riskOccurrenceTrendMethod } from "@/views/modules/eam/zcgl/instance/source/api/riskAlarmInterface";
import * as echarts from "echarts";
const chartsContainer = ref(null);
const yData = reactive([]);
const xData = reactive([]);
const props = defineProps({
  requestIp: String,
  requestName: String
})
const vulnerabilityOccurrenceTrendAxios = async () => {
  try {
    await vulnerabilityOccurrenceTrendMethod({
      ips: props.requestIp
    }).then(res => {
      yData.length = 0;
      xData.length = 0;
      res['data']['rows'].forEach(item => {
        yData.push(item['cnt'])
        xData.push(item['time'])
      })
    })
  } catch (error) {

  }
}
const riskOccurrenceTrendAxios = async () => {
  try {
    await riskOccurrenceTrendMethod({
      "dateRange": "7d",
      "orgId": '',
      ips: props.requestIp
    }).then(res => {
      yData.length = 0;
      xData.length = 0;
      res['data']['rows'].forEach(item => {
        yData.push(item['cnt'])
        xData.push(item['time'])
      })
    })
  } catch (error) {

  }
}
// 漏洞饼状图option
type EChartsOption = echarts.EChartsOption;
onMounted(async () => {

  if (props.requestName == '漏洞信息') {
    await vulnerabilityOccurrenceTrendAxios();
  }
  if (props.requestName == '风险告警') {
    await riskOccurrenceTrendAxios();
  }


  const chartDom = chartsContainer.value;
  const myChart = echarts.init(chartDom);
  let cakeShapeOption: EChartsOption;
  cakeShapeOption = {
    grid: {
      left: "10%", //距离左侧边距
      right: "0%",
      top: "6%",
      bottom: "33%",
      containLabel: false
    },
    tooltip: {
      show: true,
      trigger: "axis",
      axisPointer: {
        // 全局配置 十字准星指示器
        type: "cross" // 不想显示虚线type: 'none'
      }
    },
    xAxis: {
      type: "category",
      data: xData
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        data: yData,
        type: "line"
      },
      // {
      //   data: yData,
      //   type: "bar",
      //   barWidth: 2,
      //   itemStyle: {
      //     opacity: 0
      //   },
      //   emphasis: {
      //     itemStyle: {
      //       opacity: 1
      //     }
      //   }
      // }
    ]
  };
  cakeShapeOption && myChart.setOption(cakeShapeOption);
});
</script>

<style lang="scss" scoped></style>
