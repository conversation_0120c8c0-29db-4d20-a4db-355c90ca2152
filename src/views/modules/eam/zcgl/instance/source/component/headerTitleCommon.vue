<template>
  <span
    class="header-text"
    style="
      position: fixed;
      padding: 1rem 13.3rem 1rem 6rem;
      z-index: 11;
      background: #fff;
      top: 7.9rem;
      margin-left: -6rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      /* display: inline-block;
      background: rgb(255, 255, 255);
      z-index: 11;
      position: fixed;
      padding: 0px 11rem 1rem;
      left: 77;
      padding-top: -5rem;
      top: 8rem;
      height: 4rem;
      line-height: 4rem; */
    "
    >{{ props.title }}</span
  >
</template>

<script lang="ts" setup>
const props = defineProps({
  title: String,
  styleInfo: Object
});
</script>

<style lang="scss" scoped>
.header-text {
  &::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 18px;
    background: inherit;
    background-color: #409eff;
    border: none;
    border-radius: 8px;
    vertical-align: -3px;
    margin-right: 6px;
  }
}
</style>
