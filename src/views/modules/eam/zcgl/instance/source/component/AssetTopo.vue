<template>
  <div class="assetTopo">
    <el-row>
      <el-col :span="12">
        <headerTitleCommonAll :title="'关系拓扑图'" />
        <div class="asset-topo"  style="height: 69vh;">
          <div class="topo-legend">
            <div class="legend" v-for="(relation, i) in relations" :key="i">
              <div :style="{ background: relation.color }"></div>
              <span style="font-size:12px;">{{ relation.label }}</span>
            </div>
          </div>
          <div class="topo-container">
            <div class="wrapper" ref="wrapper">
              <!-- 连线使用svg 生成 -->
              <svg class="topo-svg" version="1.1" xmlns="http://www.w3.org/2000/svg">
                  <path
                    v-for="(pathData, index) in pathDataList"
                    :key="index"
                    :stroke="pathData.color"
                    fill="none"
                    stroke-width="1.5"
                    :d="pathData.d"
                  ></path>
              </svg>
              <div
                class="center node"
                :style="centerStyle"
                @click="currentNode = null"
              >
                <img
                  :src="
              currentNode == null
                ? getTopoImageSelect(props.categoryId,state.categoryColumns)
                : getTopoImage(props.categoryId,state.categoryColumns)
            "
                  style="width: 100px; height: 100px"
                />
                <div style="font-size:12px;">{{ centerLabel }}</div>
              </div>
              <div
                :id="'node-' + index"
                class="node"
                v-for="(node, index) in nodes"
                :key="'node-' + index"
                :style="nodeStyle(index)"
                @click="currentNode = node.category_id"
              >
                <img
                  :src="currentNode == node.category_id? getTopoImageSelect(node.category_id,state.categoryColumns): getTopoImage(node.category_id,state.categoryColumns)"
                />
                <div class="node-label"  style="font-size:12px;">{{ node.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-divider style="margin-top: 2rem; height: 66vh" direction="vertical" />
      <el-col style="padding-left: 1.3rem" :span="11">
        <headerTitleCommonAll :title="'关联设备信息'" />
        <div>
          <div style="padding-top: 2rem;
                      height: 69vh;
                    ">
            <div style="height: 100%;overflow-y: scroll;" ref="scrollContainer">
              <el-card v-for="(item, index) in showArrayInfo" style="width: auto; min-height: 133px; margin-bottom: 1rem;"
                       shadow="hover">
                <div class="card-title" style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 0.6rem;
                  ">
                  <div style="display: flex; align-items: center">
                    <h3 style="padding-right: 1.6rem;font-size: 18px;">{{ item['name'] }}</h3>
                    <el-tag v-if="item.onlineStatus__label" style="margin-right: 3rem" type="success">{{ item['onlineStatus__label'] }}</el-tag>
                  </div>
                  <div style="line-height: 0.2rem;">
                    <!-- <el-text style="font-size: 13px;cursor: pointer;" class="mx-1" type="primary">取消关联</el-text> -->
                    <el-text style="font-size: 13px;cursor: pointer;" class="mx-1" type="primary" @click="queryAssetInfo(item)">详情</el-text>
                  </div>
                </div>
                <div style="margin-bottom: 0.9rem" class="card-content">
                  <el-row v-if="topoColumns[item['category_id']]?.columns&&topoColumns[item['category_id']]?.columns.length>0">
                    <el-col v-for="(column, j) in topoColumns[item['category_id']]?.columns" :span="12" :key="j" v-show="j < 4">
                      <el-form-item>
                        <div
                          slot="label"
                          style="
                          width: 120px;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                          font-weight:bold;
                          text-align:right;
                        "
                        >
                          {{ column.name }}：
                        </div>
                        <span class="item-value">{{
                            item[column.field]
                          }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-else>
                    <el-col :span="12">
                      <el-form-item>
                        <div
                          slot="label"
                          style="
                          width: 120px;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                          font-weight:bold;
                          text-align:right;
                        "
                        >
                          IP地址：
                        </div>
                        <span class="item-value">{{
                            item['ipAddress']
                          }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item>
                        <div
                          slot="label"
                          style="
                          width: 120px;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                          font-weight:bold;
                          text-align:right;
                        "
                        >
                          MAC地址：
                        </div>
                        <span class="item-value">{{
                            item['macAddress']
                          }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item>
                        <div
                          slot="label"
                          style="
                          width: 120px;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                          font-weight:bold;
                          text-align:right;
                        "
                        >
                          资产责任人：
                        </div>
                        <span class="item-value">{{
                            item['assetDuty']
                          }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item>
                        <div
                          slot="label"
                          style="
                          width: 120px;
                          white-space: nowrap;
                          text-overflow: ellipsis;
                          overflow: hidden;
                          font-weight:bold;
                          text-align:right;
                        "
                        >
                          物理位置：
                        </div>
                        <span class="item-value">{{
                            item['phyPosition']
                          }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <div class="card-tag">
                  <el-tag style="margin-right: 0.6rem" type="success">{{ item['categoryName'] }}</el-tag>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <div class="re-drawer">
      <el-drawer  v-model="dialogVisible"
                  size="50%"
                  v-if="dialogVisible"
                  @close="dialogVisible = false">
        <template #header="{ close, titleId, titleClass }">
          <div class="flex-bc">
            <el-col :span="22">
              <el-page-header @back="dialogVisible = false" class="mb-2">
                <template #content>
                  <span class="mr-3 font-bold"> 资产详情 </span>
                </template>
              </el-page-header>
            </el-col>
          </div>
        </template>
        <show-property :instance-id="itemInstanceId" :category-id="itemCategoryId"></show-property>
      </el-drawer>
    </div>
  </div>

</template>

<script setup lang="ts">
import {resizeObserver} from "@/views/modules/eam/zcgl/instance/source/api/resizeObj";
import {queryAllPositionSet,queryDetailRelations} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {nextTick, onMounted, reactive, ref, toRefs, watch,computed} from "vue";
import {getTopoImageSelect,getTopoImage} from "@/views/modules/eam/zcgl/instance/source/api/topoAsset";
import headerTitleCommonAll from "@/views/modules/eam/zcgl/instance/source/component/headerTitleCommonAll.vue";
import ShowProperty from "@/views/modules/eam/zcgl/instance/source/component/showProperty.vue";
const props = defineProps({
  categoryId: String,
  instanceId: String,
})
const arrayInfo = reactive([]);
const allArrayInfo = reactive([]);
const currentDataIndex = ref(0);
const groupTypes = ref([]);
const state = reactive({
  dialogVisible:false,
  itemInstanceId:"",
  itemCategoryId:"",
  showArrayInfo:[],
  categoryColumns:{},
  containerOffset: {
    left: 0,
    top: 0,
  },
  centerX: 0,
  centerY: 0,
  rx: 100,
  ry: 100,
  layouts: [],
  pathDataList: [],
  currentNode: null,
  showList: false,
  centerLabel:'',
  tableDatas:[],
  relations : [
    {
      value: "自动关联",
      label: "自动关联",
      color: "#1890FF",
    },
    {
      value: "人工关联",
      label: "人工关联",
      color: "gray",
    },
  ],
  relationColors : {
    "自动关联":"#1890FF","人工关联":"gray"
  }
});
const topoColumns = ref({});
const initAllPosition = async() =>{
  queryAllPositionSet().then(res => {
    topoColumns.value = res["data"];
  });
}

const {
  pathDataList,currentNode,centerLabel,relations,
  showArrayInfo,itemInstanceId,itemCategoryId,dialogVisible
} = toRefs(state);
onMounted(()=>{
  initAllPosition();
  initCategoryColumns();
  initTopoData();
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll);
  }
  nextTick(()=>{
    init();
  })
  resizeObserver.observe(wrapper.value, initLayoutSize);
})
const initCategoryColumns = async() =>{
  queryAllPositionSet().then(res => {
    state.categoryColumns = res["data"];
  });
}
const queryAssetInfo = (item) =>{
  state.itemInstanceId = item._id;
  state.itemCategoryId = item.category_id;
  state.dialogVisible = true;
}
const initTopoData = async() =>{
  let params = {
    instance_id: props.instanceId,
    category_id: props.categoryId
  };
  queryDetailRelations(params).then(res => {
    state.centerLabel = res["data"].name;
    groupTypes.value = res["data"].list || [];
    // let i = 0;
    res["data"].list.forEach(item => {
      allArrayInfo.push(...item['children']);
      // item['children'].forEach((item, index) => {
      //   if (((index % 10) == 0) && index != 0) {
      //     arrayInfo[i].push(item);
      //     i++;
      //   } else {
      //     if (!Array.isArray(arrayInfo[i]) || arrayInfo[i].length == 0) {
      //       arrayInfo[i] = [];
      //     }
      //     arrayInfo[i].push(item);
      //   }
      // })
    })
    state.showArrayInfo.push(...allArrayInfo);
    state.tableDatas = JSON.parse(JSON.stringify(allArrayInfo));
  });
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
}
const scrollContainer = ref(null);
const handleScroll = () => {
  if (scrollContainer.value) {
    const currentChildrenHeight = scrollContainer.value.children.length * (scrollContainer.value.children[0].offsetHeight + 16)
    if ((currentChildrenHeight - scrollContainer.value.scrollTop - 100) < scrollContainer.value.offsetHeight) {
      currentDataIndex.value = currentDataIndex.value + 1;
      state.showArrayInfo.push(...arrayInfo[currentDataIndex.value]);
    }
  }
};
const centerStyle = computed(()=>{
  let centerStyle = {
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
  };
  return centerStyle;
});
const nodes = computed(()=>{
  groupTypes.value=groupTypes.value.filter(gt=>
    gt.children!=null&&gt.children.length>0
  );
  return groupTypes.value.map(groupType => {
    let { children, groupValue, groupLabel, relation,type } = groupType;
    let { name, ipAddress } = (children && children[0]) || {};
    return {
      label:
        children?.length == 1
          ? `${name}(${ipAddress})`
          : `${groupLabel}(${children.length})`,
      groupValue,
      children,
      relation,
      type,
      category_id: groupValue,
    };
  });
});

const wrapper = ref();
const init = () =>{
  let { width, height } = wrapper.value.getBoundingClientRect();
  state.rx = width / 2;
  state.ry = height / 2;
  state.centerX = state.rx;
  state.centerY = state.ry;
  updateLayouts();
}
const initLayoutSize = () =>{
  if (!wrapper?.value) return;
  let { width, height, left, top } = wrapper.value.getBoundingClientRect();
  if (width == 0 || height == 0) {
    state.showList = false;
    return;
  }
  nextTick(() => {
    setTimeout(() => {
      state.showList = true;
    }, 0);
  });

  // 如果没有布局数据
  state.pathDataList.splice(0, state.pathDataList.length);
  if (state.layouts.length == 0) return;

  init();
  Object.assign(state.containerOffset, {
    left,
    top,
  });

  updatePathDataList();
}
const nodeStyle = (index) =>{
  let nodeStyle = {};
  if (state.layouts[index]) {
    Object.assign(nodeStyle, {
      left: `${state.layouts[index].x}%`,
      top: `${state.layouts[index].y}%`,
    });
  }
  return nodeStyle;
}
const updateLayouts = () =>{
  let n = nodes.value.length;
  let step = 1;
  let increment = (step * (2 * Math.PI)) / n;
  let angle = 0;
  state.layouts.splice(0, state.layouts.length);
  for (let i = 0; i < n; i++) {
    let x = 50 + 50 * Math.cos(angle);
    let y = 50 - 50 * Math.sin(angle);
    state.layouts.push({
      x,
      y,
      angle,
      node: nodes.value[i],
    });
    angle += increment;
  }
}
const updatePathDataList = () =>{
  if (!wrapper) return;
  let { width, height } = wrapper.value.getBoundingClientRect();
  if (width > 0 || height > 0) {
    // 计算各个节点实际坐标，绘制连线
    state.layouts.forEach(layout => {
      // 中心百分比位置
      let { x, y, node } = layout;
      let X = ((width * x) / 100).toFixed(4);
      let Y = ((height * y) / 100).toFixed(4);
      state.pathDataList.push({
        d: `M${state.centerX}, ${state.centerY-15}L${X},${Y-15}`,
        color: state.relationColors[node.type],
      });
    });
  }
}

watch(nodes,(newValue) =>{
  if(newValue.length>0){
    updateLayouts();
    setTimeout(()=>{
      initLayoutSize();
    },500);
  }
},{ deep: true, immediate: true })

watch(currentNode,(newValue) =>{
  if(newValue == null){
    state.showArrayInfo = JSON.parse(JSON.stringify(state.tableDatas));
  }else{
    state.showArrayInfo = state.tableDatas.filter(item=>{
      if(newValue == item.category_id){
        return true;
      }
    })
  }
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item){
  margin-bottom:0px;
  margin-top:5px;
}
.asset-topo {
  position: relative;
  .topo-legend {
    position: absolute;
    left: 0;
    top: 0;
    width: 120px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .legend {
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      div {
        flex: 1;
        height: 3px;
        margin-right: 10px;
      }

      .connect {
        background: #1890ff;
      }

      .parent {
        background: #ffb72b;
      }

      .create {
        background: #0fbe55;
      }
    }
  }

  .topo-container {
    position: absolute;
    left: 150px;
    right: 80px;
    padding: 40px;
    top: 0;
    bottom: 55%;

    .wrapper {
      position: relative;
      width: 100%;
      height: 100%;

      .topo-svg {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .node {
        position: absolute;
        text-align: center;
        cursor: pointer;
        width: 75px;
        z-index: 200;
        transform: translate(-50%, -50%);

        .node-label {
          margin-top: 2px;
        }
      }

      .path {
        position: absolute;
        background: #0a79a1;
        height: 1px;
      }
    }
  }

  .asset-topo-list {
    position: absolute;
    background: #f5f5f5;
    padding: 10px;
    left: 0;
    right: 0;
    top: 45%;
    bottom: 0;
    .asset-topo-card {
      border: unset;
      box-shadow: unset;
      :deep(.el-card__header) {
        padding: 15px;
        .title {
          font-size: 18px;
          font-family: PingFang SC-Bold, PingFang SC;
          font-weight: bold;
          line-height: 18px;
          .icon {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
.re-drawer{
  :deep(.el-drawer__header){
    margin-bottom: 0 !important;
  }
  :deep(.el-drawer__body){
    margin:0;
    padding:0 20px;
  }
}
</style>
