<template>
  <span
    class="header-text"
    >{{ props.title }}</span
  >
</template>

<script lang="ts" setup>
const props = defineProps({
  title: String,
  styleInfo: Object
});
</script>

<style lang="scss" scoped>
.header-text {
  font-size: 16px;
  font-weight: 500;
  &::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 18px;
    background: inherit;
    background-color: #409eff;
    border: none;
    border-radius: 8px;
    vertical-align: -3px;
    margin-right: 6px;
  }
}
</style>