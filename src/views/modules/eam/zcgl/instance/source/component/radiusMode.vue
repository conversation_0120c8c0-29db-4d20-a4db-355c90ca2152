<template>
  <div ref="chartsContainer" :style="{ width: '24.3rem', height: '11rem' }"></div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
import { focusOnRiskMethod } from "@/views/modules/eam/zcgl/instance/source/api/riskAlarmInterface";
import {
  vulnerabilityDistributionMethod
} from "@/views/modules/eam/zcgl/instance/source/api/vulnerabilityInformation";
import * as echarts from "echarts";
const chartsContainer = ref(null);
const data = reactive([
]);
const props = defineProps({
  requestIp: String,
  riskName: String
})
const vulnerabilityType = ref("");
const vulnerabilityDistributionAxios = async () => {
  try {
    await vulnerabilityDistributionMethod({
      "ips": props.requestIp,
      "dealStatus":"undisposed"
    }).then(res => {
      data.length = 0;
      res['data']['rows'].forEach(item => {
        data.push({
          ...item,
          value: item['count'],
          name: item['vulType'],
        })
      })
      vulnerabilityType.value = res['data']['title'];
    })
  } catch (error) {

  }
}

const riskOccurrenceTrendAxios = async () => {
  try {
    await focusOnRiskMethod({
      "dateRange": "30d",
      "orgId": '',
      "ips": props.requestIp,
      "dealWith":"1"
    }).then(res => {
      data.length = 0;
      res['data']['rows'].forEach(item => {
        data.push({
          ...item,
          value: item['count'],
          name: item['eventType'],
        })
      })
      vulnerabilityType.value = res['data']['title'];
    })
  } catch (error) {

  }
}
// 漏洞饼状图option
type EChartsOption = echarts.EChartsOption;
onMounted(async () => {
  if (props.riskName != '重点风险关注') {
    await vulnerabilityDistributionAxios();
  }

  if (props.riskName == '重点风险关注') {
    await riskOccurrenceTrendAxios()
  }

  const chartDom = chartsContainer.value;
  const myChart = echarts.init(chartDom);
  let cakeShapeOption: EChartsOption;
  cakeShapeOption = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b} : {c} ({d}%)"
    },
    legend: {
      type: "scroll",
      // orient: "vertical",
      show: true,
      orient: "vertical",
      right: "right", //位置
      icon: "circle", //图例形状
      textStyle: {
        // 文字样式
        fontFamily: "PingFangSC-Regular",
        color: "#666666"
      },
      formatter: function (name) {
        var total = 0;
        var target;
        for (var i = 0, l = data.length; i < l; i++) {
          total += data[i].value;
          if (data[i].name == name) {
            target = data[i].value;
          }
        }
        return name + " " + ((target / total) * 100).toFixed(2) + "%";
      }
    },

    series: [
      {
        name: vulnerabilityType.value,
        type: "pie",
        radius: ["8%", "88%"],
        center: ["26%", "53%"],
        // roseType: "radius",
        itemStyle: {
          borderRadius: 5
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        data: data
      }
    ]
  };
  cakeShapeOption && myChart.setOption(cakeShapeOption);
});

</script>

<style lang="scss" scoped></style>
