<template>
  <el-main v-if="isShow" class="assetsUnknown">
    <el-row>
      <!-- <span style="color: red">说明：表格行上下拖动，可维护属性排序</span> -->
    </el-row>
    <el-row style="margin-top: 10px">
      <el-col :span="24">
        <el-button type="primary" class="new-btn" @click="addGroupCategory">添加分组
        </el-button>
        <el-button type="primary" class="new-btn" @click="insertAttrPropertyGroup">保存
        </el-button>
        <el-button type="primary" @click="closePropDialog">关闭</el-button>
      </el-col>
    </el-row>
    <el-row style="margin-top: 5px;width: 100%;height: 79vh;
        overflow: hidden;overflow-y: scroll;">
      <im-table class="im-table-one" ref="singleTable" :data="dataCategory" v-if="isSingleTable" :columns="tableColumns"
        id-key="CODE" highlight-current-row border sortable>
        <template v-slot:group_id="scope">
          <el-select v-model="scope.row.GROUP_ID" clearable>
            <el-option v-for="item in adEdTableData" :value="item.value" :key="item.value"
              :label="item.label"></el-option>
          </el-select>
        </template>
        <template v-slot:propertyType="scope">
          <span v-if="scope.row.TYPE == 'common'">公共属性</span>
          <span v-if="scope.row.TYPE == 'service'">业务属性</span>
        </template>
      </im-table>
    </el-row>
    <el-dialog style="height: 66vh;" destroy-on-close :title="addGroupTitle" append-to-body v-model="addGroupDialogFormVisible" width="80%">
      <!-- <template> -->
        <el-row>
          <el-col :span="24">
            <!-- <span style="color: red">说明:右侧表格行上下拖动，可维护分组排序</span> -->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="insertGroup()">添加</el-button>
            <el-button type="primary" @click="addGroup()">保存</el-button>
            <el-button @click="closeGroup()">关闭</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <im-table ref="leftTable" :columns="leftColumns" :data="leftData" border id-key="key" stripe
              @select-all="tableLeftSelectAll" @select="tableLeftSelect">
              <template v-slot:groupName="scope">
                <div>
                  <span v-if="scope.row.edit == false">
                    <el-input v-model="scope.row.label" />
                  </span>
                  <span v-else>{{ scope.row.label }}</span>
                </div>
              </template>
              <template v-slot:oper="scope">
                <el-button type="success" icon="el-icon-check" circle @click="saveAddGroup(scope.row)"></el-button>
                <el-button type="primary" icon="el-icon-edit" circle @click="updateGroup(scope.row)"></el-button>
                <el-button type="danger" icon="el-icon-delete" circle @click="deleteGroup(scope.row)"></el-button>
              </template>
            </im-table>
          </el-col>
          <el-col :span="4" style="text-align: center">
            <div>
              <el-button icon="el-icon-right" @click="rightClick" circle></el-button>
            </div>
            <div>
              <el-button icon="el-icon-back" @click="leftClick" circle></el-button>
            </div>
          </el-col>
          <el-col :span="10">
            <im-table ref="rightTable" sortable :columns="rightColumns" :data="rightData" border id-key="key" stripe
              @select-all="tableRightSelect" @select="tableRightSelect">
            </im-table>
          </el-col>
        </el-row>
      <!-- </template> -->
    </el-dialog>
  </el-main>
</template>
<script lang="ts" setup>
import {
  getByCategoryDataAxios,
  getByCategoryGroupAxios,
  insertAttrPropertyGroupAxios,
  getGroupCategoryAxios,
  setGroupCategoryAxios,
  insertGroupSortAxios,
  validateAttrGroupAxios,
  saveAttrGroupAxios,
  removeAttrGroupAxios,
} from "@/views/modules/eam/zcgl/instance/source/api/attrsortgroupModelInterface";
import { reactive, ref, toRefs, watch, onMounted, onUnmounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const tableColumns = ref([
  {
    label: "所属分组",
    prop: "GROUP_ID",
    align: "center",
    slot: "group_id",
  },
  {
    label: "属性类型",
    prop: "TYPE",
    align: "center",
    slot: "propertyType",
  },
  { label: "属性名称", prop: "NAME", align: "center" },
]);
const tableRightSelection = ref([]);
const tableLeftSelection = ref([]);
const tableLeftData = ref([]);
const tableRightData = ref([]);
const modelData = ref([]);
const rightColumns = ref([
  { type: "selection", prop: "key", align: "center" },
  { label: "已选分组", prop: "label", align: "center" },
]);
const leftColumns = ref([
  { type: "selection", prop: "key", align: "center" },
  {
    label: "未选分组",
    prop: "label",
    align: "center",
    slot: "groupName",
  },
  {
    label: "",
    prop: "oper",
    align: "center",
    slot: "oper",
    width: "150px",
  },
]);
const isSingleTable = ref(true);
const filterText = ref("");
const searchText = ref("");
const isExpand = ref(false);
const categoryNode = ref([]);
const isShow = ref(false);
const groupList = ref([]);
const dataCategory = ref([{ GROUP_PROPERTY_SORT: "" }]);
const addGroupDialogFormVisible = ref(false);
const groupSort = ref(false);
const addGroupTitle = ref("");
const sourceKeys = ref([]);
const targetKeys = ref([]);
const leftData = ref([]);
const rightData = ref([]);
const data = ref([]);
const adEdTableData = ref([]);
const isSingleTableAdd = ref(true);
const defaultProps = ref({
  children: "children",
  label: "label",
});
const props = defineProps({
  categoryId: {
    type: String || Number,
  },
  categoryName: {
    type: String,
  },
});

const insertGroup = () => {
  var t = 0;
  if (leftData.value.length > 0) {
    for (var i = 0; i < leftData.value.length; i++) {
      if (leftData.value[i].key + "".indexOf("rf") >= 0) {
        var g = parseInt((leftData.value[i].key + "").split("rf")[1]);
        if (g > t) {
          t = g;
        }
      }
    }
    let tt = {};
    tt['label'] = "";
    tt['key'] = "rf" + t + 1;
    tt['edit'] = false;
    leftData.value.push(tt);
  } else {
    let tt = {};
    tt['label'] = "";
    tt['key'] = "rf" + leftData.value.length + 1;
    tt['edit'] = false;
    leftData.value.push(tt);
  }
}

const updateGroup = (row) => {
  row.edit = false;
}

const deleteGroup = (row) => {
  if ((row.key + "").indexOf("rf") >= 0) {
    var c = 0;
    for (var i = 0; i < leftData.value.length; i++) {
      if (leftData.value[i].key == row.key) {
        c = i;
      }
    }
    leftData.value.splice(c, 1);
    ElMessage.success("移除成功");
  } else {
    let params = {};
    params['GROUP_ID'] = row.key;
    ElMessageBox.confirm(
      "移除此分组后，该分组下的所有属性变为未分组，是否确认移除?",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    )
      .then(() => {
        removeAttrGroupAxios(params)
          .then(res => {
            for (let i = 0; i < leftData.value.length; i++) {
              if (leftData.value[i].key == row.key) {
                c = i;
              }
            }
            leftData.value.splice(c, 1);
            ElMessage.success("移除成功");
          })
          .catch(exp => {
            ElMessage.error(exp.message);
          });
      })
      .catch(() => { });
  }
}

const saveAddGroup = (row) => {
  if (row.label == "") {
    ElMessage.error("组名称不能为空！");
    return;
  }
  let params = row;
  params.isAuth = true;
  validateAttrGroupAxios(params)
    .then(res => {
      if (res.data.result == "success") {
        saveAttrGroupAxios(params)
          .then(res1 => {
            row.key = res1.data.key;
            row.label = res1.data.label;
            row.edit = true;
          })
          .catch(exp1 => {
            ElMessage.error(exp1.message);
          });
      } else {
        ElMessage.error(res.data.result);
      }
    })
    .catch(exp => {
      ElMessage.error(exp.message);
    });
}

const tableLeftSelect = (selection, row) => {
  tableLeftSelection.value = selection;
}
const tableLeftSelectAll = (selection, row) => {
  tableLeftSelection.value = selection;
}
const tableRightSelect = (selection, row) => {
  tableRightSelection.value = selection;
}
const tableRightSelectAll = (selection, row) => {
  tableRightSelection.value = selection;
}

const leftClick = () => {
  tableRightSelection.value.forEach(item => {
    tableLeftData.value.push(item);
  });
  const array = [];
  tableRightData.value.forEach((all, index) => {
    tableRightSelection.value.forEach(node => {
      if (node.key === all.key) {
        array.push(index);
      }
    });
  });
  array
    .sort((i1, i2) => i2 - i1)
    .forEach(index => {
      tableRightData.value.splice(index, 1);
    });
  tableRightSelection.value = [];
}

const rightClick = () => {
  let tt = false;
  tableLeftSelection.value.forEach(item => {
    if ((item.key + "").indexOf("rf") >= 0) {
      ElMessage.error("选中存在未保存行，无法移动");
      tt = true;
      return;
    }
  });
  if (tt) {
    return;
  }
  tableLeftSelection.value.forEach(item => {
    tableRightData.value.push(item);
  });
  const array = [];
  tableLeftData.value.forEach((all, index) => {
    tableLeftSelection.value.forEach(node => {
      if (node.key === all.key) {
        array.push(index);
      }
    });
  });
  array
    .sort((i1, i2) => i2 - i1)
    .forEach(index => {
      tableLeftData.value.splice(index, 1);
    });

  tableLeftSelection.value = [];
}

const columnDataFn = (value) => {
  modelData.value = value;
}
const init = () => {
  isShow.value = true;
  getByCategoryData();
  getByCategoryGroup();
}
const addGroupCategory = () => {
  getGroupCategory();
  addGroupTitle.value = "分组维护";
  addGroupDialogFormVisible.value = true;
}

const getGroupCategory = () => {
  //获取所有分组
  targetKeys.value = [];
  rightData.value = [];
  leftData.value = [];
  getGroupCategoryAxios().then( (response)=> {
    let data = response.data;
    let row = data.row;
    if (null != data) {
      sourceKeys.value = [];
      var tt = [];
      for (var i = 0; i < adEdTableData.value.length; i++) {
        var flag = false;
        for (var k = 0; k < row.length; k++) {
          if (row[k].key == adEdTableData.value[i].value) {
            flag = true;
            break;
          }
        }
        if (!flag) {
          tt.push(i);
        }
      }
      for (var i = 0; i < tt.length; i++) {
        adEdTableData.value.splice(i, 1);
      }
      adEdTableData.value.forEach(row => {
        targetKeys.value.push(row);
        let json = {
          key: row.value,
          label: row.label,
          edit: true,
        };
        rightData.value.push(json);
      });
      data.row.forEach(row => {
        let isTrue = true;
        adEdTableData.value.forEach(rows => {
          if (rows.value == row.key) {
            isTrue = false;
          }
          row.edit = true;
        });
        if (isTrue) {
          leftData.value.push(row);
        }
      });
    }
  });
}

const getByCategoryData = () => {
  getByCategoryDataAxios({
    categoryId: props.categoryId,
    isAuth: true
  }).then(response => {
    let data = response.data;
    let result = data.result;
    if (null != result && result.length > 0) {
      adEdTableData.value = result;
    } else {
      adEdTableData.value = [];
    }
  });
}

const getByCategoryGroup = () => {
  var current = this;
  getByCategoryGroupAxios({
    categoryId: props.categoryId,
    isAuth: true
  }).then(response => {
    var data = response.data;
    if (null != data) {
      dataCategory.value = data;
      dataCategory.value.forEach(row => {
        if (row['GROUP_ID'] === 0) {
          row['GROUP_ID'] = "";
        }
      });
    } else {
      dataCategory.value = [];
    }
  });
}

const insertAttrPropertyGroup = () => {
  //添加属性和分组的设置
  let current = this;
  dataCategory.value.forEach(row => {
    if (row['GROUP_ID'] === "") {
      row['GROUP_ID'] = 0;
    }
  });
  const params = {};
  params['data'] = dataCategory.value;
  params['isAuth'] = true;
  insertAttrPropertyGroupAxios(params).then(response => {
    let data = response.data;
    if (data > 0) {
      ElMessage.success("成功");
      // current.groupCategory = false;
      getByCategoryData();
      getByCategoryGroup();
    } else {
      ElMessage.error("失败");
    }
  });
  // current.event = 0;
}

const insertGroupSort = () => {
  const params = {};
  params['data'] = adEdTableData.value;
  params['isAuth'] = true;
  insertGroupSortAxios(params).then(response => {
    let data = response.data;
    if (data > 0) {
      ElMessage.success("成功");
      getByCategoryData();
      getByCategoryGroup();
      groupSort.value = false;
    } else {
      ElMessage.error("失败");
    }
  });
}

const addGroupSort = () => {
  //分组排序
  groupSort.value = true;
  nextTick(() => {
    dragSortAdd();
  });
}
const setGroupCategory = (groupIds, categoryId) => {
  let current = this;
  setGroupCategoryAxios({
    GROUP_ID: groupIds,
    categoryId: categoryId,
    isAuth: true
  }).then(response => {
    let data = response.data;
    if (data > 0) {
      ElMessage.success("成功");
      addGroupDialogFormVisible.value = false;
      getByCategoryData();
      getByCategoryGroup();
    } else {
      ElMessage.error("失败");
    }
  });
}

const addGroup = () => {
  let arr = [];
  for (let item of modelData.value) {
    arr.push(item.key);
  }
  setGroupCategory(arr, props.categoryId);
}
const closeGroup = () => {
  addGroupDialogFormVisible.value = false;
}
const changePropertySortIndex = () => {
  dataCategory.value.sort((a: Object, b: Object) => {
    return a['GROUP_PROPERTY_SORT'] - b['GROUP_PROPERTY_SORT'];
  });
}

const changeGroupSortIndex = () => {
  adEdTableData.value.sort((a, b) => {
    return a.GROUP_SORT - b.GROUP_SORT;
  });
}
const emit = defineEmits(['closeSetShowGroup'])
const closePropDialog = () => {
  emit("closeSetShowGroup");
}
watch(
  leftData,
  (value: any) => {
    tableLeftData.value = value;
  },
  { deep: true, immediate: true }
);
watch(
  rightData,
  (value: any) => {
    tableRightData.value = value;
  },
  { deep: true, immediate: true }
);
watch(
  tableRightData,
  (value: any) => {
    columnDataFn(value);
  },
  { deep: true, immediate: true }
);

init();
</script>
<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 0 20px;
  padding-bottom: 30px;
  max-height: 600px;
  overflow: scroll;
}

.el-main {
  padding: 0;
}

.el-header {}

.el-header {
  padding: 0;
}

.el-el-form-item {
  margin-bottom: 2px;
}

.el-aside {
  color: #333;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.header {
  padding: 8px;
  text-align: right;
}

.custom-theme-default .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #1ca5d4;
}

.el-row {
  margin-bottom: 20px;
}

.card {
  overflow: auto;
}

.im-table-one {
  width: 100% !important;

}
</style>
