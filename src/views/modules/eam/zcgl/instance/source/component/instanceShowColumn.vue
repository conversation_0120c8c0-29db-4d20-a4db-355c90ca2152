<template>
  <el-main>
    <div class="flex-c">
      <el-input v-model="filterTemplateColumn" placeholder="过滤值" class="w-1/2"></el-input>
      <el-button type="primary" class="ml-3" @click="changeFilter(filterTemplateColumn)">查询</el-button>
      <el-button type="primary" @click="resetFilter">重置</el-button>
    </div>
    <el-row style="margin-top:20px;">
      <el-col :span="14">
        <el-checkbox-group v-model="checkSelect">
        <el-card>
          <el-divider content-position="left">公共属性</el-divider>
          <div style="margin-top:15px;">
            <el-checkbox v-for="item in commonProList1" :label="item.value" :key="item.value" @change="changeSelect($event,item)">{{item.label}}</el-checkbox>
          </div>
        </el-card>
          <el-card>
            <el-divider content-position="left" style="margin-bottom:10px;">关键业务属性</el-divider>
            <div style="margin-top:15px;">
              <el-checkbox v-for="item in serverImProList1" :label="item.value"
                           :key="item.value" @change="changeSelect($event,item)">{{item.label}}</el-checkbox>
            </div>
          </el-card>
          <el-card>
            <el-divider content-position="left" style="margin-bottom:10px;">业务属性</el-divider>
            <div style="margin-top:15px;">
              <el-checkbox v-for="item in serverProList1" :label="item.value"
                           :key="item.value" @change="changeSelect($event,item)">{{item.label}}</el-checkbox>
            </div>
          </el-card>
        </el-checkbox-group>
      </el-col>
      <el-col :span="1">&nbsp;</el-col>
      <el-col :span="9">
        <avue-crud :data="tableData" :option="columnsCOUNTOption" @sortable-change="changeSort"
        >
          <template #COLUMN_WIDTH="{ row, $index }">
            <el-input-number v-model="row.COLUMN_WIDTH"></el-input-number>
          </template>
          <template #COLUMN_ALIGN="{ row, $index }">
            <el-select v-model="row.COLUMN_ALIGN" clearable>
              <el-option v-for="item in alignList" :value="item.value" :label="item.label"></el-option>
            </el-select>
          </template>
        </avue-crud>
      </el-col>
    </el-row>
  </el-main>
</template>
<script setup lang="ts">
  import {initSetColumnAxios,saveSetShowColumnAxios} from '@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface';
  import {onMounted, reactive, toRefs} from "vue";
  import {ElMessage} from "element-plus";
  const state = reactive({
    commonProList:[],
    serverImProList:[],
    serverProList:[],
    commonProList1:[],
    serverImProList1:[],
    serverProList1:[],
    filterTemplateColumn:'',
    tableData:[],
    checkSelect:[],
    columnsCOUNTOption:{
      index:true,
      indexLabel:"序号",
      align: "center",
      menuAlign: "center",
      refreshBtn:false,
      columnBtn:false,
      gridBtn:false,
      menu: false,
      border: true,
      stripe: true,
      addBtn: false,
      editBtn: false,
      delBtn: false,
      rowKey:"propName",
      sortable: true,
      column:[
        { label: "属性名称", prop: "propName", align: "center",showOverFlowTooltip: true },
        { label: "宽度", prop: "COLUMN_WIDTH", align: "center"},
        { label: "对齐方式", prop: "COLUMN_ALIGN", align: "center" }
      ]
    },
    alignList:[{
      value:"left",label:"左对齐"
    },{
      value:"right",label:"右对齐"
    },{
      value:"center",label:"居中"
    }]
  })
  const {
    commonProList1,serverImProList1,serverProList1,filterTemplateColumn,tableData,
    checkSelect,alignList,columnsCOUNTOption
  } = toRefs(state);
  const props = defineProps({
    categoryId:{
      type:String||Number
    }
  })
  const emit = defineEmits(["closeShowColumn","tableShowClick","initTableColumns"]);
  const init = () =>{
    let params = {
      categoryId:props.categoryId,
    };
    initSetColumnAxios(params).then(res=>{
      if(res.data){
        state.commonProList=res.data.commonList;
        state.serverImProList=res.data.serverImList;
        state.serverProList=res.data.serverList;
        state.commonProList1=res.data.commonList;
        state.serverImProList1=res.data.serverImList;
        state.serverProList1=res.data.serverList;
        state.tableData=res.data.dataList;
        state.checkSelect=[];
        if(state.tableData!=null&&state.tableData.length>0){
          for(let i=0;i<state.tableData.length;i++){
            state.checkSelect.push(parseInt(state.tableData[i].PROPERTY_ID));
          }
        }

      }
    })
  }
  const changeSelect = (value,row) =>{
    let row1={
      PROPERTY_ID:row.value,
      propName:row.label,
      COLUMN_ALIGN:'center',
      COLUMN_WIDTH:150
    }
    if(value){
      state.tableData.push(row1);
    }else{
      let removeId = 0;
      let flag = false;
      for(let i =0;i<state.tableData.length;i++){
        if(state.tableData[i].PROPERTY_ID==row.value){
          flag = true;
          removeId=i;
          break;
        }
      }
      if(flag){
        state.tableData.splice(removeId,1);
      }
    }
  }
  const saveTable = () =>{
    if(state.tableData.length==0){
      ElMessage.error("请至少勾选一个属性");
      return;
    }
    let data=[];
    for(let i=0;i<state.tableData.length;i++){
      let mm = state.tableData[i];
      mm.COLUMN_HIDE=0;
      mm.ATTRSORT=i+1;
      data.push(mm);
    }
    let tt = state.tableData.length;
    for(let t=0;t<state.commonProList.length;t++){
      let flag = false;
      for(let i=0;i<state.tableData.length;i++){
        if(state.commonProList[t].value==state.tableData[i].PROPERTY_ID){
          flag = true;
          break;
        }
      }
      if(!flag){
        let mm = {};
        tt = tt+1;
        mm.ATTRSORT=tt;
        mm.PROPERTY_ID=state.commonProList[t].value;
        mm.COLUMN_WIDTH=150;
        mm.COLUMN_ALIGN='center';
        mm.COLUMN_HIDE=1;
        data.push(mm);
      }
    }
    for(let t=0;t<state.serverImProList.length;t++){
      let flag = false;
      for(let i=0;i<state.tableData.length;i++){
        if(state.serverImProList[t].value==state.tableData[i].PROPERTY_ID){
          flag = true;
          break;
        }
      }
      if(!flag){
        let mm = {};
        tt = tt+1;
        mm.ATTRSORT=tt;
        mm.PROPERTY_ID=state.serverImProList[t].value;
        mm.COLUMN_WIDTH=150;
        mm.COLUMN_ALIGN='center';
        mm.COLUMN_HIDE=1;
        data.push(mm);
      }
    }
    for(let t=0;t<state.serverProList.length;t++){
      let flag = false;
      for(let i=0;i<state.tableData.length;i++){
        if(state.serverProList[t].value==state.tableData[i].PROPERTY_ID){
          flag = true;
          break;
        }
      }
      if(!flag){
        let mm = {};
        tt = tt+1;
        mm.ATTRSORT=tt;
        mm.PROPERTY_ID=state.serverProList[t].value;
        mm.COLUMN_WIDTH=150;
        mm.COLUMN_ALIGN='center';
        mm.COLUMN_HIDE=1;
        data.push(mm);
      }
    }
    let params = {};
    params.dataList = data;
    params.categoryId = props.categoryId;
    saveSetShowColumnAxios(params).then(res=>{
      if(res.data=='success'){
        ElMessage.success("保存成功");
        emit("closeShowColumn");
        const json={categoryId:props.categoryId,pageNum:1,pageSize:10,userId:"1",isAuth:"true"}
        emit("tableShowClick",'1');
        emit("initTableColumns",json);
      }else{
        ElMessage.error(res.data);
      }

    }).catch(exp=>{
      ElMessage.error(exp.message);
    })
  }
  const  closeDialog = () =>{
    emit("closeShowColumn");
  }
  const changeFilter = (value) =>{
    state.commonProList1= state.commonProList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      },
    );;
    state.serverImProList1=state.serverImProList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      },
    );
    state.serverProList1=state.serverProList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      },
    );
  }
  const changeSort = (newIndex,oldIndex) => {
    let newRow = state.tableData[newIndex];
    let oldRow = state.tableData[oldIndex];
    state.tableData[newIndex] = oldRow;
    state.tableData[oldIndex] = newRow;
  }
  const resetFilter = () =>{
    state.filterTemplateColumn = '';
    changeFilter(state.filterTemplateColumn);
  }
  defineExpose({
    saveTable,closeDialog
  })
  onMounted(()=>{
    init();
  })

</script>
<style lang="scss" scoped>
:deep(.el-checkbox){
  width: 200px; /* 定义checkbox的宽度 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
:deep(.avue-crud__header){
  display: none;
}
</style>
