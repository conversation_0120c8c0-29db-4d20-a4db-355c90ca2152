<template>
  <div style="position:relative;">
    <el-button
      type="primary"
      size="small"
      @click="toAddProperty"
      style="position:absolute;top:15px;right:360px;z-index:100;"
    >
      添加属性
    </el-button>
    <el-button
      type="primary"
      size="small"
      @click="toAddRelationList"
      style="position:absolute;top:15px;right:280px;z-index:100;"
    >
      新增关系
    </el-button>
    <div style="position:absolute;top:15px;right:20px;width:240px;z-index:100;" v-if="activeName=='first'">
      <el-input type="text" v-model="tableFilter" clearable placeholder="请输入属性编码或属性名称过滤" @change="queryFilterTable"></el-input>
    </div>
    <el-row style="margin-bottom: 0">
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
      >
        <el-tab-pane name="first" label="属性">
        </el-tab-pane>
        <el-tab-pane label="主动关系" name="second">
        </el-tab-pane>
        <el-tab-pane name="third" label="被动关系">
        </el-tab-pane>
      </el-tabs>
    </el-row>
    <el-row style="margin-bottom: 5px">
      <avue-crud :data="tableData" :option="tableOption" v-model:page="state.pagination"
                 @size-change="handlePageSizeChange" :table-loading="tableLoading"
                 @current-change="handlePageCurrentChange">
        <template #propSlot="{row}">
          <el-dropdown placement="bottom" @command="val => propertyCommand(val,row)">
            <el-button type="primary" link style="margin-right:15px;">操作</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="removeProperty">
                  移除
                </el-dropdown-item>
                <el-dropdown-item command="resetRelationCategory" v-if="row.isRelation=='是'">
                  复原
                </el-dropdown-item>
                <el-dropdown-item command="updateCatePro">
                  属性个性化
                </el-dropdown-item>
                <el-dropdown-item command="setDefaultValue">
                  默认值个性化
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template #relationSlot="{row}">
          <el-button type="primary" link v-if="row.isEdit=='是'" @click="editRelation(row)"
          >编辑</el-button
          >
          <el-button
            @click="removeRelation(row)"
            type="primary"
            link
            size="small"
          >删除</el-button
          >
        </template>
        <template #beRelationSlot="{row}">
          <el-button
            type="primary"
            link
            @click="lookUpBeRelation(row)"
          >查看</el-button
          >
        </template>
      </avue-crud>
    </el-row>



    <el-dialog
      title="资产类别关系维护"
      v-model="categoryRelationdialogFormVisible"
      style="text-align: left"
      width="550px"
    >
      <el-form
        :model="addRelationList"
        :disabled="isEditCategoryRelation"
        :rules="ruleRelation"
        label-width="120px"
        ref="addRelationListForm"
      >
        <el-row>
          <el-form-item
            label="关联方式"
            prop="association_mode"
          >
            <el-select
              v-model="addRelationList.association_mode"
              @change="changeMode"
              filterable
              style="width:21.3rem;"
            >
              <el-option
                :key="item.value"
                v-for="item in assList"
                :value="item.value"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item
            label="关系名称"
            prop="name"
          >
            <el-input
              v-model="addRelationList.name"
              autocomplete="off"
              style="width:21.3rem;"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="关系类型"
              prop="property"
            >
              <el-select v-model="addRelationList.property" style="width:21.3rem;" filterable>
                <el-option
                  :key="item.value"
                  v-for="item in relation"
                  :value="item.value"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="addRelationList.association_mode == 'instance'"
              label="值类型"
              prop="valueCategory"
            >
              <el-select
                v-model="addRelationList.valueCategory"
                @change="changeValueCategory"
                style="width:21.3rem;"
                filterable
              >
                <el-option
                  :key="item.value"
                  v-for="item in valueCategory"
                  :value="item.value"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item
            v-if="addRelationList.association_mode == 'property'"
            label="源属性"
            prop="src_property_code"
          >
            <el-select v-model="addRelationList.src_property_code" style="width:21.3rem;" filterable>
              <el-option
                :key="item.value"
                v-for="item in scodeList"
                :value="item.value"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="关联类别"
              prop="categoryIds"
              style="width:21.3rem;"
            >
              <el-tree-select :multiple="cateMultiple" v-model="addRelationList.categoryIds" :data="RelationCategoryExceptList" only-leaf-select  @change="changeCategory"
                              node-value="id" node-key="id" :props="{label:'label',children:'children'}" filterable></el-tree-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="是否允许编辑"
              prop="isEdit"
            >
              <el-radio-group v-model="addRelationList.isEdit">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item
            v-if="addRelationList.association_mode == 'property'"
            label="类别属性"
            prop="property_code"
          >
            <el-select v-model="addRelationList.property_code" style="width:21.3rem;" filterable>
              <el-option
                :key="item.value"
                v-for="item in pcodeList"
                :value="item.value"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item
            label="关系描述"

          >
            <el-input
              v-model="addRelationList.description"
              autocomplete="off"
              style="width:21.3rem;"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!isEditCategoryRelation" style="text-align:right;margin-top:10px;">
        <el-button
          @click="saveCategoryRelation()"
          v-show="isShow"
          type="primary"
        >确认</el-button
        >
        <el-button
          @click="resetRelationList()"
          v-show="isShow"
          type="primary"
        >重置</el-button
        >
        <el-button @click="closeRelation()" type="primary"
        >取消</el-button
        >
      </div>
    </el-dialog>


    <el-dialog :v-model="editRuleDialog">
      <el-row>
        <el-form
          label-width="90px"
          ref="editRuleFormRef"
          v-model="editRuleForm"
        >
          <el-col :span="12">
            <el-form-item label="自动生成规则" prop="ruleCodeName">
              <el-input
                type="text"
                :value="editRuleForm.ruleCodeName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生成方式" prop="ruleCodeSn">
              <el-select
                v-model="editRuleForm.ruleCodeSn"
                clearable
                filterable
              >
                <el-option
                  v-for="item in ruleCodeList"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div
        slot="footer"
        class="dialog-footer"
        style="text-align:right;"
      >
        <el-button
          type="primary"
          @click="saveRuleCode"
          style="background: #24468c;border-color: #24468c"
        >确定</el-button
        >
        <el-button
          type="primary"
          @click="closeRuleCode"
          style="background: #c6c8c9;border-color:#c6c8c9;"
        >取消
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="业务属性维护"
      v-model="editServiceModel"
      width="66%"
    >
      <el-form
        class="form"
        ref="serviceViewFormRef"
        :rules="ruleValidate"
        :model="serviceViewForm"
        label-width="125px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item prop="code" label="属性编码">
              <el-input
                v-model="serviceViewForm.code"
                @input="restBianma"
                placeholder="属性编码"
                :disabled="disableflag"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="name" label="属性名称">
              <el-input
                v-model="serviceViewForm.name"
                @input="restName"
                disabled
                placeholder="属性名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="state" label="属性状态">
              <el-input
                v-model="serviceViewForm.state"
                :readonly="true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="dataType" label="数据类型">
              <el-select
                v-model="serviceViewForm.dataType"
                @change="changeDataType"
                :disabled="dateTypeDisable"
              >
                <el-option
                  v-for="(item, index) in data_type_list"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="ocodeInput">
          <el-col :span="24">
            <el-form-item prop="ocode" label="对象生成编码">
              <el-select
                v-model="serviceViewForm.ocode"
                @change="changeOcode" disabled
              >
                <el-option
                  v-for="(item, index) in ocodeList"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="enumrow">
          <el-row>
            <el-col :span="16">
              <el-form-item
                prop="enumStr"
                label="枚举值"
                class="width100"
              >
                <el-input
                  v-model="serviceViewForm.enumStr"
                  :readonly="true"
                  placeholder="枚举值"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-button
                type="primary"
                size="large"
                class="saveBtn"
                @click="addEnum" disabled
              >添加
              </el-button>
            </el-col>
          </el-row>
        </div>
        <el-row>
          <div v-if="data_length">
            <el-col :span="12">
              <el-form-item prop="dataLength" :label="lengthName">
                <el-input-number
                  class="input"
                  v-model="serviceViewForm.dataLength"
                  :min="1"
                  :step="1"
                  :max="999999999999999"
                  style="width:30.3rem"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </div>
          <el-col :span="12">
            <el-form-item prop="showType" label="展示类型">
              <el-select
                v-model="serviceViewForm.showType"
                ref="show_show_type"
                @change="changeShowType"
              >
                <el-option
                  v-for="(item1, index) in show_type_list"
                  :value="item1.value"
                  :key="index"
                  :label="item1.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isCascade">
          <el-col :span="24">
            <el-form-item prop="isCascade" label="是否级联">
              <el-radio-group
                v-model="serviceViewForm.isCascade"
                @change="cascadeChange"
              >
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >{{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="cascadeInfo">
          <el-col :span="12">
            <el-form-item prop="cascode" label="级联属性">
              <el-select
                v-model="serviceViewForm.cascode"
                multiple
                filterable
              >
                <el-option
                  v-for="(item1, index) in cascodeList"
                  :value="item1.value"
                  :label="item1.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="casfun" label="级联方式">
              <el-select v-model="serviceViewForm.casfun">
                <el-option
                  v-for="(item1, index) in casList"
                  :value="item1.value"
                  :label="item1.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="property" label="录入方式">
              <el-select
                v-model="serviceViewForm.property"
                :disabled="luru"
                :clearable="true"
              >
                <el-option
                  v-for="(item, index) in propertyData"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item prop="propertyUnit" label="属性单位">
              <el-input
                v-model="serviceViewForm.propertyUnit"
                placeholder="属性单位"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="isAuto" label="是否自动生成">
              <el-radio-group
                v-model="serviceViewForm.isAuto"
                @change="shengcheng"
              >
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="rules">
            <el-form-item prop="autoRules" label="生成规则编码">
              <el-select v-model="serviceViewForm.autoRules">
                <el-option
                  v-for="(item, index) in autoRulesList"
                  :value="item.value"
                  :label="item.label"
                  :key="index"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="isReEdit" label="是否可再编辑">
              <el-radio-group v-model="serviceViewForm.isReEdit">
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="relationType" label="关联的资产类别">
              <el-input
                type="text"
                :disabled="true"
                v-model="serviceViewForm.relationType"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.dataType=='string'||serviceViewForm.dataType=='date'
                ||serviceViewForm.dataType=='dateTime'||serviceViewForm.dataType=='long'">
          <el-col :span="12">
            <el-form-item prop="isRule" label="是否开启校验">
              <el-radio-group
                v-model="serviceViewForm.isRule"

              >
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >{{item.label}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.isRule=='1'">
            <el-form-item prop="ruleType" label="校验方式">
              <el-select v-model="serviceViewForm.ruleType">
                <el-option value="el" label="EL表达式"></el-option>
                <el-option value="reg" label="正则表达式"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.isRule=='1'">
          <el-col :span="24">
            <el-row v-if="serviceViewForm.ruleType=='el'">
              <el-col :span="24" style="padding-left:130px;">
                <el-button size="small" circle @click="addRuleValue('#value')">属性</el-button>
                <el-button size="small" circle @click="addRuleValue('>')">&gt;</el-button>
                <el-button size="small" circle @click="addRuleValue('<')">&lt;</el-button>
                <el-button size="small" circle @click="addRuleValue('>=')">&gt;=</el-button>
                <el-button size="small" circle @click="addRuleValue('<=')">&lt;</el-button>
                <el-button size="small" circle @click="addRuleValue('==')">==</el-button>
                <el-button size="small" circle @click="addRuleValue('like')">包含</el-button>
                <el-button size="small" circle @click="addRuleValue('notLike')">不包含</el-button>
                <el-button size="small" circle @click="addRuleValue('(')">(</el-button>
                <el-button size="small" circle @click="addRuleValue(')')">)</el-button>
                <el-button size="small" circle @click="addRuleValue('&&')">并且</el-button>
                <el-button size="small" circle @click="addRuleValue('||')">或者</el-button>
                示例:(#value=="ac"||#value=="ab")&&#value.indexOf("a")>-1
              </el-col>
            </el-row>
            <el-row v-if="serviceViewForm.ruleType=='reg'">
              <el-col :span="24" style="padding-left:130px;">
                <el-button size="small" circle @click="addRuleRegValue('email')">邮件</el-button>
                <el-button size="small" circle @click="addRuleRegValue('ipv4')">IPV4</el-button>
                <el-button size="small" circle @click="addRuleRegValue('yuming')">域名</el-button>
                <el-button size="small" circle @click="addRuleRegValue('phone')">电话号码</el-button>
                <el-button size="small" circle @click="addRuleRegValue('telephone')">手机号码</el-button>
                <el-button size="small" circle @click="addRuleRegValue('shenfen')">身份证号</el-button>
                示例:^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item prop="ruleValue" label="表达式">
                  <el-input type="textarea" v-model="serviceViewForm.ruleValue" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>

          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="isEncryption" label="是否加密" v-if="serviceViewForm.showType=='password'">
              <el-radio-group v-model="serviceViewForm.isEncryption">
                <el-radio :label="'1'" key="0">是</el-radio>
                <el-radio :label="'0'" key="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="isViewPlaintext" label="查看明文密码" v-if="serviceViewForm.showType=='password'&&serviceViewForm.isEncryption=='1'">
              <el-radio-group v-model="serviceViewForm.isViewPlaintext">
                <el-radio :label="'1'" key="0">是</el-radio>
                <el-radio :label="'0'" key="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="encryptionMethod" label="加密方式" v-if="serviceViewForm.showType=='password'&&serviceViewForm.isEncryption=='1'">
              <el-select v-model="serviceViewForm.encryptionMethod" clearable filterable>
                <el-option v-for="(item,index) in encryptionMethodList" :value="item.value" :label="item.label" :key="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item prop="titleDesc" label="录入提示">
              <el-input
                v-model="serviceViewForm.titleDesc"
                type="textarea"
                placeholder="请输入录入提示..."
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="tooltipType" label="表格悬浮生成方式">
              <el-select clearable filterable v-model="serviceViewForm.tooltipType" @change="changeTooltipType">
                <el-option v-for="item in toolTipTypeList" :value="item.value" :label="item.label" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.tooltipType=='2'">
            <el-form-item prop="tooltipValue" label="生成规则">
              <el-select clearable filterable v-model="serviceViewForm.tooltipValue">
                <el-option v-for="item in tooltipRuleList" :value="item.value" :label="item.label" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.tooltipType=='3'">
          <el-col :span="24">
            <el-form-item prop="tooltipValue" label="固定值">
              <el-input
                v-model="serviceViewForm.tooltipValue"
                type="textarea"
                placeholder="请输入固定值..."
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.showType=='windowbox'||serviceViewForm.showType=='mul_windowbox'
||serviceViewForm.showType=='windowboxTree'||serviceViewForm.showType=='windowboxFilter'||serviceViewForm.showType=='windowboxTreeFilter'">
          <el-col :span="24">
            <el-form-item prop="isShowClear" label="是否启用清空按钮">
              <el-radio-group
                v-model="serviceViewForm.isShowClear"
              >
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >{{item.label}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="isHyperLink" label="是否超链接">
              <el-radio-group
                v-model="serviceViewForm.isHyperLink"
              >
                <el-radio
                  v-for="(item, index) in booleanList"
                  :label="item.value"
                  :key="index"
                >{{item.label}}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.isHyperLink=='1'">
            <el-form-item prop="hyperLinkType" label="超链接类型">
              <el-select v-model="serviceViewForm.hyperLinkType">
                <el-option value="url" label="链接"></el-option>
                <el-option value="menu" label="菜单"></el-option>
                <el-option value="custom" label="自定义"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.isHyperLink=='1'">
          <el-col :span="24"  v-if="serviceViewForm.hyperLinkType=='url'">
            <el-form-item prop="hyperLinkValue" label="链接URL">
              <el-input type="textarea" v-model="serviceViewForm.hyperLinkValue"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.hyperLinkType=='menu'">
            <el-form-item prop="hyperLinkValue" label="菜单标识">
              <el-input type="text" v-model="serviceViewForm.hyperLinkValue"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.hyperLinkType=='custom'">
            <el-form-item prop="hyperLinkValue" label="自定义编码">
              <el-input type="text" v-model="serviceViewForm.hyperLinkValue"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="serviceViewForm.hyperLinkType=='menu'||serviceViewForm.hyperLinkType=='custom'">
            <el-form-item prop="hyperLinkShow" label="链接展示方式">
              <el-select v-model="serviceViewForm.hyperLinkShow">
                <el-option v-if="serviceViewForm.hyperLinkType=='menu'" value="newMenu" label="新菜单"></el-option>
                <el-option v-if="serviceViewForm.hyperLinkType!='menu'" value="newDialog" label="弹窗"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.isHyperLink=='1'">
          <el-col :span="12" v-if="serviceViewForm.hyperLinkType=='url'">
            <el-form-item prop="hyperLinkShow" label="链接展示方式">
              <el-select v-model="serviceViewForm.hyperLinkShow">
                <el-option value="newPage" label="新页面"></el-option>
                <el-option value="newDialog" label="弹窗"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="serviceViewForm.isHyperLink=='1'">
          <el-col :span="16">
            <el-form-item
              prop="hyperLinkParam"
              label="参数"
              class="width100"
            >
              <el-input
                v-model="serviceViewForm.hyperLinkParam"
                :readonly="true"
                placeholder="参数"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button
              type="primary"
              size="large"
              @click="addHyperLinkParamMethod"
            >添加
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer">
        <el-row style="position:relative;">
          <el-col
            :span="24"
            style="text-align: center"
          >
            <el-button
              type="primary"
              size="large"
              class="saveBtn"
              @click="saveService()"
            >确定
            </el-button>

            <el-button
              type="primary"
              class="modal-btn"
              size="large"
              style="background-color: #dae3ee;border-color: #dae3ee;color:#333333"
              @click="closeService()"
            >取消
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>


    <el-dialog v-model = "editEnumModel"  title="添加枚举值" width="1200" style="z-index:1000;" append-to-body>
      <el-form class="form" v-for="(enumForm,index) in enumFormList" :key="index" label-width="90px">
        <el-row>
          <el-col :span="7">
            <el-form-item prop="'id'+index" label="序号(必填)">
              <el-input v-model="enumForm.id" type="number"
                        placeholder="序号"/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item prop="'value'+index" label="编码(必填)">
              <el-input v-model="enumForm.value"
                        placeholder="编码"/>
            </el-form-item>
          </el-col>
          <el-col :span="7">

            <el-form-item prop="'label'+index" label="名称(必填)">
              <el-input v-model="enumForm.label"
                        placeholder="名称"/>
            </el-form-item>
          </el-col>


          <el-col :span="3">
            <!--<i-input v-model="enumForm.value"  placeholder="编码"/>   -->
            <el-button type="primary" class="new-btn" size="large" @click="removeForm(index)">移除</el-button>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer">
        <el-row>
          <el-col :span="6">&nbsp;&nbsp;</el-col>
          <el-col :span="4">
            <el-button type="primary" size="large" class="modal-btn"
                       @click="dataEnums()" style="background: #4178f5;border-color: #4178f5">动态加载
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="large" class="modal-btn"
                       @click="addForm()" style="background: #4178f5;border-color: #4178f5">添加行
            </el-button>
          </el-col>
          <el-col :span="4">
            <el-button type="primary" size="large" class="modal-btn"
                       @click="saveEnum()" style="background: #4178f5;border-color: #4178f5">确定
            </el-button>
          </el-col>
          <el-col :span="6">&nbsp;&nbsp;</el-col>
        </el-row>
      </div>
    </el-dialog>


    <el-dialog
      title="添加属性"
      v-model="addPropertyDialogFormVisible"
    >
      <div>
        <el-input
          placeholder="属性名称"
          v-model="peopertyNameInput"
          style="width:60;margin:0 auto;"
        >
          <template #append>
            <el-button :icon="useRenderIcon('EP-Search')"   @click="queryPropertyByName" />
          </template>
        </el-input>
        <avue-crud :data="propertyList" :option="addProOption" @selection-change="handleSelectionChange" v-model:page="state.pagination1"
                   @size-change="handleSizeChange" :table-loading="addProLoading"
                   @current-change="handleCurrentChange">
        </avue-crud>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align:right">
        <el-button @click="saveProperties" type="primary">确认</el-button>
        <el-button @click="resetPropertyList" type="primary">重置</el-button>
        <el-button @click="addPropertyDialogFormVisible = false" type="primary"
        >取消</el-button
        >
      </div>
    </el-dialog>


    <el-dialog
      v-model="modal1" width="700"
      title="动态加载">
      <el-row>
        <el-col :span="4" style="padding-top:5px;text-align:right;">生成规则：</el-col>
        <el-col :span="20"> <el-select
          v-model="enumCode"
        >
          <el-option
            v-for="(item,index) in codeList"
            :value="item.value" :label="item.label" :key="index">
          </el-option>
        </el-select></el-col>
      </el-row>
      <div slot="footer">
        <el-row>
          <el-col :span="3">&nbsp;&nbsp;</el-col>
          <el-col :span="6">
            <el-button type="primary" size="large" class="modal-btn"
                       @click="ok" style="background: #24468c;border-color: #24468c">确认
            </el-button>
          </el-col>
          <!--<i-button type="primary" size="large"-->
          <!--@click="relationCata">关联资产类别-->
          <!--</i-button>-->
          <el-col :span="6">
            <el-button type="primary" class="modal-btn" size="large" style="background: #c6c8c9;border-color:#c6c8c9;"
                       @click="resetLoad">重置
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" class="modal-btn" size="large" style="background: #c6c8c9;border-color:#c6c8c9;"
                       @click="closeLoad">取消
            </el-button>
          </el-col>
          <el-col :span="3">&nbsp;&nbsp;</el-col>
        </el-row>

      </div>
    </el-dialog>




    <el-dialog v-model="defaultValueShow" title="默认值维护" @close="closeDefaultValue">
      <el-form ref="defaultValueFormRef" :model="defaultValueForm" label-width="120px">
        <el-form-item label="默认值方式">
          <el-radio-group v-model="defaultValueForm.default_type" @change="defaultTypeChange">
            <el-radio :value="'1'">固定值</el-radio>
            <el-radio :value="'2'" v-if="defaultValueShowType!='dateTime'&&defaultValueShowType!='date'
&&defaultValueShowType!='number'&&defaultValueShowType!='checkbox'&&defaultValueShowType!='radio'&&defaultValueShowType!='cascader'
&&defaultValueShowType!='objectTable'&&defaultValueShowType!='table'">动态生成</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="生成方式" v-if="defaultValueForm.default_type=='2'">
          <el-select v-model="defaultValueForm.default_value" filterable>
            <el-option v-for="item in defaultRuleList" :label="item.label" :value="item.value" :key="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="defaultValueForm.default_type=='1'"
                      label="值"
        >
          <!--普通输入框 input （string，number）-->
          <span v-if="defaultValueShowType == 'input' || defaultValueShowType == 'telephone'|| defaultValueShowType == 'textarea' || defaultValueShowType == 'email' || defaultValueShowType == 'phone' || defaultValueShowType == 'snmp_addr'">  <!--数据类型 input、number、text、select、checkbox、radio-->
			                               <span >
			                                    <el-input v-model="defaultValueForm.default_value"
                                                    :placeholder="defaultValueName" clearable></el-input>
			                               </span>
			                           </span>
          <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
          <span v-if="defaultValueShowType == 'dateTime' || defaultValueShowType == 'date'">
            <el-date-picker clearable v-model="defaultValueForm.default_value" type="datetime"
                            placeholder="请选择日期"
            />

			                           </span>
          <span v-if="defaultValueShowType=='number'">
                                  <el-input-number v-model="defaultValueForm.default_value"></el-input-number>
                               </span>
          <span v-if="defaultValueShowType == 'checkbox'">
			                                <el-checkbox-group v-model="defaultValueForm.default_value"  >
			                                    <el-checkbox :label="cn.value"
                                                    v-for="(cn,index) in defaultValueList" :key="cn.value">{{cn.label}} </el-checkbox>

			                                </el-checkbox-group>
			                           </span>
          <!--单选-->
          <span v-if="defaultValueShowType == 'radio'">
			                                 <el-radio-group v-model="defaultValueForm.default_value"  >
			                                    <el-radio :value="cn.value"
                                                    v-for="(cn,index) in defaultValueList" :key="index">{{cn.label}}</el-radio>
			                                </el-radio-group>
			                           </span>
          <span v-if="defaultValueShowType == 'cascader'">
				                                   <el-cascader :options="defaultValueList" clearable    v-model="defaultValueForm.default_value"
                                           ></el-cascader>
			                           </span>
          <span v-if="defaultValueShowType == 'windowbox'">
                    <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
                    <el-input v-model="defaultValueForm.defaultShowValue" readonly  clearable
                              :placeholder="defaultValueName">
                                  <el-button slot="prepend" icon="el-icon-search" @click="openSelect(defaultValueObj)"></el-button>
                                  <el-button slot="append" icon="el-icon-close" @click="clearSelect()"></el-button>
                                </el-input>
			                               </span>

          <span v-if="defaultValueShowType == 'mul_windowbox'">
			                                  <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
			                                   <el-input v-model="defaultValueForm.defaultShowValue" readonly  clearable
                                                   :placeholder="defaultValueName">
                                  <el-button slot="prepend" icon="el-icon-search" @click="openMulSelect(defaultValueObj)"></el-button>
                                  <el-button slot="append" icon="el-icon-close" @click="clearMulSelect()"></el-button>
                                </el-input>
			                           </span>
          <!-- 复选下拉-->

          <span v-if="defaultValueShowType == 'mul_combobox'">
			                               <el-select placement="top" filterable
                                                multiple clearable
                                                v-model="defaultValueForm.default_value">
			                                            <el-option
                                                    v-for="(en,index) in defaultValueList"
                                                    :value="en.value" :key="index">{{ en.label }}</el-option>
			                                        </el-select>
			                           </span>

          <span v-if="defaultValueShowType == 'comboTree'">
                                    <el-tree-select filterable :props="{'label':'title','children':'children'}" :data="defaultValueList"   style="width:100%" clearable
                                                 v-model="defaultValueForm.default_value" node-key="id"  >
                                </el-tree-select>
			                           </span>
          <span v-if="defaultValueShowType == 'combobox'">
			                               <el-select  filterable clearable
                                                 v-model="defaultValueForm.default_value"
                                     >
			                                           <el-option
                                                   v-for="(en,index) in defaultValueList" :key="index"
                                                   :value="en.value" :label="en.label">
			                                           </el-option>
			                                       </el-select>
			                              </span>
          <span v-if="defaultValueShowType == 'windowboxTree'">
                    <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
                    <el-input v-model="defaultValueForm.defaultShowValue" readonly  clearable
                              :placeholder="defaultValueName">
                                  <el-button slot="prepend" icon="el-icon-search" @click="openTreeSelect(defaultValueObj)"></el-button>
                                  <el-button slot="append" icon="el-icon-close" @click="clearTreeSelect()"></el-button>
                                </el-input>
			                               </span>
          <span v-if="defaultValueShowType=='windowboxFilter'">
                      <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                        <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                          <el-button icon="el-icon-search"
                                     @click="openFilterSelect(defaultValueObj)"></el-button>
                        </div>
                        <div style="display: table-cell;">
                          <el-select style="width:100%"
                                     v-model="defaultValueForm.default_value"
                                     multiple
                                     clearable
                                     filterable
                                     remote
                                     reserve-keyword
                                     placeholder="请输入关键词"
                                     @focus="(val)=>clearTableList(val,defaultValueObj)"
                                     :remote-method="(vul)=>remoteMethod(vul,defaultValueObj)">
                            <el-option v-for="item1 in windowboxFilterTable"
                                       :key="item1.value"
                                       :label="item1.label"
                                       :value="item1.value">
                            </el-option>
                          </el-select>
                        </div>
                        <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                          <el-button icon="el-icon-close"
                                     @click="clearSelect()"></el-button>
                        </div>
                      </div>
                    </span>
          <span v-if="defaultValueShowType=='inputSelect'">
                    <el-input-tag v-model="defaultValueForm.default_value"></el-input-tag>
                  </span>
          <span v-if="defaultValueShowType == 'moreInput'">  <!--数据类型 input、number、text、select、checkbox、radio-->
                                       <el-input v-model="defaultValueForm.default_value"
                                       ></el-input>
			                           </span>
          <span v-if="defaultValueShowType=='windowboxTreeFilter'">
                      <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                        <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                          <el-button icon="el-icon-search"
                                     @click="openWindowTree(defaultValueObj)"
                          ></el-button>
                        </div>
                        <div style="display: table-cell;">
                          <el-select v-model="defaultValueForm.default_value"
                                     style="width:100%"
                                     multiple
                                     filterable
                                     clearable
                                     remote
                                     reserve-keyword
                                     placeholder="请输入关键词"
                                     @focus="(val)=>clearTreeTableList(val,defaultValueObj)"
                                     :remote-method="(vul)=>remoteTreeMethod(vul,defaultValueObj)">
                            <el-option v-for="item1 in windowboxFilterTable"
                                       :key="item1.value"
                                       :label="item1.label"
                                       :value="item1.value">
                            </el-option>
                          </el-select>
                        </div>
                        <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                          <el-button icon="el-icon-close"
                                     @click="clearWindowTree(defaultValueObj)"></el-button>
                        </div>
                      </div>

                    </span>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="saveDefaultValue">保存</el-button>
        <el-button type="primary" @click="cancelDefaultValue">取消</el-button>
      </div>
    </el-dialog>


    <el-dialog v-model="selectDataModal"
               width="800"
               append-to-body
               class="otherTable"
               title=" "
               @on-cancel="closePropSelect">
      <el-row>
        <el-col :span="20">
          <el-input v-model="propValue"
                    clearable
                    placeholder="值筛选"></el-input>
        </el-col>
        <el-col :span="2"
                style="text-align: center">
          <el-button type="primary"
                     class="new-btn"
                     size="small"
                     @click="queryValue">
            查询
          </el-button>
        </el-col>
        <el-col :span="2"
                style="text-align: center">
          <el-button type="primary"
                     class="new-btn"
                     size="small"
                     @click="resetValue"
                     style="background-color: #c6c8c9;border-color: #c6c8c9">
            重置
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="data"
                ref="windowTable"
                stripe
                v-loading="windowboxloading"
                @selection-change="handleSelectionChange"
                @row-click="handleRowClick"
                element-loading-background="rgba(0, 0, 0, 0.5)"
                element-loading-text="数据正在加载中"
                style="width: 100%"
                height="300px">
        <el-table-column type="selection"></el-table-column>
        <el-table-column v-for="(item,index) in columns"
                         :prop="item.key"
                         :label="item.title"
                         :key="index"
                         :show-overflow-tooltip="true">
        </el-table-column>
      </el-table>
      <el-pagination :current-page="currentPageW"
                     :page-size="pageSizeW"
                     :page-sizes="[5, 10, 20, 50]"
                     :total="totalW"
                     @current-change="handleCurrentChangeW"
                     @size-change="handleSizeChangeW"
                     layout="total, sizes, prev, pager, next, jumper">
      </el-pagination>
      <div slot="footer">
        <el-row>
          <el-col :span="24"
                  style="text-align: center">
            <el-button type="primary"
                       @click="saveSelect"
                       class="modal-btn"
                       style="background: #24468c;border-color: #24468c">确定
            </el-button>
            <el-button type="primary"
                       @click="closePropSelect"
                       class="modal-btn"
                       style="background: #c6c8c9;border-color:#c6c8c9;">取消
            </el-button>
          </el-col>
        </el-row>

      </div>
    </el-dialog>

    <el-dialog v-model="selectMulDataModal" v-if="selectMulDataModal" width="800" append-to-body
               class="otherTable" title=" " @on-cancel="closePropSelectMul">
      <el-row>
        <el-col :span="20">
          <el-input v-model="propValue" clearable placeholder="值筛选"></el-input>
        </el-col>
        <el-col :span="2" style="text-align: center">
          <el-button type="primary" class="new-btn" size="small" @click="queryMulValue">
            查询
          </el-button>
        </el-col>
        <el-col :span="2" style="text-align: center">
          <el-button type="primary" class="new-btn" size="small" @click="resetMulSelect" style="background-color: #c6c8c9;border-color: #c6c8c9">
            重置
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="mulData" ref="windowMulTable" stripe v-loading="windowboxloading" @selection-change="handleSelectionMulChange"
                element-loading-background="rgba(0, 0, 0, 0.5)"
                element-loading-text="数据正在加载中"
                row-key="value"
                style="width: 100%" height="300px">
        <el-table-column type="selection" reserve-selection></el-table-column>
        <el-table-column v-for="(item,index) in mulColumns"
                         :prop="item.key"
                         :label="item.title"
                         :key="index"
                         :show-overflow-tooltip="true"
        >
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentMulPageW"
        :page-size="pageMulSizeW"
        :page-sizes="[5, 10, 20, 50]"
        :total="totalMulW"
        style="text-align: right;"
        @current-change="handleCurrentMulChangeW"
        @size-change="handleSizeMulChangeW"
        layout="total, sizes, prev, pager, next, jumper"
      >
      </el-pagination>
      <div slot="footer">
        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button type="primary"
                       @click="saveSelectMul" class="modal-btn" style="background: #24468c;border-color: #24468c">确定
            </el-button>
            <el-button type="primary"
                       @click="closePropSelectMul" class="modal-btn" style="background: #c6c8c9;border-color:#c6c8c9;">取消
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <el-dialog v-model="windowTreeDialog" :modal="false" fullscreen append-to-body destroy-on-close style="z-index:99999;">
      <windowBoxTree :item="treeItem" v-if="windowTreeDialog" @closeWindowTree="closeWindowTree"></windowBoxTree>
    </el-dialog>



  </div>
</template>
<script setup lang="ts">
import {ElMessageBox,ElMessage,FormRules} from "element-plus";
 import {toRefs,ref,nextTick,onMounted,reactive} from "vue";
 import { pageSizeOptions} from "@/utils/page_util";
 import windowBoxTree from "@/views/modules/eam/zcgl/instance/source/component/windowBoxTree.vue";
import {
  queryPropertiesByCategoryIdAxios,
  queryCategoryRelationByCategoryIdAxios,
  queryBeCategoryRelationByCategoryIdAxios,
  deleteCrpUrlAxios,
  deleteCategoryPropertyAxios,
  deleteCategoryRelationByIdAxios,
  getCategoryBeRelationByIdAxios,
  queryPropertyByIdAxios,
  categoryTreeoAxios,
  categoryRelationVerifyNameAxios,
  saveCategoryRelationAxios,
  queryPropertyGxhDefaultByIdAxios,
  saveDefaultGxhValueAxios,
  saveEditRuleAxios,
  getCategoryRelationByIdAxios,
  queryNonPropertyByIdAxios,
  savePropertyAxios
} from "@/views/modules/eam/zcgl/instance/source/api/categoryModelInterface";

import {
  initShowTypeByOcodeMethod,
  initShowTypeMethod,
  validataProName,
  checkRuleElTypeAxios,
  validataProCode,
  saveCategoryProperty,
  queryEnumListDe,
  queryEnumCodeList,
  initPropertyVerifyDelMethod,
  initComboboxMethod
} from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
 import {queryEnumList} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {useRenderIcon} from "@/components/ReIcon/src/hooks";

 const props = defineProps({
   categoryData:Object
 })
 const state = reactive({
   addPropertyDialogFormVisible: false,
   selectIds:[],
   windowboxFilterTable:[],
   defaultValueShow:false,
   defaultValueShowType:"input",
   defaultValueName:"",
   defaultValueList:[],
   defaultValue1:"",
   defaultValue2:"",
   defaultRuleList:[],
   defaultValueForm:{
     id:"",
     default_type:"1",
     default_value:"" || undefined,
     defaultShowValue: ""
   },
   defaultValueObj:{},
   activeName: 'first',
   tableFilter: '',
   tableData:[],
   pcodeList: [],
   propColumn:[
     {
       prop:"name",
       label:"属性名称",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"code",
       label:"属性编码",
       align:"center",
       showOverflowTooltip:true
     },
     {
       prop:"dataType",
       label:"数据类型",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"dataLength",
       label:"数据长度",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"type",
       label:"录入方式",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"isEdit",
       label:"是否可再编辑",
       align:"center",
       showOverflowTooltip:true
     },{
       label:"操作",
       prop:"propSlot",
       align:"center",
       fixed:"right"
     }
   ],
   relationColumn:[
     {
       prop:"name",
       label:"关系名称",
       align:"center",
       showOverflowTooltip:true
     },
     {
       prop:"association_mode",
       label:"关联方式",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"src_property_code",
       label:"源属性",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"relation_category",
       label:"关联类别",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"property_code",
       label:"类别属性",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"property",
       label:"关系类型",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"isEdit",
       label:"是否可编辑",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"valueCategory",
       label:"值类型",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"relationSlot",
       label:"操作",
       align:"center",
       fixed:"right"
     }
   ],
   beRelationColumn:[
     {
       prop:"name",
       label:"关联名称",
       align:"center",
       showOverflowTooltip:true
     },
     {
       prop:"association_mode",
       label:"关联方式",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"relation_category",
       label:"源关联类别",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"src_property_code",
       label:"源类别属性",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"property_code",
       label:"被关联属性",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"property",
       label:"关系类型",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"isEdit",
       label:"是否可编辑",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"valueCategory",
       label:"值类型",
       align:"center",
       showOverflowTooltip:true
     },{
       prop:"beRelationSlot",
       label:"操作",
       align:"center",
       fixed:"right"
     }
   ],
   addProLoading:false,
   addProOption:{
     index: false,
     indexLabel: '序号',
     align: "center",
     menuAlign: "center",
     selection:true,
     selectionFixed:true,
     refreshBtn:false,
     columnBtn:false,
     gridBtn:false,
     menu: false,
     border: true,
     stripe: true,
     addBtn: false,
     editBtn: false,
     delBtn: false,
     menuWidth: 130,
     column: [
       {
         prop:"name",
         label:"属性名称",
         align:"center",
         showOverflowTooltip:true
       },{
         prop:"code",
         label:"属性编码",
         align:"center",
         showOverflowTooltip:true
       },
       {
         prop:"interaction",
         label:"展示类型",
         align:"center",
         showOverflowTooltip:true
       }
     ]
   },
   tableOption:{
     index: false,
     indexLabel: '序号',
     align: "center",
     menuAlign: "center",
     maxHeight:560,
     selection:true,
     selectionFixed:true,
     refreshBtn:false,
     columnBtn:false,
     gridBtn:false,
     menu: false,
     border: true,
     stripe: true,
     addBtn: false,
     editBtn: false,
     delBtn: false,
     menuWidth: 130,
     column: [
       {
         prop:"name",
         label:"属性名称",
         align:"center",
         showOverflowTooltip:true
       },{
         prop:"code",
         label:"属性编码",
         align:"center",
         showOverflowTooltip:true
       },
       {
         prop:"dataType",
         label:"数据类型",
         align:"center",
         showOverflowTooltip:true
       },{
         prop:"dataLength",
         label:"数据长度",
         align:"center",
         showOverflowTooltip:true
       },{
         prop:"type",
         label:"录入方式",
         align:"center",
         showOverflowTooltip:true
       },{
         prop:"isEdit",
         label:"是否可再编辑",
         align:"center",
         showOverflowTooltip:true
       },{
         label:"操作",
         prop:"propSlot",
         align:"center",
         fixed:"right"
       }
     ]
   },
   pagination: {
     total: 1,
     currentPage: 1,
     pageSize: 10,
     pageSizes: pageSizeOptions,
   },
   pagination1: {
     total: 1,
     currentPage: 1,
     pageSize: 10,
     pageSizes: pageSizeOptions,
   },
   tableLoading:false,
   scodeList:[],
   cateMultiple:false,
   addRelationList: {
     id:"",
     code:"",
     name: "",
     isEdit: true,
     association_mode: "",
     categoryIds: []||"",
     sourceCategoryId: "",
     description: "",
     property_code: "",
     src_property_code: "",
     property: "",
     valueCategory:""
   },
   categoryRelationdialogFormVisible:false,
   isEditCategoryRelation:false,
   isShow:false,
   assDisabled:false,
   propertyCodeActiveName:false,
   instanceCodeActiveName:false,
   RelationPropertyList:[],
   RelationCategoryExceptList:[],
   assList: [
     {
       label: "属性关联",
       value: "property",
     },
     { label: "实例关联", value: "instance" },
   ],
   relation: [
     {
       value: "fatherAndSon",
       label: "父子",
     },
     {
       value: "connection",
       label: "连接",
     },
     {
       value: "constitute",
       label: "构成",
     },
     {
       value: "other",
       label: "其他",
     },
   ],
   radios: [
     { value: "enable", label: "启用" },
     {
       value: "disable",
       label: "禁用",
     },
   ],
   valueCategory: [
     {
       value: "single",
       label: "关联单个实例",
     },
     {
       value: "multiple",
       label: "关联多个实例",
     },
   ],
   windowboxloading:false,
   currentPageW:1,
   pageNumW:1,
   pageSizeW:10,
   totalW:0,
   rule_pro: {},
   currentItem: null,
   windowSelect:null,
   ocode:'',
   currentId:'',
   data:[],
   selectDataModal:false,
   toolTipTypeList:[
     { value: "0", label: "关闭" },
     { value: "1",label: "当前值"},
     { value: "2", label: "规则生成" },
     { value: "3",label: "固定值"},
   ],
   tooltipRuleList:[],
   windowTreeDialog:false,
   treeItem:{},
   tableHeight:295,
   selectMulDataModal:false,
   show_type:'',
   mulCurrentItem:{},
   currentMulItem:null,
   currentMulId:null,
   mulOcode:"",
   mulShow_type:"",
   mulData:[],
   totalMulW:0,
   pageMulSizeW:10,
   currentMulPageW:1,
   windowMulSelect:[],
   propValue:'',
   currentLabel:'',
   mulColumns: [
     {
       title: "选择",
       key: "value",
       width: 70,
       type: 'selection'
     },
     {title: '值', key: 'label'}
   ],
   columns: [
     {
       title: "选择",
       width: 70,
       key: "value",

       render: (h, params) => {
         let id = params.row.value;
         let defaultS = false;
         if (state.currentId == id) {
           defaultS = true;
         } else {
           defaultS = false
         }
         return h('Radio', {
           props: {
             value: defaultS  //判断单选框状态
           },

           on: {
             'on-change': () => {
               state.currentId = id; //赋值单选框id。对比id判断状态
               state.currentLabel = params.row.label;
             }
           }
         })
       },
     }, {title: '值', key: 'label'}
   ],
   queryCondition:"",
   editRuleDialog: false,
   editRuleForm: {
     ruleCodeSn: "",
     ruleCodeName: "",
   },
   enumrow:false,
   ruleCodeList: [],
   editServiceModel: false,
   lengthName: "数据长度",
   disableflag: false,
   cascadeInfo:false,
   cascodeList: [],
   enumCode:"",
   editEnumModel:false,
   rules:false,
   modal1:false,
   propertyData: [],
   show_type_list: [],
   autoRulesList: [],
   booleanList: [
     { value: "1", label: "是" },
     {
       value: "0",
       label: "否",
     },
   ],
   luru:false,
   casList: [],
   enumFormList:[{
     index:0,
     id:'',
     label:'',
     value:''
   }],
   isCascade: false,
   ocodeInput:false,
   data_length: false,
   dateTypeDisable: false,
   index:0,
   oldName:'',
   addHyperLinkParam:false,
   hyperLinkFormList:[{
     index:0,
     label:'',
     value:''
   }],
   hyperIndex:0,
   encryptionMethodList:[],
   data_type_list:[],
   ocodeList:[],
   peopertyNameInput: "",
   propertyList: [],
   codeList:[],
 })

 const {activeName,tableFilter,tableData,tableOption,tableLoading,categoryRelationdialogFormVisible,
   isEditCategoryRelation,addRelationList,scodeList,assList,relation,valueCategory,cateMultiple,
   pcodeList,isShow,defaultValueShow,defaultValueShowType,defaultValueForm,defaultValueName,defaultValueList,defaultRuleList,
   defaultValueObj,windowboxloading,currentPageW,pageSizeW,totalW,data,selectDataModal,
   windowboxFilterTable,propValue,columns,editRuleDialog,editRuleForm,enumrow,ruleCodeList,
   editServiceModel,lengthName,disableflag,cascadeInfo,booleanList,propertyData,enumFormList,
   encryptionMethodList,toolTipTypeList,tooltipRuleList,autoRulesList,casList,cascodeList,show_type_list,dateTypeDisable,editEnumModel,
   modal1,isCascade,data_type_list,ocodeInput,ocodeList,data_length,luru,rules,addPropertyDialogFormVisible,peopertyNameInput,propertyList,
enumCode,codeList,selectMulDataModal,mulData,mulColumns,currentMulPageW,RelationCategoryExceptList,
   pageMulSizeW,totalMulW,windowTreeDialog,treeItem,addProOption,addProLoading
 } = toRefs(state);
 const queryFilterTable = () =>{
    state.pagination.currentPage = 1;
    initTableData();
 }
 const handleClick = (tab) =>{
   state.activeName = tab.props.name;
   initTableColumn();
   initTableData();
   // state.pagination.currentPage = 1;
   // initTableData();
 }
 const handlePageSizeChange = (pageSize)=>{
   state.pagination.pageSize = pageSize;
   state.pagination.currentPage = 1;
   initTableData();
 }
 const handlePageCurrentChange = (currentPage) =>{
   state.pagination.currentPage = currentPage;
   initTableData();
 }
 const initTableColumn = () =>{
   state.tableOption.column.length = 0;
   if(state.activeName == 'first'){
     state.tableOption.column.push(...state.propColumn);
   }else if(state.activeName == 'second'){
     state.tableOption.column.push(...state.relationColumn);
   }else{
     state.tableOption.column.push(...state.beRelationColumn);
   }
 }
 const serviceViewForm = reactive({
   id: "",
   name: "",
   property: "not_must",
   propertyUnit: "",
   enumStr: "",
   dataLength: 255,
   isReEdit: "0",
   code: "",
   description: "",
   state: "",
   relationType: "",
   relationIds: "",
   dataType: "",
   relationStatus: "",
   isAuto: "0",
   cascode: [],
   casfun: "",
   isCascade: "",
   autoRules: "",
   ocode: "",
   titleDesc: "",
   showType: "",
   isRule:"0",
   ruleType:"",
   ruleValue:"",
   tooltipType:"",
   tooltipValue:"",
   isShowClear:"0",
   isHyperLink:"0",
   hyperLinkType:"",
   hyperLinkValue:"",
   hyperLinkParam:"",
   hyperLinkShow:"",
   isEncryption:"0",
   isViewPlaintext:"0",
   encryptionMethod:null
 })
const changeDataType = (value) => {
  if(value=='string'||value=='long'||value=='date'||value=='dateTime'){
  }else{
    serviceViewForm.isRule="0";
  }
  if (value == "object") {
    state.ocodeInput = true;
  } else {
    state.ocodeInput = false;
  }
  if (value == "enum") {
    state.enumrow = true;
    state.data_length = false;
  } else {
    state.enumrow = false;
    if (value == "date" || value == "dateTime") {
      state.data_length = false;
    } else {
      state.data_length = true;

      if (value == "long") {
        state.lengthName = "最大值";
        serviceViewForm.dataLength = 5000;
      } else {
        serviceViewForm.dataLength = 200;
        state.lengthName = "数据长度";
      }
    }
    serviceViewForm.enumStr = "";
  }

  if (value == undefined || value == "") {
    state.show_type_list = [];
  } else {
    initShowTypeMethod(value).then(res => {
      let data = res["data"];
      if (typeof data == "string") {
        data = eval("(" + data + ")");
      }
      state.show_type_list = data;
      if (state.show_type_list.length > 0) {
        //serviceViewForm.showType = current.show_type_list[0].value;
        if (serviceViewForm.showType) {
          serviceViewForm.showType = state.show_type_list[0].value;
          if(state.show_type_list[0].value=='combobox'||state.show_type_list[0].value=='windowbox'){
            state.isCascade=true;
          }else{
            state.isCascade=false;
          }
        } else {
          let flag = false;
          for (let t = 0; t < state.show_type_list.length; t++) {
            if (state.show_type_list[t].value == serviceViewForm.showType) {
              flag = true;
              if(state.show_type_list[t].value=='combobox'||state.show_type_list[t].value=='windowbox'){
                state.isCascade=true;
              }else{
                state.isCascade=false;
              }
              break;
            }
          }
          if (!flag) {
            serviceViewForm.showType = state.show_type_list[0].value;
            if(serviceViewForm.showType=='combobox'||serviceViewForm.showType=='windowbox'){
              state.isCascade=true;
            }else{
              state.isCascade =false;
            }
          }
        }
        changeShowType(serviceViewForm.showType);
      }
    });
  }
}
const changeOcode = (value) => {
  if (!value) {
    return;
  }
  initShowTypeByOcodeMethod(value).then(res => {
    state.show_type_list = res["data"];
    if (state.show_type_list.length > 0) {
      serviceViewForm.showType = state.show_type_list[0].value;
      // console.log(current.$refs.show_show_type.value);
      changeShowType(state.show_type_list[0].value);
    }
  });
}
const changeShowType = (value) => {
  serviceViewForm.showType =value;
  if (value == "combobox" || value == "windowbox"||value=='comboTree') {
    state.isCascade = true;
    if (
      serviceViewForm.isCascade == null ||
      serviceViewForm.isCascade == ""
    ) {
      serviceViewForm.isCascade = "0";
      cascadeChange(serviceViewForm.isCascade);
    }
  } else {
    state.isCascade = false;
    serviceViewForm.isCascade = "0";
    cascadeChange(serviceViewForm.isCascade);
  }
}

const checkName = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请填写属性名称!"));
  } else {
    //console.log(vue.serviceViewForm.name);
    if (serviceViewForm.name != state.oldName) {
      validataProName(serviceViewForm).then(res => {
        if (res.data.status != "success") {
          callback(new Error("属性名称已存在，请修改！"));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  }
};
const checkCode = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请填写属性编码!"));
  } else {
    if (!serviceViewForm.id) {
      validataProCode(serviceViewForm).then(res => {
        if (res.data.status != "success") {
          callback(new Error("属性编码已存在，请修改！"));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  }
};
const checkProperty = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请选择录入方式!"));
  } else {
    callback();
  }
};
const checkDataType = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请选择数据类型!"));
  } else {
    callback();
  }
};
const checkDataLength = (rule, value, callback) => {
  if (
    serviceViewForm.dataType != "enum" &&
    serviceViewForm.dataType != "date" &&
    serviceViewForm.dataType != "dateTime"
  ) {
    if (value == "undefined" || value == null || value == "") {
      callback(new Error("请填写数据长度!"));
    } else if (value <= 0) {
      callback(new Error("数据长度必须大于0"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkShowType = (rule, value, callback) => {
  value = serviceViewForm.showType;
  if (!value) {
    callback(new Error("请选择展示类型!!!"));
  } else {
    callback();
  }
};
const checkEnumStr = (rule, value, callback) => {
  if (serviceViewForm.dataType == "enum") {
    if (!value) {
      callback(new Error("请添加枚举值!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkOcode = (rule, value, callback) => {
  if (serviceViewForm.dataType == "object") {
    if (!value) {
      callback(new Error("请输入对象生成编码!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkAutoRules = (rule, value, callback) => {
  if (serviceViewForm.isAuto == "1") {
    if (!value) {
      callback(new Error("请输入规则编码!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkIsCascade = (rule, value, callback) => {
  if (
    serviceViewForm.showType == "combobox" ||
    serviceViewForm.showType == "windowbox"
  ) {
    if (!value) {
      callback(new Error("请选择是否级联!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkCascode = (rule, value, callback) => {
  if (serviceViewForm.isCascade == "1") {
    if (!value) {
      callback(new Error("请选择级联属性!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkCasFun = (rule, value, callback) => {
  if (serviceViewForm.isCascade == "1") {
    if (!value) {
      callback(new Error("请选择级联规则!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkRuleValue = (rule,value,callback) => {
  if(serviceViewForm.isRule=='1'){
    if (!value) {
      callback(new Error('请输入表达式!'));
    } else {
      if(serviceViewForm.ruleType=='el'){
        checkRuleElTypeAxios(serviceViewForm).then(res=>{
          if(res.data=="ok"){
            callback();
          }else{
            callback(new Error("EL表达式格式错误，请检查表达式"));
          }
        })
      }else if(serviceViewForm.ruleType=='reg'){
        try{
          let flag = eval("/"+value+"/") instanceof RegExp;
          if(!flag){
            callback(new Error('正则表达式格式错误，请检查表达式!'));
          }else{
            callback();
          }
        }catch(e){
          console.log(e);
          callback(new Error('正则表达式格式错误，请检查表达式!'));
        }
      }else{
        callback();
      }

    }
  }else{
    callback();
  }
};
const checkRuleType = (rule,value,callback) => {
  if(serviceViewForm.isRule=='1'){
    if (!value) {
      callback(new Error('请选择校验类型!'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkTooltipValue = (rule,value,callback) => {
  if(serviceViewForm.tooltipType=="2"){
    if (!value) {
      callback(new Error('请选择生成规则!'));
    } else {
      callback();
    }
  }else if(serviceViewForm.tooltipType=="3"){
    if (!value) {
      callback(new Error('请填写固定值!'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkHyperLinkType = (rule,value,callback) => {
  if(serviceViewForm.isHyperLink=='1'){
    if (!value) {
      callback(new Error('请选择链接类型!'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkHyperLinkValue = (rule,value,callback) => {
  if(serviceViewForm.isHyperLink=='1'){
    if (!value) {
      if(serviceViewForm.hyperLinkType=='url'){
        callback(new Error('请填写链接地址!'));
      }else if(serviceViewForm.hyperLinkType=='menu'){
        callback(new Error('请填写菜单标识!'));
      }else if(serviceViewForm.hyperLinkType=='custom'){
        callback(new Error('请填写自定义编码'));
      }else{
        callback(new Error('请填写链接地址!'));
      }
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkHyperLinkShow = (rule,value,callback) => {
  if(serviceViewForm.isHyperLink=='1'){
    if (!value) {
      callback(new Error('请选择链接展示方式'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkIsEncryption = (rule,value,callback) => {
  if(serviceViewForm.showType=='password'){
    if (!value) {
      callback(new Error('请选择是否加密'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkIsViewPlaintext = (rule,value,callback) =>{
  if(serviceViewForm.showType=='password'&&serviceViewForm.isEncryption=='1'){
    if (!value) {
      callback(new Error('请选择是否显示明文'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};
const checkEncryptionMethod = (rule,value,callback) =>{
  if(serviceViewForm.showType=='password'&&serviceViewForm.isEncryption=='1'){
    if (!value) {
      callback(new Error('请选择加密方式'));
    } else {
      callback();
    }
  }else{
    callback();
  }
};


 const  ruleValidate = reactive<FormRules<typeof serviceViewForm>>({
   code: [
     {
       required: true,
       trigger: "blur",
       validator: checkCode,
     },
   ],
   name: [
     {
       required: true,
       trigger: "blur",
       validator: checkName,
     },
   ],
   property: [
     {
       required: true,
       trigger: "change",
       validator: checkProperty,
     },
   ],
   dataType: [
     {
       required: true,
       trigger: "change",
       validator: checkDataType,
     },
   ],
   dataLength: [
     {
       required: true,
       trigger: "blur",
       validator: checkDataLength,
     },
   ],
   showType: [
     {
       required: true,
       trigger: "change",
       validator: checkShowType,
     },
   ],
   enumStr: [
     {
       required: true,
       trigger: "blur",
       validator: checkEnumStr,
     },
   ],
   ocode: [
     {
       required: true,
       trigger: "blur",
       validator: checkOcode,
     },
   ],
   autoRules: [
     {
       required: true,
       trigger: "blur",
       validator: checkAutoRules,
     },
   ],
   isCascade: [
     {
       required: true,
       trigger: "change",
       validator: checkIsCascade,
     },
   ],
   cascode: [
     {
       required: true,
       trigger: "change",
       validator: checkCascode,
     },
   ],
   casfun: [
     {
       required: true,
       trigger: "change",
       validator: checkCasFun,
     },
   ],
   isReEdit: [
     {
       required: true,
       message: "请选择是否可编辑",
       trigger: "change",
     },
   ],
   isRule:[
     {
       required: true,
       message: "请选择是否可编辑",
       trigger: "change",
     }
   ],
   ruleType:[
     {
       required: true,
       trigger: "change",
       validator:checkRuleType
     }
   ],
   ruleValue:[
     {
       required: true,
       trigger: "blur",
       validator:checkRuleValue
     }
   ],
   tooltipType:[
     {
       required: true,
       trigger: "change",
       message: "请选择悬浮生成方式"
     }
   ],
   tooltipValue:[
     {
       required: true,
       trigger: "blur",
       validator:checkTooltipValue
     }
   ],
   isHyperLink:[
     {
       required: true,
       trigger: "change",
       message: "请选择是否超链接"
     }
   ],
   hyperLinkType:[
     {
       required: true,
       trigger: "change",
       validator:checkHyperLinkType
     }
   ],
   hyperLinkValue:[
     {
       required: true,
       trigger: "blur",
       validator:checkHyperLinkValue
     }
   ],
   hyperLinkShow:[
     {
       required: true,
       trigger: "change",
       validator:checkHyperLinkShow
     }
   ],
   isEncryption:[
     {
       required: true,
       trigger:"change",
       validator:checkIsEncryption
     }
   ],
   isViewPlaintext:[
     {
       required: true,
       trigger:"change",
       validator:checkIsViewPlaintext
     }
   ],
   encryptionMethod:[
     {
       required: true,
       trigger:"change",
       validator:checkEncryptionMethod
     }
   ]
 });
 const initTableData = () =>{
   state.tableLoading = true;
   state.tableData = [];
   let params = {
     categoryId: props.categoryData.id,
     pageNum: state.pagination.currentPage,
     pageSize: state.pagination.pageSize,
   };
   if(state.activeName == 'first'){
      params["tableFilter"] = state.tableFilter;
       queryPropertiesByCategoryIdAxios(params)
         .then(res => {
           state.tableData = res.data.list;
           state.pagination.total = res.data.total;
           state.tableLoading = false;
         })
         .catch(err => {
           state.tableLoading = false;
           console.info(err);
         });
   }else if(state.activeName == 'second'){
     queryCategoryRelationByCategoryIdAxios(params)
       .then(res => {
         state.tableLoading = false;
         state.tableData = res.data.list;
         state.pagination.total = res.data.total;
       })
       .catch(err => {
         state.tableLoading = false;
         console.info(err);
       });
   }else{
     queryBeCategoryRelationByCategoryIdAxios(params)
       .then(res => {
         state.tableData = res.data.list;
         state.pagination.total = res.data.total;
         state.tableLoading = false;
       })
       .catch(err => {
         state.tableLoading = false;
         console.info(err);
       });
   }
 }
 const resetRelationCategory = (row) =>{
   ElMessageBox.confirm("此操作将把本资产实例下属性全部还原更新, 是否继续?", "提示", {
     confirmButtonText: "确定",
     cancelButtonText: "取消",
     type: "warning",
   })
     .then(() => {
       let params = {
         categoryId: props.categoryData.id,
         propertyId: row.id,
       };
       deleteCrpUrlAxios(params).then((res) => {
         console.log(res);
         ElMessage.success("复原成功!");
         initTableData();
       });
     })
 }
 const removeProperty = (row) =>{
   ElMessageBox.confirm("此操作将把该属性从该类别移除，已有值将会丢失, 是否继续?", "提示", {
     confirmButtonText: "确定",
     cancelButtonText: "取消",
     type: "warning",
   })
     .then(() => {
       let params = {
         categoryId: props.categoryData.id,
         propertyId: row.id,
       };
       deleteCategoryPropertyAxios(params).then(res => {
         console.log(res);
         ElMessage("删除成功!");
         initTableData();
       });
     })
     .catch(() => {

     });
 }
 const removeRelation = (row) =>{
   ElMessageBox.confirm("此操作将永久删除该关系, 是否继续?", "提示", {
     confirmButtonText: "确定",
     cancelButtonText: "取消",
     type: "warning",
   })
     .then(() => {
       deleteCategoryRelationByIdAxios(row.code).then(res => {
         if(res["data"]=='success'){
           ElMessage.success("删除成功!");
           initTableData();
         }
       });
     })
     .catch(() => {
     });
 }
 const queryPcodeList = async(categoryId) =>{
   queryPropertyByIdAxios(categoryId).then(res => {
     state.pcodeList = res["data"];
   });
 }
 const lookUpBeRelation = (row) =>{
   getCategoryBeRelationByIdAxios(row.code).then(res => {
     let resData = res["data"];
     let association_mode = resData.association_mode;
     let valueCategory = resData.valueCategory;
     if (
       association_mode === "property" ||
       (association_mode === "instance" && valueCategory === "single")
     ) {
       queryPcodeList(resData.categoryIds[0])
     }
     changeMode(association_mode,'');
     toAddRelationList();
     changeCategoryRelationIsEdit();
     state.isEditCategoryRelation = true;
     nextTick(() => {
       addRelationListValue(association_mode, resData);
     });
   });
 }
 const changeMode = async(value, valueCategory) => {
  state.scodeList = [];
  if (value == "instance" && valueCategory === "multiple") {
    state.cateMultiple = true;
    let tt = state.addRelationList.categoryIds;
    if(tt){
      state.addRelationList.categoryIds = [];
      for(let i =0;i<tt.length;i++){
        state.addRelationList.categoryIds.push(String(tt[i]));
      }

    }else{
      state.addRelationList.categoryIds = [];
    }
  }else if(value=="instance"){
    state.cateMultiple = false;
    if(Array.isArray(state.addRelationList.categoryIds)){
      state.addRelationList.categoryIds = String(state.addRelationList.categoryIds[0]);
    }
    if(state.addRelationList.association_mode==null||state.addRelationList.association_mode===""){
      state.addRelationList.association_mode = "single";
    }
  } else {
    state.cateMultiple = false;
    queryPropertyByIdAxios(props.categoryData.id).then(res => {
      state.scodeList = res["data"];
      let pp = {
        "value":"instance_id",
        "label":"实例ID"
      };
      state.scodeList.push(pp);
    });
    if(value=="property"){
      if(Array.isArray(state.addRelationList.categoryIds)){
        state.addRelationList.categoryIds = String(state.addRelationList.categoryIds[0]);
      }
      if(state.addRelationList.categoryIds!=null&&state.addRelationList.categoryIds.length>0){
         changeCategory(String(state.addRelationList.categoryIds));
      }
    }
  }
  console.log(state.addRelationList.categoryIds);
}
const changeCategory = (value) =>{
  state.pcodeList = [];
  if (state.addRelationList.association_mode == "property") {
    queryPropertyByIdAxios(value).then(res => {
      let data = res["data"];
      if (data) {
        state.pcodeList = data;
        let pp = {
          "value":"instance_id",
          "label":"实例ID"
        };
        state.pcodeList.push(pp);
      }
    });
  }
}
const addRelationListForm = ref();
const toAddRelationList = () => {
  state.categoryRelationdialogFormVisible = true;
  addRelationListClear();
  state.pcodeList = [];
  changeCategoryRelationIsEdit();
  state.assDisabled = false;
  state.propertyCodeActiveName = false;
  state.instanceCodeActiveName = false;
  state.RelationPropertyList = [];
  state.RelationCategoryExceptList = [];
  if (addRelationListForm.value) {
    addRelationListForm.value.resetFields();
  }
  queryPropertyByIdAxios(props.categoryData.id)
    .then(res => {
      state.RelationPropertyList = res["data"].list;
    })
    .catch(err => {
      console.info(err);
    });
    categoryTreeoAxios().then(res=>{
      state.RelationCategoryExceptList = res["data"];
    }).catch(err => {
      ElMessage.error(err);
    });
}
const addRelationListClear = () =>{
  state.addRelationList = {
    id: "",
    code:"",
    association_mode: "",
    name: "",
    property: "",
    valueCategory: "",
    src_property_code: "",
    categoryIds: null,
    isEdit: true,
    property_code: "",
    description: "",
    sourceCategoryId:""
  };
}
const changeCategoryRelationIsEdit = () => {
  state.isEditCategoryRelation = false;
  state.isShow = true;
}
const addRelationListValue = (association_mode, data) => {
  state.addRelationList.id = data.id;
  if(data.code){
    state.addRelationList.code=data.code;
  }
  state.addRelationList.association_mode = data.association_mode;
  state.addRelationList.name = data.name;
  state.addRelationList.property = data.property;
  state.addRelationList.valueCategory = data.valueCategory;
  state.addRelationList.src_property_code = data.src_property_code;
  if (
    association_mode === "property" ||
    (data.association_mode === "instance" &&
      data.valueCategory === "single")
  ) {
    (state.addRelationList.categoryIds&&state.addRelationList.categoryIds.length>0),
      (state.addRelationList.categoryIds = String(data.categoryIds[0]));
  } else {
    state.addRelationList.categoryIds = [];
    for(let i =0;i<data.categoryIds.length;i++){
      state.addRelationList.categoryIds.push(String(data.categoryIds[i]));
    }
  }
  state.addRelationList.isEdit = data.isEdit;
  state.addRelationList.description = data.description;
  state.addRelationList.property_code = data.property_code;
}
const ruleRelation = ref({
    association_mode: [
      {
        required: true,
        trigger: "change",
        message: "请选择关联方式！",
      },
    ],
    property_code: [
      {
        required: true,
        trigger: "change",
        validator: (rule, value, callback) => {
          if (state.addRelationList.association_mode == "property") {
            if (value == "undefined" || value == null || value == "") {
              callback(new Error("请选择类别属性!"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
      },
    ],
    src_property_code: [
      {
        required: true,
        trigger: "change",
        validator: (rule, value, callback) => {
          if (state.addRelationList.association_mode == "property") {
            if (value == "undefined" || value == null || value == "") {
              callback(new Error("请选择源属性!"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
      },
    ],
    name: [
      {
        required: true,
        trigger: "blur",
        validator: (rule, value, callback) => {
          if (value == "undefined" || value == null || value == "") {
            callback(new Error("请填写关系名称!"));
          } else {
            categoryRelationVerifyNameAxios({
              code: state.addRelationList.code,
              name: state.addRelationList.name,
            }).then(response => {
              if (response.data.state != "success") {
                callback(new Error("关系名称已存在，请修改！"));
              } else {
                callback();
              }
            });
          }
        },
      },
    ],
    property: [
      {
        required: true,
        trigger: "change",
        message: "请选择关系类型！",
      },
    ],
    valueCategory: [
      {
        required: true,
        trigger: "change",
        validator: (rule, value, callback) => {
          if (state.addRelationList.association_mode != "property") {
            if (value == "undefined" || value == null || value == "") {
              callback(new Error("请填写资产关联!"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
      },
    ],
    categoryIds: [
      {
        required: true,
        trigger: "change",
        validator: (rule, value, callback) => {
          if (value == "undefined" || value == null || value == "") {
            callback(new Error("请填写资产关联!"));
          } else {
            callback();
          }
        },
      },
    ],
  })
const changeValueCategory = (value) => {
  if (value === "single") {
    state.cateMultiple = false;
    if(state.addRelationList.categoryIds&&state.addRelationList.categoryIds.length>0){
      state.addRelationList.categoryIds = state.addRelationList.categoryIds[0];
    }
  } else if (value === "multiple") {
    let tt = state.addRelationList.categoryIds;
    if(tt){
      state.addRelationList.categoryIds = [];
      state.addRelationList.categoryIds.push(tt);
    }else{
      state.addRelationList.categoryIds = [];
    }
    state.cateMultiple = true;
  }
  nextTick(() => {
    addRelationListForm.value.clearValidate();
  });
}
const saveCategoryRelation = () => {
  addRelationListForm.value.validate(valid => {
    if (valid) {
      state.addRelationList.sourceCategoryId = props.categoryData.id;
      if(typeof(state.addRelationList.categoryIds)=='string'
        ||
        typeof(state.addRelationList.categoryIds)=='number'){
        //current.addRelationList.categoryIds = current.addRelationList.categoryIds.split(",");
        let tmp = state.addRelationList.categoryIds;
        state.addRelationList.categoryIds = [];
        state.addRelationList.categoryIds.push(tmp);
      }

      saveCategoryRelationAxios(state.addRelationList).then(res => {
        state.addRelationList.id = res.data.id;
        ElMessage({
          type: "success",
          message: "操作成功!",
        });
        closeRelation();
        initTableData();
      });
    } else {
      return false;
    }
  });
}
const initRelationList = () => {
  state.addRelationList.name = "";
  state.addRelationList.isEdit = true;
  state.addRelationList.association_mode = "";
  state.addRelationList.categoryIds = [];
  state.addRelationList.sourceCategoryId = "";
  state.addRelationList.description = "";
  state.addRelationList.property_code="";
  state.addRelationList.src_property_code = "";
  state.addRelationList.property = "";
}
const resetRelationList = () => {
    initRelationList();
}
const closeRelation = () =>{
  initRelationList();
  state.categoryRelationdialogFormVisible = false;
}
const propertyCommand = (command, row) =>{
  if(command == 'removeProperty'){
    removeProperty(row);
  }else if(command == 'resetRelationCategory'){
    resetRelationCategory(row);
  }else if(command == 'updateCatePro'){
    updateCatePro(row);
  }else if(command == 'setDefaultValue'){
    setDefaultValue(row);
  }
}
const updateCatePro = (data) => {
  initPropertyVerifyDelMethod(data.id).then(res => {
    if (res["data"].state != "ERROR") {
      state.editServiceModel = true;
      state.dateTypeDisable = false;
      serviceViewForm.id = data.id;
      serviceViewForm.code = data.code;
      serviceViewForm.name = data.name;

      state.oldName = data.name;
      serviceViewForm.state = data.state;
      serviceViewForm.titleDesc = data.titleDesc;
      for (var i = 0; i < state.data_type_list.length; i++) {
        if (data.data_type == state.data_type_list[i].label) {
          serviceViewForm.dataType =
            state.data_type_list[i].value;
          changeDataType(serviceViewForm.dataType);
        }
      }
      serviceViewForm.ocode = data.ocode;
      changeOcode(serviceViewForm.ocode);
      if (data.data_length != null && data.data_length != "") {
        serviceViewForm.dataLength = parseInt(data.data_length);
      } else {
        serviceViewForm.dataLength = null;
      }
      serviceViewForm.showType = data.show_type;
      changeShowType(serviceViewForm.showType);
      serviceViewForm.enumStr = data.enumStr;
      serviceViewForm.autoRules = data.autoRules;
      if (data.isAuto != null && data.isAuto == "1") {
        serviceViewForm.isAuto = "1";
        serviceViewForm.property = "not_must";
        state.luru = true;
        state.rules = true;
      } else {
        serviceViewForm.isAuto = "0";
        state.luru = false;
        state.rules = false;
        for (var i = 0; i < state.propertyData.length; i++) {
          if (data.property == state.propertyData[i].value) {
            serviceViewForm.property =
              state.propertyData[i].value;
          }
        }
      }

      serviceViewForm.propertyUnit = data.property_unit;
      if (data.is_re_edit == "是") {
        serviceViewForm.isReEdit = "1";
      } else {
        serviceViewForm.isReEdit = "0";
      }
      serviceViewForm.relationType = data.relation_type;
      serviceViewForm.relationIds = data.relation_ids;
      //console.log(data);
      serviceViewForm.relationStatus = "{}";
      if (data.isCascade != null && data.isCascade == "1") {
        serviceViewForm.isCascade = "1";
        state.cascadeInfo = true;
      } else {
        serviceViewForm.isCascade = "0";
        state.cascadeInfo = false;
      }
      if (data.cascode != null && data.cascode != "") {
        serviceViewForm.cascode = data.cascode.split(",");
      }
      state.dateTypeDisable = true;
      serviceViewForm.casfun = data.casfun;
      serviceViewForm.isRule = data.isRule;
      serviceViewForm.ruleType = data.ruleType;
      serviceViewForm.ruleValue = data.ruleValue;
      serviceViewForm.tooltipType = data.tooltipType;
      serviceViewForm.tooltipValue = data.tooltipValue;
      serviceViewForm.isShowClear=data.isShowClear;
      serviceViewForm.isHyperLink = data.isHyperLink;
      serviceViewForm.hyperLinkType = data.hyperLinkType;
      serviceViewForm.hyperLinkValue = data.hyperLinkValue;
      serviceViewForm.hyperLinkParam = data.hyperLinkParam;
      serviceViewForm.hyperLinkShow = data.hyperLinkShow;
      serviceViewForm.isEncryption=data.isEncryption;
      serviceViewForm.isViewPlaintext=data.isViewPlaintext;
      serviceViewForm.encryptionMethod=data.encryptionMethod;
      state.disableflag = true;
    } else {
      ElMessageBox.confirm('已有录入该属性的资产实例，是否修改？', '确认框', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        state.editServiceModel = true;
        serviceViewForm.id = data.id;
        serviceViewForm.code = data.code;
        serviceViewForm.name = data.name;
        serviceViewForm.autoRules = data.autoRules;
        //serviceViewForm.ocode = data.ocode;
        serviceViewForm.titleDesc = data.titleDesc;

        if (data.isAuto != null && data.isAuto == "1") {
          serviceViewForm.isAuto = "1";
          serviceViewForm.property = "not_must";
          state.luru = true;
          state.rules = true;
        } else {
          serviceViewForm.isAuto = "0";
          state.luru = false;
          state.rules = false;
          for (let i = 0; i < state.propertyData.length; i++) {
            if (data.property == state.propertyData[i].label) {
              serviceViewForm.property =
                state.propertyData[i].value;
            }
          }
        }

        state.oldName = data.name;
        serviceViewForm.state = data.state;
        for (let i = 0; i < state.data_type_list.length; i++) {
          if (data.data_type == state.data_type_list[i].label) {
            serviceViewForm.dataType =
              state.data_type_list[i].value;
            changeDataType(serviceViewForm.dataType);
          }
        }
        serviceViewForm.ocode = data.ocode;

        state.dateTypeDisable = true;
        //console.log(data);
        if (data.data_length != null && data.data_length != "") {
          serviceViewForm.dataLength = parseInt(data.data_length);
        } else {
          serviceViewForm.dataLength = null;
        }
        changeOcode(serviceViewForm.ocode);
        serviceViewForm.showType = data.show_type;
        changeShowType(serviceViewForm.showType);
        serviceViewForm.enumStr = data.enumStr;
        serviceViewForm.propertyUnit = data.property_unit;
        if (data.is_re_edit == "是") {
          serviceViewForm.isReEdit = "1";
        } else {
          serviceViewForm.isReEdit = "0";
        }
        if (data.isCascade != null && data.isCascade == "1") {
          serviceViewForm.isCascade = "1";
          state.cascadeInfo = true;
        } else {
          serviceViewForm.isCascade = "0";
          state.cascadeInfo = false;
        }

        serviceViewForm.casfun = data.casfun;
        if (data.cascode != null && data.cascode != "") {
          serviceViewForm.cascode = data.cascode.split(",");
        }

        serviceViewForm.relationType = data.relation_type;
        serviceViewForm.relationIds = data.relation_ids;
      })
      serviceViewForm.isRule = data.isRule;
      serviceViewForm.ruleType = data.ruleType;
      serviceViewForm.ruleValue = data.ruleValue;
      serviceViewForm.tooltipType = data.tooltipType;
      serviceViewForm.tooltipValue = data.tooltipValue;
      serviceViewForm.isShowClear=data.isShowClear;
      serviceViewForm.isHyperLink = data.isHyperLink;
      serviceViewForm.hyperLinkType = data.hyperLinkType;
      serviceViewForm.hyperLinkValue = data.hyperLinkValue;
      serviceViewForm.hyperLinkParam = data.hyperLinkParam;
      serviceViewForm.hyperLinkShow = data.hyperLinkShow;
      serviceViewForm.isEncryption=data.isEncryption;
      serviceViewForm.isViewPlaintext=data.isViewPlaintext;
      serviceViewForm.encryptionMethod=data.encryptionMethod;
      state.disableflag = true;
    }
  })
}
function buildTree(
  data: any[],
  deptId: string | number,
  parentId: string | number
) {
  // 创建一个映射，用于快速查找节点
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, children: [] });
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
}
const setDefaultValue = async(row) => {
  let params = {
    id: row.id,
    categoryId: props.categoryData.id
  };
  queryPropertyGxhDefaultByIdAxios(params).then(res => {
    if(res.data.show_type=='windowboxFilter'){
      state.windowboxFilterTable = res.data.enumStr;
    }
    if(res.data.show_type=='windowboxTreeFilter'){
      state.windowboxFilterTable = res.data.enumStr[0].enumList;
    }
    state.defaultValueForm.default_value = res.data.default_value;
    nextTick(()=>{
      state.windowboxFilterTable=[];
    })
    state.defaultValueForm.id = res.data.id;
    state.defaultValueForm.defaultShowValue=res.data.defaultShowValue;
    state.defaultValueShowType = res.data.show_type;
    state.defaultValueName = res.data.name;
    if(res.data.enumStr){
      state.defaultValueList = [];
      if(state.defaultValueShowType=='comboTree'){
        state.defaultValueList.push(...buildTree(res.data.enumStr,"id","parentId"));
      }
    }else{
      state.defaultValueList = res.data.enumStr;
    }
    state.defaultValueObj = res.data;
    nextTick(()=>{
      state.defaultValueForm.default_type = res.data.default_type;
      if(state.defaultValueForm.default_type=='1'){
        state.defaultValue1 = state.defaultValueForm.default_value;
      }else{
        state.defaultValue2 = state.defaultValueForm.default_value;
      }
    })
  }).catch(exp => {
    console.log(exp);
  })
  state.defaultValueShow = true;
  state.defaultValueShowType = row.show_type;
  state.defaultValueName = row.name;
}
const closeDefaultValue = () => {
  state.defaultValueList = [];
  state.defaultValueName = "";
  state.defaultValueForm.default_value = "";
  state.defaultValueForm.defaultShowValue="";
  state.defaultValueForm.id = "";
  state.defaultValueShowType = "input";
}
const defaultTypeChange = (value) =>{
  if(value=="1"){
    state.defaultValue2 = state.defaultValueForm.default_value;
    state.defaultValueForm.default_value = state.defaultValue1;
  }else{
    state.defaultValue1 = state.defaultValueForm.default_value;
    state.defaultValueForm.default_value = state.defaultValue2;
  }
}

const openSelect =(item) => {
  getObjectPage(item);
}
const clearSelect = () =>{
  state.defaultValueForm.defaultShowValue="";
  state.defaultValueForm.default_value="";
  state.defaultValueObj["default_value"]="";
}
const windowTable = ref();
const getObjectPage = (item) => {
  if (item.ocode) {
    state.windowboxloading = true;
    //current.windowSelect = item.filed;
    state.currentItem = item;
    state.currentItem.value = item.default_value;
    //current.currentItem.label=item.field.showValue;
    state.currentId = item.default_value;
    state.ocode = item.ocode;
    state.show_type = item.show_type;
    state.selectDataModal = true;
    state.data = item.enumStr.data;
    state.totalW = item.enumStr.total;
    state.windowboxloading = false;
    state.windowSelect = {};
    state.windowSelect.value = item.default_value;
    //current.windowSelect.label = item.field.showValue;
    state.windowSelect.id = item.default_value;
    nextTick(() => {
      if (state.windowSelect.value) {
        for (let i = 0; i < state.data.length; i++) {
          if (state.data[i].value == state.windowSelect.value) {
            windowTable.value.toggleRowSelection(state.data[i], true);
          }
        }
      }
    })
  }
}

const closePropSelect = () =>{
  state.selectDataModal = false;
    state.propValue = "";
    state.data = [];
    state.windowSelect = null;
  //this.currentId = '';
    state.currentPageW = 1;
    state.totalW = 0;
    state.pageSizeW = 10;
}
const closePropSelectMul = () => {
  state.selectMulDataModal = false;
  state.propValue = "";
  state.mulData = [];
  state.windowMulSelect = null;
  //this.currentId = '';
  state.currentMulPageW = 1;
  state.totalMulW = 0;
  state.pageMulSizeW = 10;
}

const handleCurrentChangeW = (val) => {
  const json = {
    pageNum: val,
    pageSize: state.pageSizeW,
  };
  state.pageNumW = val;
  initWindowBox(json);
}
const handleCurrentMulChangeW = (val) => {
  const json = {
    pageNum: val,
    pageSize: state.pageMulSizeW,
  };
  state.pageNumW = val;
  initMulWindowBox(json);
}

const initWindowBox = (json) => {
  state.windowboxloading = true;
  json.show_type = state.defaultValueShowType;
  json.ocode = state.ocode;
  json.value = state.propValue;
  queryEnumList(json).then(res => {
    state.selectDataModal = true;
    state.data = res["data"].data;
    //current.object.total = res.data.total;
    state.totalW = res["data"].total;
    state.windowboxloading = false;
  });
}
const windowMulTable = ref();
const initMulWindowBox = (json) => {
  state.windowboxloading = true;
  json.show_type = "windowbox";
  json.ocode = state.mulOcode;
  json.value = state.propValue;
  queryEnumList(json).then(res => {
    state.selectMulDataModal = true;
    state.mulData = res["data"].data;
    //current.object.total = res.data.total;
    state.totalMulW = res["data"].total;
    state.windowboxloading = false;
    let item = state.mulCurrentItem;
    let windowMulSelects = [];
    let ttValue = null;
    let ttLabel = null;
    if (item["value"] != null) {
      if (typeof item["value"] == "string") {
        ttValue = item["value"].split(",");
      } else {
        ttValue = item["value"];
      }
      ttLabel = item["label"].split(",");
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        let cc = {
          value: ttValue[i],
          label: ttLabel[i]
        };
        windowMulSelects.push(cc);
      }
    }

    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < state.mulData.length; i++) {
            if (state.mulData[i].value == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(
                state.mulData[i],
                true,
              );
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        state.mulCurrentItem["value"] = ttValue.join(",");
        state.mulCurrentItem["label"] = ttLabel.join(",");
      }
    });
  });
}
const queryValue = () => {
  let json = {
    pageSize: state.pageSizeW,
    pageNum: 1
  }
  initWindowBox(json);
}
const resetValue = () => {
  state.propValue = "";
  state.currentId = "";
  state.currentLabel = "";
}
const handleSelectionChange = (selections) => {
  state.selectIds = [];
  selections.forEach(row => {
    state.selectIds.push(row.id);
  });
}
const handleRowClick = (row) => {
  windowTable.value.toggleRowSelection(row);
  state.windowSelect = row;
}
const handleSizeChangeW = (val) => {
  const json = {
    pageNum: 1,
    pageSize: val
  }
  state.pageSizeW = val;
  initWindowBox(json);
}
const handleCurrentChange = (val) => {
  state.pagination1.currentPage = val;
  tableReload();
}
const handleSizeChange = (val) => {
  state.pagination1.pageSize = val;
  state.pagination1.currentPage = 1;
  tableReload();
}
const tableReload = () => {
  queryPropertyByName();
}
const saveSelect = () =>{
  // if (this.currentId != '' && this.currentItem != null && this.currentLabel != '') {
  state.defaultValueForm.default_value = state.windowSelect.value;
  state.defaultValueForm.defaultShowValue= state.windowSelect.label;
  closePropSelect();
  //   }
}
const openMulSelect = (item) => {
  getMulObjectPage(item);
}
const getMulObjectPage = (item) =>{
  state.windowboxloading = true;
  //current.windowSelect = item.filed;
  state.mulCurrentItem={
    value:"",
    label:""
  };
  if(state.defaultValueForm.default_value){
    state.mulCurrentItem["value"]=JSON.parse(JSON.stringify(state.defaultValueForm.default_value));
  }
  if(state.defaultValueForm.defaultShowValue){
    state.mulCurrentItem["label"] = JSON.parse(JSON.stringify(state.defaultValueForm.defaultShowValue));
  }
  state.currentMulItem = item;
  state.currentMulId = item.default_value;
  state.mulOcode=item.ocode;
  state.mulShow_type=item.show_type;
  var params = {
    "show_type": "windowbox",
    "ocode": item.ocode,
    "pageSize":state.pageMulSizeW,
    "pageNum": state.currentMulPageW,
    "value": state.propValue
  };
  queryEnumList(params).then(res=>{
    state.selectMulDataModal = true;
    state.mulData = res["data"].data;
    state.totalMulW = res["data"].total;
    state.windowboxloading = false;
    let windowMulSelects = [];
    let ttValue=null;
    let ttLabel=null;
    if(state.mulCurrentItem["value"]!=null){
      if((typeof state.mulCurrentItem["value"]) == 'string'){
        ttValue = state.mulCurrentItem["value"].split(",");
      }else{
        ttValue = state.mulCurrentItem["value"];
      }
      ttLabel = state.mulCurrentItem["label"].split(",");
    }
    if(ttValue!=null){
      for(let i =0;i<ttValue.length;i++){
        let cc = {
          value: ttValue[i],
          label: ttLabel[i]
        };
        windowMulSelects.push(cc);
      }
    }

    nextTick(()=>{
      if (windowMulSelects.length>0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < state.mulData.length; i++) {
            if (state.mulData[i].value == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(state.mulData[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        state.mulCurrentItem["value"] = ttValue.join(",");
        state.mulCurrentItem["label"] = ttLabel.join(",");
      }
    })
  })

}
const clearMulSelect = () =>{
  state.defaultValueForm.default_value="";
  state.defaultValueForm.defaultShowValue="";
  state.defaultValueShow=false;
  state.defaultValueName="";
  state.currentMulItem=null;
}

const openTreeSelect = (defaultValueObj) =>{
  state.treeItem["field"]={};
  state.treeItem["field"].value = state.defaultValueForm.default_value;
  state.treeItem["field"].showValue = state.defaultValueForm.defaultShowValue;
  state.treeItem["field"].enumArray = defaultValueObj.enumStr;
  state.windowTreeDialog = true;
}
const clearTreeSelect = ()=>{
  state.defaultValueForm.default_value="";
  state.defaultValueForm.defaultShowValue="";
}
const closeWindowTree = (item) =>{
  state.windowTreeDialog=false;
  state.defaultValueForm.default_value=item.field.value;
  state.defaultValueForm.defaultShowValue=item.field.showValue;
}

const openFilterSelect = (item) =>{
  openFulterMulSelect(item);
}
const openFulterMulSelect = (item) =>{
  state.windowboxloading = true;
  //current.windowSelect = item.filed;
  state.mulCurrentItem={};
  state.mulCurrentItem["value"]="";
  state.mulCurrentItem["label"]="";
  if(state.defaultValueForm.default_value){
    state.mulCurrentItem["value"]=JSON.parse(JSON.stringify(state.defaultValueForm.default_value));
    if(state.defaultValueForm.default_value){
      state.mulCurrentItem["label"]="";
    }else{
      state.mulCurrentItem["label"]=JSON.parse(JSON.stringify(state.defaultValueForm.default_value));
    }

  }
  state.currentMulItem = item;
  state.currentMulId = item.default_value;
  state.mulOcode=item.ocode;
  state.mulShow_type=item.show_type;
  var params = {
    "show_type": "windowbox",
    "ocode": item.ocode,
    "pageSize":state.pageMulSizeW,
    "pageNum": state.currentMulPageW,
    "value": state.propValue
  };
  queryEnumList(params).then(res=>{
    state.selectMulDataModal = true;
    state.mulData = res["data"].data;
    //current.object.total = res.data.totayarnl;
    state.totalMulW = res["data"].total;
    state.windowboxloading = false;
    let windowMulSelects = [];
    let ttValue=null;
    let ttLabel=null;
    if(state.mulCurrentItem["value"]!=null){
      if((typeof state.mulCurrentItem["value"]) == 'string'){
        ttValue = state.mulCurrentItem["value"].split(",");
      }else{
        ttValue = state.mulCurrentItem["value"];
      }
      ttLabel = state.mulCurrentItem["label"];
    }
    if(ttValue!=null){
      for(let i =0;i<ttValue.length;i++){
        let cc = {
          value: ttValue[i],
          label: ttLabel[i]
        };
        windowMulSelects.push(cc);
      }
    }

    nextTick(()=>{
      if (windowMulSelects.length>0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < state.mulData.length; i++) {
            if (state.mulData[i].value == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(state.mulData[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        state.mulCurrentItem["value"] = ttValue;
        state.mulCurrentItem["label"] = ttLabel;
      }
    })
  })
}
const clearTableList = (val,item) =>{
  let query = "";
  if (state.queryCondition) {
    query = state.queryCondition;
  }
  state.windowboxFilterTable = [];
  remoteMethod(query, item);
}
const remoteMethod = (query,item) =>{
  state.queryCondition = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    state.windowboxFilterTable = item.enumStr.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    state.windowboxFilterTable= [];
  }
}
const clearTreeTableList = (event,item) =>{
  var query = "";
  if (state.queryCondition) {
    query = state.queryCondition;
  }
  state.windowboxFilterTable = [];
  remoteTreeMethod(query, item);
}
const remoteTreeMethod = (query,item) =>{
  state.queryCondition = query;
  if (query !== "") {
    //this.loading = true;
    //this.loading = false;
    state.windowboxFilterTable = item.enumStr[0].enumList.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    state.windowboxFilterTable= [];
  }
}
const clearWindowTree = (item) =>{
  item.default_value = [];
  state.windowboxFilterTable=[];
  state.defaultValueForm.default_value = [];
  state.windowTreeDialog = true;
  state.windowTreeDialog = false;
}
const openWindowTree = (defaultValueObj) =>{
  state.treeItem["field"]={};
  state.treeItem["field"].value = state.defaultValueForm.default_value;
  state.treeItem["field"].showValue = state.defaultValueForm.defaultShowValue;
  state.treeItem["field"].enumArray = defaultValueObj.enumStr;
  state.windowboxFilterTable =  defaultValueObj.enumStr[0].enumList;
  state.windowTreeDialog = true;
}
const saveDefaultValue = () => {
  if(state.defaultValueForm.default_type=='1'){
    if(state.defaultValueShowType=='windowboxFilter'||state.defaultValueShowType=='windowboxTreeFilter'||state.defaultValueShowType=='inputSelect'
      ||state.defaultValueShowType=='moreInput'){
      state.defaultValueForm.default_value = state.defaultValueForm.default_value.join(",");
    }
  }
  let params = state.defaultValueForm;
  params["categoryId"] = props.categoryData.id;
  saveDefaultGxhValueAxios(params).then(res => {
    if (res.data == 'success') {
      ElMessage.success("保存成功")
      state.defaultValueShow = false;
      initTableData();
    } else {
      ElMessage.error(res.data);
    }
  }).catch(exp => {
    ElMessage.error(exp.message);
  })
}
const cancelDefaultValue = () => {
  state.defaultValueShow = false;
}

const closeRuleCode = () => {
  state.editRuleForm.ruleCodeSn = "";
  state.editRuleDialog = false;
}
const saveRuleCode = () => {
  let params = {
    category_id: props.categoryData.id,
    property_id: state.rule_pro["id"],
    ruleCodeSn: state.editRuleForm.ruleCodeSn,
    ruleCode: state.rule_pro["autoRules"]
  };
  saveEditRuleAxios(params)
    .then(res => {
      if (res.data == "OK") {
        ElMessage.success("保存成功");
        state.editRuleDialog = false;
      } else {
        ElMessage.error("保存失败，" + res.data);
      }
    })
    .catch(exp => {
      ElMessage.error(exp.message);
    });
}

const editRelation = (row) => {
  getCategoryRelationByIdAxios(row.code).then(res => {
    let association_mode = res["data"].association_mode;
    let valueCategory = res["data"].valueCategory;
    state.categoryRelationdialogFormVisible = true;
    if (
      association_mode === "property" ||
      (association_mode === "instance" && valueCategory === "single")
    ) {
      state.pcodeList = [];
      queryPropertyByIdAxios(res["data"].categoryIds[0]).then(res => {
        state.pcodeList = res["data"];
      });
    }
    changeMode(association_mode, valueCategory);
    toAddRelationList();
    nextTick(() => {
      addRelationListForm.value.resetFields();
      addRelationListValue(association_mode, res["data"]);
    });
  });
}

const restBianma = () => {
  //console.log(this.$refs.serviceViewForm.code);
  serviceViewForm.code = check(
    serviceViewForm.code.toString()
  );
}
const check = (str) => {
  let temp = "";
  for (let i = 0; i < str.length; i++)
    if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 255) {
      var _ilreg = /[\]!@#+=\-/'\"$%&^*(){}|,.?~` <>\\\\[:\;]/;
      if (!_ilreg.test(str.charAt(i))) {
        temp += str.charAt(i);
      }
    }

  return temp;
}
const restName = () => {
  serviceViewForm.name = check1(
    serviceViewForm.name.toString()
  )
}
const check1 = (str) => {
  var temp = "";
  for (var i = 0; i < str.length; i++) {
    var _ilreg = /[\]!@#+=/'\"\-$%&^*(){}|,.?~` <>，。《》“：‘；？￥……（）！~·\\\\[:\;]/;
    if (!_ilreg.test(str.charAt(i))) {
      temp += str.charAt(i);
    }
  }
  return temp;
}
const cascadeChange = (value) => {
  if (value == "1") {
    state.cascadeInfo = true;
  } else {
    state.cascadeInfo = false;
    serviceViewForm.cascode = [];
    serviceViewForm.casfun = "";
  }
}

const addEnum = () => {
  state.editEnumModel=true;
  state.enumFormList[0].label="";
  state.enumFormList[0].value="";
  state.enumFormList[0].id="";
  state.index=1;
  state.enumFormList.length=1;
  if(serviceViewForm.enumStr!=""){
    let tt = eval("(" + serviceViewForm.enumStr + ")");
    state.enumFormList[0].label=tt[0].label;
    state.enumFormList[0].value=tt[0].value;
    state.enumFormList[0].id=tt[0].id;
    if(tt.length>1){
      for(let i=1;i<tt.length;i++){
        state.index++;
        state.enumFormList.push({
          index:state.index,
          id:tt[i].id,
          label:tt[i].label,
          value:tt[i].value
        })
      }
    }else{

    }
  }
}


const dataEnums = () =>{
  state.modal1= true;
  queryEnumCodeList().then(res=>{
    state.codeList = res["data"];
  })
}
const saveEnum = () =>{
  let list = state.enumFormList;
  for(let i =0;i<list.length;i++){
    if(list[i].label==""||list[i].value==""||list[i].id==undefined||list[i].id==""){
      ElMessage.error('序号,编码和名称都不能为空');
      return;
    }
  }
  if(list.length>1){
    for(let i =0;i<list.length-1;i++){
      let value = list[i].value;
      let id = list[i].id;
      for(let j=i+1;j<list.length;j++){
        if(list[j].value==value){
          ElMessage.error('编码不能相同，第'+(i+1)+"行和第"+(j+1)+"行编码相同");
          return;
        }
        if(list[j].id==id){
          ElMessage.error('序号不能相同，第'+(i+1)+"行和第"+(j+1)+"行序号相同");
          return;
        }
      }
    }
  }
  let results = [];
  for(let i=0;i<list.length;i++){
    let pp = {
      id:list[i].id,
      label:list[i].label,
      value:list[i].value
    };
    results.push(pp);
  }
  results.sort(function(a,b){
    return a.id-b.id;
  });
  serviceViewForm.enumStr=JSON.stringify(results);
  state.index=1;
  state.enumFormList.length=1;
  state.editEnumModel = false;
}
const serviceViewFormRef = ref();
const saveService = () =>{
  serviceViewFormRef.value.validate(validata=> {
    if(validata){
      if(serviceViewForm.showType!='password'){
        serviceViewForm.isEncryption='0';
        serviceViewForm.isViewPlaintext='0';
        serviceViewForm.encryptionMethod=null;
      }else{
        if(serviceViewForm.isEncryption=='0'){
          serviceViewForm.isViewPlaintext='0';
          serviceViewForm.encryptionMethod=null;
        }
      }
      let params = serviceViewForm;
      params["categoryId"] = props.categoryData.id;
      saveCategoryProperty(params).then(res=>{
        if(res.data=="success"){
          ElMessage.success("保存成功");
          closeService();
          initTableData();
        }else{
          ElMessage.error("保存失败，"+res.data);
        }
      }).catch(exp=>{
        ElMessage.error(exp);
      });
    }else{
      ElMessage.error("校验失败");
    }

  })
}
const closeService = () => {
  state.editServiceModel = false;
  state.oldName = "";
  serviceViewForm.relationIds = "";
  serviceViewForm.relationStatus = "";
  serviceViewForm.relationType = "";
  serviceViewFormRef.value.resetFields();
}
const addForm = () =>{
  state.index++;
  state.enumFormList.push({
    index:state.index,
    id:'',
    label:'',
    value:''
  })
}
const removeForm = (i) =>{
  if(state.enumFormList.length === 1){
    ElMessage.error('至少保留一个子维度信息');
    return;
  }
  state.enumFormList.splice(i, 1)
}

const addHyperLinkParamMethod = () => {
  state.addHyperLinkParam = true;
  state.hyperLinkFormList[0].label = "";
  state.hyperLinkFormList[0].value = "";
  state.hyperIndex = 1;
  state.hyperLinkFormList.length = 1;
  if (serviceViewForm.hyperLinkParam != "") {
    let tt = eval("(" + serviceViewForm.hyperLinkParam + ")");
    state.hyperLinkFormList[0].label = tt[0].label;
    state.hyperLinkFormList[0].value = tt[0].value;
    if (tt.length > 1) {
      for (let i = 1; i < tt.length; i++) {
        state.hyperIndex++;
        state.hyperLinkFormList.push({
          index: state.hyperIndex,
          label: tt[i].label,
          value: tt[i].value
        })
      }
    } else {

    }
  }
}
const changeTooltipType = () =>{
  serviceViewForm.tooltipValue="";
}
const addRuleValue = (value) =>{
  if(value=='like'){
    serviceViewForm.ruleValue+='.indexOf("")>-1';
  }else if(value=='notLike'){
    serviceViewForm.ruleValue+='.indexOf("")==-1';
  }else{
    serviceViewForm.ruleValue+=value;
  }
}
const addRuleRegValue = (value) =>{
  if(value=='email'){
    serviceViewForm.ruleValue='^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$';
  }else if(value=='phone'){
    serviceViewForm.ruleValue='^((\\d{3,4}-)|\\d{3.4}-)?\\d{7,8}$';
  }else if(value=='telephone'){
    serviceViewForm.ruleValue='^(\\d{3,4}|\\d{3,4}-|s)?\\d{7,14}$';
  }else if(value=='ipv4'){
    serviceViewForm.ruleValue='^((?:(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d))))$';
  }else if(value=='yuming'){
    serviceViewForm.ruleValue='^http(s)?:\\/\\/(.*?)\\/$';
  }else if(value=='shenfen'){
    serviceViewForm.ruleValue='(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)';
  }
}
const shengcheng = (value) => {
  if (value == "1") {
    state.rules = true;
    serviceViewForm.property = "not_must";
    state.luru = true;
  } else {
    state.rules = false;
    state.luru = false;
  }
}
const queryPropertyByName = () => {
  state.propertyList = [];
  let params = {
    categoryId: props.categoryData.id,
    name: state.peopertyNameInput,
    pageNum: state.pagination1.currentPage,
    pageSize: state.pagination1.pageSize,
  };
  queryNonPropertyByIdAxios(params)
    .then(res => {
      state.propertyList = res.data.list;
      state.pagination1.total = res.data.total;
    })
    .catch(err => {
      console.info(err);
    });
}
const saveProperties = () => {
  let param = {
    categoryId: props.categoryData.id,
    selectIds: state.selectIds,
  };
  savePropertyAxios(param).then(res => {
    console.log(res);
    ElMessage({
      type: "success",
      message: "添加属性成功!",
    });
    queryPropertyByName();
    initTableData();
  });
}
const resetPropertyList = () => {
  state.propertyList = [];
  state.selectIds = [];
  state.peopertyNameInput = "";
  queryPropertyByName();
}
const ok = () =>{
  //alert(this.enumCode);
  if(state.enumCode==null||state.enumCode==""){
    ElMessage.error("请选择生成规则");
    return;
  }
  let params={
    "emumCode":state.enumCode
  };
  queryEnumListDe(params).then(res=>{
    serviceViewForm.enumStr =  JSON.stringify(res.data);
    addEnum();
    state.modal1=false;
  })
}
const resetLoad = () =>{
  state.enumCode = "";
}
const closeLoad = () =>{
  state.modal1 = false;
}
const queryMulValue = () => {
  let json = {
    pageSize:state.pageMulSizeW,
    pageNum:1
  }
  initMulWindowBox(json);
}
const resetMulSelect = () => {
  state.propValue = "";
}
const handleSelectionMulChange = (val) =>{
  state.windowMulSelect=val;
}
const handleSizeMulChangeW = (val) => {
  const json={
    pageNum:1,
    pageSize:val
  }
  state.pageMulSizeW=val;
  initMulWindowBox(json);
}
const saveSelectMul = () => {
  // if (this.currentId != '' && this.currentItem != null && this.currentLabel != '') {
  let ttValue="";
  let ttLabel="";
  if(state.mulCurrentItem!=null){
    if((typeof state.mulCurrentItem["value"]) == 'string'){
      ttValue = state.mulCurrentItem["value"];
    }else{
      ttValue = state.mulCurrentItem["value"].join(",");
    }
    if((typeof state.mulCurrentItem["label"]) == 'string'){
      ttLabel = state.mulCurrentItem["label"];
    }else{
      ttLabel = state.mulCurrentItem["label"].join(",");
    }
  }
  if(state.windowMulSelect!=null&&state.windowMulSelect.length>0){
    if(ttValue!=""){
      ttValue += ",";
      ttLabel += ",";
    }
    for(let i=0;i<state.windowMulSelect.length;i++){
      if(i==state.windowMulSelect.length-1){
        ttValue += state.windowMulSelect[i].value;
        ttLabel += state.windowMulSelect[i].label;
      }else{
        ttValue += state.windowMulSelect[i].value+",";
        ttLabel += state.windowMulSelect[i].label+",";
      }
    }
  }

  if (state.defaultValueShowType == "windowbox") {
    state.defaultValueForm.default_value = ttValue;
  } else if (state.defaultValueShowType == "windowboxFilter") {
    state.windowboxFilterTable =
      state.defaultValueObj["enumStr"];
    state.defaultValueForm.default_value = ttValue.split(",");
    nextTick(() => {
      state.windowboxFilterTable = [];
    });
  }
  state.defaultValueForm.defaultShowValue = ttLabel;
  closePropSelectMul();
}
const initCombobox = async() => {
  initComboboxMethod().then(res => {
    let data = res["data"];
    state.data_type_list = data.dataTypeList;
    state.show_type_list = data.showTypeList;
    state.propertyData = data.propertyList;
    state.casList = data.casList;
    state.cascodeList = data.cascodeList;
    state.ocodeList = data.ocodeList;
    state.autoRulesList = data.autoRulesList;
    state.tooltipRuleList = data.tooltipRuleList;
    state.defaultRuleList = data.defaultRuleList;
    state.encryptionMethodList = data.encryptionMethodList;
  });
};
const toAddProperty = () => {
  state.selectIds = [];
  state.pagination1.currentPage = 1;
  state.pagination1.pageSize = 10;
  state.peopertyNameInput = "";

  state.propertyList = [];
  state.addPropertyDialogFormVisible = true;
    let params = {
      categoryId: props.categoryData.id,
      pageNum: state.pagination1.currentPage,
      pageSize: state.pagination1.pageSize,
    };
    queryNonPropertyByIdAxios(params)
      .then(res => {
        state.propertyList = res.data.list;
        state.pagination1.total = res.data.total;
      })
      .catch(err => {
        console.info(err);
      });

}
onMounted(() => {
  initTableColumn();
  initTableData();
  initCombobox();
})
</script>
