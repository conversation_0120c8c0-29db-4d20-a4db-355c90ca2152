<template>
  <el-main class="instanceConditionTemplate">
    <el-row>
      <el-button type="primary" @click="copyCategoryMuBan">模板克隆</el-button>
      <p style="font-size:8px;color:red">(注意：克隆其他类别的模板，只加载名字不相同且其他类别的模板)</p>
    </el-row>
    <el-row>
      <el-col :span="5">
        <el-tree ref="tree"
                 v-loading="treeLoading"
                 show-icon
                 :toolOptions="['expand']"
                 :default-expanded-keys="[0]"
                 default-expand-all
                 :data="treeList"
                 :props="{'label':'title','children':'children'}"
                 highlight-current
                 class="instanceTree"
                 :expand-on-click-node="true"
                 @on-render-node-before="onRenderNodeBefore"
                 @node-click="handleNodeClick"
                 @node-dblclick="handleDbNodeClick"
                 :operations="[
                 {icon:'el-icon-copy-document',title:'克隆',handler:copyMuBan,onShow:(data) => data.parent_id!=null},
          {icon:'el-icon-circle-plus-outline',title:'添加',handler: addMuBan, onShow: (data) => roleId=='Superman'&&data.parent_id==null&&data.id=='common'||data.parent_id==null&&data.id=='owner'},
          {icon:'el-icon-s-custom',title:'应用所有',handler:useAll,onShow:(data) => data.parent_id=='common'&&roleId=='Superman'},
          {icon:'el-icon-user',title:'应用',handler:useOne,onShow:(data) => data.parent_id!=null},
          {icon:'el-icon-edit',title:'修改',handler:updateMuBan,onShow:(data) => (data.parent_id=='common'&&roleId=='Superman')||data.parent_id=='owner'},
          {icon:'el-icon-delete',title:'删除',handler:deleteMuBan,onShow:(data) => (data.parent_id=='common'&&roleId=='Superman')||data.parent_id=='owner'}
        ]"
        />
      </el-col>
      <el-col :span="18" v-loading="templateColumnLoading">
        <el-row>
          <el-col :span="5">
            <el-button type="primary"
                       @click="saveCondition" :disabled="templateColumnDisabled">保存
            </el-button>
            <el-button type="info"
                       @click="cancalCondition">取消
            </el-button>
          </el-col>
          <el-col :span="19">
            <el-input type="text" placeholder="过滤值" v-model="filterTemplateColumn" style="width:300px;" @change="changeFilter" clearable></el-input>
          </el-col>
        </el-row>
        <el-row style="margin-top:5px;">
          <el-card  style="padding-top:8px;padding-bottom:8px; margin-bottom:10px;">
            <el-col :span="8">
              是否开启IP掩码查询：
              <el-switch v-model="ip_mask_state_save" :disabled="templateColumnDisabled"
                         active-text="是"
                         inactive-text="否"
                         active-value="open"
                         inactive-value="close"
                         @change="changeSwitch"></el-switch>
            </el-col>
            <el-col :span="2">&nbsp;</el-col>
            <el-col :span="8">
              是否开启IP地址段查询：
              <el-switch v-model="ip_seg_state_save" :disabled="templateColumnDisabled"
                         active-text="是"
                         inactive-text="否"
                         active-value="open"
                         inactive-value="close"
                         @change="changeSwitch1"></el-switch>
            </el-col>
            <el-col :span="6">&nbsp;</el-col>
          </el-card>
        </el-row>
        <el-row>
        <el-checkbox-group v-model="checkCondition"
                           style="overflow:hidden;"
                           :min='1' :disabled="templateColumnDisabled">
          <el-col :span="6"
                  v-for="item in conditionList1"
                  style="margin-bottom:8px;">
            <el-checkbox :label="item.value"
                         :key="item.value">{{ item.label }}</el-checkbox>
          </el-col>
        </el-checkbox-group>
        </el-row>
      </el-col>
    </el-row>
    <el-dialog append-to-body v-model="showSaveTemplate" width="400px" title="保存模板">
      <el-form :model="templateForm" ref="templateForm" label-width="80px" :rules="formRules">
        <el-row>
          <el-form-item prop="template_name" label="模板名称">
            <el-input type="text" v-model="templateForm.template_name"></el-input>
          </el-form-item>
        </el-row>
        <el-row v-if="roleId=='Superman'">
          <el-form-item prop="template_type" label="模板类型">
            <el-radio-group v-model="templateForm.template_type">
              <el-radio label="common">公共模板</el-radio>
              <el-radio label="owner">私有模板</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-row>
        <el-row>
          <el-button type="primary" @click="saveShowTemplate">保存</el-button>
          <el-button type="primary" @click="closeShowTemplate">关闭</el-button>
        </el-row>
      </el-form>
    </el-dialog>

    <el-dialog append-to-body v-model="showSelectCategory"  v-loading="copyCategoryLoading" width="70%" title="模板克隆">
      <el-row>
      <el-button type="primary" @click="saveCopyTemplate">保存</el-button>
      <el-button type="primary" @click="closeCopyTemplate">关闭</el-button>
      </el-row>
      <el-row>
        <el-tree v-if="showSelectCategory" ref="dataTree" multiple check-strictly show-checkbox  :data="treeData"  style="width:100%"
                 node-key="id" node-value="id" :props="{'label':'title','children':'children'}"
                 @check="changeCategory"></el-tree>
      </el-row>
    </el-dialog>
    <el-dialog append-to-body v-model="copyCategoryTemplateShow" v-if="copyCategoryTemplateShow" v-loading="copyCategoryTemplateLoading" width="70%" title="模板克隆">
      <el-row>
        <el-col :span="5">
          <el-tree  ref="categoryListTree"  :tool-options="[]" :data="categoryList"  style="width:100%"
                    node-key="id" node-value="title" :props="{'label':'title','children':'children'}" @node-click="checkNode" highlight-current
          ></el-tree>
        </el-col>
        <el-col :span="19">
          <el-form :model="queryModelForm" v-if="queryModelForm" label-width="80px">
            <el-row>
              <el-col :span="10">
                <el-form-item prop="template_name" label="模板名称">
                  <el-input type="text" v-model="queryModelForm.template_name"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item prop="template_type" label="模板类型" >
                  <el-select v-model="queryModelForm.template_type" clearable>
                    <el-option value="common" label="公共模板"></el-option>
                    <el-option value="owner" label="私有模板"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-button type="primary" @click="initCopyTable">查询</el-button>
                <el-button type="primary" @click="resetCopyTable">重置</el-button>
              </el-col>
            </el-row>
          </el-form>
          <avue-crud :data="copyTableData" :option="copyTableColumnOption" v-model:page="state.pagination"
                     @size-change="handlePageSizeChange" :table-loading="copyTableLoading" @selection-change="changeSelectRow"
                     @current-change="handlePageCurrentChange">
          </avue-crud>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveCopyCategoryTemplate">保存</el-button>
        <el-button @click="closeCopyCategoryTemplate">关闭</el-button>
      </span>
    </el-dialog>
  </el-main>
</template>

<script setup lang="ts">
  import {propertyExistsAxios} from "@/views/modules/eam/zcgl/instance/source/api/attrsortgroupModelInterface";
  import {
    deleteTemplateAxios, initCategoryListByCopyTemplateAxios, initCategoryModelAxios,
    initColumnTemplateAxios, initCopyTableAxios,
    querySelectTemplateColumnAxios, saveCopyCategoryTemplateAxios,
    saveShowTemplateColumnAxios, saveTemplateCopyAxios, saveTemplateNameAxios,
    useTemplateAllAxios,
    useTemplateOneAxios,
    validataTemplateNameAxios
  } from "@/views/modules/eam/zcgl/instance/source/api/instanceShowColumnTemplateModelInterface";
  import {reactive, ref, nextTick, onMounted, toRefs} from "vue";
  import {ElMessage, ElMessageBox, FormRules} from "element-plus";
  import {pageSizeOptions} from "@/utils/page_util";
 const validataTemplateName = (rule,value,callback) => {
   if (!value) {
     return callback(new Error('请填写模板名称'));
   }
   var params={
     isAuth: true,
     template_id:state.template_id,
     template_name:value,
     template_code:"query",
     categoryId:props.categoryId
   };
   validataTemplateNameAxios(params).then(res=>{
     if(res.data.result=="success"){
       callback();
     }else{
       callback(new Error("该模板名称已存在，请修改"));
     }
   }).catch(exp=>{
     ElMessage.error(exp.message);
   })
 }
 const state = reactive({
   selectRow:[],
   categoryList:[],
   copyTableLoading:false,

   copyTableData:[],
   copyTableColumn:[
     {type: "selection", align: "center",prop:"template_id" },
     { label: "模板名称", prop: "template_name", align: "center" },
     { label: "模板类型", prop: "template_type", align: "center"},
     { label: "所属类别", prop: "template_category", align: "center"},
     { label: "创建人", prop: "template_user", align: "center"}
   ],
   copyTableColumnOption:{
     index: false,
     indexLabel: '序号',
     align: "center",
     menuAlign: "center",
     selection:true,
     selectionFixed:true,
     refreshBtn:false,
     columnBtn:false,
     gridBtn:false,
     menu: false,
     border: true,
     stripe: true,
     addBtn: false,
     editBtn: false,
     delBtn: false,
     column:[
       { label: "模板名称", prop: "template_name", align: "center" },
       { label: "模板类型", prop: "template_type", align: "center"},
       { label: "所属类别", prop: "template_category", align: "center"},
       { label: "创建人", prop: "template_user", align: "center"}
     ]
   },
   pagination: {
     total: 1,
     currentPage: 1,
     pageSize: 10,
     pageSizes: pageSizeOptions,
   },
   copyCategoryTemplateShow:false,
   copyCategoryTemplateLoading:false,
   queryModelForm:{
     template_name:"",
     template_type:"",
     template_category:""
   },
   copyCategoryLoading:false,
   showSelectCategory:false,
   selectCategory:'',
   selectNode:{},
   treeData:[],
   showSaveTemplate:false,
   treeLoading:false,
   treeList:[],
   checkCondition:[],
   conditionList:[],
   conditionList1:[],
   tableData:[],
   templateColumnLoading:false,
   template_id:'',
   templateColumnDisabled:true,
   filterTemplateColumn:'',
   templateForm:{
     template_name:'',
     template_type:''
   },
   roleId:"",
   ip_mask_state_save:"",
   ip_seg_state_save:"",
 })

  const formRules = reactive<FormRules<typeof state.templateForm>>({
    template_name:[{ validator: validataTemplateName, trigger: 'blur' }],
    template_type:[{ required: true, message: '请选择模板类型', trigger: 'change' }]
  })
  const {treeLoading,treeList,roleId,templateColumnLoading,templateColumnDisabled,filterTemplateColumn,ip_seg_state_save,ip_mask_state_save,checkCondition,
 conditionList1,showSaveTemplate,templateForm,showSelectCategory,copyCategoryLoading,treeData,copyCategoryTemplateShow,
    copyCategoryTemplateLoading,categoryList,queryModelForm,copyTableLoading,copyTableData,copyTableColumnOption} = toRefs(state)
  const props = defineProps({
    categoryId:{
      type:String||Number
    }
  })
  const treeRef = ref();
  const init = () =>{
    let params = {
      categoryId: props.categoryId,
      isAuth:true,
      template_id: state.template_id,
      template_conf_type:"query"
    };
    state.treeLoading=true;
    initColumnTemplateAxios(params).then(res=>{
      state.treeList = res.data.treeList;
      state.conditionList = res.data.dataList;
      state.conditionList1 = res.data.dataList;
      state.ip_mask_state_save = res.data.ip_mask_state;
      state.ip_seg_state_save = res.data.ip_seg_state;
      state.treeLoading=false;
      state.roleId = res.data.role;
      state.checkCondition=res.data.checkCondition;
      nextTick(()=>{
        if(state.template_id!=null&&state.template_id!=''){
          treeRef.value.select(state.template_id);
        }
      })
    }).catch(exp=>{
      state.treeLoading = false;
      ElMessage.error("查询失败，"+exp.message);
    })
  }
  const changeSwitch = (value) => {
    if (value == "open") {
      propertyExistsAxios(props.categoryId).then(res => {
        if (res.data == "success") {
          state.ip_mask_state_save = "open";
        } else {
          ElMessage.error("未添加IP值属性，无法开启");
          changeSwitch("close");
          state.ip_mask_state_save = "close";
        }
      });
    }
  }
  const changeSwitch1 = (value) => {
    if (value == "open") {
      propertyExistsAxios(props.categoryId).then(res => {
        if (res.data == "success") {
          state.ip_seg_state_save = "open";
        } else {
          ElMessage.error("未添加IP值属性，无法开启");
          changeSwitch1("close");
          state.ip_seg_state_save = "close";
        }
      });
    }
  }
  const saveCondition = () =>{
    if(state.templateForm.template_type==""){
      if(state.roleId=='Superman'){
        state.templateForm.template_type='common';
      }else{
        state.templateForm.template_type='owner';
      }
    }
    if(state.template_id!=null&&state.template_id!=''){
      let params = {
        templateForm: state.templateForm,
        template_id: state.template_id,
        template_conf_type: "query"
      };
      saveConditionTemplate(params);
    }else{
      state.showSaveTemplate = true;
    }
  }
  const saveConditionTemplate = (params) =>{
    let resultList = [];
    for (let i = 0; i < state.conditionList.length; i++) {
      let pp = {
        property_id: state.conditionList[i].value,
        category_id: props.categoryId
      };
      if (state.checkCondition.indexOf(state.conditionList[i].value) >= 0) {
        pp["conditions"] = 1;
      } else {
        pp["conditions"] = 0;
      }
      resultList.push(pp);
    }
    params.data = resultList;
    params.categoryId = props.categoryId;
    params.template_conf_type='query';
    params.ip_seg_state = state.ip_seg_state_save;
    params.ip_mask_state = state.ip_mask_state_save;
    state.templateColumnLoading=true;
    saveShowTemplateColumnAxios(params)
      .then(res => {
        if (res.data.result == "success") {
          ElMessage.success("保存成功");
          state.templateColumnLoading=false;
          state.showSaveTemplate = false;
          if(state.template_id==null||state.template_id==''){
            state.template_id = res.data.template_id;
            init();
          }
        } else {
          state.templateColumnLoading=false;
          state.showSaveTemplate = false;
          ElMessage.error(res.data);
        }
      })
      .catch(exp => {
        state.templateColumnLoading=false;
        state.showSaveTemplate = false;
        ElMessage.error(exp.message);
      });
  }
  const emit = defineEmits(["cancalCondition"]);
  const cancalCondition = () =>{
    emit("cancalCondition");
  }
  const addMuBan = (node) =>{
    state.templateColumnLoading=true;
    state.template_id = "";
    state.tableData=[];
    state.templateColumnDisabled=false;
    state.checkCondition=[];
    state.templateForm.template_name="";
    state.templateForm.template_type=node.id;
    treeRef.value.select(null);
    state.templateColumnLoading = false;
  }
  const showMuBan = (node) =>{
    treeRef.value.select(node.id);
    state.templateColumnLoading=true;
    state.template_id = node.id;
    initSelectTemplateShow(state.template_id);
    nextTick(()=>{
      state.templateColumnDisabled=true;
      state.templateForm.template_name=node.title;
      state.templateForm.template_type=node.parent_id;
      state.templateColumnLoading = false;
    })
  }
  const  updateMuBan = (node) =>{
    treeRef.value.select(node.id);
    state.templateColumnLoading=true;
    state.template_id = node.id;
    initSelectTemplateShow(state.template_id);
    nextTick(()=>{
      state.templateColumnDisabled=false;
      state.templateForm.template_name=node.title;
      state.templateForm.template_type=node.parent_id;
      state.templateColumnLoading = false;
    })
  }
  const deleteMuBan = (node) =>{
    ElMessageBox.confirm("确认要删除该模板吗？", "是否删除", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(()=>{
      let params={
        template_id:node.id,
        isAuth:true,
        template_conf_type:"query"
      };
      deleteTemplateAxios(params).then(res=>{
        if(res.data.result=='success'){
          state.templateColumnDisabled=false;
          state.templateForm.template_name="";
          state.templateForm.template_type="";
          state.checkCondition=[];
          treeRef.value.select(null);
          state.template_id='';
          init();
        }else{
          ElMessage.error(res.data.result);
          updateMuBan(node);
        }

      }).catch(exp=>{
        ElMessage.error("删除失败，"+exp.message);
        updateMuBan(node);
      })
    }).catch(()=>{
      updateMuBan(node);
    })
  }
  const initSelectTemplateShow = (template_id) =>{
    state.templateColumnLoading=true;

    let params = {
      template_id:template_id,
      isAuth:true,
      template_conf_type:"query"
    };
    state.checkCondition=[];
    querySelectTemplateColumnAxios(params).then(res=>{
      state.templateColumnLoading = false;
      state.checkCondition=res.data.checkCondition;
      state.ip_mask_state_save=res.data.ip_mask_state;
      state.ip_seg_state_save = res.data.ip_seg_state;
    }).catch(exp=>{
      state.templateColumnLoading = false;
      ElMessage.error(exp.message);
    })
  }
  const handleNodeClick = (node) =>{
    if(node.parent_id){
      showMuBan(node);
    }
  }
  const handleDbNodeClick = (node) =>{
    showMuBan(node);
    if((node.parent_id=='common'&&state.roleId=='Superman')||node.parent_id=='owner'){
      state.showSaveTemplate=true;
    }
  }
  const saveTemplateName = (params) =>{
    saveTemplateNameAxios(params).then(res=>{
      if(res.data.result=='success'){
        state.showSaveTemplate = false;
        ElMessage.success("保存成功");
        init();
      }else{
        ElMessage.error("保存失败，"+res.data.result);
      }
    }).catch(exp=>{
      ElMessage.error(exp.message);
    })
  }
  const onRenderNodeBefore = (data) => {
    data.tooltip = true;
  }
  const templateFormRef = ref();
  const saveShowTemplate = () =>{
    templateFormRef.value.validate(function(valid) {
      if (valid) {
        let params = {};
        params["templateForm"] = state.templateForm;
        if(state.template_id!=""){
          params["template_id"]=state.template_id;
        }
        params["template_conf_type"]='query';
        if(state.template_id==null||state.template_id==""){
          saveConditionTemplate(params);
        }else{
          saveTemplateName(params);
        }
      }
    });
  }
  const closeShowTemplate = () =>{
    state.showSaveTemplate = false;
  }
  const useAll = (node) =>{
    var params = {
      isAuth:true,
      template_conf_type:"query"
    };
    ElMessageBox.confirm("确认要将该模板应用到所有用户吗？如确认，用户已配置的也将被覆盖", "是否应用", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(()=>{
      params["categoryId"]=props.categoryId;
      params["template_id"] = node.id;
      useTemplateAllAxios(params).then(res=>{
        if(res.data.result=='success'){
          ElMessage.success("保存成功");
          updateMuBan(node);
        }
      }).catch(exp=>{
        ElMessage.error(exp.message);
        updateMuBan(node);
      })
    }).catch(exp=>{
      console.log(exp);
      updateMuBan(node);
    })
  }
  const useOne = (node) =>{
    var params = {
      isAuth:true,
      template_conf_type:"query"
    };
    ElMessageBox.confirm("确认要将该模板应用到当前用户吗？", "是否应用", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(()=>{
      params["categoryId"]=props.categoryId;
      params["template_id"] = node.id;
      useTemplateOneAxios(params).then(res=>{
        if(res.data.result=='success'){
          ElMessage.success("保存成功");
          if(state.roleId=='Superman'||node.parent_id=='owner'){
            updateMuBan(node);
          }else{
            showMuBan(node);
          }

        }
      }).catch(exp=>{
        ElMessage.error(exp.message);
        if(state.roleId=='Superman'||node.parent_id=='owner'){
          updateMuBan(node);
        }else{
          showMuBan(node);
        }
      })
    }).catch(exp=>{
      console.log(exp);
      if(state.roleId=='Superman'||node.parent_id=='owner'){
        updateMuBan(node);
      }else{
        showMuBan(node);
      }
    })
  }
  const changeFilter = (value) =>{
    console.log(value);
    state.conditionList1= state.conditionList.filter(
      item1 => {
        return item1.label.toLowerCase().indexOf(value.toLowerCase()) > -1;
      },
    );
  }
  const initTree = (node) =>{
    let params={
      isAuth:true,
      template_conf_type:"query",
      template_id:node.id,
      categoryId:props.categoryId,
    };
    initCategoryModelAxios(params).then(res=>{
      state.treeData = res.data.treeData;
    }).catch(err => {
      ElMessage.error(err);
    });
  }
  const copyMuBan = (node) =>{
    state.showSelectCategory = true;
    initTree(node);
    state.selectNode = node;
  }
  const saveCopyTemplate = () =>{
    if(state.selectCategory==''){
      ElMessage.error("请选择要克隆的类别");
      return;
    }
    state.copyCategoryLoading=true;
    let params = {
      template_id:state.selectNode["id"],
      categoryId:props.categoryId,
      template_conf_type:"query",
      selectCategory:state.selectCategory
    };
    saveTemplateCopyAxios(params).then(res=>{
      if(res.data.result=='success') {
        state.showSelectCategory = false;
        state.copyCategoryLoading = false;
        let htmlMessage = "";
        let ssl = res.data.message;
        if (ssl && ssl.length > 0) {
          for (let i = 0; i < ssl.length; i++) {
            htmlMessage += "<p>" + ssl[i] + "</p>";
          }
        }
        ElMessageBox.alert(htmlMessage, '克隆结果', {
          dangerouslyUseHTMLString: true,
          callback: () => {
            if (state.roleId == 'Superman' || state.selectNode["parent_id"] == 'owner') {
              updateMuBan(state.selectNode);
            } else {
              showMuBan(state.selectNode);
            }
          }
        });
      } else {
        state.copyCategoryLoading = false;
        ElMessage.error(res.data.message);
      }
    }).catch(exp=>{
      state.copyCategoryLoading = false;
      ElMessage.error(exp);
    })
  }
  const closeCopyTemplate = () =>{
    state.showSelectCategory = false;
    state.selectCategory='';
    if(state.roleId=='Superman'||state.selectNode["parent_id"]=='owner'){
      updateMuBan(state.selectNode);
    }else{
      showMuBan(state.selectNode);
    }
  }
  const categoryListTreeRef = ref();
  const copyCategoryMuBan = () =>{
    state.copyCategoryTemplateShow=true;
    let params={
      categoryId:props.categoryId,
      template_conf_type:"query"
    };
    initCategoryListByCopyTemplateAxios(params).then(res=>{
      state.categoryList = res.data.categoryList;
      if(state.categoryList!=null&&state.categoryList.length>0){
        //this.checkNode(this.categoryList[0]);
        nextTick(() => {
          categoryListTreeRef.value.select(state.categoryList[0].id);
          checkNode(state.categoryList[0]);
        });

      }else{
        nextTick(() => {
          state.copyTableData = [];
          state.pagination.total = 0;
        });
      }
    }).catch(exp=>{
      console.log(exp);
    })
  }
  const checkNode = (node) =>{
    state.selectRow=[];
    state.selectNode = node;
    initCopyTable();
  }
  const initCopyTable = () =>{
    state.copyTableLoading=true;
    let params={
      categoryId:props.categoryId,
      template_conf_type:"query",
      template_name:state.queryModelForm.template_name,
      template_type:state.queryModelForm.template_type,
      template_category:state.selectNode["id"],
      pageNum:state.pagination.currentPage,
      pageSize:state.pagination.pageSize
    };
    initCopyTableAxios(params).then(res=>{
      state.copyTableLoading=false;
      state.copyTableData = res.data.list;
      state.pagination.total =res.data.total;
    }).catch(exp=>{
      state.copyTableLoading=false;
      ElMessage.error(exp.message);
    })
  }
  const resetCopyTable = () =>{
    state.queryModelForm.template_name="";
    state.queryModelForm.template_type="";
    state.queryModelForm["template_select"]="";
    initCopyTable();
  }
  const saveCopyCategoryTemplate = () =>{
    if(state.selectRow.length==0){
      ElMessage.error("请选择要克隆的模板");
      return;
    }else{
      let params = {
        isAuth:true,
        categoryId:props.categoryId,
        selectTemplate:state.selectRow.join(","),
        template_conf_type:"query"
      };
      state.copyCategoryTemplateLoading = true;
      saveCopyCategoryTemplateAxios(params).then(res=>{
        state.copyCategoryTemplateLoading=false;
        if(res.data.result=='success'){
          let htmlMessage = "";
          let ssl = res.data.message;
          if(ssl&&ssl.length>0){
            for(let i =0;i<ssl.length;i++){
              htmlMessage += "<p>"+ssl[i]+"</p>";
            }
          }
          ElMessageBox.alert(htmlMessage, '克隆结果', {
            dangerouslyUseHTMLString: true,
            callback: () => {
              init();
              copyCategoryMuBan();
            }
          });
        }else{
          ElMessage.error(res.data.message);
        }
      }).catch(exp=>{
        state.copyCategoryTemplateLoading=false;
        ElMessage.error(exp.message);
      });
    }
  }
  const closeCopyCategoryTemplate = () =>{
    state.copyCategoryTemplateShow = false;
  }
  const changeSelectRow = (value) =>{
    state.selectRow = [];
    if(value.length==0){
      state.selectRow = [];
    }else{
      for(let i = 0;i<value.length;i++){
        state.selectRow.push(value[i].template_id);
      }
    }
  }
  const changeCategory = (value,value1) =>{
    if(value1.checkedKeys.length==0){
      state.selectCategory=null;
    }else{
      state.selectCategory=value1.checkedKeys.join(",");
    }
  }
  const handlePageSizeChange = (pageSize)=>{
    state.pagination.pageSize = pageSize;
    state.pagination.currentPage = 1;
    initCopyTable();
  }
  const handlePageCurrentChange = (currentPage) =>{
    state.pagination.currentPage = currentPage;
    initCopyTable();
  }
  onMounted(()=>{
    init();
  })
</script>
<style lang="scss" scoped>
.instanceConditionTemplate {
  :deep(.im-tree-node) > span:first-child {
    width: calc(100% - 100px);
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    overflow: hidden;
    vertical-align: top;
  }

  :deep(.im-tree-node) {
    width: calc(100% - 21px);
    display: inline-block;
  }
  :deep(.im-tree-node-operations) {
    width: 100px;
    display: inline-block;
    float: right;
    text-align: right;
  }
}
</style>
