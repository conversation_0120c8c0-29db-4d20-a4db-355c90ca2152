<template>
  <div class="flex-bc">
    <el-col :span="22">
      <el-page-header @back="jumpTo('assetTableInfoPage')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold"> {{ props.sourceInfo.infoTitle }} </span>
        </template>
      </el-page-header>
    </el-col>
    <el-col :span="2" style="text-align: right; padding-right: 10px">
      <el-button
        type="primary"
        plain
        v-if="saveButtonShow"
        @click="saveInstance"
        v-loading="saveButtonLoading"
        >保存</el-button
      >
      <el-button
        type="primary"
        plain
        v-if="exportButtonShow"
        @click="exportInstanceInfo"
        >导出</el-button
      >
    </el-col>
  </div>
  <div class="assetInfo eam-instance-asset-info">
    <!-- 头部更改 -->
    <!-- tab切换 -->
    <el-row>
      <el-col :span="24">
        <el-tabs
          v-model="activeName"
          class="center-tabs"
          @tab-click="handleClick"
        >
          <el-tab-pane
            label="资产信息"
            name="资产信息"
            v-if="props.sourceInfo.tabName.indexOf('资产信息') >= 0"
          >
            <el-row v-if="activeName == '资产信息'">
              <el-col :span="24">
                <assetBaseInfo
                  v-model:saveButtonLoading="saveButtonLoading"
                  :infoTitle="props.sourceInfo.infoTitle"
                  @init-instance-data="initInstanceData"
                  :categoryId="props.sourceInfo.categoryId"
                  :isLoading="assetBaseInfoLoading"
                  :instanceId="props.sourceInfo.instanceId"
                  ref="baseInfoRef"
                  :editType="props.sourceInfo.editType"
                  :baseInfoAllEdit="baseInfoAllEditData"
                  :allBaseInfoAllEdit="allBaseInfoAllEdit"
                  :tabLeftNameArray="tableleftTree"
                  @updateInstance="updateInstance"
                  :select-cate="props.sourceInfo.selectCate"
                  @jump-to="jumpTo"
                  @source-select="sourceSelect"
                  @query-left-tree="getLeftTree"
                />
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="资产关系拓扑"
            name="资产关系拓扑"
            v-if="props.sourceInfo.tabName.indexOf('资产关系拓扑') >= 0"
          >
            <el-row
              style="padding-top: 5px"
              v-if="activeName == '资产关系拓扑'"
            >
              <el-col :span="24">
                <asset-topo
                  style="height: 100%"
                  :category-id="props.sourceInfo.categoryId + ''"
                  :instance-id="props.sourceInfo.instanceId + ''"
                ></asset-topo>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="资产漏洞概览"
            name="资产漏洞概览"
            v-if="
              props.sourceInfo.tabName.indexOf('资产漏洞概览') >= 0 && isShowIp
            "
          >
            <el-row
              class="vulnerabilityInformation"
              v-if="activeName == '资产漏洞概览'"
            >
              <el-col :span="8" style="height: 16rem">
                <headerTitleCommonAll :title="riskSpreadName" />
                <avue-data-display
                  style="padding-top: 1rem"
                  :option="option"
                ></avue-data-display>
              </el-col>
              <el-col :span="6" style="height: 16rem">
                <headerTitleCommonAll :title="'漏洞分布情况'" />
                <div style="width: 100%; height: 100%">
                  <radiusMode :requestIp="showIp" />
                </div>
              </el-col>
              <el-col :span="10" style="height: 16rem; padding-left: 30px">
                <headerTitleCommonAll :title="'近七天漏洞发生趋势'" />
                <div style="width: 100%; height: 100%">
                  <brokenLineMode
                    :requestName="'漏洞信息'"
                    :requestIp="showIp"
                  />
                </div>
              </el-col>
              <el-row style="width: 100%; margin-top: -50px">
                <headerTitleCommonAll :title="'漏洞列表（近六个月）'" />
                <loopholeFilterTableCommon
                  :ipAddress="showIp"
                  :tableLeftFeature="[
                    { code: 'undisposed', name: '未修复' },
                    { code: 'disposalof', name: '已修复' },
                    { code: 'noNeedHandle', name: '无需处理' },
                    { code: 'notHandledYet', name: '暂不处理' }
                  ]"
                  :tableRightFeature="['批量处理', '导出']"
                />
              </el-row>
            </el-row>
          </el-tab-pane>
          <el-tab-pane
            label="安全风险告警"
            name="安全风险告警"
            v-if="
              props.sourceInfo.tabName.indexOf('安全风险告警') >= 0 && isShowIp
            "
          >
            <el-row
              class="vulnerabilityInformation"
              v-if="activeName == '安全风险告警'"
            >
              <el-col :span="12" style="height: 16rem">
                <headerTitleCommonAll :title="'重点风险关注'" />
                <div style="display: flex; justify-content: center">
                  <radiusMode :requestIp="showIp" :riskName="'重点风险关注'" />
                </div>
                <!-- <riskConcernDataShow :requestIp="`**************`" style="margin-top: 3rem" /> -->
              </el-col>
              <el-col :span="12" style="height: 16rem">
                <headerTitleCommonAll :title="'近七天风险发生趋势'" />
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    margin-left: -5rem;
                    margin-top: 0.6rem;
                  "
                >
                  <brokenLineMode
                    :requestName="'风险告警'"
                    :requestIp="showIp"
                  />
                </div>
              </el-col>
              <el-row
                style="
                  display: flex;
                  flex-direction: column;
                  width: 100vw;
                  margin-top: -20px;
                "
              >
                <headerTitleCommonAll :title="'风险列表'" />
                <!-- <FilterTableCommon style="margin-top: 1rem" :tableLeftFeature="[
                    '待处置',
                    '已处置',
                    '已封堵',
                    '白名单',
                    '误报'
                  ]" :tableRightFeature="['封堵', '批量处理', '导出']" /> -->
                <Deal :request-ip="showIp" />
              </el-row>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
import type { TabsPaneContext } from "element-plus";
import assetBaseInfo from "@/views/modules/eam/zcgl/instance/source/component/assetBaseInfo.vue";
import headerTitleCommonAll from "@/views/modules/eam/zcgl/instance/source/component/headerTitleCommonAll.vue";
import radiusMode from "@/views/modules/eam/zcgl/instance/source/component/radiusMode.vue";
import brokenLineMode from "@/views/modules/eam/zcgl/instance/source/component/brokenLineMode.vue";
import loopholeFilterTableCommon from "@/views/modules/eam/zcgl/instance/source/component/loopholeFilterTableCommon.vue";
import cardCommon from "@/views/modules/eam/zcgl/instance/source/component/cardCommon.vue";
import {
  getInstanceByIdApi,
  queryPropertyEnumByInstanceAxios,
  initAssetInfoLeftTree,
  exportInstanceInfoAxios,
  queryIsShowIpsAxios
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";

import AssetTopo from "@/views/modules/eam/zcgl/instance/source/component/AssetTopo.vue";
const baseInfoRef = ref(assetBaseInfo);

import { vulnerabilityStatisticsMethod } from "@/views/modules/eam/zcgl/instance/source/api/vulnerabilityInformation";
import Deal from "@/views/modules/eam/zcgl/instance/source/component/listOfRisksIntroduced/deal.vue";
const props = defineProps({
  sourceInfo: {
    type: Object
  }
});
const saveButtonLoading = ref(false);
const saveInstance = () => {
  baseInfoRef.value.saveInstance();
};
const activeName = ref(props.sourceInfo.tabName[0]);
const baseInfoAllEditData = ref([]);
const allBaseInfoAllEdit = ref({});
const tableleftTree = ref([]);
//
const assetBaseInfoLoading = ref(false);

const saveButtonShow = ref(
  props.sourceInfo.editType == "edit" || props.sourceInfo.editType == "add"
);
const exportButtonShow = ref(
  props.sourceInfo.editType == "edit" || props.sourceInfo.editType == "show"
);
function buildTree(
  data: any[],
  deptId: string | number,
  parentId: string | number
) {
  // 创建一个映射，用于快速查找节点
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, children: [] });
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
}

const tableWidth = ref(566);
window.addEventListener("resize", updateWindowWidth);
function updateWindowWidth() {
  tableWidth.value = window.innerWidth - 240;
}
onMounted(() => {
  updateWindowWidth();
});

const queryPropertyEnumByInstanceMethod = params => {
  const tmpParams = {
    categoryId: props.sourceInfo.categoryId,
    isAuth: true,
    property: params
  };
  try {
    queryPropertyEnumByInstanceAxios(tmpParams).then(res => {
      if (params.showType == "comboTree") {
        params.enumArray.push(
          ...buildTree(res?.data?.enumArray, "id", "parentId")
        );
      } else {
        params.enumArray = res?.data?.enumArray;
      }
    });
  } catch (error) {}
};
const getInstanceByIdMethod = async query => {
  try {
    assetBaseInfoLoading.value = true;
    getInstanceByIdApi(query).then(res => {
      const rowsObj = res?.["data"]?.["rowsObj"];
      allBaseInfoAllEdit.value = res?.["data"];
      baseInfoAllEditData.value = rowsObj;
      if (baseInfoAllEditData.value.length > 0) {
        baseInfoAllEditData.value.forEach((item: Array<any>) => {
          item?.["rows"].forEach((item: Array<any>) => {
            item?.["colList"].forEach(item => {
              if (
                item?.field?.showType == "combobox" ||
                item?.field?.showType == "comboTree" ||
                item?.field?.showType == "mul_combobox" ||
                item?.field?.showType == "checkbox" ||
                item?.field?.showType == "radio"
              ) {
                queryPropertyEnumByInstanceMethod(item.field);
              }
              if (item?.field?.showType == "objectTable") {
                // item.field.column = [];
                Reflect.set(item.field, "column", []);
                Reflect.set(item.field, "tmpValue", [
                  {
                    macAddress: "AA-BB-BA-BC-CA-DC",
                    sortId: 1,
                    ipname: "*************",
                    Nameofnetworkcard: "echo33",
                    remarks: ""
                  }
                ]);
                Reflect.set(item.field, "operator", {
                  label: "操作",
                  fixed: "right",
                  width: "166"
                });
                if (item.field["value"].length == 0) {
                  item.field["value"].push({
                    macAddress: "",
                    sortId: 1,
                    ipname: "",
                    Nameofnetworkcard: "",
                    remarks: ""
                  });
                }
                item.field?.tableList.forEach((item02, index) => {
                  let isTree = false;
                  if (item02.showType == "comboTree") {
                    isTree = true;
                  }

                  item.field.column.push({
                    // ...item02,
                    prop: item02.code,
                    label: item02.name,
                    // item02.showType
                    showType: item02.showType,
                    isAuto: item02.isAuto,
                    dataType: item02.dataType,
                    code: item02.code,
                    property: item02.property,
                    dataLength: item02.dataLength,
                    isReEdit: item02.isReEdit,
                    tableList: isTree
                      ? [...buildTree(item02.enumArray, "id", "parentId")]
                      : item02.enumArray,
                    name: item02.name,
                    enumArray: item02.enumArray,
                    titleDesc: item02.titleDesc,
                    width: item02.width,
                    align: item02.align,
                    ruleData: item02.rule
                  });
                  if (index + 1 == item.field.tableList.length) {
                    item.field.column.push({
                      prop: "operator",
                      label: "操作"
                    });
                  }
                });
              }
            });
          });
        });
      }
      assetBaseInfoLoading.value = false;
    });
  } catch (error) {
    assetBaseInfoLoading.value = false;
  }
};
const getLeftTree = async () => {
  if (
    props.sourceInfo.editType == "edit" ||
    props.sourceInfo.editType == "show"
  ) {
    let params = {};
    params.categoryId = props.sourceInfo.categoryId;
    params.instanceId = props.sourceInfo.instanceId;
    initAssetInfoLeftTree(params).then(res => {
      tableleftTree.value = res.data;
    });
  } else {
    let data = {};
    data.id = "-1";
    data.name = "基本信息";
    tableleftTree.value.push(data);
  }
};
onMounted(() => {
  queryIsShowIps();
  getLeftTree();
  const tmpObj = {
    id: props.sourceInfo.instanceId,
    categoryId: props.sourceInfo.categoryId,
    isAuth: "true"
  };
  getInstanceByIdMethod(tmpObj);
});

const handleClick = (tab: TabsPaneContext, event: Event) => {
  if (tab.props.name == "资产信息") {
    if (
      props.sourceInfo.editType == "edit" ||
      props.sourceInfo.editType == "add"
    ) {
      saveButtonShow.value = true;
    }
    if (
      props.sourceInfo.editType == "edit" ||
      props.sourceInfo.editType == "show"
    ) {
      exportButtonShow.value = true;
    }
  } else {
    saveButtonShow.value = false;
    exportButtonShow.value = false;
  }
};

// 漏洞统计option
const riskSpreadName = ref("待修复漏洞统计");

const option = reactive({
  span: 6,
  data: []
});
const vulnerabilityAllMethod = {
  vulnerabilityStatisticsAxios: () => {
    // if(){return}  后续仅有在查看详情条件下打开的时候才会调用
    try {
      vulnerabilityStatisticsMethod({
        ips: showIp.value,
        dealStatus: "undisposed"
      }).then(res => {
        console.log("调用漏洞接口返回结果");
        console.log(res);
        riskSpreadName.value = res["data"]["title"];
        option.data = [];

        res["data"]["rows"].forEach(item => {
          option.data.push({
            ...item,
            title: item["risk"],
            color: (() => {
              switch (item["risk"]) {
                case "危急":
                  return "#A50003";
                  break;
                case "高危":
                  return "#FF0408";
                  break;
                case "中危":
                  return "#FF964B";
                  break;
                case "低危":
                  return "#0091FF";
                  break;
                case "信息":
                  return "#028015";
                  break;
                default:
                  return "#028015";
                  break;
              }
            })()
          });
        });
        option.span = parseInt(24 / option.data.length);
      });
    } catch (error) {}
  }
};

const updateInstance = data => {
  props.sourceInfo.tabName = [
    "资产信息",
    "资产关系拓扑",
    "资产漏洞概览",
    "安全风险告警"
  ];
  props.sourceInfo.instanceId = data.id;
  allBaseInfoAllEdit.value["id"] = data.id;
  props.sourceInfo.editType = "edit";
  getLeftTree();
};
const emit = defineEmits(["jump-to", "source-select"]);
const jumpTo = (sign: string) => {
  if (sign === "assetTableInfoPage") {
    let categoryId = props.sourceInfo.categoryId;
    sourceSelect(categoryId);
  }
  emit("jump-to", sign);
};
const sourceSelect = data => {
  emit("source-select", data);
};
const initInstanceData = () => {
  const tmpObj = {
    id: props.sourceInfo.instanceId,
    categoryId: props.sourceInfo.categoryId,
    isAuth: "true"
  };
  getInstanceByIdMethod(tmpObj);
};
const isShowIp = ref(true);
const showIp = ref("");
const queryIsShowIps = async () => {
  const params = props.sourceInfo?.row;
  queryIsShowIpsAxios(params).then(res => {
    if (res["data"].isShow == "1") {
      isShowIp.value = true;
      showIp.value = res["data"].ipAddress;
      vulnerabilityAllMethod.vulnerabilityStatisticsAxios();
    } else {
      isShowIp.value = false;
    }
  });
};
const exportInstanceInfo = () => {
  let params = {
    instanceId: props.sourceInfo.instanceId
  };
  exportInstanceInfoAxios(params).then(() => {});
};
</script>
<style lang="scss" scoped>
:deep(.headerFeature) {
  .el-button {
    font-size: 14px;
    height: 86%;
  }
}

:deep(.center-tabs) {
  .el-tabs__item {
    font-size: 17px;
  }

  .change-log {
    font-size: 16px;
    font-weight: 500;

    .el-tag {
      vertical-align: 1px;

      .el-tag__content {
        max-width: 7rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .el-timeline-item__wrapper {
      top: -16 !important;
    }
  }

  .vulnerabilityInformation {
    .count {
      font-size: 28px;
      width: 6rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .title {
      font-weight: 500;
    }
  }
}
</style>
