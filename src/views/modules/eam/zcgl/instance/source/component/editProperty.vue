<template>
  <div v-loading="saveLoading">
    <div class="flex-bc">
      <el-col :span="22">
        <el-page-header @back="jumpTo('assetInformationPage')" class="mb-2">
          <template #content>
            <span class="mr-3 font-bold"> 关系资产维护 </span>
          </template>
        </el-page-header>
      </el-col>
      <el-col :span="2" style="text-align: right;padding-right:10px;">
        <el-button type="primary" plain @click="saveInstance(ruleFormRef)" v-loading="saveButtonLoading">保存</el-button>
      </el-col>
    </div>
    <div v-if="state.relationCategoryId=='-2'&&props.sourceInfo.editType=='add'" style="width:30%;margin-top:30px;margin-bottom:30px;margin-left:5%;">
      <el-form>
        <el-form-item label="请选择类别" prop="categoryId">
          <el-select v-model="dataForm.categoryId" filterable clearable @change="selectCateChange">
            <el-option v-for="(item,index) in categoryList" :label="item.label" :value="item.value" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div style="width:90%;margin:auto;margin-top:20px;">
      <el-form :rules="allRules"  ref="ruleFormRef" :model="propertyForm"
               label-width="80px">
        <el-row class="addPropertyEdit">
          <el-col v-loading="propertyLoading" style="height: 69vh; overflow-y: scroll; position: relative" :span="24">
            <template v-for="(cols, index0) in propertyForm.rowsObj" :key="index0" style="border: 1px solid #f40">
              <el-row style="padding-bottom: 1.6rem">
                <el-col class="topDivider" v-if="cols['name']!='-1'" :span="24">
                  <el-divider content-position="left">
                    {{ cols["name"] }}
                  </el-divider>
                </el-col>
              </el-row>
              <el-row v-for="(col, index1) in cols['rows']" :key="index1">
                <el-col v-for="(item, index01) in col['colList']"
                        :span="item.field.showType == 'objectTable'||item.field.showType=='table' ? 24 : item.span" :key="index01"
                        v-show="item.field != null &&
                      item.rule != null &&
                      item.field.property != 'readonly' &&
                      ((item.field.isAuto!='1'&&item.field.property!='readonly')|| propertyForm.proList && propertyForm.proList.indexOf(item.field.code)>=0)">
                  <!--可编辑的属性-->
                  <div >
                    <!-- <div>{{ item['rule']['field'] }}</div> -->
                    <el-form-item :class="item['field']['rule']" :prop="item['rule']['field']"
                                  :label="item.field.propertyUnit != null &&
                      item.field.propertyUnit != ''?item.field.name +
                          '(' +
                          item.field.propertyUnit +
                          ')：':item.field.name + '：'" :key="index1" style="
                          position: relative;
                          white-space: nowrap;
                          padding-left: 1.6rem;
                          margin-bottom: 1.6rem;
                        " :rules="{
                          required: item.rule.request, type: item.rule.type, name: item.rule.name,
                          message: item.rule.message, trigger: item.rule.trigger, value: item['field']['value'],
                           validator:(rule, value, callback)=>{constMust(rule, value, callback,item)}
                        }">
                      <!--数字-->
                      <span style="width: 90%" v-if="
                          item.field.showType == 'number' &&
                          item.field.isAuto != 1
                        ">
                          <span v-if="item.field.dataLength != null">
                            <el-input-number v-model="item.field.value" :disabled="item.field.isEdit == 0
                              ? false
                              : true
                              " :max="item.field.dataLength"></el-input-number>
                          </span>
                          <span v-if="item.field.dataLength == null">
                            <el-input-number v-model="item.field.value" :disabled="item.field.isEdit == 0
                              ? false
                              : true
                              "></el-input-number>
                          </span>
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'windowboxTree'">
                          <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                                    class="ellipsis" :placeholder="item.field.name" :disabled="
                                item.field.isEdit == 0
                                ? false
                                : true
                                ">
                            <!-- <el-button slot="prepend" icon="el-icon-search" @click="openWindowTree(item)"></el-button> -->
                            <!-- <el-button slot="append" icon="el-icon-close" @click="clearWindowTree(item)"></el-button> -->
                          </el-input>
                        </span>

                      <!--普通输入框 input （string，number）-->
                      <span style="width: 90%" v-if="
                          item.field.showType == 'input' &&
                          item.field.isAuto != 1
                        ">
                          <!--数据类型 input、number、text、select、checkbox、radio-->
                          <span v-if="item.field.dataType == 'string'">
                            <span v-if="item.field.dataLength != null">
                              <el-input v-model="item.field.value" :maxlength="item.field.dataLength" :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="item.field.isEdit == 0
                                  ? false
                                  : true
                                  "></el-input>
                            </span>
                            <span v-if="item.field.dataLength == null">
                              <el-input v-model="item.field.value" :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="item.field.isEdit == 0
                                  ? false
                                  : true
                                  "></el-input>
                            </span>
                          </span>
                          <span v-if="item.field.dataType == 'long'">
                            <span v-if="item.field.dataLength != null">
                              <el-input-number v-model="item.field.value" :max="item.field.dataLength" :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-input-number>
                            </span>
                            <span v-if="item.field.dataLength == null">
                              <el-input-number v-model="item.field.value" :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-input-number>
                            </span>
                          </span>
                        </span>
                      <!--富文本-->
                      <span style="width: 90%" v-if="item.field.showType == 'textarea'">
                          <!--数据类型 input、number、text、select、checkbox、radio-->
                          <span v-if="item.field.dataLength != null">
                            <el-input v-model="item.field.value" type="textarea" :maxlength="item.field.dataLength"
                                      :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="item.field.isEdit == 0
                                  ? false
                                  : true
                                  "></el-input>
                          </span>
                          <span v-if="item.field.dataLength == null">
                            <el-input v-model="item.field.value" type="textarea" :placeholder="item.field.isEdit == 0
                              ? item.field.name
                              : ''
                              " :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-input>
                          </span>
                        </span>

                      <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                      <span class="time" style="width: 90%" v-if="item.field.showType == 'dateTime'">
                          <el-date-picker v-model="item.field.value" type="datetime" :disabled="item.field.isEdit == 0
                            ? false
                            : true
                            "></el-date-picker>
                        </span>
                      <!--日期输入框 yyyy-MM-dd-->
                      <span class="time" style="width: 90%" v-if="item.field.showType == 'date'">
                          <el-date-picker v-model="item.field.value" type="date" :disabled="item.field.isEdit == 0
                            ? false
                            : true
                            "></el-date-picker>
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'checkbox'">
                          <el-checkbox-group v-model="item.field.value">
                            <el-checkbox :label="cn.value" v-for="cn in item.field.enumArray" :disabled="item.field.isEdit == 0
                              ? false
                              : true
                              ">{{ cn.label }}
                            </el-checkbox>
                          </el-checkbox-group>
                        </span>

                      <!-- 复选下拉-->
                      <span style="width: 90%" v-if="item.field.showType == 'mul_combobox'">
                          <el-select filterable :multiple="true" :placeholder="item.field.isEdit == 0
                            ? '请选择'
                            : '  '
                            " v-model="item.field.value" :disabled="item.field.isEdit == 0
                              ? false
                              : true
                              ">
                            <el-option v-for="en in item.field.enumArray" :value="en.value"
                                       :label="en.label"></el-option>
                          </el-select>
                        </span>
                      <span style="width: 100%" v-if="item.field.showType == 'comboTree'">
                          <!-- <im-select-tree
                            :placeholder="(type=='add'||type=='edit')&&item.field.isEdit==0? '请选择':'  '"
                            clearable
                            filter-on-input :model="item.field.enumArray" children-key="children" :disabled="(type=='add'||type=='edit')&&item.field.isEdit==0?false:true"
                            fixed-position :maxContentHeight=265 v-model="item.field.value" id-key="id" title-key="title" >
                          </im-select-tree> -->
                          <el-tree-select :props="defaultProps"
                                          :disabled="item.field.isEdit == 0 ? false : true"
                                          children-key="children" node-key="id" node-value="id" id-key="id" title-key="title"
                                          clearable :placeholder="
                              item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " v-model="item.field.value" :data="item.field.enumArray" check-strictly
                                          :render-after-expand="false" style="width: 90%" />
                        </span>
                      <span v-if="item.field.showType == 'mul_windowbox'" :title="item.field.showValue">
                          <el-input v-model="item.field.showValue" :title="item.field.showValue" class="ellipsis"
                                    readonly clearable :placeholder="item.field.isEdit == 0
                              ? item.field.name
                              : ''
                              " :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                ">
                            <!-- <el-button slot="prepend" icon="el-icon-search" @click="openMulSelect(item)"></el-button> -->
                            <!-- <el-button slot="append" icon="el-icon-close" @click="clearMulSelect(item)"></el-button> -->
                          </el-input>
                        </span>

                      <!--单选-->
                      <span v-if="item.field.showType == 'radio'">
                          <el-radio-group v-model="item.field.value">
                            <el-radio :value="cn.value" v-for="cn in item.field.enumArray">
                              {{ cn.label }}
                            </el-radio>
                          </el-radio-group>
                        </span>
                      <span v-if="
                          item.field.isCascade == 1 &&
                          item.field.activeProp == ''
                        ">
                          <!--主动级联，且不被别的属性关联-->
                        <!-- 下拉选 -->
                          <span v-if="item.field.showType == 'combobox'">
                            <el-select filterable :placeholder="
                              item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " v-model="item.field.value" @on-change="selectActiveCase(item)" clearable :disabled="
                                item.field.isEdit == 0
                                ? false
                                : true
                                ">
                              <el-option v-for="en in item.field.enumArray" :value="en.value" :key="en.value"
                                         :label="en.label"></el-option>
                            </el-select>
                          </span>
                          <span v-if="item.field.showType == 'windowbox'">
                            <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                                      class="ellipsis" :placeholder="
                                item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="
                                  item.field.isEdit == 0
                                  ? false
                                  : true
                                  ">
                            </el-input>
                          </span>
                          <span v-if="item.field.showType == 'cascader'">
                            <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " @on-change="selectActiveCase(item)" clearable :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-cascader>
                          </span>
                        </span>

                      <span v-if="
                          item.field.isCascade == 1 &&
                          item.field.activeProp != ''
                        ">
                          <!--主动级联，且被别的属性关联-->
                        <!-- 下拉选 -->
                          <span v-if="item.field.showType == 'combobox'">
                            <el-select filterable v-model="item.field.value" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " @on-change="selectActiveCase(item)" @on-open-change="openBeSelect(item.field.value,item)" clearable
                                       :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                ">
                              <el-option v-for="en in item.field.enumArray" :value="en.value"
                                         :label="en.label"></el-option>
                            </el-select>
                          </span>
                          <span v-if="item.field.showType == 'windowbox'">
                            <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                                      class="ellipsis" :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="
                                  item.field.isEdit == 0
                                  ? false
                                  : true
                                  ">
                              <!-- <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button> -->
                              <!-- <el-button slot="append" icon="el-icon-close" @click="clearSelect(item)"></el-button> -->
                            </el-input>
                          </span>
                          <span v-if="item.field.showType == 'cascader'">
                            <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " @on-change="selectActiveCase(item)" @on-open-change="openBeSelect(item.field.value,item)" clearable
                                         :disabled="
                                item.field.isEdit == 0
                                ? false
                                : true
                                "></el-cascader>
                          </span>
                        </span>

                      <span style="width: 90%" v-if="
                          item.field.isCascade == 0 &&
                          item.field.activeProp == ''
                        ">
                          <!--不是主动级联,也不被级联的-->
                        <!-- 下拉选 -->
                          <span v-if="item.field.showType == 'combobox'">
                            <el-select filterable :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " v-model="item.field.value" clearable :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                ">
                              <el-option v-for="en in item.field.enumArray" :value="en.value"
                                         :label="en.label"></el-option>
                            </el-select>
                          </span>
                          <span v-if="item.field.showType == 'windowbox'">
                            <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                                      class="ellipsis" :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="
                                  item.field.isEdit == 0
                                  ? false
                                  : true
                                  ">
                              <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button>
                              <el-button slot="append" icon="el-icon-close" @click="clearSelect(item)"></el-button>
                            </el-input>
                          </span>
                          <span v-if="item.field.showType == 'cascader'">
                            <el-cascader :options="item.field.enumArray" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " v-model="item.field.value" clearable :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-cascader>
                          </span>
                        </span>
                      <span v-if="
                          item.field.isCascade == 0 &&
                          item.field.activeProp != ''
                        ">
                          <!--不是主动级联,但是被级联的-->
                        <!-- 下拉选 -->
                          <span v-if="item.field.showType == 'combobox'">
                            <el-select filterable v-model="item.field.value" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " @on-open-change="openBeSelect(item.field.value,item)" clearable :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                ">
                              <el-option v-for="en in item.field.enumArray" :value="en.value"
                                         :label="en.label"></el-option>
                            </el-select>
                          </span>
                          <span v-if="item.field.showType == 'windowbox'">
                            <el-input v-model="item.field.showValue" readonly clearable :title="item.field.showValue"
                                      class="ellipsis" :placeholder="item.field.isEdit == 0
                                ? item.field.name
                                : ''
                                " :disabled="item.field.isEdit == 0
                                  ? false
                                  : true
                                  ">
                              <!-- <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button> -->
                              <!-- <el-button slot="append" icon="el-icon-close" @click="clearSelect(item)"></el-button> -->
                            </el-input>
                          </span>
                          <span v-if="item.field.showType == 'cascader'">
                            <el-cascader :options="item.field.enumArray" v-model="item.field.value" :placeholder="item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " @on-open-change="openBeSelect(item.field.value,item)" clearable :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-cascader>
                          </span>
                        </span>

                      <span style="width: 90%" v-if="item.field.isAuto == 1"><!--自动生成-->
                        <!-- v-if="item.field.showType != 'number'" -->
                          <el-input v-if="item.field.showType != 'number'" v-model="item.field.value" readonly
                                    :placeholder="item.field.isEdit == 0
                              ? item.field.name
                              : ''
                              " :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                " />
                          <template v-if="item.field.showType == 'number'">
                            <span v-if="item.field.dataLength != null">
                              <el-input-number style="width: 100%" v-model="item.field.value" :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                " :max="item.field.dataLength"></el-input-number>
                            </span>
                            <span v-if="item.field.dataLength == null">
                              <el-input-number style="width: 100%" v-model="item.field.value" :disabled="item.field.isEdit == 0
                                ? false
                                : true
                                "></el-input-number>
                            </span>
                          </template>
                        </span>
                      <span v-if="
                          item.field.titleDesc != null &&
                          item.field.titleDesc != undefined &&
                          item.field.titleDesc != '' &&
                          item.field.titleDesc != 'null'
                        " style="position: absolute; top: 0px; left: -18px" :title="item.field.titleDesc">
                          <i type="ios-help-circle-outline" size="15" />
                        </span>
                      <span style="width: 100%" v-if="item.field.showType == 'table'">
                          <div style="
                              overflow: hidden;
                              display: flex;
                              align-items: center;
                            ">
                            <el-upload action="/rest/eam-core/zcgl-common/upload/upload" ref="upload" :on-success="(res, file) => {
                                handleSuccess(res, file, item);
                              }
                                " :show-file-list="false" :format="[
                                  'pdf',
                                  'txt',
                                  'doc',
                                  'docx',
                                  'xls',
                                  'xlsx',
                                  'jpeg',
                                  'jpg',
                                  'png'
                                ]" :on-format-error="handleFormatError" :max-size="50000" style="float: left"
                                       :on-exceeded-size="handleMaxSize">
                              <el-button @click="() => {
                                item.span = 24;
                                item.field.hide = true;
                              }
                                " size="small" type="primary">附件上传</el-button>
                            </el-upload>
                            <i class="el-icon-circle-plus-outline" v-if="!item.field.hide" style="
                                font-size: 14px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 24;
                                item.field.hide = !item.field.hide;
                              }
                                ">展开</i>
                            <i class="el-icon-remove-outline" v-else style="
                                font-size: 14px;
                                line-height: 24px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 12;
                                item.field.hide = !item.field.hide;
                              }
                                ">收起</i>
                          </div>
                          <div v-if="item.field.hide" style="margin-top: 10px; position: relative">
                            <el-table border max-height="200" :data="item.field.tableList">
                              <el-table-column type="index" label="序号" width="80px" align="center">
                              </el-table-column>
                              <el-table-column label="文件大小" align="center" prop="fileSize">
                              </el-table-column>
                              <el-table-column label="附件名称" align="center" prop="fileName">
                              </el-table-column>
                              <el-table-column label="操作" align="center" prop="action" width="150">
                                <template #default="scope">
                                  <el-button size="small" @click="
                                    download(
                                      scope.row.fullPath,
                                      scope.row.fileName
                                    )
                                    ">下载</el-button>
                                  <el-popconfirm @confirm="deleteUploadData(item, scope.row)" title="确认要删除吗?">
                                    <template #reference>
                                      <el-button size="small" >删除</el-button>
                                    </template>
                                  </el-popconfirm>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </span>
                      <span v-if="item.field.showType=='objectTable'">
                    <object-table :ref="(el) => getObjRef(el,item.field.code)" :categoryIdP="props.sourceInfo.relationCategoryId" :proCode="item.field.code" :proOcode="item.field.ocode" :type="props.sourceInfo.editType"
                                  @validateObjectFiled="validateObjectFiled(item.field.code)"   v-model:tableData="item.field.value"  :tableColumn="item.field.tableList"></object-table>
                        </span>
                    </el-form-item>
                  </div>


                  <!--只读的属性-->
                  <div v-if="
                      item.field != null &&
                      item.field.property == 'readonly' &&
                      item.field.propertyUnit != null &&
                      item.field.propertyUnit != ''
                    ">
                    <el-form-item :label="item.field.propertyUnit == null ||
                        item.field.propertyUnit == ''?item.field.name +
                        '(' +
                        item.field.propertyUnit +
                        ')：':item.field.name+'：'
                        " style="
                          position: relative;
                          white-space: nowrap;
                          padding-left: 1.6rem;
                          margin-bottom: 1.6rem;
                        ">
                        <span style="width: 90%" v-if="item.field.showType == 'combobox'">
                          <el-select disabled :placeholder="
                            item.field.isEdit == 0
                            ? '请选择'
                            : '  '
                            " v-model="item.field.value">
                            <el-option v-for="en in item.field.enumArray" :value="en.value"
                                       :label="en.label"></el-option>
                          </el-select>
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'windowbox'">
                          <el-input v-model="item.field.showValue" :title="item.field.showValue" class="ellipsis"
                                    :placeholder="
                              item.field.isEdit == 0
                              ? item.field.name
                              : ''
                              " readonly />
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'cascader'">
                          <el-cascader :data="item.field.enumArray" :placeholder="
                            item.field.isEdit == 0
                            ? '请选择'
                            : '  '
                            " v-model="item.field.value"></el-cascader>
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'checkbox'">
                          <el-checkbox-group v-model="item.field.value">
                            <el-checkbox :value="cn.value" disabled v-for="cn in item.field.enumArray"
                                         :label="cn.label">
                            </el-checkbox>
                          </el-checkbox-group>
                        </span>
                      <!--单选-->
                      <span style="width: 90%" v-if="item.field.showType == 'radio'">
                          <el-radio-group v-model="item.field.value">
                            <el-radio :value="cn.value" disabled :key="cn.value"
                                      v-for="cn in item.field.enumArray">
                              {{ cn.label }}
                            </el-radio>
                          </el-radio-group>
                        </span>

                      <span style="width: 90%" v-if="
                          item.field.showType != 'radio' &&
                          item.field.showType != 'checkbox' &&
                          item.field.showType != 'combobox' &&
                          item.field.showType != 'cascader'
                        ">
                          <el-input v-model="item.field.value" class="inputBorder" readonly></el-input>
                        </span>
                      <span v-if="
                          item.field.titleDesc != null &&
                          item.field.titleDesc != undefined &&
                          item.field.titleDesc != '' &&
                          item.field.titleDesc != 'null'
                        " style="
                            position: absolute;
                            top: 0px;
                            left: -18px;
                            width: 90%;
                          " :title="item.field.titleDesc">
                          <i type="ios-help-circle-outline" size="15" />
                        </span>
                      <span style="width: 90%" v-if="item.field.showType == 'comboTree'">
                          <!-- <im-select-tree clearable :placeholder="
                            item.field.isEdit == 0
                            ? '请选择'
                            : '  '
                            " filter-on-input :model="item.field.enumArray" children-key="children" fixed-position
                            :maxContentHeight="265" v-model="item.field.value" id-key="id" title-key="title">
                          </im-select-tree> -->
                          <el-tree-select :props="defaultProps" disabled children-key="children" node-key="id"
                                          node-value="id" id-key="id" title-key="title" clearable :placeholder="
                              item.field.isEdit == 0
                              ? '请选择'
                              : '  '
                              " v-model="item.field.value" :data="item.field.enumArray" check-strictly
                                          :render-after-expand="false" style="width: 90%" />
                        </span>
                      <span style="width: 100%" v-if="item.field.showType == 'table'">
                          <div style="overflow: hidden">
                            <el-upload action="/rest/eam-core/zcgl-common/upload/upload" ref="upload" :on-success="(res, file) => {
                              handleSuccess(res, file, item);
                            }
                              " :show-file-list="false" :format="[
                                'pdf',
                                'txt',
                                'doc',
                                'docx',
                                'xls',
                                'xlsx',
                                'jpeg',
                                'jpg',
                                'png'
                              ]" :on-format-error="handleFormatError" :max-size="50000" disabled style="float: left"
                                       :on-exceeded-size="handleMaxSize">
                              <el-button @click="() => {
                                item.span = 24;
                                item.field.hide = true;
                              }
                                " icon="ios-cloud-upload-outline" size="small" type="primary">附件上传</el-button>
                            </el-upload>
                            <i class="el-icon-circle-plus-outline" v-if="!item.field.hide" style="
                                font-size: 24px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 24;
                                item.field.hide = !item.field.hide;
                              }
                                ">展开</i>
                            <i class="el-icon-remove-outline" v-else style="
                                font-size: 24px;
                                line-height: 24px;
                                float: left;
                                margin-left: 10px;
                                cursor: pointer;
                                color: #66b1ff;
                              " @click="() => {
                                item.span = 12;
                                item.field.hide = !item.field.hide;
                              }
                                ">收起</i>
                          </div>
                          <div v-if="item.field.hide" style="margin-top: 10px; position: relative">
                            <el-table border max-height="200" :data="item.field.tableList">
                              <el-table-column type="index" label="序号" width="80px" align="center">
                              </el-table-column>
                              <el-table-column label="文件大小" align="center" prop="fileSize">
                              </el-table-column>
                              <el-table-column label="附件名称" align="center" prop="fileName">
                              </el-table-column>
                              <el-table-column label="操作" align="center" prop="action" width="150">
                                <template #default="scope">
                                  <el-button size="small" @click="
                                    download(
                                      scope.row.fullPath,
                                      scope.row.fileName
                                    )
                                    ">下载</el-button>
                                  <el-popconfirm @confirm="deleteUploadData(item, scope.row)" title="确认要删除吗?">
                                    <template #reference>
                                      <el-button size="small">删除</el-button>
                                    </template>
                                  </el-popconfirm>
                                </template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </span>
                      <span v-if="item.field.showType=='objectTable'">
                    <object-table :ref="item.field.code" :categoryIdP="props.sourceInfo.relationCategoryId" :proCode="item.field.code" :proOcode="item.field.ocode" :type="props.sourceInfo.editType"
                                  @validateObjectFiled="validateObjectFiled(item.field.code)"   :tableData.sync="item.field.value"    :tableColumn="item.field.tableList"></object-table>
                        </span>
                      <div style="width: 100%" v-if="item.field.showType == 'objectTable'">
                        <im-table max-height="300" :data="item.field.value" ref="propTable"
                                  :columns="item.field['column']" border id-key="sortId" stripe>
                          <template #[item.prop]="{ row, $index }" v-for="(item01, index) in item.field['column']">

                            <!-- <div>{{item01.label}}</div> -->
                            <!-- v-if="item01.label != '操作'" -->
                            <el-form-item v-if="item01.label != '操作'" class="objectImTable">

                              <div style="position: relative">
                                  <span v-if="
                                    item01.property == 'must' " style="color: red; position: absolute; top: 3px; left: -10px">*</span>
                                <span style="width: 100%;" v-if="item01.showType == 'number' && item01.isAuto != 1">
                                    <span v-if="item01.dataLength != null">
                                      <el-input-number style="width: 100%;" v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        " :max="item01.dataLength"></el-input-number>
                                    </span>
                                    <span v-if="item01.dataLength == null">
                                      <el-input-number style="width: 100%;" v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        "></el-input-number>
                                    </span>
                                  </span>
                                <span v-if="item01.isAuto == 1">
                                    <span v-if="item01.dataLength != null">
                                      <el-input type="text" v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        " :max="item01.dataLength"></el-input>
                                    </span>
                                    <span v-if="item01.dataLength == null">
                                      <el-input type="text" v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        "></el-input>
                                    </span>
                                  </span>
                                <span v-if="item01.showType == 'inputSelect'">
                                    <!-- v-model="row[item01.code] == ''? row[item01.code].split(','): row[item01.code]" -->
                                    <el-input-tag v-model="row[item01.code]" @rulesInput="rulesInput(item, index)"
                                                  v-if=" item01.isEdit != 1"></el-input-tag>
                                  <!-- v-model="row[item01.code] != undefined &&
                                row[item01.code] != null &&
                                row[item01.code] != ''
                                ? row[item01.code].join(',')
                                : row[item01.code]
                                " -->
                                    <el-input v-model="row[item01.code]" disabled v-else></el-input>
                                  </span>
                                <span v-if="item01.showType == 'moreInput'">
                                    <!-- <input-multiple v-model="row[item01.code]" :name="item01.name" :category-id="categoryIdP"
                                  :is-rule="item01.isRule" :ref="item01.code" @rulesInput="rulesInput(item, index)" v-if="
                                     item01.field.isEdit != 1
                                  "></input-multiple> -->
                                  <!-- <el-input disabled v-else v-model="row[item01.code] != undefined &&
                                  row[item01.code] != null &&
                                  row[item01.code] != ''
                                  ? row[item01.code].join(',')
                                  : row[item01.code]
                                "></el-input> -->
                                  </span>
                                <span style="width: 100%;" v-if="item01.showType == 'windowboxFilter'">
                                    <div style="display: table-cell; width: 44px; vertical-align: top">
                                      <el-button icon="el-icon-search" @click="openFilterSelect(item)"
                                                 v-if="item01.isEdit != 1 ? true : false"></el-button>
                                    </div>
                                    <div style="display: table-cell;width: 100%">
                                      <el-select style="width: 100%" v-model="row[item01.code]" multiple filterable
                                                 remote reserve-keyword placeholder="请输入关键词"
                                                 @focus="val => clearTableList(val, item)"
                                                 :disabled="item01.isEdit != 1 ? false : true"
                                                 :remote-method="vul => remoteMethod(vul, item)">
                                        <el-option v-for="item1 in item01.tableList" :key="item1.value"
                                                   :label="item1.label" :value="item1.value">
                                        </el-option>
                                      </el-select>
                                    </div>
                                    <div style="display: table-cell; width: 44px; vertical-align: top">
                                      <el-button icon="el-icon-close" v-if="
                                        item01.isEdit != 1 && item01.isShowClear == '1' ? true : false
                                      " @click="clearSelect(item)"></el-button>
                                    </div>
                                  </span>
                                <span v-if="item01.showType == 'windowboxTreeFilter'">
                                    <div v-if="item01.isEdit != 1 ? true : false"
                                         style="display: table-cell; width: 44px; vertical-align: top">
                                      <el-button icon="el-icon-search" @click="openWindowTree(item)"></el-button>
                                    </div>
                                    <div style="display: table-cell">
                                      <el-select v-model="row[item01.code]" multiple filterable remote reserve-keyword
                                                 placeholder="请输入关键词" @focus="val => clearTreeTableList(val, item)"
                                                 :disabled="item01.isEdit != 1 ? false : true"
                                                 :remote-method="vul => remoteTreeMethod(vul, item)">
                                        <el-option v-for="item1 in item01.tableList" :key="item1.value"
                                                   :label="item1.label" :value="item1.value">
                                        </el-option>
                                      </el-select>
                                    </div>
                                    <div v-if="
                                      item01.isEdit != 1 && item01.isShowClear == '1' ? true : false
                                    " style="display: table-cell; width: 44px; vertical-align: top">
                                      <el-button icon="el-icon-close" @click="clearWindowTree(item)"></el-button>
                                    </div>
                                  </span>
                                <span v-if="item01.showType == 'input' && item01.isAuto != 1">
                                    <!--数据类型 input、number、text、select、checkbox、radio-->
                                    <span v-if="item01.dataType == 'string'">
                                      <span v-if="item01.dataLength != null">
                                        <el-input v-model="row[item01.code]" :maxlength="item01.dataLength"
                                                  :placeholder=" item01.isEdit != 1
                                            ? item01.name
                                            : ''
                                            " :disabled=" item01.isEdit != 1
                                              ? false
                                              : true
                                              "></el-input>
                                      </span>
                                      <span v-if="item01.dataLength == null">
                                        <el-input v-model="row[item01.code]" :placeholder=" item01.isEdit != 1
                                          ? item01.name
                                          : ''
                                          " :disabled=" item01.isEdit != 1
                                            ? false
                                            : true
                                            "></el-input>
                                      </span>
                                    </span>
                                    <span v-if="item01.dataType == 'long'">
                                      <span v-if="item01.dataLength != null">
                                        <el-input-number v-model="row[item01.code]" :max="item01.dataLength" :disabled=" item01.isEdit != 1
                                          ? false
                                          : true
                                          "></el-input-number>
                                      </span>
                                      <span v-if="item01.dataLength == null">
                                        <el-input-number v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                          ? false
                                          : true
                                          "></el-input-number>
                                      </span>
                                    </span>
                                  </span>

                                <span v-if="item01.showType == 'textarea'">
                                    <!--数据类型 input、number、text、select、checkbox、radio-->
                                    <span v-if="item01.dataLength != null">
                                      <el-input v-model="row[item01.code]" type="textarea"
                                                :maxlength="item01.dataLength" :placeholder=" item01.isEdit != 1
                                          ? item01.name
                                          : ''
                                          " :disabled=" item01.isEdit != 1
                                            ? false
                                            : true
                                            "></el-input>
                                    </span>
                                    <span v-if="item01.dataLength == null">
                                      <el-input v-model="row[item01.code]" type="textarea" :placeholder=" item01.isEdit != 1
                                        ? item01.name
                                        : ''
                                        " :disabled=" item01.isEdit != 1
                                          ? false
                                          : true
                                          "></el-input>
                                    </span>
                                  </span>
                                <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                                <span class="time" v-if="item01.showType == 'dateTime'">
                                    <el-date-picker v-model="row[item01.code]" type="datetime" :disabled=" item01.isEdit != 1
                                      ? false
                                      : true
                                      "></el-date-picker>
                                  </span>
                                <!--日期输入框 yyyy-MM-dd-->
                                <span class="time" v-if="item01.showType == 'date'">
                                    <el-date-picker v-model="row[item01.code]" type="date" :disabled=" item01.isEdit != 1
                                      ? false
                                      : true
                                      "></el-date-picker>
                                  </span>

                                <span v-if="item01.showType == 'checkbox'">
                                    <el-checkbox-group v-model="row[item01.code]">
                                      <el-checkbox :label="cn.value" v-for="cn in item01.enumArray" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        ">{{ cn.label }}
                                      </el-checkbox>
                                    </el-checkbox-group>
                                  </span>

                                <span v-if="item01.showType == 'mul_combobox'">
                                    <el-select filterable :multiple="true" :placeholder=" item01.isEdit != 1
                                      ? '请选择'
                                      : '  '
                                      " v-model="row[item01.code]" :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        ">
                                      <el-option v-for="en in item01.enumArray" :value="en.value"
                                                 :label="en.label"></el-option>
                                    </el-select>
                                  </span>
                                <span v-if="item01.showType == 'comboTree'">
                                    <!-- <im-select-tree :placeholder=" item01.isEdit != 1
                                  ? '请选择'
                                  : '  '
                                  " clearable filter-on-input :model="item01.enumArray" children-key="children"
                                  :disabled=" item01.isEdit != 1
                                    ? false
                                    : true
                                    " fixed-position :maxContentHeight="265" v-model="row[item01.code]" id-key="id"
                                  style="width: 100%" title-key="title">
                                </im-select-tree> -->
                                    <el-tree-select :props="defaultProps"
                                                    :disabled=" item01.isEdit == 0 ? false : true"
                                                    children-key="children" node-key="id" node-value="id" id-key="id"
                                                    title-key="title" clearable :placeholder="
                                        item01.isEdit == 0
                                        ? '请选择'
                                        : '  '
                                        " v-model="row[item01.code]" :data="item01.enumArray" check-strictly
                                                    :render-after-expand="false" style="width: 90%" />
                                  </span>
                                <!--单选-->
                                <span v-if="item01.showType == 'radio'">
                                    <el-radio-group v-model="row[item01.code]">
                                      <el-radio :value="cn.value" v-for="cn in item01.enumArray"
                                      >
                                        {{ cn.label }}
                                      </el-radio>
                                    </el-radio-group>
                                  </span>
                                <span v-if="item01.showType == 'combobox'">
                                    <el-select filterable :placeholder=" item01.isEdit != 1
                                      ? '请选择'
                                      : '  '
                                      " v-model="row[item01.code]" clearable :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        ">
                                      <el-option v-for="en in item01.enumArray" :value="en.value"
                                                 :label="en.label"></el-option>
                                    </el-select>
                                  </span>
                                <span v-if="item01.showType == 'cascader'">
                                    <el-cascader :options="item01.enumArray" :placeholder=" item01.isEdit != 1
                                      ? '请选择'
                                      : '  '
                                      " v-model="row[item01.code]" clearable :disabled=" item01.isEdit != 1
                                        ? false
                                        : true
                                        "></el-cascader>
                                  </span>
                                <span v-if="
                                    item01.titleDesc != null &&
                                    item01.titleDesc != undefined &&
                                    item01.titleDesc != '' &&
                                    item01.titleDesc != 'null'
                                  " style="position: absolute; top: 0px; left: -18px" :title="item01.titleDesc">
                                    <i type="ios-help-circle-outline" size="15" />
                                  </span>
                              </div>
                            </el-form-item>
                            <div v-else style="display: flex;
                                      justify-content: center;">
                              <el-button type="primary" v-if="type == 'add' || type == 'edit'"
                                         @click="addRow(item.field['column'], item.field.value)">加</el-button>
                              <el-button type="primary" v-if="type == 'add' || type == 'edit'"
                                         @click="deleteRow(row, item.field.value)">减</el-button>
                            </div>
                          </template>
                          <!-- <template class="operator" #operator="{ row, $index }">
                          </template> -->
                        </im-table>
                      </div>
                    </el-form-item>
                  </div>

                </el-col>
              </el-row>
            </template>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref, toRefs} from "vue";
import ObjectTable from "@/views/modules/eam/zcgl/instance/source/component/objectTable.vue";
import {
  checkPropertyByCodeAxios,
  getInstanceByIdApi, queryCasecadeListAxios,
  queryPropertyEnumByInstanceAxios,
  queryRelationCategoryEnum, saveIns
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {ElMessage, ElMessageBox, type FormInstance, type FormRules} from "element-plus";
import {queryEnumList} from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
import {sm4} from "gm-crypt";

const sm4Key = '1B6E03BAE001B71D1AC6377806E2FF63';
const emit = defineEmits(["jump-to","source-select"]);
const jumpTo = (sign: string) => {
  props.sourceInfo.tabName=['资产信息', '资产关系拓扑', '资产漏洞概览', '安全风险告警'];
  let sourceInfo = {};
  sourceInfo.tabName = ['资产信息', '资产关系拓扑', '资产漏洞概览', '安全风险告警'];
  sourceInfo.categoryId = props.sourceInfo.categoryId;
  sourceInfo.instanceId = props.sourceInfo.srcInstanceId;
  sourceInfo.selectCate = state.relationCategoryId;
  sourceInfo.infoTitle = props.sourceInfo.infoTitle;
  sourceInfo.editType = props.sourceInfo.parentEditType;
  emit("source-select",sourceInfo);
  emit("jump-to", sign);
};

const props = defineProps({
  sourceInfo:{
    type: Object
  }
});
const state = reactive({
  relationCategoryId:"",
  categoryList:[],
  dataForm:{
    categoryId:""
  },
  propertyForm:{},
  propertyLoading:false,
  windowboxloading:false,
  //current.windowSelect = item.filed;
  currentItem:{},
  currentId:"",
  ocode:"",
  show_type:"",
  data:[],
  windowSelect:{},
  totalW:0,
  selectDataModal:false,
  object:{},
  propValue:"",
  currentPageW:1,
  pageSizeW:10,
  saveButtonLoading:false,
  saveLoading:false
})
const {categoryList,dataForm,propertyLoading,propertyForm,
  windowboxloading,currentItem,currentId,ocode,show_type,data,windowSelect,totalW,selectDataModal,
  object,propValue,currentPageW,pageSizeW,saveButtonLoading,saveLoading
} = toRefs(state);
const selectCateChange = (value)=>{
  let params = {};
  props.sourceInfo.relationCategoryId = value;
  params.categoryId = props.sourceInfo.relationCategoryId;
  params.id = props.sourceInfo.instanceId;
  params.isAuth = true;
  getInstanceByIdMethod(params);
}
const buildTree = (
  data: any[],
  deptId: string | number,
  parentId: string | number
) => {
  // 创建一个映射，用于快速查找节点
  const map = new Map();
  const rootNodes = [];

  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, children: [] });
  });

  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      }
    }
  });
  return rootNodes;
}

const queryPropertyEnumByInstanceMethod = (params) => {
  const tmpParams = {
    categoryId: props.sourceInfo.categoryId,
    isAuth: true,
    property: params
  };
  try {
    queryPropertyEnumByInstanceAxios(tmpParams).then(res => {
      // console.log(res?.data?.enumArray);
      if (params.showType == "comboTree") {
        console.log(buildTree(res?.data?.enumArray, "id", "parentId"))
        params.enumArray.push(...buildTree(res?.data?.enumArray, "id", "parentId"));
      } else {
        params.enumArray = res?.data?.enumArray;
      }
    })
  } catch (error) {
    console.log(error);
  }
}
const getInstanceByIdMethod = query => {
  try {
    state.propertyLoading = true;
    getInstanceByIdApi(query).then(res => {
      state.propertyForm = res?.["data"]
      if (state.propertyForm&&state.propertyForm.rowsObj&&state.propertyForm.rowsObj.length > 0) {
        state.propertyForm.rowsObj.forEach((item: Array<any>) => {
          item?.['rows'].forEach((item: Array<any>) => {
            item?.['colList'].forEach(item => {
              if (item?.field?.showType == "combobox" || item?.field?.showType == "comboTree") {
                queryPropertyEnumByInstanceMethod(item.field);
              }
              if (item?.field?.showType == "objectTable") {
                // item.field.column = [];
                Reflect.set(item.field, 'column', []);
                Reflect.set(item.field, 'operator', {
                  label: "操作",
                  fixed: "right",
                  width: "166"
                });
                item.field?.tableList.forEach((item02, index) => {
                  let isTree = false;
                  if (item02.showType == 'comboTree') {
                    isTree = true;
                  }

                  item.field.column.push({
                    // ...item02,
                    prop: item02.code,
                    label: item02.name,
                    // item02.showType
                    showType: item02.showType,
                    isAuto: item02.isAuto,
                    dataType: item02.dataType,
                    code: item02.code,
                    property: item02.property,
                    dataLength: item02.dataLength,
                    isReEdit: item02.isReEdit,
                    tableList: isTree ? [...buildTree(item02.enumArray, "id", "parentId")] : item02.enumArray,
                    name: item02.name,
                    enumArray: item02.enumArray,
                    titleDesc: item02.titleDesc,
                    width:item02.width,
                    align:item02.align,
                    ruleData:item02.rule
                  })
                  if (index + 1 == item.field.tableList.length) {
                    item.field.column.push({
                      prop:"operator",
                      label: "操作"
                    })
                  }

                })
              }
            })
          })
        })
      }
      state.propertyLoading = false;
    });
  } catch (error) {
    console.log(error);
    state.propertyLoading = false;
  }
};
onMounted(()=>{
  console.log(props.sourceInfo);
  queryRelationCategoryEnum(props.sourceInfo.categoryId).then(res => {
    state.categoryList = res.data.list;
  });
  state.relationCategoryId = props.sourceInfo.rcategoryId;
  if(state.relationCategoryId == "-2"&&props.sourceInfo.editType=='add'){

  }else{
    let params = {};
    params.categoryId = props.sourceInfo.relationCategoryId;
    params.id = props.sourceInfo.instanceId;
    params.isAuth = true;
    getInstanceByIdMethod(params);
  }
})
const download = (fullPath, fileName) => {
  window.location.href =
    "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
    fullPath +
    "&fileName=" +
    fileName;
};
const openUpload = (item, id) => {
  item.field.hide = !item.field.hide;
  // this.buttonIcon=item.field.hide?"el-icon-remove-outline": "el-icon-circle-plus-outline";
  // this.$set(item.field, "hide", item.field.hide);
};
const handleSuccess = (res, file, item) => {
  const result = res.data.result;
  const code = res.data.code;
  if (code == "1") {
    res.attributeId = item.field.code;
    const tableList = item.field.tableList;
    for (let x = 0; x < tableList.length; x++) {
      const rest = tableList[x];
      if (
        rest.fileSize == res.data.fileSize &&
        rest.fileName == res.data.fileName
      ) {
        ElMessage.error("重复文件，不允许上传");
        return false;
      }
    }
    item.field.tableList.push(res.data);
    if (!item.field.hide) {
      openUpload(item, item.field.code);
    }
  }
  ElMessage.success(result);
};
const deleteUploadData = (item, row) => {
  item.field.tableList.splice(row._index, 1);
  ElMessage.success("删除成功！");
};
const allRules = reactive<FormRules>({});
const objTableRefs = ref(new Map());
const getObjRef = (el:any,code:String) =>{
  objTableRefs.value.set(code,el);
}
const constMust = async (rule, value, callback, item) => {
  console.info(item);
  let checkParams = {};
  let requreid = item.rule.request;
  if(item.field.showType=='table'){
    if(requreid){
      if (  (item.field.tableList   instanceof Array) &&  item.field.tableList.length!=0) {
        callback();
      } else {
        callback(new Error(rule.name + "不能为空"));
      }
    }else{
      callback();
    }
  }else if(item.field.showType=='objectTable'){
    if(requreid){
      objTableRefs.value.get(item.field.code).objRules(requreid).then(() => {
        //callback();
        objTableRefs.value.get(item.field.code).objRules(false).then(() => {
          callback();
        }).catch(() => {
          callback(new Error(" "));
        })
      }).catch(() => {
        callback(new Error(rule.name+"不能为空"));
      })
    }else{
      objTableRefs.value.get(item.field.code).objRules(requreid).then(() => {
        callback();
      }).catch(() => {
        callback(new Error(" "));
      })
    }
  }else if (requreid) {
    // if (item.field.showType == 'moreInput') {
    //   this.$refs[item.field.code][0].cyclicCheck().then(() => {
    //     callback();
    //   }).catch(() => {
    //     callback(new Error(" "));
    //   })
    //   // // console.info(this.$refs[item.field.code][0].cyclicCheck());
    //   //
    //   // let result = await this.$refs[item.field.code][0].cyclicCheck();
    //   // console.log(result);
    //
    // } else {
    checkParams.categoryId = props.sourceInfo.relationCategoryId;
    checkParams.name = rule.name;
    checkParams.value = item.field.value;
    checkPropertyByCodeAxios(checkParams)
      .then(res => {
        if (res.data == "success") {
          callback();
        } else if (res.data == "error1") {
          callback(new Error(rule.name + "不满足校验规则"));
        } else {
          callback(new Error(rule.name + "不能为空"));
        }
      })
      .catch(exp => {
        callback(new Error(rule.name + "不满足校验规则"));
      });
    // }

  } else {
    callback();
  }
}
const caseObject = ref([]);
const  selectActiveCase = (item) => {
  updateActiveCase(item);
}
const updateActiveCase = (item) => {
  if (caseObject.value.length > 0) {
    let obj = caseObject.value.filter(p => {
      //根据当前变动的主动发起关联的属性，遍历已缓存的所有主动关联的key和value
      return p.key == item.field.code;
    });
    if (obj != null && obj.length > 0 && undefined != item.field.value) {
      //如果此属性存在，更新值
      obj[0].value = item.field.value;
    } else {
      //如果不存在，放入主动发起的缓存中
      let json = {
        key: item.field.code,
        value: item.field.value,
      };
      caseObject.value.push(json);
    }
  } else {
    let json = {
      key: item.field.code,
      value: item.field.value,
    };
    caseObject.value.push(json);
  }
}
const  openBeSelect = (val, item) => {
  if (val) {
    let code = item.field.activeCode;
    let value = "";
    if (caseObject.value.length > 0) {
      for (let i = 0; i < caseObject.value.length; i++) {
        if (caseObject.value[i].key == item.field.activeProp) {
          value = caseObject.value[i].value;
          break;
        }
      }
      let params = {};
      params.code = code;
      params.value = value;
      queryCasecadeListAxios(params)
        .then(res => {
          item.field.enumArray = eval("(" + res.data + ")");
        })
        .catch(exp => {
          ElMessage.error(exp.message);
        });
    }
  }
}
const ruleFormRef = ref<FormInstance>();
const  rulesInput = (item, code) => {
  if (item.field.isRule == "1") {
    const response = ruleFormRef.value.validateField(code);
    if(response.data=='success'){
      return true;
    }else{
      return false;
    }
  }
}
const handleFormatError = file => {
  ElMessage.warning(
    `文件格式不正确, 文件${file.name}格式不正确，请上传pdf,txt,doc,docx,xls,xlsx,jpeg,jpg,png格式`
  );
};
const handleMaxSize = file => {
  ElMessage.warning(
    `超出文件大小范围, 文件 ${file.name} 太大了, 不允许超过 50M.`
  );
};
const openSelect = (item) => {
  if (item.field.activeProp != "") {
    //如果是被级联的的
    openBeCaseWindow(item);
  } else {
    getObjectPage(item);
  }
}
const openBeCaseWindow = (item) => {
}
const windowTable = ref(null);
const getObjectPage = (item) => {
  if (item.field.ocode) {
    state.windowboxloading = true;
    //current.windowSelect = item.filed;
    state.currentItem = item;
    state.currentItem.value = item.field.value;
    state.currentItem.label = item.field.showValue;
    state.currentId = item.field.value;
    state.ocode = item.field.ocode;
    state.show_type = item.field.showType;
    var params = {
      show_type: "windowbox",
      ocode: item.field.ocode,
      pageSize: state.object.size,
      pageNum: state.object.page,
      value: state.propValue,
    };
    queryEnumList(params).then(res => {
      state.selectDataModal = true;
      state.data = res.data.data;
      //current.object.total = res.data.total;

      state.totalW = res.data.total;
      state.windowboxloading = false;
      state.windowSelect = {};
      state.windowSelect.value = item.field.value;
      state.windowSelect.label = item.field.showValue;
      state.windowSelect.id = item.field.value;

      nextTick(() => {
        if (
          state.windowSelect.value != undefined &&
          state.windowSelect.value != ""
        ) {
          for (var i = 0; i < state.data.length; i++) {
            if (state.data[i].value == state.windowSelect.value) {
              windowTable.value.toggleRowSelection(
                state.data[i],
                true,
              );
            }
          }
        }
      });
    });
  }
}
const validateObjectFiled = (code) =>{
  ruleFormRef.value.validateField(code);
}
const closePropSelect = () => {
  state.selectDataModal = false;
  state.propValue = "";
  state.data = [];
  state.windowSelect = null;
  //this.currentId = '';
  state.currentPageW = 1;
  state.totalW = 0;
  state.pageSizeW = 10;
}
const saveInstance = (formEl: FormInstance | undefined) =>{
  state.saveButtonLoading = true;
  if (!formEl){
    ElMessage.error("表单对象为空");
    return;
  }
  try{
    formEl.validate((valid) => {
      if (valid) {
        for(let  item of  state.propertyForm.rowsObj || [] ) {
          for (let items of  item.rows || []) {
            for (let key of  items.colList || []) {
              if( key.field.showType=="password"&&key.field.value!=null&&key.field.value!=''){
                key.field.value=sm4.encrypt(key.field.value,sm4Key);
              }
            }
          }
        }
        saveInstanceForm();
      }else{
        ElMessage.error("表单检验不通过！");
        state.saveButtonLoading = false;
      }
    });
  }catch(exp){
    console.log(exp)
  }

}
const saveInstanceForm = () =>{
  state.saveLoading = true;
  let params = JSON.parse(JSON.stringify(state.propertyForm));
  for(let  item of  params.rowsObj || [] ) {
    for (let items of  item.rows || []) {
      for (let key of  items.colList || []) {
        key.field.enumArray=[];
        if(key.field.code=='ipAddress'){
        }
      }
    }
  }

  params.srcInstanceId = props.sourceInfo.srcInstanceId;
  saveIns(params).then(res1 => {
    if(res1.data.message=='success'){
      for(let  item of  state.propertyForm.rowsObj || [] ){
        for(let  items of  item.rows || []){
          for(let  key of  items.colList || [] ){
            for(let  keys of  res1.data.propertyDtoList || [] ){
              key.field.instanceId = res1.data.id;
              if( key.field.code==keys.code){
                key.field.value=keys.value;
                if( keys.isReEdit==0){
                  key.field.isEdit=1;
                }
              }
            }
          }
        }
      }
      props.sourceInfo.instanceId = res1.data.id;
      state.saveButtonLoading = false;
      state.saveLoading = false;
      ElMessage.success("保存成功");


      for(let  item of  state.propertyForm.rowsObj || [] ) {
        for (let items of  item.rows || []) {
          for (let key of  items.colList || []) {
            if( key.field.showType=="password"&&key.field.value!=null&&key.field.value!=''){
              key.field.value=sm4.decrypt(key.field.value,sm4Key);
            }
          }
        }
      }
    } else {
      ElMessageBox.alert({
        dangerouslyUseHTMLString: true,
        message: '保存失败<br/>'+res1.data.data
      });
      state.saveLoading = false;
      state.saveButtonLoading = false;
      jumpTo("assetInformationPage");
    }
  }).catch(exp=>{
    ElMessage.error("保存失败:"+exp.message);
  });
}
const defaultProps = {
  children: "children",
  label: "title"
};
</script>
<style lang="scss" scoped>
.addPropertyEdit {
  :deep(.readonlyInput) {
    .el-input__inner {
      cursor: not-allowed !important;
    }
  }
  :deep(.objectImTable) {
    .el-form-item__content {
      margin-left: 0 !important;
      width: 100%;

      >div {
        width: 100%;
      }
    }
  }
  :deep(.el-divider__text) {
    font-size: 16px;
    font-weight: 500;

    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 18px;
      background: inherit;
      background-color: #409eff;
      border: none;
      border-radius: 8px;
      vertical-align: -3px;
      margin-right: 6px;
    }
  }
}
</style>
