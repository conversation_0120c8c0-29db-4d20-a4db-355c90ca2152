<template>
  <div style="padding:0;">
    <configure :categoryId="props.categoryId" :categoryName="props.categoryName"
      @closeSetShowGroup="closeSetShowGroup">
    </configure>
  </div>
</template>
<script lang="ts" setup>
import configure from "@/views/modules/eam/zcgl/instance/source/component/attrsortgroup/configure.vue";
import { reactive, ref, watch, defineEmits } from "vue";

const emit = defineEmits(['closeSetShowGroup'])
const activeName = ref("group1");
const props = defineProps({
  categoryId: {
    type: String || Number
  },
  categoryName: {
    type: String
  }
})
const closeSetShowGroup = () => {
  emit('closeSetShowGroup');
}
</script>
