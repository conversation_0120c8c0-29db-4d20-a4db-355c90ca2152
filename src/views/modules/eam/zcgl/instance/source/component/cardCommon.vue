<template>
  <div style="height: 100%;overflow-y: scroll;" ref="scrollContainer">
    <el-card v-for="(item, index) in showArrayInfo" style="width: auto; min-height: 133px; margin-bottom: 1rem;"
      shadow="hover">
      <div class="card-title" style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 0.6rem;
        ">
        <div style="display: flex; align-items: center">
          <h3 style="padding-right: 1.6rem;font-size: 18px;">{{ item['name'] }}</h3>
          <el-tag v-if="item.onlineStatus__label" style="margin-right: 3rem" type="success">{{ item['onlineStatus__label'] }}</el-tag>
        </div>
        <div style="line-height: 0.2rem;">
          <!-- <el-text style="font-size: 13px;cursor: pointer;" class="mx-1" type="primary">取消关联</el-text> -->
          <el-text style="font-size: 13px;cursor: pointer;" class="mx-1" type="primary">详情</el-text>
        </div>
      </div>
      <div style="margin-bottom: 0.9rem" class="card-content">
        <!-- <template v-for="(item, index) in [
          '*************',
          '00-15-5D-1B-C4-B0',
          '张三',
          '总院备机房机柜C5位置4'
        ]">
          <el-text style="font-size: 14px" class="mx-1" type="info">{{ item }}</el-text>
          <el-divider v-if="index != 3" direction="vertical" />
        </template> -->
        <el-text style="font-size: 14px" class="mx-1" type="info" title="IP地址">{{ item['ipAddress'] }}</el-text>
        <el-divider direction="vertical" />
        <el-text style="font-size: 14px" class="mx-1" type="info" title="MAC地址">{{ item['macAddress'] }}</el-text>
        <el-divider direction="vertical" />
        <el-text style="font-size: 14px" class="mx-1" type="info" title="资产责任人">{{ item['assetDuty'] }}</el-text>
        <el-divider direction="vertical" />
        <el-text style="font-size: 14px" class="mx-1" type="info" title="物理位置">{{ item['phyPosition'] }}</el-text>
      </div>
      <div class="card-tag">
        <!-- <template v-for="(item, index) in ['虚拟服务器', '风险资产', '僵尸资产']">
          <el-tag style="margin-right: 0.6rem" v-if="index == 0" type="success">{{ item }}</el-tag>
          <el-tag style="margin-right: 0.6rem" v-else-if="index == 1" type="danger">{{ item }}</el-tag>
          <el-tag style="margin-right: 0.6rem" v-else-if="index == 2" type="warning">{{ item }}</el-tag>
        </template> -->
        <el-tag style="margin-right: 0.6rem" type="success">{{ item['categoryName'] }}</el-tag>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
import { queryDetailRelationsMethod } from "@/views/modules/eam/zcgl/instance/source/api/assetAssociationInfoInterface";
const props = defineProps({
  category_id: String,
  instance_id: String,
})


const arrayInfo = reactive([]);
const allArrayInfo = reactive([]);
const showArrayInfo = reactive([]);
const currentDataIndex = ref(0);
onMounted(() => {
  queryDetailRelationsMethod({
    category_id: props.category_id,
    instance_id: props.instance_id,
  }).then(res => {
    let i = 0;
    res['data'].forEach(item => {
      allArrayInfo.push(...item['children']);
      item['children'].forEach((item, index) => {
        if (((index % 10) == 0) && index != 0) {
          arrayInfo[i].push(item);
          i++;
        } else {
          if (!Array.isArray(arrayInfo[i]) || arrayInfo[i].length == 0) {
            arrayInfo[i] = [];
          }
          arrayInfo[i].push(item);
        }
      })
    })

    showArrayInfo.push(...arrayInfo[currentDataIndex.value]);
  }).catch(err => {
  }).finally(() => {
  })

  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
})

const scrollContainer = ref(null);
const handleScroll = () => {
  if (scrollContainer.value) {
    const currentChildrenHeight = scrollContainer.value.children.length * (scrollContainer.value.children[0].offsetHeight + 16)
    if ((currentChildrenHeight - scrollContainer.value.scrollTop - 100) < scrollContainer.value.offsetHeight) {
      currentDataIndex.value = currentDataIndex.value + 1;
      showArrayInfo.push(...arrayInfo[currentDataIndex.value]);
    }
  }
};

onUnmounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll);
  }
});

</script>

<style lang="scss" scoped></style>
