<template>
  <div>
    <div class="flex-c">
      <el-input v-model="queryCode" placeholder="请输入查询条件" class="w-1/2">
      </el-input>
      <el-button type="primary" class="ml-3" @click="initRecordData">查询</el-button>
      <el-button type="primary" @click="resetRecordData">重置</el-button>
    </div>
    <avue-crud :data="tableData" :option="tableOption" v-model:page="state.tablePage"
               @size-change="sizeChange"
               @current-change="currentChange">
      <template #menu-left="{ size }">
        <el-button v-if="props.editType=='add'||props.editType=='edit'"
          :icon="useRenderIcon('EP-CirclePlus')" @click="addRelationData"
        >新增</el-button>
        <el-button v-if="tableOption.column.length>0&&(props.editType=='add'||props.editType=='edit')"
                   :icon="useRenderIcon('EP-UploadFilled')"
                   @click="uploadRealtion"
        >导入</el-button>
        <el-button v-if="tableOption.column.length>0"
                   :icon="useRenderIcon('EP-Download')"
                   @click="exportRelationData"
        >导出</el-button>
        <el-button
                   :icon="useRenderIcon('EP-Setting')"
                   @click="primaryUpdate"
        >主键维护</el-button>
      </template>
      <template #oper__button="{ row }">
        <el-button type="text" style="padding:0" @click="updateRelationData(row)">编辑</el-button>
<!--        <el-button type="text" style="padding:0" v-if="row.relation__inst__id" @click="removeRelation(row)">移除关系</el-button>-->
        <el-button type="text" style="padding:0" @click="deleteInstance(row.instance_id)">删除</el-button>
      </template>
    </avue-crud>
    <el-drawer size="50%" destroy-on-close v-model="importShow" @close="closeAssetImport">
      <template #header="{ close, titleId, titleClass }">
        <h4 :id="titleId" :class="titleClass">导入</h4>
      </template>
      <relation-asset-import :categoryId="props.categoryId" :relationCategoryId="props.relationCategoryId" :visible.sync="importShow"
                             :src-instance-id="props.instanceId"
                   @closeImport="closeAssetImport" />
    </el-drawer>
    <el-dialog title="主键维护" v-if="primaryEditShow" v-model="primaryEditShow" append-to-body width="500">
      <el-form :model="primaryForm" ref="primaryFormRef">
        <el-form-item label="是否启用主键" prop="haveBool">
          <el-switch
            v-model="primaryForm.haveBool"
            class="ml-2"
            inline-prompt
            style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949"
            active-text="启用"
            inactive-text="禁用"
            :active-value="'0'"
            inactive-value="'1'"
          />
        </el-form-item>
        <el-form-item label="主键" prop="primaryKey" v-if="primaryForm.haveBool=='0'" :rules="[
        { required: true,trigger:'change', message: '请选择主键' }
      ]">
          <el-select v-model="primaryForm.primaryKey" filterable multiple clearable>
            <el-option v-for="(item,index) in cateProList" :label="item.label" :value="item.value" :key="index"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="state.primaryEditShow = false">关闭</el-button>
          <el-button type="primary" @click="savePrimary">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup>

import {useRenderIcon} from "@/components/ReIcon/src/hooks";
import { reactive, toRefs,onMounted,ref} from "vue";
import {defaultPageSize, pageSizeOptions} from "@/utils/page_util";
import {
  deleteInstanceByIds,
  queryRelationTable,exportRelationDataAxios,removeRealtionAxios
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {ElMessage, ElMessageBox} from "element-plus";
import RelationAssetImport from "@/views/modules/eam/zcgl/instance/source/component/relationAssetImport.vue";
import {
  queryCategoryPrimary, queryPropertyByCategory,
  saveCategoryPrimary
} from "@/views/modules/eam/zcgl/instance/source/api/categoryModelInterface";

const addRelationData = () =>{
  let sourceInfo = {};
  sourceInfo.categoryId = props.categoryId;
  sourceInfo.instanceId = 0;
  sourceInfo.srcInstanceId = props.instanceId;
  sourceInfo.relationCategoryId = props.relationCategoryId;
  sourceInfo.rcategoryId = props.relationCategoryId;
  sourceInfo.infoTitle = props.infoTitle;
  sourceInfo.editType='add';
  sourceInfo.parentEditType=props.editType
  sourceSelect(sourceInfo);
  jumpTo("editProperty");
}
const emit = defineEmits(["jump-to","source-select","query-left-tree"]);
const closeAssetImport = () => {
  state.importShow = false;
  emit("query-left-tree");
  initRecordData();
}
const updateRelationData = (row) =>{
  let sourceInfo = {};
  sourceInfo.categoryId = props.categoryId;
  sourceInfo.instanceId = row.instance_id;
  sourceInfo.srcInstanceId = props.instanceId;
  sourceInfo.relationCategoryId = row.category_id;
  sourceInfo.infoTitle = props.infoTitle;
  sourceInfo.rcategoryId = props.relationCategoryId;
  sourceInfo.editType='edit';
  sourceInfo.parentEditType=props.editType
  sourceSelect(sourceInfo);
  jumpTo("editProperty");
}
const exportRelationData = () =>{
  let params = {};
  params.categoryId = props.categoryId;
  params.instanceId = props.instanceId;
  params.relationCategoryId = props.relationCategoryId;
  params.queryCode = state.queryCode;
  exportRelationDataAxios(params).then(() => {

  })
}
const jumpTo = (page) => {
  emit("jump-to", page);
}
const sourceSelect = (data) => {
  emit("source-select", data);
}
const props = defineProps({
  instanceId:String,
  categoryId:String,
  relationCategoryId:String,
  editType:String,
  infoTitle:String
});
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  maxHeight:475,
  menu:false,
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  column: []
});
const state = reactive({
  tableData:[],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions,
    queryCode: ''
  },
  importShow:false,
  queryCode:'',
  primaryEditShow:false,
  primaryForm:{
    categoryId:'0',
    haveBool: '1',
    primaryKey:[]
  },
  cateProList:[],
});

const {
  tableData,tablePage,importShow,queryCode,primaryEditShow,primaryForm,cateProList
} = toRefs(state);
const initRecordData = async() => {
  let params = {};
  params.categoryId = props.categoryId;
  params.instanceId = props.instanceId;
  params.relationCategoryId = props.relationCategoryId;
  params.pageNum = state.tablePage.currentPage;
  params.pageSize = state.tablePage.pageSize;
  params.queryCode = state.queryCode;
  queryRelationTable(params).then(res=>{
    let columns = [];
    for(let i = 0;i<res.data.columns.length;i++){
      let tt = {};
      tt.prop = res.data.columns[i].key;
      tt.label = res.data.columns[i].title;
      tt.align = res.data.columns[i].align;
      columns.push(tt);
    }
    if(props.editType=='add'||props.editType=='edit'){
      let c = {};
      c.prop = "oper__button";
      c.label = "操作";
      c.width = "130px";
      columns.push(c);
    }
      tableOption.column = columns;
      state.tableData = res.data.rows;
      state.tablePage.total = res.data.total;
  })
}
const resetRecordData = () =>{
  state.queryCode = "";
  initRecordData();
}
const uploadRealtion = () =>{
  state.importShow = true;
}
const removeRelation = (row) =>{
  ElMessageBox.confirm('确认要移除关系吗?', '是否确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消'
  })
    .then(() => {
      removeRealtionAxios(row.instance_id).then(res=>{
        if(res.data=='success'){
          ElMessage.success("移除成功");
          emit("query-left-tree");
          initRecordData();
        }else{
          ElMessage.error(res.data);
        }
      })
    })
    .catch(() => {

    })

}
const deleteInstance = (instanceId) => {
  ElMessageBox.confirm('确认要删除吗?', '是否确认', {
    confirmButtonText: '确认',
    cancelButtonText: '取消'
  })
    .then(() => {
      let deleteIds = [];
      deleteIds.push(parseInt(instanceId));
      deleteInstanceByIds(deleteIds).then(res => {
        if (res?.["data"] == "success") {
          ElMessage.success("删除成功");
          emit('query-left-tree');
          initRecordData();
        } else {
          ElMessage.warning("删除失败");
        }
      });

    })
    .catch(() => {

    })
}
const sizeChange = (pageSize) => {
  state.tablePage.pageSize = pageSize;
  state.tablePage.currentPage = 1;
  initRecordData();
}
const currentChange = (page) => {
  state.tablePage.currentPage = page;
  initRecordData();
}
const primaryFormRef = ref();
const savePrimary = () =>{
  primaryFormRef.value.validate(valid=>{
    if(valid){
      let params = {
        categoryId: state.primaryForm.categoryId,
        haveBool: state.primaryForm.haveBool,
        primaryKey: state.primaryForm.primaryKey.join(",")
      };
      saveCategoryPrimary(params).then(()=>{
        ElMessage.success("保存成功");
      })
    }else{
      ElMessage.error("表单校验不通过");
    }
  })
}
const primaryUpdate = () =>{
  state.primaryForm.categoryId = props.relationCategoryId;
  queryCategoryPrimary(props.relationCategoryId).then(res=>{
    if(res["data"]){
      state.primaryForm.haveBool = res["data"].haveBool;
      state.primaryForm.primaryKey = res["data"].primaryKey.split(",");
    }else{
      state.primaryForm.haveBool = '1';
      state.primaryForm.primaryKey = [];
    }
    let pp = {
      categoryId: props.relationCategoryId
    }
    queryPropertyByCategory(pp).then(res=>{
      if(res["data"]){
        state.cateProList = res["data"];
      }
    })
    state.primaryEditShow = true;
  })

}
onMounted(()=>{
  initRecordData();
})
</script>
