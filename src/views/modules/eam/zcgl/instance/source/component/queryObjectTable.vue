<template>
  <el-card>
    <el-form :model="queryDataList" v-if="queryDataList" label-width="120px">
      <el-col :span="12" v-for="(item,index) in queryList">
        <el-form-item :prop="item.code" :label="item.name">
            <span v-if="item.showType == 'number'">
                            <el-input-number v-model="queryDataList[item.code]"
                                             :max="item.dataLength"></el-input-number>
          </span>
          <span v-if="item.showType == 'inputSelect'">
          <el-input v-model="queryDataList[item.code]" :placeholder="item.name"  :precise.sync="item.hide==undefined||item.hide==null?false:item.hide">
                              </el-input>
                    </span>
          <span v-if="item.showType == 'moreInput'">
                   <el-input v-model="queryDataList[item.code]" :placeholder="item.name"  :precise.sync="item.hide==undefined||item.hide==null?false:item.hide">
                              </el-input>
                    </span>
          <span v-if="item.showType == 'input'">
                      <!--数据类型 input、number、text、select、checkbox、radio-->
                      <el-input v-model="queryDataList[item.code]" :placeholder="item.name"  :precise.sync="item.hide==undefined||item.hide==null?false:item.hide">
                              </el-input>
                    </span>
          <span v-if="item.showType == 'textarea'">
                      <el-input v-model="queryDataList[item.code]" :placeholder="item.name"  :precise.sync="item.hide==undefined||item.hide==null?false:item.hide">
                              </el-input>
                    </span>
          <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
          <span v-if="item.showType == 'dateTime'">
                      <el-date-picker v-model="queryDataList[item.code]"
                                      type="datetime"></el-date-picker>
                    </span>
          <!--日期输入框 yyyy-MM-dd-->
          <span v-if="item.showType == 'date'">
                      <el-date-picker v-model="queryDataList[item.code]"
                                      type="date"></el-date-picker>
                    </span>
          <span v-if="item.showType == 'checkbox'">
                      <el-checkbox-group v-model="queryDataList[item.code]">
                        <el-checkbox :label="cn.value"
                                     v-for="cn in item.enumArray">{{ cn.label
                          }} </el-checkbox>
                      </el-checkbox-group>
                    </span>
          <span v-if="item.showType == 'mul_combobox'">
                      <el-select placement="top"
                                 filterable
                                 multiple="true"
                                 :placeholder="'请选择'"
                                 v-model="queryDataList[item.code]">
                        <el-option v-for="en in item.enumArray"
                                   :value="en.value"
                                   :label="en.label"></el-option>
                      </el-select>
                    </span>
          <span v-if="item.showType == 'comboTree'">
                         <el-tree-select :placeholder="'请选择'"
                                         clearable
                                         filter-on-input
                                         :model="item.enumArray"
                                         children-key="children"
                                         fixed-position
                                         :maxContentHeight=265
                                         v-model="queryDataList[item.code]"
                                         id-key="id"
                                         style="width: 100%"
                                         title-key="title">
                      </el-tree-select>
                    </span>
          <span v-if="item.showType == 'radio'">
                      <el-radio-group v-model="queryDataList[item.code]">
                        <el-radio :value="cn.value"
                                  :label="cn.value"
                                  v-for="cn in item.enumArray">
                          {{ cn.label }}
                        </el-radio>
                      </el-radio-group>
                    </span>
          <span v-if="item.showType == 'combobox'">
                        <el-select placement="top"
                                   filterable
                                   :placeholder="'请选择'"
                                   v-model="queryDataList[item.code]"
                                   clearable>
                          <el-option v-for="en in item.enumArray"
                                     :value="en.value"
                                     :label="en.label"></el-option>
                        </el-select>
                      </span>
          <span v-if="item.showType == 'cascader'">
                        <el-cascader :options="item.enumArray"
                                     :placeholder="'请选择'"
                                     v-model="queryDataList[item.code]"
                                     clearable></el-cascader>
                      </span>
        </el-form-item>
      </el-col>
    </el-form>
  </el-card>

</template>
<script setup lang="ts">
import {reactive, toRefs,watch} from "vue";

  const state = reactive({
    queryDataList:{},
  })
  const {queryDataList} = toRefs(state);
  const reset = () => {
    state.queryDataList = {};
  }
  const props = defineProps({
    queryData:{
      type:String,
      default:""
    },
    queryList:{
      type:Array,
      default:[]
    },
    ocode:{
      type:String,
      default:""
    }
  });

  const emit = defineEmits(["update:queryData"]);
watch(state.queryDataList,(newVal,oldVal) => {
  var query="";
  var flag = false;
  for(var i=0;i<props.queryList.length;i++){
    var tt = props.queryList[i];
    var vv = state.queryDataList[tt.code]==undefined||state.queryDataList[tt.code]==null?"":state.queryDataList[tt.code];
    if(vv==""){
      if(i==props.queryList.length-1){
        query += '"'+tt.code+'":*'+vv+'*';
      }else{
        query += '"'+tt.code+'":*'+vv+'*,';
      }
    }else{
      flag = true;
      if(i==props.queryList.length-1){
        if(tt.showType=='input'||tt.showType=='inputSelect'||tt.showType=='moreInput'
          ||tt.showType=='textarea'){
          if(tt.hide||tt.hide=='true'){
            query += '"'+tt.code+'":"'+vv+'"';
          }else{
            query += '"'+tt.code+'":*'+vv+'*';
          }
        }else{
          query +='"'+tt.code+'":"'+vv+'"';
        }
      }else{
        if(tt.showType=='input'||tt.showType=='inputSelect'||tt.showType=='moreInput'
          ||tt.showType=='textarea'){
          if(tt.hide||tt.hide=='true'){
            query += '"'+tt.code+'":"'+vv+'",';
          }else{
            query += '"'+tt.code+'":*'+vv+'*,';
          }
        }else{
          query += '"'+tt.code + '":"'+vv+'",';
        }
      }
    }

  }
  if(!flag){
    emit('update:queryData', '');
  }else{
    emit('update:queryData', query);
  }
},{deep:true})
</script>
