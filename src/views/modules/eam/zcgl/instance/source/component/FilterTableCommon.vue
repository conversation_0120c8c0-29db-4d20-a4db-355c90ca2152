<template>
  <div class="mb-20">
    <im-table column-sortable ref="table" center :toolbar="{
      initialState: {
        aa: 2
      }
    }" table-alert :loading="props.isLoading || state.loading" :data="state.userFactData"
      :columns="state.userFactColumns" :height="tableHeight" :pagination="state.pagination"
      @on-page-size-change="handlePageCurrentChange"
      @on-reload="handlePageCurrentChange(state.pagination.currentPage, state.pagination.pageSize, true)"
      @on-page-current-change="handlePageCurrentChange" @on-filter="handleFilter" @selection-change="selectionChange">


      <template #toolbar-left="toolbarSlot: ImToolbarSlot">
        <el-radio-group v-model="vulnerabilityListState">
          <!-- <div>{{ toolbarSlot }}</div> -->
          <el-radio-button v-for="(item, index) in props.tableLeftFeature"
            :value="index">{{item}}</el-radio-button>
        </el-radio-group>
        <template v-for="item in props.tableLeftFeatureNoTab">
          <!-- <el-button v-if="item != '删除'" type="primary" :disabled="toolbarSlot.checkedRows.length == 0 && item == '删除'"
            @click="handleLeftClick(toolbarSlot.checkedRows, item)">
            {{ item }}
          </el-button> -->
          <el-popconfirm v-if="item == '批量删除'" title="确认删除吗?"
            @confirm="handleLeftDeleteClick(toolbarSlot.checkedRows, item)">
            <template #reference>
              <el-button type="primary" :disabled="toolbarSlot.checkedRows.length == 0 && item == '批量删除'
                ">
                {{ item }}
              </el-button>
            </template>
          </el-popconfirm>
        </template>
        <!-- <el-button
          type="danger"
          :disabled="toolbarSlot.checkedRows.length == 0"
        >
          批量删除
        </el-button> -->
      </template>

      <template #toolbar-right>
        <div class="flex-c gap-2">
          <!-- <el-button type="primary" @click="resetQuery">查询</el-button> -->
          <template v-for="(item, index) in props.tableRightFeature">
            <el-button type="primary" @click="resetQuery">{{ item }}</el-button>
          </template>
        </div>
      </template>

      <template #asset_label_field="{ row, $index }">
        <template v-if="row['asset_label_field'] && row['asset_label_field'].length > 0">
          <!-- <el-tag v-for="item in row['asset_label_field']" type="primary">{{ item }}</el-tag> -->
          <el-tag v-if="row['asset_label_field'].indexOf('僵尸资产') >= 0"
            style="color:white;background-color:#1890FF;">僵尸资产</el-tag>
          <el-tag v-if="row['asset_label_field'].indexOf('事件资产') >= 0"
            style="color:white;background-color:#F5222D;">事件资产</el-tag>
          <el-tag v-if="row['asset_label_field'].indexOf('漏洞资产') >= 0"
            style="color:white;background-color:#F76267;">漏洞资产</el-tag>
          <el-tag v-if="row['asset_label_field'].indexOf('已封堵') >= 0"
            style="color:white;background-color:#F59A23;">已封堵</el-tag>
          <el-tag v-if="row['asset_label_field'].indexOf('属性缺失') >= 0"
            style="color:white;background-color:#73D13D;">属性缺失</el-tag>
          <el-tag v-if="row['asset_label_field'].indexOf('属性重复') >= 0"
            style="color:white;background-color:#AAC995;">属性重复</el-tag>
        </template>
      </template>

      <template #operator="{ row, $index }">
        <el-button type="primary" @click="updateInstance(row)">编辑</el-button>

        <el-popconfirm title="确认删除吗?" @confirm="handleLeftDeleteClick([row], '删除')">
          <template #reference>
            <el-button type="primary">
              删除
            </el-button>
          </template>
        </el-popconfirm>


        <el-button @click="onlienCollection(row)" type="primary">在线探测</el-button>

        <el-button type="primary">查看详情</el-button>


      </template>

      <!-- 这里自定义类型列，#name 为prop的名字 用按钮或者标签 -->
      <!-- <template #name="{ row, $index }">
        <el-button type="primary">{{ row.name }}</el-button>
      </template> -->
    </im-table>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch, defineEmits } from "vue";
import {
  ColumnMetaProps,
  ImTableColumnProps,
  ImTableInstance,
  ImTablePaginationProps,
  ImToolbarSlot,
  IndexRender
} from "@/components/ItsmCommon";
import { ElMessage } from "element-plus";
import {
  deleteInstanceByIds,
  exportInstance
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {
  validataInstanceOnlineAxios
} from "@/views/modules/eam/zcgl/instance/source/api/batchModificationInterface";

const props = defineProps({
  tableLeftFeature: Array<String>,
  tableRightFeature: Array<String>,
  newtableHeight: String,
  userTableColumns: Array,
  isLoading: Boolean,
  userTableData: Object,
  tableLeftFeatureNoTab: Array<String>,
  divisionTable: String,
  currentNodecategoryId: String
});

const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
const updateInstance = (row) =>{
  let sourceInfo = {};
  sourceInfo.editType = 'edit';
  sourceInfo.tabName = ['资产信息', '资产关系拓扑', '资产漏洞概览', '安全风险告警'];
  sourceInfo.instanceId = row.instance_id;
  sourceInfo.categoryId = row.category_id;
  sourceInfo.infoTitle = '资产详情';
  sourceInfo.selectCate = "-1";
  emit("source-select", sourceInfo);
  jumpTo('assetInformationPage');
}

const table = ref<ImTableInstance>();
const tableHeight = ref(props.newtableHeight || 536);
const assetTypes = reactive([]);
const treeData = reactive([]);
const state = reactive({
  loading: false,
  // 快捷设置所有的列居中
  center: false,
  pagination: <ImTablePaginationProps>{
    align: "right",
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 40, 60],
    props: {
      // 更多page属性请参考官网配置
    }
  },
  // 操作
  operator: <ImTableColumnProps<any>>{
    label: "操作",
    fixed: "right",
    width: "166"
  },
  userFactColumns:
    props.userTableColumns ||
    <ImTableColumnProps<any>[]>[
      {
        label: "选择",
        type: "selection",
        width: "60px"
      },
      {
        label: "序号",
        render: IndexRender,
        width: "80px"
      },
      {
        label: "编号",
        align: "left",
        prop: "id",
        width: "120px"
      },
      {
        label: "名称",
        prop: "name",
        width: "180px",
        filterable: true,
        meta: {
          filterType: "input" // 使用简单的文本框
        }
      },
      {
        label: "下拉过滤",
        prop: "type",
        width: "180px",
        filterable: true,
        meta: {
          // 设置类型为下拉
          filterType: "select", // 或者el-select
          // 定义静态数组
          options: [
            { label: "a", value: "a" },
            { label: "b", value: "b" }
          ],
          props: <ColumnMetaProps>{
            placeholder: "请选择类型a",
            // 设置多选
            multiple: true
          }
        }
      },
      {
        label: "下拉过滤2",
        prop: "type2",
        width: "180px",
        filterable: true,
        meta: {
          // 设置类型为下拉
          filterType: "select", // 或者el-select
          // 定义一个可响应的变量，可在接口返回后能更新变化注意结构[{label, value}]
          options: assetTypes,
          props: <ColumnMetaProps>{
            placeholder: "请选择类型2a",
            // 设置是否多选
            multiple: true
          }
        }
      },
      {
        label: "下拉树过滤",
        prop: "type2",
        width: "180px",
        filterable: true,
        meta: {
          // 设置类型为下拉
          filterType: "tree", // 或者el-tree-select
          // 注意tree不使用options，而是使用props的data,请使用可响应数组
          props: <ColumnMetaProps>{
            placeholder: "请选择类型2a",
            // 设置是否多选
            multiple: true,
            data: treeData
          }
        }
      },
      {
        label: "创建时间",
        prop: "createTime",
        width: "180px",
        filterable: true,
        meta: {
          filterType: "date", // 或者el-date-picker
          // 请查看官网属性api
          props: <ColumnMetaProps>{
            startPlaceholder: "开始",
            endPlaceholder: "结束",
            type: "daterange"
          }
        }
      },
      {
        label: "计数",
        prop: "usage",
        width: "180px",
        filterable: true
      }
    ],
  userFactData: props.userTableData?.["list"] || []
});
//监听组件属性
watch(
  props,
  (newValue: any) => {
    state.pagination.total = newValue?.userTableData?.total;
  },
  { deep: true, immediate: true }
);

const vulnerabilityListState = ref(0);

const emit = defineEmits([
  "handlePageCurrentChangeEvent",
  "handleFilterTableDataEvent",
  "handleSelectChangeEvent",
  "handleEditInfoEvent",
  "jump-to",
  "source-select"
]);
// 分页查询接口
const handlePageCurrentChange = (page: number, pageSize: number, reload = false) => {
  emit("handlePageCurrentChangeEvent", page, pageSize);
};

const resetQuery = () => {
  state.pagination.currentPage = 1;
  handlePageCurrentChange(1, state.pagination.pageSize);
};

// handlePageCurrentChange(1, 20);

/**
 * 这里处理过滤操作
 *
 * @param params  {[prop]: value}
 * @param value   当前过滤选中的值
 * @param column  当前操作过滤的字段
 */
const handleFilter = (params, value, column: ImTableColumnProps<any>) => {
  let tmpParams = {};
  for (const element in params) {
    tmpParams[element] = [params[element]];
    if (!params[element]) {
      tmpParams = {};
    }
  }
  emit("handleFilterTableDataEvent", tmpParams);
};

// 所有页使用的表格方法分类
const homePageTableMethod = {
  Import: () => { },
  Export: (categoryId: any) => {
    // 展示导出全部
    try {
      exportInstance({ categoryId: categoryId, isAuth: "true" }).then(
        res => { }
      );
    } catch (error) { }
  },
  New: () => { },
  Deleted: (select: any, item: any) => {
    try {
      deleteInstanceByIds(select).then(res => {
        if (res?.["data"] == "success") {
          ElMessage.success("删除成功");
        } else {
          ElMessage.warning("删除失败");
        }
        emit(
          "handlePageCurrentChangeEvent",
          state.pagination.currentPage,
          state.pagination.pageSize,
          "删除"
        );
      });
    } catch (error) {
      ElMessage.warning("删除失败");
    }
  }
};
const handleLeftDeleteClick = (select: any, item: any) => {
  if (props.divisionTable == "home") {
    let tmpStr = "";
    select.map((item, index) => {
      if (index + 1 == select.length) {
        tmpStr = tmpStr + item.id;
        return;
      }
      tmpStr = tmpStr + item.id + ",";
    });
    homePageTableMethod.Deleted(tmpStr, item);
  }
};
const handleLeftClick = (select: any, item: any) => {
  if (props.divisionTable == "home") {
    if (item == "导入") {
    } else if (item == "导出") {
      homePageTableMethod.Export(props.currentNodecategoryId);
    } else if (item == "新增") {
    }
  }
};

const selectCount = ref(0);
const selectionChange = (select) => {
  selectCount.value = select.length;
  const tmpSelect = select.map(item => item.id);
  let strSelect = '';
  tmpSelect.forEach((item, index) => {
    if (tmpSelect.length == (index + 1)) {
      strSelect = strSelect + item;
    } else {
      strSelect = strSelect + item + ',';
    }
  })
  emit('handleSelectChangeEvent', strSelect);
}

const exposeTableDeleteMethod = (str) => {
  homePageTableMethod.Deleted(str, 'item');
}

// 在线探测
const onlienCollection = (row) => {
  validataInstanceOnlineAxios(row._id).then(res => {
    if (res['data'].result == "success") {
      ElMessage.success("探测完成，结果：" + res['data'].msg);
      // queryFunction();
      handlePageCurrentChange(state.pagination.currentPage, state.pagination.pageSize)
    } else {
      ElMessage.error(res['data'].msg);
    }
  }).catch(exp => {
    console.log(exp);
  })
}

const resetPage = () => {
  state.pagination.currentPage = 1;
  state.pagination.pageSize = 10;
}

const editInfo = (row, info = "") => {
  row['_info'] = info;
  emit("handleEditInfoEvent", row);
}



defineExpose({
  exposeTableDeleteMethod,
  resetPage
})
</script>
