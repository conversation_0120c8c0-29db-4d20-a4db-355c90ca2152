<template>
  <el-container class="assetsUnknown">
    <el-container>
      <el-aside width="390px">
        <el-card :style="{
          // height: rightBodyHeight,
        }" class="card">
          <el-row>
            <el-col style="margin-right: 0.3rem;" :span="11">
              <el-input v-model="filterText" placeholder="请输入内容"></el-input>
            </el-col>
            <el-col :span="12">
              <el-button-group>

                <el-button type="primary" plain @click="ExpandFun">
                  <svg style="width: 17px;height: 17px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                      d="M128 192h768v128H128zm0 256h512v128H128zm0 256h768v128H128zm576-352 192 160-192 128z"></path>
                  </svg>
                </el-button>
                <el-button title="全部折叠" type="primary" plain @click="CloseFun">
                  <svg style="width: 17px;height: 17px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                      d="M896 192H128v128h768zm0 256H384v128h512zm0 256H128v128h768zM320 384 128 512l192 128z"></path>
                  </svg>
                </el-button>
                <el-button title="刷新" type="primary" class="el-icon-refresh" plain @click="treeRefresh">
                  <svg style="width: 15px;height: 15px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                      d="M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z">
                    </path>
                  </svg>
                </el-button>
                <el-button v-has="'category:addParentNode'" type="primary" class="el-icon-circle-plus-outline" plain
                  @click="append('')">
                  <svg style="width: 16px;height: 16px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor" d="M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64"></path>
                    <path fill="currentColor" d="M480 672V352a32 32 0 1 1 64 0v320a32 32 0 0 1-64 0"></path>
                    <path fill="currentColor"
                      d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896">
                    </path>
                  </svg>
                </el-button>
              </el-button-group>
            </el-col>
          </el-row>
          <el-tree class="filter-tree" :data="categoryNode" :props="defaultProps" node-key="id" draggable
            :allow-drop="allowDrop" :expand-on-click-node="false" @node-click="chickNode" @node-drop="nodeDrop"
            :default-expand-all="isExpand" ref="navtree" icon-class="el-icon-arrow-right" v-loading="treeLoading"
            :filter-node-method="filterNode">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <svg-icon v-if="data.parentId == '-1'" iconClass="folder" style="margin-right: 5px"></svg-icon>
                <span :title="node.label">{{ node.label }}</span>
                <span>
                  <el-button-group style="width: 190px">
                    <el-breadcrumb separator="|">
                      <el-breadcrumb-item v-has="'category:addChildNode'"><el-button v-has="'category:addChildNode'"
                          size="mini" type="text" class="el-icon-plus" plain @click.stop="() => append(data)">
                          新建
                        </el-button>
                      </el-breadcrumb-item>
                      <el-breadcrumb-item v-has="'category:addChildNode'">
                        <el-button v-has="'category:edit'" size="mini" type="text" plain class="el-icon-edit"
                          @click.stop="() => edit(data)">
                          编辑
                        </el-button>
                      </el-breadcrumb-item>
                      <el-breadcrumb-item v-has="'category:addChildNode'">
                        <el-button v-has="'category:delete'" size="mini" type="text" class="el-icon-delete" plain
                          @click.stop="() => remove(data)">
                          删除
                        </el-button>
                      </el-breadcrumb-item>
                    </el-breadcrumb>



                  </el-button-group>
                </span>
              </span>
            </template>

          </el-tree>
        </el-card>
      </el-aside>
      <el-main v-show="detailsActiveName">
        <el-card>
          <el-collapse v-model="activeName1" accordion @change="changeCollapse">
            <el-collapse-item title="类别信息" name="1" style="
                float: left;
                font-size: 16px;
                margin-right: 50px;
                text-align: left;
                line-height: 30px;
                font-weight: bolder;
              ">
              <el-row>
                <el-button v-has="'category:export'" type="primary" @click="save('categoryData')">保存</el-button>
              </el-row>
              <el-form :model="categoryData" :rules="rulesInfo" ref="categoryDataRef" label-width="100px"
                class="demo-ruleForm">
                <el-row>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" label="父类别名称：" prop="parentName">
                      <el-input v-model="categoryData.parentName" :disabled="true">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" label="类别名称：" prop="name">
                      <el-input v-model="categoryData.name"> </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" label="描述：" prop="description">
                    <el-input type="textarea" placeholder="描述" v-model="categoryData.description" :disabled="false">
                    </el-input>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" label="类别状态：" prop="description">
                    <el-radio-group v-model="categoryData.state">
                      <el-radio value="enable">启用</el-radio>
                      <el-radio value="disable">禁用</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" label="拓扑类型：" prop="topoType">
                    <el-select v-model="categoryData.topoType">
                      <el-option v-for="item in topoList" :label="item.label" :value="item.value"
                        :key="item.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-card>
        <el-card>
          <el-button v-has="'category:addProperty'" type="primary" size="mini" icon="el-icon-plus"
            @click="toAddProperty" style="margin-right: 10px">
            添加属性
          </el-button>
          <el-button v-has="'category:addRelation'" type="primary" icon="el-icon-plus" size="mini"
            @click="toAddRelationList">
            新增关系
          </el-button>
        </el-card>
        <el-card :style="{
          height: prooertyBodyHeight,
          // height: '37rem',
          overflow: 'auto'
        }">
          <template #default>
            <div style="position:relative;height: 100%;">
              <div style="position:absolute;top:6px;right:20px;width:240px;z-index:100;" v-if="activeName == 'first'">
                <el-input type="text" v-model="tableFilter" clearable placeholder="请输入属性编码或属性名称过滤"
                  @change="queryFilterTable"></el-input>
              </div>
              <el-row style="margin-bottom: 0px">
                <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                  <el-tab-pane label="属性" name="first">
                    <!-- 属性 -->
                  </el-tab-pane>
                  <el-tab-pane label="主动关系" name="second">
                    <!-- 主动关系 -->
                  </el-tab-pane>
                  <el-tab-pane label="被动关系" name="third">
                    <!-- 被动关系 -->
                  </el-tab-pane>
                </el-tabs>
              </el-row>
              <el-row v-show="activeName == 'first'" style="margin-bottom: 5px">
                <el-table :data="tableData" :max-height="tableHeight" style="width: 100%">
                  <el-table-column prop="name" label="属性名称" align="center">
                  </el-table-column>
                  <el-table-column prop="code" label="编码" align="center">
                  </el-table-column>
                  <el-table-column prop="dataType" label="数据类型" align="center">
                  </el-table-column>
                  <el-table-column prop="dataLength" label="数据长度" align="center">
                  </el-table-column>
                  <el-table-column prop="type" label="录入方式" align="center">
                  </el-table-column>
                  <el-table-column prop="isEdit" label="是否可再编辑" align="center">
                  </el-table-column>
                  <el-table-column label="操作" align="center" fixed="right">
                    <template #default="scope">
                      <el-button v-has="'category:recovery'" type="text" v-if="scope.row.isRelation == '是'"
                        @click="resetRelationCategory(scope.row)">复原</el-button>
                      <el-button v-has="'category:recovery'" type="text" v-if="scope.row.isRelation == '否'"
                        disabled>复原</el-button>&nbsp;
                      <el-popover placement="top" width="160">
                        <div style="text-align: center; margin: 0">
                          <el-button type="text" size="mini" @click="updateCatePro(scope.row)">属性</el-button>
                          <el-button type="text" size="mini" @click="setDefaultValue(scope.row)">默认值</el-button>
                        </div>
                        <el-button v-has="'category:personal'" slot="reference" type="text">个性化</el-button>
                      </el-popover>&nbsp;
                      <el-button v-has="'category:remove'" @click="removeProperty(scope.row)" type="text" size="small"
                        v-if="scope.row.removePro">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-row>
              <el-row v-show="activeName == 'second'" style="margin-bottom: 5px">
                <el-table :data="tableData" :max-height="tableHeight" style="width: 100%">
                  <el-table-column prop="name" label="关系名称" align="center">
                  </el-table-column>
                  <el-table-column prop="association_mode" label="关联方式" align="center">
                  </el-table-column>
                  <el-table-column prop="src_property_code" label="源属性" align="center">
                  </el-table-column>
                  <el-table-column prop="relation_category" label="关联类别" align="center">
                  </el-table-column>
                  <el-table-column prop="property_code" label="类别属性" align="center">
                  </el-table-column>
                  <el-table-column prop="property" label="关系类型" align="center">
                  </el-table-column>
                  <el-table-column prop="isEdit" label="是否可编辑" align="center">
                  </el-table-column>
                  <el-table-column prop="valueCategory" label="值类型" align="center">
                  </el-table-column>
                  <el-table-column label="操作" align="center" fixed="right">
                    <template #default="scope">
                      <el-button v-has="'category:edit'" type="text" v-if="scope.row.isEdit == '是'"
                        @click="editRelation(scope.row)">编辑</el-button>
                      <el-button v-has="'category:delete'" @click="removeRelation(scope.row)" type="text"
                        size="small">删除</el-button>
                      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="pageNum1" :page-sizes="[5, 10, 20, 50]" v-if="pageshow" :page-size="pageSize1"
                        ref="pagination" style="text-align: right;" layout="total, sizes, prev, pager, next, jumper"
                        :total="total1">
                      </el-pagination>
                    </template>

                  </el-table-column>
                </el-table>
              </el-row>
              <el-row v-show="activeName == 'third'" style="margin-bottom: 5px">
                <el-table :data="tableData" :max-height="tableHeight" style="width: 100%">
                  <el-table-column prop="name" label="关系名称" align="center">
                  </el-table-column>
                  <el-table-column prop="association_mode" label="关联方式" align="center">
                  </el-table-column>
                  <el-table-column prop="relation_category" label="源关联类别" align="center">
                  </el-table-column>
                  <el-table-column prop="src_property_code" label="源类别属性" align="center">
                  </el-table-column>
                  <el-table-column prop="property_code" label="被关联属性" align="center">
                  </el-table-column>
                  <el-table-column prop="property" label="关系类型" align="center">
                  </el-table-column>
                  <el-table-column prop="isEdit" label="是否可编辑" align="center">
                  </el-table-column>
                  <el-table-column prop="valueCategory" label="值类型" align="center">
                  </el-table-column>
                  <el-table-column label="操作" align="center" fixed="right">
                    <template slot-scope="scope">
                      <el-button v-has="'category:check'" type="text"
                        @click="lookUpBeRelation(scope.row)">查看</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-row>
              <el-row style="margin-bottom: 0px">
                <el-pagination @size-change="handleSizeChange2" @current-change="handleCurrentChange2"
                  :current-page="pageNum2" :page-sizes="[5, 10, 20, 50]" v-if="pageshow2" :page-size="pageSize2"
                  ref="pagination" style="text-align: right;" layout="total, sizes, prev, pager, next, jumper"
                  :total="total2">
                </el-pagination>
              </el-row>
            </div>
            <el-dialog :visible.sync="editRuleDialog">
              <el-row>
                <el-form label-width="90px" ref="editRuleForm" v-model="editRuleForm">
                  <el-col :span="12">
                    <el-form-item label="自动生成规则" prop="ruleCodeName">
                      <el-input type="text" :value="editRuleForm.ruleCodeName" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="生成方式" prop="ruleCodeSn">
                      <el-select v-model="editRuleForm.ruleCodeSn" clearable filterable>
                        <el-option v-for="item in ruleCodeList" :label="item.label" :value="item.value">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div slot="footer" class="dialog-footer" style="text-align:right;">
                <el-button type="primary" @click="saveRuleCode"
                  style="background: #24468c;border-color: #24468c">确定</el-button>
                <el-button type="primary" @click="closeRuleCode" style="background: #c6c8c9;border-color:#c6c8c9;">取消
                </el-button>
              </div>
            </el-dialog>

            <el-dialog title="业务属性维护" :visible.sync="editServiceModel" width="66%">
              <el-form class="form" ref="serviceViewFormRef" :rules="ruleValidate" :model="serviceViewForm"
                label-width="125px">
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="code" label="属性编码">
                      <el-input v-model="serviceViewForm.code" @input="restBianma" placeholder="属性编码"
                        :disabled="disableflag" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="name" label="属性名称">
                      <el-input v-model="serviceViewForm.name" @input="restName" disabled placeholder="属性名称" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="state" label="属性状态">
                      <el-input v-model="serviceViewForm.state" :readonly="true"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="dataType" label="数据类型">
                      <el-select v-model="serviceViewForm.dataType" @change="changeDataType"
                        :disabled="dateTypeDisable">
                        <el-option v-for="(item, index) in data_type_list" :value="item.value" :label="item.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="ocodeInput">
                  <el-col :span="24">
                    <el-form-item prop="ocode" label="对象生成编码">
                      <el-select v-model="serviceViewForm.ocode" @change="changeOcode" disabled>
                        <el-option v-for="(item, index) in ocodeList" :value="item.value" :label="item.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <div v-if="enumrow">
                  <el-row>
                    <el-col :span="16">
                      <el-form-item prop="enumStr" label="枚举值" class="width100">
                        <el-input v-model="serviceViewForm.enumStr" :readonly="true" placeholder="枚举值" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-button type="primary" size="large" class="saveBtn" @click="addEnum" disabled>添加
                      </el-button>
                    </el-col>
                  </el-row>
                </div>
                <el-row>
                  <div v-if="data_length">
                    <el-col :span="12">
                      <el-form-item prop="dataLength" :label="lengthName">
                        <el-input-number class="input" v-model="serviceViewForm.dataLength" :min="1" :step="1"
                          :max="999999999999999" style="width:100%;"></el-input-number>
                      </el-form-item>
                    </el-col>
                  </div>
                  <el-col :span="12">
                    <el-form-item prop="showType" label="展示类型">
                      <el-select v-model="serviceViewForm.showType" ref="show_show_type" @change="changeShowType">
                        <el-option v-for="(item1, index) in show_type_list" :value="item1.value" :key="index"
                          :label="item1.label">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="isCascade">
                  <el-col :span="24">
                    <el-form-item prop="isCascade" label="是否级联">
                      <el-radio-group v-model="serviceViewForm.isCascade" @change="cascadeChange">
                        <el-radio v-for="(item, index) in booleanList" :value="item.value" :key="index">{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="cascadeInfo">
                  <el-col :span="12">
                    <el-form-item prop="cascode" label="级联属性">
                      <el-select v-model="serviceViewForm.cascode" multiple filterable>
                        <el-option v-for="(item1, index) in cascodeList" :value="item1.value" :label="item1.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="casfun" label="级联方式">
                      <el-select v-model="serviceViewForm.casfun">
                        <el-option v-for="(item1, index) in casList" :value="item1.value" :label="item1.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="property" label="录入方式">
                      <el-select v-model="serviceViewForm.property" :disabled="luru" :clearable="true">
                        <el-option v-for="(item, index) in propertyData" :value="item.value" :label="item.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="12">
                    <el-form-item prop="propertyUnit" label="属性单位">
                      <el-input v-model="serviceViewForm.propertyUnit" placeholder="属性单位" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="isAuto" label="是否自动生成">
                      <el-radio-group v-model="serviceViewForm.isAuto" @change="shengcheng">
                        <el-radio v-for="(item, index) in booleanList" :value="item.value" :key="index">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="rules">
                    <el-form-item prop="autoRules" label="生成规则编码">
                      <el-select v-model="serviceViewForm.autoRules">
                        <el-option v-for="(item, index) in autoRulesList" :value="item.value" :label="item.label"
                          :key="index">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="isReEdit" label="是否可再编辑">
                      <el-radio-group v-model="serviceViewForm.isReEdit">
                        <el-radio v-for="(item, index) in booleanList" :value="item.value" :key="index">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="relationType" label="关联的资产类别">
                      <el-input type="input" :disabled="true" v-model="serviceViewForm.relationType" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.dataType == 'string' || serviceViewForm.dataType == 'date'
                  || serviceViewForm.dataType == 'dateTime' || serviceViewForm.dataType == 'long'">
                  <el-col :span="12">
                    <el-form-item prop="isRule" label="是否开启校验">
                      <el-radio-group v-model="serviceViewForm.isRule">
                        <el-radio v-for="(item, index) in booleanList" :value="item.value" :key="index">{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="serviceViewForm.isRule == '1'">
                    <el-form-item prop="ruleType" label="校验方式">
                      <el-select v-model="serviceViewForm.ruleType">
                        <el-option value="el" label="EL表达式"></el-option>
                        <el-option value="reg" label="正则表达式"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.isRule == '1'">
                  <el-col :span="24">
                    <el-row v-if="serviceViewForm.ruleType == 'el'">
                      <el-col :span="24" style="padding-left:130px;">
                        <el-button size="mini" circle @click="addRuleValue('#value')">属性</el-button>
                        <el-button size="mini" circle @click="addRuleValue('>')">&gt;</el-button>
                        <el-button size="mini" circle @click="addRuleValue('<')">&lt;</el-button>
                        <el-button size="mini" circle @click="addRuleValue('>=')">&gt;=</el-button>
                        <el-button size="mini" circle @click="addRuleValue('<=')">&lt;</el-button>
                        <el-button size="mini" circle @click="addRuleValue('==')">==</el-button>
                        <el-button size="mini" circle @click="addRuleValue('like')">包含</el-button>
                        <el-button size="mini" circle @click="addRuleValue('notLike')">不包含</el-button>
                        <el-button size="mini" circle @click="addRuleValue('(')">(</el-button>
                        <el-button size="mini" circle @click="addRuleValue(')')">)</el-button>
                        <el-button size="mini" circle @click="addRuleValue('&&')">并且</el-button>
                        <el-button size="mini" circle @click="addRuleValue('||')">或者</el-button>
                        示例:(#value=="ac"||#value=="ab")&&#value.indexOf("a")>-1
                      </el-col>
                    </el-row>
                    <el-row v-if="serviceViewForm.ruleType == 'reg'">
                      <el-col :span="24" style="padding-left:130px;">
                        <el-button size="mini" circle @click="addRuleRegValue('email')">邮件</el-button>
                        <el-button size="mini" circle @click="addRuleRegValue('ipv4')">IPV4</el-button>
                        <el-button size="mini" circle @click="addRuleRegValue('yuming')">域名</el-button>
                        <el-button size="mini" circle @click="addRuleRegValue('phone')">电话号码</el-button>
                        <el-button size="mini" circle @click="addRuleRegValue('telephone')">手机号码</el-button>
                        <el-button size="mini" circle @click="addRuleRegValue('shenfen')">身份证号</el-button>
                        示例:^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item prop="ruleValue" label="表达式">
                          <el-input type="textarea" v-model="serviceViewForm.ruleValue" clearable></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>

                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="isEncryption" label="是否加密" v-if="serviceViewForm.showType == 'password'">
                      <el-radio-group v-model="serviceViewForm.isEncryption">
                        <el-radio :value="'1'" key="0">是</el-radio>
                        <el-radio :value="'0'" key="1">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="isViewPlaintext" label="查看明文密码"
                      v-if="serviceViewForm.showType == 'password' && serviceViewForm.isEncryption == '1'">
                      <el-radio-group v-model="serviceViewForm.isViewPlaintext">
                        <el-radio :value="'1'" key="0">是</el-radio>
                        <el-radio :value="'0'" key="1">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item prop="encryptionMethod" label="加密方式"
                      v-if="serviceViewForm.showType == 'password' && serviceViewForm.isEncryption == '1'">
                      <el-select v-model="serviceViewForm.encryptionMethod" clearable filterable>
                        <el-option v-for="(item, index) in encryptionMethodList" :value="item.value" :label="item.label"
                          :key="index"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item prop="titleDesc" label="录入提示">
                      <el-input v-model="serviceViewForm.titleDesc" type="textarea" placeholder="请输入录入提示..."></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="tooltipType" label="表格悬浮生成方式">
                      <el-select clearable filterable v-model="serviceViewForm.tooltipType" @change="changeTooltipType">
                        <el-option v-for="item in toolTipTypeList" :value="item.value" :label="item.label"
                          :key="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="serviceViewForm.tooltipType == '2'">
                    <el-form-item prop="tooltipValue" label="生成规则">
                      <el-select clearable filterable v-model="serviceViewForm.tooltipValue">
                        <el-option v-for="item in tooltipRuleList" :value="item.value" :label="item.label"
                          :key="item.value"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.tooltipType == '3'">
                  <el-col :span="24">
                    <el-form-item prop="tooltipValue" label="固定值">
                      <el-input v-model="serviceViewForm.tooltipValue" type="textarea"
                        placeholder="请输入固定值..."></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  v-if="serviceViewForm.showType == 'windowbox' || serviceViewForm.showType == 'mul_windowbox'
                    || serviceViewForm.showType == 'windowboxTree' || serviceViewForm.showType == 'windowboxFilter' || serviceViewForm.showType == 'windowboxTreeFilter'">
                  <el-col :span="24">
                    <el-form-item prop="isShowClear" label="是否启用清空按钮">
                      <el-radio-group v-model="serviceViewForm.isShowClear">
                        <el-radio v-for="(item, index) in booleanList" :value="item.value" :key="index">{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item prop="isHyperLink" label="是否超链接">
                      <el-radio-group v-model="serviceViewForm.isHyperLink">
                        <el-radio v-for="(item, index) in booleanList" :label="item.value" :key="index">{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="serviceViewForm.isHyperLink == '1'">
                    <el-form-item prop="hyperLinkType" label="超链接类型">
                      <el-select v-model="serviceViewForm.hyperLinkType">
                        <el-option value="url" label="链接"></el-option>
                        <el-option value="menu" label="菜单"></el-option>
                        <el-option value="custom" label="自定义"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.isHyperLink == '1'">
                  <el-col :span="24" v-if="serviceViewForm.hyperLinkType == 'url'">
                    <el-form-item prop="hyperLinkValue" label="链接URL">
                      <el-input type="textarea" v-model="serviceViewForm.hyperLinkValue"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="serviceViewForm.hyperLinkType == 'menu'">
                    <el-form-item prop="hyperLinkValue" label="菜单标识">
                      <el-input type="text" v-model="serviceViewForm.hyperLinkValue"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="serviceViewForm.hyperLinkType == 'custom'">
                    <el-form-item prop="hyperLinkValue" label="自定义编码">
                      <el-input type="text" v-model="serviceViewForm.hyperLinkValue"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12"
                    v-if="serviceViewForm.hyperLinkType == 'menu' || serviceViewForm.hyperLinkType == 'custom'">
                    <el-form-item prop="hyperLinkShow" label="链接展示方式">
                      <el-select v-model="serviceViewForm.hyperLinkShow">
                        <el-option v-if="serviceViewForm.hyperLinkType == 'url'" value="newPage"
                          label="新页面"></el-option>
                        <el-option v-if="serviceViewForm.hyperLinkType == 'menu'" value="newMenu"
                          label="新菜单"></el-option>
                        <el-option v-if="serviceViewForm.hyperLinkType != 'menu'" value="newDialog"
                          label="弹窗"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.isHyperLink == '1'">
                  <el-col :span="12" v-if="serviceViewForm.hyperLinkType == 'url'">
                    <el-form-item prop="hyperLinkShow" label="链接展示方式">
                      <el-select v-model="serviceViewForm.hyperLinkShow">
                        <el-option v-if="serviceViewForm.hyperLinkType == 'url'" value="newPage"
                          label="新页面"></el-option>
                        <el-option v-if="serviceViewForm.hyperLinkType == 'menu'" value="newMenu"
                          label="新菜单"></el-option>
                        <el-option v-if="serviceViewForm.hyperLinkType != 'menu'" value="newDialog"
                          label="弹窗"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row v-if="serviceViewForm.isHyperLink == '1'">
                  <el-col :span="16">
                    <el-form-item prop="hyperLinkParam" label="参数" class="width100">
                      <el-input v-model="serviceViewForm.hyperLinkParam" :readonly="true" placeholder="参数" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-button type="primary" size="large" @click="addHyperLinkParamMethod">添加
                    </el-button>
                  </el-col>
                </el-row>
              </el-form>

              <div slot="footer">
                <el-row style="position:relative;">
                  <el-col :span="24" style="text-align: center">
                    <el-button type="primary" size="large" class="saveBtn" @click="saveService('serviceViewForm')">确定
                    </el-button>

                    <el-button type="primary" class="modal-btn" size="large"
                      style="background-color: #dae3ee;border-color: #dae3ee;color:#333333"
                      @click="closeService('serviceViewForm')">取消
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </el-dialog>


            <el-dialog :visible.sync="editEnumModel" title="添加枚举值" width="1200" style="z-index:1000;" append-to-body>
              <el-form class="form" v-for="(enumForm, index) in enumFormList" :key="index" label-width="90px">
                <el-row>
                  <el-col :span="7">
                    <el-form-item prop="'id'+index" label="序号(必填)">
                      <el-input v-model="enumForm.id" type="number" placeholder="序号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item prop="'value'+index" label="编码(必填)">
                      <el-input v-model="enumForm.value" placeholder="编码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">

                    <el-form-item prop="'label'+index" label="名称(必填)">
                      <el-input v-model="enumForm.label" placeholder="名称" />
                    </el-form-item>
                  </el-col>


                  <el-col :span="3">
                    <!--<i-input v-model="enumForm.value"  placeholder="编码"/>   -->
                    <el-button type="primary" class="new-btn" size="large" @click="removeForm(index)">移除</el-button>
                  </el-col>
                </el-row>

              </el-form>
              <div slot="footer">
                <el-row>
                  <el-col :span="6">&nbsp;&nbsp;</el-col>
                  <el-col :span="4">
                    <el-button type="primary" size="large" class="modal-btn" @click="dataEnums()"
                      style="background: #4178f5;border-color: #4178f5">动态加载
                    </el-button>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" size="large" class="modal-btn" @click="addForm()"
                      style="background: #4178f5;border-color: #4178f5">添加行
                    </el-button>
                  </el-col>
                  <el-col :span="4">
                    <el-button type="primary" size="large" class="modal-btn" @click="saveEnum('enumFormss')"
                      style="background: #4178f5;border-color: #4178f5">确定
                    </el-button>
                  </el-col>
                  <el-col :span="6">&nbsp;&nbsp;</el-col>
                </el-row>
              </div>
            </el-dialog>


            <el-dialog title="添加属性" v-model="addPropertyDialogFormVisible">
              <template #default>
                <el-input placeholder="属性名称" v-model="peopertyNameInput">
                  <el-button slot="append" icon="el-icon-search" @click="queryPropertyByName"></el-button>
                </el-input>
                <el-table :data="propertyList" tooltip-effect="dark" style="width: 100%"
                  @selection-change="handleSelectionChange">
                  <el-table-column type="selection" align="center">
                  </el-table-column>
                  <el-table-column prop="name" label="属性名称" align="center">
                  </el-table-column>
                  <el-table-column prop="code" label="属性编码" align="center">
                  </el-table-column>
                  <el-table-column prop="interaction" label="展示类型" align="center">
                  </el-table-column>
                </el-table>
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                  :current-page="pageNum1" :page-sizes="[5, 10, 20, 50]" v-if="pageshow" :page-size="pageSize1"
                  ref="pagination" style="text-align: right;" layout="total, sizes, prev, pager, next, jumper"
                  :total="total1">
                </el-pagination>
              </template>
              <template #footer class="dialog-footer">
                <el-button @click="saveProperties">确认</el-button>
                <el-button @click="resetPropertyList">重置</el-button>
                <el-button @click="addPropertyDialogFormVisible = false">取消</el-button>
              </template>
            </el-dialog>
            <el-dialog title="资产类别关系维护" v-model="categoryRelationdialogFormVisible" style="text-align: left">
              <el-form :model="addRelationList" :disabled="isEditCategoryRelation" :rules="ruleRelation"
                ref="addRelationListRef">
                <el-row>
                  <el-form-item style="width: 100%;" label="关联方式" :label-width="formLabelWidth" prop="association_mode">
                    <el-select v-model="addRelationList.association_mode" @change="changeMode">
                      <el-option :key="item.value" v-for="item in assList" :value="item.value" :label="item.label">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" label="关系名称" :label-width="formLabelWidth" prop="name">
                    <el-input v-model="addRelationList.name" autocomplete="off"></el-input>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" label="关系类型" :label-width="formLabelWidth" prop="property">
                      <el-select style="width: 100%;" v-model="addRelationList.property">
                        <el-option :key="item.value" v-for="item in relation" :value="item.value" :label="item.label">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" v-if="addRelationList.association_mode == 'instance'" label="值类型"
                      :label-width="formLabelWidth" prop="valueCategory">
                      <el-select v-model="addRelationList.valueCategory" @change="changeValueCategory">
                        <el-option :key="item.value" v-for="item in valueCategory" :value="item.value"
                          :label="item.label">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" v-if="addRelationList.association_mode == 'property'" label="源属性"
                    :label-width="formLabelWidth" prop="src_property_code">
                    <el-select v-model="addRelationList.src_property_code">
                      <el-option :key="item.value" v-for="item in scodeList" :value="item.value" :label="item.label">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" label="关联类别" :label-width="formLabelWidth" prop="categoryIds">
                      <!--<el-select-->
                      <!--v-model="addRelationList.categoryIds"-->
                      <!--@change="changeCategory"-->
                      <!--filterable-->
                      <!--:multiple="cateMultiple"-->
                      <!--&gt;-->
                      <!--<el-option-->
                      <!--:key="item.id"-->
                      <!--v-for="item in RelationCategoryExceptList"-->
                      <!--:value="item.id"-->
                      <!--:label="item.name"-->
                      <!--&gt;-->
                      <!--</el-option>-->
                      <!--</el-select>-->
                      <!-- <div>{{ addRelationList.categoryIds }}</div> -->
                      <el-tree-select :multiple="cateMultiple" node-key="id" v-model="addRelationList.categoryIds"
                        :data="RelationCategoryExceptList" :render-after-expand="false" :show-checkbox="cateMultiple"
                        style="width: 240px" />
                      <!-- <avue-input-tree ref="tree" include-half-checked :multiple="cateMultiple"
                        v-model="addRelationList.categoryIds" placeholder="请选择内容" :dic="RelationCategoryExceptList"
                        node-key="id" title-key="label" @on-change="changeCategory" filter></avue-input-tree> -->
                      <!-- <div>{{ RelationCategoryExceptList }}</div> -->
                      <!-- <im-select-tree :multiple="cateMultiple" :tool-options="[]" :model="RelationCategoryExceptList"
                        children-key="children" style="width:100%" only-leaf-select fixed-position :maxContentHeight=265
                        v-model="addRelationList.categoryIds" id-key="id" title-key="label" @on-change="changeCategory">
                      </im-select-tree> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item style="width: 100%;" label="是否允许编辑" :label-width="formLabelWidth" prop="isEdit">
                      <el-radio-group v-model="addRelationList.isEdit">
                        <el-radio :label="true">是</el-radio>
                        <el-radio :label="false">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" v-if="addRelationList.association_mode == 'property'" label="类别属性"
                    :label-width="formLabelWidth" prop="property_code">
                    <el-select v-model="addRelationList.property_code">
                      <el-option :key="item.value" v-for="item in pcodeList" :value="item.value" :label="item.label">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-form-item style="width: 100%;" label="关系描述" :label-width="formLabelWidth">
                    <el-input v-model="addRelationList.description" autocomplete="off" type="textarea"></el-input>
                  </el-form-item>
                </el-row>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="saveCategoryRelation('addRelationList')" v-show="isShow">确认</el-button>
                <el-button @click="resetRelationList('addRelationList')" v-show="isShow">重置</el-button>
                <el-button @click="closeRelation('addRelationList')">取消</el-button>
              </div>
            </el-dialog>

            <el-dialog :visible.sync="modal1" width="700" title="动态加载">
              <el-row>
                <el-col :span="4" style="padding-top:5px;text-align:right;">生成规则：</el-col>
                <el-col :span="20"> <el-select v-model="enumCode">
                    <el-option v-for="(item, index) in codeList" :value="item.value" :label="item.label" :key="index">
                    </el-option>
                  </el-select></el-col>
              </el-row>
              <div slot="footer">
                <el-row>
                  <el-col :span="3">&nbsp;&nbsp;</el-col>
                  <el-col :span="6">
                    <el-button type="primary" size="large" class="modal-btn" @click="ok"
                      style="background: #24468c;border-color: #24468c">确认
                    </el-button>
                  </el-col>
                  <!--<i-button type="primary" size="large"-->
                  <!--@click="relationCata">关联资产类别-->
                  <!--</i-button>-->
                  <el-col :span="6">
                    <el-button type="primary" class="modal-btn" size="large"
                      style="background: #c6c8c9;border-color:#c6c8c9;" @click="resetLoad">重置
                    </el-button>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="primary" class="modal-btn" size="large"
                      style="background: #c6c8c9;border-color:#c6c8c9;" @click="closeLoad">取消
                    </el-button>
                  </el-col>
                  <el-col :span="3">&nbsp;&nbsp;</el-col>
                </el-row>

              </div>
            </el-dialog>


            <el-dialog :visible.sync="defaultValueShow" title="默认值维护" @close="closeDefaultValue">
              <el-form ref="defaultValueForm" :model="defaultValueForm" label-width="120px">
                <el-form-item label="默认值方式">
                  <el-radio-group v-model="defaultValueForm.default_type" @change="defaultTypeChange">
                    <el-radio :label="'1'">固定值</el-radio>
                    <el-radio :label="'2'" v-if="defaultValueShowType != 'dateTime' && defaultValueShowType != 'date'
                      && defaultValueShowType != 'number' && defaultValueShowType != 'checkbox' && defaultValueShowType != 'radio' && defaultValueShowType != 'cascader'
                      && defaultValueShowType != 'objectTable' && defaultValueShowType != 'table'">动态生成</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="生成方式" v-if="defaultValueForm.default_type == '2'">
                  <el-select v-model="defaultValueForm.default_value" filterable>
                    <el-option v-for="item in defaultRuleList" :label="item.label" :value="item.value"
                      :key="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="defaultValueForm.default_type == 1" label="值">
                  <!--普通输入框 input （string，number）-->
                  <span
                    v-if="defaultValueShowType == 'input' || defaultValueShowType == 'telephone' || defaultValueShowType == 'textarea' || defaultValueShowType == 'email' || defaultValueShowType == 'phone' || defaultValueShowType == 'snmp_addr'">
                    <!--数据类型 input、number、text、select、checkbox、radio-->
                    <span>
                      <el-input v-model="defaultValueForm.default_value" :placeholder="defaultValueName"
                        clearable></el-input>
                    </span>
                  </span>
                  <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                  <span v-if="defaultValueShowType == 'dateTime' || defaultValueShowType == 'date'">
                    <im-date v-model="defaultValueForm.default_value"></im-date>
                  </span>
                  <span v-if="defaultValueShowType == 'number'">
                    <el-input-number v-model="defaultValueForm.default_value"></el-input-number>
                  </span>
                  <span v-if="defaultValueShowType == 'checkbox'">
                    <checkbox-group v-model="defaultValueForm.default_value">
                      <checkbox :label="cn.value" v-for="(cn, index) in defaultValueList" :key="cn.value">{{ cn.label }}
                      </checkbox>

                    </checkbox-group>
                  </span>

                  <!--单选-->
                  <span v-if="defaultValueShowType == 'radio'">
                    <el-radio-group v-model="defaultValueForm.default_value">
                      <el-radio :label="cn.value" v-for="(cn, index) in defaultValueList" :key="index">{{ cn.label
                        }}</el-radio>
                    </el-radio-group>
                  </span>
                  <span v-if="defaultValueShowType == 'cascader'">
                    <el-cascader :options="defaultValueList" clearable
                      v-model="defaultValueForm.default_value"></el-cascader>
                  </span>
                  <span v-if="defaultValueShowType == 'windowbox'">
                    <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
                    <el-input v-model="defaultValueForm.defaultShowValue" readonly clearable
                      :placeholder="defaultValueName">
                      <el-button slot="prepend" icon="el-icon-search" @click="openSelect(defaultValueObj)"></el-button>
                      <el-button slot="append" icon="el-icon-close" @click="clearSelect(defaultValueObj)"></el-button>
                    </el-input>
                  </span>

                  <span v-if="defaultValueShowType == 'mul_windowbox'">
                    <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
                    <el-input v-model="defaultValueForm.defaultShowValue" readonly clearable
                      :placeholder="defaultValueName">
                      <el-button slot="prepend" icon="el-icon-search"
                        @click="openMulSelect(defaultValueObj)"></el-button>
                      <el-button slot="append" icon="el-icon-close"
                        @click="clearMulSelect(defaultValueObj)"></el-button>
                    </el-input>
                  </span>
                  <!-- 复选下拉-->

                  <span v-if="defaultValueShowType == 'mul_combobox'">
                    <el-select placement="top" filterable clearable :multiple="true"
                      v-model="defaultValueForm.default_value">
                      <el-option v-for="(en, index) in defaultValueList" :value="en.value" :key="index">{{ en.label
                        }}</el-option>
                    </el-select>
                  </span>

                  <span v-if="defaultValueShowType == 'comboTree'">
                    <im-select-tree filter-on-input :model="defaultValueList" children-key="children" style="width:100%"
                      clearable enable-scroll :scroll-count="20" fixed-position :maxContentHeight=265
                      v-model="defaultValueForm.default_value" id-key="id" title-key="title">
                    </im-select-tree>
                  </span>
                  <span v-if="defaultValueShowType == 'combobox'">
                    <el-select filterable clearable v-model="defaultValueForm.default_value">
                      <el-option v-for="(en, index) in defaultValueList" :key="index" :value="en.value"
                        :label="en.label">
                      </el-option>
                    </el-select>
                  </span>
                  <span v-if="defaultValueShowType == 'windowboxTree'">
                    <el-input v-model="defaultValueForm.default_value" v-show="false"></el-input>
                    <el-input v-model="defaultValueForm.defaultShowValue" readonly clearable
                      :placeholder="defaultValueName">
                      <el-button slot="prepend" icon="el-icon-search"
                        @click="openTreeSelect(defaultValueObj)"></el-button>
                      <el-button slot="append" icon="el-icon-close"
                        @click="clearTreeSelect(defaultValueObj)"></el-button>
                    </el-input>
                  </span>
                  <span v-if="defaultValueShowType == 'windowboxFilter'">
                    <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-search" @click="openFilterSelect(defaultValueObj)"></el-button>
                      </div>
                      <div style="display: table-cell;">
                        <el-select style="width:100%" v-model="defaultValueForm.default_value" multiple clearable
                          filterable remote reserve-keyword placeholder="请输入关键词"
                          @focus="(val) => clearTableList(val, defaultValueObj)"
                          :remote-method="(vul) => remoteMethod(vul, defaultValueObj)">
                          <el-option v-for="item1 in windowboxFilterTable" :key="item1.value" :label="item1.label"
                            :value="item1.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-close" @click="clearSelect(defaultValueObj)"></el-button>
                      </div>
                    </div>
                  </span>
                  <span v-if="defaultValueShowType == 'inputSelect'">
                    <el-input-tag v-model="defaultValueForm.default_value"></el-input-tag>
                  </span>
                  <span v-if="defaultValueShowType == 'moreInput'"> <!--数据类型 input、number、text、select、checkbox、radio-->
                    <input-multiple v-model="defaultValueForm.default_value"></input-multiple>
                  </span>
                  <span v-if="defaultValueShowType == 'windowboxTreeFilter'">
                    <div style="overflow:hidden;display: inline-table;vertical-align: top;    width: 100%;">
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-search" @click="openWindowTree(defaultValueObj)"></el-button>
                      </div>
                      <div style="display: table-cell;">
                        <el-select v-model="defaultValueForm.default_value" style="width:100%" multiple filterable
                          clearable remote reserve-keyword placeholder="请输入关键词"
                          @focus="(val) => clearTreeTableList(val, defaultValueObj)"
                          :remote-method="(vul) => remoteTreeMethod(vul, defaultValueObj)">
                          <el-option v-for="item1 in windowboxFilterTable" :key="item1.value" :label="item1.label"
                            :value="item1.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div style="display: table-cell;    width: 44px;    vertical-align: top;">
                        <el-button icon="el-icon-close" @click="clearWindowTree(defaultValueObj)"></el-button>
                      </div>
                    </div>

                  </span>
                </el-form-item>
              </el-form>
              <div slot="footer">
                <el-button type="primary" @click="saveDefaultValue">保存</el-button>
                <el-button type="primary" @click="cancelDefaultValue">取消</el-button>
              </div>
            </el-dialog>


            <el-dialog :visible.sync="selectMulDataModal" v-if="selectMulDataModal" width="800" append-to-body
              class="otherTable" title=" " @on-cancel="closePropSelectMul">
              <el-row>
                <el-col :span="20">
                  <el-input v-model="propValue" clearable placeholder="值筛选"></el-input>
                </el-col>
                <el-col :span="2" style="text-align: center">
                  <el-button type="primary" class="new-btn" size="small" @click="queryMulValue">
                    查询
                  </el-button>
                </el-col>
                <el-col :span="2" style="text-align: center">
                  <el-button type="primary" class="new-btn" size="small" @click="resetMulSelect"
                    style="background-color: #c6c8c9;border-color: #c6c8c9">
                    重置
                  </el-button>
                </el-col>
              </el-row>

              <el-table :data="mulData" ref="windowMulTable" stripe v-loading="windowboxloading"
                @selection-change="handleSelectionMulChange" element-loading-background="rgba(0, 0, 0, 0.5)"
                element-loading-text="数据正在加载中" row-key="value" style="width: 100%" height="300px">
                <el-table-column type="selection" reserve-selection></el-table-column>
                <el-table-column v-for="(item, index) in mulColumns" :prop="item.key" :label="item.title" :key="index"
                  :show-overflow-tooltip="true">
                </el-table-column>
              </el-table>
              <el-pagination :current-page="currentMulPageW" :page-size="pageMulSizeW" :page-sizes="[5, 10, 20, 50]"
                :total="totalMulW" style="text-align: right;" @current-change="handleCurrentMulChangeW"
                @size-change="handleSizeMulChangeW" layout="total, sizes, prev, pager, next, jumper">
              </el-pagination>
              <div slot="footer">
                <el-row>
                  <el-col :span="24" style="text-align: center">
                    <el-button type="primary" @click="saveSelectMul" class="modal-btn"
                      style="background: #24468c;border-color: #24468c">确定
                    </el-button>
                    <el-button type="primary" @click="closePropSelectMul" class="modal-btn"
                      style="background: #c6c8c9;border-color:#c6c8c9;">取消
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </el-dialog>

            <el-dialog :visible.sync="windowTreeDialog" :modal="false" fullscreen append-to-body destroy-on-close
              style="z-index:99999;">
              <windowBoxTree :item="treeItem" v-if="windowTreeDialog" @closeWindowTree="closeWindowTree">
              </windowBoxTree>
            </el-dialog>
          </template>
        </el-card>
      </el-main>
    </el-container>
  </el-container>
</template>

<script lang="ts" setup>
import {
  aveCategoryOrderAxios,
  categoryRelationVerifyNameAxios,
  categoryTreeoAxios,
  categoryVerifyDelAxios,
  categoryVerifyNameAxios,
  deleteCategoryByIdAxios,
  deleteCategoryPropertyAxios,
  deleteCategoryRelationByIdAxios,
  deleteCrpUrlAxios,
  getCategoryBeRelationByIdAxios,
  getCategoryByIdAxios,
  getCategoryRelationByIdAxios,
  queryAutoRuleInfoAxios,
  queryBeCategoryRelationByCategoryIdAxios,
  queryCategoryRelationByCategoryIdAxios,
  queryNonPropertyByIdAxios,
  queryPropertiesByCategoryIdAxios,
  queryPropertyByIdAxios,
  saveCategoryAxios,
  saveCategoryRelationAxios,
  saveEditRuleAxios,
  savePropertyAxios,
} from "@/views/modules/eam/zcgl/instance/source/api/categoryModelInterface";
// import ElInputTag from "../../util/ElInputTag";
import {
  checkRuleElTypeAxios,
  initComboboxMethod,
  initPropertyVerifyDelMethod,
  initShowTypeByOcodeMethod,
  initShowTypeMethod,
  queryEnumCodeList,
  queryEnumListDe,
  queryPropertyGxhDefaultByIdAxios,
  saveCategoryProperty,
  saveDefaultGxhValueAxios,
  validataProCode,
  validataProName,
  queryEnumList
} from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
// import windowBoxTree from "../../instance/windowBoxTree";
// import { mapGetters } from "vuex";
// import InputMultiple from "../../instance/inputMultiple";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, nextTick, watch } from 'vue';

const checkName = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请填写属性名称!"));
  } else {
    if (serviceViewForm.name != oldName.value) {
      validataProName(serviceViewForm).then(res => {
        if (res.data.status != "success") {
          callback(new Error("属性名称已存在，请修改！"));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  }
};
const checkCode = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请填写属性编码!"));
  } else {
    if (serviceViewForm.id == null || serviceViewForm.id == "") {
      validataProCode(serviceViewForm).then(res => {
        if (res.data.status != "success") {
          callback(new Error("属性编码已存在，请修改！"));
        } else {
          callback();
        }
      });
    } else {
      callback();
    }
  }
};
const checkProperty = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请选择录入方式!"));
  } else {
    callback();
  }
};
const checkDataType = (rule, value, callback) => {
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请选择数据类型!"));
  } else {
    callback();
  }
};
const checkDataLength = (rule, value, callback) => {
  if (
    serviceViewForm.dataType != "enum" &&
    serviceViewForm.dataType != "date" &&
    serviceViewForm.dataType != "dateTime"
  ) {
    if (value == "undefined" || value == null || value == "") {
      callback(new Error("请填写数据长度!"));
    } else if (value <= 0) {
      callback(new Error("数据长度必须大于0"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkShowType = (rule, value, callback) => {
  value = serviceViewForm.showType;
  if (value == "undefined" || value == null || value == "") {
    callback(new Error("请选择展示类型!!!"));
  } else {
    callback();
  }
};
const checkEnumStr = (rule, value, callback) => {
  if (serviceViewForm.dataType == "enum") {
    if (value == "undefined" || value == null || value == "") {
      callback(new Error("请添加枚举值!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkOcode = (rule, value, callback) => {
  if (serviceViewForm.dataType == "object") {
    if (value == undefined || value == null || value == "") {
      callback(new Error("请输入对象生成编码!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkAutoRules = (rule, value, callback) => {
  if (serviceViewForm.isAuto == "1") {
    if (value == undefined || value == null || value == "") {
      callback(new Error("请输入规则编码!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkIsCascade = (rule, value, callback) => {
  if (
    serviceViewForm.showType == "combobox" ||
    serviceViewForm.showType == "windowbox"
  ) {
    if (value == undefined || value == null || value == "") {
      callback(new Error("请选择是否级联!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkCascode = (rule, value, callback) => {
  if (serviceViewForm.isCascade == "1") {
    if (value == undefined || value == null || value == "") {
      callback(new Error("请选择级联属性!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkCasFun = (rule, value, callback) => {
  if (serviceViewForm.isCascade == "1") {
    if (value == undefined || value == null || value == "") {
      callback(new Error("请选择级联规则!"));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkRuleValue = (rule, value, callback) => {
  if (serviceViewForm.isRule == '1') {
    if (value == "undefined" || value == null || value == "") {
      callback(new Error('请输入表达式!'));
    } else {
      if (serviceViewForm.ruleType == 'el') {
        checkRuleElTypeAxios(serviceViewForm).then(res => {
          if (res.data == "ok") {
            callback();
          } else {
            callback(new Error("EL表达式格式错误，请检查表达式"));
          }
        })
      } else if (serviceViewForm.ruleType == 'reg') {
        try {
          let flag = eval("/" + value + "/") instanceof RegExp;
          if (!flag) {
            callback(new Error('正则表达式格式错误，请检查表达式!'));
          } else {
            callback();
          }
        } catch (e) {
          callback(new Error('正则表达式格式错误，请检查表达式!'));
        }
      } else {
        callback();
      }

    }
  } else {
    callback();
  }
};
const checkRuleType = (rule, value, callback) => {
  if (serviceViewForm.isRule == '1') {
    if (value == "undefined" || value == null || value == "") {
      callback(new Error('请选择校验类型!'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkTooltipValue = (rule, value, callback) => {
  if (serviceViewForm.tooltipType == "2") {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择生成规则!'));
    } else {
      callback();
    }
  } else if (serviceViewForm.tooltipType == "3") {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请填写固定值!'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkHyperLinkType = (rule, value, callback) => {
  if (serviceViewForm.isHyperLink == '1') {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择链接类型!'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkHyperLinkValue = (rule, value, callback) => {
  if (serviceViewForm.isHyperLink == '1') {
    if (value == undefined || value == null || value == "") {
      if (serviceViewForm.hyperLinkType == 'url') {
        callback(new Error('请填写链接地址!'));
      } else if (serviceViewForm.hyperLinkType == 'menu') {
        callback(new Error('请填写菜单标识!'));
      } else if (serviceViewForm.hyperLinkType == 'custom') {
        callback(new Error('请填写自定义编码'));
      } else {
        callback(new Error('请填写链接地址!'));
      }
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkHyperLinkShow = (rule, value, callback) => {
  if (serviceViewForm.isHyperLink == '1') {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择链接展示方式'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkIsEncryption = (rule, value, callback) => {
  if (serviceViewForm.showType == 'password') {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择是否加密'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkIsViewPlaintext = (rule, value, callback) => {
  if (serviceViewForm.showType == 'password' && serviceViewForm.isEncryption == '1') {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择是否显示明文'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const checkEncryptionMethod = (rule, value, callback) => {
  if (serviceViewForm.showType == 'password' && serviceViewForm.isEncryption == '1') {
    if (value == undefined || value == null || value == "") {
      callback(new Error('请选择加密方式'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};



const treeForm = ref([]);

const encryptionMethodList = ref([]);
const activeName1 = ref('1');
const tableFilter = ref('');
const defaultValue1 = ref("");
const defaultValue2 = ref("");
const defaultRuleList = ref([]);
const queryCondition = ref("");
const windowboxFilterTable = ref([]);
const topoList = reactive([
  { value: "3", label: "网元" },
  { value: "2", label: "链路" },
  { value: "1", label: "端口" },
  { value: "0", label: "其他" }
]);
const toolTipTypeList = reactive([
  { value: "0", label: "关闭" },
  { value: "1", label: "当前值" },
  { value: "2", label: "规则生成" },
  { value: "3", label: "固定值" },
]);
const tooltipRuleList = ref([]);
const windowTreeDialog = ref(false);
const treeItem = reactive({});
const tableHeight = ref(295);
const selectMulDataModal = ref(false);
let mulCurrentItem = reactive({});
const currentMulItem = ref(null);
const currentMulId = ref(null);
const mulOcode = ref("");
const mulShow_type = ref("");
const mulData = ref("");
const totalMulW = ref(0);
const pageMulSizeW = ref(10);
const currentMulPageW = ref(1);
const windowMulSelect = ref([]);
const selectDataModal = ref(false);
const treeLoading = ref(false);
const propValue = ref('');
const windowboxloading = ref(false);
const currentLabel = ref("");
const mulColumns = reactive([
  { title: "选择", key: "value", width: 70, type: 'selection' },
  { title: '值', key: 'label' }
]);
const columns = reactive([
  {
    title: "选择",
    width: 70,
    key: "value",
    render: (h, params) => {
      let id = params.row.value;
      let defaultS = false;
      if (currentId.value == id) {
        defaultS = true;
      } else {
        defaultS = false;
      }
      return h('Radio', {
        props: {
          value: defaultS  //判断单选框状态
        },
        on: {
          'on-change': () => {
            currentId.value = id; //赋值单选框id。对比id判断状态
            currentLabel.value = params.row.label;
          }
        }
      });
    },
  }, { title: '值', key: 'label' }
]);
const currentPageW = ref(1);
const pageSizeW = ref(10);
const totalW = ref(0);
const currentItem = ref(null);
const windowSelect = ref(null);
let defaultValueObj = reactive({});
const propData = ref([]);
const ocode = ref('');
const currentId = ref('');
const data = ref([]);
const defaultValueShow = ref(false);
const defaultValueShowType = ref("input");
const defaultValueName = ref("");
const defaultValueList = ref([]);
const defaultValueForm = reactive({
  id: "",
  default_type: "1",
  default_value: "",
  defaultShowValue: ""
});

const cascodeList = ref([]);
const enumCode = ref("");
const editEnumModel = ref(false);
const rules = ref(false);
const modal1 = ref(false);
const propertyData = ref([]);
const show_type_list = ref([]);
const autoRulesList = ref([]);
const booleanList = reactive([
  { value: "1", label: "是" },
  { value: "0", label: "否" }
]);
const luru = ref(false);
const casList = ref([]);
const enumFormList = ref([{
  index: 0,
  id: '',
  label: '',
  value: ''
}]);
const isCascade = ref(false);
const disableflag = ref(false);
const editServiceModel = ref(false);
const serviceViewForm = reactive({
  id: "",
  name: "",
  property: "not_must",
  propertyUnit: "",
  enumStr: "",
  dataLength: "",
  isReEdit: "0",
  code: "",
  description: "",
  state: "",
  relationType: "",
  relationIds: "",
  dataType: "",
  relationStatus: "",
  isAuto: "0",
  cascode: [],
  casfun: "",
  isCascade: "",
  autoRules: "",
  ocode: "",
  titleDesc: "",
  showType: "",
  isRule: "0",
  ruleType: "",
  ruleValue: "",
  tooltipType: "",
  tooltipValue: "",
  isShowClear: "0",
  isHyperLink: "0",
  hyperLinkType: "",
  hyperLinkValue: "",
  hyperLinkParam: "",
  hyperLinkShow: "",
  isEncryption: "0",
  isViewPlaintext: "0",
  encryptionMethod: null
});
const lengthName = ref("数据长度");
const ruleValidate = reactive({
  code: [
    { required: true, trigger: "blur", validator: checkCode }
  ],
  name: [
    { required: true, trigger: "blur", validator: checkName }
  ],
  property: [
    { required: true, trigger: "change", validator: checkProperty }
  ],
  dataType: [
    { required: true, trigger: "change", validator: checkDataType }
  ],
  dataLength: [
    { required: true, trigger: "blur", validator: checkDataLength }
  ],
  showType: [
    { required: true, trigger: "change", validator: checkShowType }
  ],
  enumStr: [
    { required: true, trigger: "blur", validator: checkEnumStr }
  ],
  ocode: [
    { required: true, trigger: "blur", validator: checkOcode }
  ],
  autoRules: [
    { required: true, trigger: "blur", validator: checkAutoRules }
  ],
  isCascade: [
    { required: true, trigger: "change", validator: checkIsCascade }
  ],
  cascode: [
    { required: true, trigger: "change", validator: checkCascode }
  ],
  casfun: [
    { required: true, trigger: "change", validator: checkCasFun }
  ],
  isReEdit: [
    { required: true, message: "请选择是否可编辑", trigger: "change" }
  ],
  isRule: [
    { required: true, message: "请选择是否可编辑", trigger: "change" }
  ],
  ruleType: [
    { required: true, trigger: "change", validator: checkRuleType }
  ],
  ruleValue: [
    { required: true, trigger: "blur", validator: checkRuleValue }
  ],
  tooltipType: [
    { required: true, trigger: "change", message: "请选择悬浮生成方式" }
  ],
  tooltipValue: [
    { required: true, trigger: "blur", validator: checkTooltipValue }
  ],
  isHyperLink: [
    { required: true, trigger: "change", message: "请选择是否超链接" }
  ],
  hyperLinkType: [
    { required: true, trigger: "change", validator: checkHyperLinkType }
  ],
  hyperLinkValue: [
    { required: true, trigger: "blur", validator: checkHyperLinkValue }
  ],
  hyperLinkShow: [
    { required: true, trigger: "change", validator: checkHyperLinkShow }
  ],
  isEncryption: [
    { required: true, trigger: "change", validator: checkIsEncryption }
  ],
  isViewPlaintext: [
    { required: true, trigger: "change", validator: checkIsViewPlaintext }
  ],
  encryptionMethod: [
    { required: true, trigger: "change", validator: checkEncryptionMethod }
  ]
});
const editRuleDialog = ref(false);
const editRuleForm = reactive({
  ruleCodeSn: "",
  ruleCodeName: "",
});
const enumrow = ref(false);
const ruleCodeList = ref([]);
const filterText = ref("");
const searchText = ref("");
const isExpand = ref(false);
const isShow = ref(true);
const isEditCategoryRelation = ref(false);
const selectIds = ref([]);
const rule_pro = ref({});
const pageNum1 = ref(1);
const pageshow = ref(true);
const total1 = ref(0);
const pageSize1 = ref(10);

const pageNum2 = ref(1);
const pageshow2 = ref(true);
const total2 = ref(0);
const pageSize2 = ref(10);
const pcodeList = ref([]);
let addRelationList = reactive({
  name: "",
  isEdit: true,
  association_mode: "",
  categoryIds: [],
  sourceCategoryId: "",
  description: "",
  property_code: "",
  src_property_code: "",
  property: "",
});

const assDisabled = ref(false);
const propertyCodeActiveName = ref(false);
const instanceCodeActiveName = ref(false);
const detailsActiveName = ref(false);
const cateMultiple = ref(true);
const peopertyNameInput = ref("");
const RelationCategoryExceptList = ref([]);
const RelationPropertyList = ref([]);
const relationModeList = ref([]);
const relationTypeList = ref([]);
const relationCategoryList = ref([]);
const propertyList = ref([]);
const selectPropertyList = ref([]);
const categoryNode = ref([]);
const cascadeInfo = ref(false);
let categoryData = reactive({
  state: "enable",
  parentName: "",
  id: "",
  name: "",
  parentId: "",
  topoType: "0"
});
const codeList = ref([]);
const activeName = ref("first");
const ocodeInput = ref(false);
const data_length = ref(false);
const dateTypeDisable = ref(false);
const data_type_list = ref([]);
const tableData = ref([]);
const dialogFormVisible = ref(false);
const dialogFormVisible1 = ref(false);
const addPropertyDialogFormVisible = ref(false);
const categoryRelationdialogFormVisible = ref(false);

const form = reactive({
  name: "",
  region: "",
  date1: "",
  date2: "",
  delivery: false,
  type: [],
  resource: "",
  desc: "",
});

const assList = ref([
  { label: "属性关联", value: "property" },
  { label: "实例关联", value: "instance" },
]);

const relation = ref([
  { value: "fatherAndSon", label: "父子" },
  { value: "connection", label: "连接" },
  { value: "constitute", label: "构成" },
  { value: "other", label: "其他" },
]);

const radios = ref([
  { value: "enable", label: "启用" },
  { value: "disable", label: "禁用" },
]);

const valueCategory = ref([
  { value: "single", label: "关联单个实例" },
  { value: "multiple", label: "关联多个实例" },
]);

const rulesInfo = reactive({
  name: [
    {
      required: true,
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (value == "undefined" || value == null || value == "") {
          callback(new Error("请填写类别名称!!"));
        } else {
          categoryVerifyNameAxios(categoryData).then(res => {
            if (res.data.status != "success") {
              callback(new Error("类别名称已存在!!"));
            } else {
              callback();
            }
          });
        }
      },
    },
  ],
});

const ruleRelation = reactive({
  association_mode: [
    { required: true, trigger: "change", message: "请选择关联方式！" },
  ],
  property_code: [
    {
      required: true,
      trigger: "change",
      validator: (rule, value, callback) => {
        if (addRelationList.association_mode == "property") {
          if (value == "undefined" || value == null || value == "") {
            callback(new Error("请选择类别属性!"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
  src_property_code: [
    {
      required: true,
      trigger: "change",
      validator: (rule, value, callback) => {
        if (addRelationList.association_mode == "property") {
          if (value == "undefined" || value == null || value == "") {
            callback(new Error("请选择源属性!"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
  name: [
    {
      required: true,
      trigger: "blur",
      validator: (rule, value, callback) => {
        if (value == "undefined" || value == null || value == "") {
          callback(new Error("请填写关系名称!"));
        } else {
          categoryRelationVerifyNameAxios({
            code: addRelationList.code,
            name: addRelationList.name,
          }).then(response => {
            if (response.data.state != "success") {
              callback(new Error("关系名称已存在，请修改！"));
            } else {
              callback();
            }
          });
        }
      },
    },
  ],
  property: [{ required: true, trigger: "change", message: "请选择关系类型！" }],
  valueCategory: [
    {
      required: true,
      trigger: "change",
      validator: (rule, value, callback) => {
        if (addRelationList.association_mode != "property") {
          if (value == "undefined" || value == null || value == "") {
            callback(new Error("请填写资产关联!"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
    },
  ],
  categoryIds: [
    {
      required: true, trigger: "change", validator: (rule, value, callback) => {
        if (value == "undefined" || value == null || value == "") {
          callback(new Error("请填写资产关联!"));
        } else {
          callback();
        }
      }
    },
  ],
});

const scodeList = ref([]);
const formLabelWidth = ref("120px");
const defaultProps = reactive({ children: "children", label: "label" });
const ocodeList = ref([]);
const oldName = ref("");



const allowDrop = (draggingNode, dropNode, type) => {
  console.info(draggingNode, dropNode);
  if (draggingNode.data.parentId == dropNode.data.parentId) {
    if (type == 'prev' || type == 'next') {
      return true
    }
  } else {
    return false
  }
}

const nodeDrop = (draggingNode, dropNode, type, event) => {
  treeLoading.value = true;
  let json = {
    treeList: categoryNode.value
  }
  aveCategoryOrderAxios(json).then(res => {
    if (res.data == "success") {
    }
    nextTick(() => {
      treeLoading.value = false;
    });
  }).catch(err => {
    console.info(err);
  })
}

const initCombobox = () => {
  initComboboxMethod().then(response => {
    data_type_list.value = response['data'].dataTypeList;
    show_type_list.value = response['data'].showTypeList;
    propertyData.value = response['data'].propertyList;
    // this.categoryList = response['data'].categoryList;
    casList.value = response['data'].casList;
    cascodeList.value = response['data'].cascodeList;
    ocodeList.value = response['data'].ocodeList;
    autoRulesList.value = response['data'].autoRulesList;
    tooltipRuleList.value = response['data'].tooltipRuleList;
    defaultRuleList.value = response['data'].defaultRuleList;
    encryptionMethodList.value = response['data'].encryptionMethodList;
  });
}

const editService = (data) => {
  var current = this;
  initPropertyVerifyDelMethod(data.id).then(res => {
    if (res['data'].state != "ERROR") {
      editServiceModel.value = true;
      dateTypeDisable.value = false;
      serviceViewForm.id = data.id;
      serviceViewForm.code = data.code;
      serviceViewForm.name = data.name;

      oldName.value = data.name;
      serviceViewForm.state = data.state;
      serviceViewForm.titleDesc = data.titleDesc;
      for (var i = 0; i < data_type_list.value.length; i++) {
        if (data.data_type == data_type_list.value[i].label) {
          serviceViewForm.dataType =
            data_type_list.value[i].value;
          changeDataType(serviceViewForm.dataType);
        }
      }
      serviceViewForm.ocode = data.ocode;
      changeOcode(serviceViewForm.ocode);
      if (data.data_length != null && data.data_length != "") {
        serviceViewForm['dataLength'] = parseInt(data.data_length);
      } else {
        serviceViewForm.dataLength = null;
      }
      serviceViewForm.showType = data.show_type;
      changeShowType(serviceViewForm.showType);
      serviceViewForm.enumStr = data.enumStr;
      serviceViewForm.autoRules = data.autoRules;
      if (data.isAuto != null && data.isAuto == "1") {
        serviceViewForm.isAuto = "1";
        serviceViewForm.property = "not_must";
        luru.value = true;
        rules.value = true;
      } else {
        serviceViewForm.isAuto = "0";
        luru.value = false;
        rules.value = false;
        for (var i = 0; i < propertyData.value.length; i++) {
          if (data.property == propertyData.value[i].value) {
            serviceViewForm.property =
              propertyData.value[i].value;
          }
        }
      }

      serviceViewForm.propertyUnit = data.property_unit;
      if (data.is_re_edit == "是") {
        serviceViewForm.isReEdit = "1";
      } else {
        serviceViewForm.isReEdit = "0";
      }
      serviceViewForm.relationType = data.relation_type;
      serviceViewForm.relationIds = data.relation_ids;
      serviceViewForm.relationStatus = "{}";
      if (data.isCascade != null && data.isCascade == "1") {
        serviceViewForm.isCascade = "1";
        cascadeInfo.value = true;
      } else {
        serviceViewForm.isCascade = "0";
        cascadeInfo.value = false;
      }
      if (data.cascode != null && data.cascode != "") {
        serviceViewForm.cascode = data.cascode.split(",");
      }
      dateTypeDisable.value = true;
      serviceViewForm.casfun = data.casfun;
      serviceViewForm.isRule = data.isRule;
      serviceViewForm.ruleType = data.ruleType;
      serviceViewForm.ruleValue = data.ruleValue;
      serviceViewForm.tooltipType = data.tooltipType;
      serviceViewForm.tooltipValue = data.tooltipValue;
      serviceViewForm.isShowClear = data.isShowClear;
      serviceViewForm.isHyperLink = data.isHyperLink;
      serviceViewForm.hyperLinkType = data.hyperLinkType;
      serviceViewForm.hyperLinkValue = data.hyperLinkValue;
      serviceViewForm.hyperLinkParam = data.hyperLinkParam;
      serviceViewForm.hyperLinkShow = data.hyperLinkShow;
      serviceViewForm.isEncryption = data.isEncryption;
      serviceViewForm.isViewPlaintext = data.isViewPlaintext;
      serviceViewForm.encryptionMethod = data.encryptionMethod;
      disableflag.value = true;
    } else {
      ElMessageBox.confirm('已有录入该属性的资产实例，是否修改？', 'warning', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        editServiceModel.value = true;
        serviceViewForm.id = data.id;
        serviceViewForm.code = data.code;
        serviceViewForm.name = data.name;
        serviceViewForm.autoRules = data.autoRules;
        serviceViewForm.titleDesc = data.titleDesc;

        if (data.isAuto != null && data.isAuto == "1") {
          serviceViewForm.isAuto = "1";
          serviceViewForm.property = "not_must";
          luru.value = true;
          rules.value = true;
        } else {
          serviceViewForm.isAuto = "0";
          luru.value = false;
          rules.value = false;
          for (var i = 0; i < propertyData.value.length; i++) {
            if (data.property == propertyData.value[i].label) {
              serviceViewForm.property =
                propertyData.value[i].value;
            }
          }
        }

        oldName.value = data.name;
        serviceViewForm.state = data.state;
        for (var i = 0; i < data_type_list.value.length; i++) {
          if (data.data_type == data_type_list.value[i].label) {
            serviceViewForm.dataType =
              data_type_list.value[i].value;
            changeDataType(serviceViewForm.dataType);
          }
        }
        serviceViewForm.ocode = data.ocode;

        dateTypeDisable.value = true;
        if (data.data_length != null && data.data_length != "") {
          serviceViewForm.dataLength = parseInt(data.data_length);
        } else {
          serviceViewForm.dataLength = null;
        }
        changeOcode(serviceViewForm.ocode);
        serviceViewForm.showType = data.show_type;
        changeShowType(serviceViewForm.showType);
        serviceViewForm.enumStr = data.enumStr;
        serviceViewForm.propertyUnit = data.property_unit;
        if (data.is_re_edit == "是") {
          serviceViewForm.isReEdit = "1";
        } else {
          serviceViewForm.isReEdit = "0";
        }
        if (data.isCascade != null && data.isCascade == "1") {
          serviceViewForm.isCascade = "1";
          cascadeInfo.value = true;
        } else {
          serviceViewForm.isCascade = "0";
          cascadeInfo.value = false;
        }

        serviceViewForm.casfun = data.casfun;
        if (data.cascode != null && data.cascode != "") {
          serviceViewForm.cascode = data.cascode.split(",");
        }

        serviceViewForm.relationType = data.relation_type;
        serviceViewForm.relationIds = data.relation_ids;
      })
      serviceViewForm.isRule = data.isRule;
      serviceViewForm.ruleType = data.ruleType;
      serviceViewForm.ruleValue = data.ruleValue;
      serviceViewForm.tooltipType = data.tooltipType;
      serviceViewForm.tooltipValue = data.tooltipValue;
      serviceViewForm.isShowClear = data.isShowClear;
      serviceViewForm.isHyperLink = data.isHyperLink;
      serviceViewForm.hyperLinkType = data.hyperLinkType;
      serviceViewForm.hyperLinkValue = data.hyperLinkValue;
      serviceViewForm.hyperLinkParam = data.hyperLinkParam;
      serviceViewForm.hyperLinkShow = data.hyperLinkShow;
      serviceViewForm.isEncryption = data.isEncryption;
      serviceViewForm.isViewPlaintext = data.isViewPlaintext;
      serviceViewForm.encryptionMethod = data.encryptionMethod;
      disableflag.value = true;
    }
  });
}

const ok = () => {
  //alert(enumCode.value);
  if (enumCode.value == null || enumCode.value == "") {
    ElMessage.error("请选择生成规则");
    return;
  }
  const params = {};
  params['enumCode'] = enumCode.value;
  queryEnumListDe(params).then(res => {
    serviceViewForm.enumStr = JSON.stringify(res.data);
    addEnum();
    modal1.value = false;
  })
}

const resetLoad = () => {
  enumCode.value = "";
}
const closeLoad = () => {
  modal1.value = false;
}
const dataEnums = () => {
  modal1.value = true;
  queryEnumCodeList().then(res => {
    codeList.value = res['data'];
  })
}

const index = ref(0);
const saveEnum = (name) => {
  var list = enumFormList.value;
  for (var i = 0; i < list.length; i++) {
    if (list[i].label == "" || list[i].value == "" || list[i].id == undefined || list[i].id == "") {
      ElMessage.error('序号,编码和名称都不能为空');
      return;
    }
  }
  if (list.length > 1) {
    for (var i = 0; i < list.length - 1; i++) {
      var value = list[i].value;
      var id = list[i].id;
      for (var j = i + 1; j < list.length; j++) {
        if (list[j].value == value) {
          ElMessage.error('编码不能相同，第' + (i + 1) + "行和第" + (j + 1) + "行编码相同");
          return;
        }
        if (list[j].id == id) {
          ElMessage.error('序号不能相同，第' + (i + 1) + "行和第" + (j + 1) + "行序号相同");
          return;
        }
      }
    }
  }
  var results = [];
  for (var i = 0; i < list.length; i++) {
    const pp = {};
    pp['id'] = list[i].id;
    pp['label'] = list[i].label;
    pp['value'] = list[i].value;
    results.push(pp);
  }
  results.sort(function (a, b) {
    return a.id - b.id;
  });
  serviceViewForm.enumStr = JSON.stringify(results);
  index.value = 1;
  enumFormList.value.length = 1;
  editEnumModel.value = false;

}

const serviceViewFormRef = ref(null);
const saveService = (name) => {
  serviceViewFormRef.value.validate(function (valid) {
    if (serviceViewForm.showType != 'password') {
      serviceViewForm.isEncryption = '0';
      serviceViewForm.isViewPlaintext = '0';
      serviceViewForm.encryptionMethod = null;
    } else {
      if (serviceViewForm.isEncryption == '0') {
        serviceViewForm.isViewPlaintext = '0';
        serviceViewForm.encryptionMethod = null;
      }
    }
    const params = serviceViewForm;
    params['categoryId'] = categoryData.id;
    saveCategoryProperty(params).then(res => {
      if (res.data == "success") {
        ElMessage.success("保存成功");
        closeService("serviceViewForm");
        handleClick("first");
      } else {
        ElMessage.error("保存失败，" + res.data);
      }
    }).catch(exp => {
      ElMessage.error(exp);
    });
  })
}

const closeService = (params) => {
  editServiceModel.value = false;
  oldName.value = "";
  serviceViewForm.relationIds = "";
  serviceViewForm.relationStatus = "";
  serviceViewForm.relationType = "";
  if (params == 'serviceViewForm') {
    serviceViewFormRef.value.resetFields();
  }

}

const removeForm = (i) => {
  if (enumFormList.value.length === 1) {
    ElMessage.error('至少保留一个子维度信息');
    return;
  }
  enumFormList.value.splice(i, 1)
}
const addForm = () => {
  index.value = index.value + 1;
  enumFormList.value.push({
    index: index.value,
    id: '',
    label: '',
    value: ''
  })
}
const shengcheng = (value) => {
  if (value == "1") {
    rules.value = true;
    serviceViewForm.property = "not_must";
    luru.value = true;
  } else {
    rules.value = false;
    luru.value = false;
  }
}
const cascadeChange = (value) => {
  if (value == "1") {
    cascadeInfo.value = true;
  } else {
    cascadeInfo.value = false;
    serviceViewForm.cascode = [];
    serviceViewForm.casfun = "";
  }
}

const addEnum = () => {
  editEnumModel.value = true;
  enumFormList.value[0].label = "";
  enumFormList.value[0].value = "";
  enumFormList.value[0].id = "";
  index.value = 1;
  enumFormList.value.length = 1;
  if (serviceViewForm.enumStr != "") {
    var tt = eval("(" + serviceViewForm.enumStr + ")");
    enumFormList.value[0].label = tt[0].label;
    enumFormList.value[0].value = tt[0].value;
    enumFormList.value[0].id = tt[0].id;
    if (tt.length > 1) {
      for (var i = 1; i < tt.length; i++) {
        index.value = index.value + 1;
        enumFormList.value.push({
          index: index.value,
          id: tt[i].id,
          label: tt[i].label,
          value: tt[i].value
        })
      }
    } else {

    }
  }
}

const changeDataType = (value) => {
  if (value == 'string' || value == 'long' || value == 'date' || value == 'dateTime') {
  } else {
    serviceViewForm.isRule = "0";
  }
  if (value == "object") {
    ocodeInput.value = true;
  } else {
    ocodeInput.value = false;
  }
  if (value == "enum") {
    enumrow.value = true;
    data_length.value = false;
  } else {
    enumrow.value = false;
    if (value == "date" || value == "dateTime") {
      data_length.value = false;
    } else {
      data_length.value = true;

      if (value == "long") {
        lengthName.value = "最大值";
        serviceViewForm.dataLength = 5000;
      } else {
        serviceViewForm.dataLength = 200;
        lengthName.value = "数据长度";
      }
    }
    serviceViewForm.enumStr = "";
  }

  if (value == undefined || value == "") {
    show_type_list.value = "";
  } else {
    initShowTypeMethod(value).then(res => {
      var data = res['data'];
      if (typeof data == "string") {
        data = eval("(" + data + ")");
      }
      show_type_list.value = data;
      if (show_type_list.value.length > 0) {
        //serviceViewForm.showType = show_type_list.value[0].value;
        if (serviceViewForm.showType == null || serviceViewForm.showType == "") {
          serviceViewForm.showType = show_type_list.value[0].value;
          if (show_type_list.value[0].value == 'combobox' || show_type_list.value[0].value == 'windowbox') {
            isCascade.value = true;
          } else {
            isCascade.value = false;
          }
        } else {
          var flag = false;
          for (var t = 0; t < show_type_list.value.length; t++) {
            if (show_type_list.value[t].value == serviceViewForm.showType) {
              flag = true;
              if (show_type_list.value[t].value == 'combobox' || show_type_list.value[t].value == 'windowbox') {
                isCascade.value = true;
              } else {
                isCascade.value = false;
              }
              break;
            }
          }
          if (!flag) {
            serviceViewForm.showType = show_type_list.value[0].value;
            if (serviceViewForm.showType == 'combobox' || serviceViewForm.showType == 'windowbox') {
              isCascade.value = true;
            } else {
              isCascade.value = false;
            }
          }
        }
        changeShowType(serviceViewForm.showType);
      }
    });
  }
}


const changeOcode = (value) => {
  if (value == undefined || value == null || value == "") {
    return;
  }
  initShowTypeByOcodeMethod(value).then(res => {
    show_type_list.value = res['data'];
    if (show_type_list.value.length > 0) {
      serviceViewForm.showType = show_type_list.value[0].value;
      changeShowType(show_type_list.value[0].value);
    }
  });
}
const changeShowType = (value) => {
  serviceViewForm.showType = value;
  if (value == "combobox" || value == "windowbox" || value == 'comboTree') {
    isCascade.value = true;
    if (
      serviceViewForm.isCascade == null ||
      serviceViewForm.isCascade == ""
    ) {
      serviceViewForm.isCascade = "0";
      cascadeChange(serviceViewForm.isCascade);
    }
  } else {
    isCascade.value = false;
    serviceViewForm.isCascade = "0";
    cascadeChange(serviceViewForm.isCascade);
  }
}

const restName = () => {
  serviceViewForm.name = check1(
    serviceViewForm.name.toString()
  );
}
const check1 = (str) => {
  var temp = "";
  for (var i = 0; i < str.length; i++) {
    var _ilreg = /[\]!@#+=/'\"\-$%&^*(){}|,.?~` <>，。《》“：‘；？￥……（）！~·\\\\[:\;]/;
    if (!_ilreg.test(str.charAt(i))) {
      temp += str.charAt(i);
    }
  }
  return temp;
}
const restBianma = () => {
  serviceViewForm.code = check(
    serviceViewForm.code.toString()
  );
}
const check = (str) => {
  let temp = "";
  for (let i = 0; i < str.length; i++)
    if (str.charCodeAt(i) > 0 && str.charCodeAt(i) < 255) {
      let _ilreg = /[\]!@#+=\-/'\"$%&^*(){}|,.?~` <>\\\\[:\;]/;
      if (!_ilreg.test(str.charAt(i))) {
        temp += str.charAt(i);
      }
    }
  return temp;
}

const treeRefresh = () => {
  init();
  detailsActiveName.value = false;
}
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value.trim()) !== -1;
}
const ExpandFun = () => {
  isExpand.value = true;
  DynamicScaling();
}
const CloseFun = () => {
  isExpand.value = false;
  DynamicScaling();
}

const navtree = ref(null);
const DynamicScaling = () => {
  for (let j = 0; j < navtree.value.store._getAllNodes().length; j++) {
    navtree.value.store._getAllNodes()[j].expanded = isExpand.value;
  }
}
const resetFormFields = () => {
  serviceViewForm.id = "",
    serviceViewForm.name = "",
    serviceViewForm.property = "not_must",
    serviceViewForm.propertyUnit = "",
    serviceViewForm.enumStr = "",
    serviceViewForm.dataLength = "",
    serviceViewForm.isReEdit = "0",
    serviceViewForm.code = "",
    serviceViewForm.description = "",
    serviceViewForm.state = "",
    serviceViewForm.relationType = "",
    serviceViewForm.relationIds = "",
    serviceViewForm.dataType = "",
    serviceViewForm.relationStatus = "",
    serviceViewForm.isAuto = "0",
    serviceViewForm.cascode = [],
    serviceViewForm.casfun = "",
    serviceViewForm.isCascade = "",
    serviceViewForm.autoRules = "",
    serviceViewForm.ocode = "",
    serviceViewForm.titleDesc = "",
    serviceViewForm.showType = ""
}

const init = () => {
  categoryTree();
}

const categoryTree = () => {
  treeLoading.value = true;
  categoryTreeoAxios()
    .then(res => {
      nextTick(() => {
        categoryNode.value = res['data'];
        treeLoading.value = false;
      });
    })
    .catch(err => {
      console.info(err);
    });
}
const chickNode = (data, node, event) => {
  edit(data);
}
const handleNodeClick = (node) => {
  edit(node);
}
const append = (data) => {
  detailsActiveName.value = true;
  activeName.value = "first";
  if (data.id) {
    const json = {
      name: "",
      id: 0,
      parentId: data.id || null,
      parentName: data.label || null,
      state: "enable",
      topoType: "0"
    };

    // categoryData = reactive({ ...json });
    categoryData['parentId'] = data.id || null;
    categoryData['parentName'] = data.label || null;
    categoryData['name'] = "";
    categoryData['id'] = 0;
    categoryData['description'] = "";
    categoryData['state'] = "enable";
    categoryData['topoType'] = '0';

  } else {
    // categoryData = reactive({
    //   name: "",
    //   id: 0,
    //   parentId: null,
    //   parentName: null,
    //   state: "enable",
    //   topoType: "0"
    // });
    categoryData['parentId'] = data.id || null;
    categoryData['parentName'] = data.label || null;
    categoryData['name'] = "";
    categoryData['id'] = 0;
    categoryData['description'] = "";
    categoryData['state'] = "enable";
    categoryData['topoType'] = '0';
  }
  handleClick(activeName.value);
}
const edit = (data) => {
  detailsActiveName.value = true;
  activeName.value = "first";
  // categoryData = reactive({});
  categoryData['parentId'] = data.id || null;
  categoryData['parentName'] = data.label || null;
  categoryData['name'] = data.name || null;
    categoryData['id'] = data.id || null;
    categoryData['description'] = data.description || null;
    categoryData['state'] = data.state || null;
    categoryData['topoType'] = data.topoType || null;
  tableFilter.value = '';
  pageNum2.value = 1;
  pageSize2.value = 10;
  getCategoryByIdAxios(data.id)
    .then(res => {
      // categoryData = reactive({ ...res['data'] });
      console.log(res['data'])
      categoryData['parentId'] = res['data'].id || null;
      categoryData['parentName'] = res['data'].label || null;
      categoryData['name'] = res['data'].name || null;
      categoryData['description'] = res['data'].description || null;
      categoryData['state'] = res['data'].state || null;
      categoryData['topoType'] = res['data'].topoType || null;
      handleClick(activeName.value);
    })
    .catch(err => {
      console.info(err);
    });
}

const remove = (data) => {
  categoryVerifyDelAxios(data.id).then(res => {
    if (res['data'].state === "OK") {
      ElMessageBox.confirm(
        "删除类别时，该类别下的所有类别将同时被删除,确认删除吗？",
        "warning",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          deleteCategoryByIdAxios(data.id).then(res => {
            categoryTree();
            detailsActiveName.value = false;
            categoryNode.value = res['data'];
            ElMessage.success("删除成功");

          });
        })
        .catch(() => {
          ElMessage.info("已取消删除");
        });
    } else if (res['data'].state === "CONFIRM") {
      ElMessageBox.confirm(
        res['data'].message +
        "删除类别时，该类别下的所有类别将同时被删除！确认删除吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          deleteCategoryByIdAxios(data.id).then(res => {
            categoryTree();
            categoryNode.value = res['data'];
            ElMessage.success("删除成功");
          });
        })
        .catch(() => {
          ElMessage.info("已取消删除");
        });
    } else if (res['data'].state === "ERROR") {
      ElMessage.error("当前类别或子类别下已存在资产！");
    }
  });
}

const categoryDataRef = ref(null);
const save = (formName) => {
  console.log(categoryData);
  categoryDataRef.value.validate(valid => {
    if (valid) {
      saveCategoryAxios(categoryData).then(res => {
        console.log(res.data);
        // categoryData = reactive({ ...res.data });
        categoryData['parentId'] = res.data.id || null;
        categoryData['parentName'] = res.data.label || null;
        categoryData['name'] = res.data.name || null;
        categoryData['id'] = res.data.id || null;
        categoryData['description'] = res.data.description || null;
        categoryData['state'] = res.data.state || null;
        categoryData['topoType'] = res.data.topoType || null;

        ElMessage.success("保存成功");
        categoryTree();
        handleClick(activeName.value);
      });
    } else {
      return false;
    }
  });
}
const queryRelation = () => {
  let params = {
    categoryId: categoryData.id,
    pageNum: pageNum2.value || 1,
    pageSize: pageSize2.value || 10,
  };
  queryCategoryRelationByCategoryIdAxios(params)
    .then(res => {
      tableData.value = res.data.list;
      total2.value = res.data.total;
    })
    .catch(err => {
      console.info(err);
    });
}


const handleClick = (tab) => {
  tableData.value = [];
  let params = {
    categoryId: categoryData.id,
    pageNum: pageNum2.value || 1,
    pageSize: pageSize2.value || 10,
  };
  let name = tab.name || tab;

  if (name == "first") {
    params['tableFilter'] = tableFilter.value.trim();
    queryPropertiesByCategoryIdAxios(params)
      .then(res => {
        tableData.value = res.data.list;
        total2.value = res.data.total;
      })
      .catch(err => {
        console.info(err);
      });
  } else if (name == "second") {
    queryCategoryRelationByCategoryIdAxios(params)
      .then(res => {
        tableData.value = res.data.list;
        total2.value = res.data.total;
      })
      .catch(err => {
        console.info(err);
      });
  } else if (name == "third") {
    queryBeCategoryRelationByCategoryIdAxios(params)
      .then(res => {
        tableData.value = res.data.list;
        total2.value = res.data.total;
      })
      .catch(err => {
        console.info(err);
      });
  }
}

const toAddProperty = () => {
  selectIds.value = [];
  pageNum1.value = 1;
  pageSize1.value = 10;
  peopertyNameInput.value = "";
  if (categoryData.id === "" || categoryData.id == 0) {
    ElMessage.warning("请先保存类别信息");
  } else {
    propertyList.value = [];
    addPropertyDialogFormVisible.value = true;
    let params = {
      categoryId: categoryData.id,
      pageNum: pageNum1.value,
      pageSize: pageSize1.value,
    };
    queryNonPropertyByIdAxios(params)
      .then(res => {
        propertyList.value = res.data.list;
        total1.value = res.data.total;
      })
      .catch(err => {
        console.info(err);
      });
  }
}

const addRelationListRef = ref(null);
const toAddRelationList = () => {
  categoryRelationdialogFormVisible.value = true;
  addRelationListClear();
  pcodeList.value = [];
  changeCategoryRelationIsEdit();
  assDisabled.value = false;
  propertyCodeActiveName.value = false;
  instanceCodeActiveName.value = false;
  RelationPropertyList.value = [];
  RelationCategoryExceptList.value = [];
  if (addRelationListRef.value) {
    addRelationListRef.value.resetFields();
  }
  queryPropertyByIdAxios(categoryData.id)
    .then(res => {
      RelationPropertyList.value = res['data'].list;
    })
    .catch(err => {
      console.info(err);
    });
  categoryTreeoAxios().then(res => {
    RelationCategoryExceptList.value = res['data'];
  }).catch(err => {
    ElMessage.error(err);
  });
}

const queryPropertyByName = () => {
  propertyList.value = [];
  let params = {
    categoryId: categoryData.id,
    name: peopertyNameInput.value,
    pageNum: pageNum1.value,
    pageSize: pageSize1.value,
  };
  queryNonPropertyByIdAxios(params)
    .then(res => {
      propertyList.value = res.data.list;
      total1.value = res.data.total;
    })
    .catch(err => {
      console.info(err);
    });
}
const resetPropertyList = () => {
  propertyList.value = [];
  selectIds.value = [];
  peopertyNameInput.value = "";
  queryPropertyByName();
}
const initRelationList = () => {
  addRelationList.name = "";
  addRelationList.isEdit = true;
  addRelationList.association_mode = "";
  addRelationList.categoryIds = [];
  addRelationList.sourceCategoryId = "";
  addRelationList.description = "";
  addRelationList.property_code = "";
  addRelationList.src_property_code = "";
  addRelationList.property = "";
}


const resetRelationList = (formName) => {
  if (formName == 'addRelationList') {
    initRelationList();
  } else {
    addRelationListRef.value.resetFields();
  }
}
const closeRelation = (formName) => {
  if (formName == 'addRelationList') {
    initRelationList();
  } else {
    addRelationListRef.value.resetFields();
  }
  categoryRelationdialogFormVisible.value = false;
}
const changeMode = (value, valueCategory) => {
  scodeList.value = [];
  if (value == "instance" && valueCategory === "multiple") {
    cateMultiple.value = true;
    var tt = addRelationList.categoryIds;
    if (tt && tt != null && tt != '') {
      addRelationList.categoryIds = [];
      addRelationList.categoryIds.push(tt);
    } else {
      addRelationList.categoryIds = [];
    }
  } else if (value == "instance") {
    cateMultiple.value = false;
    if (Array.isArray(addRelationList.categoryIds)) {
      addRelationList.categoryIds = addRelationList.categoryIds[0];
    }
    if (addRelationList.association_mode == null || addRelationList.association_mode === "") {
      addRelationList.association_mode = "single";
    }
  } else {
    cateMultiple.value = false;
    queryPropertyByIdAxios(categoryData.id).then(res => {
      scodeList.value = res['data'];
      const pp = {};
      pp['value'] = "instance_id";
      pp['label'] = "实例ID";
      scodeList.value.push(pp);
    });
    if (value == "property") {
      if (Array.isArray(addRelationList.categoryIds)) {
        addRelationList.categoryIds = addRelationList.categoryIds[0];
      }
      if (addRelationList.categoryIds != null && addRelationList.categoryIds != '') {
        changeCategory(addRelationList.categoryIds);
      }
    }
  }
}

const changeValueCategory = (value) => {
  if (value === "single") {
    cateMultiple.value = false;
    if (addRelationList.categoryIds && addRelationList.categoryIds != null && addRelationList.categoryIds.length > 0) {
      addRelationList.categoryIds = addRelationList.categoryIds[0];
    }
  } else if (value === "multiple") {
    var tt = addRelationList.categoryIds;
    if (tt && tt != null && tt != '') {
      addRelationList.categoryIds = [];
      addRelationList.categoryIds.push(tt);
    } else {
      addRelationList.categoryIds = [];
    }
    cateMultiple.value = true;
  }
  nextTick(() => {
    addRelationListRef.value.clearValidate();
  });
}

const changeCategory = (value) => {
  pcodeList.value = [];
  if (addRelationList.association_mode == "property") {
    queryPropertyByIdAxios(value).then(res => {
      if (res['data']) {
        pcodeList.value = res['data'];
        const pp = {};
        pp['value'] = "instance_id";
        pp['label'] = "实例ID";
        pcodeList.value.push(pp);
      }
    });
  }
}
const tableReload = (pageSize, pageNum) => {
  queryPropertyByName();
}
const handleSizeChange = (val) => {
  pageSize1.value = val;
  tableReload(val, "");
}
const handleCurrentChange = (val) => {
  pageNum1.value = val;
  tableReload("", val);
}
const handleSizeChange2 = (val) => {
  pageSize2.value = val;
  tableReload2(val, "");
}
const handleCurrentChange2 = (val) => {
  pageNum2.value = val;
  tableReload2("", val);
}
const tableReload2 = (pageSize, pageNum) => {
  handleClick(activeName.value);
}


const saveProperties = () => {
  let param = {
    categoryId: categoryData.id,
    selectIds: selectIds.value,
  };
  savePropertyAxios(param).then(res => {
    ElMessage.success("添加属性成功!");
    queryPropertyByName();
    handleClick(activeName.value);
  });
}
const handleSelectionChange = (rows) => {
  selectIds.value = [];
  rows.forEach(row => {
    selectIds.value.push(row.id);
  });
}
const resetRelationCategory = (row) => {
  ElMessageBox.confirm("此操作将把本资产实例下属性全部还原更新, 是否继续?", "warning", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        categoryId: categoryData.id,
        propertyId: row.id,
      };
      deleteCrpUrlAxios(params).then(res => {
        ElMessage.success("复原成功!");
        handleClick(activeName.value);
      });
    })
}


const removeProperty = (row) => {
  ElMessageBox.confirm("此操作将永久删除该属性, 是否继续?", "warning", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let params = {
        categoryId: categoryData.id,
        propertyId: row.id,
      };
      deleteCategoryPropertyAxios(params).then(res => {
        ElMessage.success("删除成功!");
        handleClick(activeName.value);
      });
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}
const saveCategoryRelation = (formName) => {
  addRelationListRef.value.validate(valid => {
    if (valid) {
      addRelationList.sourceCategoryId = categoryData.id;
      if (typeof (addRelationList.categoryIds) == 'string'
        ||
        typeof (addRelationList.categoryIds) == 'number') {
        let tmp = addRelationList.categoryIds;
        addRelationList.categoryIds = [];
        addRelationList.categoryIds.push(tmp);
      }

      saveCategoryRelationAxios(addRelationList).then(res => {
        addRelationList['id'] = res.data.id;
        ElMessage.success("操作成功!");
        closeRelation('addRelationList');
        handleClick(activeName.value);
      });
    } else {
      return false;
    }
  });
}

const editRelation = (row) => {
  getCategoryRelationByIdAxios(row.code).then(res => {
    let association_mode = res['data'].association_mode;
    let valueCategory = res['data'].valueCategory;
    categoryRelationdialogFormVisible.value = true;
    if (
      association_mode === "property" ||
      (association_mode === "instance" && valueCategory === "single")
    ) {
      pcodeList.value = [];
      queryPropertyByIdAxios(res['data'].categoryIds[0]).then(res => {
        pcodeList.value = res['data'];
      });
    }
    changeMode(association_mode, valueCategory);
    toAddRelationList();
    nextTick(() => {
      addRelationListRef.value.resetFields();
      addRelationListValue(association_mode, res['data']);
    });
  });
}
const removeRelation = (row) => {
  ElMessageBox.confirm("此操作将永久删除该关系, 是否继续?", "warning", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteCategoryRelationByIdAxios(row.code).then(res => {
        handleClick(activeName.value);
        ElMessage.success("删除成功!");
      });
    })
    .catch(() => {
      ElMessage.success("已取消删除");
    });
}

const lookUpBeRelation = (row) => {
  getCategoryBeRelationByIdAxios(row.code).then(res => {
    let association_mode = res['data'].association_mode;
    let valueCategory = res['data'].valueCategory;
    if (
      association_mode === "property" ||
      (association_mode === "instance" && valueCategory === "single")
    ) {
      pcodeList.value = [];
      queryPropertyByIdAxios(res['data'].categoryIds[0]).then(res => {
        pcodeList.value = res['data'];
      });
    }
    changeMode(association_mode);
    toAddRelationList();
    changeCategoryRelationIsEdit1();
    nextTick(() => {
      addRelationListValue(association_mode, res['data']);
    });
  });
}
const changeCategoryRelationIsEdit = () => {
  isEditCategoryRelation.value = false;
  isShow.value = true;
}
const changeCategoryRelationIsEdit1 = () => {
  isEditCategoryRelation.value = true;
  isShow.value = false;
}



const addRelationListClear = () => {
  addRelationList = reactive({
    id: "",
    code: "",
    association_mode: "",
    name: "",
    property: "",
    valueCategory: "",
    src_property_code: "",
    categoryIds: null,
    isEdit: true,
    property_code: "",
    description: "",
  });
}
const addRelationListValue = (association_mode, data) => {
  addRelationList['id'] = data.id;
  if (data.code) {
    addRelationList['code'] = data.code;
  }
  addRelationList.association_mode = data.association_mode;
  addRelationList.name = data.name;
  addRelationList.property = data.property;
  addRelationList['valueCategory'] = data.valueCategory;
  addRelationList.src_property_code = data.src_property_code;
  if (
    association_mode === "property" ||
    (data.association_mode === "instance" &&
      data.valueCategory === "single")
  ) {
    addRelationList['categoryIds'] = "";
    (addRelationList.categoryIds = data.categoryIds[0]);
  } else {
    addRelationList.categoryIds = data.categoryIds;
  }
  addRelationList.isEdit = data.isEdit;
  addRelationList.description = data.description;
  addRelationList.property_code = data.property_code;
}

const ruleEdit = (row) => {
  const params = {};
  params['ruleCode'] = row.autoRules;
  params['category_id'] = categoryData.id;
  params['property_id'] = row.id;
  rule_pro.value = row;
  editRuleDialog.value = true;
  queryAutoRuleInfoAxios(params).then(res => {
    ruleCodeList.value = res.data.ruleCodeList;
    editRuleForm.ruleCodeSn = res.data.ruleCodeSn;
    editRuleForm.ruleCodeName = res.data.ruleCodeName;
  });
}
const closeRuleCode = () => {
  editRuleForm.ruleCodeSn = "";
  editRuleDialog.value = false;
}
const saveRuleCode = () => {
  const params = {};
  params['category_id'] = categoryData.id;
  params['property_id'] = rule_pro.value['id'];
  params['ruleCodeSn'] = editRuleForm.ruleCodeSn;
  params['ruleCode'] = rule_pro.value['autoRules'];
  saveEditRuleAxios(params)
    .then(res => {
      if (res.data == "OK") {
        ElMessage.success("保存成功");
        editRuleDialog.value = false;
      } else {
        ElMessage.error("保存失败，" + res.data);
      }
    })
    .catch(exp => {
      ElMessage.error(exp.message);
    });
}

const updateCatePro = (row) => {
  editService(row);
  // console.log(row);
}
const closeDefaultValue = () => {
  defaultValueList.value = [];
  defaultValueName.value = "";
  defaultValueForm.default_value = "";
  defaultValueForm.defaultShowValue = "";
  defaultValueForm.id = "";
  defaultValueShowType.value = "input";
}
const defaultTypeChange = (value) => {
  if (value == "1") {
    defaultValue2.value = defaultValueForm.default_value;
    defaultValueForm.default_value = defaultValue1.value;
  } else {
    defaultValue1.value = defaultValueForm.default_value;
    defaultValueForm.default_value = defaultValue2.value;
  }
}

const saveDefaultValue = () => {
  if (defaultValueForm.default_type == '1') {
    if (defaultValueShowType.value == 'windowboxFilter' || defaultValueShowType.value == 'windowboxTreeFilter' || defaultValueShowType.value == 'inputSelect'
      || defaultValueShowType.value == 'moreInput') {
      defaultValueForm.default_value = defaultValueForm.default_value.join(",");
    }
  }
  const params = defaultValueForm;
  params['categoryId'] = categoryData.id;
  saveDefaultGxhValueAxios(params).then(res => {
    if (res.data == 'success') {
      ElMessage.success("保存成功")
      defaultValueShow.value = false;
      handleClick("first");
    } else {
      ElMessage.error(res.data);
    }
  }).catch(exp => {
    ElMessage.error(exp.message);
  })
}

const cancelDefaultValue = () => {
  defaultValueShow.value = false;
}
const openSelect = (item) => {
  getObjectPage(item);
}
const clearSelect = (item) => {
  defaultValueForm.defaultShowValue = "";
  defaultValueForm.default_value = "";
  defaultValueObj['default_value'] = "";
}
const show_type = ref("");
const windowTable = ref(null);
const getObjectPage = (item) => {
  console.log(item);
  if (item.ocode) {
    windowboxloading.value = true;
    currentItem.value = item;
    currentItem.value.value = item.default_value;
    currentId.value = item.default_value;
    ocode.value = item.ocode;
    show_type.value = item.show_type;
    selectDataModal.value = true;
    data.value = item.enumStr.data;
    console.log(data.value);
    totalW.value = item.enumStr.total;
    windowboxloading.value = false;
    windowSelect.value = {};
    windowSelect.value.value = item.default_value;
    windowSelect.value.id = item.default_value;
    nextTick(() => {
      if (windowSelect.value.value != undefined && windowSelect.value.value != '') {
        for (var i = 0; i < data.value.length; i++) {
          if (data.value[i].value == windowSelect.value.value) {
            windowTable.value.toggleRowSelection(data.value[i], true);
          }
        }
      }
    })
  }
}

const resetValue = () => {
  propValue.value = "";
  currentId.value = "";
  currentLabel.value = "";
}
const resetMulSelect = () => {
  propValue.value = "";
}
const initWindowBox = (json) => {
  windowboxloading.value = true;
  json.show_type = defaultValueShowType.value;
  json.ocode = ocode.value;
  json.value = propValue.value;
  queryEnumList(json).then(res => {
    selectDataModal.value = true;
    data.value = res.data.data;
    totalW.value = res.data.total;
    windowboxloading.value = false;
  })

}
const saveMulSelect = () => { }
const mulSelect = reactive({});
const mulSelectModel = ref();
const closeMulSelect = () => {
  mulSelectModel.value = false;
  propValue.value = "";
  mulSelect['selectIds'] = [];
}
const queryValue = () => {
  var json = {
    pageSize: pageSizeW.value,
    pageNum: 1
  }
  initWindowBox(json);
}
const mulSelectTable = ref(null);
const getSelectProp = () => {//获取复选弹框的选中值
  var arrays = mulSelectTable.value.getAllPageSelected();
  var selectIds = new Array();
  if (arrays) {
    for (var i = 0; i < arrays.length; i++) {
      var itemArray = arrays[i];
      if (itemArray) {
        for (var j = 0; j < itemArray.length; j++) {
          selectIds.push(itemArray[j]);
        }
      }
    }
  }
  mulSelect['selectIds'] = selectIds;
}




let openMulSelect = (item) => {
  mulSelectModel.value = true;
  currentItem.value = item;
  mulSelectTable.value.load();
}
const object = reactive({});
const loadMulBefore = (param) => {
  param.show_type = currentItem.value.field.showType;
  param.ocode = currentItem.value.field.ocode;
  param.pageSize = object['size'];
  param.pageNum = object['page'];
  param.value = propValue.value;
}
const closePropSelect = () => {
  selectDataModal.value = false;
  propValue.value = '';
  data.value = [];
  windowSelect.value = null;
  //this.currentId = '';
  currentPageW.value = 1;
  totalW.value = 0;
  pageSizeW.value = 10;
}
const handleRowClick = (row, column, event) => {
  windowTable.value.toggleRowSelection(row);
  windowSelect.value = row;
}
const pageNumW = ref();
const handleCurrentChangeW = (val) => {
  const json = {
    pageNum: val,
    pageSize: pageSizeW.value
  }
  pageNumW.value = val;
  initWindowBox(json);
}
const handleSizeChangeW = (val) => {
  const json = {
    pageNum: 1,
    pageSize: val
  }
  pageSizeW.value = val;
  initWindowBox(json);
}


const saveSelect = () => {
  defaultValueForm.default_value = windowSelect.value.value;
  defaultValueForm.defaultShowValue = windowSelect.value.label;
  closePropSelect();
}
const addRuleValue = (value) => {
  if (value == 'like') {
    serviceViewForm.ruleValue += '.indexOf("")>-1';
  } else if (value == 'notLike') {
    serviceViewForm.ruleValue += '.indexOf("")==-1';
  } else {
    serviceViewForm.ruleValue += value;
  }
}
const addRuleRegValue = (value) => {
  if (value == 'email') {
    serviceViewForm.ruleValue = '^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$';
  } else if (value == 'phone') {
    serviceViewForm.ruleValue = '^((\\d{3,4}-)|\\d{3.4}-)?\\d{7,8}$';
  } else if (value == 'telephone') {
    serviceViewForm.ruleValue = '^(\\d{3,4}|\\d{3,4}-|s)?\\d{7,14}$';
  } else if (value == 'ipv4') {
    serviceViewForm.ruleValue = '^((?:(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d))))$';
  } else if (value == 'yuming') {
    serviceViewForm.ruleValue = '^http(s)?:\\/\\/(.*?)\\/$';
  } else if (value == 'shenfen') {
    serviceViewForm.ruleValue = '(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)';
  }
}


const setDefaultValue = (row) => {
  const params = {};
  params['id'] = row.id;
  params['categoryId'] = categoryData.id;
  queryPropertyGxhDefaultByIdAxios(params).then(res => {
    if (res.data.show_type == 'windowboxFilter') {
      windowboxFilterTable.value = res.data.enumStr;
    }
    if (res.data.show_type == 'windowboxTreeFilter') {
      windowboxFilterTable.value = res.data.enumStr[0].enumList;
    }
    defaultValueForm.default_value = res.data.default_value;
    nextTick(() => {
      windowboxFilterTable.value = [];
    })
    defaultValueForm.id = res.data.id;
    defaultValueForm.defaultShowValue = res.data.defaultShowValue;
    defaultValueShowType.value = res.data.show_type;
    defaultValueName.value = res.data.name;
    defaultValueList.value = res.data.enumStr;
    defaultValueObj = reactive({
      ...res.data
    });
    nextTick(() => {
      defaultValueForm.default_type = res.data.default_type;
      if (defaultValueForm.default_type == '1') {
        defaultValue1.value = defaultValueForm.default_value;
      } else {
        defaultValue2.value = defaultValueForm.default_value;
      }
    })
  }).catch(exp => {

  })
  defaultValueShow.value = true;
  defaultValueShowType.value = row.show_type;
  defaultValueName.value = row.name;
}

openMulSelect = (item) => {
  getMulObjectPage(item);
}
const windowMulTable = ref(null);
const getMulObjectPage = (item) => {
  windowboxloading.value = true;
  //current.windowSelect = item.filed;
  mulCurrentItem = reactive({});
  mulCurrentItem['value'] = "";
  mulCurrentItem['label'] = "";
  if (defaultValueForm.default_value != undefined && defaultValueForm.default_value != null) {
    mulCurrentItem['value'] = JSON.parse(JSON.stringify(defaultValueForm.default_value));
  }
  if (defaultValueForm.defaultShowValue != undefined && defaultValueForm.defaultShowValue != null) {
    mulCurrentItem['label'] = JSON.parse(JSON.stringify(defaultValueForm.defaultShowValue));
  }
  currentMulItem.value = item;
  currentMulId.value = item.default_value;
  mulOcode.value = item.ocode;
  mulShow_type.value = item.show_type;
  var params = {
    "show_type": "windowbox",
    "ocode": item.ocode,
    "pageSize": pageMulSizeW.value,
    "pageNum": currentMulPageW.value,
    "value": propValue.value
  };
  queryEnumList(params).then(res => {
    selectMulDataModal.value = true;
    mulData.value = res.data.data;
    //current.object.total = res.data.totayarnl;
    totalMulW.value = res.data.total;
    windowboxloading.value = false;
    let windowMulSelects = [];
    let ttValue = null;
    let ttLabel = null;
    if (mulCurrentItem['value'] != null) {
      if ((typeof mulCurrentItem['value']) == 'string') {
        ttValue = mulCurrentItem['value'].split(",");
      } else {
        ttValue = mulCurrentItem['value'];
      }
      ttLabel = mulCurrentItem['label'].split(",");
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        const cc = {};
        cc['value'] = ttValue[i];
        cc['label'] = ttLabel[i];
        windowMulSelects.push(cc);
      }
    }
    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (var k = 0; k < windowMulSelects.length; k++) {
          for (var i = 0; i < mulData.value.length; i++) {
            if (mulData.value[i]['value'] == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(mulData.value[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        mulCurrentItem['value'] = ttValue.join(",");
        mulCurrentItem['label'] = ttLabel.join(",");
      }
    })
  })

}

const mulTotalW = ref("");
const initMulWindowBox = (json) => {
  windowboxloading.value = true;
  if (mulShow_type.value == 'windowboxFilter') {
    json.show_type = 'windowbox';
  } else {
    json.show_type = mulShow_type.value;
  }
  json.ocode = mulOcode.value;
  json.value = propValue.value;
  queryEnumList(json).then(res => {
    selectMulDataModal.value = true;
    mulData.value = res.data.data;
    //current.object.total = res.data.total;
    mulTotalW.value = res.data.total;
    windowboxloading.value = false;
    let item = mulCurrentItem;
    let windowMulSelects = [];
    let ttValue = null;
    let ttLabel = null;
    if (item['value'] != null) {
      if ((typeof item['value']) == 'string') {
        ttValue = item['value'].split(",");
      } else {
        ttValue = item['value'];
      }
      ttLabel = item['label'].split(",");
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        const cc = {};
        cc['value'] = ttValue[i];
        cc['label'] = ttLabel[i];
        windowMulSelects.push(cc);
      }
    }

    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (var k = 0; k < windowMulSelects.length; k++) {
          for (var i = 0; i < mulData.value.length; i++) {
            if (mulData.value[i]['value'] == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(mulData.value[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        if (defaultValueShowType.value == 'mul_windowbox') {
          mulCurrentItem['value'] = ttValue.join(",");
          mulCurrentItem['label'] = ttLabel.join(",");
        } else {
          mulCurrentItem['value'] = ttValue;
          mulCurrentItem['label'] = ttLabel;
        }
      }
    })
  })
}


const queryMulValue = () => {
  var json = {
    pageSize: pageMulSizeW.value,
    pageNum: 1
  }
  initMulWindowBox(json);
}
const handleCurrentMulChangeW = (val) => {
  const json = {
    pageNum: val,
    pageSize: pageMulSizeW.value
  }
  pageNumW.value = val;
  initMulWindowBox(json);
}
const handleSizeMulChangeW = (val) => {
  const json = {
    pageNum: 1,
    pageSize: val
  }
  pageMulSizeW.value = val;
  initMulWindowBox(json);
}
const closePropSelectMul = () => {
  selectMulDataModal.value = false;
  propValue.value = '';
  mulData['value'] = [];
  windowMulSelect.value = null;
  currentMulPageW.value = 1;
  totalMulW.value = 0;
  pageMulSizeW.value = 10;
}

const saveSelectMul = () => {
  let ttValue = "";
  let ttLabel = "";
  if (mulCurrentItem != null) {
    if ((typeof mulCurrentItem['value']) == 'string') {
      ttValue = mulCurrentItem['value'];
    } else {
      ttValue = mulCurrentItem['value'].join(",");
    }
    if ((typeof mulCurrentItem['label']) == 'string') {
      ttLabel = mulCurrentItem['label'];
    } else {
      ttLabel = mulCurrentItem['label'].join(",");
    }
  }
  if (windowMulSelect.value != null && windowMulSelect.value.length > 0) {
    if (ttValue != "") {
      ttValue += ",";
      ttLabel += ",";
    }
    for (var i = 0; i < windowMulSelect.value.length; i++) {
      if (i == windowMulSelect.value.length - 1) {
        ttValue += windowMulSelect.value[i].value;
        ttLabel += windowMulSelect.value[i].label;
      } else {
        ttValue += windowMulSelect.value[i].value + ",";
        ttLabel += windowMulSelect.value[i].label + ",";
      }
    }
  }

  if (defaultValueShowType.value == "windowbox") {
    defaultValueForm.default_value = ttValue;
  } else if (defaultValueShowType.value == "windowboxFilter") {
    windowboxFilterTable.value =
      defaultValueObj['enumStr'];
    defaultValueForm['default_value'] = ttValue.split(",");
    nextTick(() => {
      windowboxFilterTable.value = [];
    });
  }
  defaultValueForm.defaultShowValue = ttLabel;

  closePropSelectMul();
}


const handleSelectionMulChange = (val, row) => {
  windowMulSelect.value = val;
}
const clearMulSelect = (item) => {
  defaultValueForm.default_value = "";
  defaultValueForm.defaultShowValue = "";
  defaultValueShow.value = "";
  defaultValueName.value = "";
  currentMulItem.value = null;
}
const openTreeSelect = (defaultValueObj) => {
  treeItem['field'] = {};
  treeItem['field'].value = defaultValueForm.default_value;
  treeItem['field'].showValue = defaultValueForm.defaultShowValue;
  treeItem['field'].enumArray = defaultValueObj.enumStr;
  windowTreeDialog.value = true;
}
const clearTreeSelect = (defaultValueObj) => {
  defaultValueForm.default_value = "";
  defaultValueForm.defaultShowValue = "";
}
const closeWindowTree = (item) => {
  windowTreeDialog.value = false;
  defaultValueForm.default_value = item.field.value;
  defaultValueForm.defaultShowValue = item.field.showValue;
}
const changeTooltipType = (value) => {
  serviceViewForm.tooltipValue = "";
}
const openFilterSelect = (item) => {
  openFulterMulSelect(item);
}

const openFulterMulSelect = (item) => {
  windowboxloading.value = true;
  mulCurrentItem = reactive({});
  mulCurrentItem['value'] = "";
  mulCurrentItem['label'] = "";
  if (defaultValueForm.default_value != undefined && defaultValueForm.default_value != null) {
    mulCurrentItem['value'] = JSON.parse(JSON.stringify(defaultValueForm.default_value));
    if (defaultValueForm.default_value == '' || defaultValueForm.default_value.length == 0) {
      mulCurrentItem['label'] = "";
    } else {
      mulCurrentItem['label'] = JSON.parse(JSON.stringify(defaultValueForm.default_value));
    }
  }
  currentMulItem.value = item;
  currentMulId.value = item.default_value;
  mulOcode.value = item.ocode;
  mulShow_type.value = item.show_type;
  var params = {
    "show_type": "windowbox",
    "ocode": item.ocode,
    "pageSize": pageMulSizeW.value,
    "pageNum": currentMulPageW.value,
    "value": propValue.value
  };
  queryEnumList(params).then(res => {
    selectMulDataModal.value = true;
    mulData.value = res.data.data;
    //current.object.total = res.data.totayarnl;
    totalMulW.value = res.data.total;
    windowboxloading.value = false;
    let windowMulSelects = [];
    let ttValue = null;
    let ttLabel = null;
    if (mulCurrentItem['value'] != null) {
      if ((typeof mulCurrentItem['value']) == 'string') {
        ttValue = mulCurrentItem['value'].split(",");
      } else {
        ttValue = mulCurrentItem['value'];
      }
      ttLabel = mulCurrentItem['label'];
    }
    if (ttValue != null) {
      for (let i = 0; i < ttValue.length; i++) {
        const cc = {};
        cc['value'] = ttValue[i];
        cc['label'] = ttLabel[i];
        windowMulSelects.push(cc);
      }
    }
    nextTick(() => {
      if (windowMulSelects.length > 0) {
        for (let k = 0; k < windowMulSelects.length; k++) {
          for (let i = 0; i < mulData.value.length; i++) {
            if (mulData.value[i]['value'] == windowMulSelects[k].value) {
              windowMulTable.value.toggleRowSelection(mulData.value[i], true);
              ttValue.splice(ttValue.indexOf(windowMulSelects[k].value), 1);
              ttLabel.splice(ttLabel.indexOf(windowMulSelects[k].label), 1);
            }
          }
        }
        mulCurrentItem['value'] = ttValue;
        mulCurrentItem['label'] = ttLabel;
      }
    })
  })
}


const clearTableList = (val, item) => {
  let query = "";
  if (queryCondition.value != undefined && queryCondition.value != "") {
    query = queryCondition.value;
  }
  windowboxFilterTable.value = [];
  remoteMethod(query, item);
}
const remoteMethod = (query, item) => {
  queryCondition.value = query;
  if (query !== "") {
    windowboxFilterTable.value = item.enumStr.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    windowboxFilterTable.value = [];
  }
}
const clearTreeTableList = (event, item) => {
  var query = "";
  if (queryCondition.value != undefined && queryCondition.value != "") {
    query = queryCondition.value;
  }
  windowboxFilterTable.value = [];
  remoteTreeMethod(query, item);
}
const remoteTreeMethod = (query, item) => {
  queryCondition.value = query;
  if (query !== "") {
    windowboxFilterTable.value = item.enumStr[0].enumList.filter(item1 => {
      return item1.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
    });
  } else {
    windowboxFilterTable.value = [];
  }
}


const clearWindowTree = (item) => {
  item.default_value = [];
  windowboxFilterTable.value = [];
  defaultValueForm['default_value'] = [];
  windowTreeDialog.value = true;
  windowTreeDialog.value = false;
}
const openWindowTree = (defaultValueObj) => {
  treeItem['field'] = {};
  treeItem['field'].value = defaultValueForm.default_value;
  treeItem['field'].showValue = defaultValueForm.defaultShowValue;
  treeItem['field'].enumArray = defaultValueObj.enumStr;
  windowboxFilterTable.value = defaultValueObj.enumStr[0].enumList;
  windowTreeDialog.value = true;
}
const addHyperLinkParam = ref();
const hyperLinkFormList = ref([]);
const hyperIndex = ref();
const addHyperLinkParamMethod = () => {
  addHyperLinkParam.value = true;
  hyperLinkFormList.value[0].label = "";
  hyperLinkFormList.value[0].value = "";
  hyperIndex.value = 1;
  hyperLinkFormList.value.length = 1;
  if (serviceViewForm.hyperLinkParam != "") {
    var tt = eval("(" + serviceViewForm.hyperLinkParam + ")");
    hyperLinkFormList.value[0].label = tt[0].label;
    hyperLinkFormList.value[0].value = tt[0].value;
    if (tt.length > 1) {
      for (var i = 1; i < tt.length; i++) {
        hyperIndex.value = hyperIndex.value + 1;
        hyperLinkFormList.value.push({
          index: hyperIndex.value,
          label: tt[i].label,
          value: tt[i].value
        })
      }
    } else {

    }
  }
}

const removeHyperLinkForm = (i) => {
  hyperLinkFormList.value.splice(i, 1);
}
const addHyperLinkForm = () => {
  if (hyperIndex.value == undefined || hyperIndex.value == '') {
    hyperIndex.value = 0;
  }
  hyperIndex.value++;
  hyperLinkFormList.value.push({
    index: index.value,
    label: '',
    value: ''
  })
}
const saveHyperLinkForm = () => {
  var list = hyperLinkFormList.value;
  if (list.length == 0) {
    serviceViewForm.hyperLinkParam = "";
    hyperIndex.value = 0;
    hyperLinkFormList.value.length = 0;
    addHyperLinkParam.value = false;
    return;
  }
  for (var i = 0; i < list.length; i++) {
    if (list[i].label == "" || list[i].value == "") {
      this1.$message.error('编码和名称都不能为空');
      return;
    }
  }
  if (list.length > 1) {
    for (var i = 0; i < list.length - 1; i++) {
      var value = list[i].value;
      for (var j = i + 1; j < list.length; j++) {
        if (list[j].value == value) {
          this1.$message.error('编码不能相同，第' + (i + 1) + "行和第" + (j + 1) + "行编码相同");
          return;
        }
      }
    }
  }
  var results = [];
  for (var i = 0; i < list.length; i++) {
    var pp = {};
    pp.label = list[i].label;
    pp.value = list[i].value;
    results.push(pp);
  }
  this1.serviceViewForm.hyperLinkParam = JSON.stringify(results);
  //this1.$refs.serviceViewForm.validateField('hyperLinkParam');
  this1.hyperIndex = 1;
  this1.hyperLinkFormList.value.length = 1;
  this1.addHyperLinkParam = false;
}
const queryFilterTable = () => {
  pageNum2.value = 1;
  handleClick(activeName.value);
}
const changeCollapse = (value, flag) => {
  if (value == '1') {
    tableHeight.value = 295;
  } else {
    tableHeight.value = 495;
  }
}


init();
initCombobox();


watch(filterText, newval => {
  // navtree.value.filter(newval);
}, { immediate: true })



// computed: {
//     ...mapGetters({
//   frameStyle: "frameStyle",
//   echartsName: "echartsName",
// }),
//     rightBodyHeight() {
//     return `calc(100vh - ${this.frameStyle.headerHeight}px - 25px)`;
//   },
//   prooertyBodyHeight() {
//     return `calc(100vh - ${this.frameStyle.headerHeight}px - 435px )`;
//   },
//   tableBodyHeight() {
//     return `calc(100vh - ${this.frameStyle.headerHeight}px - 556px )`;
//   }
// }


</script>

<style lang="scss" scoped>
.assetsUnknown {
  .el-main {
    padding: 0;
  }

  .el-header {}

  .el-header {
    padding: 0;
  }

  .el-form-item {
    margin-bottom: 2px;
  }

  .el-aside {
    color: #333;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }

  .header {
    padding: 8px;
    text-align: right;
  }

  .custom-theme-default .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: #1ca5d4;
  }

  .el-row {
    margin-bottom: 20px;
  }

  .card {
    overflow: auto;
  }

  :deep(.custom-tree-node>span:first-child) {
    width: calc(100% - 190px);
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    overflow: hidden;
    vertical-align: top;
  }

  :deep(.custom-tree-node) {
    width: calc(100% - 21px);
    display: inline-block;
    line-height: 28px;
  }

  :deep(.custom-tree-node) .el-button-group {
    width: 130px;
    display: inline-block;
    float: right;
    text-align: right;
  }

  :deep(.el-tree-node__expand-icon) {
    padding: 6px;
    position: absolute;
    left: 130px;
  }

  :deep(.el-breadcrumb__separator) {
    color: #e8ebed;
  }
}
</style>
