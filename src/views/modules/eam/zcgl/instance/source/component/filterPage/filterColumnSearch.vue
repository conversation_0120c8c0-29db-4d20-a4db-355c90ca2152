<template>
  <div style="padding:10px;">
    <div>
      <div style="border-radius: 4px;">
        <el-row>
          <el-col :span="12" style="text-align:center">
            <el-button style="width:100%;" @click="sortData('ascending')">升序</el-button>
          </el-col>
          <el-col :span="12" style="text-align:center">
            <el-button style="width:100%" @click="sortData('descending')">降序</el-button>
          </el-col>
        </el-row>
      </div>
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterFilterText" clearable style="margin-top:5px;margin-bottom:5px;">
      </el-input>
      <div style="border-radius:4px; box-shadow:inset 0 0 1px 1px #dcdfe6;padding:2px;">
          <div style="border-bottom:1px solid #dcdfe6;padding-left:20px;padding-top:5px;padding-bottom:5px;">
            <el-checkbox :indeterminate="isIndeterminate" v-model="allChecked" @change="checkAllChange">全部（{{selectFilterCount}}）</el-checkbox>
            <el-button style="border-left:1px solid #dcdfe6;color:#666;padding-left:10px;" type="primary" link @click="checkNoSelect">反选</el-button>
            <el-button type="primary" link style="color:#666;margin-left:50px;" @click="exportColumnCount" v-loading="exportLoading">导出计数</el-button>
          </div>
          <el-tree
            style="height:208px;overflow:auto;"
            filterable
            ref="filterTreeRef"
            class="filter-tree"
            v-loading="filterTreeLoading"
            :toolOptions="['expand']"
            :default-expanded-keys="[0]"
            :data="filterColumnData"
            highlight-current
            :props="treeProp"
            node-key="value"
            show-checkbox
            :default-checked-keys="defaultCheckedKeys"
            :expand-on-click-node="false"
            @check="changeFilterCheckd"
            >
            <template #-="{data, node}">
              <div v-show="data.isShow" style="width:210px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" >
                <span v-if="data.code.indexOf('__tree_url')>=0&&data.label.indexOf('／')>=0" :title="data.label">
                  {{data.label.split('／')[data.label.split('／').length-1]}}
                </span>
                <span v-else :title="data.label">{{data.label}}</span>
              </div>
            </template>
          </el-tree>
      </div>
      <el-row style="padding-top:15px;padding-bottom:5px;">
        <el-button type="primary" link style="color:#666;margin-left:20px;" icon="el-icon-refresh-right" @click="refreshSelect">重置</el-button>
        <el-button @click="closePopover" style="margin-left:80px;">取消</el-button>
        <el-button type="primary" @click="saveFilterQuery" style="margin-left:10px;">确定</el-button>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
  import {queryColumnListByFilterAxios,exportExcelTempAxios} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
  import {reactive, watch, ref, toRefs, nextTick, onMounted} from "vue";
  const props = defineProps({
    categoryId:[String,Number],
    column:Object,
    colorMap:Object,
    isShow:Boolean,
    queryCondition:Object,
    propertyForm:Object,
    querySwithForm:Object,
    organizationSelectValue:String,
    queryIsShow:Boolean,
    visiable:Boolean
  });

  const state = reactive({
    isIndeterminate:true,
    filterTreeLoading:false,
    filterFilterText:'',
    filterColumnData:[],
    totalData:[],
    allChecked:false,
    selectFilterCount:0,
    selectProperty:'',
    allSelectKeys:[],
    defaultCheckedKeys:[],
    sort:"desc",
    exportLoading:false,
    treeProp:{'label':'label','children':'children'}
  })
  const {filterFilterText,isIndeterminate,treeProp,allChecked,selectFilterCount,exportLoading,filterTreeLoading,filterColumnData,defaultCheckedKeys} = toRefs(state);
  const filterTreeRef = ref();
  watch(filterFilterText,(val)=>{
    // if(val){
    //   for(let i=0;i<state.filterColumnData.length;i++){
    //     if(state.filterColumnData[i].value.indexOf(val)>=0){
    //       state.filterColumnData[i].isShow = true;
    //     }else{
    //       state.filterColumnData[i].isShow = false;
    //     }
    //   }
      state.filterColumnData = state.totalData.filter(item=>{
        return item.value.indexOf(val) >= 0;
      })
      // filterTreeRef.value.setCheckedKeys(state.allSelectKeys);
    // }else{
    //   state.filterColumnData=state.totalData;
    //   filterTreeRef.value.setCheckedKeys(state.allSelectKeys);
    // }
    try {
      state.selectFilterCount =  state.filterColumnData.length;
      if(state.filterColumnData.length==state.allSelectKeys.length){
        state.allChecked = true;
        state.isIndeterminate = false;
      }else if(state.allSelectKeys.length==0){
        state.allChecked = false;
        state.isIndeterminate = false;
      }else{
        state.allChecked = false;
        state.isIndeterminate = true;
      }
    } catch (e) {
      console.trace(e);
    }
  })
  const refreshSelect = () =>{
    state.filterFilterText = "";
    state.isIndeterminate = false;
    state.allChecked=false;
    filterTreeRef.value.setCheckedKeys([]);
  }
  const emit = defineEmits(["closePopover","queryInstanceTable","changeSort"])
  const closePopover = () =>{
    emit('closePopover',props.column);
  }
  const saveFilterQuery = () =>{
    let tt = filterTreeRef.value.getCheckedKeys();
    if(tt&&tt.length>0){
      props.colorMap[props.column.prop] = tt;
    }else{
      props.colorMap[props.column.prop] = undefined;
    }
    closePopover();
    let mm = {};
    mm["categoryId"] = props.categoryId;
    mm["isAuth"] = true;
    emit('queryInstanceTable',mm);
  }
  const sortData = (value) =>{
    let tt = {};
    tt["column"] = props.column;
    if(tt["column"]=='category'){
      tt["column"] = 'category_id';
    }
    tt["order"] = value;
    closePopover();
    emit('changeSort',tt);
  }
  const checkAllChange = (value) =>{
    state.isIndeterminate = false;
    if(value){
      let tt = [];
      for(let i=0;i<state.filterColumnData.length;i++){
        if(state.filterColumnData[i].isShow&&state.allSelectKeys.indexOf(state.filterColumnData[i].value)<0){
          tt.push(state.filterColumnData[i].value);
        }
      }
      filterTreeRef.value.setCheckedKeys(tt);
      state.allSelectKeys.push(...tt);
      state.allChecked=true;
    }else{
      state.allChecked=false;
      let tt = [];
      for(let i=0;i<state.filterColumnData.length;i++){
        if(state.filterColumnData[i].isShow&&state.allSelectKeys.indexOf(state.filterColumnData[i].value)>=0){
          tt.push(state.filterColumnData[i].value);
        }
      }
      if(tt.length>0){
        state.allSelectKeys = state.allSelectKeys.filter(item=>{
          return !tt.includes(item);
        })
      }
      filterTreeRef.value.setCheckedKeys(state.allSelectKeys);
    }
  }
  const checkNoSelect = () =>{
    filterTreeRef.value.setCheckedKeys([]);
    let tt = [];
    let removett=[];
    for(let i =0;i<state.filterColumnData.length;i++){
      if(state.filterColumnData[i].isShow){
        if(state.allSelectKeys.indexOf(state.filterColumnData[i].value)<0){
          tt.push(state.filterColumnData[i].value);
        }else{
          removett.push(state.filterColumnData[i].value);
        }
      }
    }
    state.allSelectKeys.push(...tt);
    state.allSelectKeys = state.allSelectKeys.filter(item=>{
      return !removett.includes(item);
    })
    filterTreeRef.value.setCheckedKeys(tt);

  }
  const changeFilterCheckd = () =>{
    console.log(filterTreeRef.value.getCheckedKeys());
    state.allSelectKeys = filterTreeRef.value.getCheckedKeys();
    if(state.allSelectKeys.length == state.totalData.length){
      state.allChecked = true;
      state.isIndeterminate = false;
    }else if(state.allSelectKeys.length == 0){
      state.allChecked = false;
      state.isIndeterminate = false;
    }else{
      state.allChecked = false;
      state.isIndeterminate = true;
    }
  }
  const subnet_mask_change_ip_segment = (ip_str, mask) => {
    let mark_len = 32;
    mark_len = mask;
    let nextBit = Math.min(mark_len, 8);
    let ips = ip_str.split(".");
    let maskIp = {};
    maskIp.a = ((1 << nextBit) - 1) << (8 - nextBit);
    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.b = ((1 << nextBit) - 1) << (8 - nextBit);

    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.c = ((1 << nextBit) - 1) << (8 - nextBit);

    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.d = ((1 << nextBit) - 1) << (8 - nextBit);
    // 开始IP各个位置的值
    let a = ips[0] & maskIp.a;
    let b = ips[1] & maskIp.b;
    let c = ips[2] & maskIp.c;
    let d = ips[3] & maskIp.d;

    // 开始IP
    let startIp = a + "." + b + "." + c + "." + d;
    // 结束IP各个位置的值
    a = (maskIp.a ^ 255) | ips[0];
    b = (maskIp.b ^ 255) | ips[1];
    c = (maskIp.c ^ 255) | ips[2];
    d = (maskIp.d ^ 255) | ips[3];

    // 结束IP
    let endIp = a + "." + b + "." + c + "." + d;
    return iptolong(startIp) + "#" + iptolong(endIp);
  }
  const _int2iP = (num) => {
    let str;
    let tt = new Array();
    tt[0] = (num >>> 24) >>> 0;
    tt[1] = ((num << 8) >>> 24) >>> 0;
    tt[2] = (num << 16) >>> 24;
    tt[3] = (num << 24) >>> 24;
    str =
      String(tt[0]) +
      "." +
      String(tt[1]) +
      "." +
      String(tt[2]) +
      "." +
      String(tt[3]);
    return str;
  }
  const iptolong = (ip) => {
    let num = 0;
    ip = ip.split(".");
    num =
      Number(ip[0]) * 256 * 256 * 256 +
      Number(ip[1]) * 256 * 256 +
      Number(ip[2]) * 256 +
      Number(ip[3]);
    num = num >>> 0;
    return num;
  }

  /**过滤字段值*/
  const filterColumns = (column:any) =>{
    let param = {};
    state.filterTreeLoading = true;
    param["filter_column_code"] = props.column.prop;
    param["categoryId"] = props.categoryId;

    param = Object.assign({}, param, {
      dept__zone__id: props.organizationSelectValue
    })
    if (param == null || param["pageNum"] == null) {
      param["pageNum"] = 1;
      param["pageSize"] = 10;
    }
    if (props.isShow == false) {
      param["all"] = "all";
      param["allName"] = props.queryCondition.allName;
    } else {
      let propertyForm = props.propertyForm.rows;
      if (null != propertyForm) {
        //动态条件
        for (let x = 0; x < propertyForm.length; x++) {
          let colList = propertyForm[x].colList;
          for (let y = 0; y < colList.length; y++) {
            let col = colList[y];
            if (null != col.field) {
              let code = col.field.code;
              let value = col.field.value;
              let showType = col.field.showType;

              let code_id = code + "---" + showType;
              if(showType=='input'||showType=='inputSelect'||showType=='moreInput'){
                code_id = code + "---" + showType +"---" +col.field.hide;
              }
              if (
                null != value && code
              ) {
                if (col.field.value instanceof Array) {
                  if (value[0] != "" || value[1] != "") {
                    for (let i = 0; i < value.length; i++) {
                      if (!value[i]) {
                        value[i] = "null";
                      }
                    }
                    param[code_id] = value.join(",");
                  }
                } else {
                  param[code_id] = value;
                }
              }
            }
          }
        }
      } else {
        param["name"] = props.queryCondition.name;
        param["code"] = props.queryCondition.code;
        param["manager"] = props.queryCondition.manager;
        param["status"] = props.queryCondition.status;
      }
      let ipaddr = props.querySwithForm.ipaddr;
      let yanma = props.querySwithForm.yanma;
      let startIp = props.querySwithForm.startIp;
      let endIp = props.querySwithForm.endIp;
      if (ipaddr != "") {
        param["query_yanma_startIp"] = iptolong(ipaddr);
        if (yanma != "") {
          let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
          param["query_yanma_startIp"] = cc.split("#")[0];
          param["query_yanma_endIp"] = cc.split("#")[1];
        }
      }
      if (startIp != "") {
        param["query_startIp"] = iptolong(startIp);
      }
      if (endIp != "") {
        param["query_endIp"] = iptolong(endIp);
      }
    }
    if(props.colorMap!=null){
      let mm = {};
      for(let key in props.colorMap){
        if(key!=props.column.prop){
          mm[key] = props.colorMap[key];
        }
      }
      param["filterColumnMap"] = JSON.stringify(mm);
    }

    state.selectProperty = column.prop;
    state.filterFilterText = '';
    state.allSelectKeys = [];
    state.defaultCheckedKeys = [];
    filterTreeRef.value.setCheckedKeys([]);
    queryColumnListByFilterAxios(param).then(res=>{
      state.totalData  = res["data"].list;
      // if(state.totalData.length>30){
      //   for(let i = 0; i < 30; i++){
      //     state.filterColumnData.push(state.totalData[i]);
      //   }
      // }else{
        state.filterColumnData = state.totalData;
      // }
      state.selectFilterCount = res["data"].count;
      if(state.filterColumnData&&state.filterColumnData.length>0){
        for(let i = 0;i<state.filterColumnData.length;i++){
          state.allSelectKeys.push(state.filterColumnData[i].value);
        }
      }
      state.filterTreeLoading = false;
      nextTick(()=>{
        if(props.colorMap[column.prop]){
          let tt = props.colorMap[column.prop];
          let ss = [];
          for(let i=0;i<tt.length;i++){
            if(state.allSelectKeys.indexOf(tt[i])>=0){
              ss.push(tt[i]);
            }
          }
          state.defaultCheckedKeys = ss;
          // filterTreeRef.value.checkAll(false);
          if(state.defaultCheckedKeys.length>0){
            filterTreeRef.value.setCheckedKeys(state.defaultCheckedKeys);
          }else{
            filterTreeRef.value.setCheckedKeys([]);
          }
          changeFilterCheckd();
        }else{
          state.allChecked = false;
          state.isIndeterminate = false;
        }
      })

    }).catch(exp=>{
      console.log(exp);
      state.filterTreeLoading = false;
    })
  }
  const exportColumnCount = () =>{
    state.exportLoading = true;
    let params = {};
    params["fileName"] = props.column.label+"-导出计数";
    params["sheetName"] = props.column.label+"-导出计数";
    let dataList = [];
    let properties = [];
    let pro = {};
    pro["code"] = "code";
    pro["label"] = props.column.label;
    pro["dataType"] = 'String';
    properties.push(pro);
    pro = {};
    pro["code"] = "count";
    pro["label"] = "计数";
    pro["dataType"] = 'String';
    properties.push(pro);
    pro = {};
    pro["code"] = "rate";
    pro["label"] = "占比";
    pro["dataType"] = 'String';
    properties.push(pro);
    if(state.filterColumnData&&state.filterColumnData.length>0){
      for(let i=0;i<state.filterColumnData.length;i++){
        let item = {};
        item["code"] = state.filterColumnData[i].value;
        item["count"] = state.filterColumnData[i].count;
        item["rate"] = state.filterColumnData[i].rate+"%";
        dataList.push(item);
      }
    }
    params["properties"] = properties;
    params["dataList"] = dataList;
    exportExcelTempAxios(params).then(()=>{
      state.exportLoading = false;
    }).catch(exp=>{
      console.log(exp);
      state.exportLoading = false;
    })
  }
  onMounted(()=>{
    filterColumns(props.column)
  })
  const updateShowPo = (bool) =>{
  }
  defineExpose({
    updateShowPo
  })
</script>
