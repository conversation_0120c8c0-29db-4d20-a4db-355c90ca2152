<template>
  <el-row class="demo">
    <el-transfer v-model="props.checkProperty" filterable :data="props.data" :filter-method="filterMethod" :target-order="'push'"
      :titles="[props.leftTitle, props.rightTitle]" :props="{ key: props.idKey, label: props.labelKey }"></el-transfer>
  </el-row>
</template>
<script lang="ts" setup>
import Sortable from "sortablejs";
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
const filterMethod = (query, item) => {
  let regStr = query.replace(/\*/g, ".*");
  let reg = new RegExp(regStr);
  return reg.test(item.label);
}
const props = defineProps({
  data: {
    type: Array
  },
  leftTitle: {
    type: String,
    default: ''
  },
  rightTitle: {
    type: String,
    default: ''
  },
  idKey: {
    type: String
  },
  labelKey: {
    type: String
  },
  checkProperty: {
    type: Array<any>
  }
})
const checkData = ref(props.checkProperty);
const shiftKey = ref(false);
const firstWHLeftLocation = ref(-1); // 数据左边起始值
const lastWHLeftLocation = ref(-1); // 数据左边终止值
const firstWHRightLocation = ref(-1); // 数据右边起始值
const lastWHRightLocation = ref(-1); // 数据右边终止值

onMounted(() => {
  window.addEventListener("keydown", e => {
    if (e.keyCode === 16 && e.shiftKey) {
      shiftKey.value = true;
    }
  });
  window.addEventListener("keyup", e => {
    shiftKey.value = false;
  });
  let el = document
    .querySelector(".el-transfer")
    .querySelectorAll(".el-checkbox-group")[1];
  let htmlElement: HTMLElement = el as HTMLElement;
  const tmpDocument = document;
  new Sortable(htmlElement, {
    forceFallback: false,
    onUpdate: event => {
      let box = tmpDocument
        .querySelector(".el-transfer")
        .querySelectorAll(".el-checkbox-group")[1];
      let nums = tmpDocument
        .querySelector(".el-transfer")
        .querySelectorAll(".el-checkbox-group")[1].childNodes.length;
      if (event.newIndex >= nums) {
        return;
      }
      let newIndex = event.newIndex;
      let oldIndex = event.oldIndex;
      let $label = box.children[newIndex];
      let $oldLabel = box.children[oldIndex];
      box.removeChild($label);
      if (newIndex < oldIndex) {
        box.insertBefore($label, $oldLabel);
      } else {
        box.insertBefore($label, $oldLabel.nextSibling);
      }
      let item = checkData.value.splice(oldIndex, 1);
      checkData.value.splice(newIndex, 0, item[0]);
    }
  });
})

// 公共按住shift 多选
const commonChangeFuc = (
  key,
  key1,
  hasCheckedData,
  firstLocation,
  lastLocation,
  arrList,
  value
) => {
  var _this = this;
  var cFlag = false; //取消勾选
  // debugger
  for (var i = 0; i < key.length; i++) {
    if (key[i] == key1[0]) {
      cFlag = true; //选中
    }
  }
  if (cFlag) {
    if (key.length == 1) {
      firstLocation = 0;
      hasCheckedData.push(key[0]);
    } else if (key.length > 1) {
      let arr = [];
      // 当前有选中数据 并且 按住shift
      if (shiftKey.value) {
        // if (isRight) {
        for (let i = 0; i < arrList.length; i++) {
          let item = value ? arrList[i][value] : arrList[i];
          if (item == key[key.length - 2]) {
            firstLocation = i;
          }
          if (item == key1[0]) {
            lastLocation = i;
          }
        }
        if (firstLocation != -1 && lastLocation != -1) {
          if (firstLocation < lastLocation) {
            for (var k = 0; k < arrList.length; k++) {
              let item = value ? arrList[k][value] : arrList[k];

              if (k >= firstLocation && k <= lastLocation) {
                hasCheckedData.push(item);
              }
            }
          } else if (firstLocation > lastLocation) {
            for (var k = 0; k < arrList.length; k++) {
              let item = value ? arrList[k][value] : arrList[k];
              if (k >= lastLocation && k <= firstLocation) {
                hasCheckedData.push(item);
              }
            }
          }
        }
      } else {
        //不再按shift
        hasCheckedData.push(key1[0]);
      }
    }
  } else {
    //取消选中的
    hasCheckedData = [];
    for (var i = 0; i < key.length; i++) {
      if (key[i] != key1[0]) {
        hasCheckedData.push(key[i]);
      }
    }
  }
  // 去重
  hasCheckedData = new Set(hasCheckedData);
  hasCheckedData = Array.from(hasCheckedData);
  return hasCheckedData;
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.demo>>>.el-transfer-panel {
  width: 35%;
  height: 500px;
}

.demo>>>.el-checkbox-group,
.demo>>>.el-transfer-panel__list,
.demo>>>.is-filterable {
  height: 500px;
}

.demo>>>.el-transfer-panel__body {
  height: 500px;
}
</style>
