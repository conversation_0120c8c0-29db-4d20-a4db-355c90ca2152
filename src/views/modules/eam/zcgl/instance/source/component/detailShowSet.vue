<template>
  <el-card>
    <el-row>
      <el-col style="color:red;">说明：配置只读和自动生成字段编辑查看时是否展示，默认不展示</el-col>
    </el-row>
    <el-row style="margin-top:10px;">
      <el-col>
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="margin-bottom:10px;margin-top:10px;">全选</el-checkbox>
        <el-checkbox-group v-model="checkValue" @change="changeCheckd">
            <el-checkbox v-for="item in checkList" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </el-col>
    </el-row>
  </el-card>
</template>
<script setup lang="ts">
  import {initDetailShowAxios,saveDetailShowAxios} from '@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface';
  import {onMounted, reactive, toRefs} from "vue";
  import {ElMessage, ElMessageBox} from "element-plus";
  const props = defineProps({
    categoryId:{
      type:String||Number
    }
  });
  const state = reactive({
    checkAll:false,
    isIndeterminate:false,
    checkList:[],
    checkValue:[]
  })
  const {checkAll,isIndeterminate,checkList,checkValue} = toRefs(state);
  const handleCheckAllChange = (val) =>{
    let tt=[];
    if(val){
      for(let i =0;i<state.checkList.length;i++){
        tt.push(state.checkList[i].value);
      }
    }
    state.checkValue=tt;
    state.isIndeterminate = false;
  }
  const changeCheckd = () =>{
    let checkedCount = state.checkValue.length;
    state.checkAll = checkedCount === state.checkList.length;
    state.isIndeterminate = checkedCount > 0 && checkedCount < state.checkList.length;
  }
  const initDetailShowCheckBox = () =>{
    let params={};
    params.categoryId=props.categoryId;
    initDetailShowAxios(params).then(res=>{
      state.checkList = res.data.checkList;
      state.checkValue = res.data.checkValue;
      state.checkAll = state.checkValue.length === state.checkList.length;
      state.isIndeterminate = state.checkValue.length > 0 && state.checkValue.length < state.checkList.length;
    }).catch(exp=>{
      ElMessage.error(exp.message);
    })
  }
  const saveChecked = () =>{
    let params={
      categoryId:props.categoryId,
      checkValue:state.checkValue.join(",")
    };
    saveDetailShowAxios(params).then(res=>{
      if(res.data=='success'){
        ElMessage.success("保存成功！");
      }
    }).catch(exp=>{
      ElMessage.error(exp.message);
    })
  }
  const emit = defineEmits(["closeShowColumn"]);
  const closeChecked = () =>{
    emit("closeShowColumn");
  }
  defineExpose({
    saveChecked,closeChecked
  })
  onMounted(()=>{
    initDetailShowCheckBox();
  })
</script>
