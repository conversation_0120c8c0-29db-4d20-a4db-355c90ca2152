<template>
  <div>
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition mode="out-in" name="fade-transform">
      <!-- 动态组件，根据currentComponent的值来决定渲染哪个组件 -->
      <component
        :is="currentComponent"
        :event-info="selectedEvent"
        @jump-to="comChange"
        @event-select="eventSelectHandler"
      />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, shallowRef, toRefs} from "vue";
import EventDetailInfo from "@/views/modules/security/event/components/EventDetailInfo.vue";
import EventDealManage from "@/views/modules/eam/zcgl/instance/source/component/listOfRisksIntroduced/EventDealManage.vue";

// 创建两个组件的引用
const eventDealManage = shallowRef(EventDealManage)
const eventDetailInfo = shallowRef(EventDetailInfo)
const props = defineProps({
  requestIp:String
});

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 选中的事件
  selectedEvent: null,
})
const {
  currentComponent,
  selectedEvent
} = toRefs(state)

// 默认显示EventDealManage组件
state.currentComponent = eventDealManage;

// 根据传入的值切换组件
const comChange = (val: string) => {
  if (val == "eventDealManage") {
    state.currentComponent = eventDealManage;
  }

  if (val == "eventDetailInfo") {
    state.currentComponent = eventDetailInfo;
  }
};

// 处理事件选择
const eventSelectHandler = (evt: any) => {
  state.selectedEvent = evt;
}
onMounted(()=>{
  if(props.requestIp){
    eventSelectHandler(props.requestIp);
  }
})
</script>


