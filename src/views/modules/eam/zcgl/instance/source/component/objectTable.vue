<template>
  <div class="objectTableProperty" style="width: 100%">
    <el-form :model="tableFrom" ref="objectForm" label-width="10px">
      <im-table
        ref="propTable"
        :data="tableFrom.tableData"
        :columns="computedColumns"
        border
        id-key="sortId"
        stripe
      >
        <template
          v-slot:[item.code]="scope"
          v-for="(item, index) in props.tableColumn"
        >
          <el-form-item
            :prop="
              'tableData.' +
              (scope.row.sortId ? index : scope.row.sortId) +
              '.' +
              item.code
            "
            :rules="{
              required:
                type != 'add' && type != 'edit' ? false : item.rule.request,
              type: item.rule.type,
              name: item.rule.name,
              message: item.rule.message,
              trigger: item.rule.trigger,
              validator: (rule, value, callback) => {
                constMust(rule, scope.row[item.code], callback, item);
              }
            }"
          >
            <div style="position: relative; width: 100%">
              <span
                v-if="
                  item.property == 'must' && (type == 'add' || type == 'edit')
                "
                style="color: red; position: absolute; top: 3px; left: -10px"
                >*</span
              >
              <span
                v-if="item.showType == 'number' && item.isAuto != 1"
                style="width: 100%"
              >
                <span v-if="item.dataLength != null">
                  <el-input-number
                    v-model="scope.row[item.code]"
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                    :max="item.dataLength"
                  ></el-input-number>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input-number
                    v-model="scope.row[item.code]"
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                  ></el-input-number>
                </span>
              </span>
              <span v-if="item.isAuto == 1" style="width: 100%">
                <span v-if="item.dataLength != null">
                  <el-input
                    type="text"
                    v-model="scope.row[item.code]"
                    style="width: 100%"
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                    :max="item.dataLength"
                  ></el-input>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input
                    type="text"
                    v-model="scope.row[item.code]"
                    style="width: 100%"
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                  ></el-input>
                </span>
              </span>
              <span v-if="item.showType == 'inputSelect'" style="width: 100%">
                <el-input-tag
                  v-model="scope.row[item.code]"
                  @rulesInput="rulesInput(item, index)"
                  v-if="(type == 'add' || type == 'edit') && item.isEdit != 1"
                ></el-input-tag>
                <el-input
                  disabled
                  v-else
                  v-model="scope.row[item.code]"
                ></el-input>
              </span>
              <span v-if="item.showType == 'moreInput'">
                <el-input
                  style="width: 100%"
                  v-if="
                    (type == 'add' || type == 'edit') && item.field.isEdit != 1
                  "
                  v-model="scope.row[item.code]"
                ></el-input>
                <el-input
                  disabled
                  v-else
                  v-model="scope.row[item.code]"
                ></el-input>
              </span>
              <span v-if="item.showType == 'windowboxFilter'">
                <div
                  style="display: table-cell; width: 44px; vertical-align: top"
                >
                  <el-button
                    icon="el-icon-search"
                    @click="openFilterSelect(item)"
                    v-if="item.isEdit != 1 ? true : false"
                  ></el-button>
                </div>
                <div style="display: table-cell">
                  <el-select
                    style="width: 100%"
                    v-model="scope.row[item.code]"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    @focus="val => clearTableList(val, item)"
                    :disabled="item.isEdit != 1 ? false : 'disabled'"
                    :remote-method="vul => remoteMethod(vul, item)"
                  >
                    <el-option
                      v-for="item1 in item.tableList"
                      :key="item1.value"
                      :label="item1.label"
                      :value="item1.value"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div
                  style="display: table-cell; width: 44px; vertical-align: top"
                >
                  <el-button
                    icon="el-icon-close"
                    v-if="
                      item.isEdit != 1 && item.isShowClear == '1' ? true : false
                    "
                    @click="clearSelect(item)"
                  ></el-button>
                </div>
              </span>
              <span v-if="item.showType == 'objectTable'">
                <object-table
                  :categoryIdP="categoryIdP"
                  :proOcode="item.ocode"
                  :type="type"
                  :tableData="scope.row[item.code]"
                  :tableColumn="item.tableList"
                ></object-table>
              </span>
              <span v-if="item.showType == 'windowboxTreeFilter'">
                <div
                  v-if="item.isEdit != 1 ? true : false"
                  style="display: table-cell; width: 44px; vertical-align: top"
                >
                  <el-button
                    icon="el-icon-search"
                    @click="openWindowTree(item)"
                  ></el-button>
                </div>
                <div style="display: table-cell">
                  <el-select
                    v-model="scope.row[item.code]"
                    multiple
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入关键词"
                    @focus="val => clearTreeTableList(val, item)"
                    :disabled="item.isEdit != 1 ? false : 'disabled'"
                    :remote-method="vul => remoteTreeMethod(vul, item)"
                  >
                    <el-option
                      v-for="item1 in item.tableList"
                      :key="item1.value"
                      :label="item1.label"
                      :value="item1.value"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div
                  v-if="
                    item.isEdit != 1 && item.isShowClear == '1' ? true : false
                  "
                  style="display: table-cell; width: 44px; vertical-align: top"
                >
                  <el-button
                    icon="el-icon-close"
                    @click="clearWindowTree(item)"
                  ></el-button>
                </div>
              </span>
              <span v-if="item.showType == 'input' && item.isAuto != 1">
                <!--数据类型 input、number、text、select、checkbox、radio-->
                <span v-if="item.dataType == 'string'" style="width: 100%">
                  <span v-if="item.dataLength != null" style="width: 100%">
                    <el-input
                      v-model="scope.row[item.code]"
                      style="width: 100%"
                      :maxlength="item.dataLength"
                      :placeholder="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? item.name
                          : ''
                      "
                      :disabled="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? false
                          : 'disabled'
                      "
                    ></el-input>
                  </span>
                  <span v-if="item.dataLength == null">
                    <el-input
                      v-model="scope.row[item.code]"
                      style="width: 100%"
                      :placeholder="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? item.name
                          : ''
                      "
                      :disabled="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? false
                          : 'disabled'
                      "
                    ></el-input>
                  </span>
                </span>
                <span v-if="item.dataType == 'long'">
                  <span v-if="item.dataLength != null">
                    <el-input-number
                      v-model="scope.row[item.code]"
                      style="width: 100%"
                      :max="item.dataLength"
                      :disabled="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? false
                          : 'disabled'
                      "
                    ></el-input-number>
                  </span>
                  <span v-if="item.dataLength == null">
                    <el-input-number
                      v-model="scope.row[item.code]"
                      style="width: 100%"
                      :disabled="
                        (type == 'add' || type == 'edit') && item.isEdit != 1
                          ? false
                          : 'disabled'
                      "
                    ></el-input-number>
                  </span>
                </span>
              </span>

              <span v-if="item.showType == 'textarea'">
                <!--数据类型 input、number、text、select、checkbox、radio-->
                <span v-if="item.dataLength != null">
                  <el-input
                    v-model="scope.row[item.code]"
                    style="width: 100%"
                    type="textarea"
                    :maxlength="item.dataLength"
                    :placeholder="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? item.name
                        : ''
                    "
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                  ></el-input>
                </span>
                <span v-if="item.dataLength == null">
                  <el-input
                    v-model="scope.row[item.code]"
                    style="width: 100%"
                    type="textarea"
                    :placeholder="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? item.name
                        : ''
                    "
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                  ></el-input>
                </span>
              </span>
              <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
              <span v-if="item.showType == 'dateTime'">
                <el-date-picker
                  v-model="scope.row[item.code]"
                  style="width: 100%"
                  type="datetime"
                  :disabled="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? false
                      : 'disabled'
                  "
                ></el-date-picker>
              </span>
              <!--日期输入框 yyyy-MM-dd-->
              <span v-if="item.showType == 'date'">
                <el-date-picker
                  v-model="scope.row[item.code]"
                  style="width: 100%"
                  type="date"
                  :disabled="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? false
                      : 'disabled'
                  "
                ></el-date-picker>
              </span>

              <span v-if="item.showType == 'checkbox'">
                <el-checkbox-group v-model="scope.row[item.code]">
                  <el-checkbox
                    :label="cn.value"
                    v-for="cn in item.enumArray"
                    :disabled="
                      (type == 'add' || type == 'edit') && item.isEdit != 1
                        ? false
                        : 'disabled'
                    "
                    >{{ cn.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </span>

              <span v-if="item.showType == 'mul_combobox'">
                <el-select
                  placement="top"
                  style="width: 100%"
                  filterable
                  multiple="true"
                  :placeholder="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? '请选择'
                      : '  '
                  "
                  v-model="scope.row[item.code]"
                  :disabled="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? false
                      : 'disabled'
                  "
                >
                  <el-option
                    v-for="en in item.enumArray"
                    :value="en.value"
                    :label="en.label"
                  ></el-option>
                </el-select>
              </span>
              <span v-if="item.showType == 'comboTree'">
                <el-tree-select
                  :disabled="
                    (type == 'add' || type == 'edit') && item01.isEdit == 0
                      ? false
                      : true
                  "
                  children-key="children"
                  node-key="id"
                  node-value="id"
                  id-key="id"
                  title-key="title"
                  clearable
                  :placeholder="
                    (type == 'add' || type == 'edit') && item01.isEdit == 0
                      ? '请选择'
                      : '  '
                  "
                  v-model="scope.row[item01.code]"
                  :data="item01.enumArray"
                  check-strictly
                  :render-after-expand="false"
                  style="width: 90%"
                />
              </span>
              <!--单选-->
              <span v-if="item.showType == 'radio'">
                <el-radio-group v-model="scope.row[item.code]">
                  <el-radio
                    :value="cn.value"
                    :label="cn.value"
                    v-for="cn in item.enumArray"
                    :disabled="item.isEdit != 1 ? false : 'disabled'"
                  >
                    {{ cn.label }}
                  </el-radio>
                </el-radio-group>
              </span>
              <span v-if="item.showType == 'combobox'">
                <el-select
                  placement="top"
                  style="width: 100%"
                  filterable
                  :placeholder="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? '请选择'
                      : '  '
                  "
                  v-model="scope.row[item.code]"
                  clearable
                  :disabled="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? false
                      : 'disabled'
                  "
                >
                  <el-option
                    v-for="en in item.enumArray"
                    :value="en.value"
                    :label="en.label"
                  ></el-option>
                </el-select>
              </span>
              <span v-if="item.showType == 'cascader'">
                <el-cascader
                  :options="item.enumArray"
                  style="width: 100%"
                  :placeholder="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? '请选择'
                      : '  '
                  "
                  v-model="scope.row[item.code]"
                  clearable
                  :disabled="
                    (type == 'add' || type == 'edit') && item.isEdit != 1
                      ? false
                      : 'disabled'
                  "
                ></el-cascader>
              </span>
              <span
                v-if="
                  item.titleDesc != null &&
                  item.titleDesc != undefined &&
                  item.titleDesc != '' &&
                  item.titleDesc != 'null'
                "
                style="position: absolute; top: 0px; left: -18px"
                :title="item.titleDesc"
              >
                <i type="ios-help-circle-outline" size="15" />
              </span>
            </div>
          </el-form-item>
        </template>
        <template v-slot:oper="scope">
          <el-button
            type="primary"
            :icon="useRenderIcon('EP-CirclePlus')"
            v-if="type == 'add' || type == 'edit'"
            @click="addRow()"
          ></el-button>
          <el-button
            type="primary"
            :icon="useRenderIcon('EP-Remove')"
            v-if="type == 'add' || type == 'edit'"
            @click="deleteRow(scope.row)"
          ></el-button>
        </template>
      </im-table>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  toRefs,
  watch,
  onMounted,
  onBeforeMount,
  reactive,
  computed
} from "vue";
import { checkObjectPropertyByCodeAxios } from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import { ElMessage, ElMessageBox, type FormInstance } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

onBeforeMount(() => {
  init();
});
const state = reactive({
  dataColumn: [],
  tableFrom: {
    tableData: []
  }
});
const { dataColumn, tableFrom } = toRefs(state);
const props = defineProps({
  showOper: String,
  sss: String,
  proOcode: {
    type: String,
    default: ""
  },
  type: {
    type: String,
    default: ""
  },
  tableData: {
    type: Array,
    default: []
  },
  tableColumn: {
    type: Array,
    default: []
  },
  categoryIdP: {
    type: [Number, String],
    default: 0
  },
  proCode: {
    type: String
  }
});
const emit = defineEmits(["validateObjectFiled", "update:tableData"]);
//监听组件属性
watch(
  state.tableFrom,
  async (newValue: any) => {
    emit("update:tableData", newValue.tableData);
    emit("validateObjectFiled", props.proCode);
  },
  { deep: true }
);

const constMust = async (rule, value, callback, item) => {
  let checkParams = {};
  let requreid = rule.required;
  if (requreid) {
    checkParams.ocode = props.proOcode;
    checkParams.code = item.rule.field;
    checkParams.value = value;
    checkObjectPropertyByCodeAxios(checkParams)
      .then(res => {
        if (res.data.state == "success") {
          callback();
        } else if (res.data.state == "error1") {
          callback(new Error(rule.name + "不满足校验规则"));
        } else {
          callback(new Error(rule.name + "不能为空"));
        }
      })
      .catch(exp => {
        callback(new Error(rule.name + "不满足校验规则"));
      });
  } else {
    callback();
  }
};
const addRow = () => {
  let cc = {};
  for (let i = 0; i < props.tableColumn.length; i++) {
    let showType = props.tableColumn[i].showType;
    if (showType == "number" || showType == "comboTree") {
      cc[props.tableColumn[i].code] = null;
    } else if (
      showType == "windowboxFilter" ||
      showType == "windowboxTreeFilter" ||
      showType == "inputSelect" ||
      showType == "moreInput" ||
      showType == "objectTable"
    ) {
      cc[props.tableColumn[i].code] = [];
    } else {
      cc[props.tableColumn[i].code] = "";
    }
  }
  cc.sortId = state.tableFrom.tableData.length + 1;
  state.tableFrom.tableData.push(cc);
};
const deleteRow = row => {
  if (state.tableFrom.tableData.length == 1) {
    ElMessage.error("最少保留一行");
  } else {
    let tt = 0;
    for (let i = 0; i < state.tableFrom.tableData.length; i++) {
      if (state.tableFrom.tableData[i].sortId == row.sortId) {
        tt = i;
        break;
      }
    }
    state.tableFrom.tableData.splice(tt, 1);
    for (let i = 0; i < state.tableFrom.tableData.length; i++) {
      state.tableFrom.tableData[i].sortId = i + 1;
    }
  }
};
const init = () => {
  if (props.type == "add" || props.type == "edit") {
    let tt = props.tableData;
    for (let i = 0; i < tt.length; i++) {
      tt[i]["sortId"] = i + 1;
    }
    if (tt.length == 0) {
      let cc = {};
      for (let i = 0; i < props.tableColumn.length; i++) {
        let showType = props.tableColumn[i].showType;
        if (showType == "number" || showType == "comboTree") {
          cc[props.tableColumn[i].code] = null;
        } else if (
          showType == "windowboxFilter" ||
          showType == "windowboxTreeFilter" ||
          showType == "inputSelect" ||
          showType == "moreInput" ||
          showType == "objectTable"
        ) {
          cc[props.tableColumn[i].code] = [];
        } else {
          cc[props.tableColumn[i].code] = "";
        }
      }
      cc.sortId = 1;
      tt.push(cc);
    }
    emit("update:tableData", tt);
    state.tableFrom.tableData = props.tableData;
    for (let i = 0; i < props.tableColumn.length; i++) {
      let mt = props.tableColumn[i];
      mt.label = props.tableColumn[i].name;
      mt.prop = props.tableColumn[i].code;
      mt.slot = props.tableColumn[i].code;
      //if(!this.showOper||this.showOper==null||this.showOper=="") {
      mt.width = props.tableColumn[i].width + "px";
      // }
      mt.hidden = props.tableColumn[i].hidden == "0" ? true : false;
      state.dataColumn.push(mt);
    }
    if (!props.showOper || props.showOper == null || props.showOper == "") {
      let mm1 = {};
      mm1.label = "操作";
      mm1.align = "center";
      mm1.slot = "oper";
      mm1.width = "150px";
      state.dataColumn.push(mm1);
    }
    if (state.dataColumn.length == 1) {
      let mm2 = state.dataColumn[0];
      mm2.width = undefined;
      state.dataColumn[0] = mm2;
    }
  } else {
    state.tableFrom.tableData = props.tableData;
    for (let i = 0; i < props.tableColumn.length; i++) {
      let mt = props.tableColumn[i];
      mt.label = props.tableColumn[i].name;
      mt.prop = props.tableColumn[i].code;
      mt.slot = props.tableColumn[i].code;
      //if(!this.showOper||this.showOper==null||this.showOper=="") {
      mt.width = props.tableColumn[i].width + "px";
      // }
      mt.hidden = props.tableColumn[i].hidden == "0" ? true : false;
      state.dataColumn.push(mt);
    }
  }
};

// 动态转化width -> minWidth
const computedColumns = computed(() => {
  return dataColumn.value.map(col => {
    // width -> minWidth
    return {
      ...col,
      width: null,
      minWidth: col.minWidth || col.width
    };
  });
});

const objectForm = ref<FormInstance>();

const objRules = async required => {
  const response = await new Promise((resolve, reject) => {
    objectForm.value.validate(ref => {
        if(ref){
          if (required) {
              // 模拟异步操作
              let flag = false;
              if (state.tableFrom.tableData.length == 1) {
                for (let i = 0; i < props.tableColumn.length; i++) {
                  if (
                    state.tableFrom.tableData[0][props.tableColumn[i].code] !=
                    undefined &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != null &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != "" &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != 0
                  ) {
                    flag = true;
                  }
                }
              } else {
                flag = true;
              }
              if (!flag) {
                reject(false);
              } else {
                resolve(true);
              }
            return response;
          } else {
              let flag = false;
              if (state.tableFrom.tableData.length == 1) {
                for (let i = 0; i < props.tableColumn.length; i++) {
                  if (
                    state.tableFrom.tableData[0][props.tableColumn[i].code] !=
                    undefined &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != null &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != "" &&
                    state.tableFrom.tableData[0][props.tableColumn[i].code] != 0
                  ) {
                    flag = true;
                  }
                }
              } else {
                flag = true;
              }
              if (!flag) {
                resolve(true);
              } else {
                const valid = objectForm.value.validate();
                if (valid) {
                  if (props.proCode == "wangka") {
                    let ipnames = [];
                    let mes = [];
                    for (let i = 0; i < props.tableData.length; i++) {
                      let ipname = props.tableData[i].ipname;
                      if (ipnames.indexOf(ipname) > -1) {
                        mes.push(
                          "网卡第" +
                          (ipnames.indexOf(ipname) + 1) +
                          "行与第" +
                          (i + 1) +
                          "行ip地址冲突，请检查修改"
                        );
                      }
                      ipnames.push(ipname);
                    }
                    if (mes.length > 0) {
                      let message = mes.join("<br/>");
                      reject(false);
                      ElMessageBox.alert(message, "提示信息", {
                        dangerouslyUseHTMLString: true
                      });
                    } else {
                      resolve(true);
                    }
                  } else {
                    resolve(true);
                  }
                } else {
                  reject(false);
                }
              }
            return response;
          }
        }else{
          resolve(false);
        }
    })
  });
  return response;
};
defineExpose({
  objRules
});
const rulesInput = (item, code) => {
  if (item.field.isRule == "1") {
    const vs = objectForm.value.validateField(code);
    if (vs.data == "success") {
    } else {
      return;
    }
  }
};
</script>
