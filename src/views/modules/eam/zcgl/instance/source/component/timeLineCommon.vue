<template>
  <div class="time">
    <el-timeline style="max-width: 600px">
      <!-- :timestamp="activity.timestamp" -->
      <el-timeline-item
        v-for="(activity, index) in activities"
        :key="index"
        :color="activity.color"
        :hollow="activity.hollow"
        placement="top"
      >
        <template #default>
          <!-- left: -7rem; -->
          <div style="position: relative; left: -6.3rem; top: -0.93rem">
            <span
              style="font-size: 10px; color: rgba(0, 0, 0, 0.647058823529412)"
              >{{ activity.timestamp }}</span
            >
          </div>
          <div
            style="
              position: relative;
              top: -2.3rem;
              font-weight: 400;
              font-style: normal;
              font-size: 10px;
              color: rgba(0, 0, 0, 0.647058823529412);
              line-height: 26px;
            "
          >
            <div style="height: 20px; display: flex">
              <div style="padding-right: 1rem">
                {{ activity.a }}
              </div>
              <div>
                <el-tag
                  class="tag"
                  size="small"
                  style="
                    font-size: 10px;
                    max-width: 7rem;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    height: 1.2rem;
                    line-height: 1.2rem;
                  "
                  type="primary"
                >
                  web站点变更web站点变更
                </el-tag>
              </div>
            </div>
            {{ activity.content }}
          </div>
        </template>

        <template #dot>
          <div
            v-if="index == 0"
            style="
              width: 10px;
              height: 10px;
              border: 2px solid #67c23a;
              background-color: #fff;
              border-radius: 50%;
            "
            class="custom-dot"
          ></div>
          <div v-else-if="index == 1 || index == 2" class="custom-dot"></div>
          <div
            v-else
            style="
              width: 10px;
              height: 10px;
              border: 2px solid #409eff;
              background-color: #fff;
              border-radius: 50%;
            "
            class="custom-dot"
          ></div>
        </template>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script lang="ts"setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted } from "vue";
const props = defineProps({
  activitiesData: Array
});
const activities = [
  {
    content: "Custom icon",
    timestamp: `2018-04-12`,
    a: "11:16",
    size: "large",
    type: "primary"
  },
  {
    content: "在线状态由【离线】变更成【在线】责任人由【张三】变更为【李四】",
    a: "20:46",
    color: "#0bbd87"
  },
  {
    content: "Custom size",
    a: "20:46",
    size: "large"
  },
  {
    content: "Custom hollow",
    a: "20:46",
    type: "primary",
    hollow: true
  }
  // {
  //   content: "Default node",
  //   timestamp: "2018-04-03",
  //   a: "11:16"
  // }
];
</script>

<style lang="scss" scoped>
:deep(.time) {
  font-size: 16px;
  font-weight: 500;
  .log-text {
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 18px;
      background: inherit;
      background-color: #409eff;
      border: none;
      border-radius: 8px;
      vertical-align: -3px;
      margin-right: 6px;
    }
  }
  .el-tag {
    vertical-align: 1px;
    .el-tag__content {
      max-width: 7rem;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .el-timeline-item__wrapper {
    top: -16 !important;
  }
}
</style>
