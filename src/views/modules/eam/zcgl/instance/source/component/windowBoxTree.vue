<template>
  <el-card class="windowboxTree">
    <el-row>
      <el-col :span="4">
        <el-tree
          ref="tree"
          v-loading="treeLoading"
          show-icon
          :toolOptions="['expand']"
          :default-expanded-keys="[0]"
          :model="item.field.enumArray[0].treeList"
          highlight-current
          title-key="title"
          class="instanceTree"
          :expand-on-click-node="false"
          parent-key="parentId"
          @node-click="handleNodeClick"
        />
      </el-col>
      <el-col :span="20">
        <el-form
          ref="queryFormRef"
          :model="queryForm"
          label-width="120px"
          style="overflow: hidden"
        >
          <el-col
            v-for="(item1, index1) in item.field.enumArray[0].ocodeTree
              .treeTablePros"
            :span="12"
            :key="index1"
          >
            <el-form-item
              v-if="item1.isSearch == '1' && item1.searchType == 'input'"
              :prop="item1.key"
              :label="item1.title"
            >
              <el-input type="text" v-model="queryForm[item1.key]"></el-input>
            </el-form-item>
            <el-form-item
              :prop="item1.key"
              v-if="item1.isSearch == '1' && item1.searchType == 'combobox'"
              :label="item1.title"
            >
              <el-select v-model="queryForm[item1.key]">
                <el-option
                  v-for="(item2, index2) in item1.keyList"
                  :value="item2.value"
                  :label="item2.label"
                  :key="index2"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :prop="item1.key"
              v-if="
                item1.isSearch == '1' && item1.searchType == 'searchCombobox'
              "
              :label="item1.title"
            >
              <el-select
                style="width: 100%"
                v-model="queryForm[item1.key]"
                remote
                clearable
                reserve-keyword
                filterable
                placeholder="请输入关键词"
                :remote-method="val => remoteMethod(val, item1)"
                :loading="loading"
              >
                <el-option
                  v-for="item3 in options"
                  :key="item3.value"
                  :label="item3.label"
                  :value="item3.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
        <el-row>
          <el-col :span="24" style="text-align: center">
            <el-button type="primary" @click="searchTable">查询</el-button>
            <el-button type="primary" @click="resetTable">重置</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="saveWindowboxTree"
              >保存</el-button
            >
            <el-button type="primary" @click="closeWindowboxTree"
              >取消</el-button
            >
          </el-col>
        </el-row>
        <el-table
          :data="data"
          ref="dataTable"
          :row-key="valueKey"
          @selection-change="changeSelect"
          v-loading="tableLoading"
        >
          <el-table-column type="selection" reserve-selection></el-table-column>
          <el-table-column
            v-for="item4 in item.field.enumArray[0].ocodeTree.treeTablePros"
            :prop="item4.key"
            :label="item4.title"
          >
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes, prev, pager, next, jumper"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </el-card>
</template>
<style lang="scss" scoped>
.windowboxTree {
  .card {
    overflow: auto;
  }

  .instanceTree :deep(.el-tree-node.is-current) > .el-tree-node__content {
    background-color: #bae7ff;
  }

  .instanceTree  :deep(.el-tree-node:focus) > .el-tree-node__content {
    background-color: #bae7ff;
  }
}
</style>
<script setup lang="ts">
import {
  queryTreeTableAxios,
  querySearchTableListAxios,
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {onMounted, reactive,ref,toRefs} from "vue";
import {ElMessage} from "element-plus";
const state = reactive({
  itemShowType: "",
  currentPage: 1,
  pageSize: 10,
  total: 0,
  queryForm: {},
  defaultItemValue: "",
  options: [],
  data: [],
  treeLoading: false,
  itemValue: "",
  itemShowValue: "",
  itemTreeNode: {},
  loading: false,
  selectRows: [],
  tableLoading: false,
  valueKey: "",
  labelKey: "",
  defaultSelectRows: [],
});
let {currentPage,pageSize,total,queryForm,data,valueKey,tableLoading,options,loading,treeLoading} = toRefs(state);
const changeSelect = (selection) =>{
  state.selectRows = selection;
  saveItem();
}
const remoteMethod = (query, item1) => {
  if (query !== "") {
    var params = {};
    params.key = item1.key;
    params.value = query;
    params.treeId =
      state.itemTreeNode.id == undefined ||
      state.itemTreeNode.id == null ||
      state.itemTreeNode.id == "0"
        ? ""
        : state.itemTreeNode.id;
    params.treeCode = props.item.field.enumArray[0].ocodeTree.treeFun;
    state.loading = true;
    querySearchTableListAxios(params).then(res => {
      state.options = res.data.list;
      state.loading = false;
    });
  } else {
    state.options = [];
  }
}
const queryFormRef = ref();
const dataTable = ref();
const handleNodeClick = (node) => {
  if (state.selectRows.length > 0) {
    for (let i = 0; i < state.selectRows.length; i++) {
      state.defaultSelectRows.push(state.selectRows[i]);
    }
  }
  queryFormRef.value.resetFields();
  state.currentPage = 1;
  state.itemTreeNode = node;
  dataTable.value.clearSelection();
  queryTableSelect();
}
const searchTable = () => {
  state.currentPage = 1;
  queryTable();
}
const queryTable = () => {
  state.tableLoading = true;
  let treeCode = props.item.field.enumArray[0].ocodeTree.treeClick;
  let params = state.queryForm;
  params.treeCode = treeCode;
  params.keyId =
    state.itemTreeNode.id == undefined ||
    state.itemTreeNode.id == null ||
    state.itemTreeNode.id == "0"
      ? ""
      : state.itemTreeNode.id;
  params.pageNum = state.currentPage;
  params.pageSize = state.pageSize;
  params.itemValue = state.itemValue;
  queryTreeTableAxios(params)
    .then(res => {
      state.tableLoading = false;
      state.data = res.data.list;
      state.total = res.data.total;
    })
    .catch(exp => {
      state.tableLoading = false;
      ElMessage.error(exp.message);
    });
}
const handleCurrentChange = (val) => {
  state.currentPage = val;
  queryTable();
}
const handleSizeChange = (val) => {
  state.currentPage = 1;
  state.pageSize = val;
  queryTable();
}
const resetTable = () => {
  queryFormRef.value.resetFields();
  state.currentPage = 1;
  queryTable();
}
const initFormModel = (data) => {
  let formModel = {};
  for (let i = 0; i < data.length; i++) {
    if (data[i].isSearch == "1") {
      formModel[data[i].key] = "";
    }
  }
  return formModel;
}
const saveItem = ()=> {
  let ttValue = "";
  let ttLabel = "";
  if (state.selectRows.length > 0) {
    for (let i = 0; i < state.selectRows.length; i++) {
      if (i == state.selectRows.length - 1) {
        ttValue += state.selectRows[i][state.valueKey];
        ttLabel += state.selectRows[i][state.labelKey];
      } else {
        ttValue += state.selectRows[i][state.valueKey] + ",";
        ttLabel += state.selectRows[i][state.labelKey] + ",";
      }
    }
  }
  if (state.defaultSelectRows.length > 0) {
    if (ttValue != "") {
      ttValue += ",";
      ttLabel += ",";
    }
    for (var i = 0; i < state.defaultSelectRows.length; i++) {
      if (i == state.defaultSelectRows.length - 1) {
        ttValue += state.defaultSelectRows[i][state.valueKey];
        ttLabel += state.defaultSelectRows[i][state.labelKey];
      } else {
        ttValue += state.defaultSelectRows[i][state.valueKey] + ",";
        ttLabel += state.defaultSelectRows[i][state.labelKey] + ",";
      }
    }
  }
  state.itemValue = ttValue;
  state.itemShowValue = ttLabel;
}
const emit = defineEmits(["closeWindowTree"])
const saveWindowboxTree = () => {
  if (state.itemShowType == "windowboxTree") {
    props.item.field.value = state.itemValue;
  } else {
    props.item.field.tableList = props.item.field.enumArray[0].enumList;
    props.item.field.value = state.itemValue.split(",");
  }

  props.item.field.showValue = state.itemShowValue;
  emit("closeWindowTree", props.item);
}
const closeWindowboxTree = () => {
  emit("closeWindowTree", props.item);
}
const queryTableSelect = () => {
  state.tableLoading = true;
  let treeCode = state.item.field.enumArray[0].ocodeTree.treeClick;
  let params = state.queryForm;
  params.treeCode = treeCode;
  params.itemValue = state.itemValue;
  params.keyId =
    state.itemTreeNode.id == undefined ||
    state.itemTreeNode.id == null ||
    state.itemTreeNode.id == "0"
      ? ""
      : state.itemTreeNode.id;
  params.pageNum = state.currentPage;
  params.pageSize = 10000;
  queryTreeTableAxios(params)
    .then(res => {
      state.tableLoading = false;
      state.data = res.data.list;
      state.total = res.data.total;
      var flag1 = false;
      for (let k = 0; k < state.data.length; k++) {
        if (state.defaultSelectRows.length > 0) {
          flag1 = true;
          let flag = false;
          let deleteIndex = 0;
          for (let i = 0; i < state.defaultSelectRows.length; i++) {
            if (
              state.defaultSelectRows[i][state.valueKey] ==
              state.data[k][state.valueKey]
            ) {
              dataTable.value.toggleRowSelection(
                state.data[k],
                true
              );
              flag = true;
              deleteIndex = i;
              break;
            }
          }
          if (flag) {
            state.defaultSelectRows.splice(deleteIndex, 1);
            saveItem();
          }
        } else if (!flag1) {
          if (state.itemShowType == "windowboxTree") {
            if (
              state.defaultItemValue != null &&
              state.defaultItemValue != ""
            ) {
              if (
                state.defaultItemValue
                  .split(",")
                  .indexOf(state.data[k][state.valueKey]) >= 0
              ) {
                dataTable.value.toggleRowSelection(
                  state.data[k],
                  true
                );
              }
            }
          } else {
            if (
              state.defaultItemValue != null &&
              state.defaultItemValue.length > 0
            ) {
              if (
                state.defaultItemValue.indexOf(
                  state.data[k][state.valueKey]
                ) >= 0
              ) {
                dataTable.value.toggleRowSelection(
                  state.data[k],
                  true
                );
              }
            }
          }
        }
      }
      var count = 0;
      if (state.total % 10000 == 0) {
        count = parseInt(state.total / 10000);
      } else {
        count = parseInt(state.total / 10000 + 1);
      }
      if (state.currentPage >= count || count == 0) {
        state.currentPage = 1;
        queryTable();
      } else {
        state.currentPage++;
        queryTableSelect();
      }
    })
    .catch(exp => {
      state.tableLoading = false;
      ElMessage.error(exp.message);
    });
}
onMounted(()=>{
  for (
    var i = 0;
    i < props.item.field.enumArray[0].ocodeTree.treeTablePros.length;
    i++
  ) {
    if (
      props.item.field.enumArray[0].ocodeTree.treeTablePros[i].isLabel == "1"
    ) {
      state.labelKey = props.item.field.enumArray[0].ocodeTree.treeTablePros[
        i
        ].key;
    }
    if (
      props.item.field.enumArray[0].ocodeTree.treeTablePros[i].isValue == "1"
    ) {
      state.valueKey = props.item.field.enumArray[0].ocodeTree.treeTablePros[
        i
        ].key;
    }
  }
  if (
    props.item.field.value != undefined &&
    props.item.field.value != null &&
    props.item.field.value != ""
  ) {
    state.defaultItemValue = props.item.field.value;
    state.itemValue = props.item.field.value;
    state.itemShowValue = props.item.field.showValue;
    state.itemShowType = props.item.field.showType;
  }
  state.queryForm = initFormModel(
    props.item.field.enumArray[0].ocodeTree.treeTablePros
  );
  queryTableSelect();
})
const props = defineProps({
  item: {
    type: Object,
  },
})
</script>
