<template>
  <div class="assetBaseInfo">
    <!--    <h3 style="margin-bottom:15px;">资产数据分类</h3>-->
    <el-tabs
      v-model="activeName"
      :tab-position="tabPosition"
      :stretch="true"
      style="height: calc(100vh - 200px)"
      class="base-tabs"
      @tab-click="changeInfoTree"
    >
      <el-tab-pane
        v-for="item in props.tabLeftNameArray"
        :label="item.name"
        :name="item.id"
        v-loading="saveInstanceLoading"
      >
        <!-- <div>{{ props.allBaseInfoAllEdit }}</div> -->
        <div v-if="item.id == activeName && item.name == '基本信息'">
          <el-row class="addPropertyEdit">
            <el-col
              v-loading="props.isLoading"
              style="
                height: calc(100vh - 200px);
                overflow-y: scroll;
                position: relative;
                padding-bottom: 20px;
              "
              :span="19"
            >
              <el-form
                v-if="props.editType == 'add' || props.editType == 'edit'"
                :rules="allRules"
                ref="ruleFormRef"
                :model="props.allBaseInfoAllEdit"
                label-width="112px"
              >
                <template
                  v-for="(cols, index0) in props.baseInfoAllEdit"
                  :key="index0"
                  style="border: 1px solid #f40"
                >
                  <div v-if="index0 > 0" class="group-divider"></div>
                  <div
                    class="property-group-card edit"
                    v-if="cols['name'] != -1"
                  >
                    <el-row>
                      <el-col :span="24">
                        <div class="property-group">
                          <GroupSvg class="group-img"></GroupSvg>
                          <span class="group-name">{{ cols["name"] }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <!--                  <el-row-->
                  <!--                    v-if="cols['name'] != -1"-->
                  <!--                    style="padding-bottom: 1.6rem"-->
                  <!--                  >-->
                  <!--                    <el-col class="topDivider" :span="24">-->
                  <!--                      <el-divider content-position="left">-->
                  <!--                        {{ cols["name"] }}-->
                  <!--                      </el-divider>-->
                  <!--                    </el-col>-->
                  <!--                  </el-row>-->
                  <el-row
                    class="group-items-wrap"
                    v-for="(col, index1) in cols['rows']"
                    :key="index1"
                  >
                    <el-col
                      v-for="(item, index01) in col['colList']"
                      :span="
                        item.field.showType == 'objectTable' ||
                        item.field.showType == 'table'
                          ? 24
                          : item.span
                      "
                      :key="index01"
                      v-show="
                        item.field != null &&
                        item.rule != null &&
                        item.field.property != 'readonly' &&
                        ((item.field.isAuto != '1' &&
                          item.field.property != 'readonly') ||
                          (props.allBaseInfoAllEdit.proList &&
                            props.allBaseInfoAllEdit.proList.indexOf(
                              item.field.code
                            ) >= 0))
                      "
                    >
                      <!--可编辑的属性-->
                      <div>
                        <!-- <div>{{ item['rule']['field'] }}</div> -->
                        <el-form-item
                          :class="item['field']['rule']"
                          :prop="item['rule']['field']"
                          :label="
                            item.field.propertyUnit != null &&
                            item.field.propertyUnit != ''
                              ? item.field.name +
                                '(' +
                                item.field.propertyUnit +
                                ')：'
                              : item.field.name + '：'
                          "
                          :key="index1"
                          style="
                            position: relative;
                            white-space: nowrap;
                            padding-left: 1.6rem;
                            margin-bottom: 1.6rem;
                          "
                          :rules="{
                            required: item.rule.request,
                            type: item.rule.type,
                            name: item.rule.name,
                            message: item.rule.message,
                            trigger: item.rule.trigger,
                            value: item['field']['value'],
                            validator: (rule, value, callback) => {
                              constMust(
                                rule,
                                props.allBaseInfoAllEdit[item.code],
                                callback,
                                item
                              );
                            }
                          }"
                        >
                          <!--                          <template #label="{ label }">-->
                          <!--                            <div-->
                          <!--                              :title="item.field.propertyUnit != null &&-->
                          <!--                      item.field.propertyUnit != ''?item.field.name +-->
                          <!--                          '(' +-->
                          <!--                          item.field.propertyUnit +-->
                          <!--                          ')：':item.field.name + '：'"-->
                          <!--                                style="width: 100%;-->
                          <!--                                    white-space: nowrap;-->
                          <!--                                    text-overflow: ellipsis;-->
                          <!--                                    overflow: hidden;-->
                          <!--                              font-weight:bold;-->
                          <!--                              margin:0;-->
                          <!--                              padding:0;-->
                          <!--                              text-align:right;-->
                          <!--                             "-->
                          <!--                            >{{item.field.propertyUnit != null && item.field.propertyUnit != ''?item.field.name +'(' +item.field.propertyUnit +')：':item.field.name + '：'}}</div>-->
                          <!--                          </template>-->
                          <!--数字-->
                          <span
                            style="width: 90%"
                            v-if="
                              item.field.showType == 'number' &&
                              item.field.isAuto != 1
                            "
                          >
                            <span v-if="item.field.dataLength != null">
                              <el-input-number
                                v-model="item.field.value"
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                                :max="item.field.dataLength"
                              ></el-input-number>
                            </span>
                            <span v-if="item.field.dataLength == null">
                              <el-input-number
                                v-model="item.field.value"
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-input-number>
                            </span>
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'windowboxTree'"
                          >
                            <el-input
                              v-model="item.field.showValue"
                              readonly
                              clearable
                              :title="item.field.showValue"
                              class="ellipsis"
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? item.field.name
                                  : ''
                              "
                              :disabled="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? false
                                  : true
                              "
                            >
                              <!-- <el-button slot="prepend" icon="el-icon-search" @click="openWindowTree(item)"></el-button> -->
                              <!-- <el-button slot="append" icon="el-icon-close" @click="clearWindowTree(item)"></el-button> -->
                            </el-input>
                          </span>

                          <!--普通输入框 input （string，number）-->
                          <span
                            style="width: 90%"
                            v-if="
                              item.field.showType == 'input' &&
                              item.field.isAuto != 1
                            "
                          >
                            <!--数据类型 input、number、text、select、checkbox、radio-->
                            <span v-if="item.field.dataType == 'string'">
                              <span v-if="item.field.dataLength != null">
                                <el-input
                                  v-model="item.field.value"
                                  :maxlength="item.field.dataLength"
                                  :placeholder="
                                    item.field.isEdit == 0
                                      ? item.field.name
                                      : ''
                                  "
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                ></el-input>
                              </span>
                              <span v-if="item.field.dataLength == null">
                                <el-input
                                  v-model="item.field.value"
                                  :placeholder="
                                    item.field.isEdit == 0
                                      ? item.field.name
                                      : ''
                                  "
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                ></el-input>
                              </span>
                            </span>
                            <span v-if="item.field.dataType == 'long'">
                              <span v-if="item.field.dataLength != null">
                                <el-input-number
                                  v-model="item.field.value"
                                  :max="item.field.dataLength"
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                ></el-input-number>
                              </span>
                              <span v-if="item.field.dataLength == null">
                                <el-input-number
                                  v-model="item.field.value"
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                ></el-input-number>
                              </span>
                            </span>
                          </span>
                          <!--富文本-->
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'textarea'"
                          >
                            <!--数据类型 input、number、text、select、checkbox、radio-->
                            <span v-if="item.field.dataLength != null">
                              <el-input
                                v-model="item.field.value"
                                type="textarea"
                                :maxlength="item.field.dataLength"
                                :placeholder="
                                  item.field.isEdit == 0 ? item.field.name : ''
                                "
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-input>
                            </span>
                            <span v-if="item.field.dataLength == null">
                              <el-input
                                v-model="item.field.value"
                                type="textarea"
                                :placeholder="
                                  item.field.isEdit == 0 ? item.field.name : ''
                                "
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-input>
                            </span>
                          </span>

                          <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                          <span
                            class="time"
                            style="width: 90%"
                            v-if="item.field.showType == 'dateTime'"
                          >
                            <el-date-picker
                              v-model="item.field.value"
                              type="datetime"
                              value-format="YYYY-MM-DD HH:mm:ss"
                              format="YYYY-MM-DD HH:mm:ss"
                              :disabled="item.field.isEdit == 0 ? false : true"
                            ></el-date-picker>
                          </span>
                          <!--日期输入框 yyyy-MM-dd-->
                          <span
                            class="time"
                            style="width: 90%"
                            v-if="item.field.showType == 'date'"
                          >
                            <el-date-picker
                              v-model="item.field.value"
                              type="date"
                              value-format="YYYY-MM-DD"
                              format="YYYY-MM-DD"
                              :disabled="item.field.isEdit == 0 ? false : true"
                            ></el-date-picker>
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'checkbox'"
                          >
                            <el-checkbox-group v-model="item.field.value">
                              <el-checkbox
                                :label="cn.value"
                                v-for="cn in item.field.enumArray"
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                                >{{ cn.label }}
                              </el-checkbox>
                            </el-checkbox-group>
                          </span>

                          <!-- 复选下拉-->
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'mul_combobox'"
                          >
                            <el-select
                              filterable
                              :multiple="true"
                              :placeholder="
                                item.field.isEdit == 0 ? '请选择' : '  '
                              "
                              v-model="item.field.value"
                              :disabled="item.field.isEdit == 0 ? false : true"
                            >
                              <el-option
                                v-for="en in item.field.enumArray"
                                :value="en.value"
                                :label="en.label"
                              ></el-option>
                            </el-select>
                          </span>
                          <span
                            style="width: 100%"
                            v-if="item.field.showType == 'comboTree'"
                          >
                            <!-- <im-select-tree
                            :placeholder="(type=='add'||type=='edit')&&item.field.isEdit==0? '请选择':'  '"
                            clearable
                            filter-on-input :model="item.field.enumArray" children-key="children" :disabled="(type=='add'||type=='edit')&&item.field.isEdit==0?false:true"
                            fixed-position :maxContentHeight=265 v-model="item.field.value" id-key="id" title-key="title" >
                          </im-select-tree> -->
                            <el-tree-select
                              :props="defaultProps"
                              :disabled="item.field.isEdit == 0 ? false : true"
                              children-key="children"
                              node-key="id"
                              node-value="id"
                              id-key="id"
                              title-key="title"
                              clearable
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? '请选择'
                                  : '  '
                              "
                              v-model="item.field.value"
                              :data="item.field.enumArray"
                              check-strictly
                              :render-after-expand="false"
                              style="width: 90%"
                            />
                          </span>
                          <span
                            v-if="item.field.showType == 'mul_windowbox'"
                            :title="item.field.showValue"
                          >
                            <el-input
                              v-model="item.field.showValue"
                              :title="item.field.showValue"
                              class="ellipsis"
                              readonly
                              clearable
                              :placeholder="
                                item.field.isEdit == 0 ? item.field.name : ''
                              "
                              :disabled="item.field.isEdit == 0 ? false : true"
                            >
                              <!-- <el-button slot="prepend" icon="el-icon-search" @click="openMulSelect(item)"></el-button> -->
                              <!-- <el-button slot="append" icon="el-icon-close" @click="clearMulSelect(item)"></el-button> -->
                            </el-input>
                          </span>

                          <!--单选-->
                          <span v-if="item.field.showType == 'radio'">
                            <el-radio-group v-model="item.field.value">
                              <el-radio
                                :value="cn.value"
                                v-for="cn in item.field.enumArray"
                              >
                                {{ cn.label }}
                              </el-radio>
                            </el-radio-group>
                          </span>
                          <span
                            v-if="
                              item.field.isCascade == 1 &&
                              item.field.activeProp == ''
                            "
                          >
                            <!--主动级联，且不被别的属性关联-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                filterable
                                :placeholder="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? '请选择'
                                    : '  '
                                "
                                v-model="item.field.value"
                                @on-change="selectActiveCase(item)"
                                clearable
                                :disabled="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? false
                                    : true
                                "
                              >
                                <el-option
                                  v-for="en in item.field.enumArray"
                                  :value="en.value"
                                  :key="en.value"
                                  :label="en.label"
                                ></el-option>
                              </el-select>
                            </span>
                            <span v-if="item.field.showType == 'windowbox'">
                              <el-input
                                v-model="item.field.showValue"
                                readonly
                                clearable
                                :title="item.field.showValue"
                                class="ellipsis"
                                :placeholder="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? item.field.name
                                    : ''
                                "
                                :disabled="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? false
                                    : true
                                "
                              >
                              </el-input>
                            </span>
                            <span v-if="item.field.showType == 'cascader'">
                              <el-cascader
                                :options="item.field.enumArray"
                                v-model="item.field.value"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                @on-change="selectActiveCase(item)"
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-cascader>
                            </span>
                          </span>

                          <span
                            v-if="
                              item.field.isCascade == 1 &&
                              item.field.activeProp != ''
                            "
                          >
                            <!--主动级联，且被别的属性关联-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                filterable
                                v-model="item.field.value"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                @on-change="selectActiveCase(item)"
                                @on-open-change="
                                  openBeSelect(item.field.value, item)
                                "
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              >
                                <el-option
                                  v-for="en in item.field.enumArray"
                                  :value="en.value"
                                  :label="en.label"
                                ></el-option>
                              </el-select>
                            </span>
                            <span v-if="item.field.showType == 'windowbox'">
                              <el-input
                                v-model="item.field.showValue"
                                readonly
                                clearable
                                :title="item.field.showValue"
                                class="ellipsis"
                                :placeholder="
                                  item.field.isEdit == 0 ? item.field.name : ''
                                "
                                :disabled="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? false
                                    : true
                                "
                              >
                                <!-- <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button> -->
                                <!-- <el-button slot="append" icon="el-icon-close" @click="clearSelect(item)"></el-button> -->
                              </el-input>
                            </span>
                            <span v-if="item.field.showType == 'cascader'">
                              <el-cascader
                                :options="item.field.enumArray"
                                v-model="item.field.value"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                @on-change="selectActiveCase(item)"
                                @on-open-change="
                                  openBeSelect(item.field.value, item)
                                "
                                clearable
                                :disabled="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? false
                                    : true
                                "
                              ></el-cascader>
                            </span>
                          </span>

                          <span
                            style="width: 90%"
                            v-if="
                              item.field.isCascade == 0 &&
                              item.field.activeProp == ''
                            "
                          >
                            <!--不是主动级联,也不被级联的-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                filterable
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                v-model="item.field.value"
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              >
                                <el-option
                                  v-for="en in item.field.enumArray"
                                  :value="en.value"
                                  :label="en.label"
                                ></el-option>
                              </el-select>
                            </span>
                            <span v-if="item.field.showType == 'windowbox'">
                              <el-input
                                v-model="item.field.showValue"
                                readonly
                                clearable
                                :title="item.field.showValue"
                                class="ellipsis"
                                :placeholder="
                                  item.field.isEdit == 0 ? item.field.name : ''
                                "
                                :disabled="
                                  (type == 'add' || type == 'edit') &&
                                  item.field.isEdit == 0
                                    ? false
                                    : true
                                "
                              >
                                <el-button
                                  slot="prepend"
                                  icon="el-icon-search"
                                  @click="openSelect(item)"
                                ></el-button>
                                <el-button
                                  slot="append"
                                  icon="el-icon-close"
                                  @click="clearSelect(item)"
                                ></el-button>
                              </el-input>
                            </span>
                            <span v-if="item.field.showType == 'cascader'">
                              <el-cascader
                                :options="item.field.enumArray"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                v-model="item.field.value"
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-cascader>
                            </span>
                          </span>
                          <span
                            v-if="
                              item.field.isCascade == 0 &&
                              item.field.activeProp != ''
                            "
                          >
                            <!--不是主动级联,但是被级联的-->
                            <!-- 下拉选 -->
                            <span v-if="item.field.showType == 'combobox'">
                              <el-select
                                filterable
                                v-model="item.field.value"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                @on-open-change="
                                  openBeSelect(item.field.value, item)
                                "
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              >
                                <el-option
                                  v-for="en in item.field.enumArray"
                                  :value="en.value"
                                  :label="en.label"
                                ></el-option>
                              </el-select>
                            </span>
                            <span v-if="item.field.showType == 'windowbox'">
                              <el-input
                                v-model="item.field.showValue"
                                readonly
                                clearable
                                :title="item.field.showValue"
                                class="ellipsis"
                                :placeholder="
                                  item.field.isEdit == 0 ? item.field.name : ''
                                "
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              >
                                <!-- <el-button slot="prepend" icon="el-icon-search" @click="openSelect(item)"></el-button> -->
                                <!-- <el-button slot="append" icon="el-icon-close" @click="clearSelect(item)"></el-button> -->
                              </el-input>
                            </span>
                            <span v-if="item.field.showType == 'cascader'">
                              <el-cascader
                                :options="item.field.enumArray"
                                v-model="item.field.value"
                                :placeholder="
                                  item.field.isEdit == 0 ? '请选择' : '  '
                                "
                                @on-open-change="
                                  openBeSelect(item.field.value, item)
                                "
                                clearable
                                :disabled="
                                  item.field.isEdit == 0 ? false : true
                                "
                              ></el-cascader>
                            </span>
                          </span>

                          <span style="width: 90%" v-if="item.field.isAuto == 1"
                            ><!--自动生成-->
                            <!-- v-if="item.field.showType != 'number'" -->
                            <el-input
                              v-if="item.field.showType != 'number'"
                              v-model="item.field.value"
                              readonly
                              :placeholder="
                                item.field.isEdit == 0 ? item.field.name : ''
                              "
                              :disabled="item.field.isEdit == 0 ? false : true"
                            />
                            <template v-if="item.field.showType == 'number'">
                              <span v-if="item.field.dataLength != null">
                                <el-input-number
                                  style="width: 100%"
                                  v-model="item.field.value"
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                  :max="item.field.dataLength"
                                ></el-input-number>
                              </span>
                              <span v-if="item.field.dataLength == null">
                                <el-input-number
                                  style="width: 100%"
                                  v-model="item.field.value"
                                  :disabled="
                                    item.field.isEdit == 0 ? false : true
                                  "
                                ></el-input-number>
                              </span>
                            </template>
                          </span>
                          <span
                            v-if="
                              item.field.titleDesc != null &&
                              item.field.titleDesc != undefined &&
                              item.field.titleDesc != '' &&
                              item.field.titleDesc != 'null'
                            "
                            style="position: absolute; top: 0px; left: -18px"
                            :title="item.field.titleDesc"
                          >
                            <i type="ios-help-circle-outline" size="15" />
                          </span>
                          <span
                            style="width: 100%"
                            v-if="item.field.showType == 'table'"
                          >
                            <div
                              style="
                                overflow: hidden;
                                display: flex;
                                align-items: center;
                              "
                            >
                              <el-upload
                                action="/rest/eam-core/zcgl-common/upload/upload"
                                ref="upload"
                                :on-success="
                                  (res, file) => {
                                    handleSuccess(res, file, item);
                                  }
                                "
                                :show-file-list="false"
                                :format="[
                                  'pdf',
                                  'txt',
                                  'doc',
                                  'docx',
                                  'xls',
                                  'xlsx',
                                  'jpeg',
                                  'jpg',
                                  'png'
                                ]"
                                :on-format-error="handleFormatError"
                                :max-size="50000"
                                style="float: left"
                                :on-exceeded-size="handleMaxSize"
                              >
                                <el-button
                                  @click="
                                    () => {
                                      item.span = 24;
                                      item.field.hide = false;
                                    }
                                  "
                                  size="small"
                                  type="primary"
                                  >附件上传</el-button
                                >
                              </el-upload>
                              <el-button
                                style="margin-left: 10px"
                                v-if="item.field.hide"
                                :icon="useRenderIcon('EP-Plus')"
                                @click="openUpload(item, item.field.code)"
                              ></el-button>
                              <el-button
                                style="margin-left: 10px"
                                v-else
                                :icon="useRenderIcon('EP-Minus')"
                                @click="openUpload(item, item.field.code)"
                              ></el-button>
                            </div>
                            <div
                              v-if="!item.field.hide"
                              style="margin-top: 10px; position: relative"
                            >
                              <el-table
                                border
                                max-height="200"
                                :data="item.field.tableList"
                              >
                                <el-table-column
                                  type="index"
                                  label="序号"
                                  width="80px"
                                  align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="文件大小"
                                  align="center"
                                  prop="fileSize"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="附件名称"
                                  align="center"
                                  prop="fileName"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="操作"
                                  align="center"
                                  prop="action"
                                  width="200"
                                >
                                  <template #default="scope">
                                    <el-button
                                      size="small"
                                      @click="
                                        showFileContent(
                                          scope.row.group,
                                          scope.row.path,
                                          scope.row.suffix,
                                          scope.row.fullPath,
                                          scope.row.fileName
                                        )
                                      "
                                      >预览</el-button
                                    >
                                    <el-button
                                      size="small"
                                      @click="
                                        download(
                                          scope.row.fullPath,
                                          scope.row.fileName
                                        )
                                      "
                                      >下载</el-button
                                    >
                                    <el-popconfirm
                                      @confirm="
                                        deleteUploadData(item, scope.row)
                                      "
                                      title="确认要删除吗?"
                                    >
                                      <template #reference>
                                        <el-button
                                          size="small"
                                          :disabled="
                                            type == 'add' || type == 'edit'
                                              ? false
                                              : true
                                          "
                                          >删除</el-button
                                        >
                                      </template>
                                    </el-popconfirm>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </span>
                          <span v-if="item.field.showType == 'objectTable'">
                            <object-table
                              :ref="el => getObjRef(el, item.field.code)"
                              :categoryIdP="categoryId"
                              :proCode="item.field.code"
                              :proOcode="item.field.ocode"
                              :type="type"
                              @validateObjectFiled="
                                validateObjectFiled(item.field.code)
                              "
                              v-model:tableData="item.field.value"
                              :tableColumn="item.field.tableList"
                            ></object-table>
                          </span>
                        </el-form-item>
                      </div>

                      <!--只读的属性-->
                      <div
                        v-if="
                          item.field != null &&
                          item.field.property == 'readonly' &&
                          item.field.propertyUnit != null &&
                          item.field.propertyUnit != ''
                        "
                      >
                        <el-form-item
                          :label="
                            item.field.propertyUnit == null ||
                            item.field.propertyUnit == ''
                              ? item.field.name +
                                '(' +
                                item.field.propertyUnit +
                                ')：'
                              : item.field.name + '：'
                          "
                          style="
                            position: relative;
                            white-space: nowrap;
                            padding-left: 1.6rem;
                            margin-bottom: 1.6rem;
                          "
                        >
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'combobox'"
                          >
                            <el-select
                              disabled
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? '请选择'
                                  : '  '
                              "
                              v-model="item.field.value"
                            >
                              <el-option
                                v-for="en in item.field.enumArray"
                                :value="en.value"
                                :label="en.label"
                              ></el-option>
                            </el-select>
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'windowbox'"
                          >
                            <el-input
                              v-model="item.field.showValue"
                              :title="item.field.showValue"
                              class="ellipsis"
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? item.field.name
                                  : ''
                              "
                              readonly
                            />
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'cascader'"
                          >
                            <el-cascader
                              :data="item.field.enumArray"
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? '请选择'
                                  : '  '
                              "
                              v-model="item.field.value"
                            ></el-cascader>
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'checkbox'"
                          >
                            <el-checkbox-group v-model="item.field.value">
                              <el-checkbox
                                :value="cn.value"
                                disabled
                                v-for="cn in item.field.enumArray"
                                :label="cn.label"
                              >
                              </el-checkbox>
                            </el-checkbox-group>
                          </span>
                          <!--单选-->
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'radio'"
                          >
                            <el-radio-group v-model="item.field.value">
                              <el-radio
                                :value="cn.value"
                                disabled
                                :key="cn.value"
                                v-for="cn in item.field.enumArray"
                              >
                                {{ cn.label }}
                              </el-radio>
                            </el-radio-group>
                          </span>

                          <span
                            style="width: 90%"
                            v-if="
                              item.field.showType != 'radio' &&
                              item.field.showType != 'checkbox' &&
                              item.field.showType != 'combobox' &&
                              item.field.showType != 'cascader'
                            "
                          >
                            <el-input
                              v-model="item.field.value"
                              class="inputBorder"
                              readonly
                            ></el-input>
                          </span>
                          <span
                            v-if="
                              item.field.titleDesc != null &&
                              item.field.titleDesc != undefined &&
                              item.field.titleDesc != '' &&
                              item.field.titleDesc != 'null'
                            "
                            style="
                              position: absolute;
                              top: 0px;
                              left: -18px;
                              width: 90%;
                            "
                            :title="item.field.titleDesc"
                          >
                            <i type="ios-help-circle-outline" size="15" />
                          </span>
                          <span
                            style="width: 90%"
                            v-if="item.field.showType == 'comboTree'"
                          >
                            <!-- <im-select-tree clearable :placeholder="(type == 'add' || type == 'edit') &&
                            item.field.isEdit == 0
                            ? '请选择'
                            : '  '
                            " filter-on-input :model="item.field.enumArray" children-key="children" fixed-position
                            :maxContentHeight="265" v-model="item.field.value" id-key="id" title-key="title">
                          </im-select-tree> -->
                            <el-tree-select
                              :props="defaultProps"
                              disabled
                              children-key="children"
                              node-key="id"
                              node-value="id"
                              id-key="id"
                              title-key="title"
                              clearable
                              :placeholder="
                                (type == 'add' || type == 'edit') &&
                                item.field.isEdit == 0
                                  ? '请选择'
                                  : '  '
                              "
                              v-model="item.field.value"
                              :data="item.field.enumArray"
                              check-strictly
                              :render-after-expand="false"
                              style="width: 90%"
                            />
                          </span>
                          <span
                            style="width: 100%"
                            v-if="item.field.showType == 'table'"
                          >
                            <div style="overflow: hidden">
                              <el-upload
                                action="/rest/eam-core/zcgl-common/upload/upload"
                                ref="upload"
                                :on-success="
                                  (res, file) => {
                                    handleSuccess(res, file, item);
                                  }
                                "
                                :show-file-list="false"
                                :format="[
                                  'pdf',
                                  'txt',
                                  'doc',
                                  'docx',
                                  'xls',
                                  'xlsx',
                                  'jpeg',
                                  'jpg',
                                  'png'
                                ]"
                                :on-format-error="handleFormatError"
                                :max-size="50000"
                                disabled
                                style="float: left"
                                :on-exceeded-size="handleMaxSize"
                              >
                                <el-button
                                  @click="
                                    () => {
                                      item.span = 24;
                                      item.field.hide = false;
                                    }
                                  "
                                  icon="ios-cloud-upload-outline"
                                  size="small"
                                  type="primary"
                                  >附件上传</el-button
                                >
                              </el-upload>
                              <el-button
                                style="margin-left: 10px"
                                v-if="item.field.hide"
                                :icon="useRenderIcon('EP-Plus')"
                                @click="openUpload(item, item.field.code)"
                              ></el-button>
                              <el-button
                                style="margin-left: 10px"
                                v-else
                                :icon="useRenderIcon('EP-Minus')"
                                @click="openUpload(item, item.field.code)"
                              ></el-button>
                            </div>
                            <div
                              v-if="!item.field.hide"
                              style="margin-top: 10px; position: relative"
                            >
                              <el-table
                                border
                                max-height="200"
                                :data="item.field.tableList"
                              >
                                <el-table-column
                                  type="index"
                                  label="序号"
                                  width="80px"
                                  align="center"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="文件大小"
                                  align="center"
                                  prop="fileSize"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="附件名称"
                                  align="center"
                                  prop="fileName"
                                >
                                </el-table-column>
                                <el-table-column
                                  label="操作"
                                  align="center"
                                  prop="action"
                                  width="150"
                                >
                                  <template #default="scope">
                                    <el-button
                                      size="small"
                                      @click="
                                        download(row.fullPath, row.fileName)
                                      "
                                      >下载</el-button
                                    >
                                    <el-popconfirm
                                      @confirm="deleteUploadData(item, row)"
                                      title="确认要删除吗?"
                                    >
                                      <template #reference>
                                        <el-button
                                          size="small"
                                          :disabled="
                                            type == 'add' || type == 'edit'
                                              ? false
                                              : true
                                          "
                                          >删除</el-button
                                        >
                                      </template>
                                    </el-popconfirm>
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </span>
                          <span v-if="item.field.showType == 'objectTable'">
                            <object-table
                              :ref="item.field.code"
                              :categoryIdP="categoryId"
                              :proCode="item.field.code"
                              :proOcode="item.field.ocode"
                              :type="type"
                              @validateObjectFiled="
                                validateObjectFiled(item.field.code)
                              "
                              :tableData.sync="item.field.value"
                              :tableColumn="item.field.tableList"
                            ></object-table>
                          </span>
                          <div
                            style="width: 100%"
                            v-if="item.field.showType == 'objectTable'"
                          >
                            <im-table
                              max-height="300"
                              :data="item.field.value"
                              ref="propTable"
                              :columns="item.field['column']"
                              border
                              id-key="sortId"
                              stripe
                            >
                              <template
                                #[item01.prop]="{ row, $index }"
                                v-for="(item01, index) in item.field['column']"
                              >
                                <!-- <div>{{item01.label}}</div> -->
                                <!-- v-if="item01.label != '操作'" -->
                                <el-form-item
                                  v-if="item01.label != '操作'"
                                  class="objectImTable"
                                >
                                  <div style="position: relative">
                                    <span
                                      v-if="item01.property == 'must'"
                                      style="
                                        color: red;
                                        position: absolute;
                                        top: 3px;
                                        left: -10px;
                                      "
                                      >*</span
                                    >
                                    <span
                                      style="width: 100%"
                                      v-if="
                                        item01.showType == 'number' &&
                                        item01.isAuto != 1
                                      "
                                    >
                                      <span v-if="item01.dataLength != null">
                                        <el-input-number
                                          style="width: 100%"
                                          v-model="row[item01.code]"
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                          :max="item01.dataLength"
                                        ></el-input-number>
                                      </span>
                                      <span v-if="item01.dataLength == null">
                                        <el-input-number
                                          style="width: 100%"
                                          v-model="row[item01.code]"
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                        ></el-input-number>
                                      </span>
                                    </span>
                                    <span v-if="item01.isAuto == 1">
                                      <span v-if="item01.dataLength != null">
                                        <el-input
                                          type="text"
                                          v-model="row[item01.code]"
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                          :max="item01.dataLength"
                                        ></el-input>
                                      </span>
                                      <span v-if="item01.dataLength == null">
                                        <el-input
                                          type="text"
                                          v-model="row[item01.code]"
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                        ></el-input>
                                      </span>
                                    </span>
                                    <span
                                      v-if="item01.showType == 'inputSelect'"
                                    >
                                      <!-- v-model="row[item01.code] == ''? row[item01.code].split(','): row[item01.code]" -->
                                      <el-input-tag
                                        v-model="row[item01.code]"
                                        @rulesInput="rulesInput(item, index)"
                                        v-if="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                        "
                                      ></el-input-tag>
                                      <!-- v-model="row[item01.code] != undefined &&
                                    row[item01.code] != null &&
                                    row[item01.code] != ''
                                    ? row[item01.code].join(',')
                                    : row[item01.code]
                                    " -->
                                      <el-input
                                        v-model="row[item01.code]"
                                        disabled
                                        v-else
                                      ></el-input>
                                    </span>
                                    <span v-if="item01.showType == 'moreInput'">
                                      <!-- <input-multiple v-model="row[item01.code]" :name="item01.name" :category-id="categoryIdP"
                                  :is-rule="item01.isRule" :ref="item01.code" @rulesInput="rulesInput(item, index)" v-if="
                                    (type == 'add' || type == 'edit') && item01.field.isEdit != 1
                                  "></input-multiple> -->
                                      <!-- <el-input disabled v-else v-model="row[item01.code] != undefined &&
                                      row[item01.code] != null &&
                                      row[item01.code] != ''
                                      ? row[item01.code].join(',')
                                      : row[item01.code]
                                    "></el-input> -->
                                    </span>
                                    <span
                                      style="width: 100%"
                                      v-if="
                                        item01.showType == 'windowboxFilter'
                                      "
                                    >
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-search"
                                          @click="openFilterSelect(item)"
                                          v-if="
                                            item01.isEdit != 1 ? true : false
                                          "
                                        ></el-button>
                                      </div>
                                      <div
                                        style="display: table-cell; width: 100%"
                                      >
                                        <el-select
                                          style="width: 100%"
                                          v-model="row[item01.code]"
                                          multiple
                                          filterable
                                          remote
                                          reserve-keyword
                                          placeholder="请输入关键词"
                                          @focus="
                                            val => clearTableList(val, item)
                                          "
                                          :disabled="
                                            item01.isEdit != 1 ? false : true
                                          "
                                          :remote-method="
                                            vul => remoteMethod(vul, item)
                                          "
                                        >
                                          <el-option
                                            v-for="item1 in item01.tableList"
                                            :key="item1.value"
                                            :label="item1.label"
                                            :value="item1.value"
                                          >
                                          </el-option>
                                        </el-select>
                                      </div>
                                      <div
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-close"
                                          v-if="
                                            item01.isEdit != 1 &&
                                            item01.isShowClear == '1'
                                              ? true
                                              : false
                                          "
                                          @click="clearSelect(item)"
                                        ></el-button>
                                      </div>
                                    </span>
                                    <span
                                      v-if="
                                        item01.showType == 'windowboxTreeFilter'
                                      "
                                    >
                                      <div
                                        v-if="item01.isEdit != 1 ? true : false"
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-search"
                                          @click="openWindowTree(item)"
                                        ></el-button>
                                      </div>
                                      <div style="display: table-cell">
                                        <el-select
                                          v-model="row[item01.code]"
                                          multiple
                                          filterable
                                          remote
                                          reserve-keyword
                                          placeholder="请输入关键词"
                                          @focus="
                                            val => clearTreeTableList(val, item)
                                          "
                                          :disabled="
                                            item01.isEdit != 1 ? false : true
                                          "
                                          :remote-method="
                                            vul => remoteTreeMethod(vul, item)
                                          "
                                        >
                                          <el-option
                                            v-for="item1 in item01.tableList"
                                            :key="item1.value"
                                            :label="item1.label"
                                            :value="item1.value"
                                          >
                                          </el-option>
                                        </el-select>
                                      </div>
                                      <div
                                        v-if="
                                          item01.isEdit != 1 &&
                                          item01.isShowClear == '1'
                                            ? true
                                            : false
                                        "
                                        style="
                                          display: table-cell;
                                          width: 44px;
                                          vertical-align: top;
                                        "
                                      >
                                        <el-button
                                          icon="el-icon-close"
                                          @click="clearWindowTree(item)"
                                        ></el-button>
                                      </div>
                                    </span>
                                    <span
                                      v-if="
                                        item01.showType == 'input' &&
                                        item01.isAuto != 1
                                      "
                                    >
                                      <!--数据类型 input、number、text、select、checkbox、radio-->
                                      <span v-if="item01.dataType == 'string'">
                                        <span v-if="item01.dataLength != null">
                                          <el-input
                                            v-model="row[item01.code]"
                                            :maxlength="item01.dataLength"
                                            :placeholder="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? item01.name
                                                : ''
                                            "
                                            :disabled="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? false
                                                : true
                                            "
                                          ></el-input>
                                        </span>
                                        <span v-if="item01.dataLength == null">
                                          <el-input
                                            v-model="row[item01.code]"
                                            :placeholder="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? item01.name
                                                : ''
                                            "
                                            :disabled="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? false
                                                : true
                                            "
                                          ></el-input>
                                        </span>
                                      </span>
                                      <span v-if="item01.dataType == 'long'">
                                        <span v-if="item01.dataLength != null">
                                          <el-input-number
                                            v-model="row[item01.code]"
                                            :max="item01.dataLength"
                                            :disabled="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? false
                                                : true
                                            "
                                          ></el-input-number>
                                        </span>
                                        <span v-if="item01.dataLength == null">
                                          <el-input-number
                                            v-model="row[item01.code]"
                                            :disabled="
                                              (type == 'add' ||
                                                type == 'edit') &&
                                              item01.isEdit != 1
                                                ? false
                                                : true
                                            "
                                          ></el-input-number>
                                        </span>
                                      </span>
                                    </span>

                                    <span v-if="item01.showType == 'textarea'">
                                      <!--数据类型 input、number、text、select、checkbox、radio-->
                                      <span v-if="item01.dataLength != null">
                                        <el-input
                                          v-model="row[item01.code]"
                                          type="textarea"
                                          :maxlength="item01.dataLength"
                                          :placeholder="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? item01.name
                                              : ''
                                          "
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                        ></el-input>
                                      </span>
                                      <span v-if="item01.dataLength == null">
                                        <el-input
                                          v-model="row[item01.code]"
                                          type="textarea"
                                          :placeholder="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? item01.name
                                              : ''
                                          "
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                        ></el-input>
                                      </span>
                                    </span>
                                    <!--日期输入框 yyyy-MM-dd HH:mm:ss-->
                                    <span
                                      class="time"
                                      v-if="item01.showType == 'dateTime'"
                                    >
                                      <el-date-picker
                                        v-model="row[item01.code]"
                                        type="datetime"
                                        value-format="YYYY-MM-DD HH:mm:ss"
                                        format="YYYY-MM-DD HH:mm:ss"
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? false
                                            : true
                                        "
                                      ></el-date-picker>
                                    </span>
                                    <!--日期输入框 yyyy-MM-dd-->
                                    <span
                                      class="time"
                                      v-if="item01.showType == 'date'"
                                    >
                                      <el-date-picker
                                        v-model="row[item01.code]"
                                        type="date"
                                        value-format="YYYY-MM-DD"
                                        format="YYYY-MM-DD"
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? false
                                            : true
                                        "
                                      ></el-date-picker>
                                    </span>

                                    <span v-if="item01.showType == 'checkbox'">
                                      <el-checkbox-group
                                        v-model="row[item01.code]"
                                      >
                                        <el-checkbox
                                          :label="cn.value"
                                          v-for="cn in item01.enumArray"
                                          :disabled="
                                            (type == 'add' || type == 'edit') &&
                                            item01.isEdit != 1
                                              ? false
                                              : true
                                          "
                                          >{{ cn.label }}
                                        </el-checkbox>
                                      </el-checkbox-group>
                                    </span>

                                    <span
                                      v-if="item01.showType == 'mul_combobox'"
                                    >
                                      <el-select
                                        filterable
                                        :multiple="true"
                                        :placeholder="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? '请选择'
                                            : '  '
                                        "
                                        v-model="row[item01.code]"
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? false
                                            : true
                                        "
                                      >
                                        <el-option
                                          v-for="en in item01.enumArray"
                                          :value="en.value"
                                          :label="en.label"
                                        ></el-option>
                                      </el-select>
                                    </span>
                                    <span v-if="item01.showType == 'comboTree'">
                                      <!-- <im-select-tree :placeholder="(type == 'add' || type == 'edit') && item01.isEdit != 1
                                  ? '请选择'
                                  : '  '
                                  " clearable filter-on-input :model="item01.enumArray" children-key="children"
                                  :disabled="(type == 'add' || type == 'edit') && item01.isEdit != 1
                                    ? false
                                    : true
                                    " fixed-position :maxContentHeight="265" v-model="row[item01.code]" id-key="id"
                                  style="width: 100%" title-key="title">
                                </im-select-tree> -->
                                      <el-tree-select
                                        :props="defaultProps"
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit == 0
                                            ? false
                                            : true
                                        "
                                        children-key="children"
                                        node-key="id"
                                        node-value="id"
                                        id-key="id"
                                        title-key="title"
                                        clearable
                                        :placeholder="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit == 0
                                            ? '请选择'
                                            : '  '
                                        "
                                        v-model="row[item01.code]"
                                        :data="item01.enumArray"
                                        check-strictly
                                        :render-after-expand="false"
                                        style="width: 90%"
                                      />
                                    </span>
                                    <!--单选-->
                                    <span v-if="item01.showType == 'radio'">
                                      <el-radio-group
                                        v-model="row[item01.code]"
                                      >
                                        <el-radio
                                          :value="cn.value"
                                          v-for="cn in item01.enumArray"
                                        >
                                          {{ cn.label }}
                                        </el-radio>
                                      </el-radio-group>
                                    </span>
                                    <span v-if="item01.showType == 'combobox'">
                                      <el-select
                                        filterable
                                        :placeholder="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? '请选择'
                                            : '  '
                                        "
                                        v-model="row[item01.code]"
                                        clearable
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? false
                                            : true
                                        "
                                      >
                                        <el-option
                                          v-for="en in item01.enumArray"
                                          :value="en.value"
                                          :label="en.label"
                                        ></el-option>
                                      </el-select>
                                    </span>
                                    <span v-if="item01.showType == 'cascader'">
                                      <el-cascader
                                        :options="item01.enumArray"
                                        :placeholder="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? '请选择'
                                            : '  '
                                        "
                                        v-model="row[item01.code]"
                                        clearable
                                        :disabled="
                                          (type == 'add' || type == 'edit') &&
                                          item01.isEdit != 1
                                            ? false
                                            : true
                                        "
                                      ></el-cascader>
                                    </span>
                                    <span
                                      v-if="
                                        item01.titleDesc != null &&
                                        item01.titleDesc != undefined &&
                                        item01.titleDesc != '' &&
                                        item01.titleDesc != 'null'
                                      "
                                      style="
                                        position: absolute;
                                        top: 0px;
                                        left: -18px;
                                      "
                                      :title="item01.titleDesc"
                                    >
                                      <i
                                        type="ios-help-circle-outline"
                                        size="15"
                                      />
                                    </span>
                                  </div>
                                </el-form-item>
                                <div
                                  v-else
                                  style="display: flex; justify-content: center"
                                >
                                  <el-button
                                    type="primary"
                                    v-if="type == 'add' || type == 'edit'"
                                    @click="
                                      addRow(
                                        item.field['column'],
                                        item.field.value
                                      )
                                    "
                                    >加</el-button
                                  >
                                  <el-button
                                    type="primary"
                                    v-if="type == 'add' || type == 'edit'"
                                    @click="deleteRow(row, item.field.value)"
                                    >减</el-button
                                  >
                                </div>
                              </template>
                              <!-- <template class="operator" #operator="{ row, $index }">
                              </template> -->
                            </im-table>
                          </div>
                        </el-form-item>
                      </div>
                    </el-col>
                  </el-row>
                </template>
              </el-form>
              <show-property
                v-else
                :instance-id="props.instanceId"
                :category-id="props.categoryId"
              ></show-property>
            </el-col>
            <el-col
              class="change-log"
              style="height: calc(100vh - 200px); overflow-y: scroll; top: 0"
              :span="5"
            >
              <el-timeline
                v-if="updateRecords.length > 0"
                ref="trackingTimeline"
                class="tracking-timeline"
              >
                <el-timeline-item
                  v-for="(changeTrack, index) in updateRecords"
                  :key="'ct-' + index"
                  class="primary-timeline-item"
                  :timestamp="changeTrack.date"
                  icon="el-icon-timer"
                  placement="top"
                >
                  <div
                    class="change-time-item"
                    v-for="(changeOfTime, j) in changeTrack.data"
                    :key="'time-' + j"
                  >
                    <div class="time-label">
                      {{ changeOfTime.time }}
                      <el-tag style="margin-left: 30%">属性变更</el-tag>
                    </div>
                    <div
                      style="display: flex; margin-bottom: 8px"
                      v-if="hasPropertyChanges(changeOfTime.data)"
                    >
                      <div class="text-items">
                        <div
                          class="text-item"
                          v-for="(item, i) in getPropertyChanges(
                            changeOfTime.data
                          )"
                          :key="i"
                        >
                          {{ item.title }}
                        </div>
                      </div>
                    </div>
                    <div
                      style="display: flex"
                      v-if="hasRelationChanges(changeOfTime.data)"
                    >
                      <el-tag type="warning">关系变更</el-tag>
                      <div class="text-items">
                        <div
                          class="text-item"
                          v-for="(item, i) in getRelationChanges(
                            changeOfTime.data
                          )"
                          :key="i"
                        >
                          {{ item.title }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>

              <el-empty v-else></el-empty>

              <div
                v-if="updateRecords.length > 0"
                class="change-more"
                @click="queryChangeTracks"
              >
                <span class="asset-more-change" style="font-size: 12px"
                  >查看更多变更记录</span
                >
                <i class="el-icon-arrow-right"></i>
              </div>
            </el-col>
          </el-row>
        </div>
        <template v-else>
          <avue-table-common
            v-if="item.id == activeName"
            :category-id="props.categoryId"
            :instance-id="props.instanceId"
            :infoTitle="props.infoTitle"
            :relation-category-id="activeName"
            @jump-to="jumpTo"
            @source-select="sourceSelect"
            :editType="props.editType"
            @query-left-tree="queryLeftTree"
            style="padding: 0 1rem"
          />
        </template>
      </el-tab-pane>
    </el-tabs>
    <!-- 文件预览弹窗 -->
    <!--    <el-dialog-->
    <!--      v-model="visibleFile"-->
    <!--      width="60%"-->
    <!--      :close-on-click-modal="false"-->
    <!--      @close="cancelHandler"-->
    <!--      :append-to-body="true">-->
    <!--      <div style="width:100%;height:600px;overflow:auto;">-->
    <!--        <audio-->
    <!--          style="-->
    <!--        width: 100%;-->
    <!--        height: 100px;-->
    <!--        padding: 30px;-->
    <!--        margin-top: 10px;-->
    <!--      "-->
    <!--          v-if="isVideo"-->
    <!--          controls-->
    <!--          :src="previewUrl"-->
    <!--        ></audio>-->
    <!--        <vue-office-docx v-if="isWord" style="padding: 20px; margin-top: 20px" :src="excelPreviewUrl" />-->
    <!--        <div style="width:500%;" v-if="isExcel">-->
    <!--          <vue-office-excel-->
    <!--            v-if="isExcel"-->
    <!--            :src="excelPreviewUrl"-->
    <!--            :options="{xls:false,minColLength:0,minRowLength:0,widthOffset:10,heightOffset:10,autoColumnWidth:true}"-->
    <!--          />-->
    <!--        </div>-->
    <!--        <div style="width:500%;" v-if="isXlsExcel">-->
    <!--        <vue-office-excel-->
    <!--          v-if="isXlsExcel"-->
    <!--          :src="excelPreviewUrl"-->
    <!--          :options="{xls:true,minColLength:0,minRowLength:0,widthOffset:10,heightOffset:10,autoColumnWidth:true}"-->
    <!--        />-->
    <!--        </div>-->
    <!--        <vue-office-pdf-->
    <!--          v-if="isPdf"-->
    <!--          :src="excelPreviewUrl"-->
    <!--        />-->

    <!--        <video-->
    <!--          style="padding: 20px; margin-top: 20px"-->
    <!--          v-if="isAudio"-->
    <!--          width="100%"-->
    <!--          height="600"-->
    <!--          controls-->
    <!--          :src="previewUrl"-->
    <!--        ></video>-->

    <!--        <div-->
    <!--          style="-->
    <!--        width: 100%;-->
    <!--        height: 600px;-->
    <!--        display: flex;-->
    <!--        justify-content: center;-->
    <!--        align-items: center;-->
    <!--      "-->
    <!--          v-if="isImage"-->
    <!--        >-->
    <!--          <img-->
    <!--            class="previewImg"-->
    <!--            :src="previewUrl"-->
    <!--            alt=""-->
    <!--            style="max-width: 100%; max-height: 700px"-->
    <!--          />-->
    <!--        </div>-->
    <!--        <div v-if="isTxt" style="white-space: pre-wrap;">-->
    <!--          {{ textContent }}-->
    <!--        </div>-->
    <!--      </div>-->
    <!--    </el-dialog>-->
    <el-dialog
      title="文件预览"
      class="prview-dialog"
      width="90%"
      style="height: 800px"
      top="5vh"
      append-to-body
      center
      v-model="visibleFile"
      :before-close="cancelHandler"
    >
      <div class="prview-container" v-loading="previewFileLoading">
        <iframe
          v-if="!!previewFileUrl"
          frameborder="0"
          width="100%"
          height="700px"
          style="overflow: auto"
          :src="previewFileUrl"
          ref="myIframe"
          @load="handleLoad"
        >
        </iframe>
        <div v-if="!!previewImageUrl" style="height: 700px; overflow: auto">
          <img :src="previewImageUrl" @load="imageLoad" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, nextTick } from "vue";
import { sm4 } from "sm-crypto";
import avueTableCommon from "@/views/modules/eam/zcgl/instance/source/component/avueTableCommon.vue";
import type {
  TabsInstance,
  ComponentSize,
  FormInstance,
  FormRules
} from "element-plus";
import { ElMessage, ElMessageBox, TabsPaneContext } from "element-plus";
import {
  checkPropertyByCodeAxios,
  saveIns,
  queryUpdateRecord,
  queryCasecadeListAxios,
  viewFile
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import ObjectTable from "@/views/modules/eam/zcgl/instance/source/component/objectTable.vue";
import { queryEnumList } from "@/views/modules/eam/zcgl/instance/source/api/propertyModelInterface";
import ShowProperty from "@/views/modules/eam/zcgl/instance/source/component/showProperty.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import GroupSvg from "../assets/group.svg";
const props = defineProps({
  tabLeftNameArray: Array<object>,
  baseInfoAllEdit: Array,
  isLoading: Boolean,
  categoryId: String,
  instanceId: String,
  allBaseInfoAllEdit: Object,
  instanceIdP: String,
  editType: String,
  selectCate: String,
  saveButtonLoading: Boolean,
  infoTitle: String
});
const isOneEnter = ref(false);
const allRules = reactive<FormRules>({});
const ruleFormRef = ref<FormInstance>();
const visibleFile = ref(false);
const previewFileUrl = ref(null);
const previewImageUrl = ref(null);
const myIframe = ref(null);
const previewFileLoading = ref(false);

//监听组件属性
watch(props, async (newValue: any) => {}, { deep: true, immediate: true });
const tabPosition = ref<TabsInstance["tabPosition"]>("left");
const activeName = ref(props.selectCate);
const load = () => {
  console.log("触底加载");
};
const refInstanceId = ref(props.instanceId);
const handleLoad = () => {
  previewFileLoading.value = false;
  if (
    !myIframe.value ||
    !myIframe.value.contentWindow ||
    !myIframe.value.contentWindow.document ||
    !myIframe.value.contentWindow.document.body ||
    !myIframe.value.contentWindow.document.body.innerHTML
  ) {
    ElMessage.error("当前文件类型不支持预览");
    cancelHandler();
  } else if (
    myIframe.value.contentWindow.document.body.innerHTML.indexOf(
      "服务异常，请重试或联系开发人员"
    ) >= 0
  ) {
    ElMessage.error("文件过大，加载失败，请下载查看");
    cancelHandler();
  }
};
const imageLoad = () => {
  previewFileLoading.value = false;
};
const renderingCompleted = () => {
  console.log("渲染完成");
};
/** 查询资产详情-变更追踪 */
const queryChangeTracks = () => {
  if (state.recordPageNum == -1) {
    ElMessage.info("已经没有数据了");
    return;
  }
  if (state.recordPageNum == 1) {
    state.updateRecords.splice(0, state.updateRecords.length);
  }
  queryUpdateRecord({
    instance_id: refInstanceId.value,
    category_id: props.categoryId,
    pageNum: state.recordPageNum++,
    pageSize: 10
  }).then(res => {
    let changeTracks = res.data || [];
    state.updateRecords.push(...changeTracks);
    if (changeTracks.length == 0) {
      if (state.recordPageNum > 2) {
        ElMessage.info("已经没有数据了");
      }
      state.recordPageNum = -1;
    }
  });
};

const cancelHandler = () => {
  visibleFile.value = false;
  previewImageUrl.value = null;
  previewFileUrl.value = null;
  previewFileLoading.value = false;
};

/** 是否存在属性变更 */
const hasPropertyChanges = items => {
  return (items || []).find(item => item.type == "1");
};

/** 是否存在属性变更 */
const hasRelationChanges = items => {
  return (items || []).find(item => item.type == "2");
};

/** 合并属性变更，如果没有返回空 */
const getPropertyChanges = items => {
  return (items || []).filter(item => item.type == "1");
};

/** 合并关系变更，如果没有返回空 */
const getRelationChanges = items => {
  return (items || []).filter(item => item.type == "2");
};

onMounted(() => {
  queryChangeTracks();
});
const validataSaveForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return false;
  await formEl.validate((valid, fields) => {
    if (valid) {
      return true;
    } else {
      return false;
    }
  });
};

// 表单数据
const formInlineData = reactive({
  value1: [],
  input: "",
  value2: ""
});

// 表单用到的所有
const defaultProps = {
  children: "children",
  label: "title"
};

const constMust = async (rule, value, callback, item) => {
  console.info(item);
  let checkParams = {};
  let requreid = item.rule.request;
  if (item.field.showType == "table") {
    if (requreid) {
      if (
        item.field.tableList instanceof Array &&
        item.field.tableList.length != 0
      ) {
        callback();
      } else {
        callback(new Error(rule.name + "不能为空"));
      }
    } else {
      callback();
    }
  } else if (item.field.showType == "objectTable") {

    if (requreid) {
      objTableRefs.value
        .get(item.field.code)
        .objRules(requreid)
        .then(() => {
          //callback();
          objTableRefs.value
            .get(item.field.code)
            .objRules(false)
            .then(ref => {
              if(ref){
                callback();
              }else{
                callback(item.field.name+"数据校验不通过");
              }

            })
            .catch(() => {
              callback(new Error(" "));
            });
        })
        .catch(() => {
          callback(new Error(rule.name + "不能为空"));
        });
    } else {
      objTableRefs.value
        .get(item.field.code)
        .objRules(requreid)
        .then(ref => {
          if(ref){
            callback();
          }else{
            callback(item.field.name+"数据校验不通过");
          }
        })
        .catch(() => {
          callback(new Error(" "));
        });
    }
  } else if (requreid) {
    checkParams.categoryId = props.categoryId;
    checkParams.name = rule.name;
    checkParams.value = item.field.value;
    checkPropertyByCodeAxios(checkParams)
      .then(res => {
        if (res.data == "success") {
          callback();
        } else if (res.data == "error1") {
          callback(new Error(rule.name + "不满足校验规则"));
        } else {
          callback(new Error(rule.name + "不能为空"));
        }
      })
      .catch(exp => {
        callback(new Error(rule.name + "不满足校验规则"));
      });
  } else {
    callback();
  }
};

const type = ref("add"); // 传递进来判断当前是新增编辑还是查看
const handleFormatError = file => {
  ElMessage.warning(
    `文件格式不正确, 文件${file.name}格式不正确，请上传pdf,txt,doc,docx,xls,xlsx,jpeg,jpg,png格式`
  );
};
const handleMaxSize = file => {
  ElMessage.warning(
    `超出文件大小范围, 文件 ${file.name} 太大了, 不允许超过 50M.`
  );
};
const download = (fullPath, fileName) => {
  window.location.href =
    "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
    fullPath +
    "&fileName=" +
    fileName;
};

const showFileContent = (group, filePath, suffix, fullPath, fileName) => {
  visibleFile.value = true;
  previewFileLoading.value = true;
  // let params = {
  //   group: group,
  //   path: filePath,
  //   suffix: suffix
  // }
  if (
    suffix.endsWith("jpg") ||
    suffix.endsWith("png") ||
    suffix.endsWith("jpeg") ||
    suffix.endsWith("svg") ||
    suffix.endsWith("JPG") ||
    suffix.endsWith("PNG")
  ) {
    previewImageUrl.value =
      "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
      fullPath +
      "&fileName=" +
      fileName;
  } else {
    previewFileUrl.value =
      "/rest/eam-core/zcgl-common/file/viewFiles?group=" +
      group +
      "&path=" +
      filePath +
      "&suffix=" +
      suffix;
  }
  // viewFile(params).then(res => {
  //
  //
  // }).catch(err => {
  //   console.info(err);
  // });
};

// const showFileContent = (fullPath, fileName) => {
//   visibleFile.value = true;
//   let fileExtension = fileName.split(".")[fileName.split(".").length-1];
//   let url = "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
//     fullPath +
//     "&fileName=" +
//     fileName;
//   if (fileExtension == "mp3") {
//     previewUrl.value = url;
//     isVideo.value = true;
//   } else if (fileExtension == "mp4") {
//     previewUrl.value = url;
//     isAudio.value = true;
//   } else if (fileExtension == "xlsx") {
//     isExcel.value = true;
//     excelPreviewUrl.value = "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
//       fullPath +
//       "&fileName=" +
//       fileName;
//
//   }else if(fileExtension == "xls"){
//     isXlsExcel.value = true;
//     excelPreviewUrl.value = "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
//       fullPath +
//       "&fileName=" +
//       fileName;
//   } else if(fileExtension == "pdf"){
//     excelPreviewUrl.value = "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
//       fullPath +
//       "&fileName=" +
//       fileName;
//     isPdf.value = true;
//   } else if(fileExtension == "doc" || fileExtension == "docx") {
//     excelPreviewUrl.value = "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
//       fullPath +
//       "&fileName=" +
//       fileName;
//     isWord.value = true;
//   } else if(fileExtension === 'txt' || fileExtension === 'TXT' || fileExtension === 'sql' || fileExtension === 'log'){
//     isTxt.value = true;
//     axios.get(url, {
//       responseType: 'text'
//     }).then(res => {
//       textContent.value = res.data
//     })
//
//   } else if(fileExtension == 'jpg'
//     || fileExtension == 'png'
//     || fileExtension == 'jpeg'
//     || fileExtension == 'svg'
//     || fileExtension == 'JPEG'
//     || fileExtension == 'JPG') {
//     previewUrl.value = url;
//     // 显示预览
//     isImage.value = true;
//   } else{
//     ElMessage.error("暂不支持此格式文件预览");
//     visibleFile.value = false;
//   }
// }
const openUpload = (item, id) => {
  item.field.hide = !item.field.hide;
  // this.buttonIcon=item.field.hide?"el-icon-remove-outline": "el-icon-circle-plus-outline";
  // this.$set(item.field, "hide", item.field.hide);
};
const handleSuccess = (res, file, item) => {
  const result = res.data.result;
  const code = res.data.code;
  if (code == "1") {
    res.attributeId = item.field.code;
    const tableList = item.field.tableList;
    for (let x = 0; x < tableList.length; x++) {
      const rest = tableList[x];
      if (
        rest.fileSize == res.data.fileSize &&
        rest.fileName == res.data.fileName
      ) {
        ElMessage.error("重复文件，不允许上传");
        return false;
      }
    }
    item.field.tableList.push(res.data);
    if (item.field.hide) {
      openUpload(item, item.field.code);
    }
  }
  ElMessage.success(result);
};
const deleteUploadData = (item, row) => {
  item.field.tableList.splice(row._index, 1);
  ElMessage.success("删除成功！");
};
const addRow = (column, tableVal) => {
  const cc = {};
  for (let i = 0; i < column.length; i++) {
    const showType = column[i].showType;
    if (showType == "number" || showType == "comboTree") {
      cc[column[i].code] = null;
    } else if (
      showType == "windowboxFilter" ||
      showType == "windowboxTreeFilter" ||
      showType == "inputSelect" ||
      showType == "moreInput" ||
      showType == "objectTable"
    ) {
      cc[column[i].code] = [];
    } else {
      cc[column[i].code] = "";
    }
  }
  cc["sortId"] = tableVal.length + 1;
  tableVal.push(cc);
  // this.tableFrom.tableData = props.tableData;
};
const deleteRow = (row, tableVal) => {
  console.log(row);
  if (tableVal.length == 1) {
    ElMessage.error("最少保留一行");
  } else {
    var tt = 0;
    for (var i = 0; i < tableVal.length; i++) {
      if (tableVal[i].sortId == row.sortId) {
        tt = i;
        break;
      }
    }
    tableVal.splice(tt, 1);
    for (var i = 0; i < tableVal.length; i++) {
      tableVal[i].sortId = i + 1;
    }
  }
};

const validateObjectFiled = code => {
  ruleFormRef.value[0].validateField(code);
};

const state = reactive({
  // 操作
  operator: {
    label: "操作",
    fixed: "right",
    width: "166"
  },
  saveInstanceLoading: false,
  recordPageNum: 1,
  updateRecords: [],
  windowboxloading: false,
  //current.windowSelect = item.filed;
  currentItem: {},
  currentId: "",
  ocode: "",
  show_type: "",
  data: [],
  windowSelect: {},
  totalW: 0,
  selectDataModal: false,
  object: {},
  propValue: "",
  currentPageW: 1,
  pageSizeW: 10
});

const {
  operator,
  saveInstanceLoading,
  recordPageNum,
  updateRecords,
  windowboxloading,
  currentItem,
  currentId,
  ocode,
  show_type,
  data,
  windowSelect,
  totalW,
  selectDataModal,
  object,
  propValue,
  currentPageW,
  pageSizeW
} = toRefs(state);

const rulesInput = (item, code) => {
  if (item.field.isRule == "1") {
    const response = ruleFormRef.value[0].validateField(code);
    if (response.data == "success") {
      return true;
    } else {
      return false;
    }
  }
};
const saveInstance = () => {
  emit("update:saveButtonLoading", true);
  if (!ruleFormRef) {
    ElMessage.error("表单对象为空");
    return;
  }
  try {
    ruleFormRef.value[0].validate(valid => {
      if (valid) {
        for (let item of props.allBaseInfoAllEdit.rowsObj || []) {
          for (let items of item.rows || []) {
            for (let key of items.colList || []) {
              if (
                key.field.showType == "password" &&
                key.field.value != null &&
                key.field.value != ""
              ) {
                key.field.value = sm4.encrypt(key.field.value, sm4Key);
              }
            }
          }
        }
        saveInstanceForm();
      } else {
        ElMessage.error("表单检验不通过！");
        emit("update:saveButtonLoading", false);
      }
    });
  } catch (exp) {
    console.log(exp);
  }
};
const emit = defineEmits([
  "updateInstance",
  "jump-to",
  "source-select,query-left-tree",
  "update:saveButtonLoading",
  "initInstanceData"
]);
const sm4Key = "1B6E03BAE001B71D1AC6377806E2FF63";
const objTableRefs = ref(new Map());
const jumpTo = page => {
  emit("jump-to", page);
};
const sourceSelect = data => {
  emit("source-select", data);
};
const getObjRef = (el: any, code: String) => {
  objTableRefs.value.set(code, el);
};
const queryLeftTree = () => {
  emit("query-left-tree");
};
const saveInstanceForm = () => {
  state.saveInstanceLoading = true;
  let params = JSON.parse(JSON.stringify(props.allBaseInfoAllEdit));
  for (let item of params.rowsObj || []) {
    for (let items of item.rows || []) {
      for (let key of items.colList || []) {
        key.field.enumArray = [];
      }
    }
  }
  saveIns(params)
    .then(res1 => {
      if (res1.data.message == "success") {
        for (let item of props.allBaseInfoAllEdit.rowsObj || []) {
          for (let items of item.rows || []) {
            for (let key of items.colList || []) {
              for (let keys of res1.data.propertyDtoList || []) {
                key.field.instanceId = res1.data.id;
                if (key.field.code == keys.code) {
                  key.field.value = keys.value;
                  if (keys.isReEdit == 0) {
                    key.field.isEdit = 1;
                  }
                }
              }
            }
          }
        }
        emit("updateInstance", res1.data);
        emit("update:saveButtonLoading", false);
        state.saveInstanceLoading = false;
        ElMessage.success("保存成功");
        props.allBaseInfoAllEdit.id = res1.data.id;
        state.recordPageNum = 1;
        refInstanceId.value = res1.data.id;
        queryChangeTracks();
        for (let item of props.allBaseInfoAllEdit.rowsObj || []) {
          for (let items of item.rows || []) {
            for (let key of items.colList || []) {
              if (
                key.field.showType == "password" &&
                key.field.value != null &&
                key.field.value != ""
              ) {
                key.field.value = sm4.decrypt(key.field.value, sm4Key);
              }
            }
          }
        }
      } else {
        ElMessageBox.alert("保存失败<br/>" + res1.data.data, "保存失败", {
          dangerouslyUseHTMLString: true
        });
        state.saveInstanceLoading = false;
        emit("update:saveButtonLoading", false);
      }
    })
    .catch(exp => {
      ElMessage.error("保存失败:" + exp.message);
    });
};
const changeInfoTree = (pane: TabsPaneContext, ev: Event) => {
  if (pane.props.name == "-1") {
    emit("initInstanceData");
    state.recordPageNum = 1;
    queryChangeTracks();
  }
};
const caseObject = ref([]);
const openBeSelect = (val, item) => {
  if (val) {
    let code = item.field.activeCode;
    let value = "";
    if (caseObject.value.length > 0) {
      for (let i = 0; i < caseObject.value.length; i++) {
        if (caseObject.value[i].key == item.field.activeProp) {
          value = caseObject.value[i].value;
          break;
        }
      }
      let params = {};
      params.code = code;
      params.value = value;
      queryCasecadeListAxios(params)
        .then(res => {
          item.field.enumArray = eval("(" + res.data + ")");
        })
        .catch(exp => {
          ElMessage.error(exp.message);
        });
    }
  }
};
const selectActiveCase = item => {
  updateActiveCase(item);
};
const updateActiveCase = item => {
  if (caseObject.value.length > 0) {
    let obj = caseObject.value.filter(p => {
      //根据当前变动的主动发起关联的属性，遍历已缓存的所有主动关联的key和value
      return p.key == item.field.code;
    });
    if (obj != null && obj.length > 0 && undefined != item.field.value) {
      //如果此属性存在，更新值
      obj[0].value = item.field.value;
    } else {
      //如果不存在，放入主动发起的缓存中
      let json = {
        key: item.field.code,
        value: item.field.value
      };
      caseObject.value.push(json);
    }
  } else {
    let json = {
      key: item.field.code,
      value: item.field.value
    };
    caseObject.value.push(json);
  }
};
const openSelect = item => {
  if (item.field.activeProp != "") {
    //如果是被级联的的
    openBeCaseWindow(item);
  } else {
    getObjectPage(item);
  }
};
const openBeCaseWindow = item => {};
const windowTable = ref(null);
const getObjectPage = item => {
  if (item.field.ocode) {
    state.windowboxloading = true;
    //current.windowSelect = item.filed;
    state.currentItem = item;
    state.currentItem.value = item.field.value;
    state.currentItem.label = item.field.showValue;
    state.currentId = item.field.value;
    state.ocode = item.field.ocode;
    state.show_type = item.field.showType;
    var params = {
      show_type: "windowbox",
      ocode: item.field.ocode,
      pageSize: state.object.size,
      pageNum: state.object.page,
      value: state.propValue
    };
    queryEnumList(params).then(res => {
      state.selectDataModal = true;
      state.data = res.data.data;
      //current.object.total = res.data.total;

      state.totalW = res.data.total;
      state.windowboxloading = false;
      state.windowSelect = {};
      state.windowSelect.value = item.field.value;
      state.windowSelect.label = item.field.showValue;
      state.windowSelect.id = item.field.value;

      nextTick(() => {
        if (
          state.windowSelect.value != undefined &&
          state.windowSelect.value != ""
        ) {
          for (var i = 0; i < state.data.length; i++) {
            if (state.data[i].value == state.windowSelect.value) {
              windowTable.value.toggleRowSelection(state.data[i], true);
            }
          }
        }
      });
    });
  }
};
const clearSelect = item => {
  item.field.value = "";
  item.field.showValue = "";
  state.currentItem.field.value = "";
  state.currentItem.field.showValue = "";
  if (state.currentItem.field.isCascade === 1) {
    //如果是主动发起的需要更新缓存
    updateActiveCase(state.currentItem);
  }

  closePropSelect();
  //item.field.value = "";
  //item.field.showValue = "";
};
const closePropSelect = () => {
  state.selectDataModal = false;
  state.propValue = "";
  state.data = [];
  state.windowSelect = null;
  //this.currentId = '';
  state.currentPageW = 1;
  state.totalW = 0;
  state.pageSizeW = 10;
};
defineExpose({
  saveInstance
});
</script>

<style lang="scss" scoped>
.assetBaseInfo {
  // readonly
  .addPropertyEdit {
    :deep(.readonlyInput) {
      .el-input__inner {
        cursor: not-allowed !important;
      }
    }
    :deep(.objectImTable) {
      .el-form-item__content {
        margin-left: 0 !important;
        width: 100%;

        > div {
          width: 100%;
        }
      }
    }
    :deep(.el-form-item__label) {
      //display: inline-block;
      //width: 100%;
      //white-space: nowrap;
      //text-overflow: ellipsis;
      //overflow: hidden;
      //font-weight:bold;
      //text-align: right;
    }
  }
}

:deep(.base-tabs) {
  //   padding-top: 1rem;
  .el-tabs__item {
    font-size: 13px !important;
    justify-content: left;
    padding-left: 1rem;
  }

  .el-form {
    .topDivider {
      margin-top: 1rem;
    }
  }

  .el-divider__text {
    font-size: 16px;
    font-weight: 500;

    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 18px;
      background: inherit;
      background-color: #409eff;
      border: none;
      border-radius: 8px;
      vertical-align: -3px;
      margin-right: 6px;
    }
  }

  .time {
    div.el-input {
      width: 100% !important;
    }
  }
}
.tracking-timeline {
  padding-left: 10px;
  overflow: auto;
  scroll-behavior: smooth;

  :deep(.el-timeline-item__icon) {
    background: unset !important;
    color: unset !important;
    font-size: 15px !important;
    margin-left: -2px;
  }

  :deep(.el-timeline-item__wrapper) {
    .el-timeline-item__timestamp {
      margin-left: -10px;
      margin-bottom: 10px;
      font-size: 12px;
      font-family:
        PingFang SC-Bold,
        PingFang SC;
      font-weight: bold;
      color: #3c4a54;
      line-height: 14px;
    }

    .el-timeline-item__content {
      flex: 2;

      .change-time-item {
        margin-bottom: 15px;
        .time-label {
          font-size: 14px;
          font-family: DIN-Medium, DIN;
          font-weight: 500;
          color: #6f6e89;
          line-height: 14px;
          margin: 8px 0;
        }
        .text-items {
          margin-left: 3px;
          .text-item {
            font-size: 14px;
            font-family:
              PingFang SC-Bold,
              PingFang SC;
            line-height: 18px;
            word-break: break-all;
            padding-bottom: 4px;
          }
        }
      }
    }
  }
}
.change-more {
  font-size: 14px;
  font-family:
    PingFang SC-Bold,
    PingFang SC;
  font-weight: 500;
  color: var(--el-color-primary);
  line-height: 14px;
  margin: 20px;
  cursor: pointer;
}
</style>
