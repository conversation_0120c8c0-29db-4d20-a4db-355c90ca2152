<template>
  <div class="selectPopover">
    <el-select v-model="selectValue"
               popper-append-to-body
               multiple
               filterable
               remote
               reserve-keyword
               placeholder="请输入关键词"
               ref="selectPopover"
               @change="selectChange"
               :remote-method="remoteMethod"
               @focus="clearTreeTableList"
               style="width: 100%">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :title="item.tooltip"
        :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>

<script setup lang="ts">

import {computed, nextTick, watch,ref} from "vue";

const props = defineProps({
  value: Array,
  options: Array,
})
const selectValue = ref(null);
const selectPopover = ref();
watch(props.value, (newValue) => {
  selectValue.value = newValue;
  let arr = [];
  for (let key of newValue) {
    for (let item of props.options) {
      if (item.value == key) {
        arr.push(item.tooltip);
      }
    }
  }
},{immediate: true, deep: true});
const emit = defineEmits(["focus","remote-method","input"]);
const clearTreeTableList = (val) => {
  emit("focus", val);
}
const remoteMethod = (val) => {
  emit("remote-method", val);
}
const  selectChange = (val) =>{
  let arr = [];
  for (let key of val) {
    for (let item of props.options) {
      if (item.value == key) {
        arr.push(item.tooltip);
      }
    }
  }
  emit("input", val);
}
</script>
<style lang="scss" scoped>
.selectPopover {
  .el-main {
    padding: 0;
    margin-left: 6px;
  }

}
</style>
