<template>
    <div>
        <!-- <div class="title" style="display: flex; align-items: center" slot="title">
            <h2 class="label" style="margin-left: 10px;padding-bottom: 1rem;">{{ categoryName }}导入</h2>
        </div> -->
        <el-row v-if="importVersion != '1'">
            <div style="height:110px;">
                <el-row>
                    <el-form :model="importForm" ref="importFormRef" label-width="80px" :rules="ruleImportForm">
                        <el-row v-if="currentCate=='-2'">
                          <el-form-item label="关系类别" :rules="{required:true,message:'请选择关系类别',trigger:'change'}">
                            <el-select style="width: 10rem;" clearable filterable v-model="selectCate" @change="changeCate">
                              <el-option v-for="(item,index) in categoryList" :label="item.label" :value="item.value" :key="index"></el-option>
                            </el-select>
                          </el-form-item>
                        </el-row>
                        <el-row>
                            <!-- <div>{{ importForm }}</div> -->
                            <el-form-item prop="primary" label="导入主键">
                                <el-select style="width: 10rem;" v-model="importForm.primary" filterable>
                                    <el-option v-for="(item, index) in props" :label="item.label" :value="item.value"
                                        :key="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-form-item prop="importType" label="导入规则">
                                <el-radio-group v-model="importForm.importType">
                                    <el-radio value="add">仅新增</el-radio>
                                    <el-radio value="update">仅更新</el-radio>
                                    <el-radio value="addUpdate">新增并更新</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-row>
                    </el-form>
                </el-row>
            </div>
        </el-row>
        <div class="asset-import" v-loading="loading"
            style="padding: 0 20px; height: 99%; position: relative; overflow: auto;margin-top:20px;">
            <el-upload ref="uploader" class="asset-upload" :headers="uploadHeaders" :data="extraParams"
                :on-error="onUploadError" :on-success="onUploadSuccess" :on-progress="onFileProgress"
                :before-upload="beforeUpload" drag :limit="1" accept=".xlsx"
                :action="'/rest/eam-core/zcgl-common/instance/importNew?token='+token" multiple>
              <el-icon class="el-icon-upload" style="font-size:80px;color:#999;"><upload-filled /></el-icon>
              <div class="el-upload__text"><em>点击上传文件</em> 或者拖拽上传</div>
              <div class="upload-desc">只能上传excel文件</div>
            </el-upload>
            <div style="display: flex; align-items: center;margin: 5px 0;">
                <!--<img class="icon" src="../../images/download.png" style="margin-right: 5px"/>-->
                <el-button type="text" @click="exportTemplate">资产导入模板下载</el-button>
            </div>
            <div>
                <div class="import-bz" style="font-weight: bold;">平台提供两种导入方式
                </div>
                <div class="import-bz">1、导出资产数据后，可直接导入系统
                </div>
                <div class="import-bz">2、下载资产导入模板，填充数据后导入</div>
            </div>
            <el-row style="margin-top: 10px">
                <el-col :span="12">

                    <el-button type="primary" @click="fileListClick">查看导入记录（近三天）</el-button>
                </el-col>
                <el-col :span="12" style="text-align: right">
                    <span style="font-size: 13px;">只看导入失败</span>
                    <el-switch style="margin-left: 1rem;" v-model="value" @change="valueChange" active-color="#13ce66" inactive-color="#ff4949">
                    </el-switch>
                </el-col>
            </el-row>
            <div v-show="fileListShow">
                <el-table :data="fileListComp" style="height:16rem;    overflow: auto;">
                    <template v-for="item of fileColumns">
                        <el-table-column :prop="item.prop" :key="item.prop" :label="item.label"
                            v-if="item.label != '执行结果'" align="center" showOverflowTooltip>
                        </el-table-column>
                        <el-table-column v-else :key="item.prop" :label="item.label" align="center" showOverflowTooltip>
                            <template #default="scope">
                                <span
                                    v-if="scope.row.errorData && scope.row.index == 0 && Array.isArray(scope.row.errorData) && scope.row.errorData.length == 0">执行成功</span>
                                <span v-else-if="!scope.row.errorData && scope.row.index == 0">执行中</span>
                                <span v-else-if="!scope.row.errorData && scope.row.index > 0">排队中</span>
                                <el-button type="text" v-else
                                    @click="resultsEnforcementClick(scope.row)">执行失败</el-button>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
                <el-pagination background @size-change="addedDisRecordsSizeChange"
                    @current-change="addedDisRecordsCurrentChange" :current-page.sync="addedDisRecordsPageNum"
                    :page-sizes="[10, 20, 50]" :page-size="addedDisRecordsPageSize" layout="total, prev, pager, next"
                    :total="addedDisRecordsTotal">
                </el-pagination>
            </div>
            <div v-show="!fileListShow">
                <el-collapse v-model="activeName" v-if="validataResult">
                    <el-collapse-item :title="validataResult" name="1">
                        <div v-for="item in errorData" :key="item">
                            {{ item }}
                        </div>
                    </el-collapse-item>
                    <el-collapse-item title="导入处理结果" name="2" v-if="summaryInfo.dataImportBatchTotal">
                        <el-form :model="summaryInfo" ref="summaryInfo" label-width="150px">
                            <el-row>
                                <el-col span="8">
                                    <el-form-item label="数据导入总条数" prop="dataImportTotal">
                                        <el-tag type="success">
                                            {{ summaryInfo.dataImportTotal }}
                                        </el-tag>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="数据导入新增总条数" prop="dataImportInsert">
                                        <el-tag type="success">
                                            {{ summaryInfo.dataImportInsert }}
                                        </el-tag>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="数据导入更新总条数" prop="dataImportUpdate">
                                        <el-tag type="success">
                                            {{ summaryInfo.dataImportUpdate }}
                                        </el-tag>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <br>
                        </el-form>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </div>
        <el-dialog destroy-on-close v-model="resultsEnforcementDialog" append-to-body title="执行结果" :close-on-click-modal="false">

            <el-collapse v-model="activeName">
                <el-collapse-item :title="validataResult" name="1">
                    <div v-for="item in errorData" :key="item">
                        {{ item }}
                    </div>
                </el-collapse-item>
                <el-collapse-item title="导入处理结果" name="2" v-if="summaryInfo.dataImportBatchTotal">
                    <el-form :model="summaryInfo" ref="summaryInfo" label-width="150px">
                        <el-row>
                            <el-col span="8">
                                <el-form-item label="数据导入总条数" prop="dataImportTotal">
                                    <el-tag type="success">
                                        {{ summaryInfo.dataImportTotal }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="数据导入新增总条数" prop="dataImportInsert">
                                    <el-tag type="success">
                                        {{ summaryInfo.dataImportInsert }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="数据导入更新总条数" prop="dataImportUpdate">
                                    <el-tag type="success">
                                        {{ summaryInfo.dataImportUpdate }}
                                    </el-tag>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <br>
                        <el-row>
                            <el-col :span="24">
                                <el-form-item label="错误数据" prop="errorData">
                                    <div style="height: 200px;overflow: auto">
                                        <el-row v-for="(item, index) in summaryInfo.errorData" :key="index">
                                            <el-col :span="3">
                                                <el-tooltip :content="item.reason">
                                                    <el-tag type="danger">
                                                        {{ item.reason }}
                                                    </el-tag>
                                                </el-tooltip>
                                            </el-col>
                                            <el-col :span="21">
                                                <el-popover trigger="hover" title="详细信息" width="500">
                                                    <el-tag type="success">
                                                        {{ item.data }}
                                                    </el-tag>
                                                    <div slot="content" style="height: 200px;">
                                                        {{ item.data }}
                                                    </div>
                                                </el-popover>
                                            </el-col>
                                        </el-row>
                                    </div>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-collapse-item>
            </el-collapse>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs, watch, onMounted, onUnmounted, computed } from "vue";
import { getToken } from "@/utils/auth";
import {
    exportInsTemp, queryImportRuleAxios, queryImportVersionAxios, queryPropertyByIdAxios
} from "@/views/modules/eam/zcgl/instance/source/api/instanceAssetImport";
import { ElMessage } from "element-plus";
import {UploadFilled} from "@element-plus/icons-vue";
import {queryRelationCategoryEnum} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";

// 所有props
const allProps = defineProps({
    visible: Boolean,
    categoryId: [String, Number],
    categoryName: String,
    relationCategoryId:String,
    srcInstanceId:String
});

// 所有数据
const showImportDialog = ref(false);
const importFormRef = ref(null);
const ruleImportForm = reactive({
    importType: [
        { required: true, trigger: 'blur', msg: "请选择导入规则" }
    ],
    primary: [
        { required: true, trigger: 'blur', msg: "请选择导入主键" }
    ],
});
const props = ref([]);
const importForm = reactive({
    primary: "",
    importType: ""
});
const token = ref("");
const loading = ref(false);
const activeName = ref("1");
const fileListShow = ref(false);
const value = ref(false);
const resultsEnforcementDialog = ref(false);
const errorData = ref([]);
const validataResult = ref("");
const msgResult = ref("");
const drawer = ref(false);
const fileList = ref([]);
const fileLisAll = ref([]);
const fileColumns = reactive([
    { label: "上传文件", prop: "fileName", align: "center", showOverflowTooltip: true },
    { label: "上传时间", prop: "startTime", align: "center", showOverflowTooltip: true },
    { label: "当前排队", prop: "importNum", align: "center", showOverflowTooltip: true },
    { label: "执行完成时间", prop: "endTime", align: "center", showOverflowTooltip: true },
    { label: "执行结果", slot: "resultsEnforcement", align: "center", showOverflowTooltip: true },
]);
const importStateResult = ref("none");
const fileName = ref("未选择");
const percent = ref(0);
const uploaded = ref("");
const total = ref("");
const showUploadProgress = ref(false);
const groupId = ref("");
const showErrorMsg = ref(false);
const errorMsgList = ref([]);
const exceptionMsg = ref("");
const totalModal = ref(false);
const summaryInfo = ref({});
const source = ref(null);
const sourceSuccess = ref(null);
const addedDisRecordsPageSize = ref(10);
const addedDisRecordsPageNum = ref(1);
const importVersion = ref("");
const categoryList = ref([]);

// 计算属性
const fileListComp = computed(() => {
    let arrReturn = [];
    const begin = (addedDisRecordsPageNum.value - 1) * addedDisRecordsPageSize.value;
    const end = addedDisRecordsPageNum.value * addedDisRecordsPageSize.value;
    const arr = JSON.parse(JSON.stringify(fileLisAll.value));
    if (value.value) {
        fileList.value = arr.filter(val => {
            return (Array.isArray(val.errorData) && val.errorData.length != 0);
        });
    } else {
        fileList.value = arr;
    }
    arrReturn = fileList.value.slice(
        begin,
        end,
    );
    return arrReturn;
})

const addedDisRecordsTotal = computed(() => {
    return fileList.value.length || 0;
})

const extraParams = computed(() => {
    if(currentCate.value!='-2'){
      return {
        srcInstanceId:allProps.srcInstanceId,
        categoryId: allProps.relationCategoryId,
        isAuth: true,
        importPrimary: importForm.primary,
        importType: importForm.importType
      };
    }else{
      return {
        srcInstanceId:allProps.srcInstanceId,
        categoryId: selectCate.value,
        isAuth: true,
        importPrimary: importForm.primary,
        importType: importForm.importType
      };
    }

})

const selectCate = ref("");
// 所有方法
const uploadHeaders = () => {
    return { "Authorization": getToken().accessToken,"token": getToken().accessToken};
}

const queryImportRule = () => {
    queryImportRuleAxios(allProps.relationCategoryId).then((res: any) => {
        importForm.primary = res.data.importPrimary;
        importForm.importType = res.data.importType;
    }).catch(exp => {
        console.log(exp);
    })
}

const queryImportVersion = () => {
    queryImportVersionAxios().then(res => {
        importVersion.value = res['data'];
    }).catch(exp => {
        console.log(exp);
    })
}

const initProps = () => {
    queryPropertyByIdAxios(allProps.relationCategoryId).then(res => {
        props.value = res['data'];
    }).catch(exp => {
        console.log(exp);
    })
}

const addedDisRecordsSizeChange = (val) => {
    addedDisRecordsPageSize.value = val;
}

const addedDisRecordsCurrentChange = (val) => {
    addedDisRecordsPageNum.value = val;
}

const valueChange = (val) => {
    const arr = JSON.parse(JSON.stringify(fileLisAll));
    if (val) {
        fileList.value = arr.filter(val => {
            return (Array.isArray(val.errorData) && val.errorData.length != 0);
        });
    } else {
        fileList.value = arr;
    }
}

const fileListClick = () => {
    fileListShow.value = !fileListShow.value;
    if (fileListShow.value) {
        if (source.value) {
            source.value.close();
            source.value = null;
        }
        source.value = new EventSource(
            "/rest/eam-core/zcgl-common/instance/queryImportStatusByUser?token=" + getToken().accessToken,
        );
        source.value.addEventListener("data", data => {
            let json = JSON.parse(data.data);
            console.info(json);
            fileList.value = json.data;
            fileLisAll.value = json.data;
        });
        source.value.addEventListener("close", data => {
            source.value.close();
        });
    }
}

const resultsEnforcementClick = (row) => {
    errorData.value = [];
    validataResult.value = "";
    errorData.value = row.errorData;
    if (row.importResult) {
        summaryInfo.value = JSON.parse(row.importResult);
    } else {
        summaryInfo.value = {};
    }
    validataResult.value = row.validataResult;
    resultsEnforcementDialog.value = true;
}

const handleTotal = () => {
    totalModal.value = true;
    var params = {
        groupId: groupId.value,
    };

}

const emit = defineEmits(["update:visible", "closeImport"])
/** 关闭抽屉 */
const handleDrawerClose = () => {
    drawer.value = false;
    emit("update:visible", false);
    emit("closeImport");
}

const clearFormValidates = () => {
}

/** 导出模板 */
const exportTemplate = () => {
  if(currentCate.value!='-2'){
    const params = {};
    params['categoryId'] = allProps.relationCategoryId;
    exportInsTemp(params)
      .then(res => {
        ElMessage.success("导出模板成功");
      })
      .catch(() => {
        ElMessage.error("导出模板失败，请联系管理员");
      });
  }else{
    if(selectCate.value){
      const params = {};
      params['categoryId'] = selectCate.value;
      exportInsTemp(params)
        .then(res => {
          ElMessage.success("导出模板成功");
        })
        .catch(() => {
          ElMessage.error("导出模板失败，请联系管理员");
        });
    }else{
      ElMessage.error("请先选择关联类别");
    }
  }

}

const uploader = ref(null);
const onUploadError = (err, file, fileList) => {
    loading.value = false;
    let message = err.message;
    showErrorMsg.value = true;
    try {
        let errData = JSON.parse(message);
        let msg = errData && errData.msg;
        let errorMsgList01 = null;
        try {
            errorMsgList01 = JSON.parse(msg);
            if (Array.isArray(errorMsgList01)) {
                errorMsgList.value = errorMsgList01;
            } else {
                exceptionMsg.value = msg;
            }
        } catch (e) {
            // 服务异常
            exceptionMsg.value = msg;
        }
    } catch (error) {
        exceptionMsg.value = message;
    }
    console.error(exceptionMsg.value);
    uploader.value.clearFiles();
}

const onUploadSuccess = (response, file, fileList) => {
    loading.value = false;
    // this.$message.info(response);
    if (response.data.result == 'success') {
        fileListShow.value = false;
        fileListClick();
    } else {
        msgResult.value = response.data.message;
        ElMessage.error(response.data.message);
    }
    uploader.value.clearFiles();
}

/**钩子函数： 进度*/
const onFileProgress = (event, file, fileList) => {
    percent.value = Number(event.percent.toFixed(2));
}

const beforeUpload = (file) => {
  console.log("进入导入前判断")
    showErrorMsg.value = false;
    errorMsgList.value = [];
    exceptionMsg.value = "";
    if (!importForm.primary) {
        ElMessage.error("请选择导入主键");
        return false;
    }
    if(currentCate.value=='-2'){
      if(!selectCate.value){
        ElMessage.error("请选择关系类别");
        return false;
      }
    }
    loading.value = true;
}
const initCategoryList = () => {
  queryRelationCategoryEnum(allProps.categoryId).then((res) => {
    categoryList.value = res.data.list;
  })
}
const currentCate = ref(allProps.relationCategoryId);
onMounted(() => {
  token.value = getToken().accessToken;
    initProps();
    queryImportVersion();
    queryImportRule();
    if(allProps.relationCategoryId=="-2"){
      initCategoryList();
    }
})
const changeCate = async(value) =>{
  if(currentCate.value=='-2'){
    queryPropertyByIdAxios(value).then(res => {
      props.value = res['data'];
    }).catch(exp => {
      console.log(exp);
    })
  }
}
watch(
    allProps,
    val => {

    },
    { deep: true, immediate: true }
);

</script>

<style lang="scss" scoped>
.asset-import {
    padding: 20px;

    :deep(.el-upload) {
        width: 100%;

        .el-upload-dragger {
            width: 100% !important;
            height: 250px;
        }

        .upload-desc {
            height: 18px;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #6E7493;
            line-height: 18px;
            margin-top: 20px;
        }
    }

    .import-progress {
        margin-top: 20px;

        .complete-percent {
            font-size: 16px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #3C4A54;
        }

        .filename {
            float: right;
            font-size: 14px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #3C4A54;
        }

        .upload-progress {
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #6E7493;
        }
    }

    .import-bz {
        margin-top: 8px;
        font-size: 14px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #3C4A54;
    }


    .error-items {
        .error-item {
            margin: 8px;
        }
    }
}
</style>
