<template>
  <div>
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition mode="out-in" name="fade-transform">
      <!-- 动态组件，根据currentComponent的值来决定渲染哪个组件 -->
      <div :key="$route.fullPath">
        <component
          :is="currentComponent"
          :source-info="selectedSource"
          @jump-to="comChange"
          @source-select="sourceSelectHandler"
        />
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, shallowRef, toRefs } from "vue";
import assetTableInfo from "@/views/modules/eam/zcgl/instance/source/component/assetTableInfo.vue";
import assetInformation from "@/views/modules/eam/zcgl/instance/source/component/assetInformation.vue";
import editProperty from "@/views/modules/eam/zcgl/instance/source/component/editProperty.vue";
import instanceDelete from "@/views/modules/eam/zcgl/instance/source/component/instanceDelete.vue";
// 创建两个组件的引用
const assetTableInfoPage = shallowRef(assetTableInfo);
const assetInformationPage = shallowRef(assetInformation);
const editPropertyPage = shallowRef(editProperty);
const instanceDeletePage = shallowRef(instanceDelete);

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 选中的事件
  selectedSource: null
});
const { currentComponent, selectedSource } = toRefs(state);

// 默认显示EventDealManage组件
state.currentComponent = assetTableInfoPage;

// 根据传入的值切换组件
const comChange = (val: string) => {
  if (val == "assetTableInfoPage") {
    state.currentComponent = assetTableInfoPage;
  }

  if (val == "editProperty") {
    state.currentComponent = editPropertyPage;
  }

  if (val == "assetInformationPage") {
    state.currentComponent = assetInformationPage;
  }
  if (val == "instanceDelete") {
    state.currentComponent = instanceDeletePage;
  }
};

// 处理事件选择
const sourceSelectHandler = (evt: any) => {
  state.selectedSource = evt;
};
</script>
<style lang="scss">
@import "./css/eam-instance-asset-info";
</style>
