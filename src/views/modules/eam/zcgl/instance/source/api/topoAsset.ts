let defaultImage = {
  label: "默认",
  value: "default",
  // 默认用中间件的图标
  categoryImage: "middleware",
  topoImage: "middleware",
  topoImageSelect:"middleware-select",
};

let images = [
  {
    label: "业务系统",
    value: "bus-system",
    categoryImage: "bus-system",
    topoImage: "bus-system",
    topoImageSelect: "bus-system-select",
  },
  {
    label: "信息系统",
    value: "info-system",
    categoryImage: "info-system",
    topoImage: "bus-system",
    topoImageSelect: "bus-system-select",
  },
  {
    label: "中间件",
    value: "middleware",
    categoryImage: "middleware",
    topoImage: "middleware",
    topoImageSelect: "middleware-select",
  },
  {
    label: "交换机",
    value: "switch",
    categoryImage: "switch",
    topoImage: "switch",
    topoImageSelect: "switch-select",
  },
  {
    label: "交换机2",
    value: "switch2",
    categoryImage: "switch2",
    topoImage: "switch",
    topoImageSelect: "switch-select",
  },
  {
    label: "其它办公设备终端",
    value: "other-office-terminal",
    categoryImage: "other-office-terminal",
    topoImage: "other-office-terminal",
    topoImageSelect: "other-office-terminal-select",
  },
  {
    label: "笔记本",
    value: "notebook",
    categoryImage: "notebook",
    topoImage: "other-office-terminal",
    topoImageSelect: "other-office-terminal",
  },
  {
    label: "其它用户终端",
    value: "other-user-terminal",
    categoryImage: "other-user-terminal",
    topoImage: "other-user-terminal",
    topoImageSelect: "other-user-terminal-select",
  },
  {
    label: "网络设备",
    value: "network-equipment",
    categoryImage: "network-equipment",
    topoImage: "network-equipment",
    topoImageSelect: "network-equipment-select",
  },
  {
    label: "办公设备",
    value: "office-equipment",
    categoryImage: "office-equipment",
    topoImage: "office-equipment",
    topoImageSelect: "office-equipment-select",
  },
  {
    label: "办公设备2",
    value: "office-equipment2",
    categoryImage: "office-equipment2",
    topoImage: "office-equipment2",
    topoImageSelect: "office-equipment-select",
  },
  {
    label: "台式机",
    value: "office-equipment3",
    categoryImage: "office-equipment3",
    topoImage: "office-equipment3",
    topoImageSelect: "office-equipment3-select",
  },
  {
    label: "安全设备",
    value: "safety-equipment",
    categoryImage: "safety-equipment",
    topoImage: "safety-equipment",
    topoImageSelect: "safety-equipment-select",
  },
  {
    label: "安全设备2",
    value: "safety-equipment2",
    categoryImage: "safety-equipment2",
    topoImage: "safety-equipment2",
    topoImageSelect: "safety-equipment-select",
  },
  {
    label: "打印机",
    value: "printer",
    categoryImage: "printer",
    // 暂时和办公设备图标使用同一组
    topoImage: "printer",
    topoImageSelect: "printer",
  },
  {
    label: "打印机2",
    value: "printer2",
    categoryImage: "printer2",
    topoImage: "office-equipment",
    topoImageSelect: "office-equipment-select",
  },
  {
    label: "数据库",
    value: "database",
    categoryImage: "database",
    topoImage: "database",
    topoImageSelect: "database-select",
  },
  {
    label: "服务端口",
    value: "service-port",
    categoryImage: "service-port",
    topoImage: "service-port",
    topoImageSelect: "service-port-select",
  },
  {
    label: "物理服务器",
    value: "physical-server",
    categoryImage: "physical-server",
    topoImage: "physical-server",
    topoImageSelect: "physical-server-select",
  },
  {
    label: "虚拟服务器",
    value: "virtual-server",
    categoryImage: "virtual-server",
    topoImage: "virtual-server",
    topoImageSelect: "virtual-server-select",
  },
  {
    label: "虚拟服务器2",
    value: "virtual-server2",
    categoryImage: "virtual-server2",
    topoImage: "virtual-server",
    topoImageSelect: "virtual-server-select",
  },
  {
    label: "线路",
    value: "line",
    categoryImage: "line",
    topoImage: "line",
    topoImageSelect: "line-select",
  },
  {
    label: "链路",
    value: "link",
    categoryImage: "link",
    topoImage: "link",
    topoImageSelect: "link-select",
  },
  {
    label: "网络端口",
    value: "network-port",
    categoryImage: "network-port",
    topoImage: "network-port",
    topoImageSelect: "network-port-select",
  },
  {
    label: "路由器",
    value: "router",
    categoryImage: "router",
    topoImage: "router",
    topoImageSelect: "router-select",
  },
  {
    label: "路由器2",
    value: "router2",
    categoryImage: "router2",
    topoImage: "router",
    topoImageSelect: "router-select",
  },
  {
    label: "无线设备",
    value: "router3",
    categoryImage: "router3",
    topoImage: "router",
    topoImageSelect: "router-select",
  },
  {
    label: "通用软件",
    value: "software",
    categoryImage: "software",
    topoImage: "software",
    topoImageSelect: "software-select",
  },
  {
    label: "平板PAD",
    value: "pad",
    categoryImage: "pad",
    topoImage: "pad",
    topoImageSelect: "pad-select",
  },
  {
    label: "手机",
    value: "telphone",
    categoryImage: "telphone",
    topoImage: "telphone",
    topoImageSelect: "telphone-select",
  },
  {
    label: "wifi",
    value: "wifi",
    categoryImage: "wifi",
    // 使用网络设备topo图
    topoImage: "network-equipment",
    topoImageSelect: "network-equipment-select",
  },
];
const imagesMap = {};
for (let image of images) {
  let { value } = image;
  imagesMap[value] = image;
}
const getImages = () =>{
  return images;
}

import {getSmallImageUrl} from "@/views/modules/eam/zcgl/instance/source/images/small/smallImage";
import {getTopoImageUrl} from "@/views/modules/eam/zcgl/instance/source/images/topo/topoImage";

const getCategoryInfo = (category_id,categoryColumns) => {
   return categoryColumns[category_id];
 }
 const getCategoryImage = (category_id,categoryColumns) => {
   let {position_image} = getCategoryInfo(category_id,categoryColumns) || {};
   let categoryImage = getImageInfo(position_image);
   return getSmallImageUrl(categoryImage);
 }
  const getTopoImage = (category_id,categoryColumns) => {
    let {position_image} = getCategoryInfo(category_id,categoryColumns) || {};
    let topoImage = getImageInfo(position_image).topoImage;
    return getTopoImageUrl(topoImage);
  }
  const getTopoImageSelect = (category_id,categoryColumns) => {
    let {position_image} = getCategoryInfo(category_id,categoryColumns) || {};
    let topoImageSelect = getImageInfo(position_image).topoImageSelect;
    return getTopoImageUrl(topoImageSelect);
  }
  const getColumns = (category_id,categoryColumns) => {
    let columns = categoryColumns[category_id]?.columns || [];
    return columns;
  }
  const getPortType = (category_id,categoryColumns) => {
    let portType = categoryColumns[category_id]?.portType;
    return portType;
 }

const getImageUrl = (url) =>{
  return getTopoImageUrl(url)?getTopoImageUrl(url):getSmallImageUrl(url);
}
/** 获取图片信息 */
const getImageInfo = (value) => {
  if(value){
    let imageInfo = imagesMap[value];
    if (!imageInfo) {
      console.warn(`type of value `+value+` is not exist `);
    }
    return imageInfo || defaultImage;
  }else{
    return defaultImage;
  }

};

 export {
   getCategoryInfo,getCategoryImage,getTopoImage,getTopoImageSelect,getColumns,getPortType,getImages,getImageUrl
 }
