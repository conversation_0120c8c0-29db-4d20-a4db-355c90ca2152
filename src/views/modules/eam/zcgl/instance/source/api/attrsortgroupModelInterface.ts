import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
  getByCategoryDataUrl: "/attrSortGroup/getByCategoryData",//通过类别获取分组
  getByCategoryGroupUrl:"/attrSortGroup/getByCategoryGroup",//通过类别ID  获取实例字段  分组
  insertAttrPropertyGroupUrl:"/attrSortGroup/insertAttrPropertyGroup",//属性和分组设置
  getGroupCategoryUrl:"/attrSortGroup/getGroupCategory",//获取所有分组
  setGroupCategoryUrl:"/attrSortGroup/setGroupCategory",//添加分组和类别的设置
  insertGroupSortUrl:"/attrSortGroup/insertGroupSort",//分组排序
  validateAttrGroupUrl:"/attrSortGroup/validateAttrGroup",
  saveAttrGroupUrl:"/attrSortGroup/saveAttrGroup",
  removeAttrGroupUrl:"/attrSortGroup/removeAttrGroup",//删除组
  getIsQueryIpUrl:"/maintain/getIsQueryIp",
  propertyExists:"/maintain/propertyExists",
};

const getByCategoryDataAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.getByCategoryDataUrl}`, params);
}

const getByCategoryGroupAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.getByCategoryGroupUrl}`, params);
}

const insertAttrPropertyGroupAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.insertAttrPropertyGroupUrl}`, params);
}

const getGroupCategoryAxios = (params: any = {}) => {
  return http.postJson<any>(`${basePath}${pathList.getGroupCategoryUrl}`, params);
}

const setGroupCategoryAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.setGroupCategoryUrl}`, params);
}

const insertGroupSortAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.insertGroupSortUrl}`, params);
}

const validateAttrGroupAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.validateAttrGroupUrl}`, params);
}

const saveAttrGroupAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.saveAttrGroupUrl}`, params);
}

const removeAttrGroupAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.removeAttrGroupUrl}`, params);
}
const getIsQueryIpAxios = (params) => {
  return http.postJson<any>(`${basePath}${pathList.getIsQueryIpUrl}`, params);
}
const propertyExistsAxios = (params) => {
  return http.get(`${basePath}${pathList.propertyExists}?categoryId=`+params);
}
export {
  getByCategoryDataAxios,
  getByCategoryGroupAxios,
  insertAttrPropertyGroupAxios,
  getGroupCategoryAxios,
  setGroupCategoryAxios,
  insertGroupSortAxios,
  validateAttrGroupAxios,
  saveAttrGroupAxios,
  removeAttrGroupAxios,
  getIsQueryIpAxios,
  propertyExistsAxios
};
