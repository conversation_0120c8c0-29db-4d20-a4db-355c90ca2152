import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;
const basePath1 = `${ServerNames.restProxyServer}/`;

const pathList = {
  initTreeDataNewUrl: "category/queryNewCategoryCountByDept",
  initSelectTreeData: "property/queryZoneListByRole",
  initTableColumsUrl: "/business/columns",
  initTableDataUrl: "/instance/data",
  initQueryLabelUrl: "/category/initQueryLabel",
  deleteInstanceUrl: "/instance/deleteByIds",
  exportInstanceUrl: "/instance/exportReport",
  getInstanceById: '/instance/getInstanceById',
  queryPropertyEnumByInstanceUrl: "/instance/queryPropertyEnum",
  checkPropertyByCodeUrl:"/instance/checkPropertyByCode",
  checkObjectPropertyByCodeUrl:"/objectTable/checkObjectPropertyByCode",
  saveInsUrl:"/instance/save",
  assetInfoLeftTreeUrl:"/category/queryAssetInfoCategorysByCategoryId",
  queryUpdateRecordUrl:"/assetPosition/queryAssetChangeByDate",
  queryRelationTableUrl:"/category/queryAssetInfoTable",
  queryRelationCategoryComboUrl:"/category/queryRelationCategoryCombo",
  queryCasecadeListUrl:"/rule/rest/rules/cascade",
  exportRelationDataUrl:"/category/exportAssetInfoTable",
  removeRelationUrl:"/category/removeRelationData",
  queryAllPositionSetUrl:"/assetPosition/queryAllPositionSet",
  queryDetailRelationsUrl:"/instance/queryTopoRelationData",
  queryInstancePropDataToolTipUrl:"/instance/queryInstancePropDataToolTip",
  queryTreeTableUrl:"/property/queryTreeTable",
  querySearchTableListUrl:"/property/querySearchTableList",
  exportExcelTempUrl:"exportExcel/exportExcelTemp",
  queryColumnListByFilterUrl:"/instance/queryColumnListByFilter",
  initConditionUrl:"/maintain/getConditionData",
  queryEnumListUrl:"/property/object/enumList",
  queryConditionByCategoryUserUrl:"/instance/queryConditionByCategoryUser",
  queryPropByCateUrl:"/category/queryPropByCate",
  saveConditionByCategoryUserUrl:"/instance/saveConditionByCategoryUser",
  initSetColumnUrl:"/instance/initSetColumn",
  saveSetShowColumnUrl:"/instance/saveSetShowColumn",
  initDetailShowUrl:"/property/initDetailShow",
  saveDetailShowUrl:"/property/saveDetailShow",
  queryLabelCountUrl:"/instance/queryLabelCount",
  initDeleteTableUrl:"/instanceDelete/queryDeleteData",
  initDeleteTreeDataUrl:"/instanceDelete/getDeleteTreeData",
  verifyReduInstanceUrl:"/instanceDelete/verifyReduInstance",
  reduInstanceUrl:"/instanceDelete/reduInstance",
  deleteInstanceDeleteUrl:"/instanceDelete/deleteInstance",
  exportInstanceInfoUrl:"/instanceExport/exportInstanceInfo",
  initAssetPositionSetUrl:"/instancePosition/initAssetPositionSet",
  saveAssetPositionSetUrl:"/instancePosition/saveAssetPositionSet",
  queryIsShowIpsAxioxUrl:"/newInstance/queryIsShowIps",
  viewFileUrl:"/file/viewFiles"
};

const initTreeDataNewMethod = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.initTreeDataNewUrl}`, params);
}

const initSelectTreeDataMethod = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.initSelectTreeData}`, params);
}


const initTableColumsMethod = (params: any) => {
  return http.post(`${basePath}${pathList.initTableColumsUrl}`, { data: params }, {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

const initTableDataMethod = (params: any) => {
  return http.post(`${basePath}${pathList.initTableDataUrl}`, { data: params }, {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

const initQueryLabelMethod = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.initQueryLabelUrl}`, params);
}

const deleteInstanceByIds = (params: any) => {
  return http.get(`${basePath}${pathList.deleteInstanceUrl}?ids=${params}`);
}

const exportInstance = (params: any) => {
  return http.postBlobWithJson(`${basePath}${pathList.exportInstanceUrl}`, params);
}

const getInstanceByIdApi = (params: any) => {
  return http.get(`${basePath}${pathList.getInstanceById}?id=${params.id}&categoryId=${params.categoryId}&userId=${params.userId}&isAuth=${params.isAuth}`);
}

const queryPropertyEnumByInstanceAxios = (params: any) => {
  return http.postJson<any>(`${basePath}${pathList.queryPropertyEnumByInstanceUrl}`, params);
}

const checkPropertyByCodeAxios =(params: any) => {
  return http.postJson<any>(`${basePath}${pathList.checkPropertyByCodeUrl}`, params);
}

const checkObjectPropertyByCodeAxios = (params:any) =>{
  return http.postJson<any>(`${basePath}${pathList.checkObjectPropertyByCodeUrl}`, params);
}
const saveIns = (params:any) =>{
  return http.postJson<any>(`${basePath}${pathList.saveInsUrl}`, params);
}
const initAssetInfoLeftTree = (params:any) =>{
  return http.get(`${basePath}${pathList.assetInfoLeftTreeUrl}?categoryId=${params.categoryId}&instanceId=${params.instanceId}`);
}
const queryUpdateRecord = (params:any) =>{
  return http.postJson<any>(`${basePath1}${pathList.queryUpdateRecordUrl}`, params);
}
const queryRelationTable = (params:any) =>{
  return http.postJson<any>(`${basePath}${pathList.queryRelationTableUrl}`, params);
}
const queryRelationCategoryEnum = (params:any) => {
  return http.get(`${basePath}${pathList.queryRelationCategoryComboUrl}?categoryId=${params}`);
}
const queryCasecadeListAxios = (params:any) =>{
  return http.postJson<any>(`${basePath}${pathList.queryCasecadeListUrl}`, params);
}
const exportRelationDataAxios = (params:any) =>{
  return http.postBlobWithJson(`${basePath}${pathList.exportRelationDataUrl}`, params);
}
const removeRealtionAxios = (params:any) =>{
  return http.get(`${basePath}${pathList.removeRelationUrl}?id=${params}`);
}
const queryAllPositionSet = () =>{
  return http.get(`${basePath1}${pathList.queryAllPositionSetUrl}`)
}
const queryDetailRelations = (params) =>{
  return http.postJson(`${basePath}${pathList.queryDetailRelationsUrl}`,params);
}
const queryInstancePropDataToolTipAxios = (params) =>{
  return http.postJson(`${basePath}${pathList.queryInstancePropDataToolTipUrl}`,params);
}
const queryTreeTableAxios = (params) =>{
  return http.postJson(`${basePath}${pathList.queryTreeTableUrl}`, params);
}
const querySearchTableListAxios = (params) =>{
  return http.postJson(`${basePath}${pathList.querySearchTableListUrl}`,params);
}
const exportExcelTempAxios = (params) =>{
  return http.postBlobWithJson(`${basePath}${pathList.exportExcelTempUrl}`, params);
}
const queryColumnListByFilterAxios = (params) =>{
  return http.postJson(`${basePath}${pathList.queryColumnListByFilterUrl}`,params);
}
const initConditionMethod = (params) =>{
  return http.post(`${basePath}${pathList.initConditionUrl}`,{ data: params }, {
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  });
}
const queryEnumList=(params)=>{
  return http.postJson(`${basePath}${pathList.queryEnumListUrl}`,params);
}
const queryConditionByCategoryUserAxios = (params) =>{
  return http.postJson(`${basePath}${pathList.queryConditionByCategoryUserUrl}`,params);
}

const queryPropByCateAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.queryPropByCateUrl}`,params);
}

const saveConditionByCategoryUserAxios=(params)=> {
  return http.postJson(`${basePath}${pathList.saveConditionByCategoryUserUrl}`, params);
}
const initSetColumnAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.initSetColumnUrl}`, params);
}
const saveSetShowColumnAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.saveSetShowColumnUrl}`, params);
}
const initDetailShowAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.initDetailShowUrl}`, params);
}
const saveDetailShowAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.saveDetailShowUrl}`, params);
}
const queryLabelCountAxios = (params) =>{
  return http.get(`${basePath}${pathList.queryLabelCountUrl}?categoryId=${params.categoryId}&labelId=${params.labelId}&zoneName=${params.zoneName}`);
}
const initDeleteTableDataMethod = (params) => {
  return http.postJson(`${basePath}${pathList.initDeleteTableUrl}`, params);
}
const initDeleteTreeDataMethod=(params)=>{
  return http.postJson(`${basePath}${pathList.initDeleteTreeDataUrl}`, params);
}
const verifyReduInstanceAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.verifyReduInstanceUrl}`, params);
}
const reduInstanceAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.reduInstanceUrl}`, params);
}
const deleteInstanceDeleteAxios=(params)=>{
  return http.postJson(`${basePath}${pathList.deleteInstanceDeleteUrl}`, params);
}
const exportInstanceInfoAxios = (params) =>{
  return http.postBlobWithJson(`${basePath}${pathList.exportInstanceInfoUrl}`, params);
}
const initAssetPositionSetAxios = (params) => {
  return http.postJson(`${basePath}${pathList.initAssetPositionSetUrl}`, params);
}
const saveAssetPositionSetAxios = (params) => {
  return http.postJson(`${basePath}${pathList.saveAssetPositionSetUrl}`, params);
}
const queryIsShowIpsAxios = (params) => {
  return http.postJson(`${basePath}${pathList.queryIsShowIpsAxioxUrl}`, params);
}
const viewFile = (params) =>{
  return http.postBlobWithJson(`${basePath}${pathList.viewFileUrl}`, params);
}
export {
  initTreeDataNewMethod,
  initSelectTreeDataMethod,
  initTableColumsMethod,
  initTableDataMethod,
  initQueryLabelMethod,
  deleteInstanceByIds,
  exportInstance,
  getInstanceByIdApi,
  queryPropertyEnumByInstanceAxios,
  checkPropertyByCodeAxios,
  checkObjectPropertyByCodeAxios,
  saveIns,initAssetInfoLeftTree,
  queryUpdateRecord,
  queryRelationTable,
  queryRelationCategoryEnum,
  queryCasecadeListAxios,
  exportRelationDataAxios,
  removeRealtionAxios,
  queryAllPositionSet,
  queryDetailRelations,
  queryInstancePropDataToolTipAxios,
  queryTreeTableAxios,
  querySearchTableListAxios,
  exportExcelTempAxios,
  queryColumnListByFilterAxios,
  initConditionMethod,
  queryEnumList,
  queryConditionByCategoryUserAxios,
  queryPropByCateAxios,
  saveConditionByCategoryUserAxios,
  initSetColumnAxios,
  saveSetShowColumnAxios,
  initDetailShowAxios,
  saveDetailShowAxios,
  queryLabelCountAxios,
  initDeleteTableDataMethod,
  initDeleteTreeDataMethod,
  verifyReduInstanceAxios,
  reduInstanceAxios,
  deleteInstanceDeleteAxios,
  exportInstanceInfoAxios,
  initAssetPositionSetAxios,
  saveAssetPositionSetAxios,
  queryIsShowIpsAxios,
  viewFile
};
