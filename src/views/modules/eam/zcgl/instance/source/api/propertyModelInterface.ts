import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
    initComboboxUrl: "/property/initCombobox",
    initPropertyVerifyDelUrl: "/property/verifyDel",
    queryShowTypeUrl: "/property/queryShowType/",
    queryShowTypeByOcodeUrl: "/property/queryShowTypeByOcode/",
    queryCodeListUrl: "/property/queryCodeList",
    queryEnumListUrl: "/property/queryEnumList",
    validataCodeUrl: "/property/verifyCode",
    validataNameUrl: "/property/verifyName",
    saveCategoryPropertyUrl: "/property/saveCategoryProperty",
    queryPropertyGxhDefaultByIdUrl: "/property/queryPropertyGxhDefaultById",
    saveDefaultGxhValueUrl: "/property/saveDefaultGxhValue",
    checkRuleElTypeUrl: "/property/checkRuleElType",
    queryEnumListUrl02: "/property/object/enumList",
};

const postJson = (path: String, params: any) => {
    return http.postJson<any>(`${basePath}${path}`, params);
}

const get = (path: String) => {
    return http.get(`${basePath}${path}`);
}

const getQuery = (path: String, params: any) => {
    return http.get(`${basePath}${path}/${params}`);
}

const deleteMethod = (path: String, params: any) => {
    return http.request(`delete`, `${basePath}${path}/${params}`);
}

const deleteParamsMethod = (path: String, params: any) => {
    return http.request(`delete`, `${basePath}${path}?categoryId=${params.categoryId}&propertyId=${params.propertyId}`, {},
        {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            }
        }
    );
}

const initComboboxMethod = () => get(pathList.initComboboxUrl);
const initPropertyVerifyDelMethod = (params) => getQuery(pathList.initPropertyVerifyDelUrl, params);
const initShowTypeMethod = (params) => getQuery(pathList.queryShowTypeUrl, params);
const initShowTypeByOcodeMethod = (params) => getQuery(pathList.queryShowTypeByOcodeUrl, params);
const queryEnumCodeList = () => get(pathList.queryCodeListUrl);
const queryEnumListDe = (params) => postJson(pathList.queryEnumListUrl, params);
const validataProCode = (params) => postJson(pathList.validataCodeUrl, params);
const validataProName = (params) => postJson(pathList.validataNameUrl, params);
const saveCategoryProperty = (params) => postJson(pathList.saveCategoryPropertyUrl, params);
const queryPropertyGxhDefaultByIdAxios = (params) => postJson(pathList.queryPropertyGxhDefaultByIdUrl, params);
const saveDefaultGxhValueAxios = (params) => postJson(pathList.saveDefaultGxhValueUrl, params);
const checkRuleElTypeAxios = (params) => postJson(pathList.checkRuleElTypeUrl, params);
const queryEnumList = (params) => postJson(pathList.queryEnumListUrl02, params);


export {
    checkRuleElTypeAxios,
    initComboboxMethod,
    initPropertyVerifyDelMethod,
    initShowTypeByOcodeMethod,
    initShowTypeMethod,
    queryEnumCodeList,
    queryEnumListDe,
    queryPropertyGxhDefaultByIdAxios,
    saveCategoryProperty,
    saveDefaultGxhValueAxios,
    validataProCode,
    validataProName,
    queryEnumList
};
