import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
  exportInstanceTempUrl:"/instance/exportExcelTemp",
  importRuleUrl:"/instance/queryImportRuleByUserCate",
  importVersionUrl:"/instance/queryImportType",
  queryPropertyByIdUrl:"/category/queryProperty",//根据资产查询属性
};

const exportInsTemp = (params: any) => {
  return http.postBlobWithJson<any>(`${basePath}${pathList.exportInstanceTempUrl}`, params);
}

const queryImportRuleAxios = (params: any) => {
  return http.get(`${basePath}${pathList.importRuleUrl}?categoryId=${params}`);
}

const queryImportVersionAxios = () => {
  return http.get(`${basePath}${pathList.importVersionUrl}`);
}

const queryPropertyByIdAxios = (params: any) => {
  return http.get(`${basePath}${pathList.queryPropertyByIdUrl}/${params}`);
}

export {
  exportInsTemp,
  queryImportRuleAxios,
  queryImportVersionAxios,
  queryPropertyByIdAxios
};
