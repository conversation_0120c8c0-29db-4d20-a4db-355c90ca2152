import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

// const basePath = `${ServerNames.eamCoreServer}/`;
const basePath = `/security-event`;

const pathList = {
    riskOccurrenceTrendUrl: "/sem/eventTrend", // 风险发生趋势
    focusOnRiskUrl: "/sem/eventTypeSpread" // 重点风险关注
};

const riskOccurrenceTrendMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.riskOccurrenceTrendUrl}`, params);
}

const focusOnRiskMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.focusOnRiskUrl}`, params);
}

export {
    riskOccurrenceTrendMethod,
    focusOnRiskMethod
};
