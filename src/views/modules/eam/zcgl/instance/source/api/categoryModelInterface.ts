import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
    categoryTreeUrl: "/category/categoryTree",//资产类别树
    queryPropertyByIdUrl: "/category/queryProperty",//根据资产查询属性
    getCategoryRelationByIdUrl: "/categoryRelation/get",//获取类别关系
    categoryRelationVerifyNameUrl: "/categoryRelation/verifyName",//校验关联类型
    deleteCategoryRelationByIdUrl: "/categoryRelation/delete",//删除类别关系
    getCategoryBeRelationByIdUrl: "/categoryRelation/getBe",//获取类别被动关联关系
    getCategoryByIdUrl: "/category/get",//查询资产类别信息
    saveCategoryUrl: "/category/save",//添加资产类型
    queryNonPropertyByIdUrl: "/category/queryNonProperty",//查询资产类别没有分配的属性--待写接口
    queryPropertiesByCategoryIdUrl: "/property/queryProperties",//根据资产类别查询对应的业务属性
    queryCategoryRelationByCategoryIdUrl: "/categoryRelation/queryCategoryRelationByCategoryId",//根据资产类别查询主动关系
    queryBeCategoryRelationByCategoryIdUrl: "/categoryRelation/queryBeCategoryRelationByCategoryId",//根据资产类别查询主动关系
    categoryVerifyNameUrl: "/category/verifyName",//资产名字校验
    savePropertyUrl: "/category/saveProperty",//添加属性
    deleteCategoryPropertyUrl: "/category/deleteProperty",//删除类别中的属性categoryId=90&propertyId=1022
    categoryVerifyDelUrl: "/category/verifyDel",//校验资产类别是否可删除
    deleteCategoryByIdUrl: "/category/delete",//删除资产类别
    saveCategoryRelationUrl: "/categoryRelation/save",//新增关系
    queryAutoRuleInfoUrl: "/property/queryEditRuleData",
    saveEditRuleUrl: "/property/saveEditRule",
    deleteCrpUrl: "/property/deleteCrp",
    aveCategoryOrder: '/category/saveCategoryOrder',
  queryPropertyGxhDefaultByIdUrl:"/property/queryPropertyGxhDefaultById",
  saveDefaultGxhValueUrl:"/property/saveDefaultGxhValue",
  queryCategoryPrimaryUrl:"/newInstance/queryCategoryPrimary",
  saveCategoryPrimaryUrl:"/newInstance/saveCategoryPrimary",
  queryPropertyByCategoryUrl:"/category/queryPropCodeByCate",
  queryInfoRelationCategorySettingUrl:"/categoryRelation/queryInfoRelationCategorySetting",
  saveInfoRelationCategorySettingUrl:"/categoryRelation/saveInfoRelationCategorySetting",
};

const postJson = (path: String, params: any) => {
    return http.postJson<any>(`${basePath}${path}`, params);
}

const get = (path: String) => {
    return http.get(`${basePath}${path}`);
}

const getQuery = (path: String, params: any) => {
    return http.get(`${basePath}${path}/${params}`);
}

const deleteMethod = (path: String, params: any) => {
    return http.request(`delete`, `${basePath}${path}/${params}`);
}

const deleteParamsMethod = (path: String, params: any) => {
    return http.request(`delete`, `${basePath}${path}?categoryId=${params.categoryId}&propertyId=${params.propertyId}`, {},
        {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded"
            }
        }
    );
}

const queryPropertyGxhDefaultByIdAxios=(params)=>postJson(pathList.queryPropertyGxhDefaultByIdUrl,params);
const aveCategoryOrderAxios = params => postJson(pathList.aveCategoryOrder, params);
const categoryTreeoAxios = () => get(pathList.categoryTreeUrl);
const queryPropertyByIdAxios = params => getQuery(pathList.queryPropertyByIdUrl, params);
const getCategoryRelationByIdAxios = params => getQuery(pathList.getCategoryRelationByIdUrl, params);
const categoryRelationVerifyNameAxios = params => postJson(pathList.categoryRelationVerifyNameUrl, params);
const deleteCategoryRelationByIdAxios = params => deleteMethod(pathList.deleteCategoryRelationByIdUrl, params);
const getCategoryBeRelationByIdAxios = params => getQuery(pathList.getCategoryBeRelationByIdUrl, params);
const getCategoryByIdAxios = params => getQuery(pathList.getCategoryByIdUrl, params);
const saveCategoryAxios = params => postJson(pathList.saveCategoryUrl, params);
const queryNonPropertyByIdAxios = params => postJson(pathList.queryNonPropertyByIdUrl, params);
const queryPropertiesByCategoryIdAxios = params => postJson(pathList.queryPropertiesByCategoryIdUrl, params);
const queryCategoryRelationByCategoryIdAxios = params => postJson(pathList.queryCategoryRelationByCategoryIdUrl, params);
const queryBeCategoryRelationByCategoryIdAxios = params => postJson(pathList.queryBeCategoryRelationByCategoryIdUrl, params);
const categoryVerifyNameAxios = params => postJson(pathList.categoryVerifyNameUrl, params);
const savePropertyAxios = params => postJson(pathList.savePropertyUrl, params);
const deleteCategoryPropertyAxios = params => deleteParamsMethod(pathList.deleteCategoryPropertyUrl, params);
const categoryVerifyDelAxios = params => getQuery(pathList.categoryVerifyDelUrl, params);
const deleteCategoryByIdAxios = params => getQuery(pathList.deleteCategoryByIdUrl, params);
const saveCategoryRelationAxios = params => postJson(pathList.saveCategoryRelationUrl, params);
const queryAutoRuleInfoAxios = params => postJson(pathList.queryAutoRuleInfoUrl, params);
const saveEditRuleAxios = params => postJson(pathList.saveEditRuleUrl, params);
const deleteCrpUrlAxios = params => postJson(pathList.deleteCrpUrl, params);
const saveDefaultGxhValueAxios=(params)=>postJson(pathList.saveDefaultGxhValueUrl,params);
const queryCategoryPrimary = (params) => get(pathList.queryCategoryPrimaryUrl+"?categoryId="+params);
const saveCategoryPrimary = (params) => postJson(pathList.saveCategoryPrimaryUrl,params);
const queryPropertyByCategory = (params) => postJson(pathList.queryPropertyByCategoryUrl,params);
const queryInfoRelationCategorySetting = (params) => get(pathList.queryInfoRelationCategorySettingUrl+"?categoryId="+params);
const saveInfoRelationCategorySetting = (params) => postJson(pathList.saveInfoRelationCategorySettingUrl,params);
export {
    aveCategoryOrderAxios,
    categoryRelationVerifyNameAxios,
    categoryTreeoAxios,
    categoryVerifyDelAxios,
    categoryVerifyNameAxios,
    deleteCategoryByIdAxios,
    deleteCategoryPropertyAxios,
    deleteCategoryRelationByIdAxios,
    deleteCrpUrlAxios,
    getCategoryBeRelationByIdAxios,
    getCategoryByIdAxios,
    getCategoryRelationByIdAxios,
    queryAutoRuleInfoAxios,
    queryBeCategoryRelationByCategoryIdAxios,
    queryCategoryRelationByCategoryIdAxios,
    queryNonPropertyByIdAxios,
    queryPropertiesByCategoryIdAxios,
    queryPropertyByIdAxios,
    saveCategoryAxios,
    saveCategoryRelationAxios,
    saveEditRuleAxios,
    savePropertyAxios,
  queryPropertyGxhDefaultByIdAxios,
  saveDefaultGxhValueAxios,
  queryCategoryPrimary,
  saveCategoryPrimary,
  queryPropertyByCategory,
  queryInfoRelationCategorySetting,
  saveInfoRelationCategorySetting
};
