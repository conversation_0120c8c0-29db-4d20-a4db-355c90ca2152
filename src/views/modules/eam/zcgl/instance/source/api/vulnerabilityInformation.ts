import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

// const basePath = `${ServerNames.eamCoreServer}/`;
const basePath = `/security-vul`;

const pathList = {
    vulnerabilityStatisticsUrl: "/v3/sum/riskSpread", // 待修复漏洞统计
    vulnerabilityDistributionUrl: "/v3/sum/vulType", // 漏洞分布情况
    vulnerabilityOccurrenceTrendUrl: "/v3/sum/vulTrend", // 近七天漏洞发生趋势
    vulnerabilityListUrl: "/v3/sum/assetVulDetails", //漏洞列表
    vulnerabilityExportUrl: '/v3/sum/import/vulDetails', // 漏洞导出
    batchDisposalUrl: '/v3/sum/disposalBatch', // 批量处置
};

const vulnerabilityStatisticsMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulnerabilityStatisticsUrl}`, params);
}

const vulnerabilityDistributionMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulnerabilityDistributionUrl}`, params);
}

const vulnerabilityOccurrenceTrendMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulnerabilityOccurrenceTrendUrl}`, params);
}

const vulnerabilityListMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulnerabilityListUrl}`, params);
}

const vulnerabilityExportMethod = (params: any) => {
    return http.postBlobWithJson(`${basePath}${pathList.vulnerabilityExportUrl}`, params);
}

const batchDisposalMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.batchDisposalUrl}`, params);
}

export {
    vulnerabilityStatisticsMethod,
    vulnerabilityDistributionMethod,
    vulnerabilityOccurrenceTrendMethod,
    vulnerabilityListMethod,
    vulnerabilityExportMethod,
    batchDisposalMethod
};
