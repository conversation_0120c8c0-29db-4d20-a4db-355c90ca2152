let defaultImage = {
  label: "默认",
  value: "default",
  // 默认用中间件的图标
  categoryImage: require("./small/middleware.svg"),
  topoImage: require("./topo/middleware.png"),
  topoImageSelect: require("./topo/middleware-select.png"),
};

let images = [
  {
    label: "业务系统",
    value: "bus-system",
    categoryImage: require("./small/bus-system.svg"),
    topoImage: require("./topo/bus-system.png"),
    topoImageSelect: require("./topo/bus-system-select.png"),
  },
  {
    label: "信息系统",
    value: "info-system",
    categoryImage: require("./small/info-system.svg"),
    topoImage: require("./topo/bus-system.png"),
    topoImageSelect: require("./topo/bus-system-select.png"),
  },
  {
    label: "中间件",
    value: "middleware",
    categoryImage: require("./small/middleware.svg"),
    topoImage: require("./topo/middleware.png"),
    topoImageSelect: require("./topo/middleware-select.png"),
  },
  {
    label: "交换机",
    value: "switch",
    categoryImage: require("./small/switch.svg"),
    topoImage: require("./topo/switch.png"),
    topoImageSelect: require("./topo/switch-select.png"),
  },
  {
    label: "交换机2",
    value: "switch2",
    categoryImage: require("./small/switch2.svg"),
    topoImage: require("./topo/switch.png"),
    topoImageSelect: require("./topo/switch-select.png"),
  },
  {
    label: "其它办公设备终端",
    value: "other-office-terminal",
    categoryImage: require("./small/other-office-terminal.svg"),
    topoImage: require("./topo/other-office-terminal.png"),
    topoImageSelect: require("./topo/other-office-terminal-select.png"),
  },
  {
    label: "笔记本",
    value: "notebook",
    categoryImage: require("./small/notebook.svg"),
    topoImage: require("./topo/other-office-terminal.png"),
    topoImageSelect: require("./topo/other-office-terminal-select.png"),
  },
  {
    label: "其它用户终端",
    value: "other-user-terminal",
    categoryImage: require("./small/other-user-terminal.svg"),
    topoImage: require("./topo/other-user-terminal.png"),
    topoImageSelect: require("./topo/other-user-terminal-select.png"),
  },
  {
    label: "网络设备",
    value: "network-equipment",
    categoryImage: require("./small/network-equipment.svg"),
    topoImage: require("./topo/network-equipment.png"),
    topoImageSelect: require("./topo/network-equipment-select.png"),
  },
  {
    label: "办公设备",
    value: "office-equipment",
    categoryImage: require("./small/office-equipment.svg"),
    topoImage: require("./topo/office-equipment.png"),
    topoImageSelect: require("./topo/office-equipment-select.png"),
  },
  {
    label: "办公设备2",
    value: "office-equipment2",
    categoryImage: require("./small/office-equipment2.svg"),
    topoImage: require("./topo/office-equipment.png"),
    topoImageSelect: require("./topo/office-equipment-select.png"),
  },
  {
    label: "台式机",
    value: "office-equipment3",
    categoryImage: require("./small/office-equipment3.svg"),
    topoImage: require("./topo/office-equipment.png"),
    topoImageSelect: require("./topo/office-equipment-select.png"),
  },
  {
    label: "安全设备",
    value: "safety-equipment",
    categoryImage: require("./small/safety-equipment.svg"),
    topoImage: require("./topo/safety-equipment.png"),
    topoImageSelect: require("./topo/safety-equipment-select.png"),
  },
  {
    label: "安全设备2",
    value: "safety-equipment2",
    categoryImage: require("./small/safety-equipment2.svg"),
    topoImage: require("./topo/safety-equipment.png"),
    topoImageSelect: require("./topo/safety-equipment-select.png"),
  },
  {
    label: "打印机",
    value: "printer",
    categoryImage: require("./small/printer.svg"),
    // 暂时和办公设备图标使用同一组
    topoImage: require("./topo/printer.png"),
    topoImageSelect: require("./topo/printer.png"),
  },
  {
    label: "打印机2",
    value: "printer2",
    categoryImage: require("./small/printer2.svg"),
    topoImage: require("./topo/office-equipment.png"),
    topoImageSelect: require("./topo/office-equipment-select.png"),
  },
  {
    label: "数据库",
    value: "database",
    categoryImage: require("./small/database.svg"),
    topoImage: require("./topo/database.png"),
    topoImageSelect: require("./topo/database-select.png"),
  },
  {
    label: "服务端口",
    value: "service-port",
    categoryImage: require("./small/service-port.svg"),
    topoImage: require("./topo/service-port.png"),
    topoImageSelect: require("./topo/service-port-select.png"),
  },
  {
    label: "物理服务器",
    value: "physical-server",
    categoryImage: require("./small/physical-server.svg"),
    topoImage: require("./topo/physical-server.png"),
    topoImageSelect: require("./topo/physical-server-select.png"),
  },
  {
    label: "虚拟服务器",
    value: "virtual-server",
    categoryImage: require("./small/virtual-server.svg"),
    topoImage: require("./topo/virtual-server.png"),
    topoImageSelect: require("./topo/virtual-server-select.png"),
  },
  {
    label: "虚拟服务器2",
    value: "virtual-server2",
    categoryImage: require("./small/virtual-server2.svg"),
    topoImage: require("./topo/virtual-server.png"),
    topoImageSelect: require("./topo/virtual-server-select.png"),
  },
  {
    label: "线路",
    value: "line",
    categoryImage: require("./small/line.svg"),
    topoImage: require("./topo/line.png"),
    topoImageSelect: require("./topo/line-select.png"),
  },
  {
    label: "链路",
    value: "link",
    categoryImage: require("./small/link.svg"),
    topoImage: require("./topo/link.png"),
    topoImageSelect: require("./topo/link-select.png"),
  },
  {
    label: "网络端口",
    value: "network-port",
    categoryImage: require("./small/network-port.svg"),
    topoImage: require("./topo/network-port.png"),
    topoImageSelect: require("./topo/network-port-select.png"),
  },
  {
    label: "路由器",
    value: "router",
    categoryImage: require("./small/router.svg"),
    topoImage: require("./topo/router.png"),
    topoImageSelect: require("./topo/router-select.png"),
  },
  {
    label: "路由器2",
    value: "router2",
    categoryImage: require("./small/router2.svg"),
    topoImage: require("./topo/router.png"),
    topoImageSelect: require("./topo/router-select.png"),
  },
  {
    label: "无线设备",
    value: "router3",
    categoryImage: require("./small/router3.svg"),
    topoImage: require("./topo/router.png"),
    topoImageSelect: require("./topo/router-select.png"),
  },
  {
    label: "通用软件",
    value: "software",
    categoryImage: require("./small/software.svg"),
    topoImage: require("./topo/software.png"),
    topoImageSelect: require("./topo/software-select.png"),
  },
  {
    label: "平板PAD",
    value: "pad",
    categoryImage: require("./small/pad.svg"),
    topoImage: require("./topo/pad.png"),
    topoImageSelect: require("./topo/pad-select.png"),
  },
  {
    label: "手机",
    value: "telphone",
    categoryImage: require("./small/telphone.svg"),
    topoImage: require("./topo/telphone.png"),
    topoImageSelect: require("./topo/telphone-select.png"),
  },
  {
    label: "wifi",
    value: "wifi",
    categoryImage: require("./small/wifi.svg"),
    // 使用网络设备topo图
    topoImage: require("./topo/network-equipment.png"),
    topoImageSelect: require("./topo/network-equipment-select.png"),
  },
];

const imagesMap = {};
for (let image of images) {
  let { value } = image;
  imagesMap[value] = image;
}

export default images;

/** 获取图片信息 */
export const getImageInfo = value => {
  let imageInfo = imagesMap[value];
  if (!imageInfo) {
    console.warn(`type of value ${value} is not exist `);
  }
  return imageInfo || defaultImage;
};

/** 获取小图标 */
export const getCategoryImage = value => {
  return getImageInfo(value).categoryImage;
};

/** 获取拓扑图标 */
export const getTopoImage = value => {
  return getImageInfo(value).topoImage;
};

/** 获取拓扑图标选中 */
export const getTopoImageSelect = value => {
  return getImageInfo(value).topoImageSelect;
};

