import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
    validateMoreUpdateUrl: "/instance/validateMoreUpdate",
    initMoreUpdatePropertyUrl: "/instance/initMoreUpdateProperty",
    saveMoreUpdatePropertyUrl: "/instance/saveMoreUpdateProperty",
    validataInstanceOnlineUrl: "/instance/validataInstanceOnline", // 在线探测
};

const validateMoreUpdateAxios = (params: any) => {
    return http.postJson<any>(`${basePath}${pathList.validateMoreUpdateUrl}`, params);
}

const initMoreUpdatePropertyAxios = (params: any) => {
    return http.postJson<any>(`${basePath}${pathList.initMoreUpdatePropertyUrl}`, params);
}

const saveMoreUpdatePropertyAxios = (params: any) => {
    return http.postJson<any>(`${basePath}${pathList.saveMoreUpdatePropertyUrl}`, params);
}

const validataInstanceOnlineAxios = (params: any) => {
    return http.get(`${basePath}${pathList.validataInstanceOnlineUrl}?id=${params}`);
}


export {
    validateMoreUpdateAxios,
    initMoreUpdatePropertyAxios,
    saveMoreUpdatePropertyAxios,
    validataInstanceOnlineAxios
};
