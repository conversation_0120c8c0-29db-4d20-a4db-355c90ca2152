/**绑定事件*/
const addEventListener = (element, eventName, callback) => {
  if(element.addEventListener) {
    element.addEventListener(eventName, callback);
  } else if(element.attachEvent) {
    element.attachEvent(`on${eventName}`, callback);
  }
}

/**清除事件*/
const removeEventListener = (element, eventName, callback) => {
  if(element.removeEventListener) {
    element.removeEventListener(eventName, callback);
  } else if(element.detachEvent) {
    element.detachEvent(`on${eventName}`, callback);
  }
}

/**派发事件*/
const dispathEvent = (element, eventName) => {
  if(element.dispatchEvent) {
    let event = document.createEvent("HTMLEvents");
    event.initEvent(eventName, true, true);
    element.dispatchEvent(event);
  } else if(element.fireEvent) {
    let evtObject = document.createEvent("HTMLEvents");
    element.fireEvent(`on${eventName}`,evtObject);
  }
}

export {
  addEventListener,
  removeEventListener,
  dispathEvent
}
