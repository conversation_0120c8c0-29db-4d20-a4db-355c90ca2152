import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;

const pathList = {
  exportInstanceUrl: "/instance/exportReport",
  exportInstanceTempUrl: "/instance/exportExcelTemp",
  exportRelationReportUrl: "/instance/exportRelationReport",
};

const exportInstance = (params: any) => {
  return http.postBlobWithJson(`${basePath}${pathList.exportInstanceUrl}`, params);
}

const exportInsTemp = (params: any) => {
  return http.postBlobWithJson(`${basePath}${pathList.exportInstanceTempUrl}`, params);
}

const exportRelationReportAxios = (params: any) => {
  return http.postBlobWithJson(`${basePath}${pathList.exportRelationReportUrl}`, params);
}


export {
  exportInstance,
  exportInsTemp,
  exportRelationReportAxios
};
