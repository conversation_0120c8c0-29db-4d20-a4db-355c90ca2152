import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

// const basePath = `${ServerNames.eamCoreServer}/`;
const basePath = `/rest-proxy`;

const pathList = {
    queryDetailRelationsUrl: "/assetPosition/queryTopoRelationData", // 关联设备信息
};

const queryDetailRelationsMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.queryDetailRelationsUrl}`, params);
}


export {
    queryDetailRelationsMethod,
};
