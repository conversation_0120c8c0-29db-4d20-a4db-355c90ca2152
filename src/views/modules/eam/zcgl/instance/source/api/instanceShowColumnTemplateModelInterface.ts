import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.eamCoreServer}/`;



const pathList = {
  initColumnTemplateUrl:"/columnTemplate/initColumnTemplate",
  saveShowTemplateColumnUrl:"/columnTemplate/saveShowTemplateColumn",
  querySelectTemplateColumnUrl:"/columnTemplate/querySelectTemplateColumn",
  deleteTemplateUrl:"/columnTemplate/deleteTemplate",
  useTemplateAllUrl:"/columnTemplate/useTemplateAll",
  useTemplateOneUrl:"/columnTemplate/useTemplateOne",
  getByCategoryGroupTemplateUrl:"/columnTemplate/getByCategoryGroupTemplate",
  getByCategoryDataTemplateUrl:"/columnTemplate/getByCategoryDataTemplate",
  saveGroupCategoryTemplateUrl:"/columnTemplate/saveGroupCategoryTemplate",
  saveGroupPropertyTemplateUrl:"/columnTemplate/saveGroupPropertyTemplate",
  validataTemplateNameUrl:"/columnTemplate/validataTemplateName",
  saveTemplateNameUrl:"/columnTemplate/saveTemplateName",
  queryHxTemplateTableUrl:"/columnTemplate/queryHxTemplateTable",
  saveHxTemplateTableUrl:"/columnTemplate/saveHxTemplateTable",
  initCategoryModelUrl:"/columnTemplate/initCategoryModel",
  saveTemplateCopyUrl:"/columnTemplate/saveTemplateCopy",
  initCopyTableUrl:"/columnTemplate/initCopyTable",
  initCategoryListByCopyTemplateUrl:"/columnTemplate/initCategoryListByCopyTemplate",
  saveCopyCategoryTemplateUrl:"/columnTemplate/saveCopyCategoryTemplate",
  queryPropertyEnumByInstanceUrl:"/instance/queryPropertyEnum"
}
const postJson = (path: String, params: any) => {
  return http.postJson<any>(`${basePath}${path}`, params);
}

const get = (path: String) => {
  return http.get(`${basePath}${path}`);
}


const initColumnTemplateAxios = (params) => postJson(pathList.initColumnTemplateUrl,params);
const saveShowTemplateColumnAxios = (params) => postJson(pathList.saveShowTemplateColumnUrl,params);
const querySelectTemplateColumnAxios = (params) => postJson(pathList.querySelectTemplateColumnUrl,params);
const deleteTemplateAxios = (params) => postJson(pathList.deleteTemplateUrl,params);
const useTemplateAllAxios = (params) => postJson(pathList.useTemplateAllUrl,params);
const useTemplateOneAxios = (params) => postJson(pathList.useTemplateOneUrl,params);
const getByCategoryGroupTemplateAxios = (params) => postJson(pathList.getByCategoryGroupTemplateUrl,params);
const getByCategoryDataTemplateAxios = (params) => postJson(pathList.getByCategoryDataTemplateUrl,params);
const saveGroupCategoryTemplateAxios = (params) => postJson(pathList.saveGroupCategoryTemplateUrl,params);
const saveGroupPropertyTemplateAxios = (params) => postJson(pathList.saveGroupPropertyTemplateUrl,params);
const validataTemplateNameAxios = (params) => postJson(pathList.validataTemplateNameUrl,params);
const saveTemplateNameAxios = (params) => postJson(pathList.saveTemplateNameUrl,params);
const queryHxTemplateTableAxios = () => get(pathList.queryHxTemplateTableUrl);
const saveHxTemplateTableAxios = (params) => postJson(pathList.saveHxTemplateTableUrl,params);
const initCategoryModelAxios = (params) => postJson(pathList.initCategoryModelUrl,params);
const saveTemplateCopyAxios = (params) => postJson(pathList.saveTemplateCopyUrl,params);
const initCopyTableAxios = (params) => postJson(pathList.initCopyTableUrl,params);
const initCategoryListByCopyTemplateAxios = (params) => postJson(pathList.initCategoryListByCopyTemplateUrl,params);
const saveCopyCategoryTemplateAxios = (params) => postJson(pathList.saveCopyCategoryTemplateUrl,params);
const queryPropertyEnumByInstanceAxios = (params) => postJson(pathList.queryPropertyEnumByInstanceUrl,params);
export{
  initColumnTemplateAxios,saveShowTemplateColumnAxios,querySelectTemplateColumnAxios,deleteTemplateAxios,
  useTemplateAllAxios,useTemplateOneAxios,getByCategoryGroupTemplateAxios,getByCategoryDataTemplateAxios,
  saveGroupCategoryTemplateAxios,saveGroupPropertyTemplateAxios,validataTemplateNameAxios,saveTemplateNameAxios,
  queryHxTemplateTableAxios,saveHxTemplateTableAxios,initCategoryModelAxios,saveTemplateCopyAxios,
  initCopyTableAxios,initCategoryListByCopyTemplateAxios,saveCopyCategoryTemplateAxios,queryPropertyEnumByInstanceAxios
}
