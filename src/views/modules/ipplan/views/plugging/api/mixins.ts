const makeUp = (d): string => {
  return d < 10 ? "0" + d : d + "";
};

export const formatData = isDate => {
  let date = new Date(isDate);
  let seperator1 = "-";
  let seperator2 = ":";
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let hours = date.getHours();
  let minutes = date.getMinutes();
  let seconds = date.getSeconds();
  return (
    date.getFullYear() +
    seperator1 +
    makeUp(month) +
    seperator1 +
    makeUp(day) +
    " " +
    makeUp(hours) +
    seperator2 +
    makeUp(minutes) +
    seperator2 +
    makeUp(seconds)
  );
};

export default {
  methods: {
    formatData
  }
};
