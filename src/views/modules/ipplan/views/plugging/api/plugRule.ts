import { http } from "@/utils/http";
let BathPath = "eam-ip-plan/";
export const apiList = {
  //规则新增 修改 ipplan/plug/saveOrUpdateRule
  saveOrUpdateRule(params) {
    return http.postJson(BathPath + "ipplan/plug/saveOrUpdateRule", params);
  },

  //删除
  deleteOne(params) {
    return http.get(BathPath + "ipplan/plug/deleteRule", { params });
  },
  //  批量删除
  deleteByIds(params) {
    return http.postJson(BathPath + "ipplan/plug/deleteRuleByIds", params);
  },
  //单位
  getDept(params) {
    return http.get("eam-ip-plan/ipplan/getDept", { params });
  }
};
