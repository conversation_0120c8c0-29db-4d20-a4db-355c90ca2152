import { http } from "@/utils/http";
let BathPath = "eam-ip-plan/";
export const apiList = {
  BathPath: BathPath,
  getPlugStatistical(queryDate) {
    return http.postJson(BathPath + "ipplan/plug/plugStatistical", queryDate);
  },
  getDataSource(queryDate) {
    return http.postJson(BathPath + "ipplan/plug/plugDataSource", queryDate);
  },
  getEamTop(url, queryDate) {
    return http.postJson(BathPath + url, queryDate);
  },

  //新增 修改  白名单

  saveOrUpdateWhitelist(params) {
    return http.postJson(
      BathPath + "ipplan/plug/saveOrUpdateWhitelist",
      params
    );
  },
  savePlug(params) {
    return http.postJson(BathPath + "ipplan/plug/savePlug", params);
  },
  //删除白名单 ipplan/plug/deleteWhitelist
  deleteWhitelist(params) {
    return http.get(BathPath + "ipplan/plug/deleteWhitelist", { params });
  },
  //  批量删除白名单   ipplan/plug/deleteWhitelistByIds
  deleteWhitelistByIds(params) {
    return http.postJson(BathPath + "ipplan/plug/deleteWhitelistByIds", params);
  },

  //  下载模板 ipplan/plug/exportTemplate
  exportTemplate(params) {
    return http.getBlob(BathPath + "ipplan/plug/exportTemplate", params);
  },

  //  导出 ipplan/plug/exportData
  exportData(params) {
    return http.postBlobWithJson(BathPath + "ipplan/plug/exportData", params);
  },
  //  导入 ipplan/plug/importData
  importData(params) {
    return http.postJson(BathPath + "ipplan/plug/importData", params);
  },
  //  申请解封ipplan/plug/applyUnPlug
  applyUnPlug(params) {
    return http.postJson(BathPath + "ipplan/plug/applyUnPlug", params);
  },
  //  确认解封 ipplan/plug/confirmUnPlug
  confirmUnPlug(params) {
    return http.postJson(BathPath + "ipplan/plug/confirmUnPlug", params);
  },
  //  批量解封
  applyBatchUnPlug(params) {
    return http.postJson(BathPath + "ipplan/plug/applyBatchUnPlug", params);
  },
  //xiangqig ipplan/plug/plugDetail
  plugDetail(params) {
    return http.postJson(BathPath + "ipplan/plug/plugDetail", params);
  },

  //  导入解封 文件类型为 text ipplan/plug/importUnPlugData
  importUnPlugData(params) {
    return http.postJson(BathPath + "ipplan/plug/importUnPlugData", params);
  },

  searchBlacklist(params) {
    return http.get(BathPath + "ipplan/plug/searchBlacklist", { params });
  }
};
