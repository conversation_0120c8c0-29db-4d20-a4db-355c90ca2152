<!--<template>-->
<!--  <el-container>-->
<!--    <el-header>-->
<!--      <el-form :inline="true" ref="form"  :model="formData" >-->
<!--        <el-form-item prop="ruleContent" label="规则内容">-->
<!--          <el-input v-model="formData.ruleContent"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item prop="ruleType" label="规则类型" label-width="80px">-->
<!--          <el-select v-model="formData.ruleType" placeholder="请选择规则类型" >-->
<!--            <el-option label="未登记资产" value="未登记资产"></el-option>-->
<!--            <el-option label="未知资产" value="未知资产"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button type="primary" @click="onSubmit">查询</el-button>-->
<!--          <el-button @click="resetForm">重置</el-button>-->
<!--        </el-form-item>-->
<!--        <el-form-item style="float: right">-->
<!--          <el-button type="primary" @click="workForm('add')">新增</el-button>-->
<!--          <el-button type="danger" @click="deleteByIds('multiple')">批量删除</el-button>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--    </el-header>-->
<!--    <el-main>-->
<!--      <im-table-->
<!--        ref="columnTable"-->
<!--        method="post"-->
<!--        request-data-root="data"-->
<!--        read-property="records"-->
<!--        :pagination="{total: 'total', currentPage: 'pageNum', pageSize: 'pageSize'}"-->
<!--        :columns="tableColumns"-->
<!--        :url="url"-->
<!--        id-key="id"-->
<!--        @on-load-before="onLoadBefore"-->
<!--        enable-hide-columns-->
<!--        columns-sortable-->
<!--      >-->

<!--        <template v-slot:isEnable="scope">-->
<!--          <el-switch-->
<!--            @change="changeStatus(scope.row)"-->
<!--            v-model="scope.row.isEnable"-->
<!--            active-color="#13ce66"-->
<!--            inactive-color="#ff4949"-->
<!--            active-value="1"-->
<!--            inactive-value="0">-->
<!--          </el-switch>-->
<!--        </template>-->

<!--        <template v-slot:operateTable="scope">-->
<!--          <el-button type="text" @click="workForm('edit',scope.row)">修改</el-button>-->
<!--          <el-button type="text" @click="deleteOne(scope.row)">删除</el-button>-->
<!--        </template>-->
<!--      </im-table>-->
<!--    </el-main>-->
<!--    <el-dialog title="资产封堵规则" :visible="dialogFormVisible" :before-close="closeFormVisible">-->
<!--      <el-form  ref="dialogForm" :rules="rules" :model="dialogForm" style="height: 450px;overflow: auto">-->
<!--        <el-form-item label="规则名称" prop="ruleName" :label-width="formLabelWidth">-->
<!--          <el-input v-model="dialogForm.ruleName" autocomplete="off"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="规则类型" prop="ruleType" :label-width="formLabelWidth">-->
<!--          <el-select v-model="dialogForm.ruleType" placeholder="请选择规则类型" @change="changeType">-->
<!--            <el-option label="未登记资产" value="未登记资产"></el-option>-->
<!--            <el-option label="未知资产" value="未知资产"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="规则内容" :label-width="formLabelWidth">-->
<!--          <el-tabs v-model="dialogForm.ruleContentType" @tab-click="handleClick" style="margin-top: -5px;">-->
<!--            <el-tab-pane v-if="dialogForm.ruleType!='未知资产'" label="自定义" name="customize">-->
<!--              <discoveryScope   ref="discoveryScope"  v-model="dialogForm.ruleContent" label="noLabel" ></discoveryScope>-->
<!--            </el-tab-pane>-->
<!--            <el-tab-pane label="单位" name="dept">-->
<!--&lt;!&ndash;              <div style="height: 500px">&ndash;&gt;-->
<!--                <im-select-tree-->
<!--                  title-key="name"-->
<!--                  id-key="id"-->
<!--                  children-key="children"-->
<!--                  filterOnInput-->
<!--                  ref="treeForm"-->
<!--                  :maxContentHeight="500"-->
<!--                  v-model="dialogForm.ruleContentId"-->
<!--                  :model="ruleContentIdOptions"-->
<!--                  style="width:100%"-->
<!--                  clearable-->
<!--                  fixedPosition-->
<!--                  multipleTags-->
<!--                />-->
<!--&lt;!&ndash;              </div>&ndash;&gt;-->
<!--            </el-tab-pane>-->
<!--          </el-tabs>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="执行周期" prop="executeCycle" :label-width="formLabelWidth">-->
<!--          <el-radio-group v-model="dialogForm.executeCycle">-->
<!--            <el-radio label="h">每小时</el-radio>-->
<!--            <el-radio label="d">每天</el-radio>-->
<!--            <el-radio label="w">每周</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="封禁说明" :label-width="formLabelWidth">-->
<!--          <div slot="label" style="color: red">-->
<!--            封禁说明-->
<!--          </div>-->
<!--          <div>封禁策略主要用于资产模块对资产进行封堵，策略生效期间，满足以下条件系统将自动解封：</div>-->
<!--          <div>1、已在资产台账内完成登记。</div>-->
<!--          <div>2、无安全事件、漏洞等风险存在。</div>-->
<!--          <div>3、安全事件、漏洞的工单均以处置。</div>-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="closeFormVisible">取 消</el-button>-->
<!--        <el-button type="primary" :loading="saveLoading" @click="saveOrUpdateRule">确 定</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
<!--  </el-container>-->
<!--</template>-->

<!--<script>-->
<!--import { pageMixin } from "@visual/modules/ipplan/views/plugging/api/mixins"-->
<!--import {apiList} from "@visual/modules/ipplan/views/plugging/api/plugRule,js";-->
<!--import discoveryScope from "@visual/modules/ip/assetsDiscovery/conponents/discoveryScope/indexVmodel"-->
<!--export default {-->
<!--  name: "rule",-->
<!--  mixins:[pageMixin],-->
<!--  data(){-->
<!--    return{-->
<!--      deptOptions:[],-->
<!--      formData:{-->
<!--        ruleContent:"",-->
<!--        ruleType:"",-->
<!--      },-->
<!--      tableColumns: [-->
<!--        { type: "selection" },-->
<!--        { label: "规则名称",align: "center", prop: "ruleName", },-->
<!--        { label: "规则类型", align: "center", prop: "ruleType", showOverflowTooltip: true },-->
<!--        { label: "规则内容", align: "center", prop: "ruleContent", showOverflowTooltip: true },-->
<!--        { label: "启用状态", align: "center", slot: "isEnable", },-->
<!--        { label: "创建时间", align: "center", prop: "createTime" },-->
<!--        { label: "更新时间", align: "center", prop: "updateTime", "sortable": true  },-->
<!--        { label: "操作", align: "center", slot: "operateTable" },-->
<!--      ],-->
<!--      url:"",-->
<!--      dialogFormVisible:false,-->
<!--      saveLoading:false,-->
<!--      dialogForm:{-->
<!--        id: null,-->
<!--        "ruleName":"",-->
<!--        "ruleType":"",-->
<!--        "ruleContentId":"",-->
<!--        "ruleContent":"",-->
<!--        "ruleContentType":"customize",-->
<!--        "isEnable":"1",-->
<!--        "createTime":this.formatData(new Date()),-->
<!--        "updateTime":this.formatData(new Date()),-->
<!--        "executeCycle":""-->
<!--      },-->
<!--      formLabelWidth:"100px",-->
<!--      activeName:"custom",-->
<!--      checkList:["h"],-->
<!--      ruleContentIdOptions:[],-->
<!--      rules:{-->
<!--        ruleName: [-->
<!--          { required: true, message: '请输入规则名称', trigger: 'blur' }-->
<!--        ],-->
<!--        ruleType: [-->
<!--          { required: true, message: '请选择规则类型', trigger: 'change' }-->
<!--        ],-->
<!--        ruleContentId: [-->
<!--          { required: false, message: '请输入规则内容', trigger: 'blur' }-->
<!--        ],-->
<!--        executeCycle: [-->
<!--          { required: true, message: '请选择执行周期', trigger: 'change' }-->
<!--        ],-->
<!--      }-->
<!--    }-->
<!--  },-->
<!--  components:{-->
<!--    discoveryScope-->
<!--  },-->
<!--  mounted() {-->
<!--    this.url="eam-ip-plan/ipplan/plug/ruleList"-->
<!--    apiList.getDept().then(res=>{-->
<!--      this.deptOptions=res.data[0]-->
<!--    })-->
<!--  },-->
<!--  methods:{-->
<!--    closeFormVisible() {-->
<!--      this.dialogFormVisible = false-->
<!--    },-->
<!--    changeType(val){-->
<!--      if(val=="未知资产"){-->
<!--        this.dialogForm.ruleContentType="dept"-->
<!--      }-->
<!--    },-->
<!--    changeStatus(row){-->
<!--      apiList.saveOrUpdateRule(row).then(res=>{-->
<!--        this.dialogFormVisible=false-->
<!--        this.$refs.columnTable.loadPage(1)-->
<!--      })-->
<!--    },-->
<!--    onSubmit(){-->
<!--      this.$refs.columnTable.loadPage(1)-->
<!--    },-->
<!--    resetForm(){-->
<!--      this.$refs.form.resetFields();-->
<!--      this.$refs.columnTable.loadPage(1)-->
<!--    },-->
<!--    onLoadBefore(params, requestConfig) {-->
<!--      Object.assign(requestConfig, {-->
<!--        headers: {-->
<!--          "Content-Type": "application/json"-->
<!--        }-->
<!--      });-->
<!--      Object.assign(params,this.formData);-->
<!--    },-->
<!--    getOptions() {-->
<!--      this.$axios.get('eam-ip-plan/ipplan/ipPool/trees').then(res => {-->
<!--        this.ruleContentIdOptions = res.data-->
<!--      })-->
<!--    },-->
<!--    copy(obj) {-->
<!--      let newObj = null-->
<!--      if (typeof obj === 'object' && obj !== null) {-->
<!--        newObj = obj instanceof Array ? [] : {}-->
<!--        for (let i in obj) {-->
<!--          newObj[i] = typeof obj[i] === 'object' ? this.copy(obj[i]) : obj[i]-->
<!--        }-->
<!--      } else {-->
<!--        newObj = obj-->
<!--      }-->
<!--      return newObj-->
<!--    },-->
<!--    workForm(type,row){-->
<!--      this.dialogFormVisible=true-->
<!--      this.getOptions()-->
<!--      if (type=="add"){-->
<!--        this.$nextTick(() => {-->
<!--          this.dialogForm = {-->
<!--            id: null,-->
<!--            "ruleName": "",-->
<!--            "ruleType": "",-->
<!--            ruleContentId: "",-->
<!--            "ruleContent":"",-->
<!--            "ruleContentType":"customize",-->
<!--            "isEnable":"1",-->
<!--            "createTime": this.formatData(new Date()),-->
<!--            "updateTime": this.formatData(new Date()),-->
<!--            "executeCycle":""-->
<!--          }-->
<!--          this.$refs.treeForm.currentText = ''-->
<!--          this.$refs.treeForm.setCurrentValue(null, false)-->
<!--        })-->
<!--      }else {-->

<!--        this.dialogForm = this.copy(row)-->
<!--        if(row.ruleContentId) {-->
<!--          this.dialogForm.ruleContent = ''-->
<!--        }-->
<!--        console.log('44444444', this.dialogForm);-->
<!--        this.dialogForm.updateTime = this.formatData(new Date())-->
<!--      }-->

<!--    },-->

<!--    saveOrUpdateRule(){-->
<!--      this.$refs.dialogForm.validate((vaild)=>{-->
<!--        if (vaild){-->
<!--          let flag=false-->
<!--          if (!this.dialogForm.ruleContentId && !this.dialogForm.ruleContent){-->
<!--            flag=true-->
<!--          }else {-->
<!--            flag=false-->
<!--          }-->
<!--          if (flag){-->
<!--            this.$message.warning("请完善规则内容")-->
<!--            return-->
<!--          }-->
<!--          this.saveLoading = true-->
<!--          if(this.dialogForm.ruleContentType == 'customize') {-->
<!--            this.dialogForm.ruleContentId = null-->
<!--          } else {-->
<!--            this.dialogForm.ruleContent = null-->
<!--          }-->
<!--          let params = {}-->
<!--          console.log('5555555', this.dialogForm)-->
<!--          if(!this.dialogForm.id) {-->
<!--            params = {-->
<!--              "ruleName": this.dialogForm.ruleName,-->
<!--              "ruleType":this.dialogForm.ruleType,-->
<!--              "ruleContentId": this.dialogForm.ruleContentId,-->
<!--              "ruleContent": this.dialogForm.ruleContent || '',-->
<!--              "ruleContentType": this.dialogForm.ruleContentType,-->
<!--              "isEnable": this.dialogForm.isEnable,-->
<!--              "createTime": this.dialogForm.createTime,-->
<!--              "updateTime": this.dialogForm.updateTime,-->
<!--              "executeCycle": this.dialogForm.executeCycle-->
<!--            }-->
<!--          } else {-->
<!--            params = {-->
<!--              id: this.dialogForm.id,-->
<!--              "ruleName": this.dialogForm.ruleName,-->
<!--              "ruleType":this.dialogForm.ruleType,-->
<!--              "ruleContentId": this.dialogForm.ruleContentId,-->
<!--              "ruleContent": this.dialogForm.ruleContent || '',-->
<!--              "ruleContentType": this.dialogForm.ruleContentType,-->
<!--              "isEnable": this.dialogForm.isEnable,-->
<!--              "createTime": this.dialogForm.createTime,-->
<!--              "updateTime": this.dialogForm.updateTime,-->
<!--              "executeCycle": this.dialogForm.executeCycle-->
<!--            }-->
<!--          }-->

<!--          apiList.saveOrUpdateRule(params).then(res=>{-->
<!--            this.dialogFormVisible=false-->
<!--            this.$refs.columnTable.loadPage(1)-->
<!--            this.$message.success('保存成功')-->
<!--            this.saveLoading = false-->
<!--          }).catch(() => {-->
<!--            this.saveLoading = false-->
<!--          })-->
<!--        }else {-->
<!--          this.$message.warning("校验失败")-->
<!--          this.saveLoading = false-->
<!--        }-->
<!--      })-->
<!--    },-->

<!--    deleteOne(row){-->
<!--      this.$confirm("此操作将永久删除, 是否继续?", "提示").then(() => {-->
<!--        apiList.deleteOne({id: row.id }).then(res=>{-->
<!--          this.$message.success("删除成功")-->
<!--          this.$refs.columnTable.loadPage(1)-->
<!--        }).catch(err=>{-->
<!--          this.$message.error("删除失败")-->
<!--        })-->
<!--      });-->
<!--    },-->
<!--    deleteByIds(){-->
<!--      let ids= this.$refs.columnTable.getSelectionKeys()-->
<!--      if (!ids.length){-->
<!--        this.$message.warning("请选择规则规则")-->
<!--        return-->
<!--      }-->
<!--      this.$confirm("此操作将永久删除, 是否继续?", "提示").then(() => {-->
<!--        apiList.deleteByIds(ids).then(res=>{-->
<!--          this.$message.success("删除成功")-->
<!--          this.$refs.columnTable.loadPage(1)-->
<!--        }).catch(err=>{-->
<!--          this.$message.error("删除失败")-->
<!--        })-->
<!--      });-->

<!--    },-->

<!--    handleClick(){-->
<!--      // this.dialogForm.ruleContentId=""-->
<!--      // this.dialogForm.ruleContentId=""-->
<!--    }-->
<!--  }-->
<!--};-->
<!--</script>-->

<!--<style scoped>-->

<!--</style>-->
<template>
  
</template>
