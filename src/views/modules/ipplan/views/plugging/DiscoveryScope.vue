<template>
  <el-form :model="dataForm" :rules="rules" ref="dataForm" label-width="100px">
    <el-form-item
      label="扫描范围"
      prop="ipAddress"
      :rules="[
           {
              validator: validateKey,
              required: true ,
              trigger: ['blur','change'],
           }
          ]"
      :class="{range:iprange}"
    >

      <el-row>
        <el-col :span="22">
          <el-input :disabled="disabled"
                    type="textarea"
                    :rows="2"
                    v-model="dataForm.ipAddress"
                    @change='onChange'
                    :value='dataForm.ipAddress'
          ></el-input>
<!--          <span style="color: red;font-size: 12px">{{ iptooltip }}</span>-->
        </el-col>
        <el-col :span="2">
          <el-popover
            placement="bottom-end"
            width="300"
            trigger="hover"
          >
            <p>
              支持单IP，同一网段IP范围、同一网段IP和子网；
            </p>
            <p>格式如下（一行一个）</p>
            <p>单个IP：***********</p>
            <p>IP范围：***********-*************</p>
            <p>IP范围：***********-255</p>
            <!--                <p>IP/子网掩码：***********/*************</p>-->
            <p>IP/子网掩码：***********/24</p>
            <el-button style="margin-left: 5px" slot="reference" icon="el-icon-question" circle></el-button>
          </el-popover>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script>

export default {
  name: "strategy",
  props:['disabled',"value"],

  model: {
    prop: 'value',
    event: 'changeMock'
  },
  data() {
    return {
      dataForm: {
        ipAddress:this.value
      },
      rules: {},
      iprange: false,
      iptooltip: "",
      currentValue: this.value
    };
  },
  mounted() {
  },
  components: {

  },
  methods: {
    onChange(val) {

      this.$emit('changeMock', val)

    },

    validateKey(list, val, callback) {
      let enterReg = /[\n]/g;
      //ipList判断行数
      let ipList = enterReg.exec(val)?.input?.split("\n");
      if (ipList) {
        //多行
        if (ipList.length < 17) {
          for (let i in ipList) {
            this.regRule(callback,ipList[i], i);
          }
        } else {
          return;
        }
      } else {
        //单行范围ip类型
        this.regRule(callback,val);
      }
    },

    regRule(callback,val, index) {
      //第一位1-254
      let ipSpotRegOne = "(?:25[0-4]|2[0-4]\\d|1?[1-9]\\d?|1[0-9]\\d?)\\.";
      //0-255.
      let ipSpotReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)\\.";
      //0-255
      let ipNoSoptReg = "(?:25[0-5]|2[0-4]\\d|1?\\d\\d?)";
      //0-254.
      let ipLessReg = "(?:25[0-4]|2[0-4]\\d|1?\\d\\d?)\\.";
      //链接符为-
      let currentRangeReg = `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\-(?:\\1\\2(?<endThree>${ipSpotReg}))?(?<end>${ipNoSoptReg}))?$`;
      //连接符为/
      let currentMaskReg =
        `^(?<one>${ipSpotRegOne})(?<two>${ipSpotReg})(?<three>${ipSpotReg})(?<four>${ipNoSoptReg})(?:\\/(?<end>(?:(?:(?:1[6-9])|(?:2[0-9])|(?:3[0-2])))))?$`;
      let checkRangeReg = new RegExp(currentRangeReg, "g");
      let checkMaskReg = new RegExp(currentMaskReg, "g");
      if (checkRangeReg.exec(val)) {
        //规则2、3
        //判断 后面C段 > 前面C段
        //判断 后面C段 = 前面C段 =>后面D段 > 前面D段
        checkRangeReg.lastIndex = 0;
        let data = checkRangeReg.exec(val);
        if (!data.groups.endThree) {
          //没有第endThree时
          this.iprange = false;
          this.iptooltip = "";
          return;
        } else {
          if (data.groups.three < data.groups.endThree) {
            this.iprange = false;
            this.iptooltip = "";
            return;
          } else if (data.groups.three == data.groups.endThree) {
            if (data.groups.four < data.groups.end) {
              this.iprange = false;
              this.iptooltip = "";
              callback()
              return;
            } else {
              callback()
              return;
            }
          } else {
            callback()
            return;
          }
        }
      } else if (checkMaskReg.exec(val)) {
        console.log(1111);
        //  规则4、5
        checkMaskReg.lastIndex = 0;
        let data = checkMaskReg.exec(val);
        if (data.groups.one <= 191 && data.groups.end <= 32 && data.groups.end >= 16) {
          this.iptooltip = "";
          this.iprange = false;
        } else if (data.groups.one <= 223 && data.groups.end <= 32 && data.groups.end >= 24) {
          this.iprange = false;
          this.iptooltip = "";
          callback()
          return;
        } else {
          this.iprange = false;
          this.iptooltip = "";
          console.log("2223")
          callback()
          return;
        }
      } else {
        // 没有匹配上
        this.iprange = true;
        this.iptooltip = index ? `请检查第${index / 1 + 1}行IP范围` : `请输入正确IP范围`;
        try {
          let last = val.split("/")[1];
          let d = parseInt(last);
          if(d >= 8 && d < 16) {
            this.iprange = false;
            this.iptooltip = "";
            callback();
          }
        } catch (e) {
        }
        callback(this.iptooltip)
      }
    },

  },
  activated() {
  },
  created() {
  },
  watch: {
    "dataForm.policyName":{
      deep: true,
      handler: function(newV) {
        if(newV.length>0){
          this.policyName=true
          this.policyNameTooltip=""
        }else{
          this.policyName=false
          this.policyNameTooltip="请输入策略名称"
        }
      }
    },
    "moveDeptFormData.destinationParentId": {
      deep: true,
      handler: function(newV) {
        if (newV) {
          this.iptooltip = "";
          this.iprange = false;
        }
      }
    },
    iprange(val) {
      if (val) {
        document.onkeydown = function(e) {
          //捕捉回车事件
          var ev = (typeof event != "undefined") ? window.event : e;
          if (ev.keyCode == 13 || event.which == 13) {
            return false;
          }
        };
      } else {
        document.onkeydown = function(e) {
          //捕捉回车事件
          var ev = (typeof event != "undefined") ? window.event : e;
          if (ev.keyCode == 13 || event.which == 13) {
            return true;
          }
        };
      }
    },
    value: {

      handler() {

        this.dataForm.ipAddress = this.value

      }

    }
  }
};
</script>

<style lang="scss" scoped>
.el-radio {
  margin: 0;
  padding: 0;
}

::v-deep.range {

  .el-textarea__inner {
    background: rgba(255, 0, 0, 0.2) !important;
  }

}
</style>
