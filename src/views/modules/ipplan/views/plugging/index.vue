<template>
  <div>
    <el-card style="height: 45px">
      <el-form ref="form" :model="form" :inline="true" label-width="80px">
        <el-form-item label=" ">
          <el-radio-group
            v-model="form.dateType"
            @change="radioGropChange"
            prop="dateType"
          >
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="1h">近1小时</el-radio-button>
            <el-radio-button label="24h">近24小时</el-radio-button>
            <el-radio-button label="7d">近7天</el-radio-button>
            <el-radio-button label="1m">近一个月</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="自定义" prop="dateType">
          <el-date-picker
            v-model="form.date"
            @change="dateChange"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label=" ">
          <el-button type="primary" @click="query">查询</el-button>
          <el-button @click="rest">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-row type="flex">
      <el-card style="width: 30%; margin-right: 10px" class="cardHeight">
        <div slot="header" class="slotHeader">
          <img
            class="titleIcon"
            src="./assets/images/unknow_asset.png"
            alt=""
          />
          <span class="titleSize">封禁管理分布</span>
        </div>
        <div
          style="
            width: 100%;
            height: 265px;
            display: flex;
            flex-direction: column;
            justify-content: center;
          "
        >
          <div>
            <div style="font-size: 18px; font-weight: 600">累计封禁数</div>
            <span style="font-size: 32px; font-weight: bold">{{
              plug + unPlug
            }}</span>
          </div>
          <div style="margin: 30px 0; width: 100%">
            <div style="width: 100%; display: flex">
              <div
                class="blue"
                :style="{
                  width: disposeCountWidth + '%',
                  height: '16px',
                  marginRight: '2px'
                }"
              ></div>
              <div
                class="green"
                :style="{
                  width:
                    nodisposeCountWidth < 1 && nodisposeCountWidth != 0
                      ? '1%'
                      : nodisposeCountWidth + '%',
                  height: '16px'
                }"
              ></div>
            </div>
          </div>
          <div>
            <div style="width: 100%; display: flex">
              <div style="width: 50%; height: 16px; margin-right: 2px">
                <div>
                  <span
                    style="
                      display: inline-block;
                      width: 8px;
                      height: 8px;
                      background: #1890ff;
                    "
                  ></span>
                  <span style="padding-left: 5px">封堵</span>
                </div>
                <div
                  style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #1890ff;
                    padding-left: 12px;
                  "
                >
                  {{ plug }}
                </div>
              </div>
              <div style="width: 50%; height: 16px">
                <div>
                  <span
                    style="
                      display: inline-block;
                      width: 8px;
                      height: 8px;
                      background: #55c8ab;
                    "
                  ></span>
                  <span style="padding-left: 5px">解禁</span>
                </div>
                <div
                  style="
                    font-size: 18px;
                    font-weight: bold;
                    color: #55c8ab;
                    padding-left: 12px;
                  "
                >
                  {{ unPlug }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
      <el-card style="width: 30%; margin-right: 10px" class="cardHeight">
        <div slot="header" class="slotHeader">
          <img
            class="titleIcon"
            src="./assets/images/asset_source.png"
            alt=""
          />
          <span class="titleSize">封堵数据来源</span>
        </div>
        <div>
          <ChartComponent :option="cakeChart" height="245px"></ChartComponent>
          <!--          <compCharts :option="cakeChart" height-charts="245px"></compCharts>-->
        </div>
      </el-card>
      <el-card style="width: calc(40% - 20px)" class="cardHeight">
        <div slot="header" class="slotHeader">
          <img class="titleIcon" src="./assets/images/asset_gs.png" alt="" />
          <span class="titleSize">封堵资产 TOP5</span>
        </div>
        <div style="position: relative">
          <div
            style="
              position: absolute;
              cursor: pointer;
              display: flex;
              z-index: 100;
            "
          >
            <div
              @click="assetTOP5 = false"
              :class="{ titleColor: !assetTOP5 }"
              style="font-size: 13px; height: 15px; margin-right: 10px"
            >
              按IP统计
            </div>
            <div
              style="
                border-left: 1px solid #a3a7b9;
                font-size: 13px;
                height: 15px;
                margin-right: 10px;
              "
            ></div>
            <div
              @click="assetTOP5 = true"
              :class="{ titleColor: assetTOP5 }"
              style="font-size: 13px; height: 15px"
            >
              按组织统计
            </div>
          </div>
          <ChartComponent :option="barChart" height="245px"></ChartComponent>
          <!--          <compCharts :option="barChart" height-charts="245px"></compCharts>-->
        </div>
      </el-card>
    </el-row>
    <el-card style="margin-top: 10px; padding: 0px">
      <el-row style="margin-top: 20px">
        <el-col :span="24" style="margin-top: -10px; margin-bottom: 10px">
          <im-search-form
            :fuzzyable="true"
            class="search-form"
            select-width="210px"
            :search-style="{ width: '70%' }"
            :columns="columns"
            :options="{ placeholder: '请输入 ${name}' }"
            fit
            :col-num-per-row="4"
            label-width="120px"
            @on-update-row-num="val => (conditionRowNum = val)"
            @on-expanded-change="val => (expandConditions = val)"
            @on-search="onSearch"
          >
          </im-search-form>
        </el-col>
        <el-col :span="6" style="text-align: left">
          <el-radio-group v-model="activeName" @change="handleClick">
            <el-radio-button label="plugging">封堵</el-radio-button>
            <el-radio-button label="unblock">解封</el-radio-button>
          </el-radio-group>
        </el-col>
        <el-col :span="18" style="text-align: right">
          <div style="float: right; display: flex; align-items: center">
            <el-button
              v-has="'ipmodular_plugging_permissions:blackSetting'"
              type="text"
              @click="blackList"
              style="margin-right: 15px"
              >黑名单管理</el-button
            >
            <el-button
              v-has="'ipmodular_plugging_permissions:whiteSetting'"
              type="text"
              @click="whiteList"
              style="margin-right: 15px"
              >白名单设置</el-button
            >
            <!-- v-has="'ipmodular_plugging_permissions:blockingRule'" -->
            <el-dropdown
              v-has="'ipmodular_plugging_permissions:blockingRule'"
              @command="routeCommand"
            >
              <el-button type="text" style="margin-right: 15px">
                封堵规则管理<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="plugn"
                    >资产封堵设置</el-dropdown-item
                  >
                  <el-dropdown-item command="events"
                    >事件封堵设置</el-dropdown-item
                  >
                  <!--              <el-dropdown-item>漏洞封堵设置</el-dropdown-item>-->
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-dropdown
              v-show="activeName == 'plugging'"
              v-has="'ipmodular_plugging_permissions:moreOperate'"
              @command="handleCommand"
            >
              <el-button type="text" style="margin-right: 15px">
                更多操作<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="addPlug">人工封禁</el-dropdown-item>
                <el-dropdown-item command="importList"
                  >批量封禁</el-dropdown-item
                >
                <!--                <el-dropdown-item command="exportData">导出数据</el-dropdown-item>-->
                <el-dropdown-item command="unblockList"
                  >勾选解封</el-dropdown-item
                >
                <el-dropdown-item command="elUploadText">
                  <el-upload
                    ref="elUploadText"
                    action="xx"
                    accept=".txt"
                    :auto-upload="false"
                    :drag="false"
                    multiple
                    :on-change="unsealUploadChange"
                    :on-success="uploadTextSuccess"
                    :on-error="uploadTextError"
                  >
                    <!-- /rest//eam-ip-plug/ipplan/plug/importUnPlugData -->
                    批量解封
                    <el-tooltip
                      class="item"
                      effect="dark"
                      placement="top-start"
                    >
                      <div slot="content">
                        <span
                          >支持单IP，同一网殷IP范围、同一网段IP和子网格式如下
                          (一行一个)</span
                        ><br />
                        <span>单个P: ***********</span><br />

                        <span>IP范围: ***********-*************</span><br />
                        <span>IP范围: ***********-255</span><br />
                        <span>IP/子网掩码:***********/24</span><br />
                        <span>说明:为保证扫描效率，子网掩码需大于等于16</span
                        ><br />
                      </div>
                      <icon class="el-icon-warning-outline"></icon>
                    </el-tooltip>
                  </el-upload>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              type="text"
              @click="exportData"
              v-has="'ipmodular_plugging_permissions:export'"
            >
              导出
            </el-button>
          </div>
        </el-col>
        <el-col>
          <!--        <el-tabs v-model="activeName" @tab-click="handleClick">-->
          <!--          <el-tab-pane label="封堵" name="plugging">-->
          <!--          </el-tab-pane>-->
          <!--          <el-tab-pane label="解封" name="unblock">-->
          <!--          </el-tab-pane>-->
          <!--        </el-tabs>-->
          <im-table
            ref="columnTable"
            method="post"
            request-data-root="data"
            read-property="records"
            :pagination="pagination"
            :columns="
              activeName == 'plugging'
                ? tableColumns.plugn
                : tableColumns.unPlugn
            "
            :url="url"
            id-key="id"
            style="margin-top: 10px"
            :height="'calc(100vh - 575px)'"
            @on-load-before="onLoadBefore"
            enable-hide-columns
            columns-sortable
          >
            <template v-slot:plugLabel="scope">
              <el-tag
                v-for="(item, i) of tableTag(scope.row.plugLabel)"
                :key="i"
                :type="getTagType(item)"
                style="margin-right: 6px"
                @click="moveToIp(item)"
              >
                {{ item }}
              </el-tag>
            </template>
            <template v-slot:operateTable="scope">
              <template v-if="scope.row.isApply == 1">
                <el-button
                  v-has="'ipmodular_plugging_permissions:applyUnPlug'"
                  type="text"
                  @click="applyUnPlug(scope.row)"
                  >申请解封</el-button
                >
              </template>
              <template v-if="scope.row.isApply == 2">
                <el-button
                  v-has="'ipmodular_plugging_permissions:confirmUnPlug'"
                  type="text"
                  @click="confirmUnPlug(scope.row)"
                  >确认解封</el-button
                >
              </template>
              <el-button type="text" @click="doDetail(scope.row)"
                >详情</el-button
              >
            </template>
          </im-table>
        </el-col>
      </el-row>
    </el-card>

    <!-- 黑名单   -->
    <el-dialog
      title="黑名单查询"
      :visible.sync="blackListrawer"
      width="30%"
      :wrapperClosable="false"
      :before-close="
        () => {
          blackListrawer = false;
        }
      "
    >
      <el-header class="header">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-input
              v-model="ipAddress"
              placeholder="请输入IPV4地址"
              clearable
              @keyup.enter.native="searchDetail"
              @blur="
                e => {
                  IPChange(e);
                }
              "
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="searchDetail"
              ></el-button>
            </el-input>
          </el-col>
        </el-row>
      </el-header>
      <el-main class="main">
        <el-card style="height: 160px">
          <p
            v-if="
              searchResult === null ||
              searchResult === '' ||
              // searchResult === {}
              false
            "
            style="color: red; text-align: center"
          >
            未查询到相应数据
          </p>
          <el-row v-else>
            <el-col :span="24">
              <div v-for="(value, key) in searchResult" :key="key">
                <p>
                  <span
                    style="
                      margin-right: 15px;
                      text-align: right;
                      width: 70px;
                      display: inline-block;
                    "
                    >{{ key }}:</span
                  >
                  <span style="font-weight: bold; text-align: left">{{
                    value
                  }}</span>
                </p>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-main>
    </el-dialog>
    <!--    批量解封原因-->
    <!-- 白名单   -->
    <el-drawer
      title="白名单"
      :visible.sync="whiteListrawer"
      size="60%"
      :wrapperClosable="false"
      :before-close="
        () => {
          whiteListrawer = false;
        }
      "
    >
      <el-row style="padding: 5px 10px">
        <el-button type="primary" @click="addWhiteList">新增</el-button>
        <el-button type="danger" @click="deleteWhitelistByIds"
          >批量删除</el-button
        >
        <im-table
          ref="whiteTable"
          method="post"
          request-data-root="data"
          read-property="records"
          :pagination="{
            total: 'total',
            currentPage: 'pageNum',
            pageSize: 'pageSize'
          }"
          :columns="whiteColumns"
          url="eam-ip-plan/ipplan/plug/whitelistList"
          id-key="id"
          @on-load-before="onLoadBefore"
          enable-hide-columns
          columns-sortable
        >
          <template v-slot:isEnable="scope">
            <el-switch
              @change="changeStatus(scope.row)"
              v-model="scope.row.isEnable"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-value="1"
              inactive-value="0"
            >
            </el-switch>
          </template>
          <template v-slot:operateTable="scope">
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click.native="editWhiteList(scope.row)"
              >修改</el-link
            >
            <el-link
              type="primary"
              @click.native.prevent="deleteWhitelist1(scope.row)"
              >删除</el-link
            >
          </template>
        </im-table>
      </el-row>
    </el-drawer>
    <!--    批量解封原因-->
    <el-drawer
      title="批量解封"
      :visible.sync="unblockDrawer"
      size="50%"
      :before-close="
        () => {
          unblockDrawer = false;
        }
      "
    >
      <div class="demo-drawer__content">
        <el-form
          :model="unblockForm"
          :rules="unblockFormRules"
          ref="unblockFormRef"
        >
          <el-form-item
            label="解封原因"
            :label-width="formLabelWidth"
            prop="plugDescribe"
          >
            <el-input
              type="textarea"
              :rows="5"
              v-model="unblockForm.plugDescribe"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer">
          <el-button type="primary" @click="clickUnblock">确 定</el-button>
          <el-button @click="unblockDrawer = false">取 消</el-button>
        </div>
      </div>
    </el-drawer>
    <!--    解封确认-解封管理-->
    <el-drawer
      :title="title"
      :visible.sync="unblockOneDrawer"
      size="50%"
      :before-close="
        () => {
          unblockOneDrawer = false;
        }
      "
    >
      <div class="demo-drawer__content">
        <el-form
          :model="applyUnlockForm"
          :rules="applyUnlockFormRules"
          ref="applyUnlockFormRef"
        >
          <el-form-item label="IPV4地址" :label-width="formLabelWidth">
            {{ applyUnlockForm.ipAddress }}
          </el-form-item>
          <el-form-item label="所属单位" :label-width="formLabelWidth">
            {{ applyUnlockForm.deptName }}
          </el-form-item>
          <el-form-item
            label="申请解封原因"
            :label-width="formLabelWidth"
            prop="plugDescribe"
          >
            <el-input
              type="textarea"
              :rows="5"
              v-model="applyUnlockForm.plugDescribe"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="demo-drawer">
          <el-button type="primary" @click="confirmPlug">确 定</el-button>
          <el-button @click="unblockOneDrawer = false">取 消</el-button>
        </div>
      </div>
    </el-drawer>
    <!--    详情-->
    <el-drawer title="详情" :visible.sync="detailDrawer" size="50%">
      <div class="demo-drawer__content">
        <el-form :inline="true">
          <el-row>
            <el-col :span="8">
              <el-form-item label="IPV4地址" :label-width="formLabelWidth">
                {{ plugDetail.ipAddress }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属单位" :label-width="formLabelWidth">
                {{ plugDetail.deptName }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="封堵状态" :label-width="formLabelWidth">
                {{ plugDetail.plugStatus }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-timeline style="max-height: 80vh; overflow: auto; margin-top: 15px">
          <el-timeline-item
            v-for="(item, i) in plugDetail.list"
            :key="i"
            :timestamp="item.plugDateFlow"
            placement="top"
          >
            <el-row style="margin-top: -25px">
              <el-card
                v-for="(item1, index) of item.list"
                :key="index"
                :style="{ marginTop: index != 0 ? '10px' : '5px' }"
              >
                <el-row>
                  <el-col :span="6"
                    ><span>类型: {{ item1.plugType }}</span></el-col
                  >
                  <el-col :span="6" v-if="item1.plugType != '解封'"
                    ><span>封堵标签: {{ item1.plugLabel }}</span></el-col
                  >
                  <el-col :span="6"
                    ><span>方式: {{ item1.way }}</span></el-col
                  >
                  <el-col :span="6"
                    ><span>执行时间: {{ item1.plugDate }}</span></el-col
                  >
                  <el-col :span="6"
                    ><span>操作账号: {{ item1.userName }}</span></el-col
                  >
                  <el-col
                    :span="24"
                    v-if="item1.plugType == '解封'"
                    style="margin-top: 20px"
                    ><span>解封原因: {{ item1.plugDescribe }}</span></el-col
                  >
                  <el-col :span="24" v-else style="margin-top: 20px"
                    ><span>封控原因: {{ item1.plugDescribe }}</span></el-col
                  >
                </el-row>
              </el-card>
            </el-row>
          </el-timeline-item>
        </el-timeline>
        <el-link
          style="margin-left: 190px; margin-right: 15px"
          v-show="detailMoreStatus"
          type="primary"
          href="#"
          :underline="false"
          @click.native="getPlugDetail(1)"
        >
          点击查看更多
          <i class="el-icon-arrow-down"></i>
        </el-link>
        <el-link
          type="primary"
          :style="{ marginLeft: detailMoreStatus ? '0px' : '190px' }"
          v-show="detailLessStatus"
          href="#"
          :underline="false"
          @click.native="getPlugDetail(2)"
        >
          点击收起
          <i class="el-icon-arrow-up"></i>
        </el-link>
        <!--        <div class="demo-drawer">-->
        <!--          <el-button type="primary">确 定</el-button>-->
        <!--          <el-button>取 消</el-button>-->
        <!--        </div>-->
      </div>
    </el-drawer>
    <!--    导入-->
    <el-drawer title="上传文件" :visible.sync="importDrawer" size="50%">
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        :limit="1"
        accept=".xlsx"
        :headers="uploadHeaders"
        action="//"
        auto-upload
        :on-error="uploadError"
        :on-success="uploadSuccess"
        :on-change="changeUpload"
        multiple
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text"><em>点击上传文件</em> 或者拖拽上传</div>
        <div class="upload-desc">只能上传excel文件</div>
      </el-upload>

      <el-button @click="exportTemplate" type="text" icon="el-icon-download">
        导入封堵模板下载
      </el-button>

      <!--      <div class="demo-drawer">-->
      <!--        <el-button type="primary">确 定</el-button>-->
      <!--        <el-button>取 消</el-button>-->
      <!--      </div>-->
    </el-drawer>
    <el-dialog :title="whiteFormTitle" :visible.sync="dialogwhiteListVisible">
      <el-form ref="whiteForm" :model="whiteForm" :rules="whiteFormRules">
        <el-form-item label="">
          <!--          <el-input v-model="whiteForm.ipMaskAddress" autocomplete="off"></el-input>-->
          <discoveryScope
            ref="discoveryScope"
            v-model="whiteForm.ipMaskAddress"
            label="noLabel"
          ></discoveryScope>
        </el-form-item>
        <el-form-item
          label="关联功能"
          prop="involvedModule"
          :label-width="formLabelWidth"
        >
          <el-select
            v-model="whiteForm.involvedModule"
            placeholder="请选择关联功能"
          >
            <el-option label="安全事件" value="安全事件"></el-option>
            <el-option label="安全漏洞" value="安全漏洞"></el-option>
            <el-option label="未登记资产" value="未登记资产"></el-option>
            <el-option label="未知资产" value="未知资产"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="生效时间"
          prop="time"
          :label-width="formLabelWidth"
        >
          <el-date-picker
            is-range
            v-model="whiteForm.time"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            :default-value="defaultValue"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <!--        <el-form-item label="是否启用" prop="isEnable" :label-width="formLabelWidth">-->
        <!--          <el-switch-->
        <!--            v-model="whiteForm.isEnable"-->
        <!--            active-color="#13ce66"-->
        <!--            inactive-color="#ff4949"-->
        <!--            active-value="1"-->
        <!--            inactive-value="0">-->
        <!--          </el-switch>-->
        <!--        </el-form-item>-->
        <el-form-item
          label="描述"
          prop="description"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="whiteForm.description"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveOrUpdateWhitelist"
          >保存</el-button
        >
        <el-button @click="dialogwhiteListVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="plugFormTitle"
      :visible.sync="dialogplugVisible"
      width="40%"
    >
      <el-form ref="plugForm" :model="plugForm" :rules="plugFormRules">
        <el-form-item
          label="IP地址"
          prop="ipAddress"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="plugForm.ipAddress"
            autocomplete="off"
            @blur="
              e => {
                IPChange(e);
              }
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          label="封堵来源"
          prop="plugLabel"
          :label-width="formLabelWidth"
        >
          <el-select
            v-model="plugForm.plugLabel"
            placeholder="请选择封堵来源"
            style="width: 100%"
          >
            <el-option
              :label="item"
              :key="item"
              :value="item"
              v-for="item in plugLabelList"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="防火墙"
          prop="firewall"
          :label-width="formLabelWidth"
        >
          <!--<el-select
            v-model="selectedFirewalls"
            multiple
            placeholder="请选择防火墙"
            style="width: 100%"
            @change="handleSelectChange"
          >
            <el-option :label="item.name" :key="item.name" :value="item.value"
                       v-for="item in firewallList"></el-option>
          </el-select>-->

          <im-select-tree
            title-key="combinedTitle"
            id-key="id"
            v-model="plugForm.firewallIds"
            style="width: 100%"
            filterOnInput
            clearable
            :model="preparedFirewallList"
            enable-scroll
            only-leaf-select
            children-key="children"
            multiple
          >
          </im-select-tree>
        </el-form-item>
        <el-form-item
          label="封堵时间"
          prop="expireTime"
          :label-width="formLabelWidth"
        >
          <el-select
            v-model="plugForm.expireTime"
            placeholder="封堵时间"
            style="width: 100%"
          >
            <el-option
              :label="item.name"
              :key="item.name"
              :value="item.value"
              v-for="item in expireTimeList"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="封堵原因"
          prop="plugDescribe"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="plugForm.plugDescribe"
            autocomplete="off"
            type="textarea"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="savePlug">保存</el-button>
        <el-button @click="dialogplugVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pageMixin, { formatData } from "./api/mixins";
// import compCharts from "@visual/modules/ip/ipViews/components/compCharts";
import { apiList } from "./api";
import moment from "moment";
import DiscoveryScope from "./DiscoveryScope.vue";
import axios, { http } from "@/utils/http";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";

export default {
  name: "index",
  mixins: [pageMixin],
  data() {
    return {
      selectedFirewalls: [], // 用于存储选中的值
      selectedFirewallDetails: [], // 用于存储选中的 value 和 ip
      fileList: [],
      //plugLabelList:["其他", "未知资产", "未登记资产", "安全事件", "安全漏洞"],
      plugLabelList: ["安全事件", "人工封禁", "其他"],
      //   firewallList:[{"name":"华为USG防火墙","value":"huaweiFirewallUsg"}],
      firewallList: [],
      expireTimeList: [
        { name: "1天", value: "1" },
        { name: "2天", value: "2" },
        { name: "3天", value: "3" },
        { name: "4天", value: "4" },
        { name: "5天", value: "5" },
        { name: "6天", value: "6" },
        { name: "7天", value: "7" },
        { name: "15天", value: "15" },
        { name: "30天", value: "30" },
        { name: "永久", value: "0" }
      ],
      form: {
        dateType: "all",
        date: []
      },
      assetTOP5: false,
      uploadHeaders: {},
      plugDetail: {
        list: [],
        deptName: "",
        ipAddress: "",
        plugStatus: ""
      },
      plug: 0,
      total: 0,
      pageSize: 5,
      pageNum: 0,
      detailMoreStatus: false,
      detailLessStatus: false,
      reqStatus: 0,
      title: null,
      unPlug: 0,
      defaultValue: [
        moment().format("YYYY-MM-DD HH:mm:ss"),
        moment().add(+1, "month").format("YYYY-MM-DD HH:mm:ss")
      ],
      pagination: {
        total: "total",
        currentPage: "pageNum",
        pageSize: "pageSize",
        options: {
          pageSizes: [10, 20, 30, 40, 50, 100, 500]
        },
        align: "right"
      },
      conditions: [],
      whiteFormRules: {
        ipMaskAddress: [
          { required: true, message: "请选择IP范围", trigger: "blur" }
        ],
        involvedModule: [
          { required: true, message: "请选择关联功能", trigger: "change" }
        ],
        time: [
          { required: true, message: "请选择生效时间", trigger: "change" }
        ],
        description: [
          { required: true, message: "请选择描述", trigger: "blur" }
        ]
      },
      plugFormRules: {
        ipAddress: [
          { required: true, message: "请输入IP地址", trigger: "blur" }
        ],
        plugLabel: [
          { required: true, message: "请选择封堵来源", trigger: "change" }
        ],
        firewallIds: [
          { required: true, message: "请选择防火墙", trigger: "change" }
        ],
        plugDescribe: [
          { required: true, message: "请输入封堵原因", trigger: "blur" }
        ]
      },

      cakeChart: null,
      sealChart: null,
      barChart: null,
      deptOption: [],
      activeName: "plugging",
      url: "",
      tableColumns: {
        plugn: [
          { type: "selection" },
          { label: "ip地址", align: "center", prop: "ipAddress" },
          {
            label: "所属单位",
            align: "center",
            prop: "deptName",
            showOverflowTooltip: true
          },
          { label: "封堵标签", align: "center", slot: "plugLabel" },
          { label: "封堵次数", align: "center", prop: "plugTimes" },
          { label: "封堵时间", align: "center", prop: "plugDate" },
          { label: "封堵方式", align: "center", prop: "way" },
          { label: "网络区域", align: "center", prop: "network" },
          {
            label: "操作",
            align: "center",
            slot: "operateTable",
            width: "300px"
          }
        ],
        unPlugn: [
          { type: "selection" },
          { label: "ip地址", align: "center", prop: "ipAddress" },
          {
            label: "所属单位",
            align: "center",
            prop: "deptName",
            showOverflowTooltip: true
          },
          // { label: "解封次数", align: "center", prop: "plugTimes", "sortable": true },
          // { label: "解封描述", align: "center", prop: "plugDescribe", },
          { label: "解封时间", align: "center", prop: "plugDate" },
          { label: "解封方式", align: "center", prop: "way" },
          { label: "网络区域", align: "center", prop: "network" },
          {
            label: "操作",
            align: "center",
            slot: "operateTable",
            width: "300px"
          }
        ]
      },
      ipAddress: "",
      searchResult: "",
      whiteListrawer: false,
      dialogplugVisible: false,
      plugListrawer: false,
      blackListrawer: false,
      whiteFormTitle: "添加白名单",
      plugFormTitle: "人工封堵",
      dialogwhiteListVisible: false,
      dialogblackListVisible: false,
      formLabelWidth: "120px",
      whiteForm: {
        ipMaskAddress: "",
        involvedModule: "",
        time: "",
        description: "",
        isEnable: 1
      },
      plugForm: {
        ipAddress: "",
        plugLabel: "",
        plugDescribe: "",
        firewall: "",
        firewallIds: "",
        expireTime: ""
      },
      whiteColumns: [
        { type: "selection" },
        {
          label: " IP/掩码地址",
          align: "center",
          prop: "ipMaskAddress",
          showOverflowTooltip: true
        },
        {
          label: "涉及模块",
          align: "center",
          prop: "involvedModule",
          showOverflowTooltip: true
        },
        {
          label: "是否启用",
          align: "center",
          slot: "isEnable",
          showOverflowTooltip: true
        },
        {
          label: "描述",
          align: "center",
          prop: "description",
          showOverflowTooltip: true
        },
        {
          label: "更新时间",
          align: "center",
          prop: "updateTime",
          showOverflowTooltip: true
        },
        {
          label: "操作",
          align: "center",
          slot: "operateTable",
          width: "300px"
        }
      ],
      unblockDrawer: false,
      unblockForm: { plugDescribe: "" },
      unblockFormRules: {
        plugDescribe: [
          { required: true, message: "请输入解封原因", trigger: "blur" }
        ]
      },
      applyUnlockFormRules: {
        plugDescribe: [
          { required: true, message: "请输入解封原因", trigger: "blur" }
        ]
      },
      applyUnlockForm: {
        plugId: null,
        ipAddress: null,
        deptName: null,
        plugDescribe: null,
        deptId: null
      },
      showStatus1: false,
      unblockOneDrawer: false,
      detailDrawer: false,
      importDrawer: false,
      basePath: "eam-ip-plan"
    };
  },
  components: {
    ChartComponent,
    DiscoveryScope
  },
  computed: {
    columns() {
      return [
        {
          field: "ipAddress",
          name: "IP地址",
          component: {
            fuzzyable: true,
            disableSearch: false,
            operator: "",
            type: "Input"
          }
        },
        {
          field: "deptId",
          name: "所属单位",
          component: {
            fuzzyable: true,
            disableSearch: false,
            operator: "",
            props: {
              model: this.deptOption,
              placeholder: "请选择所属组织",
              idKey: "deptId",
              titleKey: "deptName",
              enableScroll: true,
              // onlyLeafSelect: true,
              fixedPosition: true,
              appendToBody: true,
              maxContentHeight: 270,
              filterOnInput: true,
              filterByParent: true,
              style: "width: 100%"
            },
            type: "SelectTree"
          }
        },
        {
          field: "plugLabel",
          name: "封禁标签",
          component: {
            fuzzyable: true,
            disableSearch: false,
            operator: "",
            type: "Input"
          }
        },
        {
          field: "plugDate",
          name: "封堵时间",
          component: {
            type: "DatePicker",
            options: [],
            operator: "datetimerange",
            props: {
              type: "datetimerange",
              valueFormat: "yyyy-MM-dd HH:mm:dd"
            }
          }
        },
        {
          field: "network",
          name: "网络区域",
          component: {
            fuzzyable: true,
            disableSearch: false,
            operator: "exact",
            props: {
              model: [
                {
                  name: "内部网络",
                  value: "内部网络"
                },
                {
                  name: "外部网络",
                  value: "外部网络"
                }
              ],
              placeholder: "请选择网络区域",
              idKey: "name",
              titleKey: "value",
              enableScroll: true,
              // onlyLeafSelect: true,
              fixedPosition: true,
              appendToBody: true,
              maxContentHeight: 270,
              filterOnInput: true,
              filterByParent: true,
              style: "width: 100%"
            },
            type: "SelectTree"
          }
        }
      ];
    },
    nodisposeCountWidth() {
      return (this.unPlug / (this.plug + this.unPlug)).toFixed(2) * 100;
    },
    disposeCountWidth() {
      return (this.plug / (this.plug + this.unPlug)).toFixed(2) * 100;
    },
    preparedFirewallList() {
      return this.prepareFirewallList(this.firewallList);
    }
  },
  mounted() {
    this.url = "eam-ip-plan/ipplan/plug/plugList";
    http.get("framework/sysmanage/dept/getUserDeptTree").then(res => {
      this.deptOption = res.data;
    });
    this.initData();
    http.get("eam-ip-plan/ipplan/plug/queryFirewallList").then(res => {
      this.firewallList = res.data;
    });
  },
  watch: {
    assetTOP5: {
      handler(val) {
        let queryDate = "";
        if (this.form.date.length != 0) {
          queryDate = this.form.date;
        } else {
          queryDate = this.form.dateType;
        }
        this.getAssetTop5({ queryDate });
      }
    }
  },
  methods: {
    prepareFirewallList(nodes) {
      return nodes.map(node => ({
        ...node,
        combinedTitle:
          node.domain && node.domain.trim() !== ""
            ? `${node.name} (${node.domain})`
            : node.name,
        //   combinedValue: node.domain && node.domain.trim() !== '' ? `${node.value} (${node.domain})` : node.value,
        children: node.children
          ? this.prepareFirewallList(node.children)
          : undefined
      }));
    },
    onSelectChange(val, oldValue, currentText) {
      debugger;

      console.log(val);
      console.log(oldValue);
      console.log(currentText);

      // 将选中的值转换为包含 value 和 ip 的数组
      var firewall = val.map(value => {
        const node = this.findNodeByValue(this.prepareFirewallList, value);
        return { value, ip: node ? node.ip : "" };
      });

      console.log(firewall);
      console.log(this.plugForm.firewall);
    },
    handleSelectChange(selectedValues) {
      debugger;
      // 将选中的值转换为包含 value 和 ip 的数组
      this.plugForm.firewall = selectedValues.map(value => {
        const node = this.findNodeByValue(this.prepareFirewallList, value);
        return { value, ip: node ? node.ip : "" };
      });
    },
    findNodeByValue(nodes, value) {
      for (const node of nodes) {
        if (node.value === value) {
          return node;
        }
        if (node.children) {
          const childNode = this.findNodeByValue(node.children, value);
          if (childNode) {
            return childNode;
          }
        }
      }
      return null;
    },
    loadOpen() {
      this.loading = this.$loading({
        lock: true
      });
    },
    loadClose() {
      this.loading.close();
    },

    uploadTextSuccess() {
      this.$refs.elUploadText.clearFiles();
      this.$refs.columnTable.loadPage(1);
    },
    ipCheck(ip) {
      let pattern =
        /^(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$/;
      if (pattern.test(ip)) {
        return true;
      } else {
        return false;
      }
    },
    IPChange(event) {
      if (!event.target.value) {
        return;
      }
      if (!this.ipCheck(event.target.value)) {
        this.$message.error("IP输入格式有误！");
        event.target.value = "";
      }
    },
    uploadTextError() {
      this.$refs.elUploadText.clearFiles();
    },

    rest() {
      this.form.date = [];
      this.form.dateType = "all";
      this.$refs.columnTable.loadPage(1);
      this.initData();
    },
    query() {
      this.$refs.columnTable.loadPage(1);
      this.initData();
    },
    radioGropChange(val) {
      this.form.date = [];
      this.form.dateType = val;
    },
    dateChange(val) {
      this.form.dateType = null;
    },
    getTagType(item) {
      if (item == "未登记资产") {
        return;
      } else if (item == "安全事件") {
        return "warning";
      } else if (item == "未知资产") {
        return "danger";
      } else if (item == "安全隐患") {
        return "success";
      } else {
        return "info";
      }
    },
    uploadError(err) {
      console.log("5555555555", err);
      this.$refs.upload.clearFiles();
    },
    uploadSuccess() {
      this.$refs.upload.clearFiles();
      this.importDrawer = false;
      this.$refs.columnTable.loadPage(1);
    },
    changeUpload(file) {
      if (file) {
        let param = new FormData();
        param.append("file", file.raw);
        axios({
          url: apiList.BathPath + "/ipplan/plug/importData",
          method: "post",
          headers: {
            "Content-Type": "multipart/form-data"
          },
          data: param
        })
          .then(res => {
            //debug;
            if (res.status == "0") {
              this.$message.success("导入成功");
              this.uploadSuccess();
            } else {
              this.$message.error(res.msg);
              this.uploadError();
            }
          })
          .catch(err => {
            this.$message.error(err.msg);
            this.uploadError();
          });
      }
    },
    clickUnblock() {
      this.$refs.unblockFormRef.validate(valid => {
        if (valid) {
          let json = {
            plugDescribe: this.unblockForm.plugDescribe,
            plugIdList: this.$refs.columnTable.getSelectionKeys()
          };
          apiList.applyBatchUnPlug(json).then(res => {
            this.unblockDrawer = false;
            this.$message.success("正在批量解封");
            this.initData();
            this.$refs.columnTable.loadPage(1);
          });
        }
      });
    },

    exportTemplate() {
      apiList.exportTemplate({}).then(res => {
        this.$message.success("正在导出模板");
      });
    },
    moveToIp(val) {
      // if (val == "未登记资产") {
      //   this.$router.push({
      //     name: "est_eam_instance",
      //     query: {}
      //   });
      // } else if (val == "未知资产") {
      //   this.$router.push({
      //     name: "test_eam_unknownAssets",
      //     query: {}
      //   });
      // } else {
      //   this.$router.push({
      //     name: "security_overview",
      //     query: {}
      //   });
      // }
    },
    tableTag(val) {
      if (!val) {
        return [];
      }
      let arr = [];
      arr = val.split(",");
      return arr;
    },

    routeCommand(command) {
      if (command == "plugn") {
        this.$store.commit("backableview/forwardView", {
          title: "资产封堵",
          component: () => import("./rule.vue"),
          hideOnSwitchRoute: true,
          props: {},
          onClose: () => {
            this.$refs.columnTable.load();
          }
        });
      } else if (command == "events") {
        this.$store.commit("backableview/forwardView", {
          title: "事件封堵",
          // component: () =>
          //   import(
          //     "@/plugin/visual/modules/safe/views/rule/plugging/prequelRule.vue"
          //   ),
          hideOnSwitchRoute: true,
          props: {},
          onClose: () => {
            this.$refs.columnTable.load();
          }
        });
      }
    },
    applyUnPlug(row) {
      this.unblockOneDrawer = true;
      this.applyUnlockForm = {
        plugId: row.id,
        deptId: row.deptId,
        ipAddress: row.ipAddress,
        deptName: row.deptName,
        plugDescribe: ""
      };
      this.title = "申请解封";
    },
    copy(obj) {
      let newObj = null;
      if (typeof obj === "object" && obj !== null) {
        newObj = obj instanceof Array ? [] : {};
        for (let i in obj) {
          newObj[i] = typeof obj[i] === "object" ? this.copy(obj[i]) : obj[i];
        }
      } else {
        newObj = obj;
      }
      return newObj;
    },
    confirmPlug(row) {
      this.$refs.applyUnlockFormRef.validate(valid => {
        if (valid) {
          let postParams = Object.assign({}, this.applyUnlockForm);
          if (this.title == "申请解封") {
            postParams = Object.assign({}, this.applyUnlockForm);
            apiList.applyUnPlug(postParams).then(res => {
              this.$message.success("申请解封");
              this.$refs.columnTable.loadPage(1);
              this.unblockOneDrawer = false;
            });
          } else {
            postParams = Object.assign({}, this.applyUnlockForm, {
              confirmStatu: "1"
            });
            apiList.confirmUnPlug(postParams).then(res => {
              this.$message({
                type: "success",
                message: "解封成功!"
              });
              this.initData();
              this.$refs.columnTable.loadPage(1);
              this.unblockOneDrawer = false;
            });
          }
        }
      });
    },
    confirmUnPlug(row) {
      this.unblockOneDrawer = true;
      this.applyUnlockForm = {
        plugId: row.id,
        ipAddress: row.ipAddress,
        deptName: row.deptName,
        deptId: row.deptId,
        firewall: row.firewall,
        firewallId: row.firewallId,
        expireTime: row.expireTime,
        plugDescribe: ""
      };
      this.title = "确认解封";
    },
    movetoWhite(row) {
      apiList.saveOrUpdateWhitelist(row).then(res => {
        this.$message.success("移入白名单成功");
      });
    },
    exportData() {
      // let ids= this.$refs.whiteTable.getSelectionKeys()
      // if (!ids.length){
      //   this.$message.warning("请勾选需要导出的数据")
      // }

      let querys = {
        conditions: this.conditions,
        isPlug: this.activeName == "plugging" ? 1 : 0,
        pageSize: this.$refs.columnTable.page.pageSize,
        pageNum: this.$refs.columnTable.page.currentPage
      };
      apiList.exportData(querys).then(res => {
        this.$message.success("开始导出数据");
      });
    },

    importData() {},

    changeStatus(row) {
      apiList.saveOrUpdateWhitelist(row).then(res => {});
    },
    initData() {
      let queryDate = "";
      if (this.form.date.length != 0) {
        queryDate = this.form.date;
      } else {
        queryDate = this.form.dateType;
      }
      apiList.getPlugStatistical({ queryDate }).then(res => {
        this.plug = res.data.plug;
        this.unPlug = res.data.unPlug;
      });
      apiList.getDataSource({ queryDate }).then(res => {
        let chartData = [];
        for (let i in res.data) {
          let json = {};
          json.name = res.data[i].plugLabel;
          json.value = res.data[i].cnt;
          chartData.push(json);
        }
        this.cakeChart = {
          color: [
            "#409eff",
            "#50b7c1",
            "#7fb80e",
            "#ffc20e",
            "#F56C6C",
            "#909399"
          ],
          tooltip: {
            trigger: "item"
          },
          legend: {
            type: "scroll",
            orient: "vertical",
            right: "10%",
            top: "center",
            formatter: function (name) {
              // var data = this.cakeChart.series[0].data;
              var total = 0;
              var tarValue;
              let tarName = null;
              if (chartData && chartData.length != 0) {
                chartData.forEach(item => {
                  total += Number(item.value);
                });
                let singleData = chartData.filter(function (item) {
                  return item.name == name;
                });
                tarValue = singleData[0].value;
                tarName = singleData[0].name;
                var v = tarValue;
                var p = ((tarValue / total) * 100).toFixed(2);
                return `${tarName}  ${v}(${p}%)`;
              } else {
                return ``;
              }
            }
          },

          series: [
            {
              name: "封堵数据来源",
              type: "pie",
              radius: "80%",
              // width: "240px",
              // height: "240px",
              minAngle: 5,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)"
                }
              },
              label: {
                show: false
              },
              labelLine: {
                show: false
              },
              center: ["35%", "50%"],
              data: chartData
            }
          ]
        };
      });
      this.getAssetTop5({ queryDate });
    },
    getAssetTop5(queryDate) {
      let url = null;
      if (this.assetTOP5) {
        url = "ipplan/plug/plugDeptTop";
      } else {
        url = "ipplan/plug/plugEamTop";
      }
      apiList.getEamTop(url, queryDate).then(res => {
        let xData = [];
        let yData = [];
        for (let item of res.data) {
          xData.push(item.cnt);
          yData.push(item?.ipAddress || item?.deptName || "");
        }
        xData.reverse();
        yData.reverse();
        this.barChart = {
          color: ["#409eff"],
          xAxis: {
            type: "value",
            minInterval: 1
          },
          tooltip: {
            trigger: "item"
          },
          yAxis: {
            type: "category",
            data: yData
          },
          grid: {
            top: "15%",
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true
          },
          series: [
            {
              name: "封堵资产 TOP5",
              data: xData,
              type: "bar",
              barMaxWidth: "20"
            }
          ]
        };
      });
    },
    onSearch(val) {
      this.conditions = val;
      this.$refs.columnTable.loadPage(1);
    },
    handleClick() {
      this.$refs.columnTable.loadPage(1);
    },

    saveOrUpdateWhitelist() {
      this.$refs.whiteForm.validate(valid => {
        if (valid) {
          if (this.$refs.discoveryScope.iprange) {
            this.$message.warning("请完善规则内容");
            return;
          }

          let resultParams = this.copy(this.whiteForm);
          resultParams.effectStartTime = this.whiteForm.time[0];
          resultParams.effectEndTime = this.whiteForm.time[1];
          (resultParams.updateTime = this.formatData(new Date())),
            // resultParams.updateTime=new Date()
            apiList
              .saveOrUpdateWhitelist(resultParams)
              .then(res => {
                this.dialogwhiteListVisible = false;
                this.$refs.whiteTable.loadPage(1);
              })
              .catch(err => {
                console.log(err);
              });
        } else {
          this.$message.warning("校验失败");
        }
      });
    },

    savePlug() {
      this.loadOpen();
      this.$refs.plugForm.validate(valid => {
        if (valid) {
          debugger;
          // this.plugForm.firewall = this.plugForm.firewall.join(',');
          // resultParams.updateTime=new Date()
          apiList
            .savePlug(this.plugForm)
            .then(res => {
              this.loadClose();
              let data = res.data;
              if (data === "success") {
                this.dialogplugVisible = false;
                this.$message.success(res.message || res.msg);
                this.$refs.columnTable.loadPage(1);
              } else if (data === "fail") {
                this.$message.error(res.message || res.msg);
              }
            })
            .catch(err => {
              console.log(err);
              this.loadClose();
            });
        } else {
          this.loadClose();
          this.$message.warning("校验失败");
        }
      });
    },
    deleteWhitelist1(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          apiList
            .deleteWhitelist({ id: row.id })
            .then(res => {
              this.$message.success("删除成功");
              this.$refs.whiteTable.loadPage(1);
            })
            .catch(err => {
              this.$message.error("删除失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    deleteWhitelistByIds() {
      let ids = this.$refs.whiteTable.getSelectionKeys();
      if (!ids.length) {
        this.$message.warning("请选择规则白名单");
        return;
      }
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          apiList
            .deleteWhitelistByIds(ids)
            .then(res => {
              this.$message.success("删除成功");
              this.$refs.whiteTable.loadPage(1);
            })
            .catch(err => {
              this.$message.error("删除失败");
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    onLoadBefore(params, requestConfig) {
      let queryDate = "";
      if (this.form.date.length != 0) {
        queryDate = this.form.date;
      } else {
        queryDate = this.form.dateType;
      }

      let querys = {
        queryDate: queryDate,
        conditions: this.conditions,
        isPlug: this.activeName == "plugging" ? 1 : 0
      };

      Object.assign(requestConfig, {
        headers: {
          "Content-Type": "application/json"
        }
      });
      Object.assign(params, querys);
    },
    //封堵规则管理

    //更多操作
    handleCommand(command) {
      if (command == "addPlug") {
        this.addPlug();
      }
      if (command == "importList") {
        this.importDrawer = true;
      } else if (command == "unblockList") {
        if (this.$refs.columnTable.getSelectionKeys().length < 1) {
          this.$message.warning("请勾选解封资产");
          return;
        }
        this.unblockDrawer = true;
        this.unblockForm.plugDescribe = "";
      } // else if (command == "exportData") {
      //   this.exportData();
      // }
    },

    unBlock(row) {
      this.unblockOneDrawer = true;
    },
    moveToWhiteList(row) {},
    doDetail(row) {
      this.pageNum = 0;
      this.detailDrawer = true;
      this.ipAddress = row.ipAddress;
      this.getPlugDetail(1);
    },
    getPlugDetail(status) {
      this.reqStatus++;
      if (this.reqStatus != 1) {
        return;
      }
      let params = {};
      if (status == 1) {
        this.pageNum++;
        params = {
          isPlug: this.activeName == "plugging" ? "1" : "0",
          ipAddress: this.ipAddress,
          pageNum: this.pageNum
        };
      } else {
        this.pageNum = 1;
        params = {
          isPlug: this.activeName == "plugging" ? "1" : "0",
          ipAddress: this.ipAddress,
          pageNum: this.pageNum
        };
      }
      apiList
        .plugDetail(params)
        .then(res => {
          if (this.pageNum == 1) {
            this.plugDetail = res.data;
          } else {
            this.plugDetail.list.push(...res.data.list);
          }
          this.total = res.data.total;
          this.reqStatus = 0;
          if (this.pageNum > 1) {
            this.detailLessStatus = true;
          } else {
            this.detailLessStatus = false;
          }
          if ((this.plugDetail?.list?.length || 0) < this.total) {
            this.detailMoreStatus = true;
          } else {
            this.detailMoreStatus = false;
          }
        })
        .catch(err => {
          this.plugDetail = {
            list: [],
            deptName: "",
            ipAddress: "",
            plugStatus: ""
          };
          console.log(err);
          this.reqStatus = 0;
          this.detailMoreStatus = false;
          this.detailLessStatus = false;
        });
    },
    searchDetail() {
      this.searchResult = "";

      apiList
        .searchBlacklist({ ipAddress: this.ipAddress })
        .then(res => {
          if (res.data && Object.keys(res.data).length > 0) {
            this.searchResult = res.data;
          }
          console.info("this.searchResult");
          console.info(this.searchResult);
        })
        .catch(error => {
          console.error(error);
        });
    },
    blackList() {
      this.searchResult = "";
      this.ipAddress = "";
      this.blackListrawer = true;
    },
    whiteList() {
      this.whiteListrawer = true;
      this.$nextTick(() => {
        this.$refs.whiteTable.loadPage(1);
      });
    },
    addWhiteList() {
      this.dialogwhiteListVisible = true;
      this.whiteFormTitle = "添加白名单";
      this.$nextTick(() => {
        this.whiteForm = {
          ipMaskAddress: "",
          involvedModule: "",
          time: "",
          description: "",
          isEnable: 1
        };
        this.$refs.whiteForm.resetFields();
      });
    },
    addPlug() {
      this.dialogplugVisible = true;
      this.plugFormTitle = "人工封禁";
      this.$nextTick(() => {
        (this.plugForm = {
          ipAddress: "",
          plugLabel: "",
          plugDescribe: "",
          firewall: "",
          expireTime: ""
        }),
          (this.selectedFirewalls = []);
        this.$refs.plugForm.resetFields();
      });
    },
    editWhiteList(row) {
      this.dialogwhiteListVisible = true;
      this.whiteFormTitle = "修改白名单";

      let obj = {};
      Object.assign(obj, row);

      this.whiteForm = obj;
      this.whiteForm.time = [row.effectStartTime, row.effectEndTime];
    },
    deleteWhite() {},
    unsealUploadChange(file) {
      if (file) {
        let param = new FormData();
        param.append("file", file.raw);
        axios({
          url: apiList.BathPath + "/ipplan/plug/importUnPlugData",
          method: "post",
          headers: {
            "Content-Type": "multipart/form-data"
          },
          data: param
        })
          .then(res => {
            console.log(res.status);
            if (res.status == "0") {
              this.$message.success("导入成功");
              this.uploadTextSuccess();
            } else {
              this.$message.error(res.msg);
              this.uploadTextError();
            }
          })
          .catch(err => {
            console.log("err", err);
            // this.$message.error(err.msg);
            this.uploadTextError();
          });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "./assets/css/table";
.intervalCol {
  border: 1px solid #0b85af;
  padding: 0px 10px;
}
.colCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

.colAround {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.cardHeight {
  height: $cardHeight;
}

.commonChart {
  height: 300px;

  .subTitle {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 2px;
  }

  .valueStyle {
    font-size: 18px;
    margin: 10px 10px;
  }
}

.demo-drawer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.demo-drawer__content {
  padding: 0px 10px;
}
::v-deep .el-timeline-item__timestamp.is-top {
  width: 150px;
  text-align: right;
  margin-left: -200px;
}
::v-deep .el-timeline-item {
  margin-left: 120px;
}
.titleColor {
  color: #409eff;
}
.green {
  background: linear-gradient(90deg, #72e6d4 0%, #55c8ab 100%);
}

.blue {
  background: linear-gradient(90deg, #79bfff 0%, #1890ff 100%);
}
.titleSize {
  font-size: $titleSize;
  color: $titleColor;
  font-weight: $titleFontWeight;
  padding: 0 5px;
}

.titleIcon {
  font-size: $titleIcon;
  vertical-align: bottom;
}

::v-deep .el-upload {
  width: 100%;

  .el-upload-dragger {
    width: 100% !important;
    height: 250px;
  }

  .upload-desc {
    height: 18px;
    font-size: 14px;
    font-family:
      Source Han Sans CN-Regular,
      Source Han Sans CN;
    font-weight: 400;
    color: #6e7493;
    line-height: 18px;
    margin-top: 20px;
  }
}
</style>
