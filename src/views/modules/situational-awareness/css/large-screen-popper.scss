.large-screen-popper {
  border: unset !important;
  background: #1b2a47;

}
.large-screen-popper .el-select-dropdown__list {
  background-color: #1b2a47 !important;
}
//.large-screen-popper .el-picker-panel {
//  background: #1b2a47 !important;
//  color: #fff !important;
//}
//
//.large-screen-popper .el-date-table td.in-range .el-date-table-cell {
//  background-color: unset !important;
//}
.large-screen-popper .el-picker-panel__icon-btn {
  color: #fff !important;
}

.large-screen-popper .el-select-dropdown__item {
  background: #1b2a47 !important;
  color: #fff;

  &.is-selected {
    color: #409eff !important;
  }
}
.large-screen-popper .el-popper__arrow:before {
  background: #1b2a47 !important;
  border: 1px solid #1b2a47 !important;
}
.dark-select {
  position: relative;
  input {
    //display: none !important;
    color: #fff !important;
  }
  .el-select__wrapper {
    line-height: 32px;
    height: 32px;
    border-radius: 2px;
    box-shadow: unset !important;
    background-color: rgba(0, 149, 255, 0.1) !important;
    padding: 2px 12px !important;
  }
  .el-select__placeholder {
    color: #fff;
  }
  :deep(.el-input__wrapper) {
    background: unset !important;
    box-shadow: unset !important;
    justify-content: flex-end !important;
  }
}
