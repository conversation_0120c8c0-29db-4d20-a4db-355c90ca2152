<template>
  <div class="cube-rotate">
    <div class="cube">
      <div class="face front">
        <div></div>
      </div>
      <div class="face back">
        <div></div>
      </div>
      <div class="face right">
        <div></div>
      </div>
      <div class="face left">
        <div></div>
      </div>
      <div class="face top">
        <div></div>
      </div>
      <div class="face bottom">
        <div></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup></script>
<style lang="scss" scoped>
.cube {
  width: 80px;
  height: 80px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotateCube 10s infinite linear;
  transform: scale(0.8);
}

.face {
  position: absolute;
  //transform-origin: left top;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  backface-visibility: hidden; /* 隐藏翻转时的背面 */
  box-shadow:
    //0 0 10px 1px rgba(255, 0, 0, 0.7), /* 红色阴影 */
    //0 0 20px 2px rgba(255, 165, 0, 0.7), /* 橙色阴影 */
    //0 0 30px 3px rgba(255, 255, 0, 0.7); /* 黄色阴影 */
    0 0 2px 2px rgba(230, 243, 182, 0.7),
    0 0 4px 2px rgba(16, 159, 245, 0.7),
    0 0 6px 2px rgba(173, 194, 246, 0.7),
    0 0 8px 2px rgba(173, 194, 246, 0.7); /* 黄色阴影 */
  background: rgba(255, 255, 255, 0.8);
  div {
    width: calc(100% - 4px);
    height: calc(100% - 4px);
  }
}

.front {
  transform: translateZ(40px);
  div {
    background: linear-gradient(
      to bottom,
      rgba(0, 145, 255, 0.9),
      rgba(127, 226, 255, 0.35)
    );
  }
}
.back {
  transform: rotateY(180deg) translateZ(40px);
  div {
    background: linear-gradient(
      to left,
      rgba(0, 145, 255, 0.9),
      rgba(127, 226, 255, 0.35)
    );
  }
}
.right {
  transform: rotateY(90deg) translateZ(40px);
  div {
    background: linear-gradient(
      to right,
      rgba(0, 145, 255, 0.9),
      rgba(127, 226, 255, 0.35)
    );
  }
}
.left {
  transform: rotateY(-90deg) translateZ(40px);
  background: linear-gradient(
    to left,
    rgba(0, 145, 255, 0.9),
    rgba(127, 226, 255, 0.35)
  );
}
.top {
  transform: rotateX(90deg) translateZ(40px);
  div {
    background: linear-gradient(
      to top,
      rgba(0, 145, 255, 0.9),
      rgba(127, 226, 255, 0.35)
    );
  }
}
.bottom {
  transform: rotateX(-90deg) translateZ(40px);
  div {
    background: linear-gradient(
      to bottom,
      rgba(0, 145, 255, 0.9),
      rgba(127, 226, 255, 0.35)
    );
  }
}

@keyframes rotateCube {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: rotateX(180deg) rotateY(180deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}
</style>
