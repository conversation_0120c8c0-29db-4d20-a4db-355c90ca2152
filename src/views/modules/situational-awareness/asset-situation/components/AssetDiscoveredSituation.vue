<template>
  <div class="asset-discovered-situation">
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content" :style="style">
      <ChartComponent :option="state.option" resizeable></ChartComponent>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 近7天发现资产情况
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  onMounted,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import * as echarts from "echarts";
import {
  queryOnlineChart,
  queryUnknowChart
} from "@/views/modules/situational-awareness/asset-situation/api";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const setMockData = () => {
  state.option = buildOption(
    [
      "2024-01",
      "2024-02",
      "2024-03",
      "2024-04",
      "2024-05",
      "2024-06",
      "2024-07"
    ],
    [
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000))
    ]
  );
};

const baseFontStyle = {
  fontSize: "12px",
  fontWeight: 300,
  color: "#D1E4FF"
};

const buildOption = (xData, ydata): any => {
  return <EChartsOption>{
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      axisLabel: {
        ...baseFontStyle
        // interval: 0
      },
      data: xData || []
    },
    yAxis: {
      axisLine: {
        show: true
      },
      axisLabel: {
        ...baseFontStyle,
        formatter: function (value) {
          return value.toString().replace(",", "");
        }
      },
      splitLine: {
        lineStyle: {
          color: "#0095FF",
          opacity: 0.16
        }
      }
    },
    series: [
      {
        type: "line",
        areaStyle: {},
        smooth: true,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "#00AAFF"
          },
          {
            offset: 1,
            color: "rgba(0,41,255,0.05)"
          }
        ]),
        data: ydata || []
      }
    ],
    grid: {
      left: 40,
      top: 40,
      bottom: 25,
      right: 20
    }
  };
};

const state = reactive({
  option: buildOption([], [])
});
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryOnlineChart(queryParams.value).then(res => {
    let data = res.data || {};
    let { xList, yList } = data || {};
    state.option = buildOption(xList, yList);
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

const handleDrill = () => {
  $router.push({
    name: "est_eam_instance"
  });
};
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-discovered-situation {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
  }
}
</style>
