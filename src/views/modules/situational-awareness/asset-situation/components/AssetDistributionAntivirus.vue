<template>
  <div class="asset-distribution-antivirus">
    <!-- 下钻最好定位布局，不会影响内容区的高度 -->
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content flex flex-col" :style="style">
      <div class="flex justify-between">
        <span class="col-label">组织名称</span>
        <span class="col-label">部署数量</span>
      </div>
      <AssetBarChart
        class="asset-bar-chart"
        :items="state.items"
        :bar-wrap-height="14"
        :bar-height="8"
        item-justify="unset"
      >
        <template #item="{ item }">
          <div class="item flex justify-between">
            <span class="item-label">{{ item["label"] }}</span>
            <div class="flex justify-end items-center">
              <span class="item-value">{{ item["value"] }}</span>
              <span class="desc">
                <span class="desc-label">{{ item["upLabel"] }}：</span>
                <span
                  class="desc-value"
                  :class="{ up: item['up'] > 0, down: item['up'] < 0 }"
                  >{{ item["up"] }}</span
                >
              </span>
              <img v-if="item['up'] > 0" :src="getImage('redUp')" />
              <img v-else-if="item['up'] < 0" :src="getImage('greenDown')" />
            </div>
          </div>
        </template>
      </AssetBarChart>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 资产管理情况
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { getImage } from "@/views/modules/situational-awareness/asset-situation/assets/image";
import AssetBarChart from "@/views/modules/situational-awareness/components/charts/AssetBarChart.vue";
import { TitleDrill } from "@/views/modules/ddls";
import { queryFhComp } from "@/views/modules/situational-awareness/asset-situation/api";

const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const state = reactive({
  items: []
});

const setMockData = () => {
  state.items = Array.from(
    {
      length: 112
    },
    () => {
      return {
        label: "研发部门",
        percent: parseInt((Math.random() * 10000) % 100),
        value: 3455,
        up: 12
      };
    }
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  let zoneName = queryParams.value["zoneName"];
  let postData = {
    ...queryParams.value,
    type: zoneName == "" ? "zoneName" : "category"
  };
  queryFhComp(postData).then(res => {
    let { data = [] } = res.data || {};
    state.items = data;
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

const handleDrill = () => {
  $router.push({
    name: "est_eam_instance"
  });
};
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-distribution-antivirus {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 638px;
  .content {
    height: 100%;
  }
  .asset-bar-chart {
    margin-top: 20px;
  }
  .col-label {
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    color: #b1c7d6;
    line-height: 14px;
  }
  .item {
    margin-top: 20px;
    margin-bottom: 8px;
    .item-label {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #d1e4ff;
      line-height: 14px;
    }
    .item-value {
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 16px;
      margin-right: 10px;
    }
    .desc {
      .desc-label {
        color: #d1e4ff;
      }
      .desc-value {
        color: #d1e4ff;
      }
      .up {
        color: #ff3838;
      }
      .down {
        color: #66e1df;
      }
    }
  }
}
</style>
