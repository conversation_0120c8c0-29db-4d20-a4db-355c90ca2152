<template>
  <div class="asset-trend-antivirus">
    <div class="content" :style="style">
      <ChartComponent :option="state.option" resizeable></ChartComponent>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 防病毒趋势
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { queryFhChart } from "@/views/modules/situational-awareness/asset-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const setMockData = () => {
  state.option = buildOption(
    [
      "2024-01",
      "2024-02",
      "2024-03",
      "2024-04",
      "2024-05",
      "2024-06",
      "2024-07"
    ],
    [
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000))
    ]
  );
};

const baseFontStyle = {
  fontSize: "12px",
  fontWeight: 300,
  color: "#D1E4FF"
};

const buildOption = (xData, ydata): any => {
  return <EChartsOption>{
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      axisLabel: {
        ...baseFontStyle
      },
      data: xData || []
    },
    yAxis: {
      axisLine: {
        show: true
      },
      axisLabel: {
        ...baseFontStyle,
        formatter: function (value) {
          return value.toString().replace(",", "");
        }
      },
      splitLine: {
        lineStyle: {
          color: "#0095FF",
          opacity: 0.16
        }
      }
    },
    series: [
      {
        type: "line",
        areaStyle: {},
        smooth: true,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "#00AAFF"
          },
          {
            offset: 1,
            color: "rgba(0,41,255,0.05)"
          }
        ]),
        data: ydata || []
      }
    ],
    grid: {
      left: 40,
      top: 40,
      bottom: 25,
      right: 20
    }
  };
};

const state = reactive({
  option: buildOption([], [])
});

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryFhChart(queryParams.value).then(res => {
    let data = res.data || {};
    let { xList, yList } = data || {};
    state.option = buildOption(xList, yList);
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-trend-antivirus {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
  }
}
</style>
