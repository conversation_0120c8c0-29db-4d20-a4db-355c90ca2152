<template>
  <div class="asset-trend-antivirus">
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content" :style="style">
      <div
        v-for="(item, index) in state.items"
        :key="index"
        class="block"
        :style="blockBgStyle"
      >
        <img
          :src="item.img"
          style="width: 72px; height: 72px; margin-bottom: 12px"
        />
        <span class="label label-16">{{ item.label }}</span>
        <span class="label label-14">安装数量 / 安装率</span>
        <div class="label label-16">
          <span>{{ item.installCount }}</span>
          <span> / </span>
          <span>{{ item.installRate }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 防病毒趋势
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/asset-situation/assets/image";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { queryFhComp } from "@/views/modules/situational-awareness/asset-situation/api";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const blockBgStyle = <CSSProperties>{
  backgroundImage: `url(${getImage("blockRect")})`,
  width: "192px",
  height: "209px"
};

const state = reactive({
  items: [
    {
      label: "服务器",
      img: getImage("server"),
      installCount: 0,
      installRate: 0
    },
    {
      label: "终端设备",
      img: getImage("terminal"),
      installCount: 0,
      installRate: 0
    }
  ]
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  Object.assign(state.items[0], {
    installCount: 549,
    installRate: 40
  });
  Object.assign(state.items[1], {
    installCount: 278,
    installRate: 27
  });
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  let zoneName = queryParams.value["zoneName"];
  let postData = {
    ...queryParams.value,
    type: zoneName == "" ? "zoneName" : "category"
  };
  queryFhComp(postData).then(res => {
    let { data = [] } = res.data || {};
    for (let i = 0; i < 2; ++i) {
      let item = data[i];
      if (item) {
        Object.assign(state.items[i], {
          installCount: item.value || 0,
          installRate: item.percent || 0
        });
      }
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

const handleDrill = () => {
  $router.push({
    name: "est_eam_instance"
  });
};
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-trend-antivirus {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    justify-content: space-around;
    .block {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .label {
        font-family: MiSans, MiSans;
        font-weight: 500;
        color: #ffffff;
      }
      .label-16 {
        line-height: 16px;
        font-size: 16px;
      }
      .label-14 {
        line-height: 14px;
        font-size: 14px;
        margin: 16px 0 11px 0;
      }
    }
  }
}
</style>
