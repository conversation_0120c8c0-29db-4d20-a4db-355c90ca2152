<template>
  <div class="asset-situation-center">
    <!--    <el-switch v-model="state.animateDisabled"></el-switch>-->
    <div class="overview flex items-center justify-around">
      <div
        v-for="(item, index) in state.overviewData"
        class="flex items-center overview-item"
        :key="index"
      >
        <img :src="item.img" style="width: 72px; height: 54px" />
        <div class="flex flex-col" style="margin-left: 16px">
          <span class="label">{{ item.label }}</span>
          <span class="count" @click="handleDrillItem(item)"
            ><DigitalAnimate
              :value="item.value || 0"
              :digit-value-style-fn="digitFn"
              :digit-style="{
                fontFamily: 'YouSheBiaoTiHei'
              }"
          /></span>
          <span class="desc flex items-center">
            <span class="desc-label">{{ item.upLabel }}：</span>
            <span
              class="desc-value"
              :class="{ up: item['up'] > 0, down: item['up'] < 0 }"
              >{{ Math.abs(item["up"] || 0) }}</span
            >
            <img
              v-if="item['up'] > 0"
              :src="getImage('redUp')"
              style="width: 7px; height: 10px"
            />
            <img
              v-else-if="item['up'] < 0"
              :src="getImage('greenDown')"
              style="width: 7px; height: 10px"
            />
          </span>
        </div>
      </div>
    </div>
    <div
      class="main"
      @mouseenter="
        () => {
          state.animatePauseFlag = true;
        }
      "
      @mouseleave="
        () => {
          state.animatePauseFlag = false;
        }
      "
      :style="{
        opacity: state.opacity
      }"
    >
      <div
        class="absolute asset-items-wrap"
        style="width: 720px; height: 600px"
      >
        <div :key="state.itemsKey" class="w-full h-full relative">
          <!--          <svg-->
          <!--            class="absolute"-->
          <!--            width="720"-->
          <!--            height="600"-->
          <!--            xmlns="http://www.w3.org/2000/svg"-->
          <!--          >-->
          <!--            <path-->
          <!--              d="M360,0-->
          <!--             C540,0 720,150 720,300-->
          <!--             C720,450 540,600 360,600-->
          <!--             C180,600 0,450 0,300-->
          <!--             C0,150 180,0 360,0-->
          <!--             Z"-->
          <!--              fill="none"-->
          <!--              stroke="#fff"-->
          <!--            />-->
          <!--          </svg>-->
          <div
            class="absolute item"
            v-for="(item, index) in state.items"
            :key="index"
            :style="itemStyle(index, state.items.length)"
          >
            <div
              class="asset-wrap"
              :class="getAssetClass(item)"
              style="left: 0; top: 0; width: 100%; height: 100%"
              :style="{
                backgroundImage: `url(${getImage('item')})`,
                transform: `rotate(${-state.deg}deg)`
              }"
            >
              <div
                class="asset-data"
                :class="{ center: !isGroup }"
                @click="handleDrillItem2(item.assetData)"
              >
                <div v-if="isGroup" class="line">
                  <span class="label">发现资产数：</span>
                  <span class="value">{{ item.assetData.discoverCount }}</span>
                </div>
                <div class="line" style="margin: 6px 0">
                  <span class="label">登记资产数：</span>
                  <span class="value">{{
                    item.assetData.registeredCount
                  }}</span>
                </div>
                <div v-if="isGroup || item.assetData.rate != '0'" class="line">
                  <span class="label">防护资产率：</span>
                  <span class="value rate">{{
                    item.assetData.rate != "-1"
                      ? item.assetData.rate + "%"
                      : "-"
                  }}</span>
                </div>
              </div>
              <img
                class="red-img absolute"
                :src="getImage('redLeft')"
                style="top: 108px"
              />
              <img
                class="red-img absolute"
                :src="getImage('redRight')"
                style="right: -4px; top: 108px"
              />
              <img
                class="orange-img absolute"
                :src="getImage('redLeft')"
                style="top: 108px"
              />
              <img
                class="orange-img absolute"
                :src="getImage('redRight')"
                style="right: -4px; top: 108px"
              />
              <span class="absolute asset-label">{{
                item.assetData.label
              }}</span>
            </div>
            <!--            <img-->
            <!--              v-if="item.connect"-->
            <!--              class="absolute"-->
            <!--              :src="item.connect"-->
            <!--              :style="item.connectStyle"-->
            <!--            />-->
          </div>
        </div>
      </div>
      <!--      <div-->
      <!--        class="absolute"-->
      <!--        style="left: 20px; top: 0; bottom: 40px; right: 20px"-->
      <!--        :style="mainRotateStyle"-->
      <!--      ></div>-->

      <CubeRotate class="cube-rotate"></CubeRotate>
      <img
        class="plinth"
        :src="getImage('plinth')"
        style="width: 396px; height: 177px"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { getImage } from "@/views/modules/situational-awareness/asset-situation/assets/image";
import CubeRotate from "@/views/modules/situational-awareness/asset-situation/effects/CubeRotate.vue";
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  queryAssetCountByType,
  queryOnlineZqCount
} from "@/views/modules/situational-awareness/asset-situation/api";
import { DigitalAnimate } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const isGroup = computed(() => {
  return queryParams.value.zoneName == "";
});
const digitFn = (val): CSSProperties => {
  //  处理字体YouSheBiaoTiHei数字1宽度问题
  if (val == "1") {
    return {
      width: "0.5em"
    };
  }
  return {};
};

// 最大数量10
const COUNT = 10;

const createPlaceholderItems = (length: number) => {
  return Array.from(
    {
      length
    },
    (_, index) => {
      return {
        key: index,
        assetData: {}
      };
    }
  );
};

const state = reactive({
  overviewData: [
    {
      label: "发现资产数",
      value: 0,
      up: 0,
      upLabel: "较上周",
      drillable: false,
      drillRoute: "eam_assetMapping_homePage",
      routeQuery: {},
      img: getImage("regAsset")
    },
    {
      label: "登记资产数",
      value: 0,
      up: 0,
      upLabel: "较上周",
      drillable: true,
      drillRoute: "est_eam_instance",
      routeQuery: {},
      img: getImage("regAsset")
    },
    {
      label: "防护资产数",
      value: 0,
      up: 0,
      upLabel: "较上周",
      drillable: true,
      drillRoute: "est_eam_instance",
      routeQuery: {
        label: "已安装防病毒设备"
      },
      img: getImage("protectAsset")
    }
  ],
  itemsKey: 1,
  // 固定13个组件占位
  items: <any[]>createPlaceholderItems(COUNT),
  // 所有的资产数据列表
  assetDatas: [],
  // 记录索引位置
  offset: 0,
  // 旋转度数
  deg: 0,
  animatePauseFlag: false,
  animateDisabled: true,

  requestId: -1,
  interval: -1,
  opacity: 1
});

const setMockData = () => {
  let mockData = [
    { value: 3756, up: 485 },
    { value: 3547, up: 18 },
    { value: 3500, up: 25 }
  ];
  for (let i = 0; i < 3; ++i) {
    Object.assign(state.overviewData[i], mockData[i]);
  }
  state.assetDatas = Array.from(
    {
      length: COUNT
    },
    () => {
      return {
        label: "研究院",
        discoverCount: 189,
        registeredCount: 189,
        rate: "90.18"
      };
    }
  );
  updateItemAssetDatas();
};

const labels = ["发现资产数", "登记资产数", "防护资产数"];
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  // 概况数字(发现资产数/登记资产数/防护资产数)
  queryOnlineZqCount(queryParams.value).then(res => {
    const { data = [] } = res.data || {};
    for (let i = 0; i < 3; ++i) {
      let {
        label = labels[i],
        value = 0,
        up = 0,
        upLabel = "较上周"
      } = data[i] || {};
      Object.assign(state.overviewData[i], {
        label,
        value,
        up,
        upLabel
      });
    }
  });

  let zoneName = queryParams.value["zoneName"];
  let postData = {
    ...queryParams.value,
    type: zoneName == "" ? "zoneName" : "category"
  };

  // 轮盘数据查询
  queryAssetCountByType(postData).then(res => {
    const { data = [] } = res.data || {};
    state.assetDatas = data || [];
    // 更新占位列表
    state.items = createPlaceholderItems(
      Math.min(state.assetDatas.length, COUNT)
    );
    // 重置dom（重置动画）
    updateItemAssetDatas();
    if (state.assetDatas.length == state.items.length) {
      clearInterval(state.interval);
    } else {
      startTimer();
    }
  });

  // 初始化
  updateItemAssetDatas();
};

const itemStyle = (index, total): CSSProperties => {
  const rate = (index * 100) / total;
  return {
    "--start-position": `${rate}%`
  };
};

// 每圈动画完成后使用先进先出更新轮盘数据
const updateItemAssetDatas = () => {
  // offset
  let { items, assetDatas } = state;
  if (!assetDatas) {
    assetDatas = [];
  }
  let offset = state.offset,
    length = assetDatas.length;

  let itemCount = items.length;
  // let hasMore = length > itemCount;
  // if (!hasMore) {
  //   offset = 0;
  // }
  for (let i = 0; i < itemCount; ++i) {
    let item = items[i];
    item.assetData = /*hasMore
      ? assetDatas[offset % length]
      : */ assetDatas[offset] || {
      empty: true,
      label: "",
      discoverCount: "-",
      registeredCount: "-",
      rate: "-1"
    };
    ++offset;
  }
};

// const mainRotateStyle = computed((): CSSProperties => {
//   return {
//     transformOrigin: `center center`,
//     transform: `rotate(${state.deg}deg)`
//   };
// });

const getAssetClass = item => {
  let clsArr = [];
  let { rate } = item?.assetData || {};
  try {
    if (typeof rate == "string") {
      rate = parseFloat(rate);
    }
  } catch (e) {}
  if (rate != -1) {
    if (rate < 60) {
      return ["red"];
    } else if (rate < 80) {
      return ["orange"];
    } else {
      return ["blue"];
    }
  }
  return clsArr;
};

// const stopAnimate = () => {
//   if (state.requestId != -1) {
//     cancelAnimationFrame(state.requestId);
//     state.requestId = -1;
//   }
// };

// const beginAnimate = () => {
//   if (state.animateDisabled || state.animatePauseFlag) {
//     state.requestId = requestAnimationFrame(beginAnimate);
//     return;
//   }
//   state.deg += 1 / 30;
//   if (state.deg >= 360) {
//     state.deg = 0;
//     if (state.assetDatas?.length > 9) {
//       ++state.offset;
//       updateItemAssetDatas();
//     }
//   }
//   state.requestId = requestAnimationFrame(beginAnimate);
// };

const handleDrillItem = item => {
  let { drillable, routeQuery } = item;
  if (drillable) {
    $router.push({
      name: item.drillRoute,
      query: {
        zoneName: queryParams.value.zoneName,
        ...(routeQuery || {})
      }
    });
  }
};

const handleDrillItem2 = assetData => {
  console.log("handleDrillItem2 assetData", assetData);
  let { label, zoneName } = assetData || {};
  if (label) {
    $router.push({
      name: "est_eam_instance",
      query: {
        zoneName
      }
    });
  }
};

const startTimer = () => {
  clearInterval(state.interval);
  state.interval = setInterval(() => {
    state.offset += COUNT;
    if (state.offset >= state.assetDatas.length) {
      state.offset = 0;
    }
    let nextCount = Math.min(state.assetDatas.length - state.offset, COUNT);
    state.items = createPlaceholderItems(nextCount);
    updateItemAssetDatas();
  }, 15000) as any;
};

onMounted(() => {
  // // 开启动画
  // requestAnimationFrame(beginAnimate);
  startTimer();
});

onBeforeUnmount(() => {
  // stopAnimate();
  clearInterval(state.interval);
});

watch(
  () => state.items.length,
  () => {
    ++state.itemsKey;
  }
);

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.asset-situation-center {
  width: 956px;
  height: 968px;
  .overview {
    padding: 0 20px;
    height: 175px;
    .overview-item {
      width: 240px;
    }
    .label {
      font-family: MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #b1c7d6;
      line-height: 14px;
      white-space: nowrap;
    }
    .count {
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 24px;
      color: #fcfbff;
      line-height: 24px;
      text-shadow: 0px 2px 6px #006fff;
      margin: 9px 0 4px 0;
    }
    .desc {
      display: flex;
      .desc-label {
        color: #d1e4ff;
      }
      .desc-value {
        color: #d1e4ff;
      }
      .up {
        color: #ff3838;
      }
      .down {
        color: #66e1df;
      }
    }
  }
  .main {
    height: 793px;
    position: relative;
    transition: all 1.5s;
    .item {
      transform: scale(0.95);
    }
    &:hover {
      .item {
        animation-play-state: paused;
      }
    }
    .asset-items-wrap {
      top: 60px;
      left: 50%;
      transform: translateX(-50%);
    }
    .plinth {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
    .cube-rotate {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, calc(-90% - 75px)) scale(0.8);
      z-index: 200;
    }
    .item {
      width: 150px;
      height: 169.13px;
      position: absolute;
      offset-path: path(
        "M360,0 C540,0 720,150 720,300 C720,450 540,600 360,600 C180,600 0,450 0,300 C0,150 180,0 360,0Z"
      );
      offset-rotate: 0deg;
      offset-distance: var(--start-position);
      animation: moveAlongPath 60s linear infinite;
    }
  }
  .asset-label {
    left: 0;
    top: 100%;
    width: 100%;
    font-family: MiSans, MiSans;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    line-height: 14px;
    text-align: center;
  }
  .asset-wrap {
    .red-img {
      display: none;
    }
    .orange-img {
      display: none;
    }
    &.red {
      .rate {
        color: #ff3838 !important;
      }
      .red-img {
        display: block;
      }
    }
    &.orange {
      .rate {
        color: #ff7716 !important;
      }
    }
    &.blue {
      .rate {
        color: #00eaff !important;
      }
    }
    .asset-data {
      margin: 0 15px;
      top: 0;
      height: 60px;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      margin-left: 20px;
      &.center {
        align-items: center;
      }
      .line {
        height: 12px;
        line-height: 12px;
        .label {
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 12px;
          color: #d1e4ff;
          line-height: 12px;
        }
        .value {
          font-family: DIN, DIN;
          font-weight: 500;
          font-size: 12px;
          color: #ffffff;
          line-height: 12px;
        }
      }
    }
  }
  @keyframes moveAlongPath {
    0% {
      offset-distance: var(--start-position);
    }
    100% {
      offset-distance: calc(var(--start-position) + 100%);
    }
  }
}
</style>
