<template>
  <div class="asset-type-distr">
    <div class="title absolute"></div>
    <div class="content" :style="style">
      <div class="item relative">
        <img
          class="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
          :src="getImage('scanRadar')"
          style="width: 171px; height: 171px"
        />
        <span
          class="total absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
        >
          <DigitalAnimate
            :value="state.total || 0"
            :digitValueStyleFn="
              () => {
                return {
                  width: '0.6em',
                  fontSize: '18px'
                };
              }
            "
          />
        </span>
      </div>
      <div class="item flex-c">
        <el-row>
          <el-col
            v-for="(item, index) in state.items"
            class="flex flex-col justify-center h-[80px]"
            :key="index"
            :span="item.span"
          >
            <div class="flex items-center justify-start">
              <img :src="item.img" style="width: 22px; height: 18px" />
              <span class="label">{{ item.label }}</span>
            </div>
            <div class="count" @click="handleDrillItem(item)">
              {{ item.count }}
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 资产管理情况
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  onMounted,
  reactive,
  watch
} from "vue";
import { getImage } from "@/views/modules/situational-awareness/asset-situation/assets/image";
import { queryAssetAuditCount } from "@/views/modules/situational-awareness/asset-situation/api";
import { DigitalAnimate } from "@/views/modules/ddls";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const decoration = getImage("decoration");
const state = reactive({
  total: 0,
  items: [
    {
      label: "僵尸资产",
      count: 0,
      span: 12,
      drillRoute: "test_eam_newZombieAssets",
      img: decoration
    },
    {
      label: "重复资产",
      count: 0,
      span: 12,
      drillRoute: "auditAsset_audit_repetitiveness",
      img: decoration
    },
    {
      label: "关键属性缺失资产",
      count: 0,
      span: 24,
      drillRoute: "auditAsset_test_auditAsset_Integrity",
      img: decoration
    }
  ]
});

const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});
const query = () => {
  if (props.selectionMode) {
    return;
  }
  queryAssetAuditCount(queryParams.value).then(res => {
    let data = res.data || {};
    let total = 0;
    for (let item of state.items) {
      item.count = data[item.label] || 0;
      total += item.count;
    }
    state.total = total;
  });
};

const handleDrillItem = item => {
  console.log("drill call");
  $router.push({
    name: item.drillRoute
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-type-distr {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
      width: 50%;
      height: 100%;
      .total {
        font-family: OPPOSans, OPPOSans;
        font-weight: 800;
        font-size: 24px;
        color: #ffffff;
        line-height: 24px;
        text-shadow: 0px 6px 0px rgba(0, 0, 0, 0.15);
      }
      .label {
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
        line-height: 14px;
        margin-left: 5px;
        white-space: nowrap;
      }
      .count {
        font-family: DIN, DIN;
        font-weight: bold;
        font-size: 18px;
        color: #ffffff;
        line-height: 18px;
        margin-left: 26px;
        margin-top: 11px;
      }
    }
  }
}
</style>
