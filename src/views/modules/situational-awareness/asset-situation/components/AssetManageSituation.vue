<template>
  <div class="asset-manage-situation">
    <!-- 下钻最好定位布局，不会影响内容区的高度 -->
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content" :style="style">
      <el-row>
        <el-col
          :span="12"
          class="flex-c h-[120px]"
          v-for="(item, index) in state.items"
          :key="index"
        >
          <div class="flex-c" style="width: 68px; height: 68px">
            <img :src="item.img" />
          </div>
          <div style="margin-left: 15px; width: 120px">
            <div class="label">{{ item.showLabel }}</div>
            <div class="count" @click="handleDrillItem(item)">
              {{ item.count || 0 }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 资产管理情况
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { getImage } from "@/views/modules/situational-awareness/asset-situation/assets/image";
import { TitleDrill } from "@/views/modules/ddls";
import { queryAssetOnlineCloudCount } from "@/views/modules/situational-awareness/asset-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

// 自适应处理：
//    确保组件高度和UI设计的高度一致（包含标题）,如果组件外配置了标题，使用paddingTop，titleHeight是标题的高度
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const state = reactive({
  items: [
    {
      label: "在线资产数",
      showLabel: "在线资产数",
      routeLabelAlias: "在线设备",
      count: 0,
      img: getImage("onlineAsset")
    },
    {
      label: "离线资产数",
      showLabel: "离线资产数",
      routeLabelAlias: "离线设备",
      count: 0,
      img: getImage("offlineAsset")
    },
    {
      label: "本地云资产数",
      showLabel: "本地资产",
      routeLabelAlias: "本地云",
      count: 0,
      img: getImage("localCloud")
    },
    {
      label: "国资云资产数",
      showLabel: "云上资产",
      routeLabelAlias: "国资云",
      count: 0,
      img: getImage("gzCloud")
    }
  ]
});

const setMockData = () => {
  for (let item of state.items) {
    item.count = Math.abs(parseInt(Math.random() * 1000));
  }
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryAssetOnlineCloudCount(queryParams.value).then(res => {
    let data = res.data || {};
    for (let item of state.items) {
      item.count = data[item.label] || 0;
    }
  });
};

const handleDrill = () => {
  $router.push({
    name: "est_eam_instance"
  });
};

const handleDrillItem = item => {
  $router.push({
    name: "est_eam_instance",
    query: {
      label: item.routeLabelAlias,
      zoneName: queryParams.value["zoneName"]
    }
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-manage-situation {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .item {
      height: 120px;
    }
  }
  .label {
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 16px;
    color: #d1e4ff;
    line-height: 16px;
    white-space: nowrap;
  }
  .count {
    font-family: DIN, DIN;
    font-weight: bold;
    font-size: 24px;
    color: #ffffff;
    margin-top: 10px;
  }
}
</style>
