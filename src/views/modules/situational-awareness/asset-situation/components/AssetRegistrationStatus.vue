<template>
  <div class="asset-registration-status">
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content flex flex-col" :style="style">
      <div class="flex justify-between">
        <span class="label">已登记资产</span>
        <span class="label">未登记资产</span>
      </div>
      <div class="percent-bar">
        <div class="registered" :style="registeredBarStyle"></div>
        <div class="unregistered" :style="unregisteredBarStyle"></div>
      </div>
      <div class="flex justify-between">
        <div class="registered">
          <span class="count"
            >{{ state.registered }} ({{ registeredPercent }}%)</span
          >
        </div>
        <div class="unregistered">
          <span class="count"
            >{{ state.unregistered }} ({{ 100 - registeredPercent }}%)</span
          >
        </div>
      </div>
      <div class="chart flex-1 mt-[20px]">
        <ChartComponent
          :option="state.option as any"
          resizeable
        ></ChartComponent>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 登记
import { baseComponentProps } from "@/views/modules/ddls/module";
import { TitleDrill } from "@/views/modules/ddls";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  onMounted,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import {
  queryAssetAuditCount,
  queryUnknowChart
} from "@/views/modules/situational-awareness/asset-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const registeredBarStyle = computed((): CSSProperties => {
  return {
    width: `${registeredPercent.value}%`
  };
});
const unregisteredBarStyle = computed((): CSSProperties => {
  return {
    width: `${100 - registeredPercent.value}%`
  };
});

const baseFontStyle = {
  fontSize: "12px",
  fontWeight: 300,
  color: "#D1E4FF"
};

const buildOption = (xData, ydata): any => {
  return <EChartsOption>{
    title: {
      text: "近六个月资产登记情况变化趋势",
      textStyle: baseFontStyle
    },
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      axisLabel: {
        ...baseFontStyle,
        interval: 0
      },
      data: xData || []
    },
    yAxis: {
      max: 4000,
      axisLine: {
        show: true
      },
      axisLabel: {
        ...baseFontStyle,
        formatter: function (value) {
          return value.toString().replace(",", "");
        }
      },
      splitLine: {
        lineStyle: {
          color: "#0095FF",
          opacity: 0.16
        }
      }
    },
    series: [
      {
        type: "bar",
        barWidth: "14px",
        label: {
          show: true, // 显示 label
          position: "top", // 在柱子顶部显示
          formatter: function (params) {
            return params.value; // 这里直接返回数据值，也可以进行格式化
          },
          textStyle: {
            ...baseFontStyle,
            fontFamily: "DIN, DIN",
            fontWeight: 550,
            fontSize: "12px",
            color: "#00AAFF"
          }
        },
        data: ydata || []
      }
    ],
    grid: {
      left: 40,
      top: 40,
      bottom: 25,
      right: 20
    }
  };
};

const state = reactive({
  registered: 0,
  unregistered: 0,
  option: buildOption([], [])
});

const registeredPercent = computed(() => {
  let total = state.registered + state.unregistered;
  if (total == 0) return 50;
  return Math.round((100 * state.registered) / total);
});

const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const setMockData = () => {
  state.unregistered = 45;
  state.registered = 130;
  state.option = buildOption(
    [
      "2024-01",
      "2024-02",
      "2024-03",
      "2024-04",
      "2024-05",
      "2024-06",
      "2024-07"
    ],
    [
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000))
    ]
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryUnknowChart(queryParams.value).then(res => {
    let data = res.data || {};
    let { registered, unregistered, chartMap } = data || {};
    state.unregistered = unregistered || 0;
    state.registered = registered || 0;
    let { xList, yList } = chartMap || {};
    state.option = buildOption(xList, yList);
  });
};

const handleDrill = () => {
  $router.push({
    name: "test_eam_unknownAssets"
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.asset-registration-status {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    .label {
      font-family: OPPOSans, OPPOSans;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 14px;
    }
    .percent-bar {
      height: 8px;
      display: flex;
      margin: 7px 0 10px 0;
      .registered {
        background: #00aaff;
      }
      .unregistered {
        background: #ff7734;
      }
    }
    .registered {
      color: #00aaff;
    }
    .unregistered {
      color: #ff7734;
    }
    .count {
      font-family: MiSans, MiSans;
      font-weight: 600;
      font-size: 14px;
      line-height: 14px;
    }
  }
}
</style>
