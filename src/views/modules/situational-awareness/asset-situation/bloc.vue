<template>
  <div class="asset-situation-bloc">
    <ls-view :ref-id="refId" :view-index="state.viewIndex">
      <template #tr="{ screenScales, pageScales }">
        <div class="asset-situation-bloc-tr">
          <span class="label">周期统计：</span>
          <div class="dark-select">
            <el-select
              placeholder="请选择"
              style="width: 120px"
              popper-class="large-screen-popper"
              persistent
              v-model="globalQuery.dateType"
            >
              <el-option
                v-for="(periodOption, index) in state.periodOptions"
                :key="index"
                v-bind="periodOption"
              ></el-option>
            </el-select>
          </div>
          <span class="label" style="margin-left: 20px">视图切换：</span>
          <div class="dark-select">
            <el-select
              v-model="globalQuery.zoneName"
              placeholder="全部"
              style="width: 140px"
              popper-class="large-screen-popper"
              persistent
              filterable
              @change="handleDeptChange"
            >
              <el-option label="全部" value="">
                <div style="font-weight: bold">全部</div>
              </el-option>
              <el-option
                v-for="(deptOption, index) in state.deptOptions"
                :key="index"
                v-bind="deptOption"
              ></el-option>
            </el-select>
          </div>
          <dark-clock style="margin-left: 20px"></dark-clock>
        </div>
      </template>
    </ls-view>
  </div>
</template>
<script lang="ts" setup>
// 资产态势集团 大屏设计关联的编号
import { onBeforeUnmount, onMounted, provide, reactive, ref } from "vue";
import DarkClock from "@/views/modules/situational-awareness/components/dark-clock/DarkClock.vue";
import { getAllDeptData } from "@/views/modules/situational-awareness/asset-situation/api";
const refId = "099ce1576dc00000";
defineOptions({
  name: "asset_situation_bloc",
  activated() {
    window.dispatchEvent(new Event("resize"));
  }
});
const state = reactive({
  // 0 代表集团视角； 1 代表子公司视角
  timer: -1,
  viewIndex: 0,
  deptOptions: [],
  periodOptions: [
    { label: "本周", value: "week" },
    { label: "本月", value: "month" },
    { label: "本季度", value: "quarter" },
    { label: "本年", value: "year" }
  ]
});
// 全局查询条件
const globalQuery = reactive({
  zoneName: "",
  dateType: "week",
  reloadFlag: 1
});

const handleDeptChange = val => {
  if (val == "") {
    state.viewIndex = 0;
  } else {
    state.viewIndex = 1;
  }
};

const queryDeptData = () => {
  getAllDeptData().then(res => {
    console.log(res);
    let deptData = res.data || [];
    state.deptOptions = deptData
      .filter(dept => dept.deptId != "" && dept.deptId != "0")
      .map(dept => {
        return {
          label: dept.deptName,
          value: dept.deptId
        };
      });

    // if (state.deptOptions.length > 0) {
    //   globalQuery.zoneName = state.deptOptions[0].value;
    // }
  });
};

const triggerReload = () => {
  ++globalQuery.reloadFlag;
};
const selectNextDept = () => {
  let deptOptions = state.deptOptions;
  if (globalQuery.zoneName == "") {
    globalQuery.zoneName = deptOptions[0]?.value || "";
  } else {
    let index = deptOptions.findIndex(
      deptOption => deptOption.value == globalQuery.zoneName
    );
    globalQuery.zoneName = deptOptions[index + 1]?.value || "";
  }
};

onMounted(() => {
  queryDeptData();
  // 5分钟刷新一次
  state.timer = setInterval(() => {
    // selectNextDept();
    triggerReload();
  }, 60000) as any;
});

// 停止定时器
onBeforeUnmount(() => {
  clearInterval(state.timer);
});

provide("globalQuery", globalQuery);
</script>
<style lang="scss">
@import "../css/large-screen-popper";
</style>
<style lang="scss" scoped>
.asset-situation-bloc {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  .asset-situation-bloc-tr {
    width: 560px;
    margin-top: 15px;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .label {
      width: 70px;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #d1e4ff;
      line-height: 14px;
    }
  }
}
</style>
