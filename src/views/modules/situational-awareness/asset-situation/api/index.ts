import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";
const portalServerPath = `${ServerNames.portalServer}`;

/**
 * 查询部门列表
 */
export const getAllDeptData = () => {
  return http.get<any, any>(
    `${portalServerPath}/framework/sysmanage/dept/getAllDeptData`
  );
};

/**
 * 资产管理情况
 * 注意 istance 不是 instance ??
 * @param params
 */
export const queryAssetOnlineCloudCount = params => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryAssetOnlineCloudCount`,
    { params: params || {} }
  );
};

/**
 * 资产类型分布(资产稽核情况)
 *
 * @param params
 */
export const queryAssetAuditCount = params => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryAssetAuditCount`,
    { params: params || {} }
  );
};

/**
 * 资产登记情况
 *
 * @param params
 */
export const queryUnknowChart = params => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryUnknowChart`,
    { params: params || {} }
  );
};

/**
 * 近7天发现资产情况
 *
 * @param params
 */
export const queryOnlineChart = params => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryOnlineChart`,
    { params: params || {} }
  );
};

/**
 * 防病毒部署趋势变化
 *
 * @param params
 */
export const queryFhChart = params => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryFhChart`,
    { params: params || {} }
  );
};

/**
 * 防病毒部署分布情况 (集团视角条状图和子公司视角2个指标公用)
 *
 * @param params
 */
export const queryFhComp = params => {
  return http.postJson<any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryFhComp`,
    params
  );
};

/**
 * 资产态势 - 大屏中顶部资产数
 *
 * @param params
 */
export const queryOnlineZqCount = params => {
  return http.postJson<any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryOnlineZqCount`,
    params
  );
};

/**
 * 资产态势 - 大屏中下轮盘数据
 *
 * @param params
 */
export const queryAssetCountByType = params => {
  return http.postJson<any>(
    `${ServerNames.eamCoreServer}/overview/instance/queryAssetCountByType`,
    params
  );
};
