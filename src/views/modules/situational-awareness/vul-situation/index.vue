<template>
  <div class="vul-situation">
    <ls-view :ref-id="refId" :view-index="0">
      <template #tr>
        <div style="margin-top: 15px; margin-right: 20px">
          <dark-clock></dark-clock>
        </div>
      </template>
    </ls-view>
  </div>
</template>
<script lang="ts" setup>
// 攻击态势 大屏设计关联的编号
import DarkClock from "@/views/modules/situational-awareness/components/dark-clock/DarkClock.vue";
import { onBeforeUnmount, onMounted, provide, reactive } from "vue";

const refId = "09ba875aff000000";
defineOptions({
  name: "vul_situation",
  activated() {
    window.dispatchEvent(new Event("resize"));
  }
});
const state = reactive({
  timer: -1
});
// 全局查询条件
const globalQuery = reactive({
  reloadFlag: 1
});

const triggerReload = () => {
  ++globalQuery.reloadFlag;
};
onMounted(() => {
  // 5分钟刷新一次
  state.timer = setInterval(() => {
    triggerReload();
  }, 300000) as any;
});

// 停止定时器
onBeforeUnmount(() => {
  clearInterval(state.timer);
});
provide("globalQuery", globalQuery);
</script>
<style lang="scss" scoped>
.vul-situation {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
