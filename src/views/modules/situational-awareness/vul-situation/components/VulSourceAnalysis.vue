<template>
  <div class="VulSourceAnalysis">
    <div class="content" :style="style">
      <ChartComponent :option="state.option" resizeable></ChartComponent>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 防病毒趋势
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import * as echarts from "echarts";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const setMockData = () => {
  state.option = buildOption(
    ["迪普漏扫", "长亭漏扫", "绿盟漏扫"],
    [
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000))
    ]
  );
};

const baseFontStyle = {
  fontSize: "12px",
  fontWeight: 300,
  color: "#D1E4FF"
};

const buildOption = (xData, ydata): any => {
  return <EChartsOption>{
    tooltip: {
      trigger: "axis"
    },
    xAxis: {
      axisLabel: {
        ...baseFontStyle,
        interval: 0
      },
      data: xData || []
    },
    yAxis: {
      max: 4000,
      axisLine: {
        show: true
      },
      axisLabel: {
        ...baseFontStyle,
        formatter: function (value) {
          return value.toString().replace(",", "");
        }
      },
      splitLine: {
        lineStyle: {
          color: "#0095FF",
          opacity: 0.16
        }
      }
    },
    series: [
      {
        type: "bar",
        barWidth: "14px",
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "#00AAFF"
          },
          {
            offset: 1,
            color: "rgba(0,41,255,0.05)"
          }
        ]),
        // color: "linear-gradient( 180deg, #0095FF 0%, rgba(0,149,255,0.2) 100%)",
        label: {
          show: true, // 显示 label
          position: "top", // 在柱子顶部显示
          formatter: function (params) {
            return params.value; // 这里直接返回数据值，也可以进行格式化
          },
          textStyle: {
            ...baseFontStyle,
            fontFamily: "DIN, DIN",
            fontWeight: 550,
            fontSize: "12px",
            color: "#00AAFF"
          }
        },
        data: ydata || []
      }
    ],
    grid: {
      left: 40,
      top: 10,
      bottom: 25,
      right: 20
    }
  };
};

const state = reactive({
  option: buildOption([], [])
});

const query = () => {
  setMockData();
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  // queryFhChart(queryParams.value).then(res => {
  //   let data = res.data || {};
  //   let { xList, yList } = data || {};
  //   state.option = buildOption(xList, yList);
  // });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.VulSourceAnalysis {
  position: relative;
  padding: 20px;
  width: 613px;
  height: 297px;
  .content {
    height: 100%;
  }
}
</style>
