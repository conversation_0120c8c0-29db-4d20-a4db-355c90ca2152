<template>
  <div class="RankingAttackDistribution">
    <div class="content" :style="style">
      <div class="pie">
        <ChartComponent
          class="pie-chart"
          :option="state.option"
        ></ChartComponent>
        <div class="pie-bg">
          <span class="label">{{ state.total }}</span>
        </div>
      </div>
      <div class="legend">
        <div
          class="legend-item"
          v-for="(item, index) in state.items"
          :key="index"
        >
          <div class="left">
            <div
              class="circle"
              :style="{
                background: colors[index]
              }"
            ></div>
            <div class="label">{{ item.name }}</div>
          </div>
          <div class="right">
            <span class="value">{{ item.value }}</span>
            <!--            <el-divider direction="vertical"></el-divider>-->
            <!--            <span class="percent">{{ item.percent }}%</span>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/security-situation/assets/image";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { TitleDrill } from "@/views/modules/ddls";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { queryRankingAttackDistribution } from "@/views/modules/situational-awareness/attack-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const colors = ["#FF3838", "#FF7716", "#E4C513", "#0095FF", "#66E1DF"];

const buildOption = data => {
  return <any>{
    color: colors,
    tooltip: {
      trigger: "item",
      confine: true
    },
    series: [
      {
        name: "攻击来源地分布排名",
        type: "pie",
        radius: ["75%", "95%"],
        padAngle: 2,
        itemStyle: {
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};

const items = Array.from({ length: 5 }, (_, index) => {
  return {
    name: "香港-" + index,
    value: Math.round(Math.random() * 256),
    percent: Math.round(Math.random() * 100)
  };
});
const state = reactive({
  total: 0,
  items: [],
  option: buildOption([])
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.total = items.map(item => item.value).reduce((v1, v2) => v1 + v2);
  state.items = items;
  state.option = buildOption(items);
};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryRankingAttackDistribution(queryParams.value).then(res => {
    console.log("queryRankingAttackDistribution", res);
    let rows = res.data || [];
    // 计算总数
    let total = 0;
    let datas = [];
    for (let row of rows) {
      let { count = 0, filedName: name } = row;
      total += row.count;
      datas.push({
        name,
        value: count,
        percent: 0
      });
    }
    for (let item of datas) {
      let { value } = item;
      item.percent =
        total == 0 ? 0 : parseFloat(((value * 100) / total).toFixed(2));
    }
    state.total = total;
    state.items = datas;
    state.option = buildOption(datas);
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.RankingAttackDistribution {
  position: relative;
  padding: 20px;
  width: 420px;
  height: 298px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    .pie {
      width: 168px;
      height: 168px;
      position: relative;
      .pie-chart {
        position: absolute;
        z-index: 200;
      }
      .pie-bg {
        position: absolute;
        left: 0;
        top: 0;
        background-image: url("../assets/pie_bg.png");
        width: 168px;
        height: 168px;
        background-size: 100% 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .label {
          font-family: MiSans, MiSans;
          font-weight: bold;
          font-size: 16px;
          color: #d1e4ff;
        }
      }
    }
    .legend {
      width: 180px;
      height: 180px;
      display: flex;
      flex-direction: column;
      gap: 5px;
      .legend-item {
        background-image: url("../assets/rect.png");
        width: 180px;
        height: 32px;
        display: flex;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          padding-left: 10px;
          gap: 10px;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          .circle {
            width: 8px;
            height: 8px;
            border-radius: 4px;
          }
        }
        .right {
          padding-right: 10px;
          display: flex;
          align-items: center;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
