<template>
  <div class="RankingAttackDistribution">
    <div class="content" :style="style">
      <BarTopN class="bar-topn" :data="state.data"></BarTopN>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { queryRankingAttackedDepts } from "@/views/modules/situational-awareness/attack-situation/api";
import BarTopN from "@/views/modules/situational-awareness/components/topn/BarTopN.vue";

const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const colors = ["#FF3838", "#FF7716", "#E4C513", "#0095FF", "#66E1DF"];

const state = reactive({
  data: []
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.data = Array.from({ length: 5 }, () => {
    return {
      label: "南昌区",
      value: 123,
      percent: 32
    };
  });
};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryRankingAttackedDepts(queryParams.value).then(res => {
    console.log("queryRankingAttackedDistricts", res);
    let datas = res.data || [];
    let total = 0;
    let data = [];
    for (let item of datas) {
      let { filedName, count = 0 } = item;
      total += count;
      data.push({
        label: filedName,
        value: count,
        percent: 0
      });
    }
    for (let item of data) {
      let { value = 0 } = item;
      item.percent = (value * 100) / total;
    }
    state.data = data;
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.RankingAttackDistribution {
  position: relative;
  padding: 20px;
  width: 420px;
  height: 298px;
  .content {
    height: 100%;
  }
}
</style>
