<template>
  <div class="AttackSituationWorldMap">
    <MapComponent
      ref="worldMapEl"
      class="attack-world-map"
      :map-name="mapName"
      show-tooltip
      :geo-config="geoConfig"
      :tooltip="tooltip"
      :show-label="false"
      :line-data="state.lineData"
      :scatter-symbol="scatterSymbol"
      :scatter-data="state.scatterData"
      @chart-click="handleChartClick"
      @update="onUpdateOption"
    ></MapComponent>
  </div>
</template>
<script setup lang="ts">
import {
  computed,
  inject,
  onActivated,
  onBeforeMount,
  onBeforeUnmount,
  onDeactivated,
  onMounted,
  reactive,
  ref,
  watch
} from "vue";
import MapComponent from "@/components/Echarts/MapComponent.vue";
import markRed from "../assets/markRed.png";
import markBlue from "../assets/markBlue.png";
import { querySummaryAttackEventGloEarth } from "@/views/modules/situational-awareness/attack-situation/api";
const worldMapEl = ref();
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const mapName = ref<string>("world");
const defaultWorldGeoConfig = {
  zoom: 0.7,
  center: [40, 28.68],
  scaleLimit: {
    min: 0.7,
    max: 20
  }
};
// 默认世界地图配置
const geoConfig = ref<any>(defaultWorldGeoConfig);

const scatterSymbol = (value, params) => {
  let { obj } = params.data;
  let { type } = obj || {};
  if (type == "src") {
    return `image://${markBlue}`;
  } else {
    return `image://${markRed}`;
  }
};
const onUpdateOption = option => {
  console.log("option", option);
};

const tooltip = {
  trigger: "item",
  formatter: function (params) {
    if (["effectScatter", "scatter"].includes(params.seriesType)) {
      let { name, obj = {} } = params.data;
      return `${name}: ${obj?.count || 0}`;
    } else if (params.seriesType == "lines") {
      let { obj } = params.data;
      if (obj) {
        let { src, dst, count } = obj;
        return `${src}攻击了${dst}一共${count}次`;
      }
    } else if (params.seriesType == "map") {
      let { name } = params.data;
      let findItem = state.scatterData.find(d => d.name == name);
      if (findItem) {
        let { name, obj = { count: 0 } } = findItem;
        return `${name}: ${obj?.count || 0}`;
      }
    }
    return null;
  }
};

const handleChartClick = params => {
  console.log("handleChartClick", params);
};

const state = reactive({
  center: [55.858197, 28.682892],
  // 国内外连线数据
  storeLineData: [],
  lineData: [
    {
      obj: {
        src: "美国",
        dst: "南昌",
        count: 120
      },
      coords: [
        [-108.24311, 34.052713],
        [115.89, 28.68]
      ]
    }
  ],
  // 国内外打点数据
  storeScatterData: [],
  scatterData: [
    {
      name: "南昌",
      value: [115.89, 28.68],
      obj: {
        type: "dst",
        count: 100
      }
    },
    {
      name: "美国",
      value: [-108.24311, 34.052713],
      obj: {
        type: "src",
        count: 100
      }
    }
  ]
});

const query = () => {
  querySummaryAttackEventGloEarth({
    dateRange: "1y"
  }).then(res => {
    let res2 = {
      status: "0",
      data: {
        sources: [
          {
            domestic: false,
            name: "美国",
            count: 3757,
            location: "[-95.712891,37.090240]"
          },
          {
            domestic: false,
            name: "瑞士",
            count: 12,
            location: "[8.227512,46.818188]"
          },
          {
            domestic: false,
            name: "波兰",
            count: 21,
            location: "[19.145136,51.919438]"
          },
          {
            domestic: true,
            name: "上海",
            count: 133,
            location: "[121.473701,31.230416]"
          },
          {
            domestic: true,
            name: "江西省",
            count: 187,
            location: "[115.509228,29.975696]"
          },
          {
            domestic: false,
            name: "俄罗斯",
            count: 8,
            location: "[105.318756,61.524010]"
          },
          {
            domestic: true,
            name: "广东省",
            count: 3,
            location: "[113.266530,23.132191]"
          },
          {
            domestic: false,
            name: "加拿大",
            count: 9,
            location: "[-106.346771,56.130366]"
          },
          {
            domestic: false,
            name: "日本",
            count: 6,
            location: "[138.252924,36.204824]"
          },
          {
            domestic: true,
            name: "山东省",
            count: 3,
            location: "[117.020359,36.668530]"
          },
          {
            domestic: false,
            name: "墨西哥",
            count: 2,
            location: "[-102.552784,23.634501]"
          },
          // {
          //   domestic: false,
          //   name: "中国",
          //   count: 5,
          //   location: "[104.195397,35.861660]"
          // },
          {
            domestic: true,
            name: "河北省",
            count: 2,
            location: "[114.468664,38.037057]"
          },
          {
            domestic: true,
            name: "北京",
            count: 397,
            location: "[116.407526,39.904030]"
          }
        ],
        targets: [
          {
            name: "南昌市",
            count: 4545,
            location: "[115.858197, 28.682892]"
          }
        ]
      },
      errors: null,
      msg: "OK",
      timestamp: 1737453041941
    };

    let { sources, targets } = res.data;
    let scatterData = [];
    let linkData = [];
    let center = [];
    let target = targets[0];
    let targetData = null;
    if (target) {
      center = JSON.parse(target.location);
      scatterData.push(
        (targetData = {
          name: target.name,
          value: JSON.parse(target.location),
          obj: {
            type: "dst",
            ...target
          }
        })
      );
    }

    // 构建节点和连线
    for (let d of sources || []) {
      let { name, count, location, domestic } = d || {};
      let value = JSON.parse(location);
      scatterData.push({
        name,
        value,
        domestic,
        obj: {
          type: "src",
          ...(d || {})
        }
      });
      if (targetData) {
        let link = {
          obj: {
            src: name,
            dst: targetData.name,
            count: count
          },
          domestic,
          coords: [value, targetData.value]
        };
        linkData.push(link);
      }
      state.storeScatterData = scatterData;
      state.storeLineData = linkData;
      filterMapData();
    }
  });
};

const filterMapData = () => {
  let targetDomestic = mapName.value == "china";
  state.scatterData = state.storeScatterData.filter(
    d => d.domestic == undefined || d.domestic == targetDomestic
  );
  state.lineData = state.storeLineData.filter(
    d => d.domestic == targetDomestic
  );
};

onBeforeMount(() => {
  query();
});

onMounted(() => {
  // 中国地图和世界地图切换
  setInterval(() => {
    if (mapName.value == "china") {
      // 切换到世界地图
      // 5s动画
      worldMapEl.value.setOption({
        animation: true, // 启用动画
        animationDurationUpdate: 2000, // 动画持续时间
        animationEasing: "cubicOut", // 动画缓动效果
        geo: {
          center: [90.89, 32.68],
          zoom: 0.3
        }
      });

      // 2秒后更新(动画完成后更新)
      setTimeout(() => {
        mapName.value = "world";
        geoConfig.value = defaultWorldGeoConfig;
        filterMapData();
      }, 2000);
    } else {
      // 切换到china
      // 5s动画
      worldMapEl.value.setOption({
        animation: true, // 启用动画
        animationDurationUpdate: 5000, // 动画持续时间
        animationEasing: "cubicOut", // 动画缓动效果
        geo: {
          center: [108.89, 32.68],
          zoom: 5 // 修改 zoom 值
        }
      });
      // 5秒后更新(动画完成后更新)
      setTimeout(() => {
        mapName.value = "china";
        geoConfig.value = {
          zoom: 1,
          center: [108.89, 32.68],
          scaleLimit: {
            min: 0.8,
            max: 20
          }
        };
        filterMapData();
      }, 5000);
    }
  }, 10000);
});

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.AttackSituationWorldMap {
  width: 100%;
  height: 100%;
  .attack-world-map {
  }
}
</style>
