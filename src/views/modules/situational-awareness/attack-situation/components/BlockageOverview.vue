<template>
  <div class="BlockageOverview">
    <div class="content" :style="style">
      <div class="item relative">
        <img
          class="scan-radar absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
          src="../assets/scanRadar.png"
          style="width: 171px; height: 171px"
        />
        <span
          class="total absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
        >
          扫描
        </span>
      </div>
      <div class="item flex-c">
        <el-row>
          <el-col
            v-for="(item, index) in state.items"
            class="flex flex-col justify-center h-[80px]"
            :key="index"
            :span="item.span"
          >
            <div class="flex items-center justify-start" style="gap: 10px">
              <img :src="item.img" style="width: 48px; height: 48px" />
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                "
              >
                <span class="count" @click="handleDrillItem(item)">
                  {{ item.count }}
                </span>
                <span class="label">{{ item.label }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
// 封堵概览
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
import { queryIpPlanBlockCount } from "@/views/modules/situational-awareness/attack-situation/api";
import blockage from "../assets/blockage.png";
import unfix from "../assets/unfix.png";

const { $router } = getCurrentInstance().appContext.config.globalProperties;
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const state = reactive({
  total: 0,
  items: [
    {
      label: "高危告警",
      count: 0,
      span: 24,
      img: blockage
    },
    {
      label: "IP封堵",
      count: 0,
      span: 24,
      img: unfix
    }
  ]
});

const globalQuery = inject("globalQuery");
const shareDataContext: {
  alarmCount: number;
} = inject("shareDataContext") || {
  alarmCount: 0
};
const queryParams = computed(() => {
  return globalQuery || {};
});
const query = () => {
  if (props.selectionMode) {
    state.items = [
      {
        label: "高危告警",
        count: 25388,
        span: 24,
        img: blockage
      },
      {
        label: "IP封堵",
        count: 2588,
        span: 24,
        img: unfix
      }
    ];
    return;
  }

  // 单独查询ip封堵数量
  queryIpPlanBlockCount({
    dateRange: "1m"
  }).then(res => {
    state.items[1].count = typeof res.data == "number" ? res.data : 0;
  });
};

const handleDrillItem = item => {
  // console.log("drill call");
  // $router.push({
  //   name: item.drillRoute
  // });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => shareDataContext,
  () => {
    state.items[0].count = shareDataContext?.alarmCount || 0;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
// 注意高度直接使用UI给的高度，不要设置百分比，外层会自动缩放
.BlockageOverview {
  position: relative;
  padding: 20px;
  width: 420px;
  height: 298px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .scan-radar {
      transition: transform 0.5s ease; /* 平滑过渡效果 */
      animation: rotate 10s linear infinite; /* 无限循环旋转动画 */
    }
    .item {
      width: 50%;
      height: 100%;
      .total {
        font-family: OPPOSans, OPPOSans;
        font-weight: 800;
        font-size: 20px;
        color: #ffffff;
        line-height: 24px;
        text-shadow: 0px 6px 0px rgba(0, 0, 0, 0.15);
      }
      .label {
        font-family: MiSans-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #d1e4ff;
        line-height: 16px;
        margin-top: 9px;
      }
      .count {
        font-family: DIN-Medium;
        font-weight: 500;
        font-size: 24px;
        color: #ffffff;
        line-height: 18px;
      }
    }
  }

  @keyframes rotate {
    0% {
      transform: translate(-50%, -50%) rotate(0deg); /* 从0度开始 */
    }
    100% {
      transform: translate(-50%, -50%) rotate(360deg); /* 旋转360度 */
    }
  }
}
</style>
