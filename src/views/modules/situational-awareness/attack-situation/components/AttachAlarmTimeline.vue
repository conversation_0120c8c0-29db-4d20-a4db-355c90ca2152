<template>
  <div class="AttachAlarmTimeline">
    <div class="content" :style="style">
      <AlarmTimeline :event-list="state.eventList"></AlarmTimeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
import { queryAlarmTimelineScreen } from "@/views/modules/situational-awareness/attack-situation/api";
import AlarmTimeline from "@/views/modules/situational-awareness/components/timeline/AlarmTimeline.vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const state = reactive({
  eventList: Array.from({ length: 100 }, () => {
    return {
      event_subtype_name: "僵尸网络",
      event_agent: "深信服态势感知平台",
      event_name: "有害程序-僵尸资产",
      last_time: "2020-11-22",
      reseverity: "5"
    };
  })
});

const shareDataContext: {
  alarmCount: number;
} = inject("shareDataContext");
const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});
const query = () => {
  if (props.selectionMode) {
    state.eventList = Array.from({ length: 100 }, () => {
      return {
        event_subtype_name: "僵尸网络",
        event_agent: "深信服态势感知平台",
        event_name: "有害程序-僵尸资产",
        last_time: "2020-11-22",
        reseverity: "5"
      };
    });
    return;
  }
  queryAlarmTimelineScreen({
    conditions: [],
    dateRange: "1y",
    event_type_tag: null,
    orgId: "",
    asset_app_name: "",
    event_agent: "",
    reseverity: ["5", "4"],
    model: "event",
    pageNum: 1,
    pageSize: 200,
    ...(queryParams.value as any)
  }).then(res => {
    let { totalElements, rows } = res.data || {};
    shareDataContext.alarmCount = totalElements;
    state.eventList = rows;
  });
};

const handleDrillItem = item => {
  // console.log("drill call");
  // $router.push({
  //   name: item.drillRoute
  // });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.AttachAlarmTimeline {
  position: relative;
  padding: 20px;
  width: 420px;
  height: 955px;
  .content {
    width: 100%;
    height: 100%;
  }
}
</style>
