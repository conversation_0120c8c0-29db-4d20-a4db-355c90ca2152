import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";
const portalServerPath = `${ServerNames.portalServer}`;
const securityEventPath = `${ServerNames.securityEventServer}`;
const securityVulPath = ServerNames.securityVulServer.startsWith("/")
  ? ServerNames.securityVulServer
  : `/${ServerNames.securityVulServer}`;
/**
 * 封堵概览
 *
 * @param params
 */
export const queryIpPlanBlockCount = params => {
  return http.get<any, any>(
    `${ServerNames.eamIpPlan}/ipplan/plug/queryPlugCount`,
    { params: params || {} }
  );
};

/**
 * 受攻击组织排名top3
 *
 * @param params
 */
export const queryRankingAttackedDepts = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/summaryAlarmByField`,
    {
      field: "dst_org_name",
      dateRange: "1y",
      top: 5,
      ...(params || {})
    }
  );
};

/**
 * 攻击来源地分布排名top5
 *
 * @param params
 */
export const queryRankingAttackDistribution = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/summaryAlarmByField`,
    {
      field: "src_country_name",
      dateRange: "1y",
      top: 5,
      ...(params || {})
    }
  );
};

/**
 * 攻击告警时间轴
 *
 * @param params
 */
export const queryAlarmTimelineScreen = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/queryAlarmListScreen`,
    params
  );
};

/**
 * 攻击来源地分布排名
 *
 * @param params
 */
export const querySummaryAttackEventGloEarth = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/summaryAttackEventGloEarth`,
    params
  );
};
