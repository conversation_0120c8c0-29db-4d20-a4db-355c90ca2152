<template>
  <div class="attack-situation">
    <ls-view :ref-id="refId" :view-index="0">
      <template #tr>
        <div style="margin-top: 15px; margin-right: 20px">
          <dark-clock></dark-clock>
        </div>
      </template>
    </ls-view>
  </div>
</template>
<script lang="ts" setup>
// 攻击态势 大屏设计关联的编号
import DarkClock from "@/views/modules/situational-awareness/components/dark-clock/DarkClock.vue";

const refId = "09ba8a0cf4400000";
import { onBeforeUnmount, onMounted, provide, reactive, watch } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();
defineOptions({
  name: "attack_situation",
  activated() {
    window.dispatchEvent(new Event("resize"));
  }
});
const state = reactive({
  timer: -1
});
// 全局查询条件
const globalQuery = reactive({
  dateRange: "1d",
  reloadFlag: 1
});
// 共享的数据上下文
const shareDataContext = reactive({
  alarmCount: 0
});

const triggerReload = () => {
  ++globalQuery.reloadFlag;
};
onMounted(() => {
  // 5分钟刷新一次
  state.timer = setInterval(() => {
    triggerReload();
  }, 300000) as any;
});

// 停止定时器
onBeforeUnmount(() => {
  clearInterval(state.timer);
});
provide("globalQuery", globalQuery);
provide("shareDataContext", shareDataContext);

watch(
  () => route?.query.dateRange as string,
  val => {
    globalQuery.dateRange = val || "1d";
  }
);
</script>
<style lang="scss" scoped>
.attack-situation {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
