<script setup lang="ts">
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { queryApiEventGroup } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { baseComponentProps } from "@/views/modules/ddls/module";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const state = reactive({
  alarmLevels: [
    {
      label: "高风险",
      key: "3",
      value: 0,
      percentage: 0,
      color: "#FE3838",
      unit: "个"
    },
    {
      label: "中风险",
      key: "2",
      value: 0,
      percentage: 0,
      color: "#FFA254",
      unit: "个"
    },
    {
      label: "低风险",
      key: "1",
      value: 0,
      percentage: 0,
      color: "#0091FF",
      unit: "个"
    }
  ]
});

const setMockData = () => {
  state.alarmLevels = [
    {
      label: "高风险",
      key: "3",
      value: 20,
      percentage: 20,
      color: "#FE3838",
      unit: "个"
    },
    {
      label: "中风险",
      key: "2",
      value: 30,
      percentage: 30,
      color: "#FFA254",
      unit: "个"
    },
    {
      label: "低风险",
      key: "1",
      value: 50,
      percentage: 50,
      color: "#0091FF",
      unit: "个"
    }
  ];
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }

  // 重置
  for (let alarmLevel of state.alarmLevels) {
    alarmLevel.value = 0;
    alarmLevel.percentage = 0;
  }
  queryApiEventGroup({
    conditions: [],
    model: "",
    field: "riskLevel",
    limit: 10,
    headerFilter: {
      filters: []
    },
    ...queryParams.value
  }).then(res => {
    let { options } = res.data || {};
    if (Array.isArray(options)) {
      let total = 0;
      for (let option of options) {
        let { value, count } = option;
        if (value == "3") {
          state.alarmLevels[0].value = count;
          total += count;
        } else if (value == "2") {
          state.alarmLevels[1].value = count;
          total += count;
        } else if (value == "1") {
          state.alarmLevels[2].value = count;
          total += count;
        }
      }

      if (total > 0) {
        // 计算百分比
        state.alarmLevels[0].percentage = parseFloat(
          ((state.alarmLevels[0].value / total) * 100).toFixed(2)
        );
        state.alarmLevels[1].percentage = parseFloat(
          ((state.alarmLevels[1].value / total) * 100).toFixed(2)
        );
        let rem =
          100 -
          state.alarmLevels[0].percentage -
          state.alarmLevels[1].percentage;
        state.alarmLevels[2].percentage = parseFloat(rem.toFixed(2));
      }
    }
  });
};

const handleDrill = () => {
  $router.push({
    name: "api_risk_management",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="InterfaceAlarmLevelStatistics">
    <div class="header-title">
      <!--      <img class="img-title" src="./img/InterfaceAlarmLevelStatistics.png" />-->
      <div class="img-title">
        <img class="title-img" src="../img/group.png" />
        <span class="title-text">API告警</span>
      </div>
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>
    <div class="items">
      <div class="item" v-for="(item, index) in state.alarmLevels" :key="index">
        <el-progress
          :color="item.color"
          type="circle"
          :percentage="item.percentage"
          :width="48"
          :stroke-width="6"
          stroke-linecap="butt"
        />
        <div class="label-value">
          <span class="value-unit">
            <span class="value" :style="{ color: item.color }">{{
              item.value
            }}</span>
            <span class="unit">{{ item.unit }}</span>
          </span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.InterfaceAlarmLevelStatistics {
  width: 100%;
  .header-title {
    margin-bottom: 20px;
    position: relative;
    .img-title {
      display: flex;
      align-items: center;
      gap: 6px;
      .title-text {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #ffffff 0%, #0091ff 100%);
        background-clip: text;
        color: transparent;
      }
    }
  }
  .items {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .item {
      display: flex;
      align-items: center;
      gap: 10px;
      --el-fill-color-light: rgba(0, 145, 255, 0.6);
      :deep(.el-progress__text) {
        font-size: 10px !important;
        color: rgba(255, 255, 255, 0.8);
      }
      .label-value {
        display: flex;
        flex-direction: column;
        gap: 6px;
        .label {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          line-height: 14px;
        }
        .value {
          font-family: DIN-Bold;
          font-size: 16px;
          line-height: 16px;
          color: #ffffff;
        }
        .unit {
          font-family: PingFang SC-Regular;
          font-size: 12px;
          line-height: 12px;
          color: #b1c7d6;
          margin-left: 6px;
        }
      }
    }
  }
}
</style>
