<script setup lang="ts">
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
import { baseComponentProps } from "@/views/modules/ddls/module";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import FlowTable from "@/views/modules/ddls/components/FlowTable.vue";
import { queryApiEventCount } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { v4 } from "uuid";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});

const state = reactive({
  columns: [
    // { label: "应用名称", prop: "appName", width: "40%", align: "center" },
    // {
    //   label: "接口名称",
    //   prop: "reqUrl",
    //   width: "40%",
    //   align: "left"
    // },
    {
      label: "IP接口",
      prop: "ipReqUrl",
      width: "80%",
      headerAlign: "center",
      align: "left"
    },

    {
      label: "风险数量",
      prop: "riskCount",
      width: "20%",
      align: "center"
    }
  ],
  rows: []
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.rows = Array.from({ length: 115 }, (_, index) => {
    return {
      rowId: v4(),
      appName: "智能AI运营平台 " + index,
      reqUrl: "/nacos/V1/cs/configs/list... - " + index,
      ipReqUrl: "*******/nacos/V1/cs/configs/list... - " + index,
      riskCount: 1
    };
  });
};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryApiEventCount({
    conditions: [],
    model: "",
    headerFilter: {
      filters: []
    },
    pageNum: 1,
    pageSize: 200,
    ...queryParams.value
  }).then(res => {
    console.log("queryApiEventCount", res);
    let { list } = res.data || {};
    if (Array.isArray(list)) {
      state.rows = list.map(item => {
        return {
          rowId: v4(),
          ipReqUrl: item.appAddress + item.reqUrl,
          ...item
        };
      });
    }
  });
};

const handleDrill = () => {
  $router.push({
    name: "api_risk_management",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="ApiRiskAlertStatistics">
    <div class="header-title">
      <img class="img-title" src="./img/ApiRiskAlertStatistics.png" />
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>
    <div class="table-wrap">
      <flow-table
        row-key="rowId"
        :columns="state.columns"
        :rows="state.rows"
        :row-height="32"
        auto-scroll
        :actual-render-rows="200"
        :stripe-offset="1"
        stripe-background="rgba(0,145,255,0.1)"
        header-background="rgba(14,47,100,0.6)"
      >
        <template #top="{ offsetIndex }">
          <div class="top-value" :class="['top-value-' + offsetIndex]">
            {{ offsetIndex < 9 ? "0" : "" }}{{ offsetIndex + 1 }}
          </div>
        </template>
      </flow-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ApiRiskAlertStatistics {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 20px;
  }
  .table-wrap {
    height: 290px;
  }
}
</style>
