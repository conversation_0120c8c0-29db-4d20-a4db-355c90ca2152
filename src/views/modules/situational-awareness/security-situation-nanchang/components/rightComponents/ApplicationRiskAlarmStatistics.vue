<script setup lang="ts">
import TableTop5 from "@/views/modules/situational-awareness/components/topn/TableTop5.vue";
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { baseComponentProps } from "@/views/modules/ddls/module";
import { queryApiEventGroup } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { TitleDrill } from "@/views/modules/ddls";

const props = defineProps({
  ...baseComponentProps
});

const computedColumns = computed(() => {
  const label = "告警类型";
  return [
    {
      label: label,
      prop: "label"
    },
    {
      label: "高危漏洞数",
      prop: "value"
    }
  ];
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const state = reactive({
  data: []
});

const setMockData = () => {
  const labels = [
    "昌通码客户端",
    "公共能力支撑平台",
    "四级体系一站式平台",
    "公共能力支撑平台",
    "广电计量"
  ];
  state.data = Array.from(
    {
      length: 5
    },
    (_, index) => {
      return {
        label: labels[index % 5],
        percent: parseInt((Math.random() * 1000) % 100),
        value: parseInt((Math.random() * 1000) % 1000)
      };
    }
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }

  // 清空数据
  state.data = [];
  queryApiEventGroup({
    conditions: [],
    model: "",
    field: "appName",
    limit: 5,
    headerFilter: {
      filters: []
    },
    ...queryParams.value
  }).then(res => {
    let { options } = res.data || {};
    if (Array.isArray(options)) {
      options = options.slice(0, 5);
      let total = 0;
      let rows = [];
      for (let option of options) {
        let { label, count } = option;
        total += count;
        rows.push({
          label,
          value: count
        });
      }
      for (let i = 0; i < rows.length; i++) {
        rows[i].percent = parseFloat(
          ((rows[i].value / total) * 100).toFixed(2)
        );
      }
      state.data = rows;
    }
  });
};

const handleDrill = () => {
  $router.push({
    name: "api_risk_management",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="ApplicationRiskAlarmStatistics">
    <div class="header-title">
      <img class="img-title" src="./img/ApplicationRiskAlarmStatistics.png" />
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>
    <TableTop5
      class="table-top5"
      :columns="computedColumns"
      :data="state.data"
      hidden-header
      hidden-top-col
    ></TableTop5>
  </div>
</template>

<style scoped lang="scss">
.ApplicationRiskAlarmStatistics {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 20px;
  }
  .table-top5 {
    min-height: 166px;
    height: 166px;
    :deep(.data-row) {
      margin-bottom: 9px;
      &:last-child {
        margin-bottom: 0 !important;
      }
    }
    :deep(.label-col) {
      flex: 1;
      padding: 0 10px 0 0;
      .bar-wrap {
        margin-top: 0px !important;
      }
      .bar-percent {
        background: linear-gradient(
          270deg,
          #0095ff 0%,
          rgba(0, 149, 255, 0) 100%
        ) !important;
      }
    }
  }
}
</style>
