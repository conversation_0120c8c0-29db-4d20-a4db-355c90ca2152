<script setup lang="ts">
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import ApiBasicInformation_sensitive from "./img/ApiBasicInformation_sensitive.png";
import ApiBasicInformation_active from "./img/ApiBasicInformation_active.png";
import ApiBasicInformation_login_inf from "./img/ApiBasicInformation_login_inf.png";
import ApiBasicInformation_file_download from "./img/ApiBasicInformation_file_download.png";
import { queryApiEventTags } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { baseComponentProps } from "@/views/modules/ddls/module";
const props = defineProps({
  ...baseComponentProps
});
const state = reactive({
  items: [
    {
      label: "监控应用总数",
      unit: "个",
      value: 0,
      routeQuery: {
        focusApi: "监控应用总数"
      },
      img: ApiBasicInformation_sensitive
    },
    {
      label: "监控API数量",
      unit: "个",
      value: 0,
      routeQuery: {
        focusApi: "监控API数量"
      },
      img: ApiBasicInformation_active
    },
    {
      label: "调用访问总量",
      unit: "次",
      value: 0,
      routeQuery: {
        focusApi: "调用访问总量"
      },
      img: ApiBasicInformation_login_inf
    },
    {
      label: "涉敏接口数量",
      unit: "个",
      value: 0,
      routeQuery: {
        focusApi: "涉敏接口数量"
      },
      img: ApiBasicInformation_file_download
    }
  ]
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const mockflag = true;

const query = () => {
  if (mockflag || props.selectionMode || props.designMode) {
    state.items[0].value = 329;
    state.items[1].value = 13417;
    state.items[2].value = 729771;
    state.items[3].value = 5998;
    return;
  }

  // 重置
  for (let item of state.items) {
    item.value = 0;
  }

  let items = state.items;

  // 清空数据
  queryApiEventTags({
    conditions: [],
    model: "",
    field: "focusApi",
    headerFilter: {
      filters: []
    },
    ...queryParams.value
  }).then(res => {
    console.log("queryApiEventTags ", res);
    let data = res.data;
    if (Array.isArray(data)) {
      for (let item of data) {
        let { label, count } = item;
        if (label == items[0].label /*"敏感API"*/) {
          state.items[0].value = count;
        } else if (label == items[1].label /*"活跃API"*/) {
          state.items[1].value = count;
        } else if (label == items[2].label /*"登录接口"*/) {
          state.items[2].value = count;
        } else if (label == items[3].label /*"文件下载接口"*/) {
          state.items[3].value = count;
        }
      }
    }
  });
};

const handleDrill = item => {
  // api 清单
  $router.push({
    name: "api_list_management",
    query: {
      dateRange: queryParams.value.dateRange,
      ...(item.routeQuery || {})
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="ApiBasicInformation">
    <img class="img-title" src="./img/ApiBasicInformation.png" />
    <div class="items">
      <div class="item" v-for="(item, index) in state.items" :key="index">
        <img :src="item.img" width="32" height="32" />
        <div class="label-value">
          <span class="value-unit">
            <span
              class="value"
              :class="{ disableClick: mockflag }"
              @click="handleDrill(item)"
              >{{ item.value }}</span
            >
            <span class="unit">{{ item.unit }}</span>
          </span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ApiBasicInformation {
  width: 100%;
  cursor: default;
  .img-title {
    margin-bottom: 20px;
  }
  .items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px 20px;
    padding: 0 50px;
    .item {
      display: flex;
      align-items: flex-end;
      gap: 10px;
      .label-value {
        display: flex;
        flex-direction: column;
        gap: 10px;
        .label {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          line-height: 14px;
        }
        .value {
          cursor: pointer;
          font-family: DIN-Bold;
          font-size: 16px;
          line-height: 16px;
          color: #ffffff;
          &:hover {
            text-decoration: underline;
          }
        }
        .disableClick {
          cursor: default !important;
          text-decoration: none !important;
          pointer-events: none;
        }
        .unit {
          font-family: PingFang SC-Regular;
          font-size: 12px;
          line-height: 12px;
          color: #b1c7d6;
          margin-left: 6px;
        }
      }
    }
  }
}
</style>
