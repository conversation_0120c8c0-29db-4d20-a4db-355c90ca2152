<template>
  <div class="DataSecuritySituation">
    <div class="content" :style="style">
      <api-basic-information v-bind="props"></api-basic-information>
      <!--      <interface-alarm-level-statistics-->
      <!--        v-bind="props"-->
      <!--      ></interface-alarm-level-statistics>-->
      <trigger-event-statistics v-bind="props"></trigger-event-statistics>
      <api-risk-alert-statistics v-bind="props"></api-risk-alert-statistics>
      <application-risk-alarm-statistics
        v-bind="props"
      ></application-risk-alarm-statistics>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties } from "vue";
import ApiBasicInformation from "./rightComponents/ApiBasicInformation.vue";
import InterfaceAlarmLevelStatistics from "./rightComponents/InterfaceAlarmLevelStatistics.vue";
import TriggerEventStatistics from "./rightComponents/TriggerEventStatistics.vue";
import ApplicationRiskAlarmStatistics from "./rightComponents/ApplicationRiskAlarmStatistics.vue";
import ApiRiskAlertStatistics from "./rightComponents/ApiRiskAlertStatistics.vue";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
</script>
<style scoped lang="scss">
.DataSecuritySituation {
  position: relative;
  padding: 20px;
  width: 460px;
  height: 970px;
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}
</style>
