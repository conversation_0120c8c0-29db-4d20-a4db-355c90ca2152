<template>
  <div class="ControlAssetStatistics">
    <img class="img-title" src="./img/ControlAssetStatistics.png" />
    <div class="items">
      <div class="item" v-for="(item, index) in state.items" :key="index">
        <img :src="item.img" width="32" height="32" />
        <div class="label-value">
          <span class="value-unit">
            <span class="value" @click="handleDrill(item, index)">{{
              item.value
            }}</span>
            <span class="unit">{{ item.unit }}</span>
          </span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
import ControlAssetStatistics_reg_asset from "./img/ControlAssetStatistics_reg_asset.png";
import ControlAssetStatistics_risk_event from "./img/ControlAssetStatistics_risk_event.png";
import ControlAssetStatistics_ai_res from "./img/ControlAssetStatistics_ai_res.png";
import { baseComponentProps } from "@/views/modules/ddls/module";
import { queryOnlineZqCount } from "@/views/modules/situational-awareness/asset-situation/api";
import { queryIpPlanBlockCount } from "@/views/modules/situational-awareness/attack-situation/api";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const shareDataContext: {
  alarmCount: number;
} = inject("shareDataContext") || {
  alarmCount: 0
};
const state = reactive({
  items: [
    {
      label: "登记资产数",
      unit: "个",
      value: 0,
      img: ControlAssetStatistics_reg_asset
    },
    {
      label: "风险告警数",
      unit: "次",
      value: 0,
      img: ControlAssetStatistics_risk_event
    }
    /*{
      label: "智能响应数",
      unit: "次",
      value: 0,
      img: ControlAssetStatistics_ai_res
    }*/
  ]
});

const query = () => {
  if (props.selectionMode || props.designMode) {
    return;
  }
  // 1.登记资产数 -> 复用资产态势接口 概况数字(发现资产数/登记资产数/防护资产数)
  let params = queryParams.value || {};
  queryOnlineZqCount(params).then(res => {
    const { data = [] } = res.data || {};
    // 获取第2条记录(登记资产数)
    state.items[0].value = data[1]?.value || 0;
  });

  // 2.查询智能响应数（攻击态势： 封堵数）
  queryIpPlanBlockCount(params).then(res => {
    state.items[2].value = typeof res.data == "number" ? res.data : 0;
  });
};

const handleDrill = (item, index) => {
  if (index == 0) {
    // 资产台账
    $router.push({
      name: "est_eam_instance",
      query: {
        dateRange: queryParams.value.dateRange
      }
    });
  } else if (index == 1) {
    // 安全告警
    $router.push({
      name: "security_deal",
      query: {
        dateRange: queryParams.value.dateRange
      }
    });
  } else {
    // 封堵管理
    $router.push({
      name: "ipmodular_plugging_index",
      query: {
        dateRange: queryParams.value.dateRange
      }
    });
  }
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => shareDataContext.alarmCount,
  val => {
    state.items[1].value = val;
  }
);
</script>
<style scoped lang="scss">
.ControlAssetStatistics {
  cursor: default;
  width: 100%;
  .img-title {
    margin-bottom: 20px;
  }
  .items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .item {
      display: flex;
      align-items: flex-end;
      gap: 10px;
      .label-value {
        display: flex;
        flex-direction: column;
        gap: 10px;
        .label {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          line-height: 14px;
        }
        .value {
          cursor: pointer;
          font-family: DIN-Bold;
          font-size: 16px;
          line-height: 16px;
          color: #ffffff;
          &:hover {
            text-decoration: underline;
          }
        }
        .unit {
          font-family: PingFang SC-Regular;
          font-size: 12px;
          line-height: 12px;
          color: #b1c7d6;
          margin-left: 6px;
        }
      }
    }
  }
}
</style>
