<script setup lang="ts">
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { queryEventRiskSpread } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { baseComponentProps } from "@/views/modules/ddls/module";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const colors = ["#FF3838", "#FFA254", "#0091FF"];
const buildOption = data => {
  return <any>{
    color: colors,
    tooltip: {
      show: false,
      trigger: "item",
      confine: true
    },
    series: [
      {
        name: "告警等级分布",
        type: "pie",
        radius: ["75%", "95%"],
        padAngle: 2,
        itemStyle: {
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};

const items = [
  {
    name: "高风险",
    value: 0,
    percent: 0
  },
  {
    name: "中风险",
    value: 0,
    percent: 0
  },
  {
    name: "低风险",
    value: 0,
    percent: 0
  }
];
const state = reactive({
  items,
  option: buildOption(items)
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryEventRiskSpread({
    ...queryParams.value
  }).then(res => {
    let rows = res.data?.rows || [];
    // 计算总数
    for (let row of rows) {
      let { count = 0, typeName, rate } = row;
      if (typeName == "高危") {
        state.items[0].value = count;
        state.items[0].percent = parseFloat(rate);
      } else if (typeName == "中危") {
        state.items[1].value = count;
        state.items[1].percent = parseFloat(rate);
      } else if (typeName == "低危") {
        state.items[2].value = count;
        state.items[2].percent = parseFloat(rate);
      }
    }
    // update option
    state.option = buildOption(state.items);
  });
};

const handleDrill = () => {
  $router.push({
    name: "security_deal",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="SecurityIncidentStatistics">
    <div class="header-title">
      <img class="img-title" src="./img/SecurityIncidentStatistics.png" />
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>

    <div class="pie-chart">
      <div class="pie">
        <ChartComponent :option="state.option"></ChartComponent>
        <div class="pie-bg">
          <span class="label">告警等级</span>
        </div>
      </div>
      <div class="legend">
        <div
          class="legend-item"
          v-for="(item, index) in state.items"
          :key="index"
        >
          <div class="left">
            <div
              class="circle"
              :style="{
                background: colors[index]
              }"
            ></div>
            <div class="label">{{ item.name }}</div>
          </div>
          <div class="right">
            <span class="value">{{ item.value }}</span>
            <el-divider direction="vertical"></el-divider>
            <span class="percent">{{ item.percent }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.SecurityIncidentStatistics {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 20px;
  }
  .pie-chart {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .pie {
      width: 90px;
      height: 90px;
      position: relative;
      .pie-chart {
        position: absolute;
        z-index: 200;
      }
      .pie-bg {
        position: absolute;
        left: 0;
        top: 0;
        background-image: url("./img/pie_bg.png");
        background-size: 100% 100%;
        width: 90px;
        height: 90px;
        display: flex;
        justify-content: center;
        align-items: center;
        .label {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 500;
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
        }
      }
    }
    .legend {
      width: 234px;
      height: 88px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      .legend-item {
        background-image: url("./img/levelRect.png");
        width: 220px;
        height: 24px;
        display: flex;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          padding-left: 10px;
          gap: 10px;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          .circle {
            width: 8px;
            height: 8px;
            border-radius: 4px;
          }
        }
        .right {
          padding-right: 10px;
          display: flex;
          align-items: center;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          .percent {
            width: 0;
          }
        }
      }
    }
  }
}
</style>
