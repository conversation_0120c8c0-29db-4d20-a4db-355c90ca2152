<script setup lang="ts">
import { computed, inject, reactive, watch } from "vue";
import { baseComponentProps } from "@/views/modules/ddls/module";
import AttackSituationTop5_att_dept from "./img/AttackSituationTop5_att_dept.png";
import AttackSituationTop5_att_app from "./img/AttackSituationTop5_att_app.png";
import FlowTable from "@/views/modules/ddls/components/FlowTable.vue";
import {
  queryRankingAttackedDepts,
  summaryAttackedEvent
} from "@/views/modules/situational-awareness/security-situation-nanchang/api";
const props = defineProps({
  ...baseComponentProps
});
const state = reactive({
  topViewAngle: "dept",
  items: [
    {
      label: "被攻击单位统计",
      angle: "dept",
      unit: "个",
      value: 0,
      img: AttackSituationTop5_att_dept
    },
    {
      label: "被攻击系统统计",
      angle: "app",
      unit: "次",
      value: 0,
      img: AttackSituationTop5_att_app
    }
  ],
  rows: []
});

const computedColumns = computed(() => {
  return [
    { label: "排名", prop: "top", width: "25%", align: "center" },
    {
      label: state.topViewAngle == "dept" ? "所属单位" : "应用名称",
      prop: "filedName",
      width: "50%",
      align: "left"
    },
    {
      label: "被攻击次数",
      prop: "count",
      width: "25%",
      align: "left"
    }
  ];
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const shareDataContext: {
  alarmCount: number;
} = inject("shareDataContext") || {
  alarmCount: 0
};

const setMockData = () => {
  state.rows = Array.from({ length: 115 }, (_, index) => {
    return {
      id: index,
      dept: "上海交通局 - " + index,
      count: 1
    };
  });
};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }

  // 汇总
  summaryAttackedEvent({
    ...queryParams.value
  }).then(res => {
    let { dstAssetAppNameCount, dstOrgNameCount, total } = res.data || {};
    state.items[0].value = dstOrgNameCount;
    state.items[1].value = dstAssetAppNameCount;
    shareDataContext.alarmCount = total;
  });

  // top5
  state.rows = [];
  handleQueryRankingAttacked();
};

const handleQueryRankingAttacked = () => {
  let query = {
    field: state.topViewAngle == "dept" ? "dst_org_name" : "dst_asset_app_name"
  };
  queryRankingAttackedDepts({
    ...query,
    ...queryParams.value
  }).then(res => {
    let data = res.data || [];
    if (Array.isArray(data)) {
      state.rows = data.slice(0, 5);
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="AttackSituationTop5">
    <div class="header-title">
      <img class="img-title" src="./img/AttackSituationTop5.png" />
    </div>
    <div class="items">
      <div
        class="item"
        v-for="(item, index) in state.items"
        :key="index"
        :class="{ selected: state.topViewAngle == item.angle }"
        @click="
          () => {
            state.topViewAngle = item.angle;
            handleQueryRankingAttacked();
          }
        "
      >
        <img :src="item.img" width="32" height="32" />
        <div class="label-value">
          <span class="value-unit">
            <span class="value">{{ item.value }}</span>
          </span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <div class="table-wrap">
      <flow-table
        :columns="computedColumns"
        :rows="state.rows"
        :row-height="32"
        :stripe-offset="1"
        stripe-background="rgba(0,145,255,0.1)"
        header-background="rgba(14,47,100,0.6)"
      >
        <template #top="{ offsetIndex }">
          <div class="top-value" :class="['top-value-' + offsetIndex]">
            {{ offsetIndex < 9 ? "0" : "" }}{{ offsetIndex + 1 }}
          </div>
        </template>
      </flow-table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.AttackSituationTop5 {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 13px;
  }
  .items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 12px;
    .item {
      display: flex;
      align-items: flex-end;
      gap: 10px;
      &.selected {
        .label-value {
          .value {
            color: var(--el-color-primary);
          }
          .label {
            color: var(--el-color-primary);
          }
        }
      }
      .label-value {
        display: flex;
        flex-direction: column;
        gap: 6px;
        .label {
          font-family:
            PingFang SC,
            PingFang SC;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          line-height: 14px;
        }
        .value {
          font-family: DIN-Bold;
          font-size: 18px;
          line-height: 18px;
          color: #ffffff;
        }
        .unit {
          font-family: PingFang SC-Regular;
          font-size: 12px;
          line-height: 12px;
          color: #b1c7d6;
          margin-left: 6px;
        }
      }
    }
  }
  .table-wrap {
    height: 192px;
    .top-value {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #0091ff;
      line-height: 14px;
      &.top-value-0 {
        color: #ff3838;
      }
      &.top-value-1 {
        color: #ffa254;
      }
      &.top-value-2 {
        color: #e4c513;
      }
    }
  }
}
</style>
