<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import * as echarts from "echarts";
import { queryAlarmTrend } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed(() => {
  return globalQuery || {};
});

const setMockData = () => {
  state.option = buildOption(
    [
      "2024-01",
      "2024-02",
      "2024-03",
      "2024-04",
      "2024-05",
      "2024-06",
      "2024-07"
    ],
    [
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000)),
      Math.abs(parseInt(Math.random() * 4000))
    ]
  );
};

const baseFontStyle = {
  fontSize: "12px",
  fontWeight: 300,
  color: "#D1E4FF"
};

const buildOption = (xData, ydata): any => {
  return <EChartsOption>{
    tooltip: {
      trigger: "axis",
      confine: true
    },
    xAxis: {
      axisLabel: {
        ...baseFontStyle
      },
      interval: 2,
      data: xData || []
    },
    yAxis: {
      axisLine: {
        show: true
      },
      axisLabel: {
        ...baseFontStyle,
        formatter: function (value) {
          return value.toString().replace(",", "");
        }
      },
      splitLine: {
        lineStyle: {
          color: "#0095FF",
          opacity: 0.16
        }
      }
    },
    series: [
      {
        name: "告警数",
        type: "line",
        areaStyle: {},
        smooth: true,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "#00AAFF"
          },
          {
            offset: 1,
            color: "rgba(0,41,255,0.05)"
          }
        ]),
        data: ydata || []
      }
    ],
    grid: {
      left: 40,
      top: 10,
      bottom: 20,
      right: 20
    }
  };
};

const state = reactive({
  option: buildOption([], [])
});

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryAlarmTrend({
    dealwith: 1,
    dateRange: "7d"
  }).then(res => {
    let data = res.data || [];
    console.log("queryAlarmTrend data", data); // .slice(0, 7);
    let xData = [],
      ydata = [];
    for (let item of data) {
      let { time, docCount = 0 } = item;
      xData.push(time);
      ydata.push(docCount);
    }
    state.option = buildOption(xData, ydata);
  });
};

const handleDrill = () => {
  $router.push({
    name: "security_deal",
    query: {
      dateRangeSign: "7d"
    }
  });
};

watch(
  () => queryParams.value,
  () => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="SecurityIncidentsChangeTrend">
    <div class="header-title">
      <img class="img-title" src="./img/SecurityIncidentsChangeTrend.png" />
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>
    <ChartComponent
      class="trend-chart"
      :option="state.option"
      resizeable
    ></ChartComponent>
  </div>
</template>

<style scoped lang="scss">
.SecurityIncidentsChangeTrend {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 20px;
  }
  .trend-chart {
    width: 100%;
    height: 100px;
  }
}
</style>
