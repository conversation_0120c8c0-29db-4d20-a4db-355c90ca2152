import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const securityEventPath = `${ServerNames.securityEventServer}`;

/**
 * 告警等级分布（威胁等级分布）
 *
 * @param params
 */
export const queryEventRiskSpread = params => {
  console.log("queryEventRiskSpread situ/eventRiskSpread", params);
  return http.postJson<any>(
    `${securityEventPath}/situ/eventRiskSpread`,
    params
  );
};

/**
 * 告警类型分布
 *
 * @param params
 */
export const eventTypeSpread = params => {
  return http.postJson<any>(`${securityEventPath}/sem/eventTypeSpread`, params);
};

/**
 * 近7天告警趋势图
 * @param params
 */
export const queryAlarmTrend = params => {
  return http.postJson<any>(`${securityEventPath}/sem/eventTrendNew`, params);
};

/**
 * 受攻击汇总
 *
 * @param params
 */
export const summaryAttackedEvent = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEvent/v3/summaryAttackedEvent`,
    params
  );
};

/**
 * 受攻击组织排名(top5)
 *
 * @param params
 */
export const queryRankingAttackedDepts = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/summaryAlarmByField`,
    {
      dateRange: "1y",
      model: "event",
      top: 5,
      ...(params || {})
    }
  );
};

/**
 * 攻击告警时间轴
 *
 * @param params
 */
export const queryAlarmTimelineScreen = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEvent/v3/queryEventScreenNc`,
    params
  );
  // return http.postJson<any>(`${securityEventPath}/situ/eventDetails`, params);
};

/**
 * 攻击来源地分布排名
 *
 * @param params
 */
export const querySummaryAttackEventGloEarth = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/summaryAttackEventGloEarth`,
    params
  );
};

/**
 * 告警级别统计（右2）和应用风险告警统计（右4）共用一个接口，参数不同
 *
 * @param params
 */
export const queryApiEventGroup = params => {
  return http.postJson<any>(
    `${securityEventPath}/apiEvent/queryApiEventGroup`,
    params
  );
};

/**
 * 查询api基本信息（右1）
 * @param params
 */
export const queryApiEventTags = params => {
  return http.postJson<any>(
    `${securityEventPath}/apiEvent/screen/queryApiEventTags`,
    params
  );
};

/**
 * 触发事件类型统计
 *
 * @param params
 */
export const queryApiEventRules = params => {
  return http.postJson<any>(
    `${securityEventPath}/apiEvent/screen/queryApiEventRules`,
    params
  );
};

/**
 * 风险告警统计
 *
 * @param params
 */
export const queryApiEventCount = params => {
  return http.postJson<any>(
    `${securityEventPath}/apiEvent/screen/queryApiEventCount`,
    params
  );
};
