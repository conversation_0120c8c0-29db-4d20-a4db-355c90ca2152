<template>
  <div class="security-situation-nanchang">
    <ls-view :ref-id="refId" :view-index="state.viewIndex">
      <template #tr="{ screenScales, pageScales }">
        <div class="asset-situation-bloc-tr">
          <span class="label">周期统计：</span>
          <div class="dark-select">
            <el-select
              placeholder="请选择"
              style="width: 120px"
              popper-class="large-screen-popper"
              persistent
              v-model="globalQuery.dateRange"
            >
              <el-option
                v-for="(periodOption, index) in state.dateRanges"
                :key="index"
                v-bind="periodOption"
              ></el-option>
            </el-select>
          </div>
          <!--          <span class="label" style="margin-left: 20px">视图切换：</span>-->
          <!--          <div class="dark-select">-->
          <!--            <el-select-->
          <!--              v-model="globalQuery.zoneName"-->
          <!--              placeholder="全部"-->
          <!--              style="width: 140px"-->
          <!--              popper-class="large-screen-popper"-->
          <!--              persistent-->
          <!--              filterable-->
          <!--              @change="handleDeptChange"-->
          <!--            >-->
          <!--              <el-option label="全部" value="">-->
          <!--                <div style="font-weight: bold">全部</div>-->
          <!--              </el-option>-->
          <!--              <el-option-->
          <!--                v-for="(deptOption, index) in state.deptOptions"-->
          <!--                :key="index"-->
          <!--                v-bind="deptOption"-->
          <!--              ></el-option>-->
          <!--            </el-select>-->
          <!--          </div>-->
          <dark-clock style="margin-left: 20px"></dark-clock>
        </div>
      </template>
    </ls-view>
  </div>
</template>
<script lang="ts" setup>
// 安全态势-南昌 大屏设计关联的编号
const refId = "0a389fb2f3800000";
import { onBeforeUnmount, onMounted, provide, reactive, ref } from "vue";
import DarkClock from "@/views/modules/situational-awareness/components/dark-clock/DarkClock.vue";
defineOptions({
  name: "security_situation_nanchang",
  activated() {
    window.dispatchEvent(new Event("resize"));
  }
});
const state = reactive({
  // 0 代表集团视角； 1 代表子公司视角
  viewIndex: 0,
  deptOptions: [],
  timer: -1,

  dateRanges: [
    // { label: "近24小时", value: "1d" },
    { label: "近7天", value: "7d" }
    // { label: "近半年", value: "6m" },
    // { label: "近1年", value: "1y" }
  ]
});
// 全局查询条件
const globalQuery = reactive({
  dateRange: "7d",
  reloadFlag: 1
});

// 共享的数据上下文
const shareDataContext = reactive({
  alarmCount: 0
});

const triggerReload = () => {
  ++globalQuery.reloadFlag;
};

onMounted(() => {
  // 1分钟刷新一次
  state.timer = setInterval(() => {
    // selectNextDept();
    triggerReload();
  }, 300000) as any;
});

// 停止定时器
onBeforeUnmount(() => {
  clearInterval(state.timer);
});
provide("shareDataContext", shareDataContext);
provide("globalQuery", globalQuery);
</script>
<style lang="scss">
@import "../css/large-screen-popper";
</style>
<style lang="scss" scoped>
.security-situation-nanchang {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  .asset-situation-bloc-tr {
    width: 560px;
    margin-top: 15px;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .label {
      width: 70px;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #d1e4ff;
      line-height: 14px;
    }
  }
  :deep(.right-tabs) {
    position: absolute;
    width: 160px;
    height: 16px;
    gap: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    .tab {
      width: 76px;
      height: 29px;
      text-align: right;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: rgba(184, 209, 255, 0.8);
      opacity: 0.8;
      line-height: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      &.selected {
        opacity: 1;
        color: var(--el-color-primary);
      }
    }
  }
}
</style>
