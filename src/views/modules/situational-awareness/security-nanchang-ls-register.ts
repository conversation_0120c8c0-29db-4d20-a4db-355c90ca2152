import { registerComponent, ScreenComponent } from "@/views/modules/ddls";
import SecurityProtectionSituation from "@/views/modules/situational-awareness/security-situation-nanchang/components/LeftSecurityProtectionSituation.vue";
import DataSecuritySituation from "@/views/modules/situational-awareness/security-situation-nanchang/components/RightDataSecuritySituation.vue";
import RealtimeDataMonitoring from "@/views/modules/situational-awareness/security-situation-nanchang/components/BottomRealtimeDataMonitoring.vue";
import SecSituationWorldMap from "@/views/modules/situational-awareness/security-situation-nanchang/components/CenterSecSituationWorldMap.vue";
registerComponent({
  name: "安全防护情况",
  key: "SecurityProtectionSituation",
  group: "安全态势-nc",
  component: SecurityProtectionSituation
} as ScreenComponent);

registerComponent({
  name: "数据安全情况",
  key: "DataSecuritySituation",
  group: "安全态势-nc",
  component: DataSecuritySituation
} as ScreenComponent);

registerComponent({
  name: "实时数据监测",
  key: "RealtimeDataMonitoring",
  group: "安全态势-nc",
  component: RealtimeDataMonitoring
} as ScreenComponent);

registerComponent({
  name: "中间-攻击地图",
  key: "SecSituationWorldMap",
  group: "安全态势-nc",
  component: SecSituationWorldMap
} as ScreenComponent);
