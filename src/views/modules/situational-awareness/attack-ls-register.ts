import { registerComponent, ScreenComponent } from "@/views/modules/ddls";
import BlockageOverview from "@/views/modules/situational-awareness/attack-situation/components/BlockageOverview.vue";
import RankingAttackDistribution from "@/views/modules/situational-awareness/attack-situation/components/RankingAttackDistribution.vue";
import RankingAttackedDepts from "@/views/modules/situational-awareness/attack-situation/components/RankingAttackedDepts.vue";
import AttachAlarmTimeline from "@/views/modules/situational-awareness/attack-situation/components/AttachAlarmTimeline.vue";
import AttackSituationWorldMap from "@/views/modules/situational-awareness/attack-situation/components/AttackSituationWorldMap.vue";
registerComponent({
  name: "封堵概览",
  key: "BlockageOverview",
  group: "攻击态势",
  component: BlockageOverview
} as ScreenComponent);

registerComponent({
  name: "受攻击组织排名",
  key: "RankingAttackedDistricts",
  group: "攻击态势",
  component: RankingAttackedDepts
} as ScreenComponent);

registerComponent({
  name: "攻击来源地分布排名",
  key: "RankingAttackDistribution",
  group: "攻击态势",
  component: RankingAttackDistribution
} as ScreenComponent);

registerComponent({
  name: "攻击告警时间轴",
  key: "AttachAlarmTimeline",
  group: "攻击态势",
  component: AttachAlarmTimeline
} as ScreenComponent);

registerComponent({
  name: "攻击态势-世界地图",
  key: "AttackSituationWorldMap",
  group: "攻击态势",
  component: AttackSituationWorldMap
} as ScreenComponent);
