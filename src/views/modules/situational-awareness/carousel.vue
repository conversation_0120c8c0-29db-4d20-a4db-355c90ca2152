<template>
  <div
    class="carousel-screen"
    @mousemove="showControls = true"
    @mouseleave="showControls = false"
    @mouseenter="showControls = true"
  >
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition :name="`${transitionName}`">
      <!-- 动态组件，根据currentComponent的值来决定渲染哪个组件 -->
      <KeepAlive :include="state.cachedScreenNames">
        <component :is="screen" />
      </KeepAlive>
    </transition>
    <div v-show="showControls" class="carousel-controls">
      <div class="indicators">
        <span
          v-for="(_, index) in carouselScreens"
          :key="index"
          :title="_.label"
          :class="{ active: screenIndex === index }"
          @click="jumpTo(index)"
        ></span>
      </div>

      <el-dropdown
        trigger="hover"
        :teleported="false"
        popper-class="carousel-interval-popper"
        @command="changeInterval"
      >
        <el-icon class="carousel-setting" title="轮播间隔">
          <Setting></Setting>
        </el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="(intervalOption, key) in state.intervalOptions"
              :key="key"
              :command="intervalOption.value"
              ><span :class="{ current: intervalOption.value == interval }">{{
                intervalOption.label
              }}</span></el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  nextTick,
  onBeforeMount,
  onBeforeUnmount,
  reactive,
  toRefs
} from "vue";
import { Setting } from "@element-plus/icons-vue";
import SecuritySituation from "./security-situation/index.vue";
import AssetSituation from "./asset-situation/bloc.vue";
const localIntervalKey = "carousel-interval";
//数据对象
const state = reactive({
  showControls: true,

  // 当前显示的组件
  timer: -1,
  interval: 15,
  screen: null,
  screenIndex: 0,
  transitionName: "carousel-right",
  // 需要缓存的大屏名称(name)列表
  cachedScreenNames: ["security_situation_bloc", "asset_situation_bloc"],
  carouselScreens: [
    {
      label: "综合安全态势",
      screen: SecuritySituation
    },
    {
      label: "资产态势",
      screen: AssetSituation
    }
  ],
  intervalOptions: [
    { label: "5秒", value: 5 },
    { label: "10秒", value: 10 },
    { label: "15秒", value: 15 },
    { label: "30秒", value: 30 },
    { label: "45秒", value: 45 },
    { label: "60秒", value: 60 },
    { label: "停止", value: -1 }
  ]
});
const {
  showControls,
  screen,
  screenIndex,
  transitionName,
  carouselScreens,
  interval
} = toRefs(state);

const switchScreen = () => {
  let index = (screenIndex.value + 1) % carouselScreens.value.length;
  screen.value = carouselScreens.value[index].screen;
  screenIndex.value = index;
  transitionName.value = "carousel-left";
  setTimeout(() => {
    showControls.value = true;
  }, 1000);
};

const jumpTo = index => {
  transitionName.value =
    index > screenIndex.value ? "carousel-left" : "carousel-right";
  screenIndex.value = index;
  screen.value = carouselScreens.value[index].screen;
  resetTimer();
};

const resetTimer = () => {
  clearInterval(state.timer);
  if (interval.value > 0) {
    state.timer = setInterval(switchScreen, interval.value * 1000) as any;
  }
};

const changeInterval = command => {
  interval.value = command;
  resetTimer();
  localStorage.setItem(localIntervalKey, command);
};

const initInterval = () => {
  try {
    let localInterval = localStorage.getItem(localIntervalKey);
    if (localInterval) {
      interval.value = parseInt(localInterval);
    }
  } catch (e) {
    interval.value = 15;
  }
};

onBeforeMount(() => {
  screen.value = SecuritySituation;
  initInterval();
  resetTimer();
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
});
</script>

<style lang="scss">
.carousel-screen {
  .carousel-left-enter-active,
  .carousel-left-leave-active,
  .carousel-right-enter-active,
  .carousel-right-leave-active {
    transition: all 0.7s ease-in-out;
    position: absolute;
    width: 100%;
    top: 0;
  }

  /* 向左滑动修正 */
  .carousel-left-enter-from {
    transform: translateX(100%);
  }
  .carousel-left-leave-to {
    transform: translateX(-100%);
  }

  /* 向右滑动修正 */
  .carousel-right-enter-from {
    transform: translateX(-100%);
  }
  .carousel-right-leave-to {
    transform: translateX(100%);
  }
}
</style>

<style scoped lang="scss">
.carousel-screen {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  .carousel-controls {
    position: fixed;
    bottom: 2px;
    left: 50%;
    min-width: 120px;
    width: auto;
    height: 30px;
    transform: translateX(-50%);
    z-index: 1000;
    background: rgba(29, 29, 29, 0.5);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    .indicators {
      display: flex;
      gap: 10px;
      span {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.75);
        cursor: pointer;
        &.active {
          background: var(--el-color-primary);
        }
      }
    }
    .carousel-setting {
      margin-left: 20px;
      font-size: 1.1em;
      color: var(--el-color-primary);
      cursor: pointer;
    }
    :deep(.carousel-interval-popper) {
      border: unset !important;
      background: #000;
      padding: 2px;
      .el-popper__arrow {
        display: none;
      }
      ul {
        background: #000;
        outline: unset !important;
        gap: 2px;
        li {
          color: rgba(255, 255, 255, 0.8) !important;
          padding-left: 12px;
          margin: 4px 0;
          width: 75px;
          &:hover {
            background: #000;
            color: var(--el-color-primary) !important;
          }
          span.current {
            font-weight: bold;
            color: var(--el-color-primary) !important;
          }
        }
      }
    }
  }
}
</style>
