import { registerComponent, ScreenComponent } from "@/views/modules/ddls";
import VulLevelDistribution from "@/views/modules/situational-awareness/security-situation/components/VulLevelDistribution.vue";
import HighRiskVulDistrbution from "@/views/modules/situational-awareness/security-situation/components/HighRiskVulDistrbution.vue";
import InfoSystemVulTop5 from "@/views/modules/situational-awareness/security-situation/components/InfoSystemVulTop5.vue";
import EventAlarmTrend from "@/views/modules/situational-awareness/security-situation/components/EventAlarmTrend.vue";
import AlarmTypeTOP10 from "@/views/modules/situational-awareness/security-situation/components/AlarmTypeTOP10.vue";
import AssetHighRiskEventTOP5 from "@/views/modules/situational-awareness/security-situation/components/AssetHighRiskEventTOP5.vue";
import SecSitRealtimeAlarm from "@/views/modules/situational-awareness/security-situation/components/SecSitRealtimeAlarm.vue";
import SecuritySituationCenter from "@/views/modules/situational-awareness/security-situation/components/SecuritySituationCenter.vue";
registerComponent({
  name: "漏洞等级分布情况",
  key: "VulLevelDistribution",
  group: "安全态势",
  component: VulLevelDistribution
} as ScreenComponent);

registerComponent({
  name: "高危漏洞分布情况",
  key: "HighRiskVulDistrbution",
  group: "安全态势",
  component: HighRiskVulDistrbution
} as ScreenComponent);

registerComponent({
  name: "信息系统漏洞-TOP5",
  key: "InfoSystemVulTop5",
  group: "安全态势",
  component: InfoSystemVulTop5
} as ScreenComponent);

registerComponent({
  name: "事件告警趋势（近7日）",
  key: "EventAlarmTrend",
  group: "安全态势",
  component: EventAlarmTrend
} as ScreenComponent);

registerComponent({
  name: "告警类型-TOP10",
  key: "AlarmTypeTOP10",
  group: "安全态势",
  component: AlarmTypeTOP10
} as ScreenComponent);

registerComponent({
  name: "资产高危事件-TOP5",
  key: "AssetHighRiskEventTOP5",
  group: "安全态势",
  component: AssetHighRiskEventTOP5
} as ScreenComponent);

registerComponent({
  name: "实时事件监测",
  key: "SecSitRealtimeAlarm",
  group: "安全态势",
  component: SecSitRealtimeAlarm
} as ScreenComponent);

registerComponent({
  name: "安全态势-中心",
  key: "SecuritySituationCenter",
  group: "安全态势",
  component: SecuritySituationCenter
} as ScreenComponent);
