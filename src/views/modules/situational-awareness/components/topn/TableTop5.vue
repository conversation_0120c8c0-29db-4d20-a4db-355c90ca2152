<template>
  <div class="table-top5">
    <div v-if="!hiddenHeader" class="col-row">
      <div
        class="col-item"
        v-for="(column, index) in columns"
        :class="{
          'topn-col': index == 0,
          'label-col': index == 1,
          'count-col': index == 2
        }"
        :key="index"
      >
        {{ column.label }}
      </div>
    </div>

    <div class="data-rows">
      <div class="data-row" v-for="(item, index) in data || []" :key="index">
        <div v-if="!hiddenTopCol" class="topn-col data-item">
          <div :class="['topn-bg', 'topn-bg-' + index]" :style="bgStyle(index)">
            {{ index + 1 }}
          </div>
        </div>
        <div class="label-col bar-col data-item">
          <div class="label" :title="item.label">{{ item.label }}</div>
          <div class="bar-wrap" :style="barWrapStyle">
            <div class="bar" :style="barItemStyle()">
              <div
                class="bar-percent"
                :class="['bar-percent-' + index]"
                :style="barPercentStyle(item)"
              ></div>
            </div>
            <div class="end-rect" :style="endRectStyle(item)"></div>
          </div>
        </div>
        <div class="count-col data-item flex items-center">
          {{ item.value }}
        </div>
      </div>
    </div>

    <div class="empty-desc" v-if="(data || []).length == 0">暂无数据</div>
  </div>
</template>
<script setup lang="ts">
import { PropType, CSSProperties, computed } from "vue";
import TOP5_bg from "./TOP5_bg.png";
defineProps({
  hiddenHeader: Boolean,
  hiddenTopCol: Boolean,

  columns: Array as PropType<any[]>,
  data: {
    type: Array as PropType<any[]>,
    default: () => {
      return [];
    }
  }
});

const barWrapStyle = computed((): CSSProperties => {
  return {
    height: `10px`
  };
});

const bgStyle = (index): CSSProperties => {
  if (index > 4) {
    return {
      backgroundImage: `url("${TOP5_bg}")`
    };
  }
  return {};
};

const barItemStyle = (): CSSProperties => {
  return {
    height: `6px`
  };
};
const barPercentStyle = (item): CSSProperties => {
  if (typeof item.percent == "string" && item.percent.indexOf("%") > -1) {
    return {
      width: item.percent
    };
  }
  return {
    width: `${Math.min(item.percent || 0, 100)}%`
  };
};
const endRectStyle = (item): CSSProperties => {
  if (typeof item.percent == "string" && item.percent.indexOf("%") > -1) {
    return {
      left: item.percent
    };
  }
  return {
    left: `${Math.min(item.percent || 0, 100)}%`
  };
};
</script>
<style scoped lang="scss">
.table-top5 {
  width: 100%;
  min-height: 265px;
  display: flex;
  flex-direction: column;
  .col-row {
    display: flex;
    margin: 10px 0 10px 0;
    .col-item {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #b1c7d6;
      line-height: 14px;
      display: flex;
      align-items: center;
    }
  }
  .data-rows {
    max-height: 220px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    &::-webkit-scrollbar-track {
      display: none;
    }
  }
  .data-row {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    &:last-child {
      margin-bottom: 0;
    }
    .count-col {
      background-image: url("./valueBg.png");
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 16px;
      color: #ffffff;
      line-height: 16px;
      padding-left: 12px;
    }
    .data-item {
      display: flex;
    }
    .bar-col {
      display: flex;
      flex-direction: column;
      align-items: flex-start !important;
      justify-content: space-between;
      .label {
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
        line-height: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }
      .bar-wrap {
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        .bar {
          width: 100%;
          background: rgba(0, 149, 255, 0.1);
          .bar-percent {
            background: linear-gradient(90deg, #0066ff 0%, #00aaff 100%);
            height: 100%;
            &.bar-percent-0 {
              background: linear-gradient(
                270deg,
                #ff3838 0%,
                rgba(255, 56, 56, 0) 100%
              );
            }
            &.bar-percent-1 {
              background: linear-gradient(
                270deg,
                #ff7716 0%,
                rgba(255, 119, 22, 0) 100%
              );
            }
            &.bar-percent-2 {
              background: linear-gradient(
                270deg,
                #e4c513 0%,
                rgba(228, 197, 19, 0) 100%
              );
            }
            &.bar-percent-3 {
              background: linear-gradient(
                270deg,
                #0095ff 0%,
                rgba(0, 149, 255, 0) 100%
              );
            }
            &.bar-percent-4 {
              background: linear-gradient(
                270deg,
                #0095ff 0%,
                rgba(0, 149, 255, 0) 100%
              );
            }
          }
        }
        .end-rect {
          width: 4px;
          position: absolute;
          background: #fff;
          height: 100%;
        }
      }
    }
  }
  .topn-col {
    width: 60px;
    height: 26px;
    justify-content: center;
  }
  .label-col {
    width: calc(100% - 136px);
    height: 26px;
    padding: 0 10px 0 20px;
  }
  .count-col {
    width: 76px;
    height: 26px;
  }
  .topn-bg {
    margin-top: 5px;
    width: 60px;
    height: 21px;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    color: #e3e9f3;
    line-height: 14px;
    display: flex;
    align-items: center;
    //padding-left: 20px;
    justify-content: center;
  }
  .topn-bg-0 {
    background-image: url("./TOP1_bg.png");
  }
  .topn-bg-1 {
    background-image: url("./TOP2_bg.png");
  }
  .topn-bg-2 {
    background-image: url("./TOP3_bg.png");
  }
  .topn-bg-3 {
    background-image: url("./TOP4_bg.png");
  }
  .topn-bg-4 {
    background-image: url("./TOP5_bg.png");
  }

  .empty-desc {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 40px;
    color: #d1e4ff;
    opacity: 0.5;
  }
}
</style>
