<template>
  <div class="bar-topn">
    <div
      class="top-item"
      v-for="(item, index) in (data || []).slice(0, 10)"
      :key="index"
    >
      <div class="item-wrap">
        <div class="top-col">
          <img :src="topImgs[index]" />
          <span class="label">{{ item.label }}</span>
          <span class="value">{{ item.value }}</span>
        </div>
        <div class="bar-col">
          <div class="bar-wrap" :style="barWrapStyle">
            <div class="bar" :style="barItemStyle()">
              <div
                class="bar-percent"
                :class="['bar-percent-' + (index > 4 ? 4 : index)]"
                :style="barPercentStyle(item)"
              ></div>
            </div>
            <div class="end-rect" :style="endRectStyle(item)"></div>
          </div>
        </div>
      </div>
      <slot name="bar-append"></slot>
    </div>
    <div class="empty-desc" v-if="(data || []).length == 0">暂无数据</div>
  </div>
</template>
<script setup lang="ts">
import { PropType, CSSProperties, computed } from "vue";
defineProps({
  data: {
    type: Array as PropType<any[]>,
    default: () => {
      return [];
    }
  }
});
import p01 from "./barn/01.png";
import p02 from "./barn/02.png";
import p03 from "./barn/03.png";
import p04 from "./barn/04.png";
import p05 from "./barn/05.png";
import p06 from "./barn/06.png";
import p07 from "./barn/07.png";
import p08 from "./barn/08.png";
import p09 from "./barn/09.png";
import p10 from "./barn/10.png";

const topImgs = [p01, p02, p03, p04, p05, p06, p07, p08, p09, p10];
const barWrapStyle = computed((): CSSProperties => {
  return {
    height: `10px`
  };
});
const barItemStyle = (): CSSProperties => {
  return {
    height: `4px`
  };
};
const barPercentStyle = (item): CSSProperties => {
  return {
    width: `${Math.min(item.percent || 0, 100)}%`
  };
};
const endRectStyle = (item): CSSProperties => {
  return {
    left: `${Math.min(item.percent || 0, 100)}%`
  };
};
</script>
<style scoped lang="scss">
.bar-topn {
  width: 100%;
  display: flex;
  flex-direction: column;
  .top-item {
    display: flex;
    align-items: center;
    .item-wrap {
      width: 100%;
      display: flex;
      flex-direction: column;
      .top-col {
        display: flex;
        .label {
          margin-left: 8px;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          line-height: 14px;
        }
        .value {
          flex: 1;
          text-align: right;
          font-family: DIN-Medium;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
          line-height: 14px;
        }
      }
      .bar-col {
        width: 100%;
        height: 26px;
        display: flex;
        flex-direction: column;
        align-items: flex-start !important;
        justify-content: space-between;
        .bar-wrap {
          width: 100%;
          position: relative;
          display: flex;
          align-items: center;
          margin-top: 4px;
          .bar {
            width: 100%;
            background: rgba(0, 149, 255, 0.1);
            .bar-percent {
              background: linear-gradient(90deg, #0066ff 0%, #00aaff 100%);
              height: 100%;
              &.bar-percent-0 {
                background: linear-gradient(
                  270deg,
                  #ff3838 0%,
                  rgba(255, 56, 56, 0) 100%
                );
              }
              &.bar-percent-1 {
                background: linear-gradient(
                  270deg,
                  #ff7716 0%,
                  rgba(255, 119, 22, 0) 100%
                );
              }
              &.bar-percent-2 {
                background: linear-gradient(
                  270deg,
                  #e4c513 0%,
                  rgba(228, 197, 19, 0) 100%
                );
              }
              &.bar-percent-3 {
                background: linear-gradient(
                  270deg,
                  #0095ff 0%,
                  rgba(0, 149, 255, 0) 100%
                );
              }
              &.bar-percent-4 {
                background: linear-gradient(
                  270deg,
                  #0095ff 0%,
                  rgba(0, 149, 255, 0) 100%
                );
              }
            }
          }
          .end-rect {
            width: 4px;
            position: absolute;
            background: #fff;
            height: 100%;
          }
        }
      }
    }
  }
  .empty-desc {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 40px;
    color: #d1e4ff;
    opacity: 0.5;
  }
}
</style>
