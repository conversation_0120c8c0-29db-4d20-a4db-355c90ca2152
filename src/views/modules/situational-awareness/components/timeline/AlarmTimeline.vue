<template>
  <div ref="scrollDomRef" class="alarm-event-timeline">
    <div v-if="!eventList || eventList.length == 0">
      <span style="color: #a5afba; padding: 20px">暂无数据</span>
    </div>
    <template v-else>
      <div class="dashed-line" :style="dashedStyle"></div>
      <div class="event-timeline-items">
        <div
          class="event-timeline-item"
          v-for="(eventData, index) in eventList"
          :key="index"
        >
          <div class="timeline-title">
            <img src="../../assets/timeline-point.png" width="18" />
            <div class="time-label">{{ eventData.last_time }}</div>
          </div>
          <div class="timeline-content">
            <div
              class="event-level"
              :class="{
                'event-level-highrisk': eventData.reseverity == '5',
                'event-level-critical': eventData.reseverity == '4'
              }"
            >
              <span v-if="eventData.reseverity == '5'">[ 严重 ]</span>
              <span v-else>[ 高危 ]</span>
            </div>
            <div class="event-type">
              <span :title="eventData.event_name || ''">
                {{ eventData.event_name || "" }}
              </span>
            </div>
            <div>
              <span class="prop-label">告警类型：</span>
              <span class="prop-value">{{
                eventData.event_subtype_name || ""
              }}</span>
            </div>
            <div>
              <span class="prop-label">告警来源：</span>
              <span class="prop-value">{{ eventData.event_agent || "" }}</span>
            </div>
            <div>
              <span class="prop-label">资产归属：</span>
              <span class="prop-value">{{
                eventData.dst_org_name || "互联网"
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref
} from "vue";
const props = defineProps({
  delays: {
    type: Number,
    default: 1
  },
  eventList: {
    type: Array as PropType<any[]>,
    default: () => {
      return [];
    }
  }
});
const scrollDomRef = ref<HTMLDivElement>();
const state = reactive({
  scrollTimer: null,
  scrollUp: true,
  beginAt: null,
  scrollDom: null
});

const eventCount = computed(() => {
  return props.eventList ? props.eventList.length : 0;
});
const dashedStyle = computed((): CSSProperties => {
  return {
    height: `${eventCount.value * 175}px`
  };
});

const stopTimer = () => {
  if (state.scrollTimer) {
    clearInterval(state.scrollTimer);
    state.scrollTimer = null;
  }
};

const scrollData = () => {
  // 延迟滚动逻辑
  if (props.delays > 0 && Date.now() - state.beginAt < props.delays * 1000) {
    return;
  }
  if (!state.scrollUp || !state.scrollDom) return;
  let domScrollTop = state.scrollDom.scrollTop;
  let lastScrollTop = parseInt(domScrollTop + 0.5); // 四舍五入
  let scrollTop = parseInt(lastScrollTop + 1);
  state.scrollDom.scrollTop = scrollTop;
  if (state.scrollDom.scrollTop < scrollTop) {
    state.scrollDom.scrollTop = scrollTop + 1;
  }
  if (state.scrollDom.scrollTop == domScrollTop) {
    state.scrollDom.scrollTop = 0;
  }
};

onMounted(() => {
  state.scrollTimer = setInterval(scrollData, 60);
  setTimeout(() => {
    state.beginAt = new Date();
    let scrollDom = scrollDomRef.value;
    scrollDom.onmouseenter = () => {
      state.scrollUp = false;
    };
    scrollDom.onmouseleave = () => {
      state.scrollUp = true;
    };
    state.scrollDom = scrollDom;
  }, 100);
});

onBeforeUnmount(() => {
  stopTimer();
});
</script>
<style lang="scss" scoped>
.alarm-event-timeline {
  margin-top: 1%;
  height: 100%;
  /**设置宽度可巧妙的隐藏滚动条*/
  width: 200%;
  overflow: auto;
  overflow-x: hidden;
  position: relative;
  //&::-webkit-scrollbar {
  //  display: none;
  //}
  //&::-webkit-scrollbar-track-piece {
  //  display: none;
  //}
  .dashed-line {
    position: absolute;
    top: 5px;
    left: 9px;
    width: 0;
    border-left: 1px dashed #0783fa;
    opacity: 0.6;
    z-index: 1;
  }

  .event-timeline-items {
    position: absolute;
    z-index: 2;
  }

  .event-timeline-item {
    height: 215px;
  }

  .timeline-title {
    display: flex;
    align-items: center;

    .time-label {
      background: url("../../assets/timetext-bg.png") no-repeat;
      background-size: 365px 36px;
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      line-height: 21px;
      width: 420px;
      height: 36px;
      display: flex;
      align-items: center;
      padding-left: 20px;
    }
  }

  .timeline-content {
    //border: 1px dashed #3d73ff;
    background: linear-gradient(
      270deg,
      rgba(4, 25, 40, 0.4) 0%,
      rgba(4, 25, 40, 0.8) 100%
    );
    width: 356px;
    height: 160px;
    margin-left: 26px;
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    padding: 20px 10px;

    .event-level {
      font-family: MiSans, MiSans;
      font-weight: 500;
      font-size: 18px;
      line-height: 18px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .event-level-highrisk {
      color: #d92b2b;
    }

    .event-level-critical {
      color: #fd8745;
    }

    .event-type {
      margin: 20px 0 10px 0;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 14px;
      span {
        display: inline-block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .prop-label {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #d1e4ff;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .prop-value {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #fff;
      line-height: 14px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
}
</style>
