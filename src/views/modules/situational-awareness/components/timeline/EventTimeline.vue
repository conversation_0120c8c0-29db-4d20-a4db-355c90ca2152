<template>
  <div ref="scrollDom" class="event-timeline">
    <div class="empty-desc" v-if="!eventList || eventList.length == 0">
      <span style="color: #a5afba; padding: 20px">暂无数据</span>
    </div>
    <template v-else>
      <div class="dashed-line" :style="dashedStyle" />
      <div class="event-timeline-items">
        <div
          class="event-timeline-item"
          v-for="(eventData, index) in eventList"
          :key="index"
        >
          <div style="display: flex">
            <div style="flex: 1">
              <div
                class="timeline-title"
                :class="['level-' + eventData.reseverity]"
              >
                <div class="circle"></div>
                <div class="title">
                  <span class="date">
                    {{ eventData.update_time }}
                  </span>
                  <span class="level-text"
                    >【{{ getLevelText(eventData.reseverity) }}】</span
                  >
                  <!--                  <span class="desc"-->
                  <!--                    >10.12.6.208（OA管理系统-本地-陈工）黑客工具-代理工具</span-->
                  <!--                  >-->
                </div>
              </div>
              <div class="desc">
                攻击者<span class="label">{{ eventData.src_asset_name }}</span
                >，通过<span class="label">{{ eventData.event_name }}</span
                >方式，对受害者
                <span class="label">{{ eventData.dst_asset_name }}</span
                >发起<span class="label">{{ eventData.event_cnt }}</span>
                次攻击
              </div>
              <!--              <div class="timeline-content">-->
              <!--                <div class="content">-->
              <!--                  <span class="label">{{ eventData.update_time }}</span> 攻击者-->
              <!--                  <span class="label">{{ eventData.src_asset_name }}</span-->
              <!--                  >，通过<span class="label">{{ eventData.attack_type }}</span>-->
              <!--                  方式，对受害者-->
              <!--                  <span class="label">{{ eventData.dst_asset_name }}}</span>-->
              <!--                  发起-->
              <!--                  <span class="label">{{ eventData.event_cnt }}</span> 次攻击-->
              <!--                </div>-->
              <!--              </div>-->
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  onActivated,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  toRefs
} from "vue";
import { onDeactivated } from "vue";
const props = defineProps({
  delays: {
    type: Number,
    default: 2
  },
  eventList: Array as PropType<any[]>
});

const state = reactive({
  offset: 0
});

const { delays, eventList } = toRefs(props);
const scrollUp = ref(true);
// 虚拟滚动设计，最多显示100条
// const scrollEventList = computed(() => {
//   let allList = eventList.value || [];
//   return allList.slice(state.offset, state.offset + 100);
// });
const eventCount = computed(() => {
  return eventList.value ? eventList.value.length : 0;
});
const dashedStyle = computed(() => {
  return {
    height: `${eventCount.value * 60}px`
  };
});

const getLevelText = reseverity => {
  if (reseverity == "5") {
    return "严重";
  } else if (reseverity == "4") {
    return "高危";
  } else if (reseverity == "3") {
    return "中危";
  } else if (reseverity == "2") {
    return "低危";
  } else {
    return "提示";
  }
};

const scrollTimer = ref<any>(0);
const scrollDom = ref(null);
const beginAt = ref<Date>(new Date());
const stopTimer = () => {
  if (scrollTimer.value) {
    clearInterval(scrollTimer.value);
    scrollTimer.value = 0;
  }
};
const scrollData = () => {
  // 延迟滚动逻辑
  if (
    delays.value > 0 &&
    Date.now() - beginAt.value.getTime() < delays.value * 1000
  ) {
    return;
  }
  if (eventCount.value < 4) {
    return;
  }
  if (!scrollUp.value) return;
  const domScrollTop = scrollDom.value.scrollTop;
  const lastScrollTop = parseInt(domScrollTop + 0.5); // 四舍五入
  const scrollTop = parseInt(lastScrollTop + 1);
  scrollDom.value.scrollTop = scrollTop;
  if (scrollDom.value.scrollTop < scrollTop) {
    scrollDom.value.scrollTop = scrollTop + 1;
  }
  if (scrollDom.value.scrollTop == domScrollTop) {
    scrollDom.value.scrollTop = 0;
  }
};

onMounted(() => {
  scrollTimer.value = setInterval(scrollData, 60);
  beginAt.value = new Date();
  scrollDom.value.onmouseenter = () => {
    scrollUp.value = false;
  };
  scrollDom.value.onmouseleave = () => {
    scrollUp.value = true;
  };
});

onActivated(() => {
  scrollUp.value = true;
});
onDeactivated(() => {
  scrollUp.value = false;
});
onBeforeUnmount(() => {
  stopTimer();
});
</script>
<style lang="scss" scoped>
.event-timeline {
  margin-top: 1%;
  height: 100%;
  /**设置宽度可巧妙的隐藏滚动条*/
  //width: 200%;
  overflow: auto;
  overflow-x: hidden;
  position: relative;
  &::-webkit-scrollbar {
    display: none;
  }
  &::-webkit-scrollbar-track-piece {
    display: none;
  }
  .dashed-line {
    position: absolute;
    //top: 5px;
    left: 4px;
    width: 0;
    border-left: 1px dashed #0783fa;
    opacity: 0.6;
    z-index: 1;
  }

  .event-timeline-items {
    position: absolute;
    z-index: 2;
  }

  .event-timeline-item {
    min-height: 60px;
  }

  .time-label {
    padding-top: 6px;
    font-family: MiSans, MiSans;
    font-weight: 300;
    font-size: 14px;
    color: #ffffff;
    line-height: 14px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    opacity: 0.8;
  }

  .timeline-title {
    &.level-5 {
      .circle {
        background-color: #aa1c1c;
      }
      .level-text {
        color: #aa1c1c;
      }
    }
    &.level-4 {
      .circle {
        background-color: #aa1c1c;
      }
      .level-text {
        color: #aa1c1c;
      }
    }
    &.level-3 {
      .circle {
        background-color: #ff7716;
      }
      .level-text {
        color: #ff7716;
      }
    }
    &.level-2 {
      .circle {
        background-color: #66e1df;
      }
      .level-text {
        color: #66e1df;
      }
    }
    &.level-1 {
      .circle {
        background-color: #fff;
      }
      .level-text {
        color: #fff;
      }
    }
    display: flex;
    align-items: center;
    gap: 4px;
    .circle {
      width: 8px;
      height: 8px;
      border-radius: 4px;
      background-color: #fff;
    }
    .level-text {
      color: #fff;
    }
    .date {
      margin-left: 10px;
      color: #d1e4ff;
    }
  }
  .desc {
    margin-left: 20px;
    margin-bottom: 8px;
    width: 100%;
    word-break: break-all;
    word-wrap: break-word;
    display: inline-block;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    .label {
      color: #d1e4ff;
      margin: 0 1px;
      font-size: 13px;
    }
  }

  .timeline-content {
    height: 36px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .content {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }
  }
  .empty-desc {
    padding-top: 36px;
    text-align: center;
    opacity: 0.5;
  }
}
</style>
