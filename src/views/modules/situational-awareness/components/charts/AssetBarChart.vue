<template>
  <div class="bar-chart" :style="mainStyle">
    <div v-if="items.length == 0" class="empty-desc">暂无数据</div>
    <template v-else>
      <template v-for="(item, index) in items" :key="index">
        <div>
          <slot name="item" v-bind="{ item }"></slot>
          <div class="item">
            <slot v-if="showLabel" name="label" v-bind="{ item }">
              <div class="label" style="width: 100px" :title="item.label">
                {{ item.label }}
              </div>
            </slot>
            <div class="bar-wrap" :style="barWrapStyle">
              <div class="bar" :style="barItemStyle()">
                <div class="bar-percent" :style="barPercentStyle(item)"></div>
              </div>
              <div class="end-rect" :style="endRectStyle(item)"></div>
            </div>
            <slot v-if="showValue" name="value" v-bind="{ item }">
              <div class="value-wrap">
                <div class="value">{{ item.value }}</div>
                <div v-if="showPercent" class="percent">
                  {{ item.percent }}%
                </div>
              </div>
            </slot>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed, CSSProperties, PropType } from "vue";
type ItemJustify = "space-evenly" | "space-around" | "space-between" | "unset";
const props = defineProps({
  items: Array,
  showLabel: Boolean,
  showValue: Boolean,
  showPercent: Boolean,
  barWrapHeight: Number,
  barHeight: Number,
  itemJustify: String as PropType<ItemJustify>
});

const items = computed(() => props.items || []);
const showValue = computed(() => props.showValue);
const showPercent = computed(() => props.showPercent);

const mainStyle = computed((): CSSProperties => {
  return {
    justifyContent: `${props.itemJustify}`
  };
});

const barItemStyle = (): CSSProperties => {
  return {
    height: `${props.barHeight || 8}px`
  };
};
const barPercentStyle = (item): CSSProperties => {
  return {
    width: `${Math.min(item.percent || 0, 100)}%`
  };
};
const endRectStyle = (item): CSSProperties => {
  return {
    left: `${Math.min(item.percent || 0, 100)}%`
  };
};

const barWrapStyle = computed((): CSSProperties => {
  return {
    height: `${props.barWrapHeight | 14}px`
  };
});
</script>

<style lang="scss" scoped>
.bar-chart {
  height: 100%;
  // padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  overflow: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  .item {
    display: flex;
    align-items: center;
    .label {
      border: 1px solid rgba(0, 145, 255, 0.2);
      background: #04184b;
      height: 26px;
      padding: 2px 10px;
      margin-right: 10px;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #00aaff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .bar-wrap {
      flex: 1;
      // border: 1px solid rgba(0, 145, 255, 0.2);
      // background: #182e62;
      position: relative;
      display: flex;
      align-items: center;
      .bar {
        width: 100%;
        background: rgba(0, 149, 255, 0.1);
        .bar-percent {
          background: linear-gradient(90deg, #0066ff 0%, #00aaff 100%);
          height: 100%;
        }
      }
      .end-rect {
        width: 4px;
        position: absolute;
        background: #fff;
        height: 100%;
      }
    }
    .value-wrap {
      margin-left: 10px;
      width: 100px;
      display: flex;
      align-items: center;
      font-family:
        Agency FB,
        Agency FB;
      font-weight: bold;
      font-size: 16px;
      color: #00aaff;
      line-height: 12px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      .value,
      .percent {
        flex: 1;
        text-align: center;
      }
    }
  }
  .empty-desc {
    color: #fff;
    opacity: 0.6;
    font-size: 12px;
    text-align: center;
  }
}
</style>
