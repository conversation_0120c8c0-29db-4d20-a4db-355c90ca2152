import { registerComponent, ScreenComponent } from "@/views/modules/ddls";
import AssetManageSituation from "@/views/modules/situational-awareness/asset-situation/components/AssetManageSituation.vue";
import AssetTypeDistribution from "@/views/modules/situational-awareness/asset-situation/components/AssetTypeDistribution.vue";
import AssetRegistrationStatus from "@/views/modules/situational-awareness/asset-situation/components/AssetRegistrationStatus.vue";
import AssetDistributionAntivirus from "@/views/modules/situational-awareness/asset-situation/components/AssetDistributionAntivirus.vue";
import AssetTrendAntivirus from "@/views/modules/situational-awareness/asset-situation/components/AssetTrendAntivirus.vue";
import AssetSituationCenter from "@/views/modules/situational-awareness/asset-situation/components/AssetSituationCenter.vue";
import AssetDiscoveredSituation from "@/views/modules/situational-awareness/asset-situation/components/AssetDiscoveredSituation.vue";
import AssetDeployAntivirus from "@/views/modules/situational-awareness/asset-situation/components/AssetDeployAntivirus.vue";
registerComponent({
  name: "资产管理情况",
  key: "AssetManageSituation",
  group: "资产态势",
  component: AssetManageSituation
} as ScreenComponent);

registerComponent({
  name: "资产类型分布",
  key: "AssetTypeDistribution",
  group: "资产态势",
  component: AssetTypeDistribution
} as ScreenComponent);

registerComponent({
  name: "资产登记情况",
  key: "AssetRegistrationStatus",
  group: "资产态势",
  component: AssetRegistrationStatus
} as ScreenComponent);

registerComponent({
  name: "防病毒部署分布情况",
  key: "AssetDistributionAntivirus",
  group: "资产态势",
  component: AssetDistributionAntivirus
} as ScreenComponent);

registerComponent({
  name: "防病毒部署趋势变化",
  key: "AssetTrendAntivirus",
  group: "资产态势",
  component: AssetTrendAntivirus
} as ScreenComponent);

registerComponent({
  name: "资产态势-中心",
  key: "AssetSituationCenter",
  group: "资产态势",
  component: AssetSituationCenter
} as ScreenComponent);

registerComponent({
  name: "近7天资产发现情况",
  key: "AssetDiscoveredSituation",
  group: "资产态势",
  component: AssetDiscoveredSituation
} as ScreenComponent);

registerComponent({
  name: "防病毒部署分布情况",
  key: "AssetDeployAntivirus",
  group: "资产态势",
  component: AssetDeployAntivirus
} as ScreenComponent);
