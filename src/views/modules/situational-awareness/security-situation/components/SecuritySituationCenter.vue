<template>
  <div class="SecuritySituationCenter">
    <!--    <el-switch v-model="state.animateDisabled"></el-switch>-->
    <div class="overview flex items-center justify-center">
      <div
        v-for="(item, index) in state.overviewData"
        class="flex items-center overview-item"
        :key="index"
      >
        <div class="flex flex-col items-center" style="margin-left: 16px">
          <span class="count" @click="handleDrillItem(item)">
            <digital-animate
              :value="item.value"
              :digit-value-style-fn="digitFn"
              :digit-style="{
                fontFamily: 'YouSheBiaoTiHei'
              }"
            ></digital-animate>
          </span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <div class="main">
      <div class="center-bg">
        <div class="light"></div>
      </div>
      <div class="score">{{ state.totalScore }}</div>
      <div class="bottom-circle2"></div>
      <div class="bottom-circle3"></div>
      <div class="bottom-circle4"></div>
      <div class="bottom-light"></div>

      <div class="items-wrap">
        <svg
          class="bottom-circle"
          width="716"
          height="191"
          viewBox="0 0 716 191"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            id="&#229;&#186;&#149;&#233;&#131;&#168;-&#229;&#164;&#167;&#229;&#156;&#134;&#231;&#142;&#175;-&#229;&#164;&#150;"
            d="M714.5 95.5C714.5 107.875 705.075 120.046 687.066 131.404C669.132 142.715 643.082 152.957 610.758 161.579C546.131 178.819 456.767 189.5 358 189.5C259.233 189.5 169.869 178.819 105.242 161.579C72.9182 152.957 46.8676 142.715 28.9336 131.404C10.9247 120.046 1.5 107.875 1.5 95.5C1.5 83.1245 10.9247 70.9539 28.9336 59.5958C46.8676 48.285 72.9182 38.0434 105.242 29.4206C169.869 12.1809 259.233 1.5 358 1.5C456.767 1.5 546.131 12.1809 610.758 29.4206C643.082 38.0434 669.132 48.285 687.066 59.5958C705.075 70.9539 714.5 83.1245 714.5 95.5Z"
            stroke="url(#paint0_linear_145_4892)"
            stroke-width="3"
          />
          <defs>
            <linearGradient
              id="paint0_linear_145_4892"
              x1="665.937"
              y1="-6.66555e-06"
              x2="660.945"
              y2="241.201"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#0066FF" stop-opacity="0.1" />
              <stop offset="0.5" stop-color="white" />
              <stop offset="1" stop-color="#0066FF" stop-opacity="0.1" />
            </linearGradient>
          </defs>
        </svg>
        <div
          class="score-item"
          v-for="(item, index) in state.items"
          :style="itemStyle(index, state.items.length)"
          :key="index"
        >
          <div class="item-row">
            <div class="label">{{ item.label }}：</div>
            <div class="value">{{ item.value }}</div>
          </div>
          <div class="item-row">
            <div class="label">扣除分数：</div>
            <div class="sub-score">{{ item.score }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  onBeforeUnmount,
  onMounted,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { baseComponentProps } from "@/views/modules/ddls/module";
import { DigitalAnimate } from "@/views/modules/ddls";
import {
  queryAffectAssetLabelCount,
  querySafeScore
} from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const isGroup = computed(() => {
  return queryParams.value.zoneName == "";
});
const digitFn = (val): CSSProperties => {
  //  处理字体YouSheBiaoTiHei数字1宽度问题
  if (val == "1") {
    return {
      width: "0.5em"
    };
  }
  return {};
};
const itemStyle = (index, total): CSSProperties => {
  const rate = (index * 100) / total;
  return {
    "--start-position": `${rate}%`
  };
};

const state = reactive({
  overviewData: [
    {
      label: "事件数",
      value: 0,
      routeQuery: {
        dateRange: "7d"
      },
      drillRoute: "security_deal"
    },
    {
      label: "漏洞数",
      value: 0,
      routeQuery: {
        dateRange: ""
      },
      drillRoute: "security_vulnerability_page"
    },
    {
      label: "影响资产数",
      value: 0,
      routeQuery: {
        label: "风险资产"
      },
      drillRoute: "est_eam_instance"
    }
  ],
  // 总分
  totalScore: 0.0,
  // 固定9个组件占位
  items: []
});

const setMockData = () => {
  let mockData = [
    { value: 3756, up: 485 },
    { value: 3547, up: 18 },
    { value: 3500, up: 25 }
  ];
  for (let i = 0; i < 3; ++i) {
    Object.assign(state.overviewData[i], mockData[i]);
  }
  state.items = [
    {
      label: "弱口令",
      value: 256,
      subScore: 1
    },
    {
      label: "系统漏洞",
      value: 256,
      subScore: 1
    },
    {
      label: "web漏洞",
      value: 256,
      subScore: 5
    },
    {
      label: "sql注入攻击",
      value: 256,
      subScore: 5
    },
    {
      label: "暴力破解",
      value: 256,
      subScore: 5
    },
    {
      label: "木马病毒",
      value: 256,
      subScore: 5
    },
    {
      label: "webshell",
      value: 256,
      subScore: 3
    }
  ];
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  querySafeScore({
    orgId: queryParams.value.zoneName
  }).then(res => {
    let data = res.data || {};
    // 统计数量
    state.overviewData[0].value = data.eventCount || 0;
    state.overviewData[1].value = data.vulCount || 0;
    // state.overviewData[2].value = data.assetCount || 0;
    // 总分
    state.totalScore = parseFloat(data.score || 0);
    state.items = (data.scoreInfo || []).map(item => {
      let { scoreName, value, score } = item;
      return {
        label: scoreName,
        value: value || "-",
        score: score || 0
      };
    });
  });

  // 单独查询影响资产数量
  queryAffectAssetLabelCount(queryParams.value.zoneName).then(res => {
    let { count = 0 } = res.data || {};
    state.overviewData[2].value = count || 0;
  });
};

const handleDrillItem = item => {
  console.log("drill call");

  let routeQuery = item.routeQuery;
  if (item.drillRoute == "est_eam_instance") {
    routeQuery = {
      ...routeQuery,
      zoneName: queryParams.value.zoneName
    };
  }
  $router.push({
    name: item.drillRoute,
    query: routeQuery
  });
};

onMounted(() => {});

onBeforeUnmount(() => {});

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style lang="scss" scoped>
.SecuritySituationCenter {
  width: 956px;
  height: 642px;
  .overview {
    height: 80px;
    .overview-item {
      width: 200px;
      justify-content: center;
    }
    .label {
      font-family: MiSans;
      font-weight: 400;
      font-size: 18px;
      color: #b1c7d6;
      line-height: 18px;
      margin: 9px 0 4px 0;
    }
    .count {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      line-height: 32px;
      text-shadow: 0px 3px 6px #1da5ff;
    }
  }
  .main {
    height: 560px;
    position: relative;
    .score {
      position: absolute;
      top: 170px;
      left: 50%;
      z-index: 1000;
      transform: translateX(-50%);
      font-family: DIN, DIN;
      font-weight: bold;
      font-size: 42px;
      color: #ffffff;
      line-height: 42px;
    }
    .center-bg {
      position: absolute;
      top: 0;
      left: 50%;
      z-index: 500;
      transform: translateX(-50%);
      background-image: url("../assets/center-bg.png");
      width: 348px;
      height: 362px;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      .light {
        background-image: url("../assets/light.png");
        width: 237px;
        height: 115px;
      }
    }
    .bottom-circle2 {
      position: absolute;
      top: 345px;
      left: 50%;
      z-index: 1000;
      transform: translateX(-50%);
      background-image: url("../assets/bottomCircle2.png");
      width: 262px;
      height: 48px;
    }
    .bottom-circle3 {
      position: absolute;
      top: 150px;
      left: 50%;
      z-index: 400;
      transform: translateX(-50%);
      background-image: url("../assets/bottomCircle3.png");
      width: 178px;
      height: 39px;
    }
    .bottom-circle4 {
      position: absolute;
      top: 336px;
      left: 50%;
      z-index: 1000;
      transform: translateX(-50%);
      background-image: url("../assets/bottomCircle4.png");
      width: 160px;
      height: 28px;
    }
    .bottom-light {
      position: absolute;
      background-image: url("../assets/bottomLight.png");
      width: 378px;
      height: 378px;
      top: 190px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 500;
    }
    .items-wrap {
      position: absolute;
      top: 320px;
      left: 50%;
      width: 716px;
      height: 191px;
      transform: translateX(-50%);
      .bottom-circle {
        position: absolute;
        width: 716px;
        height: 191px;
      }
      &:hover {
        .score-item {
          animation-play-state: paused;
        }
      }
      .score-item {
        background-image: url("../assets/itemContent.png");
        min-width: 141px;
        width: auto;
        height: 58px;
        padding-right: 20px;
        background-size: 100% 100%;
        padding-left: 18px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 6px;
        position: absolute;
        offset-path: path(
          "M714.5 95.5C714.5 107.875 705.075 120.046 687.066 131.404C669.132 142.715 643.082 152.957 610.758 161.579C546.131 178.819 456.767 189.5 358 189.5C259.233 189.5 169.869 178.819 105.242 161.579C72.9182 152.957 46.8676 142.715 28.9336 131.404C10.9247 120.046 1.5 107.875 1.5 95.5C1.5 83.1245 10.9247 70.9539 28.9336 59.5958C46.8676 48.285 72.9182 38.0434 105.242 29.4206C169.869 12.1809 259.233 1.5 358 1.5C456.767 1.5 546.131 12.1809 610.758 29.4206C643.082 38.0434 669.132 48.285 687.066 59.5958C705.075 70.9539 714.5 83.1245 714.5 95.5Z"
        );
        offset-rotate: 0deg;
        offset-distance: var(--start-position);
        animation: moveAlongPath 30s linear infinite;
        .item-row {
          display: flex;
          .label {
            white-space: nowrap;
            font-family: MiSans, MiSans;
            font-weight: 400;
            font-size: 12px;
            color: #d1e4ff;
            line-height: 14px;
          }
          .value {
            font-family: DIN, DIN;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 14px;
          }
          .sub-score {
            font-family: DIN, DIN;
            font-weight: 500;
            font-size: 14px;
            color: #ff3838;
            line-height: 14px;
          }
        }
      }
    }
  }
  @keyframes moveAlongPath {
    0% {
      offset-distance: var(--start-position);
    }
    100% {
      offset-distance: calc(var(--start-position) + 100%);
    }
  }
}
</style>
