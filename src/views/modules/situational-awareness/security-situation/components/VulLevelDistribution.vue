<template>
  <div class="VulLevelDistribution">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div class="content" :style="style">
      <div class="pie">
        <ChartComponent
          class="pie-chart"
          :option="state.option"
        ></ChartComponent>
        <div class="pie-bg">
          <span class="label">漏洞分布</span>
        </div>
      </div>
      <div class="legend">
        <div
          class="legend-item"
          v-for="(item, index) in state.items"
          :key="index"
        >
          <div class="left">
            <div
              class="circle"
              :style="{
                background: colors[index]
              }"
            ></div>
            <div class="label">{{ item.name }}</div>
          </div>
          <div class="right">
            <span class="value">{{ item.value }}</span>
            <el-divider direction="vertical"></el-divider>
            <span class="percent">{{ item.percent }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/security-situation/assets/image";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { TitleDrill } from "@/views/modules/ddls";
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { queryRiskSpread } from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const colors = ["#AA1C1C", "#FF7716", "#C4B130"];

const buildOption = data => {
  return <any>{
    color: colors,
    tooltip: {
      trigger: "item",
      confine: true
    },
    series: [
      {
        name: "漏洞等级分布",
        type: "pie",
        radius: ["75%", "95%"],
        padAngle: 2,
        itemStyle: {
          borderWidth: 2
        },
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: "center"
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};

const items = [
  {
    name: "危急",
    value: 0,
    percent: 0
  },
  {
    name: "高危",
    value: 0,
    percent: 0
  },
  {
    name: "中危",
    value: 0,
    percent: 0
  }
];
const names = items.map(item => item.name);
const state = reactive({
  items,
  option: buildOption(items)
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryRiskSpread({
    dateRange: "1y",
    orgId: queryParams.value.zoneName
  }).then(res => {
    let rows = res.data?.rows || [];
    // 计算总数
    let total = 0;
    let map = {};
    for (let row of rows) {
      let { count = 0, risk } = row;
      if (names.includes(risk)) {
        total += row.count;
        map[risk] = count;
      }
    }
    let items = state.items;
    for (let item of items) {
      let { name } = item;
      let count = map[name];
      item.value = count || 0;
      item.percent =
        total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
    }
    // update option
    state.option = buildOption(items);
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.VulLevelDistribution {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    .pie {
      width: 160px;
      height: 160px;
      position: relative;
      .pie-chart {
        position: absolute;
        z-index: 200;
      }
      .pie-bg {
        position: absolute;
        left: 0;
        top: 0;
        background-image: url("../assets/pie_bg.png");
        width: 160px;
        height: 160px;
        display: flex;
        justify-content: center;
        align-items: center;
        .label {
          font-family: MiSans, MiSans;
          font-weight: bold;
          font-size: 16px;
          color: #d1e4ff;
          opacity: 0.85;
        }
      }
    }
    .legend {
      width: 220px;
      height: 150px;
      display: flex;
      flex-direction: column;
      gap: 22px;
      .legend-item {
        background-image: url("../assets/velLevelRect.png");
        width: 220px;
        height: 32px;
        display: flex;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          padding-left: 10px;
          gap: 10px;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          .circle {
            width: 8px;
            height: 8px;
            border-radius: 4px;
          }
        }
        .right {
          padding-right: 10px;
          display: flex;
          align-items: center;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
