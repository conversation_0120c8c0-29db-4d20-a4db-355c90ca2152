<template>
  <div class="HighRiskVulDistrbution">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div class="content" :style="style">
      <TableTop5 :columns="computedColumns" :data="state.data"></TableTop5>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/security-situation/assets/image";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { TitleDrill } from "@/views/modules/ddls";
import TableTop5 from "@/views/modules/situational-awareness/components/topn/TableTop5.vue";
import {
  queryOrgTreeByVulTop,
  queryVulDetailByAssetTop
} from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const state = reactive({
  data: []
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const isGroup = computed(() => {
  return queryParams.value.zoneName == "";
});
const computedColumns = computed(() => {
  const label = isGroup.value ? "单位名称" : "资产名称";
  return [
    { label: "排名", prop: "" },
    {
      label: label,
      prop: "label"
    },
    {
      label: "高危漏洞数",
      prop: "value"
    }
  ];
});

const setMockData = () => {
  const labels = [
    "新一代产业园区",
    "国际商贸",
    "暨通系统",
    "智慧园区",
    "广电计量"
  ];
  state.data = Array.from(
    {
      length: 5
    },
    (_, index) => {
      return {
        label: labels[index],
        percent: parseInt((Math.random() * 1000) % 100),
        value: parseInt((Math.random() * 1000) % 1000)
      };
    }
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  // 集体视角下查询组织top5
  // 去掉top5限制
  if (isGroup.value) {
    queryOrgTreeByVulTop({
      highLevel: true
      // topN: 5
    }).then(res => {
      let rows = res.data || [];
      let total = 0;
      let data = [];
      for (let row of rows) {
        let { deptName: label, vulCount: value } = row;
        total += value;
        data.push({
          label,
          value
        });
      }
      for (let item of data) {
        item["percent"] =
          total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
      }
      state.data = data;
      console.log("total", total, data);
    });
  } else {
    // 子公司视角查询 - 信息系统top5模块（左下角）的的资产视角接口
    queryVulDetailByAssetTop({
      highLevel: true,
      topN: 5,
      orgId: queryParams.value.zoneName
    }).then(res => {
      let data = res.data || [];
      let assetData = [];
      let total = 0;
      for (let item of data) {
        let { ip, assetName, count = 0 } = item;
        total += count || 0;
        assetData.push({
          label: assetName || ip,
          value: count || 0
        });
      }
      for (let item of assetData) {
        item["percent"] =
          total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
      }
      state.data = assetData;
    });
  }
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);

// 集团和子公司切换时
watch(
  () => isGroup.value,
  () => {
    state.data = [];
  }
);
</script>
<style scoped lang="scss">
.HighRiskVulDistrbution {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
