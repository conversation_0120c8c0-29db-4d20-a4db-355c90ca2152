<template>
  <div class="InfoSystemVulTop5">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div v-if="isGroup" class="right-tabs">
      <div
        class="app tab"
        :class="{ selected: state.topViewAngle == 'app' }"
        @click="state.topViewAngle = 'app'"
      >
        应用视角
      </div>
      <div
        class="asset tab"
        :class="{ selected: state.topViewAngle == 'asset' }"
        @click="state.topViewAngle = 'asset'"
      >
        资产视角
      </div>
    </div>
    <div class="content" :style="style">
      <TableTop5 :columns="computedColumns" :data="topData"></TableTop5>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/security-situation/assets/image";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { TitleDrill } from "@/views/modules/ddls";
import TableTop5 from "@/views/modules/situational-awareness/components/topn/TableTop5.vue";
import {
  queryAssetAppByVul,
  queryVulDetailByAssetTop
} from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const isGroup = computed(() => {
  return queryParams.value.zoneName == "";
});

const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const state = reactive({
  appData: [],
  assetData: [],
  topViewAngle: "app"
});

const showAssetAngle = computed(() => {
  return isGroup.value && state.topViewAngle == "asset";
});

const computedColumns = computed(() => {
  let label = showAssetAngle.value ? "资产名称" : "应用名称";
  return [
    { label: "排名", prop: "" },
    {
      label: label,
      prop: "label"
    },
    {
      label: "漏洞数量",
      prop: "count"
    }
  ];
});

const topData = computed(() => {
  // 当集团且视角位资产视角时
  if (showAssetAngle.value) {
    return state.assetData.slice(0, 5);
  } else {
    // 其他情况显示应用视角
    return state.appData.slice(0, 5);
  }
});

const setMockData = () => {
  const labels = [
    "新一代产业园区",
    "国际商贸",
    "暨通系统",
    "智慧园区",
    "广电计量"
  ];
  state.appData = state.assetData = Array.from(
    {
      length: 5
    },
    (_, index) => {
      return {
        label: labels[index],
        percent: parseInt((Math.random() * 1000) % 100),
        value: parseInt((Math.random() * 1000) % 1000)
      };
    }
  );
};

// 应用视角
const queryVulApp = () => {
  queryAssetAppByVul({
    highLevel: true,
    topN: 5,
    orgId: queryParams.value.zoneName
  }).then(res => {
    let data = res.data || [];
    let appData = [];
    let total = 0;
    for (let item of data) {
      let { assetApp, count = 0 } = item;
      total += count || 0;
      appData.push({
        label: assetApp,
        value: count || 0
      });
    }
    for (let item of appData) {
      item["percent"] =
        total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
    }
    state.appData = appData;
  });
};

// 资产视角
const queryVulAsset = () => {
  queryVulDetailByAssetTop({
    highLevel: true,
    topN: 5,
    orgId: queryParams.value.zoneName
  }).then(res => {
    let data = res.data || [];
    let assetData = [];
    let total = 0;
    for (let item of data) {
      let { ip, assetName, count = 0 } = item;
      total += count || 0;
      assetData.push({
        label: assetName || ip,
        value: count || 0
      });
    }
    for (let item of assetData) {
      item["percent"] =
        total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
    }
    state.assetData = assetData;
  });
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  // 查询应用视角数据
  queryVulApp();
  // 只有集团视角需要查询资产视角
  if (isGroup.value) {
    queryVulAsset();
  }
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.InfoSystemVulTop5 {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
