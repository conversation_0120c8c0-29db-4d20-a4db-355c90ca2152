<template>
  <div class="SecSitRealtimeAlarm">
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>
    <div class="content" :style="style">
      <div class="notice">
        <img class="icon" src="../assets/emergencyTreatment.png" />
        <div class="desr">
          急需处理的事件数：失陷数 {{ state.highTotal }} / 事件数
          {{ state.total }}
        </div>
      </div>
      <div class="event-line">
        <EventTimeline :event-list="state.eventList"></EventTimeline>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { TitleDrill } from "@/views/modules/ddls";
import EventTimeline from "@/views/modules/situational-awareness/components/timeline/EventTimeline.vue";
import { queryEventScreen } from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});
const state = reactive({
  highTotal: 0,
  total: 0,
  eventList: []
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.highTotal = Math.round(Math.random() * 1000);
  state.total = state.highTotal + Math.round(Math.random() * 200);
  state.eventList = Array.from(
    {
      length: 100
    },
    (_, index) => {
      return {
        time: "2020",
        ip: "aaaa",
        title: "asdsdsd"
      };
    }
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryEventScreen({
    orgId: queryParams.value.zoneName
  }).then(res => {
    let data = res.data || {};
    let { highTotal, total, rows } = data;
    state.highTotal = highTotal || 0;
    state.total = total || 0;
    // 后端控制了最多200条，此处保险再处理下
    state.eventList = (rows || []).slice(0, 200);
  });
};

const handleDrill = () => {
  $router.push({
    // name: "security_alarm"
    name: "security_deal"
  });
};
watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.SecSitRealtimeAlarm {
  position: relative;
  padding: 20px;
  width: 956px;
  height: 306px;
  .content {
    height: 100%;
    .notice {
      display: flex;
      align-items: center;
      gap: 10px;
      .desr {
        background-image: url("../assets/rectangle.png");
        width: 238px;
        height: 28px;
        display: flex;
        align-items: center;
        padding-left: 10px;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 14px;
        color: #d1e4ff;
        line-height: 14px;
        white-space: nowrap;
      }
    }
    .event-line {
      margin-top: 10px;
      height: 160px;
    }
  }
}
</style>
