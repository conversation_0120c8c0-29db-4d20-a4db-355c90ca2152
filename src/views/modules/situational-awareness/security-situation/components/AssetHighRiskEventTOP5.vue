<template>
  <div class="AssetHighRiskEventTOP5">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div v-if="isGroup" class="right-tabs">
      <div
        class="tab"
        :class="{ selected: state.topViewAngle == 'dept' }"
        @click="state.topViewAngle = 'dept'"
      >
        组织视角
      </div>
      <div
        class="tab"
        :class="{ selected: state.topViewAngle == 'asset' }"
        @click="state.topViewAngle = 'asset'"
      >
        资产视角
      </div>
    </div>
    <div class="content" :style="style">
      <TableTop5 :columns="computedColumns" :data="top5Data"></TableTop5>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { getImage } from "@/views/modules/situational-awareness/security-situation/assets/image";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
import { TitleDrill } from "@/views/modules/ddls";
import TableTop5 from "@/views/modules/situational-awareness/components/topn/TableTop5.vue";
import {
  queryOrgListByEvent,
  queryRiskAccessByAsset
} from "@/views/modules/situational-awareness/security-situation/api";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const state = reactive({
  assetAngleData: [],
  deptAngleData: [],
  topViewAngle: "dept"
});
const top5Data = computed(() => {
  // 当集团且视角位组织视角时
  if (isGroup.value && state.topViewAngle == "dept") {
    return state.deptAngleData.slice(0, 5);
  } else {
    // 子公司只显示资产视角
    return state.assetAngleData.slice(0, 5);
  }
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const isGroup = computed(() => {
  return queryParams.value.zoneName == "";
});
const setMockData = () => {
  const labels = [
    "新一代产业园区",
    "国际商贸",
    "暨通系统",
    "智慧园区",
    "广电计量"
  ];
  state.assetAngleData = state.deptAngleData = Array.from(
    {
      length: 5
    },
    (_, index) => {
      return {
        label: labels[index],
        percent: parseInt((Math.random() * 1000) % 100),
        value: parseInt((Math.random() * 1000) % 1000)
      };
    }
  );
};

const computedColumns = computed(() => {
  const label =
    isGroup.value && state.topViewAngle == "dept" ? "单位名称" : "资产名称";
  return [
    { label: "排名", prop: "" },
    {
      label: label,
      prop: "label"
    },
    {
      label: "事件数量",
      prop: "count"
    }
  ];
});

const queryEventAsset = () => {
  queryRiskAccessByAsset({
    dateRange: "7d",
    dealWith: 1,
    orgId: queryParams.value.zoneName
  }).then(res => {
    let data = res.data?.rows || [];
    let total = 0;
    let assetAngleData = [];
    for (let item of data) {
      let { ip, assetName, eventCnt = 0 } = item;
      total += eventCnt;
      assetAngleData.push({
        label: assetName || ip,
        value: eventCnt
      });
    }
    for (let d of assetAngleData) {
      d.percent =
        total == 0 ? 0 : parseFloat(((d.value * 100) / total).toFixed(2));
    }
    state.assetAngleData = assetAngleData;
  });
};
const queryEventDept = () => {
  queryOrgListByEvent({
    // 只有集团（全部）调用这里传不传标识都可以
    dateRange: "7d",
    orgId: queryParams.value.zoneName,
    reseverity: ["5", "4"]
  }).then(res => {
    let data = res.data || [];
    const deptAngleData = [];
    let total = 0;
    for (let item of data) {
      let { deptName: label, eventCount: value = 0 } = item;
      total += value || 0;
      deptAngleData.push({
        label,
        value
      });
    }
    for (let item of deptAngleData) {
      item["percent"] =
        total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
    }
    state.deptAngleData = deptAngleData;
  });
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  queryEventAsset();
  // 只有集团视角下查询组织,子公司查询资产
  if (isGroup.value) {
    queryEventDept();
  }
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.AssetHighRiskEventTOP5 {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
