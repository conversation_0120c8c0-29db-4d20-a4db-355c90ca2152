<template>
  <div class="AlarmTypeTOP10">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div class="content" :style="style">
      <!--      <ChartComponent :option="state.option" resizeable></ChartComponent>-->
      <div class="top-items">
        <div
          class="top-item"
          v-for="(item, index) in state.topData"
          :style="itemStyle(index)"
        >
          <span :title="item.name + ':' + item.value">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties, inject, reactive, watch } from "vue";
// import ChartComponent from "@/components/Echarts/ChartComponent.vue";
// import { EChartsOption } from "echarts";
import { eventTypeSpread } from "@/views/modules/situational-awareness/security-situation/api";

const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

// const buildOption = (data): any => {
//   return <EChartsOption>{
//     tooltip: {
//       trigger: "item"
//     },
//     series: [
//       {
//         type: "wordCloud",
//         shape: "circle",
//         keepAspect: false,
//         left: "center",
//         top: "center",
//         width: "100%",
//         height: "100%",
//         right: null,
//         bottom: null,
//         sizeRange: [16, 36],
//         rotationRange: [-80, 80],
//         rotationStep: 20,
//         gridSize: 8,
//         drawOutOfBound: false,
//         layoutAnimation: true,
//         textStyle: {
//           fontFamily: "sans-serif",
//           fontWeight: "bold",
//           color: function (params) {
//             let colors = [
//               "rgb(228,175,175)",
//               "#b66bda",
//               "rgb(8,218,211)",
//               "#9E87FFb3",
//               "#73DDFF",
//               "#58D5FF"
//             ];
//             return colors[parseInt(Math.random() * colors.length)];
//             // return (
//             //   "rgb(" +
//             //   [
//             //     Math.round(Math.random() * 160),
//             //     Math.round(Math.random() * 160),
//             //     Math.round(Math.random() * 160)
//             //   ].join(",") +
//             //   ")"
//             // );
//           }
//         },
//         emphasis: {
//           focus: "self",
//           textStyle: {
//             textShadowBlur: 10,
//             textShadowColor: "#999"
//           }
//         },
//         data
//       }
//     ]
//   };
// };
const state = reactive({
  // option: buildOption([]),
  count: 0,
  // 固定10个位置
  topData: [],
  topPositions: [
    {
      top: 1,
      scale: 1,
      opacity: 1,
      p: [140, 90]
    },
    {
      top: 2,
      scale: 0.8,
      opacity: 0.85,
      p: [8, 112]
    },
    {
      top: 3,
      scale: 0.8,
      opacity: 0.85,
      p: [120, 35]
    },
    {
      top: 4,
      scale: 0.7,
      opacity: 0.75,
      p: [125, 156]
    },
    {
      top: 5,
      scale: 0.7,
      opacity: 0.75,
      p: [272, 140]
    },
    {
      top: 6,
      scale: 0.6,
      opacity: 0.65,
      p: [296, 60]
    },
    {
      top: 7,
      scale: 0.6,
      opacity: 0.65,
      p: [237, 184]
    },
    {
      top: 8,
      scale: 0.6,
      opacity: 0.6,
      p: [260, 8]
    },
    {
      top: 9,
      scale: 0.55,
      opacity: 0.5,
      p: [43, 190]
    },
    {
      top: 10,
      scale: 0.5,
      opacity: 0.4,
      p: [19, 21]
    }
  ]
});

const itemStyle = (index): CSSProperties => {
  if (index >= state.count) {
    return {
      display: "none"
    };
  }
  let { scale, p, opacity } = state.topPositions[index];
  return {
    left: `${p[0]}px`,
    top: `${p[1]}px`,
    opacity: opacity,
    transform: `scale(${scale})`
  };
};

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.topData = [
    { name: "互联网服务", value: 1000 },
    { name: "交通运输", value: 850 },
    { name: "公司", value: 800 },
    { name: "军工", value: 600 },
    { name: "医药", value: 900 },
    { name: "商务服务", value: 600 },
    { name: "城乡规划", value: 800 },
    { name: "家政服务", value: 400 },
    { name: "安防", value: 850 },
    { name: "医疗服务", value: 200 }
  ];
  state.count = state.topData.length;
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  eventTypeSpread({
    orgId: queryParams.value.zoneName,
    dateRange: "7d"
  }).then(res => {
    let data = (res.data?.rows || []).slice(0, 10);
    const chartData = data.map(item => {
      let { eventType: name, count: value } = item;
      return {
        name,
        value: value
      };
    });
    state.count = chartData.length;
    state.topData = chartData;
    // state.option = buildOption(chartData);
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.AlarmTypeTOP10 {
  position: relative;
  padding: 20px;
  width: 440px;
  height: 306px;
  .content {
    height: 100%;
    .top-items {
      width: 100%;
      height: 100%;
      position: relative;
      .top-item {
        position: absolute;
        background-image: url("../assets/wordRect.png");

        //background: rgba(0, 145, 255, 0.2);
        box-shadow: inset 0px 0px 10px 0px rgba(0, 201, 255, 0.6);
        border-radius: 4px;
        //border: 1px solid #3877f2;
        // width: auto;
        min-width: 126px;
        max-width: 10em;
        background-size: 100% 100%;
        height: 44px;
        padding: 6px 15px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        //display: flex;
        //align-items: center;
        //justify-content: center;
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 20px;
        color: #ffffff;
        transform-origin: left top;
      }
    }
  }
}
</style>
