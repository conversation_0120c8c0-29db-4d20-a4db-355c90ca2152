import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const securityEventPath = `${ServerNames.securityEventServer}`;
const securityVulPath = ServerNames.securityVulServer.startsWith("/")
  ? ServerNames.securityVulServer
  : `/${ServerNames.securityVulServer}`;

/**
 * 漏洞等级分布
 *
 * @param params
 */
export const queryRiskSpread = params => {
  return http.postJson<any>(`${securityVulPath}/v3/sum/riskSpread`, {
    ...(params || {}),
    status: "undisposed"
  });
};

/**
 * 高危漏洞分布情况（组织单位）
 *
 * @param params
 */
export const queryOrgTreeByVulTop = params => {
  return http.postJson<any>(`${securityVulPath}/v3/suba/queryOrgTreeByVulTop`, {
    ...(params || {}),
    status: "undisposed"
  });
};

/**
 * 信息系统漏洞TOP5-应用视角
 *
 * @param params
 */
export const queryAssetAppByVul = params => {
  return http.postJson<any>(`${securityVulPath}/v3/suba/queryAssetAppByVul`, {
    ...(params || {}),
    status: "undisposed"
  });
};

/**
 * 信息系统漏洞TOP5-资产视角
 *
 * @param params
 */
export const queryVulDetailByAssetTop = params => {
  return http.postJson<any>(`${securityVulPath}/v3/suba/vulDetailByAssetTop`, {
    ...(params || {}),
    status: "undisposed"
  });
};

/**
 * 近7天告警趋势图
 * @param params
 */
export const queryAlarmTrend = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyAlarm/v3/queryAlarmTrend`,
    params
  );
};

/**
 * 告警类型-TOP10
 *
 * @param params
 */
export const eventTypeSpread = params => {
  return http.postJson<any>(`${securityEventPath}/sem/eventTypeSpread`, params);
};

/**
 * 安全事件 - 组织视角
 *
 * @param params
 */
export const queryOrgListByEvent = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEventAggregation/v3/queryOrgListByEvent`,
    params
  );
};

/**
 * 安全事件 - 资产视角
 *
 * @param params
 */
export const queryRiskAccessByAsset = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEventAggregation/v3/riskAccess`,
    params
  );
};

/**
 * 综合安全态势 - 中间组件算分接口
 *
 * @param params
 */
export const querySafeScore = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEventAggregation/v3/querySafeScore`,
    params
  );
};

// /eam-core/zcgl-common//instance/queryLabelCount?categoryId=0&labelId=69&zoneName=

/**
 * 查询影响资产数（风险资产数）
 *
 * @param zoneName
 */
export const queryAffectAssetLabelCount = zoneName => {
  return http.get<any, any>(
    `${ServerNames.eamCoreServer}/instance/queryLabelCount?categoryId=0&labelId=69&zoneName=${zoneName || ""}`
  );
};

/**
 * 实时告警 （事件列表综合安全态势）
 *
 * @param params
 */
export const queryEventScreen = params => {
  return http.postJson<any>(
    `${securityEventPath}/safetyEvent/v3/queryEventScreen`,
    params
  );
};
