<template>
  <div class="ls-view-wrap">
    <ls-view :ref-id="state.refId"></ls-view>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, reactive, watch } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();

const state = reactive({
  refId: ""
});

onMounted(() => {
  state.refId = route.query.id as string;
});

watch(
  () => route?.query.id,
  val => {
    state.refId = val as string;
  }
);
</script>
<style scoped>
.ls-view-wrap {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
