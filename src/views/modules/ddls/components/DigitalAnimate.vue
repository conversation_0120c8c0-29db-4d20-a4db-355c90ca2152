<template>
  <span class="digit-animate" :style="digitStyle || {}">
    <div
      class="digit-wrapper"
      :class="{ 'decimal-point': digit == '.' }"
      v-for="(digit, index) in digits"
      :style="digitValueStyleFn ? digitValueStyleFn(digit) : {}"
      :data-digit="digit"
      :key="index"
    >
      <div v-if="digit == '-'" class="minus-sign">-</div>
      <div v-else-if="digit == '.'">.</div>
      <template v-else>
        <div
          v-for="i in 10"
          :key="i"
          class="item"
          :style="itemStyle(i - 1, digit)"
        >
          {{ i - 1 }}
        </div>
      </template>
    </div>
  </span>
</template>

<script lang="ts" setup>
import { reactive, watch, computed, CSSProperties, PropType } from "vue";
const props = defineProps({
  digitStyle: Object as PropType<CSSProperties>,
  digitValueStyleFn: Function,
  value: {
    type: [Number, String]
  }
});
const value = computed(() => props.value);
const itemStyle = (index, value) => {
  return {
    // top: `${index - value}em`,
    transform: `translateY(${index - value}em)`
  };
};
const updateDigits = val => {
  if (!val) val = 0;
  digits.splice(0, digits.length, ...(val + "").split(""));
};

const digits = reactive([]);
// const valRef = ref(props.value);
watch(
  value,
  val => {
    updateDigits(val);
  },
  {
    immediate: true
  }
);
</script>

<style lang="scss" scoped>
.digit-animate {
  font-family: digifacewide;
  display: flex;
  .digit-wrapper {
    height: 1em;
    width: 0.8em;
    line-height: 1em;
    overflow: hidden;
    position: relative;

    .minus-sign {
    }
    &.decimal-point {
      width: 0.4em;
      margin-left: 0.1em;
    }
    .item {
      position: absolute;
      left: 0px;
      height: 1em;
      line-height: 1em;
      transition: all 0.5s;
    }
  }
}
</style>
