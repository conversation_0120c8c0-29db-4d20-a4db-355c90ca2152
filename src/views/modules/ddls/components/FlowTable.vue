<template>
  <div class="flow-table">
    <div v-if="showHeader" class="table-header" :style="computedHeaderStyle">
      <div
        v-for="(column, index) in columns"
        class="table-header-item"
        :key="index"
        :style="{
          width: column.width,
          justifyContent: column.headerAlign || column.align || align,
          ...(column.style || {})
        }"
      >
        {{ column.label }}
      </div>
    </div>
    <div
      ref="tableWrap"
      class="table-body"
      :style="{ height: showHeader ? `calc(100% - ${rowHeight}px)` : '100%' }"
    >
      <div
        v-if="enableVirtualScroll"
        class="virtual-scroll-dom"
        :style="virtualDomStyle"
      ></div>
      <div ref="rowsWrap">
        <div
          class="flex-row"
          v-for="(row, rowIndex) in tableRows"
          :key="getRowKey(row, rowIndex)"
          :style="rowStyle(rowIndex)"
        >
          <div
            v-for="(column, colIndex) in columns"
            class="table-data-item"
            :key="colIndex"
            :style="{
              width: column.width,
              justifyContent: column.align || align,
              ...(column.style || {})
            }"
          >
            <slot
              :name="column.prop"
              v-bind="{
                row,
                column,
                offsetIndex: offset + rowIndex,
                rowIndex,
                colIndex
              }"
            >
              <div class="cell-text" :title="row[column.prop]">
                <span>{{ row[column.prop] }}</span>
              </div>
            </slot>
          </div>
        </div>
      </div>
      <div v-if="rows.length == 0" class="empty-desc">暂无数据</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  CSSProperties,
  nextTick,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch
} from "vue";
import { debounce } from "lodash";
const tableWrap = ref<HTMLDivElement>(null);
const rowsWrap = ref<HTMLDivElement>(null);

export interface FlowTableColumn {
  prop: string;
  label: string;
  width: string;
  align?: string;
  style?: CSSProperties;
}

const props = defineProps({
  showHeader: {
    type: Boolean,
    default: true
  },
  rowKey: String,
  headerBackground: String,
  rowHeight: {
    type: Number,
    default: 40
  },
  // 自动滚动
  autoScroll: Boolean,
  // 实际渲染的行数(最低为5，如果小于5时将失效）
  actualRenderRows: Number,
  align: {
    type: String,
    default: "center"
  },
  columns: Array as PropType<FlowTableColumn[]>,
  rows: Array,
  stripe: {
    type: Boolean,
    default: true
  },
  stripeOffset: {
    type: Number,
    default: 0
  },
  stripeBackground: String,
  showBorderBottom: Boolean
});
let stripeOffset = computed(() => {
  return props.stripeOffset == 0 ? 0 : 1;
});
const stripe = computed(() => props.stripe);
const showHeader = computed(() => props.showHeader);
const showBorderBottom = computed(() => props.showBorderBottom);
const headerBackground = computed(() => props.headerBackground);
const rowHeight = computed(() => props.rowHeight);
const align = computed(() => props.align);

const computedHeaderStyle = computed(() => {
  const s: CSSProperties = {};
  if (headerBackground.value) {
    s.background = headerBackground.value;
  }

  if (rowHeight.value) {
    s.height = rowHeight.value + "px";
  }

  return s;
});

const state = reactive({
  timer: 0,
  scrollUp: true
});

const getRowKey = (row, rowIndex) => {
  if (props.rowKey) {
    return row[props.rowKey];
  } else {
    return rowIndex;
  }
};

const rowStyle = index => {
  const style: CSSProperties = {};
  if (stripe.value && (index & 1) == stripeOffset.value) {
    style.background = props.stripeBackground || "#032053";
    style.opacity = "0.9";
  }
  if (rowHeight.value) {
    style.height = rowHeight.value + "px";
  }
  if (showBorderBottom.value && index != rows.value.length - 1) {
    style.borderBottom = "1px dashed rgba(61, 115, 255, 0.5)";
  }
  return style;
};

const columns = computed(() => props.columns || []);
const rows = computed(() => props.rows || []);

const virtualDomStyle = computed(() => {
  console.log(
    "rows.value?.length * " + rowHeight.value,
    rows.value?.length * rowHeight.value
  );
  return {
    height: `${(rows.value?.length + 1) * rowHeight.value}px`
  };
});

const enableVirtualScroll = computed(() => {
  if (props.autoScroll) {
    // 开启了自动滚动禁用虚拟滚动
    return false;
  }
  return props.rows?.length > 100;
});
const offset = ref<number>(0);
const actualCount = ref<number>(10);

const initScroll = () => {
  nextTick(() => {
    if (!tableWrap.value) return;
    tableWrap.value.scrollTop = 0;
    if (!props.actualRenderRows || props.actualRenderRows < 5) {
      let { height } = tableWrap.value.getBoundingClientRect();
      if (height == 0) return;
      actualCount.value = Math.round(height / rowHeight.value + 0.5);
    } else {
      actualCount.value = props.actualRenderRows;
    }
  });
};

const tableRows = computed(() => {
  if (!enableVirtualScroll.value) {
    return props.rows;
  }
  return props.rows.slice(offset.value, offset.value + actualCount.value);
});

const onVirtualScroll = event => {
  if (enableVirtualScroll.value) {
    let scrollTop = event.target.scrollTop;
    let pos = parseInt(scrollTop / rowHeight.value + 0.5);
    offset.value = pos;
    rowsWrap.value.style.marginTop = `${scrollTop}px`;
  }
};

const resizeOnDomChange = () => {
  // reset offset
  // offset.value = 0;
  if (!rowsWrap.value) return;
  const { width } = rowsWrap.value.getBoundingClientRect();
  if (width == 0) {
    state.scrollUp = false;
    return;
  }
  state.scrollUp = true;
  // reset
  let marginTop = rowsWrap.value.style.marginTop;
  if (marginTop && tableWrap.value.scrollTop <= 1) {
    tableWrap.value.scrollTop = parseInt(marginTop) || 0;
  }
};
const handleResize = debounce(resizeOnDomChange, 200);
const ro = new ResizeObserver(handleResize);

const handleAutoScroll = () => {
  if (!state.scrollUp) return;
  let scrollTop = tableWrap.value.scrollTop;
  tableWrap.value.scrollTop = scrollTop + 1;
  let { clientHeight, scrollHeight } = tableWrap.value;
  // 判断是否滚动到最后
  if (clientHeight + tableWrap.value.scrollTop + 1 >= scrollHeight) {
    tableWrap.value.scrollTop = 0;
  }
};

onMounted(() => {
  nextTick(() => {
    initScroll();
    // 自动滚动暂时禁用滚动渲染
    if (props.autoScroll) {
      startScrollTimer();
      tableWrap.value.onmouseenter = () => {
        state.scrollUp = false;
      };
      tableWrap.value.onmouseleave = () => {
        state.scrollUp = true;
      };
    } else {
      // 监听实现虚拟滚动
      tableWrap.value.addEventListener("scroll", onVirtualScroll, true);
    }
    ro.observe(tableWrap.value);
  });
});

const startScrollTimer = () => {
  state.timer = setInterval(() => {
    handleAutoScroll();
  }, 60) as any;
};

onBeforeUnmount(() => {
  ro.disconnect();
  if (state.timer) {
    clearInterval(state.timer);
  }
});

watch(
  () => props.autoScroll,
  val => {
    if (state.timer) {
      clearInterval(state.timer);
    }
    if (val) {
      startScrollTimer();
    }
  }
);
</script>

<style lang="scss" scoped>
.flow-table {
  height: 100%;
  .table-header {
    display: flex;
    height: 40px;
    color: #d1e4ff;
    font-size: 14px;
    font-weight: 550;
    opacity: 0.95;
    border-bottom: 1px solid #032053;
    .table-header-item {
      font-family: PingFang SC-Regular;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
      white-space: nowrap;
    }
  }
  .flex-row {
    display: flex;
    height: 40px;
  }
  .table-body {
    position: relative;
    height: calc(100% - 40px);
    overflow: auto;
    &::-webkit-scrollbar {
      display: none !important;
    }
    &::-webkit-scrollbar-track-piece {
      display: none !important;
    }
    .table-data-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      text-align: left;
      font-style: normal;
      text-transform: none;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      padding: 0 2px;
    }
  }
  .cell-text {
    padding: 0 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .empty-desc {
    color: #fff;
    opacity: 0.6;
    font-size: 12px;
    text-align: center;
    margin: 10% 0;
  }

  .virtual-scroll-dom {
    float: left;
  }
}
</style>
