<template>
  <div
    class="title-drill"
    :style="{
      height: `${titleHeight}px`
    }"
  >
    <el-button
      type="primary"
      link
      :icon="MoreFilled"
      @click="emits('on-drill')"
      >{{ btnText }}</el-button
    >
  </div>
</template>
<script setup lang="ts">
import { MoreFilled } from "@element-plus/icons-vue";
const emits = defineEmits(["on-drill"]);
const props = defineProps({
  titleHeight: Number,
  btnText: String
});
</script>

<style scoped lang="scss">
.title-drill {
  position: absolute;
  top: 0;
  right: 20px;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
