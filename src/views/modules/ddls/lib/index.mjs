import { defineComponent as ee, computed as P, createElement<PERSON>lock as F, openBlock as E, normalize<PERSON><PERSON>le as he, normalize<PERSON>lass as K, createElementVNode as B, createCommentVNode as q, unref as S, toDisplayString as pe, inject as xe, ref as J, reactive as _e, onMounted as Te, onUpdated as Ba, onBeforeUnmount as Qe, watch as Z, resolveComponent as R, createBlock as W, resolveDynamicComponent as Me, mergeProps as lt, withCtx as p, nextTick as Ee, getCurrentInstance as Ke, readonly as Na, getCurrentScope as $l, onScopeDispose as Ha, shallowRef as fn, watchEffect as Fa, isRef as $a, warn as Ul, provide as Ye, renderSlot as ye, toRef as Fn, onUnmounted as jl, useAttrs as Wl, useSlots as Ua, Fragment as me, withModifiers as rt, createVNode as c, Transition as Yo, withDirectives as Ot, createTextVNode as ae, vShow as zt, Text as Gl, h as ql, shallowReactive as Yl, isVNode as to, render as $n, markRaw as Oo, toRefs as Zl, with<PERSON><PERSON><PERSON> as An, renderList as Re, normalizeProps as Un, guardReactiveProps as jn, createSlots as Kl, onBeforeMount as Xl } from "vue";
const Ql = "data:image/png;base64,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", Jl = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Ql
}, Symbol.toStringTag, { value: "Module" })), es = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFkAAABZCAYAAABVC4ivAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAATISURBVHic7Z09c+JWFIZfaTJQyUOhLo3YJlthbeFqXSzuUuE0qXYndruZjOEXbPwLsCfjtMaTKmmCq3QxhbdyEbwV2yxqNpULTaigWFIc7kqWBRIyOrqw55lhsNCHrx8dzj33IiwDUc6mBwAaABwA7oP1wjw8AH18wil+MnrhFcbnn9pTByVcgeQKj8FAD2McomV4tAiI4HzwMEEdLcMzAQBlnEMErxoVuDDwy/QFTFoQcuHQhImjolux4TRMSAWRN44JycV545pFt+BLQCQzIJIZEMkMiGQGRDIDIpkBkcyASGZAJDMgkhkQyQyIZAZEMgMimQGRzIBIZuArjl/i2sCf3wKOtdx+vY9A/TJ5u/M94ODpcsf2x3Ts/t1y+2WBJZKzCAaAF18n7+dYywsGgEqZ2sUBSyRnEeyPgc4A8EaLt/NGQOsaOKot0Z6t7O3KAovkMMavqz/myTt6pGX6evVtWIR0fAyIZAZEMgPsOfkxuDZQKVFnl9Qh6sRaRfJRDbjaB97sFN2S5VgryeuK9unCsShyXZseAA0+XBvoDoHLIc+o7TFoK7lSAtq780dzSvrPO8DJLXB8A/gT3jamRUvJjkW5V43IvBHQ/UBRC9CQeNumE+BYQHMb2H8C1Lt6doiskv1x8jaV0n3Brev40Vx3SNHbrFHEqxPz7PfkiO4M6AT1Pi7/N2TBwNl0mvcvadaAH54CF4Pk4W/7OUUmADz7I12+dW3gn+/p584AOPw7eR/H4ot6FslpCcuaF8HzUBENANXf9EobWpVwjSo9e6N4wZVSMCCJcvIuEKtbHa2V5P2Z5M7g4bqDb4DhK4r04StajtL9QM+q1NOFwiU7Fn2ycb4XyLmN5GFVzlXKs+XybDkS0ar6cO2HxyySwku4uE82olWIsxUIVlTK9Pq8jlEd8yLmXcFN4ZL9SZAe5g08vP9IfFi0P6bXw4TXq2Pq0AEWLrl/F5RcahTXqAK9f4Nt/AlVGypl+GNajtbD2/bDY+pA4ZLD9O9I8v4ToPX2/rrOexqAOFuzyI4ZcKh3QneYf1uXofCOL8zxDT07FtW9UfwJnYg4wc1aMEq8/FIlp/lk2BsFubS9m74ycO1gIHJymzxKbNaoFIw7kXnAIvmqQbXt+V7ytq3roLNKI0IJA2g/9W5YhJo6VScmb1iG1eGP4NNcEhA3C9cZUP2syrtGlXJ3eJu0s3DLtuexaNXxKbwRzaa92aHJIseieeN5dAbx1YYuaCkZmJVtb4GL97Oord7P0f07ehzf6FELL0JbyYqwTHVhYdrpTF3QqoTbVLSP5DDHNzQXoWvuncdaSV63i1oUki4YEMkMiGQG2HPy8GX6bU9TXtzdfk6lXXRiXxdYJHujYPirvkqQhvYuTVsu6uzUxS1Z28UBS7r47q90F7ZESfudkSwXqXgjahcHWl13salIx8eASGZAJDMgkhkQyQyIZAZEMgMimQGRzIBIZkAkMyCSGRDJDIhkBkQyAyKZAZHMgAmgX3QjNhzPBN12UsiPvgkgxT9qFDLzCad0+86z6RByN7PVY6CH10adOr4J6pC0sWo8jHEIqOqiZdBNUw30imzVBvH5JrRA+JbKCrpn6hHojpMOa9PWmz4oG1ziR6MTXvE/PnZnzXcH+JoAAAAASUVORK5CYII=", ts = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: es
}, Symbol.toStringTag, { value: "Module" })), ns = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFkAAABZCAYAAABVC4ivAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAScSURBVHic7Z0/aBtXHMe/7/T/DxFBIEHAqmqDksXGixfhQE09RmToUBcvcUsHd8nipXPWZsjiDiVxlrYKJEOIR5cYWtQhi4mW2OD0kCGggGjlWLJ0lu51kE86uVZysqTfXdTfBwx670n6/fj48e7dOz2ewBmSS/dvQeAmIJOAmD3bzvREBbADqd9Ts99umxuE8SK59GMSwvscQJI0tbFEbEPWV9TsqgqcSmbBI0GF1BbU7KrqBgAI30ZreGhRKe7j8M0rnFT+tivBjwrF7YVweRCOTyGSmDGqjY77qUgu/fQZhPLcaCnt5VB5+9qWZMeBUGwS0VS6UyGxokAot41yufCSBQ9I5e1rVIr7nQqBmwqA9gziyNzIXJjDN69MJZlUYLrYyeYJeULjSPe1TMwq5ka9oRGn8/9A+fBbmEFhyQSwZAJYMgEsmQCWTABLJoAlE8CSCWDJBLBkAlgyASyZAJZMAEsmgCUT4KYI4p/PwH89g0ZhF5XH65D1Y4qw5+KKTyD0xXcQvgDePbgDvVwaeUySnuy/ngEAuBNX4f7kKkXI3rnMZ6BEohD+IHxzn5PEJB8uhC9AHbJnfOELksTkMZkAlkwASyaAJRPAkglgyQSwZAJYMgEsmQDHSRa+AFyxib7vDJVIFK7YxIiyGgySBSKrKJEowstrUCJR6OUSDte/t/Q5dyKF8PIaAEDL51DdfDjCLPvHMT3ZLNgoG68/hDvRWXTyTqcRvHFrFCleGEdIPisYABqFXcvLkFo+B1mrtstOE2275PMEN4sHqDxet/wdermEo1/uOla0rWPyeYJlrQotn4Mn1f9mWC2fg29usV32Trd2Idk9RtsmWfgC/xEMAMIfRGDxy6HF8U6nof9TQu2PZ0P7zn6xbbgQ/qDlC9uguBMpkji9sE2yXi7hZG+HJJaWz5HE6YWtY3J1cwPh5TW44p2bCFmrovJk/UIPOP3zGXhn0l11td+fQcv/OXCug2Dr7ELWj3H08w9oFg/adcIfRPDGCoBWb7f6551OnyvYzrHYwPYp3HmijVmHVTyp2fYTcQOnCAYcIBnoLdrqhfHsmoWTBAMOkQx0RDcKuwCA+outvu74msUD6OUSjrceOUow4LAFopbou31/Ti+X8O7BnRFkNBwc05PHGZZMAEsmgCUTwJIJYMkEsGQCWDIBLJkAlkwAiWTzGgTFRpj3Yd4UJOvV97xzeJCsXVQ3N+CbW0SzeIBGYY8iZE+Otx4BaD0cqL/4jSQmieRGYc92uQZ6uYTKE+s/NxgGPCYTwJIJYMkEsGQCWDIBLJkAlkwASyaAJRPAkglgyQSwZAJYMgEsmQCWTABLJoAljwDF7e0uA7K9O8YTukye0DgiXB5zUVUAoRqlS1eukSc0joTjU+bijgKJp0YpFJ9CKDZJn9UYEYpNmg8+BKR+r3V851f3/4LpNLNy4SWOivuQzRM+dMsintBlXLpyDaGuXiy21V+/Xmg9rZbagvmM1Ehipvu/wVwEFbK+ApzOLtTsqgqpLQBi29a0xof2IbSA6Uhlg9MzU2+jdeJkkja3jxm5AwgVEk/V7DcPzS3/Ap7VgtPMUzEcAAAAAElFTkSuQmCC", os = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ns
}, Symbol.toStringTag, { value: "Module" })), rs = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFkAAABZCAYAAABVC4ivAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAANdSURBVHic7Z2xTttQFIY/WxWZkDJ0oovZmAodslVq0j4A6QNUStSNqqJ5AugTUFQxA+rWCaZOlAxsLGmndMILnZAaiSkZSIcrExewlIDzXxfOJ0XOjWOfo09Xx/ZJpBtwla1hA1gGImDp2n4jixjocMEm74N2ekdw+W5jGDHDIU6ucRcC2vRp0gpiNwQTPB1iBtRoBXEIQIltTHDeJBOXgM/DKqEbGFOhGRKy6juLe85yiN1BTJsoxGrxtFkKfWfwEDDJAkyyAJMswCQLMMkCTLIAkyzAJAswyQJMsgCTLMAkCzDJAkyyAJMswCQLkEheq8BwBQ6XoTyjiJjN0mM4eQN/3kI0q4kpkbxecdvqE/fyyVrFyS2XYPWpJqa8XPieyen45ZImptVkASZZgEkWYJIFmGQBJlmASRZgkgWYZAGFk1yecf2FSZ8Mo1l3XBF55DuBNNEsHNbdNj6H+S/jHVedc8cB7HSh+X16Od6GwszktOBkPG6X7EWq6dRYgO2X+ed3Fwoh+apggPapm83jsNuFXn80Lppo75JvEtw5g9ffxj9HfA61/eKK9lqTbxLc67uZWZ+f/Hw7XfiwOBo3FtzWd432Jrk8c10wuB7vxvP84jQW3Ez/eJzfOSfFW7kol3Q//1TnNHGy8CY5Poe9E02s3a4mThZea3LzAKL6vw8Rvb676I17Z5FmrTKqwwnrx7Dz62553hWvknsDqO252pyILpdg+5X7fBLRWYJ91uIE77dwiejO2eiz5K5jXOrzo1/EE4oiGAogGbJFj3thXLzSsyiSYCiIZBiJbp+68acfkz3xdc7c91tHxRIMBWsQ9QbuyW1S4nN49jX/fPKiMDP5PmOSBZhkASZZgEkWYJIFmGQBJlmASRZgkgVIJKd7ELfpE+dJb5B638/+Xp5IehfNA1hddE2c9m9FxGxaR27b68PmT03MgK3hUBPq4WI1WYBJFmCSBZhkASZZgEkWYJIFmGQBJlmASRZgkgWYZAEmWYBJFmCSBZhkASZZQAh0fCdxz4lD3LKTxvTohMAt/nZtjM0Fm275zq3hCbaaWf4EtFkJau7CN6CGlY28ienThOTuohW4RVMD2j6zukdcLkIL6SWVE9yaqau4FScjaWr/Nx1cNdjnXbCT3vEXSazCsnRrUYIAAAAASUVORK5CYII=", as = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: rs
}, Symbol.toStringTag, { value: "Module" })), is = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABZCAYAAAC+PDOsAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAR3SURBVHic7Z0xaBtXGMf/76STZMlGMQ41Na2rEup6cXAGDw4t1NhbHEroELdL7bYZ3CVD50xZmyFLO4SkWpqIQinB2tqQgoM7pIOoF8cQerHBoIIIwrIqn073OsgnnWTZlRXru3fK95vu3T34Pv34eHr37qQn0ERi4e4iID+HwBlATDZfZ47FAJCBtG8bqWu/uy8I5yCx8H0CIvQYQII0tZ5FZiDLV4zUsgEciGbJXcOANGeM1LIRBAAI/Re4JO/ubKCQfY7y3kuP8vMfWjAEEdDRP3wOAyPj0IIhAHAK+F2RWLjzEYT2GABsy0R2/VcW/IrosUG8eeFS/YTEkgahXXfauzsbLPkUKO+9xO7ORv2EwMcagNrMopB97kFavUmjS5nQ4BqbK/t75An1Ko0jg5jUPMvkNYNFE8GiiWDRRLBoIlg0ESyaCBZNBIsmgkUTwaKJYNFEsGgiWDQRLJoIFk0EiyYiSBFEiw8hemkR2pkhinBtY+dzKKaTsPO5rsciER354DKC77xPEepEaPGzCE/N4t/ffup+rK5HAFD5Z5siTEdQVDNAVNH7Tx8BAAJvvE0Rrm2srU2Y62sksUhEA3XZrys86yCCrKJPA31sEn1zVyHCfSjcv4VKVt2xvxnfVHRoYhqxT76GFh+CiETR/9k3CAyrNeYfhy9EhyamEZ1fajjnN9nKi24l2UFEohj44gZCE9PEWZ0cpUUfJ9lNdH5JednKig6Ojh2SLEvFI/tH55cQHB3rdlodo6zo8NRcQ7u0utIwy7BePDt0sxGauEiSWycoK9raelY7Lq2uoPRk5VCfYjrZIFvlW31l59H7Tx/VKtja2jyyXzGdhPnX2v/28xplRQPti1NZsIOyQ0evwaKJYNFEsGgiWDQRLJoIFk0EiyZCadH62GRbC0Xt9vMSZe8Mw1Oz6Ju7CgAw19dQTCdb9ovOL9YWk5rXPlRCWdH6e/V/GTpqVc4tGagurbLoE1J6soJ+19tNzbKb33ySpSL2/1T3lQZlx2hraxPF9A9t9ZWlovJPxZUVDQDm+h8o/PjtsU9W/CAZUFw0UK3swv1bLWX7RTLgA9EAUMluH5LtJ8mAT0QDddmV7HbDsV9QdtbRikp2G7v3bnqdRkf4pqL9DllFh6dmlXs/2s7nWj5d7wZkP62IfHiZItSJsfM5krtJkqFDRPoownREYPgtkjhkP63Q4mfJPlS7WC82UVrtoaHDzuew9/N3FKGUhWcdRLBoIlg0ESyaCBZNBIsmgkUTwaKJYNFEsGgiWDQRLJoIFk0EiyaCRRPBoolg0URogMw4DT026GUuPUUgHHM3DQ0QhtPqHz5HnlCv0uQyo0HiodMaGBnnqj4F9NggBkbG6yekfbu6Feqnd//GwS5wtmXWduiUlTJsy/QiV1+ixwZrO3TWkRnjwVcXqk/BpTnj7DmrBUOIj55HfPS8J8n2GAZk+QpwMOswUssGpDnj/mJkXpnaxr6Aa7tqh4M9aK+junNngjY3vyMzgDAg8dBIfZl0X/kPwcVuKN6tS5AAAAAASUVORK5CYII=", ls = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: is
}, Symbol.toStringTag, { value: "Module" })), ss = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFoAAABZCAYAAAC+PDOsAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAANGSURBVHic7Z29ThtBFEbPriKoEiElFWlMRSpwCurYUKWCNOkibKVEkfEThDxBQBG1yRMQP0DAFKkosqEiFUuBK6QgUeECp5gY2/walP08a9/T7K5nrDs+Gl3P7M5qAi6z3iwQsEiTMSB7pdy4jRiIOGeND0GtsyC4OPvczDDCNpBRtmyAiWjwhnIQQ0u0SU6KmAZ5ykEcAjDCJiY5CVodmIAvzRyhuzASoxgSUup3K4aA+RAbWSjIhFhuVpAN+92CYcFEizDRIky0CBMtwkSLMNEiTLQIEy3CRIsw0SJMtAgTLcJEizDRIky0CBMt4pEiSOYxVGbd0SfiUyhuuWPSSER/nIHcc0Wk+5F5AqUpKP9IPpYkdfw6VkR5GIeC3gyiHr26547TzxTRemenDhv7mlgB682mJtRwY6MOEakSvTABB+/gz3vIepaG7iI1oguTsPnaDRHHRmF7Pl2yUyG6MAmVue7P0ibbe9HXSW4xNgo/37o6vuO16Nskd1KZ81+2t6Jz41cln5zdXL8y577jK96KLk13X6/sQtQxw6wdXZ1sLL5Ivl0PxVvRO0ft85Vd+LR7tU5xq1u2z1N9yRT8IazutXtwrX5zveIWfN2/u16/8VY09C7OZ8EtvE0dg4aJFmGiRZhoESZahIkWYaJFmGgRXotemOjtRlGv9fqJt6KXp9wTle0Ft/jmJiqz7XoFj28qeTsFn59on98ksDLbXfZqXLd84L54u9wgN+56aa+cnEG+2n0r1Se8TR21OhS/91bXd8ngsWiAjd+Q/3b7k5U0SAbPRYPr2fnq9bLTIhlSIBqcyMuy0yQZUiIa2rKj4+7ztODtqGPQSE2PTjuyCcvylH/ro+PT65+uJ4Hs1YqVGUWk+3N4qplNSlLH2IgiysOYfqqJI+nRa3vuxZys6Ef1Sq2uSx026hBhow4RJlqEiRZhokWYaBEmWoSJFmGiRZhoESZahIkWYaJFmGgRJlqEiRZhokWYaBEhEPW7EUNAHOK27zSSJQqBar9bMfCcs+a2Ql1vHmC7wCVFxFLw0v0ZNshjKSQJYhq8gdaooxzE/2TbH+P/42JjX+jcrrqF24O2hNu5MyNtWvqJcJmhylKw0VnwF4IGzMt/kUGTAAAAAElFTkSuQmCC", us = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: ss
}, Symbol.toStringTag, { value: "Module" })), cs = "", ds = "", Ar = () => new Promise((e) => {
  e([]);
}), Ie = {
  basePath: cs,
  resourcePath: ds,
  post: Ar,
  get: Ar
}, fs = (e) => e ? e.config && e.data && e.request && typeof e.status == "number" && typeof e.statusText == "string" ? e.data : e : null, ps = (e) => e ? (e.config && e.isAxiosError && e.request && e.response && (e = e.response.data), e) : null, xt = (e) => new Promise((t, n) => {
  e.then((o) => {
    t(fs(o));
  }).catch((o) => {
    n(ps(o));
  });
}), ms = (e) => xt(
  Ie.post(
    `${Ie.basePath}/dev-platform/large-screen/list`,
    e,
    {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded"
      }
    }
  )
), hs = (e) => xt(
  Ie.get(
    `${Ie.basePath}/dev-platform/large-screen/delete?id=${e}`
  )
), gs = (e) => xt(
  Ie.post(
    `${Ie.basePath}/dev-platform/large-screen/save`,
    e,
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  )
), vs = (e) => xt(
  Ie.post(
    `${Ie.basePath}/dev-platform/large-screen/copy`,
    e,
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  )
), ys = (e) => xt(
  Ie.post(
    `${Ie.basePath}/dev-platform/data-layout/save`,
    e,
    {
      headers: {
        "Content-Type": "application/json"
      }
    }
  )
), ja = (e, t, n, o) => {
  let r = `${Ie.basePath}/dev-platform/data-layout/query`;
  return xt(
    Ie.get(r, {
      params: {
        refId: e,
        category: t,
        groupId: n
      }
    })
  );
}, Yt = () => Ie.resourcePath || "", bs = (e) => xt(
  Ie.post(
    `${Ie.basePath}/dev-platform/resource/uploads`,
    e,
    {
      headers: {
        "Content-Type": "multipart/form-data"
      }
    }
  )
), ws = (e) => xt(
  Ie.get(
    `${Ie.basePath}/dev-platform/resource/category/${e}`
  )
);
let zo = null;
const Zo = /* @__PURE__ */ new Map(), Ko = /* @__PURE__ */ new Map(), Xo = /* @__PURE__ */ new Map(), Qo = /* @__PURE__ */ new Map(), Dn = {}, lv = (e) => {
  Dn[e.key] = e, Wa(e);
}, Wa = (e) => {
  zo && zo.component("ls-" + e.key, e.component);
}, sv = (e, t) => {
  Zo.set("ls-title-" + e, t);
}, uv = (e, t) => {
  Ko.set("ls-header-" + e, t);
}, cv = (e, t) => {
  Xo.set("ls-page-" + e, t);
}, dv = (e, t) => {
  Qo.set("ls-component-" + e, t);
}, Ga = (e) => e != null && e.startsWith("/") ? Yt() + e : Zo.get(e), no = (e) => e != null && e.startsWith("/") ? Yt() + e : Ko.get(e), qa = (e) => e != null && e.startsWith("/") ? Yt() + e : Xo.get(e), Jo = (e) => e != null && e.startsWith("/") ? Yt() + e : Qo.get(e), xs = () => oo(Zo), Ya = () => oo(Ko), Za = () => oo(Xo), Ss = () => oo(Qo), oo = (e) => e.entries().map(([t, n], o) => ({
  label: t,
  value: t
})).toArray(), er = () => {
  if (Object.values)
    return Object.values(Dn);
  {
    let e = [];
    for (let t in Dn)
      e.push(Dn[t]);
    return e;
  }
}, _s = () => {
  let e = [], t = er();
  for (let n of t)
    e.includes(n.group) || e.push(n.group);
  return e;
}, Es = () => {
  er().forEach((e) => {
    Wa(e);
  });
}, Cs = {
  install(e) {
    zo = e, Es();
  }
}, ks = {
  key: 0,
  class: "icon"
}, As = ["src"], Is = { class: "title-text" }, Ts = /* @__PURE__ */ ee({
  __name: "LsHeaderTitle",
  props: {
    scale: {
      type: Number,
      default: 1
    },
    headerTitle: Object,
    defaultTextStyle: Object
  },
  setup(e) {
    const t = e, n = P(() => {
      let { width: r, height: a, backgroundColor: l, backgroundImage: s } = t.headerTitle, u = {
        backgroundColor: l
      };
      return s && (u.backgroundImage = `url("${no(s)}")`), r > 0 && (u.width = `${r}px`), a > 0 && (u.height = `${a}px`), u;
    }), o = P(() => {
      let r = t.defaultTextStyle || {}, a = {
        ...r
      }, l = t.headerTitle.style;
      for (let i in l) {
        let d = l[i];
        if (!d && d !== 0) {
          let h = r[i];
          h && (a[i] = h);
        } else
          a[i] = d;
      }
      let { left: s, top: u } = t.headerTitle;
      return s != null && (a.marginLeft = `${s * t.scale}px`), u != null && (a.marginTop = `${u}px`), a.background && (a.color = "transparent", a.backgroundClip = "text"), a;
    });
    return (r, a) => (E(), F("div", {
      class: K(["ls-header-title", { float: e.headerTitle.float }]),
      style: he(n.value)
    }, [
      B("div", {
        class: "ls-header-title-text",
        style: he(o.value)
      }, [
        e.headerTitle.icon ? (E(), F("span", ks, [
          B("img", {
            src: S(Ga)(e.headerTitle.icon),
            style: he(t.headerTitle.iconStyle || {})
          }, null, 12, As)
        ])) : q("", !0),
        B("span", Is, pe(e.headerTitle.text), 1)
      ], 4)
    ], 6));
  }
}), ct = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
}, Ka = /* @__PURE__ */ ct(Ts, [["__scopeId", "data-v-fcdc331d"]]), Vs = /* @__PURE__ */ ee({
  __name: "LsComponent",
  props: {
    data: Object
  },
  setup(e) {
    const t = xe("designContext"), n = xe("pageScales") || [1, 1], o = J(), r = J(), a = e, l = P(() => {
      var M;
      return !!((M = t == null ? void 0 : t.designState) != null && M.designMode);
    }), s = P(() => a.data.props || {}), u = P(() => a.data.title && !a.data.title.hidden), i = P(() => u.value ? a.data.title.height : 0), d = P(() => {
      let M = [];
      return a.data.className && M.push(a.data.className), M;
    }), h = P(() => {
      let { backgroundColor: M, backgroundImage: y, style: $ } = a.data, Y = {
        ...$,
        backgroundColor: M
      };
      return y && (Y.backgroundImage = `url("${Jo(y)}")`), Y;
    }), x = () => ({
      left: 0,
      top: 0,
      contentWidth: 1,
      contentHeight: 1,
      wrapSized: !1,
      wrapRawWidth: 0,
      wrapRawHeight: 0,
      wrapWidth: 1,
      wrapHeight: 1,
      tipVisible: !1
    }), m = J({
      top: 0,
      left: 0,
      bottom: 0,
      right: 0
    }), f = J({
      getBoundingClientRect: () => m.value
    }), v = ({ clientX: M, clientY: y }) => {
      let { width: $, left: Y, top: se } = o.value.getBoundingClientRect(), V = Y, te = V + $, ie = se, oe = ie + 60;
      M >= V && M <= te && y >= ie && y <= oe ? I.tipVisible = !0 : I.tipVisible = !1, m.value = new DOMRect((V + te) / 2, ie, 0, 0);
    }, C = () => {
      I.tipVisible = !1;
    }, A = (M) => {
      let y = a.data.textContexts || [];
      for (let $ of y)
        if ($.key == M)
          return !0;
      return !1;
    }, O = (M, y) => {
      let $ = a.data.textContexts || [];
      for (let Y of $)
        if (Y.key == M)
          return Y.value;
      return y;
    }, I = _e(x()), k = _e([1, 1]), g = () => {
      a.data.fit || I.wrapSized && Ee(() => {
        k[0] = I.contentWidth / (I.wrapWidth || 1), k[1] = I.contentHeight / (I.wrapHeight || 1);
      });
    }, b = P(() => a.data.fit ? {} : {
      transform: `scale(${k[0]}, ${k[1]})`
    }), w = () => {
      let { width: M, height: y } = r.value.getBoundingClientRect();
      M > 20 && y > 20 && (I.wrapWidth = M, I.wrapHeight = y, I.wrapRawWidth = M / n[0], I.wrapRawHeight = y / n[1], I.wrapSized = !0);
    }, T = () => {
      let {
        width: M,
        height: y,
        left: $,
        top: Y
      } = o.value.getBoundingClientRect();
      M < 10 || y < 10 || (I.left = $, I.top = Y, I.contentWidth = M, I.contentHeight = y, I.wrapSized || w(), g());
    }, _ = new ResizeObserver(() => {
      T();
    }), L = () => {
      Object.assign(I, x()), k[0] = k[1] = 1;
    };
    return Te(async () => {
      _.observe(o.value), _.observe(r.value), window.addEventListener("resize", T), setTimeout(T, 10);
    }), Ba(() => {
      setTimeout(T, 50);
    }), Qe(() => {
      _.disconnect(), window.removeEventListener("resize", T);
    }), Z(
      () => n,
      (M) => {
        I.wrapWidth = I.wrapRawWidth * M[0], I.wrapHeight = I.wrapRawHeight * M[1];
      },
      {
        deep: !0
      }
    ), Z(
      () => u.value,
      (M) => {
        T();
      }
    ), Z(
      () => i.value,
      (M) => {
        T();
      }
    ), Z(
      () => a.data.component,
      (M) => {
        M && L();
      }
    ), (M, y) => {
      const $ = R("el-tooltip");
      return E(), F("div", {
        ref_key: "mainEl",
        ref: o,
        class: K(["ls-component", d.value]),
        style: he(h.value),
        onMousemove: v,
        onMouseout: C
      }, [
        u.value ? (E(), W(Ka, {
          key: 0,
          scale: k[0] || 1,
          "header-title": e.data.title,
          "default-text-style": {
            color: "#fff",
            fontSize: "24px",
            textAlign: "left"
          }
        }, null, 8, ["scale", "header-title"])) : q("", !0),
        B("div", {
          ref_key: "wrapEl",
          ref: r,
          class: K(["ls-component-wrap", { fitWrap: a.data.fit }]),
          style: he(b.value)
        }, [
          (E(), W(Me(
            "ls-" + a.data.component
            /*|| 'computingPowerResources'*/
          ), lt(s.value, {
            "show-title": u.value,
            fit: a.data.fit,
            "title-height": u.value ? i.value / k[1] : 0,
            "design-mode": l.value,
            tip: a.data.disableAutoTip ? a.data.tip : "",
            "has-context-text": A,
            "get-context-text": O
          }), null, 16, ["show-title", "fit", "title-height", "design-mode", "tip"]))
        ], 6),
        a.data.tip ? (E(), W($, {
          key: 1,
          visible: I.tipVisible,
          "onUpdate:visible": y[0] || (y[0] = (Y) => I.tipVisible = Y),
          content: a.data.tip,
          placement: "bottom",
          effect: "dark",
          trigger: "click",
          "show-arrow": !1,
          "virtual-triggering": "",
          "virtual-ref": f.value,
          teleported: !1
        }, {
          content: p(() => [
            B("div", {
              class: "component-tooltip-content",
              style: he({ maxWidth: I.contentWidth - 40 + "px" })
            }, pe(a.data.tip), 5)
          ]),
          _: 1
        }, 8, ["visible", "content", "virtual-ref"])) : q("", !0)
      ], 38);
    };
  }
}), Ms = /* @__PURE__ */ ct(Vs, [["__scopeId", "data-v-4d07eff5"]]), Xa = Symbol(), Rn = "el", Os = "is-", At = (e, t, n, o, r) => {
  let a = `${e}-${t}`;
  return n && (a += `-${n}`), o && (a += `__${o}`), r && (a += `--${r}`), a;
}, Qa = Symbol("namespaceContextKey"), Ja = (e) => {
  const t = e || (Ke() ? xe(Qa, J(Rn)) : J(Rn));
  return P(() => S(t) || Rn);
}, Xe = (e, t) => {
  const n = Ja(t);
  return {
    namespace: n,
    b: (v = "") => At(n.value, e, v, "", ""),
    e: (v) => v ? At(n.value, e, "", v, "") : "",
    m: (v) => v ? At(n.value, e, "", "", v) : "",
    be: (v, C) => v && C ? At(n.value, e, v, C, "") : "",
    em: (v, C) => v && C ? At(n.value, e, "", v, C) : "",
    bm: (v, C) => v && C ? At(n.value, e, v, "", C) : "",
    bem: (v, C, A) => v && C && A ? At(n.value, e, v, C, A) : "",
    is: (v, ...C) => {
      const A = C.length >= 1 ? C[0] : !0;
      return v && A ? `${Os}${v}` : "";
    },
    cssVar: (v) => {
      const C = {};
      for (const A in v)
        v[A] && (C[`--${n.value}-${A}`] = v[A]);
      return C;
    },
    cssVarName: (v) => `--${n.value}-${v}`,
    cssVarBlock: (v) => {
      const C = {};
      for (const A in v)
        v[A] && (C[`--${n.value}-${e}-${A}`] = v[A]);
      return C;
    },
    cssVarBlockName: (v) => `--${n.value}-${e}-${v}`
  };
};
/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
process.env.NODE_ENV !== "production" && Object.freeze({});
process.env.NODE_ENV !== "production" && Object.freeze([]);
const pn = () => {
}, zs = Object.prototype.hasOwnProperty, Wn = (e, t) => zs.call(e, t), st = (e) => typeof e == "function", Fe = (e) => typeof e == "string", Zt = (e) => e !== null && typeof e == "object", Ps = (e) => {
  const t = /* @__PURE__ */ Object.create(null);
  return (n) => t[n] || (t[n] = e(n));
}, Ls = /-(\w)/g, Ds = Ps(
  (e) => e.replace(Ls, (t, n) => n ? n.toUpperCase() : "")
);
var Rs = typeof global == "object" && global && global.Object === Object && global, Bs = typeof self == "object" && self && self.Object === Object && self, tr = Rs || Bs || Function("return this")(), wt = tr.Symbol, ei = Object.prototype, Ns = ei.hasOwnProperty, Hs = ei.toString, rn = wt ? wt.toStringTag : void 0;
function Fs(e) {
  var t = Ns.call(e, rn), n = e[rn];
  try {
    e[rn] = void 0;
    var o = !0;
  } catch {
  }
  var r = Hs.call(e);
  return o && (t ? e[rn] = n : delete e[rn]), r;
}
var $s = Object.prototype, Us = $s.toString;
function js(e) {
  return Us.call(e);
}
var Ws = "[object Null]", Gs = "[object Undefined]", Ir = wt ? wt.toStringTag : void 0;
function nr(e) {
  return e == null ? e === void 0 ? Gs : Ws : Ir && Ir in Object(e) ? Fs(e) : js(e);
}
function or(e) {
  return e != null && typeof e == "object";
}
var qs = "[object Symbol]";
function rr(e) {
  return typeof e == "symbol" || or(e) && nr(e) == qs;
}
function Ys(e, t) {
  for (var n = -1, o = e == null ? 0 : e.length, r = Array(o); ++n < o; )
    r[n] = t(e[n], n, e);
  return r;
}
var Cn = Array.isArray, Tr = wt ? wt.prototype : void 0, Vr = Tr ? Tr.toString : void 0;
function ti(e) {
  if (typeof e == "string")
    return e;
  if (Cn(e))
    return Ys(e, ti) + "";
  if (rr(e))
    return Vr ? Vr.call(e) : "";
  var t = e + "";
  return t == "0" && 1 / e == -1 / 0 ? "-0" : t;
}
function Gn(e) {
  var t = typeof e;
  return e != null && (t == "object" || t == "function");
}
function Zs(e) {
  return e;
}
var Ks = "[object AsyncFunction]", Xs = "[object Function]", Qs = "[object GeneratorFunction]", Js = "[object Proxy]";
function eu(e) {
  if (!Gn(e))
    return !1;
  var t = nr(e);
  return t == Xs || t == Qs || t == Ks || t == Js;
}
var bo = tr["__core-js_shared__"], Mr = function() {
  var e = /[^.]+$/.exec(bo && bo.keys && bo.keys.IE_PROTO || "");
  return e ? "Symbol(src)_1." + e : "";
}();
function tu(e) {
  return !!Mr && Mr in e;
}
var nu = Function.prototype, ou = nu.toString;
function ru(e) {
  if (e != null) {
    try {
      return ou.call(e);
    } catch {
    }
    try {
      return e + "";
    } catch {
    }
  }
  return "";
}
var au = /[\\^$.*+?()[\]{}|]/g, iu = /^\[object .+?Constructor\]$/, lu = Function.prototype, su = Object.prototype, uu = lu.toString, cu = su.hasOwnProperty, du = RegExp(
  "^" + uu.call(cu).replace(au, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
);
function fu(e) {
  if (!Gn(e) || tu(e))
    return !1;
  var t = eu(e) ? du : iu;
  return t.test(ru(e));
}
function pu(e, t) {
  return e == null ? void 0 : e[t];
}
function ar(e, t) {
  var n = pu(e, t);
  return fu(n) ? n : void 0;
}
function mu(e, t, n) {
  switch (n.length) {
    case 0:
      return e.call(t);
    case 1:
      return e.call(t, n[0]);
    case 2:
      return e.call(t, n[0], n[1]);
    case 3:
      return e.call(t, n[0], n[1], n[2]);
  }
  return e.apply(t, n);
}
var hu = 800, gu = 16, vu = Date.now;
function yu(e) {
  var t = 0, n = 0;
  return function() {
    var o = vu(), r = gu - (o - n);
    if (n = o, r > 0) {
      if (++t >= hu)
        return arguments[0];
    } else
      t = 0;
    return e.apply(void 0, arguments);
  };
}
function bu(e) {
  return function() {
    return e;
  };
}
var qn = function() {
  try {
    var e = ar(Object, "defineProperty");
    return e({}, "", {}), e;
  } catch {
  }
}(), wu = qn ? function(e, t) {
  return qn(e, "toString", {
    configurable: !0,
    enumerable: !1,
    value: bu(t),
    writable: !0
  });
} : Zs, xu = yu(wu), Su = 9007199254740991, _u = /^(?:0|[1-9]\d*)$/;
function ni(e, t) {
  var n = typeof e;
  return t = t ?? Su, !!t && (n == "number" || n != "symbol" && _u.test(e)) && e > -1 && e % 1 == 0 && e < t;
}
function Eu(e, t, n) {
  t == "__proto__" && qn ? qn(e, t, {
    configurable: !0,
    enumerable: !0,
    value: n,
    writable: !0
  }) : e[t] = n;
}
function oi(e, t) {
  return e === t || e !== e && t !== t;
}
var Cu = Object.prototype, ku = Cu.hasOwnProperty;
function Au(e, t, n) {
  var o = e[t];
  (!(ku.call(e, t) && oi(o, n)) || n === void 0 && !(t in e)) && Eu(e, t, n);
}
var Or = Math.max;
function Iu(e, t, n) {
  return t = Or(t === void 0 ? e.length - 1 : t, 0), function() {
    for (var o = arguments, r = -1, a = Or(o.length - t, 0), l = Array(a); ++r < a; )
      l[r] = o[t + r];
    r = -1;
    for (var s = Array(t + 1); ++r < t; )
      s[r] = o[r];
    return s[t] = n(l), mu(e, this, s);
  };
}
var Tu = 9007199254740991;
function Vu(e) {
  return typeof e == "number" && e > -1 && e % 1 == 0 && e <= Tu;
}
var Mu = "[object Arguments]";
function zr(e) {
  return or(e) && nr(e) == Mu;
}
var ri = Object.prototype, Ou = ri.hasOwnProperty, zu = ri.propertyIsEnumerable, ai = zr(/* @__PURE__ */ function() {
  return arguments;
}()) ? zr : function(e) {
  return or(e) && Ou.call(e, "callee") && !zu.call(e, "callee");
}, Pu = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/, Lu = /^\w*$/;
function Du(e, t) {
  if (Cn(e))
    return !1;
  var n = typeof e;
  return n == "number" || n == "symbol" || n == "boolean" || e == null || rr(e) ? !0 : Lu.test(e) || !Pu.test(e) || t != null && e in Object(t);
}
var vn = ar(Object, "create");
function Ru() {
  this.__data__ = vn ? vn(null) : {}, this.size = 0;
}
function Bu(e) {
  var t = this.has(e) && delete this.__data__[e];
  return this.size -= t ? 1 : 0, t;
}
var Nu = "__lodash_hash_undefined__", Hu = Object.prototype, Fu = Hu.hasOwnProperty;
function $u(e) {
  var t = this.__data__;
  if (vn) {
    var n = t[e];
    return n === Nu ? void 0 : n;
  }
  return Fu.call(t, e) ? t[e] : void 0;
}
var Uu = Object.prototype, ju = Uu.hasOwnProperty;
function Wu(e) {
  var t = this.__data__;
  return vn ? t[e] !== void 0 : ju.call(t, e);
}
var Gu = "__lodash_hash_undefined__";
function qu(e, t) {
  var n = this.__data__;
  return this.size += this.has(e) ? 0 : 1, n[e] = vn && t === void 0 ? Gu : t, this;
}
function Dt(e) {
  var t = -1, n = e == null ? 0 : e.length;
  for (this.clear(); ++t < n; ) {
    var o = e[t];
    this.set(o[0], o[1]);
  }
}
Dt.prototype.clear = Ru;
Dt.prototype.delete = Bu;
Dt.prototype.get = $u;
Dt.prototype.has = Wu;
Dt.prototype.set = qu;
function Yu() {
  this.__data__ = [], this.size = 0;
}
function ro(e, t) {
  for (var n = e.length; n--; )
    if (oi(e[n][0], t))
      return n;
  return -1;
}
var Zu = Array.prototype, Ku = Zu.splice;
function Xu(e) {
  var t = this.__data__, n = ro(t, e);
  if (n < 0)
    return !1;
  var o = t.length - 1;
  return n == o ? t.pop() : Ku.call(t, n, 1), --this.size, !0;
}
function Qu(e) {
  var t = this.__data__, n = ro(t, e);
  return n < 0 ? void 0 : t[n][1];
}
function Ju(e) {
  return ro(this.__data__, e) > -1;
}
function ec(e, t) {
  var n = this.__data__, o = ro(n, e);
  return o < 0 ? (++this.size, n.push([e, t])) : n[o][1] = t, this;
}
function en(e) {
  var t = -1, n = e == null ? 0 : e.length;
  for (this.clear(); ++t < n; ) {
    var o = e[t];
    this.set(o[0], o[1]);
  }
}
en.prototype.clear = Yu;
en.prototype.delete = Xu;
en.prototype.get = Qu;
en.prototype.has = Ju;
en.prototype.set = ec;
var tc = ar(tr, "Map");
function nc() {
  this.size = 0, this.__data__ = {
    hash: new Dt(),
    map: new (tc || en)(),
    string: new Dt()
  };
}
function oc(e) {
  var t = typeof e;
  return t == "string" || t == "number" || t == "symbol" || t == "boolean" ? e !== "__proto__" : e === null;
}
function ao(e, t) {
  var n = e.__data__;
  return oc(t) ? n[typeof t == "string" ? "string" : "hash"] : n.map;
}
function rc(e) {
  var t = ao(this, e).delete(e);
  return this.size -= t ? 1 : 0, t;
}
function ac(e) {
  return ao(this, e).get(e);
}
function ic(e) {
  return ao(this, e).has(e);
}
function lc(e, t) {
  var n = ao(this, e), o = n.size;
  return n.set(e, t), this.size += n.size == o ? 0 : 1, this;
}
function Ht(e) {
  var t = -1, n = e == null ? 0 : e.length;
  for (this.clear(); ++t < n; ) {
    var o = e[t];
    this.set(o[0], o[1]);
  }
}
Ht.prototype.clear = nc;
Ht.prototype.delete = rc;
Ht.prototype.get = ac;
Ht.prototype.has = ic;
Ht.prototype.set = lc;
var sc = "Expected a function";
function ir(e, t) {
  if (typeof e != "function" || t != null && typeof t != "function")
    throw new TypeError(sc);
  var n = function() {
    var o = arguments, r = t ? t.apply(this, o) : o[0], a = n.cache;
    if (a.has(r))
      return a.get(r);
    var l = e.apply(this, o);
    return n.cache = a.set(r, l) || a, l;
  };
  return n.cache = new (ir.Cache || Ht)(), n;
}
ir.Cache = Ht;
var uc = 500;
function cc(e) {
  var t = ir(e, function(o) {
    return n.size === uc && n.clear(), o;
  }), n = t.cache;
  return t;
}
var dc = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g, fc = /\\(\\)?/g, pc = cc(function(e) {
  var t = [];
  return e.charCodeAt(0) === 46 && t.push(""), e.replace(dc, function(n, o, r, a) {
    t.push(r ? a.replace(fc, "$1") : o || n);
  }), t;
});
function mc(e) {
  return e == null ? "" : ti(e);
}
function io(e, t) {
  return Cn(e) ? e : Du(e, t) ? [e] : pc(mc(e));
}
function lr(e) {
  if (typeof e == "string" || rr(e))
    return e;
  var t = e + "";
  return t == "0" && 1 / e == -1 / 0 ? "-0" : t;
}
function ii(e, t) {
  t = io(t, e);
  for (var n = 0, o = t.length; e != null && n < o; )
    e = e[lr(t[n++])];
  return n && n == o ? e : void 0;
}
function hc(e, t, n) {
  var o = e == null ? void 0 : ii(e, t);
  return o === void 0 ? n : o;
}
function gc(e, t) {
  for (var n = -1, o = t.length, r = e.length; ++n < o; )
    e[r + n] = t[n];
  return e;
}
var Pr = wt ? wt.isConcatSpreadable : void 0;
function vc(e) {
  return Cn(e) || ai(e) || !!(Pr && e && e[Pr]);
}
function yc(e, t, n, o, r) {
  var a = -1, l = e.length;
  for (n || (n = vc), r || (r = []); ++a < l; ) {
    var s = e[a];
    n(s) ? gc(r, s) : r[r.length] = s;
  }
  return r;
}
function bc(e) {
  var t = e == null ? 0 : e.length;
  return t ? yc(e) : [];
}
function wc(e) {
  return xu(Iu(e, void 0, bc), e + "");
}
function xc(e, t) {
  return e != null && t in Object(e);
}
function Sc(e, t, n) {
  t = io(t, e);
  for (var o = -1, r = t.length, a = !1; ++o < r; ) {
    var l = lr(t[o]);
    if (!(a = e != null && n(e, l)))
      break;
    e = e[l];
  }
  return a || ++o != r ? a : (r = e == null ? 0 : e.length, !!r && Vu(r) && ni(l, r) && (Cn(e) || ai(e)));
}
function _c(e, t) {
  return e != null && Sc(e, t, xc);
}
function li(e) {
  for (var t = -1, n = e == null ? 0 : e.length, o = {}; ++t < n; ) {
    var r = e[t];
    o[r[0]] = r[1];
  }
  return o;
}
function si(e) {
  return e == null;
}
function Ec(e, t, n, o) {
  if (!Gn(e))
    return e;
  t = io(t, e);
  for (var r = -1, a = t.length, l = a - 1, s = e; s != null && ++r < a; ) {
    var u = lr(t[r]), i = n;
    if (u === "__proto__" || u === "constructor" || u === "prototype")
      return e;
    if (r != l) {
      var d = s[u];
      i = void 0, i === void 0 && (i = Gn(d) ? d : ni(t[r + 1]) ? [] : {});
    }
    Au(s, u, i), s = s[u];
  }
  return e;
}
function Cc(e, t, n) {
  for (var o = -1, r = t.length, a = {}; ++o < r; ) {
    var l = t[o], s = ii(e, l);
    n(s, l) && Ec(a, io(l, e), s);
  }
  return a;
}
function kc(e, t) {
  return Cc(e, t, function(n, o) {
    return _c(e, o);
  });
}
var Ac = wc(function(e, t) {
  return e == null ? {} : kc(e, t);
});
const ui = (e) => e === void 0, Lr = (e) => typeof e == "boolean", ut = (e) => typeof e == "number", Kt = (e) => typeof Element > "u" ? !1 : e instanceof Element, Ic = (e) => Fe(e) ? !Number.isNaN(Number(e)) : !1;
var Tc = Object.defineProperty, Vc = Object.defineProperties, Mc = Object.getOwnPropertyDescriptors, Dr = Object.getOwnPropertySymbols, Oc = Object.prototype.hasOwnProperty, zc = Object.prototype.propertyIsEnumerable, Rr = (e, t, n) => t in e ? Tc(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n, Pc = (e, t) => {
  for (var n in t || (t = {}))
    Oc.call(t, n) && Rr(e, n, t[n]);
  if (Dr)
    for (var n of Dr(t))
      zc.call(t, n) && Rr(e, n, t[n]);
  return e;
}, Lc = (e, t) => Vc(e, Mc(t));
function Dc(e, t) {
  var n;
  const o = fn();
  return Fa(() => {
    o.value = e();
  }, Lc(Pc({}, t), {
    flush: (n = void 0) != null ? n : "sync"
  })), Na(o);
}
var Br;
const Pe = typeof window < "u", Rc = (e) => typeof e == "string", Bc = () => {
};
Pe && ((Br = window == null ? void 0 : window.navigator) != null && Br.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);
function sr(e) {
  return typeof e == "function" ? e() : S(e);
}
function Nc(e) {
  return e;
}
function ur(e) {
  return $l() ? (Ha(e), !0) : !1;
}
function Hc(e, t = !0) {
  Ke() ? Te(e) : t ? e() : Ee(e);
}
function Fc(e, t, n = {}) {
  const {
    immediate: o = !0
  } = n, r = J(!1);
  let a = null;
  function l() {
    a && (clearTimeout(a), a = null);
  }
  function s() {
    r.value = !1, l();
  }
  function u(...i) {
    l(), r.value = !0, a = setTimeout(() => {
      r.value = !1, a = null, e(...i);
    }, sr(t));
  }
  return o && (r.value = !0, Pe && u()), ur(s), {
    isPending: Na(r),
    start: u,
    stop: s
  };
}
function ci(e) {
  var t;
  const n = sr(e);
  return (t = n == null ? void 0 : n.$el) != null ? t : n;
}
const di = Pe ? window : void 0;
function jt(...e) {
  let t, n, o, r;
  if (Rc(e[0]) || Array.isArray(e[0]) ? ([n, o, r] = e, t = di) : [t, n, o, r] = e, !t)
    return Bc;
  Array.isArray(n) || (n = [n]), Array.isArray(o) || (o = [o]);
  const a = [], l = () => {
    a.forEach((d) => d()), a.length = 0;
  }, s = (d, h, x, m) => (d.addEventListener(h, x, m), () => d.removeEventListener(h, x, m)), u = Z(() => [ci(t), sr(r)], ([d, h]) => {
    l(), d && a.push(...n.flatMap((x) => o.map((m) => s(d, x, m, h))));
  }, { immediate: !0, flush: "post" }), i = () => {
    u(), l();
  };
  return ur(i), i;
}
function $c(e, t = !1) {
  const n = J(), o = () => n.value = !!e();
  return o(), Hc(o, t), n;
}
const Nr = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {}, Hr = "__vueuse_ssr_handlers__";
Nr[Hr] = Nr[Hr] || {};
var Fr = Object.getOwnPropertySymbols, Uc = Object.prototype.hasOwnProperty, jc = Object.prototype.propertyIsEnumerable, Wc = (e, t) => {
  var n = {};
  for (var o in e)
    Uc.call(e, o) && t.indexOf(o) < 0 && (n[o] = e[o]);
  if (e != null && Fr)
    for (var o of Fr(e))
      t.indexOf(o) < 0 && jc.call(e, o) && (n[o] = e[o]);
  return n;
};
function fi(e, t, n = {}) {
  const o = n, { window: r = di } = o, a = Wc(o, ["window"]);
  let l;
  const s = $c(() => r && "ResizeObserver" in r), u = () => {
    l && (l.disconnect(), l = void 0);
  }, i = Z(() => ci(e), (h) => {
    u(), s.value && r && h && (l = new ResizeObserver(t), l.observe(h, a));
  }, { immediate: !0, flush: "post" }), d = () => {
    u(), i();
  };
  return ur(d), {
    isSupported: s,
    stop: d
  };
}
var $r;
(function(e) {
  e.UP = "UP", e.RIGHT = "RIGHT", e.DOWN = "DOWN", e.LEFT = "LEFT", e.NONE = "NONE";
})($r || ($r = {}));
var Gc = Object.defineProperty, Ur = Object.getOwnPropertySymbols, qc = Object.prototype.hasOwnProperty, Yc = Object.prototype.propertyIsEnumerable, jr = (e, t, n) => t in e ? Gc(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n, Zc = (e, t) => {
  for (var n in t || (t = {}))
    qc.call(t, n) && jr(e, n, t[n]);
  if (Ur)
    for (var n of Ur(t))
      Yc.call(t, n) && jr(e, n, t[n]);
  return e;
};
const Kc = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
Zc({
  linear: Nc
}, Kc);
class pi extends Error {
  constructor(t) {
    super(t), this.name = "ElementPlusError";
  }
}
function Xc(e, t) {
  throw new pi(`[${e}] ${t}`);
}
function qe(e, t) {
  if (process.env.NODE_ENV !== "production") {
    const n = Fe(e) ? new pi(`[${e}] ${t}`) : e;
    console.warn(n);
  }
}
const Wr = {
  current: 0
}, Gr = J(0), mi = 2e3, qr = Symbol("elZIndexContextKey"), hi = Symbol("zIndexContextKey"), Qc = (e) => {
  const t = Ke() ? xe(qr, Wr) : Wr, n = e || (Ke() ? xe(hi, void 0) : void 0), o = P(() => {
    const l = S(n);
    return ut(l) ? l : mi;
  }), r = P(() => o.value + Gr.value), a = () => (t.current++, Gr.value = t.current, r.value);
  return !Pe && !xe(qr) && qe("ZIndexInjection", `Looks like you are using server rendering, you must provide a z-index provider to ensure the hydration process to be succeed
usage: app.provide(ZINDEX_INJECTION_KEY, { current: 0 })`), {
    initialZIndex: o,
    currentZIndex: r,
    nextZIndex: a
  };
};
var Jc = {
  name: "en",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "Clear",
      defaultLabel: "color picker",
      description: "current color is {color}. press enter to select a new color.",
      alphaLabel: "pick alpha value"
    },
    datepicker: {
      now: "Now",
      today: "Today",
      cancel: "Cancel",
      clear: "Clear",
      confirm: "OK",
      dateTablePrompt: "Use the arrow keys and enter to select the day of the month",
      monthTablePrompt: "Use the arrow keys and enter to select the month",
      yearTablePrompt: "Use the arrow keys and enter to select the year",
      selectedDate: "Selected date",
      selectDate: "Select date",
      selectTime: "Select time",
      startDate: "Start Date",
      startTime: "Start Time",
      endDate: "End Date",
      endTime: "End Time",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "January",
      month2: "February",
      month3: "March",
      month4: "April",
      month5: "May",
      month6: "June",
      month7: "July",
      month8: "August",
      month9: "September",
      month10: "October",
      month11: "November",
      month12: "December",
      week: "week",
      weeks: {
        sun: "Sun",
        mon: "Mon",
        tue: "Tue",
        wed: "Wed",
        thu: "Thu",
        fri: "Fri",
        sat: "Sat"
      },
      weeksFull: {
        sun: "Sunday",
        mon: "Monday",
        tue: "Tuesday",
        wed: "Wednesday",
        thu: "Thursday",
        fri: "Friday",
        sat: "Saturday"
      },
      months: {
        jan: "Jan",
        feb: "Feb",
        mar: "Mar",
        apr: "Apr",
        may: "May",
        jun: "Jun",
        jul: "Jul",
        aug: "Aug",
        sep: "Sep",
        oct: "Oct",
        nov: "Nov",
        dec: "Dec"
      }
    },
    inputNumber: {
      decrease: "decrease number",
      increase: "increase number"
    },
    select: {
      loading: "Loading",
      noMatch: "No matching data",
      noData: "No data",
      placeholder: "Select"
    },
    mention: {
      loading: "Loading"
    },
    dropdown: {
      toggleDropdown: "Toggle Dropdown"
    },
    cascader: {
      noMatch: "No matching data",
      loading: "Loading",
      placeholder: "Select",
      noData: "No data"
    },
    pagination: {
      goto: "Go to",
      pagesize: "/page",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages",
      deprecationWarning: "Deprecated usages detected, please refer to the el-pagination documentation for more details"
    },
    dialog: {
      close: "Close this dialog"
    },
    drawer: {
      close: "Close this dialog"
    },
    messagebox: {
      title: "Message",
      confirm: "OK",
      cancel: "Cancel",
      error: "Illegal input",
      close: "Close this dialog"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Delete",
      preview: "Preview",
      continue: "Continue"
    },
    slider: {
      defaultLabel: "slider between {min} and {max}",
      defaultRangeStartLabel: "pick start value",
      defaultRangeEndLabel: "pick end value"
    },
    table: {
      emptyText: "No Data",
      confirmFilter: "Confirm",
      resetFilter: "Reset",
      clearFilter: "All",
      sumText: "Sum"
    },
    tour: {
      next: "Next",
      previous: "Previous",
      finish: "Finish"
    },
    tree: {
      emptyText: "No Data"
    },
    transfer: {
      noMatch: "No matching data",
      noData: "No data",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Enter keyword",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};
const ed = (e) => (t, n) => td(t, n, S(e)), td = (e, t, n) => hc(n, e, e).replace(/\{(\w+)\}/g, (o, r) => {
  var a;
  return `${(a = t == null ? void 0 : t[r]) != null ? a : `{${r}}`}`;
}), nd = (e) => {
  const t = P(() => S(e).name), n = $a(e) ? e : J(e);
  return {
    lang: t,
    locale: n,
    t: ed(e)
  };
}, gi = Symbol("localeContextKey"), od = (e) => {
  const t = e || xe(gi, J());
  return nd(P(() => t.value || Jc));
}, vi = "__epPropKey", Oe = (e) => e, rd = (e) => Zt(e) && !!e[vi], yi = (e, t) => {
  if (!Zt(e) || rd(e))
    return e;
  const { values: n, required: o, default: r, type: a, validator: l } = e, u = {
    type: a,
    required: !!o,
    validator: n || l ? (i) => {
      let d = !1, h = [];
      if (n && (h = Array.from(n), Wn(e, "default") && h.push(r), d || (d = h.includes(i))), l && (d || (d = l(i))), !d && h.length > 0) {
        const x = [...new Set(h)].map((m) => JSON.stringify(m)).join(", ");
        Ul(`Invalid prop: validation failed${t ? ` for prop "${t}"` : ""}. Expected one of [${x}], got value ${JSON.stringify(i)}.`);
      }
      return d;
    } : void 0,
    [vi]: !0
  };
  return Wn(e, "default") && (u.default = r), u;
}, dt = (e) => li(Object.entries(e).map(([t, n]) => [
  t,
  yi(n, t)
])), bi = ["", "default", "small", "large"], cr = yi({
  type: String,
  values: bi,
  required: !1
}), wi = Symbol("size"), ad = () => {
  const e = xe(wi, {});
  return P(() => S(e.size) || "");
}, id = Symbol("emptyValuesContextKey"), ld = dt({
  emptyValues: Array,
  valueOnClear: {
    type: [String, Number, Boolean, Function],
    default: void 0,
    validator: (e) => st(e) ? !e() : !e
  }
}), Yr = (e) => Object.keys(e), Yn = J();
function dr(e, t = void 0) {
  const n = Ke() ? xe(Xa, Yn) : Yn;
  return e ? P(() => {
    var o, r;
    return (r = (o = n.value) == null ? void 0 : o[e]) != null ? r : t;
  }) : n;
}
function xi(e, t) {
  const n = dr(), o = Xe(e, P(() => {
    var s;
    return ((s = n.value) == null ? void 0 : s.namespace) || Rn;
  })), r = od(P(() => {
    var s;
    return (s = n.value) == null ? void 0 : s.locale;
  })), a = Qc(P(() => {
    var s;
    return ((s = n.value) == null ? void 0 : s.zIndex) || mi;
  })), l = P(() => {
    var s;
    return S(t) || ((s = n.value) == null ? void 0 : s.size) || "";
  });
  return Si(P(() => S(n) || {})), {
    ns: o,
    locale: r,
    zIndex: a,
    size: l
  };
}
const Si = (e, t, n = !1) => {
  var o;
  const r = !!Ke(), a = r ? dr() : void 0, l = (o = void 0) != null ? o : r ? Ye : void 0;
  if (!l) {
    qe("provideGlobalConfig", "provideGlobalConfig() can only be used inside setup().");
    return;
  }
  const s = P(() => {
    const u = S(e);
    return a != null && a.value ? sd(a.value, u) : u;
  });
  return l(Xa, s), l(gi, P(() => s.value.locale)), l(Qa, P(() => s.value.namespace)), l(hi, P(() => s.value.zIndex)), l(wi, {
    size: P(() => s.value.size || "")
  }), l(id, P(() => ({
    emptyValues: s.value.emptyValues,
    valueOnClear: s.value.valueOnClear
  }))), (n || !Yn.value) && (Yn.value = s.value), s;
}, sd = (e, t) => {
  const n = [.../* @__PURE__ */ new Set([...Yr(e), ...Yr(t)])], o = {};
  for (const r of n)
    o[r] = t[r] !== void 0 ? t[r] : e[r];
  return o;
}, Po = "update:modelValue";
var St = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
};
const ud = "utils/dom/style", _i = (e = "") => e.split(" ").filter((t) => !!t.trim()), Zr = (e, t) => {
  if (!e || !t)
    return !1;
  if (t.includes(" "))
    throw new Error("className should not contain space.");
  return e.classList.contains(t);
}, cd = (e, t) => {
  !e || !t.trim() || e.classList.add(..._i(t));
}, dd = (e, t) => {
  !e || !t.trim() || e.classList.remove(..._i(t));
}, fd = (e, t) => {
  var n;
  if (!Pe || !e)
    return "";
  let o = Ds(t);
  o === "float" && (o = "cssFloat");
  try {
    const r = e.style[o];
    if (r)
      return r;
    const a = (n = document.defaultView) == null ? void 0 : n.getComputedStyle(e, "");
    return a ? a[o] : "";
  } catch {
    return e.style[o];
  }
};
function yn(e, t = "px") {
  if (!e)
    return "";
  if (ut(e) || Ic(e))
    return `${e}${t}`;
  if (Fe(e))
    return e;
  qe(ud, "binding value must be a string or number");
}
let In;
const pd = (e) => {
  var t;
  if (!Pe)
    return 0;
  if (In !== void 0)
    return In;
  const n = document.createElement("div");
  n.className = `${e}-scrollbar__wrap`, n.style.visibility = "hidden", n.style.width = "100px", n.style.position = "absolute", n.style.top = "-9999px", document.body.appendChild(n);
  const o = n.offsetWidth;
  n.style.overflow = "scroll";
  const r = document.createElement("div");
  r.style.width = "100%", n.appendChild(r);
  const a = r.offsetWidth;
  return (t = n.parentNode) == null || t.removeChild(n), In = o - a, In;
}, lo = (e, t) => {
  if (e.install = (n) => {
    for (const o of [e, ...Object.values(t ?? {})])
      n.component(o.name, o);
  }, t)
    for (const [n, o] of Object.entries(t))
      e[n] = o;
  return e;
}, md = (e, t) => (e.install = (n) => {
  e._context = n._context, n.config.globalProperties[t] = e;
}, e), hd = (e) => (e.install = pn, e), gd = dt({
  size: {
    type: Oe([Number, String])
  },
  color: {
    type: String
  }
}), vd = ee({
  name: "ElIcon",
  inheritAttrs: !1
}), yd = /* @__PURE__ */ ee({
  ...vd,
  props: gd,
  setup(e) {
    const t = e, n = Xe("icon"), o = P(() => {
      const { size: r, color: a } = t;
      return !r && !a ? {} : {
        fontSize: ui(r) ? void 0 : yn(r),
        "--color": a
      };
    });
    return (r, a) => (E(), F("i", lt({
      class: S(n).b(),
      style: S(o)
    }, r.$attrs), [
      ye(r.$slots, "default")
    ], 16));
  }
});
var bd = /* @__PURE__ */ St(yd, [["__file", "icon.vue"]]);
const Ze = lo(bd);
/*! Element Plus Icons Vue v2.3.1 */
var wd = /* @__PURE__ */ ee({
  name: "Checked",
  __name: "checked",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M704 192h160v736H160V192h160.064v64H704zM311.616 537.28l-45.312 45.248L447.36 763.52l316.8-316.8-45.312-45.184L447.36 673.024zM384 192V96h256v96z"
      })
    ]));
  }
}), xd = wd, Sd = /* @__PURE__ */ ee({
  name: "CircleCheck",
  __name: "circle-check",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"
      }),
      B("path", {
        fill: "currentColor",
        d: "M745.344 361.344a32 32 0 0 1 45.312 45.312l-288 288a32 32 0 0 1-45.312 0l-160-160a32 32 0 1 1 45.312-45.312L480 626.752l265.344-265.408z"
      })
    ]));
  }
}), _d = Sd, Ed = /* @__PURE__ */ ee({
  name: "CircleCloseFilled",
  __name: "circle-close-filled",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336z"
      })
    ]));
  }
}), Ei = Ed, Cd = /* @__PURE__ */ ee({
  name: "CircleClose",
  __name: "circle-close",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248z"
      }),
      B("path", {
        fill: "currentColor",
        d: "M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"
      })
    ]));
  }
}), so = Cd, kd = /* @__PURE__ */ ee({
  name: "Close",
  __name: "close",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"
      })
    ]));
  }
}), Ci = kd, Ad = /* @__PURE__ */ ee({
  name: "Grid",
  __name: "grid",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M640 384v256H384V384zm64 0h192v256H704zm-64 512H384V704h256zm64 0V704h192v192zm-64-768v192H384V128zm64 0h192v192H704zM320 384v256H128V384zm0 512H128V704h192zm0-768v192H128V128z"
      })
    ]));
  }
}), Id = Ad, Td = /* @__PURE__ */ ee({
  name: "Hide",
  __name: "hide",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M876.8 156.8c0-9.6-3.2-16-9.6-22.4-6.4-6.4-12.8-9.6-22.4-9.6-9.6 0-16 3.2-22.4 9.6L736 220.8c-64-32-137.6-51.2-224-60.8-160 16-288 73.6-377.6 176C44.8 438.4 0 496 0 512s48 73.6 134.4 176c22.4 25.6 44.8 48 73.6 67.2l-86.4 89.6c-6.4 6.4-9.6 12.8-9.6 22.4 0 9.6 3.2 16 9.6 22.4 6.4 6.4 12.8 9.6 22.4 9.6 9.6 0 16-3.2 22.4-9.6l704-710.4c3.2-6.4 6.4-12.8 6.4-22.4Zm-646.4 528c-76.8-70.4-128-128-153.6-172.8 28.8-48 80-105.6 153.6-172.8C304 272 400 230.4 512 224c64 3.2 124.8 19.2 176 44.8l-54.4 54.4C598.4 300.8 560 288 512 288c-64 0-115.2 22.4-160 64s-64 96-64 160c0 48 12.8 89.6 35.2 124.8L256 707.2c-9.6-6.4-19.2-16-25.6-22.4Zm140.8-96c-12.8-22.4-19.2-48-19.2-76.8 0-44.8 16-83.2 48-112 32-28.8 67.2-48 112-48 28.8 0 54.4 6.4 73.6 19.2zM889.599 336c-12.8-16-28.8-28.8-41.6-41.6l-48 48c73.6 67.2 124.8 124.8 150.4 169.6-28.8 48-80 105.6-153.6 172.8-73.6 67.2-172.8 108.8-284.8 115.2-51.2-3.2-99.2-12.8-140.8-28.8l-48 48c57.6 22.4 118.4 38.4 188.8 44.8 160-16 288-73.6 377.6-176C979.199 585.6 1024 528 1024 512s-48.001-73.6-134.401-176Z"
      }),
      B("path", {
        fill: "currentColor",
        d: "M511.998 672c-12.8 0-25.6-3.2-38.4-6.4l-51.2 51.2c28.8 12.8 57.6 19.2 89.6 19.2 64 0 115.2-22.4 160-64 41.6-41.6 64-96 64-160 0-32-6.4-64-19.2-89.6l-51.2 51.2c3.2 12.8 6.4 25.6 6.4 38.4 0 44.8-16 83.2-48 112-32 28.8-67.2 48-112 48Z"
      })
    ]));
  }
}), Vd = Td, Md = /* @__PURE__ */ ee({
  name: "InfoFilled",
  __name: "info-filled",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64m67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344M590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.992 12.992 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296-44.096 0-108.992 44.736-148.48 101.504 0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04 67.84 0 107.904-43.648 147.456-100.416z"
      })
    ]));
  }
}), ki = Md, Od = /* @__PURE__ */ ee({
  name: "Loading",
  __name: "loading",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32m0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32m448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32m-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32M195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248zm452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248zM828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0m-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0z"
      })
    ]));
  }
}), Zn = Od, zd = /* @__PURE__ */ ee({
  name: "Plus",
  __name: "plus",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z"
      })
    ]));
  }
}), fr = zd, Pd = /* @__PURE__ */ ee({
  name: "QuestionFilled",
  __name: "question-filled",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592 0-42.944-14.08-76.736-42.24-101.376-28.16-25.344-65.472-37.312-111.232-37.312zm-12.672 406.208a54.272 54.272 0 0 0-38.72 14.784 49.408 49.408 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.848 54.848 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.968 51.968 0 0 0-15.488-38.016 55.936 55.936 0 0 0-39.424-14.784z"
      })
    ]));
  }
}), Kr = Pd, Ld = /* @__PURE__ */ ee({
  name: "Refresh",
  __name: "refresh",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z"
      })
    ]));
  }
}), Dd = Ld, Rd = /* @__PURE__ */ ee({
  name: "Remove",
  __name: "remove",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64"
      }),
      B("path", {
        fill: "currentColor",
        d: "M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"
      })
    ]));
  }
}), Bd = Rd, Nd = /* @__PURE__ */ ee({
  name: "Search",
  __name: "search",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"
      })
    ]));
  }
}), Ai = Nd, Hd = /* @__PURE__ */ ee({
  name: "Setting",
  __name: "setting",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357.12 357.12 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a351.616 351.616 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357.12 357.12 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088-24.512 11.968a294.113 294.113 0 0 0-34.816 20.096l-22.656 15.36-116.224-25.088-65.28 113.152 79.68 88.192-1.92 27.136a293.12 293.12 0 0 0 0 40.192l1.92 27.136-79.808 88.192 65.344 113.152 116.224-25.024 22.656 15.296a294.113 294.113 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152 24.448-11.904a288.282 288.282 0 0 0 34.752-20.096l22.592-15.296 116.288 25.024 65.28-113.152-79.744-88.192 1.92-27.136a293.12 293.12 0 0 0 0-40.256l-1.92-27.136 79.808-88.128-65.344-113.152-116.288 24.96-22.592-15.232a287.616 287.616 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384m0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256"
      })
    ]));
  }
}), wo = Hd, Fd = /* @__PURE__ */ ee({
  name: "SuccessFilled",
  __name: "success-filled",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z"
      })
    ]));
  }
}), Ii = Fd, $d = /* @__PURE__ */ ee({
  name: "Upload",
  __name: "upload",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248z"
      })
    ]));
  }
}), Ud = $d, jd = /* @__PURE__ */ ee({
  name: "View",
  __name: "view",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352m0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288zm0 64a224 224 0 1 1 0 448 224 224 0 0 1 0-448m0 64a160.192 160.192 0 0 0-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160"
      })
    ]));
  }
}), Wd = jd, Gd = /* @__PURE__ */ ee({
  name: "WarningFilled",
  __name: "warning-filled",
  setup(e) {
    return (t, n) => (E(), F("svg", {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 1024 1024"
    }, [
      B("path", {
        fill: "currentColor",
        d: "M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 192a58.432 58.432 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.432 58.432 0 0 0 512 256m0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4"
      })
    ]));
  }
}), Ti = Gd;
const bn = Oe([
  String,
  Object,
  Function
]), Vi = {
  Close: Ci,
  SuccessFilled: Ii,
  InfoFilled: ki,
  WarningFilled: Ti,
  CircleCloseFilled: Ei
}, Kn = {
  success: Ii,
  warning: Ti,
  error: Ei,
  info: ki
}, qd = {
  validating: Zn,
  success: _d,
  error: so
}, Yd = () => Pe && /firefox/i.test(window.navigator.userAgent);
let De;
const Zd = {
  height: "0",
  visibility: "hidden",
  overflow: Yd() ? "" : "hidden",
  position: "absolute",
  "z-index": "-1000",
  top: "0",
  right: "0"
}, Kd = [
  "letter-spacing",
  "line-height",
  "padding-top",
  "padding-bottom",
  "font-family",
  "font-weight",
  "font-size",
  "text-rendering",
  "text-transform",
  "width",
  "text-indent",
  "padding-left",
  "padding-right",
  "border-width",
  "box-sizing"
];
function Xd(e) {
  const t = window.getComputedStyle(e), n = t.getPropertyValue("box-sizing"), o = Number.parseFloat(t.getPropertyValue("padding-bottom")) + Number.parseFloat(t.getPropertyValue("padding-top")), r = Number.parseFloat(t.getPropertyValue("border-bottom-width")) + Number.parseFloat(t.getPropertyValue("border-top-width"));
  return { contextStyle: Kd.map((l) => [
    l,
    t.getPropertyValue(l)
  ]), paddingSize: o, borderSize: r, boxSizing: n };
}
function Xr(e, t = 1, n) {
  var o;
  De || (De = document.createElement("textarea"), document.body.appendChild(De));
  const { paddingSize: r, borderSize: a, boxSizing: l, contextStyle: s } = Xd(e);
  s.forEach(([h, x]) => De == null ? void 0 : De.style.setProperty(h, x)), Object.entries(Zd).forEach(([h, x]) => De == null ? void 0 : De.style.setProperty(h, x, "important")), De.value = e.value || e.placeholder || "";
  let u = De.scrollHeight;
  const i = {};
  l === "border-box" ? u = u + a : l === "content-box" && (u = u - r), De.value = "";
  const d = De.scrollHeight - r;
  if (ut(t)) {
    let h = d * t;
    l === "border-box" && (h = h + r + a), u = Math.max(h, u), i.minHeight = `${h}px`;
  }
  if (ut(n)) {
    let h = d * n;
    l === "border-box" && (h = h + r + a), u = Math.min(h, u);
  }
  return i.height = `${u}px`, (o = De.parentNode) == null || o.removeChild(De), De = void 0, i;
}
const Mi = (e) => e, Qd = dt({
  ariaLabel: String,
  ariaOrientation: {
    type: String,
    values: ["horizontal", "vertical", "undefined"]
  },
  ariaControls: String
}), Jd = (e) => Ac(Qd, e), ef = dt({
  id: {
    type: String,
    default: void 0
  },
  size: cr,
  disabled: Boolean,
  modelValue: {
    type: Oe([
      String,
      Number,
      Object
    ]),
    default: ""
  },
  maxlength: {
    type: [String, Number]
  },
  minlength: {
    type: [String, Number]
  },
  type: {
    type: String,
    default: "text"
  },
  resize: {
    type: String,
    values: ["none", "both", "horizontal", "vertical"]
  },
  autosize: {
    type: Oe([Boolean, Object]),
    default: !1
  },
  autocomplete: {
    type: String,
    default: "off"
  },
  formatter: {
    type: Function
  },
  parser: {
    type: Function
  },
  placeholder: {
    type: String
  },
  form: {
    type: String
  },
  readonly: Boolean,
  clearable: Boolean,
  showPassword: Boolean,
  showWordLimit: Boolean,
  suffixIcon: {
    type: bn
  },
  prefixIcon: {
    type: bn
  },
  containerRole: {
    type: String,
    default: void 0
  },
  tabindex: {
    type: [String, Number],
    default: 0
  },
  validateEvent: {
    type: Boolean,
    default: !0
  },
  inputStyle: {
    type: Oe([Object, Array, String]),
    default: () => Mi({})
  },
  autofocus: Boolean,
  rows: {
    type: Number,
    default: 2
  },
  ...Jd(["ariaLabel"])
}), tf = {
  [Po]: (e) => Fe(e),
  input: (e) => Fe(e),
  change: (e) => Fe(e),
  focus: (e) => e instanceof FocusEvent,
  blur: (e) => e instanceof FocusEvent,
  clear: () => !0,
  mouseleave: (e) => e instanceof MouseEvent,
  mouseenter: (e) => e instanceof MouseEvent,
  keydown: (e) => e instanceof Event,
  compositionstart: (e) => e instanceof CompositionEvent,
  compositionupdate: (e) => e instanceof CompositionEvent,
  compositionend: (e) => e instanceof CompositionEvent
}, nf = ["class", "style"], of = /^on[A-Z]/, rf = (e = {}) => {
  const { excludeListeners: t = !1, excludeKeys: n } = e, o = P(() => ((n == null ? void 0 : n.value) || []).concat(nf)), r = Ke();
  return r ? P(() => {
    var a;
    return li(Object.entries((a = r.proxy) == null ? void 0 : a.$attrs).filter(([l]) => !o.value.includes(l) && !(t && of.test(l))));
  }) : (qe("use-attrs", "getCurrentInstance() returned null. useAttrs() must be called at the top of a setup function"), P(() => ({})));
}, pr = Symbol("formContextKey"), Oi = Symbol("formItemContextKey"), Lo = {
  prefix: Math.floor(Math.random() * 1e4),
  current: 0
}, af = Symbol("elIdInjection"), lf = () => Ke() ? xe(af, Lo) : Lo, Do = (e) => {
  const t = lf();
  !Pe && t === Lo && qe("IdInjection", `Looks like you are using server rendering, you must provide a id provider to ensure the hydration process to be succeed
usage: app.provide(ID_INJECTION_KEY, {
  prefix: number,
  current: number,
})`);
  const n = Ja();
  return Dc(() => S(e) || `${n.value}-id-${t.prefix}-${t.current++}`);
}, zi = () => {
  const e = xe(pr, void 0), t = xe(Oi, void 0);
  return {
    form: e,
    formItem: t
  };
}, sf = (e, {
  formItemContext: t,
  disableIdGeneration: n,
  disableIdManagement: o
}) => {
  n || (n = J(!1)), o || (o = J(!1));
  const r = J();
  let a;
  const l = P(() => {
    var s;
    return !!(!(e.label || e.ariaLabel) && t && t.inputIds && ((s = t.inputIds) == null ? void 0 : s.length) <= 1);
  });
  return Te(() => {
    a = Z([Fn(e, "id"), n], ([s, u]) => {
      const i = s ?? (u ? void 0 : Do().value);
      i !== r.value && (t != null && t.removeInputId && (r.value && t.removeInputId(r.value), !(o != null && o.value) && !u && i && t.addInputId(i)), r.value = i);
    }, { immediate: !0 });
  }), jl(() => {
    a && a(), t != null && t.removeInputId && r.value && t.removeInputId(r.value);
  }), {
    isLabeledByFormItem: l,
    inputId: r
  };
}, Pi = (e) => {
  const t = Ke();
  return P(() => {
    var n, o;
    return (o = (n = t == null ? void 0 : t.proxy) == null ? void 0 : n.$props) == null ? void 0 : o[e];
  });
}, Li = (e, t = {}) => {
  const n = J(void 0), o = t.prop ? n : Pi("size"), r = t.global ? n : ad(), a = t.form ? { size: void 0 } : xe(pr, void 0), l = t.formItem ? { size: void 0 } : xe(Oi, void 0);
  return P(() => o.value || S(e) || (l == null ? void 0 : l.size) || (a == null ? void 0 : a.size) || r.value || "");
}, mr = (e) => {
  const t = Pi("disabled"), n = xe(pr, void 0);
  return P(() => t.value || S(e) || (n == null ? void 0 : n.disabled) || !1);
};
function uf(e, {
  beforeFocus: t,
  afterFocus: n,
  beforeBlur: o,
  afterBlur: r
} = {}) {
  const a = Ke(), { emit: l } = a, s = fn(), u = J(!1), i = (x) => {
    st(t) && t(x) || u.value || (u.value = !0, l("focus", x), n == null || n());
  }, d = (x) => {
    var m;
    st(o) && o(x) || x.relatedTarget && ((m = s.value) != null && m.contains(x.relatedTarget)) || (u.value = !1, l("blur", x), r == null || r());
  }, h = () => {
    var x, m;
    (x = s.value) != null && x.contains(document.activeElement) && s.value !== document.activeElement || (m = e.value) == null || m.focus();
  };
  return Z(s, (x) => {
    x && x.setAttribute("tabindex", "-1");
  }), jt(s, "focus", i, !0), jt(s, "blur", d, !0), jt(s, "click", h, !0), process.env.NODE_ENV === "test" && Te(() => {
    const x = Kt(e.value) ? e.value : document.querySelector("input,textarea");
    x && (jt(x, "focus", i, !0), jt(x, "blur", d, !0));
  }), {
    isFocused: u,
    wrapperRef: s,
    handleFocus: i,
    handleBlur: d
  };
}
const cf = (e) => /([\uAC00-\uD7AF\u3130-\u318F])+/gi.test(e);
function df({
  afterComposition: e,
  emit: t
}) {
  const n = J(!1), o = (s) => {
    t == null || t("compositionstart", s), n.value = !0;
  }, r = (s) => {
    var u;
    t == null || t("compositionupdate", s);
    const i = (u = s.target) == null ? void 0 : u.value, d = i[i.length - 1] || "";
    n.value = !cf(d);
  }, a = (s) => {
    t == null || t("compositionend", s), n.value && (n.value = !1, Ee(() => e(s)));
  };
  return {
    isComposing: n,
    handleComposition: (s) => {
      s.type === "compositionend" ? a(s) : r(s);
    },
    handleCompositionStart: o,
    handleCompositionUpdate: r,
    handleCompositionEnd: a
  };
}
function ff(e) {
  let t;
  function n() {
    if (e.value == null)
      return;
    const { selectionStart: r, selectionEnd: a, value: l } = e.value;
    if (r == null || a == null)
      return;
    const s = l.slice(0, Math.max(0, r)), u = l.slice(Math.max(0, a));
    t = {
      selectionStart: r,
      selectionEnd: a,
      value: l,
      beforeTxt: s,
      afterTxt: u
    };
  }
  function o() {
    if (e.value == null || t == null)
      return;
    const { value: r } = e.value, { beforeTxt: a, afterTxt: l, selectionStart: s } = t;
    if (a == null || l == null || s == null)
      return;
    let u = r.length;
    if (r.endsWith(l))
      u = r.length - l.length;
    else if (r.startsWith(a))
      u = a.length;
    else {
      const i = a[s - 1], d = r.indexOf(i, s - 1);
      d !== -1 && (u = d + 1);
    }
    e.value.setSelectionRange(u, u);
  }
  return [n, o];
}
const pf = ee({
  name: "ElInput",
  inheritAttrs: !1
}), mf = /* @__PURE__ */ ee({
  ...pf,
  props: ef,
  emits: tf,
  setup(e, { expose: t, emit: n }) {
    const o = e, r = Wl(), a = rf(), l = Ua(), s = P(() => [
      o.type === "textarea" ? v.b() : f.b(),
      f.m(x.value),
      f.is("disabled", m.value),
      f.is("exceed", z.value),
      {
        [f.b("group")]: l.prepend || l.append,
        [f.m("prefix")]: l.prefix || o.prefixIcon,
        [f.m("suffix")]: l.suffix || o.suffixIcon || o.clearable || o.showPassword,
        [f.bm("suffix", "password-clear")]: ie.value && oe.value,
        [f.b("hidden")]: o.type === "hidden"
      },
      r.class
    ]), u = P(() => [
      f.e("wrapper"),
      f.is("focus", T.value)
    ]), { form: i, formItem: d } = zi(), { inputId: h } = sf(o, {
      formItemContext: d
    }), x = Li(), m = mr(), f = Xe("input"), v = Xe("textarea"), C = fn(), A = fn(), O = J(!1), I = J(!1), k = J(), g = fn(o.inputStyle), b = P(() => C.value || A.value), { wrapperRef: w, isFocused: T, handleFocus: _, handleBlur: L } = uf(b, {
      beforeFocus() {
        return m.value;
      },
      afterBlur() {
        var U;
        o.validateEvent && ((U = d == null ? void 0 : d.validate) == null || U.call(d, "blur").catch((de) => qe(de)));
      }
    }), M = P(() => {
      var U;
      return (U = i == null ? void 0 : i.statusIcon) != null ? U : !1;
    }), y = P(() => (d == null ? void 0 : d.validateState) || ""), $ = P(() => y.value && qd[y.value]), Y = P(() => I.value ? Wd : Vd), se = P(() => [
      r.style
    ]), V = P(() => [
      o.inputStyle,
      g.value,
      { resize: o.resize }
    ]), te = P(() => si(o.modelValue) ? "" : String(o.modelValue)), ie = P(() => o.clearable && !m.value && !o.readonly && !!te.value && (T.value || O.value)), oe = P(() => o.showPassword && !m.value && !!te.value && (!!te.value || T.value)), ge = P(() => o.showWordLimit && !!o.maxlength && (o.type === "text" || o.type === "textarea") && !m.value && !o.readonly && !o.showPassword), $e = P(() => te.value.length), z = P(() => !!ge.value && $e.value > Number(o.maxlength)), X = P(() => !!l.suffix || !!o.suffixIcon || ie.value || o.showPassword || ge.value || !!y.value && M.value), [j, ce] = ff(C);
    fi(A, (U) => {
      if (le(), !ge.value || o.resize !== "both")
        return;
      const de = U[0], { width: et } = de.contentRect;
      k.value = {
        right: `calc(100% - ${et + 15 + 6}px)`
      };
    });
    const N = () => {
      const { type: U, autosize: de } = o;
      if (!(!Pe || U !== "textarea" || !A.value))
        if (de) {
          const et = Zt(de) ? de.minRows : void 0, on = Zt(de) ? de.maxRows : void 0, we = Xr(A.value, et, on);
          g.value = {
            overflowY: "hidden",
            ...we
          }, Ee(() => {
            A.value.offsetHeight, g.value = we;
          });
        } else
          g.value = {
            minHeight: Xr(A.value).minHeight
          };
    }, le = ((U) => {
      let de = !1;
      return () => {
        var et;
        if (de || !o.autosize)
          return;
        ((et = A.value) == null ? void 0 : et.offsetParent) === null || (U(), de = !0);
      };
    })(N), H = () => {
      const U = b.value, de = o.formatter ? o.formatter(te.value) : te.value;
      !U || U.value === de || (U.value = de);
    }, re = async (U) => {
      j();
      let { value: de } = U.target;
      if (o.formatter && o.parser && (de = o.parser(de)), !Se.value) {
        if (de === te.value) {
          H();
          return;
        }
        n(Po, de), n("input", de), await Ee(), H(), ce();
      }
    }, ue = (U) => {
      let { value: de } = U.target;
      o.formatter && o.parser && (de = o.parser(de)), n("change", de);
    }, {
      isComposing: Se,
      handleCompositionStart: Ce,
      handleCompositionUpdate: ke,
      handleCompositionEnd: Be
    } = df({ emit: n, afterComposition: re }), ze = () => {
      j(), I.value = !I.value, setTimeout(ce);
    }, fe = () => {
      var U;
      return (U = b.value) == null ? void 0 : U.focus();
    }, Le = () => {
      var U;
      return (U = b.value) == null ? void 0 : U.blur();
    }, Je = (U) => {
      O.value = !1, n("mouseleave", U);
    }, Ue = (U) => {
      O.value = !0, n("mouseenter", U);
    }, He = (U) => {
      n("keydown", U);
    }, Ft = () => {
      var U;
      (U = b.value) == null || U.select();
    }, nn = () => {
      n(Po, ""), n("change", ""), n("clear"), n("input", "");
    };
    return Z(() => o.modelValue, () => {
      var U;
      Ee(() => N()), o.validateEvent && ((U = d == null ? void 0 : d.validate) == null || U.call(d, "change").catch((de) => qe(de)));
    }), Z(te, () => H()), Z(() => o.type, async () => {
      await Ee(), H(), N();
    }), Te(() => {
      !o.formatter && o.parser && qe("ElInput", "If you set the parser, you also need to set the formatter."), H(), Ee(N);
    }), t({
      input: C,
      textarea: A,
      ref: b,
      textareaStyle: V,
      autosize: Fn(o, "autosize"),
      isComposing: Se,
      focus: fe,
      blur: Le,
      select: Ft,
      clear: nn,
      resizeTextarea: N
    }), (U, de) => (E(), F("div", {
      class: K([
        S(s),
        {
          [S(f).bm("group", "append")]: U.$slots.append,
          [S(f).bm("group", "prepend")]: U.$slots.prepend
        }
      ]),
      style: he(S(se)),
      onMouseenter: Ue,
      onMouseleave: Je
    }, [
      q(" input "),
      U.type !== "textarea" ? (E(), F(me, { key: 0 }, [
        q(" prepend slot "),
        U.$slots.prepend ? (E(), F("div", {
          key: 0,
          class: K(S(f).be("group", "prepend"))
        }, [
          ye(U.$slots, "prepend")
        ], 2)) : q("v-if", !0),
        B("div", {
          ref_key: "wrapperRef",
          ref: w,
          class: K(S(u))
        }, [
          q(" prefix slot "),
          U.$slots.prefix || U.prefixIcon ? (E(), F("span", {
            key: 0,
            class: K(S(f).e("prefix"))
          }, [
            B("span", {
              class: K(S(f).e("prefix-inner"))
            }, [
              ye(U.$slots, "prefix"),
              U.prefixIcon ? (E(), W(S(Ze), {
                key: 0,
                class: K(S(f).e("icon"))
              }, {
                default: p(() => [
                  (E(), W(Me(U.prefixIcon)))
                ]),
                _: 1
              }, 8, ["class"])) : q("v-if", !0)
            ], 2)
          ], 2)) : q("v-if", !0),
          B("input", lt({
            id: S(h),
            ref_key: "input",
            ref: C,
            class: S(f).e("inner")
          }, S(a), {
            minlength: U.minlength,
            maxlength: U.maxlength,
            type: U.showPassword ? I.value ? "text" : "password" : U.type,
            disabled: S(m),
            readonly: U.readonly,
            autocomplete: U.autocomplete,
            tabindex: U.tabindex,
            "aria-label": U.ariaLabel,
            placeholder: U.placeholder,
            style: U.inputStyle,
            form: U.form,
            autofocus: U.autofocus,
            role: U.containerRole,
            onCompositionstart: S(Ce),
            onCompositionupdate: S(ke),
            onCompositionend: S(Be),
            onInput: re,
            onChange: ue,
            onKeydown: He
          }), null, 16, ["id", "minlength", "maxlength", "type", "disabled", "readonly", "autocomplete", "tabindex", "aria-label", "placeholder", "form", "autofocus", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend"]),
          q(" suffix slot "),
          S(X) ? (E(), F("span", {
            key: 1,
            class: K(S(f).e("suffix"))
          }, [
            B("span", {
              class: K(S(f).e("suffix-inner"))
            }, [
              !S(ie) || !S(oe) || !S(ge) ? (E(), F(me, { key: 0 }, [
                ye(U.$slots, "suffix"),
                U.suffixIcon ? (E(), W(S(Ze), {
                  key: 0,
                  class: K(S(f).e("icon"))
                }, {
                  default: p(() => [
                    (E(), W(Me(U.suffixIcon)))
                  ]),
                  _: 1
                }, 8, ["class"])) : q("v-if", !0)
              ], 64)) : q("v-if", !0),
              S(ie) ? (E(), W(S(Ze), {
                key: 1,
                class: K([S(f).e("icon"), S(f).e("clear")]),
                onMousedown: rt(S(pn), ["prevent"]),
                onClick: nn
              }, {
                default: p(() => [
                  c(S(so))
                ]),
                _: 1
              }, 8, ["class", "onMousedown"])) : q("v-if", !0),
              S(oe) ? (E(), W(S(Ze), {
                key: 2,
                class: K([S(f).e("icon"), S(f).e("password")]),
                onClick: ze
              }, {
                default: p(() => [
                  (E(), W(Me(S(Y))))
                ]),
                _: 1
              }, 8, ["class"])) : q("v-if", !0),
              S(ge) ? (E(), F("span", {
                key: 3,
                class: K(S(f).e("count"))
              }, [
                B("span", {
                  class: K(S(f).e("count-inner"))
                }, pe(S($e)) + " / " + pe(U.maxlength), 3)
              ], 2)) : q("v-if", !0),
              S(y) && S($) && S(M) ? (E(), W(S(Ze), {
                key: 4,
                class: K([
                  S(f).e("icon"),
                  S(f).e("validateIcon"),
                  S(f).is("loading", S(y) === "validating")
                ])
              }, {
                default: p(() => [
                  (E(), W(Me(S($))))
                ]),
                _: 1
              }, 8, ["class"])) : q("v-if", !0)
            ], 2)
          ], 2)) : q("v-if", !0)
        ], 2),
        q(" append slot "),
        U.$slots.append ? (E(), F("div", {
          key: 1,
          class: K(S(f).be("group", "append"))
        }, [
          ye(U.$slots, "append")
        ], 2)) : q("v-if", !0)
      ], 64)) : (E(), F(me, { key: 1 }, [
        q(" textarea "),
        B("textarea", lt({
          id: S(h),
          ref_key: "textarea",
          ref: A,
          class: [S(v).e("inner"), S(f).is("focus", S(T))]
        }, S(a), {
          minlength: U.minlength,
          maxlength: U.maxlength,
          tabindex: U.tabindex,
          disabled: S(m),
          readonly: U.readonly,
          autocomplete: U.autocomplete,
          style: S(V),
          "aria-label": U.ariaLabel,
          placeholder: U.placeholder,
          form: U.form,
          autofocus: U.autofocus,
          rows: U.rows,
          role: U.containerRole,
          onCompositionstart: S(Ce),
          onCompositionupdate: S(ke),
          onCompositionend: S(Be),
          onInput: re,
          onFocus: S(_),
          onBlur: S(L),
          onChange: ue,
          onKeydown: He
        }), null, 16, ["id", "minlength", "maxlength", "tabindex", "disabled", "readonly", "autocomplete", "aria-label", "placeholder", "form", "autofocus", "rows", "role", "onCompositionstart", "onCompositionupdate", "onCompositionend", "onFocus", "onBlur"]),
        S(ge) ? (E(), F("span", {
          key: 0,
          style: he(k.value),
          class: K(S(f).e("count"))
        }, pe(S($e)) + " / " + pe(U.maxlength), 7)) : q("v-if", !0)
      ], 64))
    ], 38));
  }
});
var hf = /* @__PURE__ */ St(mf, [["__file", "input.vue"]]);
const gf = lo(hf), vf = 'a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])', yf = (e) => process.env.NODE_ENV === "test" ? !0 : getComputedStyle(e).position === "fixed" ? !1 : e.offsetParent !== null, Qr = (e) => Array.from(e.querySelectorAll(vf)).filter((t) => Di(t) && yf(t)), Di = (e) => {
  if (e.tabIndex > 0 || e.tabIndex === 0 && e.getAttribute("tabIndex") !== null)
    return !0;
  if (e.tabIndex < 0 || e.hasAttribute("disabled") || e.getAttribute("aria-disabled") === "true")
    return !1;
  switch (e.nodeName) {
    case "A":
      return !!e.href && e.rel !== "ignore";
    case "INPUT":
      return !(e.type === "hidden" || e.type === "file");
    case "BUTTON":
    case "SELECT":
    case "TEXTAREA":
      return !0;
    default:
      return !1;
  }
}, xo = "focus-trap.focus-after-trapped", So = "focus-trap.focus-after-released", bf = "focus-trap.focusout-prevented", Jr = {
  cancelable: !0,
  bubbles: !1
}, wf = {
  cancelable: !0,
  bubbles: !1
}, ea = "focusAfterTrapped", ta = "focusAfterReleased", xf = Symbol("elFocusTrap"), hr = J(), uo = J(0), gr = J(0);
let Tn = 0;
const Ri = (e) => {
  const t = [], n = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
    acceptNode: (o) => {
      const r = o.tagName === "INPUT" && o.type === "hidden";
      return o.disabled || o.hidden || r ? NodeFilter.FILTER_SKIP : o.tabIndex >= 0 || o === document.activeElement ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
    }
  });
  for (; n.nextNode(); )
    t.push(n.currentNode);
  return t;
}, na = (e, t) => {
  for (const n of e)
    if (!Sf(n, t))
      return n;
}, Sf = (e, t) => {
  if (process.env.NODE_ENV === "test")
    return !1;
  if (getComputedStyle(e).visibility === "hidden")
    return !0;
  for (; e; ) {
    if (t && e === t)
      return !1;
    if (getComputedStyle(e).display === "none")
      return !0;
    e = e.parentElement;
  }
  return !1;
}, _f = (e) => {
  const t = Ri(e), n = na(t, e), o = na(t.reverse(), e);
  return [n, o];
}, Ef = (e) => e instanceof HTMLInputElement && "select" in e, ht = (e, t) => {
  if (e && e.focus) {
    const n = document.activeElement;
    let o = !1;
    Kt(e) && !Di(e) && !e.getAttribute("tabindex") && (e.setAttribute("tabindex", "-1"), o = !0), e.focus({ preventScroll: !0 }), gr.value = window.performance.now(), e !== n && Ef(e) && t && e.select(), Kt(e) && o && e.removeAttribute("tabindex");
  }
};
function oa(e, t) {
  const n = [...e], o = e.indexOf(t);
  return o !== -1 && n.splice(o, 1), n;
}
const Cf = () => {
  let e = [];
  return {
    push: (o) => {
      const r = e[0];
      r && o !== r && r.pause(), e = oa(e, o), e.unshift(o);
    },
    remove: (o) => {
      var r, a;
      e = oa(e, o), (a = (r = e[0]) == null ? void 0 : r.resume) == null || a.call(r);
    }
  };
}, kf = (e, t = !1) => {
  const n = document.activeElement;
  for (const o of e)
    if (ht(o, t), document.activeElement !== n)
      return;
}, ra = Cf(), Af = () => uo.value > gr.value, Vn = () => {
  hr.value = "pointer", uo.value = window.performance.now();
}, aa = () => {
  hr.value = "keyboard", uo.value = window.performance.now();
}, If = () => (Te(() => {
  Tn === 0 && (document.addEventListener("mousedown", Vn), document.addEventListener("touchstart", Vn), document.addEventListener("keydown", aa)), Tn++;
}), Qe(() => {
  Tn--, Tn <= 0 && (document.removeEventListener("mousedown", Vn), document.removeEventListener("touchstart", Vn), document.removeEventListener("keydown", aa));
}), {
  focusReason: hr,
  lastUserFocusTimestamp: uo,
  lastAutomatedFocusTimestamp: gr
}), Mn = (e) => new CustomEvent(bf, {
  ...wf,
  detail: e
}), co = {
  tab: "Tab",
  esc: "Escape"
};
let Wt = [];
const ia = (e) => {
  e.code === co.esc && Wt.forEach((t) => t(e));
}, Tf = (e) => {
  Te(() => {
    Wt.length === 0 && document.addEventListener("keydown", ia), Pe && Wt.push(e);
  }), Qe(() => {
    Wt = Wt.filter((t) => t !== e), Wt.length === 0 && Pe && document.removeEventListener("keydown", ia);
  });
}, Vf = ee({
  name: "ElFocusTrap",
  inheritAttrs: !1,
  props: {
    loop: Boolean,
    trapped: Boolean,
    focusTrapEl: Object,
    focusStartEl: {
      type: [Object, String],
      default: "first"
    }
  },
  emits: [
    ea,
    ta,
    "focusin",
    "focusout",
    "focusout-prevented",
    "release-requested"
  ],
  setup(e, { emit: t }) {
    const n = J();
    let o, r;
    const { focusReason: a } = If();
    Tf((f) => {
      e.trapped && !l.paused && t("release-requested", f);
    });
    const l = {
      paused: !1,
      pause() {
        this.paused = !0;
      },
      resume() {
        this.paused = !1;
      }
    }, s = (f) => {
      if (!e.loop && !e.trapped || l.paused)
        return;
      const { code: v, altKey: C, ctrlKey: A, metaKey: O, currentTarget: I, shiftKey: k } = f, { loop: g } = e, b = v === co.tab && !C && !A && !O, w = document.activeElement;
      if (b && w) {
        const T = I, [_, L] = _f(T);
        if (_ && L) {
          if (!k && w === L) {
            const y = Mn({
              focusReason: a.value
            });
            t("focusout-prevented", y), y.defaultPrevented || (f.preventDefault(), g && ht(_, !0));
          } else if (k && [_, T].includes(w)) {
            const y = Mn({
              focusReason: a.value
            });
            t("focusout-prevented", y), y.defaultPrevented || (f.preventDefault(), g && ht(L, !0));
          }
        } else if (w === T) {
          const y = Mn({
            focusReason: a.value
          });
          t("focusout-prevented", y), y.defaultPrevented || f.preventDefault();
        }
      }
    };
    Ye(xf, {
      focusTrapRef: n,
      onKeydown: s
    }), Z(() => e.focusTrapEl, (f) => {
      f && (n.value = f);
    }, { immediate: !0 }), Z([n], ([f], [v]) => {
      f && (f.addEventListener("keydown", s), f.addEventListener("focusin", d), f.addEventListener("focusout", h)), v && (v.removeEventListener("keydown", s), v.removeEventListener("focusin", d), v.removeEventListener("focusout", h));
    });
    const u = (f) => {
      t(ea, f);
    }, i = (f) => t(ta, f), d = (f) => {
      const v = S(n);
      if (!v)
        return;
      const C = f.target, A = f.relatedTarget, O = C && v.contains(C);
      e.trapped || A && v.contains(A) || (o = A), O && t("focusin", f), !l.paused && e.trapped && (O ? r = C : ht(r, !0));
    }, h = (f) => {
      const v = S(n);
      if (!(l.paused || !v))
        if (e.trapped) {
          const C = f.relatedTarget;
          !si(C) && !v.contains(C) && setTimeout(() => {
            if (!l.paused && e.trapped) {
              const A = Mn({
                focusReason: a.value
              });
              t("focusout-prevented", A), A.defaultPrevented || ht(r, !0);
            }
          }, 0);
        } else {
          const C = f.target;
          C && v.contains(C) || t("focusout", f);
        }
    };
    async function x() {
      await Ee();
      const f = S(n);
      if (f) {
        ra.push(l);
        const v = f.contains(document.activeElement) ? o : document.activeElement;
        if (o = v, !f.contains(v)) {
          const A = new Event(xo, Jr);
          f.addEventListener(xo, u), f.dispatchEvent(A), A.defaultPrevented || Ee(() => {
            let O = e.focusStartEl;
            Fe(O) || (ht(O), document.activeElement !== O && (O = "first")), O === "first" && kf(Ri(f), !0), (document.activeElement === v || O === "container") && ht(f);
          });
        }
      }
    }
    function m() {
      const f = S(n);
      if (f) {
        f.removeEventListener(xo, u);
        const v = new CustomEvent(So, {
          ...Jr,
          detail: {
            focusReason: a.value
          }
        });
        f.addEventListener(So, i), f.dispatchEvent(v), !v.defaultPrevented && (a.value == "keyboard" || !Af() || f.contains(document.activeElement)) && ht(o ?? document.body), f.removeEventListener(So, i), ra.remove(l);
      }
    }
    return Te(() => {
      e.trapped && x(), Z(() => e.trapped, (f) => {
        f ? x() : m();
      });
    }), Qe(() => {
      e.trapped && m(), n.value && (n.value.removeEventListener("keydown", s), n.value.removeEventListener("focusin", d), n.value.removeEventListener("focusout", h), n.value = void 0);
    }), {
      onKeydown: s
    };
  }
});
function Mf(e, t, n, o, r, a) {
  return ye(e.$slots, "default", { handleKeydown: e.onKeydown });
}
var Of = /* @__PURE__ */ St(Vf, [["render", Mf], ["__file", "focus-trap.vue"]]);
const zf = dt({
  value: {
    type: [String, Number],
    default: ""
  },
  max: {
    type: Number,
    default: 99
  },
  isDot: Boolean,
  hidden: Boolean,
  type: {
    type: String,
    values: ["primary", "success", "warning", "info", "danger"],
    default: "danger"
  },
  showZero: {
    type: Boolean,
    default: !0
  },
  color: String,
  badgeStyle: {
    type: Oe([String, Object, Array])
  },
  offset: {
    type: Oe(Array),
    default: [0, 0]
  },
  badgeClass: {
    type: String
  }
}), Pf = ee({
  name: "ElBadge"
}), Lf = /* @__PURE__ */ ee({
  ...Pf,
  props: zf,
  setup(e, { expose: t }) {
    const n = e, o = Xe("badge"), r = P(() => n.isDot ? "" : ut(n.value) && ut(n.max) ? n.max < n.value ? `${n.max}+` : `${n.value}` : `${n.value}`), a = P(() => {
      var l, s, u, i, d;
      return [
        {
          backgroundColor: n.color,
          marginRight: yn(-((s = (l = n.offset) == null ? void 0 : l[0]) != null ? s : 0)),
          marginTop: yn((i = (u = n.offset) == null ? void 0 : u[1]) != null ? i : 0)
        },
        (d = n.badgeStyle) != null ? d : {}
      ];
    });
    return t({
      content: r
    }), (l, s) => (E(), F("div", {
      class: K(S(o).b())
    }, [
      ye(l.$slots, "default"),
      c(Yo, {
        name: `${S(o).namespace.value}-zoom-in-center`,
        persisted: ""
      }, {
        default: p(() => [
          Ot(B("sup", {
            class: K([
              S(o).e("content"),
              S(o).em("content", l.type),
              S(o).is("fixed", !!l.$slots.default),
              S(o).is("dot", l.isDot),
              S(o).is("hide-zero", !l.showZero && n.value === 0),
              l.badgeClass
            ]),
            style: he(S(a))
          }, [
            ye(l.$slots, "content", { value: S(r) }, () => [
              ae(pe(S(r)), 1)
            ])
          ], 6), [
            [zt, !l.hidden && (S(r) || l.isDot || l.$slots.content)]
          ])
        ]),
        _: 3
      }, 8, ["name"])
    ], 2));
  }
});
var Df = /* @__PURE__ */ St(Lf, [["__file", "badge.vue"]]);
const Rf = lo(Df), Bi = Symbol("buttonGroupContextKey"), Bf = ({ from: e, replacement: t, scope: n, version: o, ref: r, type: a = "API" }, l) => {
  Z(() => S(l), (s) => {
    s && qe(n, `[${a}] ${e} is about to be deprecated in version ${o}, please use ${t} instead.
For more detail, please visit: ${r}
`);
  }, {
    immediate: !0
  });
}, Nf = (e, t) => {
  Bf({
    from: "type.text",
    replacement: "link",
    version: "3.0.0",
    scope: "props",
    ref: "https://element-plus.org/en-US/component/button.html#button-attributes"
  }, P(() => e.type === "text"));
  const n = xe(Bi, void 0), o = dr("button"), { form: r } = zi(), a = Li(P(() => n == null ? void 0 : n.size)), l = mr(), s = J(), u = Ua(), i = P(() => e.type || (n == null ? void 0 : n.type) || ""), d = P(() => {
    var f, v, C;
    return (C = (v = e.autoInsertSpace) != null ? v : (f = o.value) == null ? void 0 : f.autoInsertSpace) != null ? C : !1;
  }), h = P(() => e.tag === "button" ? {
    ariaDisabled: l.value || e.loading,
    disabled: l.value || e.loading,
    autofocus: e.autofocus,
    type: e.nativeType
  } : {}), x = P(() => {
    var f;
    const v = (f = u.default) == null ? void 0 : f.call(u);
    if (d.value && (v == null ? void 0 : v.length) === 1) {
      const C = v[0];
      if ((C == null ? void 0 : C.type) === Gl) {
        const A = C.children;
        return new RegExp("^\\p{Unified_Ideograph}{2}$", "u").test(A.trim());
      }
    }
    return !1;
  });
  return {
    _disabled: l,
    _size: a,
    _type: i,
    _ref: s,
    _props: h,
    shouldAddSpace: x,
    handleClick: (f) => {
      if (l.value || e.loading) {
        f.stopPropagation();
        return;
      }
      e.nativeType === "reset" && (r == null || r.resetFields()), t("click", f);
    }
  };
}, Hf = [
  "default",
  "primary",
  "success",
  "warning",
  "info",
  "danger",
  "text",
  ""
], Ff = ["button", "submit", "reset"], Ro = dt({
  size: cr,
  disabled: Boolean,
  type: {
    type: String,
    values: Hf,
    default: ""
  },
  icon: {
    type: bn
  },
  nativeType: {
    type: String,
    values: Ff,
    default: "button"
  },
  loading: Boolean,
  loadingIcon: {
    type: bn,
    default: () => Zn
  },
  plain: Boolean,
  text: Boolean,
  link: Boolean,
  bg: Boolean,
  autofocus: Boolean,
  round: Boolean,
  circle: Boolean,
  color: String,
  dark: Boolean,
  autoInsertSpace: {
    type: Boolean,
    default: void 0
  },
  tag: {
    type: Oe([String, Object]),
    default: "button"
  }
}), $f = {
  click: (e) => e instanceof MouseEvent
};
function Ae(e, t) {
  Uf(e) && (e = "100%");
  var n = jf(e);
  return e = t === 360 ? e : Math.min(t, Math.max(0, parseFloat(e))), n && (e = parseInt(String(e * t), 10) / 100), Math.abs(e - t) < 1e-6 ? 1 : (t === 360 ? e = (e < 0 ? e % t + t : e % t) / parseFloat(String(t)) : e = e % t / parseFloat(String(t)), e);
}
function On(e) {
  return Math.min(1, Math.max(0, e));
}
function Uf(e) {
  return typeof e == "string" && e.indexOf(".") !== -1 && parseFloat(e) === 1;
}
function jf(e) {
  return typeof e == "string" && e.indexOf("%") !== -1;
}
function Ni(e) {
  return e = parseFloat(e), (isNaN(e) || e < 0 || e > 1) && (e = 1), e;
}
function zn(e) {
  return e <= 1 ? "".concat(Number(e) * 100, "%") : e;
}
function Vt(e) {
  return e.length === 1 ? "0" + e : String(e);
}
function Wf(e, t, n) {
  return {
    r: Ae(e, 255) * 255,
    g: Ae(t, 255) * 255,
    b: Ae(n, 255) * 255
  };
}
function la(e, t, n) {
  e = Ae(e, 255), t = Ae(t, 255), n = Ae(n, 255);
  var o = Math.max(e, t, n), r = Math.min(e, t, n), a = 0, l = 0, s = (o + r) / 2;
  if (o === r)
    l = 0, a = 0;
  else {
    var u = o - r;
    switch (l = s > 0.5 ? u / (2 - o - r) : u / (o + r), o) {
      case e:
        a = (t - n) / u + (t < n ? 6 : 0);
        break;
      case t:
        a = (n - e) / u + 2;
        break;
      case n:
        a = (e - t) / u + 4;
        break;
    }
    a /= 6;
  }
  return { h: a, s: l, l: s };
}
function _o(e, t, n) {
  return n < 0 && (n += 1), n > 1 && (n -= 1), n < 1 / 6 ? e + (t - e) * (6 * n) : n < 1 / 2 ? t : n < 2 / 3 ? e + (t - e) * (2 / 3 - n) * 6 : e;
}
function Gf(e, t, n) {
  var o, r, a;
  if (e = Ae(e, 360), t = Ae(t, 100), n = Ae(n, 100), t === 0)
    r = n, a = n, o = n;
  else {
    var l = n < 0.5 ? n * (1 + t) : n + t - n * t, s = 2 * n - l;
    o = _o(s, l, e + 1 / 3), r = _o(s, l, e), a = _o(s, l, e - 1 / 3);
  }
  return { r: o * 255, g: r * 255, b: a * 255 };
}
function sa(e, t, n) {
  e = Ae(e, 255), t = Ae(t, 255), n = Ae(n, 255);
  var o = Math.max(e, t, n), r = Math.min(e, t, n), a = 0, l = o, s = o - r, u = o === 0 ? 0 : s / o;
  if (o === r)
    a = 0;
  else {
    switch (o) {
      case e:
        a = (t - n) / s + (t < n ? 6 : 0);
        break;
      case t:
        a = (n - e) / s + 2;
        break;
      case n:
        a = (e - t) / s + 4;
        break;
    }
    a /= 6;
  }
  return { h: a, s: u, v: l };
}
function qf(e, t, n) {
  e = Ae(e, 360) * 6, t = Ae(t, 100), n = Ae(n, 100);
  var o = Math.floor(e), r = e - o, a = n * (1 - t), l = n * (1 - r * t), s = n * (1 - (1 - r) * t), u = o % 6, i = [n, l, a, a, s, n][u], d = [s, n, n, l, a, a][u], h = [a, a, s, n, n, l][u];
  return { r: i * 255, g: d * 255, b: h * 255 };
}
function ua(e, t, n, o) {
  var r = [
    Vt(Math.round(e).toString(16)),
    Vt(Math.round(t).toString(16)),
    Vt(Math.round(n).toString(16))
  ];
  return o && r[0].startsWith(r[0].charAt(1)) && r[1].startsWith(r[1].charAt(1)) && r[2].startsWith(r[2].charAt(1)) ? r[0].charAt(0) + r[1].charAt(0) + r[2].charAt(0) : r.join("");
}
function Yf(e, t, n, o, r) {
  var a = [
    Vt(Math.round(e).toString(16)),
    Vt(Math.round(t).toString(16)),
    Vt(Math.round(n).toString(16)),
    Vt(Zf(o))
  ];
  return r && a[0].startsWith(a[0].charAt(1)) && a[1].startsWith(a[1].charAt(1)) && a[2].startsWith(a[2].charAt(1)) && a[3].startsWith(a[3].charAt(1)) ? a[0].charAt(0) + a[1].charAt(0) + a[2].charAt(0) + a[3].charAt(0) : a.join("");
}
function Zf(e) {
  return Math.round(parseFloat(e) * 255).toString(16);
}
function ca(e) {
  return Ne(e) / 255;
}
function Ne(e) {
  return parseInt(e, 16);
}
function Kf(e) {
  return {
    r: e >> 16,
    g: (e & 65280) >> 8,
    b: e & 255
  };
}
var Bo = {
  aliceblue: "#f0f8ff",
  antiquewhite: "#faebd7",
  aqua: "#00ffff",
  aquamarine: "#7fffd4",
  azure: "#f0ffff",
  beige: "#f5f5dc",
  bisque: "#ffe4c4",
  black: "#000000",
  blanchedalmond: "#ffebcd",
  blue: "#0000ff",
  blueviolet: "#8a2be2",
  brown: "#a52a2a",
  burlywood: "#deb887",
  cadetblue: "#5f9ea0",
  chartreuse: "#7fff00",
  chocolate: "#d2691e",
  coral: "#ff7f50",
  cornflowerblue: "#6495ed",
  cornsilk: "#fff8dc",
  crimson: "#dc143c",
  cyan: "#00ffff",
  darkblue: "#00008b",
  darkcyan: "#008b8b",
  darkgoldenrod: "#b8860b",
  darkgray: "#a9a9a9",
  darkgreen: "#006400",
  darkgrey: "#a9a9a9",
  darkkhaki: "#bdb76b",
  darkmagenta: "#8b008b",
  darkolivegreen: "#556b2f",
  darkorange: "#ff8c00",
  darkorchid: "#9932cc",
  darkred: "#8b0000",
  darksalmon: "#e9967a",
  darkseagreen: "#8fbc8f",
  darkslateblue: "#483d8b",
  darkslategray: "#2f4f4f",
  darkslategrey: "#2f4f4f",
  darkturquoise: "#00ced1",
  darkviolet: "#9400d3",
  deeppink: "#ff1493",
  deepskyblue: "#00bfff",
  dimgray: "#696969",
  dimgrey: "#696969",
  dodgerblue: "#1e90ff",
  firebrick: "#b22222",
  floralwhite: "#fffaf0",
  forestgreen: "#228b22",
  fuchsia: "#ff00ff",
  gainsboro: "#dcdcdc",
  ghostwhite: "#f8f8ff",
  goldenrod: "#daa520",
  gold: "#ffd700",
  gray: "#808080",
  green: "#008000",
  greenyellow: "#adff2f",
  grey: "#808080",
  honeydew: "#f0fff0",
  hotpink: "#ff69b4",
  indianred: "#cd5c5c",
  indigo: "#4b0082",
  ivory: "#fffff0",
  khaki: "#f0e68c",
  lavenderblush: "#fff0f5",
  lavender: "#e6e6fa",
  lawngreen: "#7cfc00",
  lemonchiffon: "#fffacd",
  lightblue: "#add8e6",
  lightcoral: "#f08080",
  lightcyan: "#e0ffff",
  lightgoldenrodyellow: "#fafad2",
  lightgray: "#d3d3d3",
  lightgreen: "#90ee90",
  lightgrey: "#d3d3d3",
  lightpink: "#ffb6c1",
  lightsalmon: "#ffa07a",
  lightseagreen: "#20b2aa",
  lightskyblue: "#87cefa",
  lightslategray: "#778899",
  lightslategrey: "#778899",
  lightsteelblue: "#b0c4de",
  lightyellow: "#ffffe0",
  lime: "#00ff00",
  limegreen: "#32cd32",
  linen: "#faf0e6",
  magenta: "#ff00ff",
  maroon: "#800000",
  mediumaquamarine: "#66cdaa",
  mediumblue: "#0000cd",
  mediumorchid: "#ba55d3",
  mediumpurple: "#9370db",
  mediumseagreen: "#3cb371",
  mediumslateblue: "#7b68ee",
  mediumspringgreen: "#00fa9a",
  mediumturquoise: "#48d1cc",
  mediumvioletred: "#c71585",
  midnightblue: "#191970",
  mintcream: "#f5fffa",
  mistyrose: "#ffe4e1",
  moccasin: "#ffe4b5",
  navajowhite: "#ffdead",
  navy: "#000080",
  oldlace: "#fdf5e6",
  olive: "#808000",
  olivedrab: "#6b8e23",
  orange: "#ffa500",
  orangered: "#ff4500",
  orchid: "#da70d6",
  palegoldenrod: "#eee8aa",
  palegreen: "#98fb98",
  paleturquoise: "#afeeee",
  palevioletred: "#db7093",
  papayawhip: "#ffefd5",
  peachpuff: "#ffdab9",
  peru: "#cd853f",
  pink: "#ffc0cb",
  plum: "#dda0dd",
  powderblue: "#b0e0e6",
  purple: "#800080",
  rebeccapurple: "#663399",
  red: "#ff0000",
  rosybrown: "#bc8f8f",
  royalblue: "#4169e1",
  saddlebrown: "#8b4513",
  salmon: "#fa8072",
  sandybrown: "#f4a460",
  seagreen: "#2e8b57",
  seashell: "#fff5ee",
  sienna: "#a0522d",
  silver: "#c0c0c0",
  skyblue: "#87ceeb",
  slateblue: "#6a5acd",
  slategray: "#708090",
  slategrey: "#708090",
  snow: "#fffafa",
  springgreen: "#00ff7f",
  steelblue: "#4682b4",
  tan: "#d2b48c",
  teal: "#008080",
  thistle: "#d8bfd8",
  tomato: "#ff6347",
  turquoise: "#40e0d0",
  violet: "#ee82ee",
  wheat: "#f5deb3",
  white: "#ffffff",
  whitesmoke: "#f5f5f5",
  yellow: "#ffff00",
  yellowgreen: "#9acd32"
};
function Xf(e) {
  var t = { r: 0, g: 0, b: 0 }, n = 1, o = null, r = null, a = null, l = !1, s = !1;
  return typeof e == "string" && (e = ep(e)), typeof e == "object" && (nt(e.r) && nt(e.g) && nt(e.b) ? (t = Wf(e.r, e.g, e.b), l = !0, s = String(e.r).substr(-1) === "%" ? "prgb" : "rgb") : nt(e.h) && nt(e.s) && nt(e.v) ? (o = zn(e.s), r = zn(e.v), t = qf(e.h, o, r), l = !0, s = "hsv") : nt(e.h) && nt(e.s) && nt(e.l) && (o = zn(e.s), a = zn(e.l), t = Gf(e.h, o, a), l = !0, s = "hsl"), Object.prototype.hasOwnProperty.call(e, "a") && (n = e.a)), n = Ni(n), {
    ok: l,
    format: e.format || s,
    r: Math.min(255, Math.max(t.r, 0)),
    g: Math.min(255, Math.max(t.g, 0)),
    b: Math.min(255, Math.max(t.b, 0)),
    a: n
  };
}
var Qf = "[-\\+]?\\d+%?", Jf = "[-\\+]?\\d*\\.\\d+%?", yt = "(?:".concat(Jf, ")|(?:").concat(Qf, ")"), Eo = "[\\s|\\(]+(".concat(yt, ")[,|\\s]+(").concat(yt, ")[,|\\s]+(").concat(yt, ")\\s*\\)?"), Co = "[\\s|\\(]+(".concat(yt, ")[,|\\s]+(").concat(yt, ")[,|\\s]+(").concat(yt, ")[,|\\s]+(").concat(yt, ")\\s*\\)?"), je = {
  CSS_UNIT: new RegExp(yt),
  rgb: new RegExp("rgb" + Eo),
  rgba: new RegExp("rgba" + Co),
  hsl: new RegExp("hsl" + Eo),
  hsla: new RegExp("hsla" + Co),
  hsv: new RegExp("hsv" + Eo),
  hsva: new RegExp("hsva" + Co),
  hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,
  hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,
  hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/
};
function ep(e) {
  if (e = e.trim().toLowerCase(), e.length === 0)
    return !1;
  var t = !1;
  if (Bo[e])
    e = Bo[e], t = !0;
  else if (e === "transparent")
    return { r: 0, g: 0, b: 0, a: 0, format: "name" };
  var n = je.rgb.exec(e);
  return n ? { r: n[1], g: n[2], b: n[3] } : (n = je.rgba.exec(e), n ? { r: n[1], g: n[2], b: n[3], a: n[4] } : (n = je.hsl.exec(e), n ? { h: n[1], s: n[2], l: n[3] } : (n = je.hsla.exec(e), n ? { h: n[1], s: n[2], l: n[3], a: n[4] } : (n = je.hsv.exec(e), n ? { h: n[1], s: n[2], v: n[3] } : (n = je.hsva.exec(e), n ? { h: n[1], s: n[2], v: n[3], a: n[4] } : (n = je.hex8.exec(e), n ? {
    r: Ne(n[1]),
    g: Ne(n[2]),
    b: Ne(n[3]),
    a: ca(n[4]),
    format: t ? "name" : "hex8"
  } : (n = je.hex6.exec(e), n ? {
    r: Ne(n[1]),
    g: Ne(n[2]),
    b: Ne(n[3]),
    format: t ? "name" : "hex"
  } : (n = je.hex4.exec(e), n ? {
    r: Ne(n[1] + n[1]),
    g: Ne(n[2] + n[2]),
    b: Ne(n[3] + n[3]),
    a: ca(n[4] + n[4]),
    format: t ? "name" : "hex8"
  } : (n = je.hex3.exec(e), n ? {
    r: Ne(n[1] + n[1]),
    g: Ne(n[2] + n[2]),
    b: Ne(n[3] + n[3]),
    format: t ? "name" : "hex"
  } : !1)))))))));
}
function nt(e) {
  return !!je.CSS_UNIT.exec(String(e));
}
var tp = (
  /** @class */
  function() {
    function e(t, n) {
      t === void 0 && (t = ""), n === void 0 && (n = {});
      var o;
      if (t instanceof e)
        return t;
      typeof t == "number" && (t = Kf(t)), this.originalInput = t;
      var r = Xf(t);
      this.originalInput = t, this.r = r.r, this.g = r.g, this.b = r.b, this.a = r.a, this.roundA = Math.round(100 * this.a) / 100, this.format = (o = n.format) !== null && o !== void 0 ? o : r.format, this.gradientType = n.gradientType, this.r < 1 && (this.r = Math.round(this.r)), this.g < 1 && (this.g = Math.round(this.g)), this.b < 1 && (this.b = Math.round(this.b)), this.isValid = r.ok;
    }
    return e.prototype.isDark = function() {
      return this.getBrightness() < 128;
    }, e.prototype.isLight = function() {
      return !this.isDark();
    }, e.prototype.getBrightness = function() {
      var t = this.toRgb();
      return (t.r * 299 + t.g * 587 + t.b * 114) / 1e3;
    }, e.prototype.getLuminance = function() {
      var t = this.toRgb(), n, o, r, a = t.r / 255, l = t.g / 255, s = t.b / 255;
      return a <= 0.03928 ? n = a / 12.92 : n = Math.pow((a + 0.055) / 1.055, 2.4), l <= 0.03928 ? o = l / 12.92 : o = Math.pow((l + 0.055) / 1.055, 2.4), s <= 0.03928 ? r = s / 12.92 : r = Math.pow((s + 0.055) / 1.055, 2.4), 0.2126 * n + 0.7152 * o + 0.0722 * r;
    }, e.prototype.getAlpha = function() {
      return this.a;
    }, e.prototype.setAlpha = function(t) {
      return this.a = Ni(t), this.roundA = Math.round(100 * this.a) / 100, this;
    }, e.prototype.isMonochrome = function() {
      var t = this.toHsl().s;
      return t === 0;
    }, e.prototype.toHsv = function() {
      var t = sa(this.r, this.g, this.b);
      return { h: t.h * 360, s: t.s, v: t.v, a: this.a };
    }, e.prototype.toHsvString = function() {
      var t = sa(this.r, this.g, this.b), n = Math.round(t.h * 360), o = Math.round(t.s * 100), r = Math.round(t.v * 100);
      return this.a === 1 ? "hsv(".concat(n, ", ").concat(o, "%, ").concat(r, "%)") : "hsva(".concat(n, ", ").concat(o, "%, ").concat(r, "%, ").concat(this.roundA, ")");
    }, e.prototype.toHsl = function() {
      var t = la(this.r, this.g, this.b);
      return { h: t.h * 360, s: t.s, l: t.l, a: this.a };
    }, e.prototype.toHslString = function() {
      var t = la(this.r, this.g, this.b), n = Math.round(t.h * 360), o = Math.round(t.s * 100), r = Math.round(t.l * 100);
      return this.a === 1 ? "hsl(".concat(n, ", ").concat(o, "%, ").concat(r, "%)") : "hsla(".concat(n, ", ").concat(o, "%, ").concat(r, "%, ").concat(this.roundA, ")");
    }, e.prototype.toHex = function(t) {
      return t === void 0 && (t = !1), ua(this.r, this.g, this.b, t);
    }, e.prototype.toHexString = function(t) {
      return t === void 0 && (t = !1), "#" + this.toHex(t);
    }, e.prototype.toHex8 = function(t) {
      return t === void 0 && (t = !1), Yf(this.r, this.g, this.b, this.a, t);
    }, e.prototype.toHex8String = function(t) {
      return t === void 0 && (t = !1), "#" + this.toHex8(t);
    }, e.prototype.toHexShortString = function(t) {
      return t === void 0 && (t = !1), this.a === 1 ? this.toHexString(t) : this.toHex8String(t);
    }, e.prototype.toRgb = function() {
      return {
        r: Math.round(this.r),
        g: Math.round(this.g),
        b: Math.round(this.b),
        a: this.a
      };
    }, e.prototype.toRgbString = function() {
      var t = Math.round(this.r), n = Math.round(this.g), o = Math.round(this.b);
      return this.a === 1 ? "rgb(".concat(t, ", ").concat(n, ", ").concat(o, ")") : "rgba(".concat(t, ", ").concat(n, ", ").concat(o, ", ").concat(this.roundA, ")");
    }, e.prototype.toPercentageRgb = function() {
      var t = function(n) {
        return "".concat(Math.round(Ae(n, 255) * 100), "%");
      };
      return {
        r: t(this.r),
        g: t(this.g),
        b: t(this.b),
        a: this.a
      };
    }, e.prototype.toPercentageRgbString = function() {
      var t = function(n) {
        return Math.round(Ae(n, 255) * 100);
      };
      return this.a === 1 ? "rgb(".concat(t(this.r), "%, ").concat(t(this.g), "%, ").concat(t(this.b), "%)") : "rgba(".concat(t(this.r), "%, ").concat(t(this.g), "%, ").concat(t(this.b), "%, ").concat(this.roundA, ")");
    }, e.prototype.toName = function() {
      if (this.a === 0)
        return "transparent";
      if (this.a < 1)
        return !1;
      for (var t = "#" + ua(this.r, this.g, this.b, !1), n = 0, o = Object.entries(Bo); n < o.length; n++) {
        var r = o[n], a = r[0], l = r[1];
        if (t === l)
          return a;
      }
      return !1;
    }, e.prototype.toString = function(t) {
      var n = !!t;
      t = t ?? this.format;
      var o = !1, r = this.a < 1 && this.a >= 0, a = !n && r && (t.startsWith("hex") || t === "name");
      return a ? t === "name" && this.a === 0 ? this.toName() : this.toRgbString() : (t === "rgb" && (o = this.toRgbString()), t === "prgb" && (o = this.toPercentageRgbString()), (t === "hex" || t === "hex6") && (o = this.toHexString()), t === "hex3" && (o = this.toHexString(!0)), t === "hex4" && (o = this.toHex8String(!0)), t === "hex8" && (o = this.toHex8String()), t === "name" && (o = this.toName()), t === "hsl" && (o = this.toHslString()), t === "hsv" && (o = this.toHsvString()), o || this.toHexString());
    }, e.prototype.toNumber = function() {
      return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);
    }, e.prototype.clone = function() {
      return new e(this.toString());
    }, e.prototype.lighten = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.l += t / 100, n.l = On(n.l), new e(n);
    }, e.prototype.brighten = function(t) {
      t === void 0 && (t = 10);
      var n = this.toRgb();
      return n.r = Math.max(0, Math.min(255, n.r - Math.round(255 * -(t / 100)))), n.g = Math.max(0, Math.min(255, n.g - Math.round(255 * -(t / 100)))), n.b = Math.max(0, Math.min(255, n.b - Math.round(255 * -(t / 100)))), new e(n);
    }, e.prototype.darken = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.l -= t / 100, n.l = On(n.l), new e(n);
    }, e.prototype.tint = function(t) {
      return t === void 0 && (t = 10), this.mix("white", t);
    }, e.prototype.shade = function(t) {
      return t === void 0 && (t = 10), this.mix("black", t);
    }, e.prototype.desaturate = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.s -= t / 100, n.s = On(n.s), new e(n);
    }, e.prototype.saturate = function(t) {
      t === void 0 && (t = 10);
      var n = this.toHsl();
      return n.s += t / 100, n.s = On(n.s), new e(n);
    }, e.prototype.greyscale = function() {
      return this.desaturate(100);
    }, e.prototype.spin = function(t) {
      var n = this.toHsl(), o = (n.h + t) % 360;
      return n.h = o < 0 ? 360 + o : o, new e(n);
    }, e.prototype.mix = function(t, n) {
      n === void 0 && (n = 50);
      var o = this.toRgb(), r = new e(t).toRgb(), a = n / 100, l = {
        r: (r.r - o.r) * a + o.r,
        g: (r.g - o.g) * a + o.g,
        b: (r.b - o.b) * a + o.b,
        a: (r.a - o.a) * a + o.a
      };
      return new e(l);
    }, e.prototype.analogous = function(t, n) {
      t === void 0 && (t = 6), n === void 0 && (n = 30);
      var o = this.toHsl(), r = 360 / n, a = [this];
      for (o.h = (o.h - (r * t >> 1) + 720) % 360; --t; )
        o.h = (o.h + r) % 360, a.push(new e(o));
      return a;
    }, e.prototype.complement = function() {
      var t = this.toHsl();
      return t.h = (t.h + 180) % 360, new e(t);
    }, e.prototype.monochromatic = function(t) {
      t === void 0 && (t = 6);
      for (var n = this.toHsv(), o = n.h, r = n.s, a = n.v, l = [], s = 1 / t; t--; )
        l.push(new e({ h: o, s: r, v: a })), a = (a + s) % 1;
      return l;
    }, e.prototype.splitcomplement = function() {
      var t = this.toHsl(), n = t.h;
      return [
        this,
        new e({ h: (n + 72) % 360, s: t.s, l: t.l }),
        new e({ h: (n + 216) % 360, s: t.s, l: t.l })
      ];
    }, e.prototype.onBackground = function(t) {
      var n = this.toRgb(), o = new e(t).toRgb(), r = n.a + o.a * (1 - n.a);
      return new e({
        r: (n.r * n.a + o.r * o.a * (1 - n.a)) / r,
        g: (n.g * n.a + o.g * o.a * (1 - n.a)) / r,
        b: (n.b * n.a + o.b * o.a * (1 - n.a)) / r,
        a: r
      });
    }, e.prototype.triad = function() {
      return this.polyad(3);
    }, e.prototype.tetrad = function() {
      return this.polyad(4);
    }, e.prototype.polyad = function(t) {
      for (var n = this.toHsl(), o = n.h, r = [this], a = 360 / t, l = 1; l < t; l++)
        r.push(new e({ h: (o + l * a) % 360, s: n.s, l: n.l }));
      return r;
    }, e.prototype.equals = function(t) {
      return this.toRgbString() === new e(t).toRgbString();
    }, e;
  }()
);
function ft(e, t = 20) {
  return e.mix("#141414", t).toString();
}
function np(e) {
  const t = mr(), n = Xe("button");
  return P(() => {
    let o = {}, r = e.color;
    if (r) {
      const a = r.match(/var\((.*?)\)/);
      a && (r = window.getComputedStyle(window.document.documentElement).getPropertyValue(a[1]));
      const l = new tp(r), s = e.dark ? l.tint(20).toString() : ft(l, 20);
      if (e.plain)
        o = n.cssVarBlock({
          "bg-color": e.dark ? ft(l, 90) : l.tint(90).toString(),
          "text-color": r,
          "border-color": e.dark ? ft(l, 50) : l.tint(50).toString(),
          "hover-text-color": `var(${n.cssVarName("color-white")})`,
          "hover-bg-color": r,
          "hover-border-color": r,
          "active-bg-color": s,
          "active-text-color": `var(${n.cssVarName("color-white")})`,
          "active-border-color": s
        }), t.value && (o[n.cssVarBlockName("disabled-bg-color")] = e.dark ? ft(l, 90) : l.tint(90).toString(), o[n.cssVarBlockName("disabled-text-color")] = e.dark ? ft(l, 50) : l.tint(50).toString(), o[n.cssVarBlockName("disabled-border-color")] = e.dark ? ft(l, 80) : l.tint(80).toString());
      else {
        const u = e.dark ? ft(l, 30) : l.tint(30).toString(), i = l.isDark() ? `var(${n.cssVarName("color-white")})` : `var(${n.cssVarName("color-black")})`;
        if (o = n.cssVarBlock({
          "bg-color": r,
          "text-color": i,
          "border-color": r,
          "hover-bg-color": u,
          "hover-text-color": i,
          "hover-border-color": u,
          "active-bg-color": s,
          "active-border-color": s
        }), t.value) {
          const d = e.dark ? ft(l, 50) : l.tint(50).toString();
          o[n.cssVarBlockName("disabled-bg-color")] = d, o[n.cssVarBlockName("disabled-text-color")] = e.dark ? "rgba(255, 255, 255, 0.5)" : `var(${n.cssVarName("color-white")})`, o[n.cssVarBlockName("disabled-border-color")] = d;
        }
      }
    }
    return o;
  });
}
const op = ee({
  name: "ElButton"
}), rp = /* @__PURE__ */ ee({
  ...op,
  props: Ro,
  emits: $f,
  setup(e, { expose: t, emit: n }) {
    const o = e, r = np(o), a = Xe("button"), { _ref: l, _size: s, _type: u, _disabled: i, _props: d, shouldAddSpace: h, handleClick: x } = Nf(o, n), m = P(() => [
      a.b(),
      a.m(u.value),
      a.m(s.value),
      a.is("disabled", i.value),
      a.is("loading", o.loading),
      a.is("plain", o.plain),
      a.is("round", o.round),
      a.is("circle", o.circle),
      a.is("text", o.text),
      a.is("link", o.link),
      a.is("has-bg", o.bg)
    ]);
    return t({
      ref: l,
      size: s,
      type: u,
      disabled: i,
      shouldAddSpace: h
    }), (f, v) => (E(), W(Me(f.tag), lt({
      ref_key: "_ref",
      ref: l
    }, S(d), {
      class: S(m),
      style: S(r),
      onClick: S(x)
    }), {
      default: p(() => [
        f.loading ? (E(), F(me, { key: 0 }, [
          f.$slots.loading ? ye(f.$slots, "loading", { key: 0 }) : (E(), W(S(Ze), {
            key: 1,
            class: K(S(a).is("loading"))
          }, {
            default: p(() => [
              (E(), W(Me(f.loadingIcon)))
            ]),
            _: 1
          }, 8, ["class"]))
        ], 64)) : f.icon || f.$slots.icon ? (E(), W(S(Ze), { key: 1 }, {
          default: p(() => [
            f.icon ? (E(), W(Me(f.icon), { key: 0 })) : ye(f.$slots, "icon", { key: 1 })
          ]),
          _: 3
        })) : q("v-if", !0),
        f.$slots.default ? (E(), F("span", {
          key: 2,
          class: K({ [S(a).em("text", "expand")]: S(h) })
        }, [
          ye(f.$slots, "default")
        ], 2)) : q("v-if", !0)
      ]),
      _: 3
    }, 16, ["class", "style", "onClick"]));
  }
});
var ap = /* @__PURE__ */ St(rp, [["__file", "button.vue"]]);
const ip = {
  size: Ro.size,
  type: Ro.type
}, lp = ee({
  name: "ElButtonGroup"
}), sp = /* @__PURE__ */ ee({
  ...lp,
  props: ip,
  setup(e) {
    const t = e;
    Ye(Bi, _e({
      size: Fn(t, "size"),
      type: Fn(t, "type")
    }));
    const n = Xe("button");
    return (o, r) => (E(), F("div", {
      class: K(S(n).b("group"))
    }, [
      ye(o.$slots, "default")
    ], 2));
  }
});
var Hi = /* @__PURE__ */ St(sp, [["__file", "button-group.vue"]]);
const up = lo(ap, {
  ButtonGroup: Hi
});
hd(Hi);
var Bn = /* @__PURE__ */ ((e) => (e[e.TEXT = 1] = "TEXT", e[e.CLASS = 2] = "CLASS", e[e.STYLE = 4] = "STYLE", e[e.PROPS = 8] = "PROPS", e[e.FULL_PROPS = 16] = "FULL_PROPS", e[e.HYDRATE_EVENTS = 32] = "HYDRATE_EVENTS", e[e.STABLE_FRAGMENT = 64] = "STABLE_FRAGMENT", e[e.KEYED_FRAGMENT = 128] = "KEYED_FRAGMENT", e[e.UNKEYED_FRAGMENT = 256] = "UNKEYED_FRAGMENT", e[e.NEED_PATCH = 512] = "NEED_PATCH", e[e.DYNAMIC_SLOTS = 1024] = "DYNAMIC_SLOTS", e[e.HOISTED = -1] = "HOISTED", e[e.BAIL = -2] = "BAIL", e))(Bn || {});
const cp = dt({
  a11y: {
    type: Boolean,
    default: !0
  },
  locale: {
    type: Oe(Object)
  },
  size: cr,
  button: {
    type: Oe(Object)
  },
  experimentalFeatures: {
    type: Oe(Object)
  },
  keyboardNavigation: {
    type: Boolean,
    default: !0
  },
  message: {
    type: Oe(Object)
  },
  zIndex: Number,
  namespace: {
    type: String,
    default: "el"
  },
  ...ld
}), We = {};
ee({
  name: "ElConfigProvider",
  props: cp,
  setup(e, { slots: t }) {
    Z(() => e.message, (o) => {
      Object.assign(We, o ?? {});
    }, { immediate: !0, deep: !0 });
    const n = Si(e);
    return () => ye(t, "default", { config: n == null ? void 0 : n.value });
  }
});
const Fi = (e) => {
  if (!e)
    return { onClick: pn, onMousedown: pn, onMouseup: pn };
  let t = !1, n = !1;
  return { onClick: (l) => {
    t && n && e(l), t = n = !1;
  }, onMousedown: (l) => {
    t = l.target === l.currentTarget;
  }, onMouseup: (l) => {
    n = l.target === l.currentTarget;
  } };
}, dp = dt({
  mask: {
    type: Boolean,
    default: !0
  },
  customMaskEvent: Boolean,
  overlayClass: {
    type: Oe([
      String,
      Array,
      Object
    ])
  },
  zIndex: {
    type: Oe([String, Number])
  }
}), fp = {
  click: (e) => e instanceof MouseEvent
}, pp = "overlay";
var mp = ee({
  name: "ElOverlay",
  props: dp,
  emits: fp,
  setup(e, { slots: t, emit: n }) {
    const o = Xe(pp), r = (u) => {
      n("click", u);
    }, { onClick: a, onMousedown: l, onMouseup: s } = Fi(e.customMaskEvent ? void 0 : r);
    return () => e.mask ? c("div", {
      class: [o.b(), e.overlayClass],
      style: {
        zIndex: e.zIndex
      },
      onClick: a,
      onMousedown: l,
      onMouseup: s
    }, [ye(t, "default")], Bn.STYLE | Bn.CLASS | Bn.PROPS, ["onClick", "onMouseup", "onMousedown"]) : ql("div", {
      class: e.overlayClass,
      style: {
        zIndex: e.zIndex,
        position: "fixed",
        top: "0px",
        right: "0px",
        bottom: "0px",
        left: "0px"
      }
    }, [ye(t, "default")]);
  }
});
const hp = mp, gp = (e, t, n, o) => {
  let r = {
    offsetX: 0,
    offsetY: 0
  };
  const a = (i) => {
    const d = i.clientX, h = i.clientY, { offsetX: x, offsetY: m } = r, f = e.value.getBoundingClientRect(), v = f.left, C = f.top, A = f.width, O = f.height, I = document.documentElement.clientWidth, k = document.documentElement.clientHeight, g = -v + x, b = -C + m, w = I - v - A + x, T = k - C - O + m, _ = (M) => {
      let y = x + M.clientX - d, $ = m + M.clientY - h;
      o != null && o.value || (y = Math.min(Math.max(y, g), w), $ = Math.min(Math.max($, b), T)), r = {
        offsetX: y,
        offsetY: $
      }, e.value && (e.value.style.transform = `translate(${yn(y)}, ${yn($)})`);
    }, L = () => {
      document.removeEventListener("mousemove", _), document.removeEventListener("mouseup", L);
    };
    document.addEventListener("mousemove", _), document.addEventListener("mouseup", L);
  }, l = () => {
    t.value && e.value && t.value.addEventListener("mousedown", a);
  }, s = () => {
    t.value && e.value && t.value.removeEventListener("mousedown", a);
  }, u = () => {
    r = {
      offsetX: 0,
      offsetY: 0
    }, e.value && (e.value.style.transform = "none");
  };
  return Te(() => {
    Fa(() => {
      n.value ? l() : s();
    });
  }), Qe(() => {
    s();
  }), {
    resetPosition: u
  };
}, vp = (e, t = {}) => {
  $a(e) || Xc("[useLockscreen]", "You need to pass a ref param to this function");
  const n = t.ns || Xe("popup"), o = P(() => n.bm("parent", "hidden"));
  if (!Pe || Zr(document.body, o.value))
    return;
  let r = 0, a = !1, l = "0";
  const s = () => {
    setTimeout(() => {
      typeof document > "u" || a && document && (document.body.style.width = l, dd(document.body, o.value));
    }, 200);
  };
  Z(e, (u) => {
    if (!u) {
      s();
      return;
    }
    a = !Zr(document.body, o.value), a && (l = document.body.style.width, cd(document.body, o.value)), r = pd(n.namespace.value);
    const i = document.documentElement.clientHeight < document.body.scrollHeight, d = fd(document.body, "overflowY");
    r > 0 && (i || d === "scroll") && a && (document.body.style.width = `calc(100% - ${r}px)`);
  }), Ha(() => s());
}, yp = (e) => ["", ...bi].includes(e), $i = ["success", "info", "warning", "error"], Ve = Mi({
  customClass: "",
  center: !1,
  dangerouslyUseHTMLString: !1,
  duration: 3e3,
  icon: void 0,
  id: "",
  message: "",
  onClose: void 0,
  showClose: !1,
  type: "info",
  plain: !1,
  offset: 16,
  zIndex: 0,
  grouping: !1,
  repeatNum: 1,
  appendTo: Pe ? document.body : void 0
}), bp = dt({
  customClass: {
    type: String,
    default: Ve.customClass
  },
  center: {
    type: Boolean,
    default: Ve.center
  },
  dangerouslyUseHTMLString: {
    type: Boolean,
    default: Ve.dangerouslyUseHTMLString
  },
  duration: {
    type: Number,
    default: Ve.duration
  },
  icon: {
    type: bn,
    default: Ve.icon
  },
  id: {
    type: String,
    default: Ve.id
  },
  message: {
    type: Oe([
      String,
      Object,
      Function
    ]),
    default: Ve.message
  },
  onClose: {
    type: Oe(Function),
    default: Ve.onClose
  },
  showClose: {
    type: Boolean,
    default: Ve.showClose
  },
  type: {
    type: String,
    values: $i,
    default: Ve.type
  },
  plain: {
    type: Boolean,
    default: Ve.plain
  },
  offset: {
    type: Number,
    default: Ve.offset
  },
  zIndex: {
    type: Number,
    default: Ve.zIndex
  },
  grouping: {
    type: Boolean,
    default: Ve.grouping
  },
  repeatNum: {
    type: Number,
    default: Ve.repeatNum
  }
}), wp = {
  destroy: () => !0
}, Ge = Yl([]), xp = (e) => {
  const t = Ge.findIndex((r) => r.id === e), n = Ge[t];
  let o;
  return t > 0 && (o = Ge[t - 1]), { current: n, prev: o };
}, Sp = (e) => {
  const { prev: t } = xp(e);
  return t ? t.vm.exposed.bottom.value : 0;
}, _p = (e, t) => Ge.findIndex((o) => o.id === e) > 0 ? 16 : t, Ep = ee({
  name: "ElMessage"
}), Cp = /* @__PURE__ */ ee({
  ...Ep,
  props: bp,
  emits: wp,
  setup(e, { expose: t }) {
    const n = e, { Close: o } = Vi, { ns: r, zIndex: a } = xi("message"), { currentZIndex: l, nextZIndex: s } = a, u = J(), i = J(!1), d = J(0);
    let h;
    const x = P(() => n.type ? n.type === "error" ? "danger" : n.type : "info"), m = P(() => {
      const w = n.type;
      return { [r.bm("icon", w)]: w && Kn[w] };
    }), f = P(() => n.icon || Kn[n.type] || ""), v = P(() => Sp(n.id)), C = P(() => _p(n.id, n.offset) + v.value), A = P(() => d.value + C.value), O = P(() => ({
      top: `${C.value}px`,
      zIndex: l.value
    }));
    function I() {
      n.duration !== 0 && ({ stop: h } = Fc(() => {
        g();
      }, n.duration));
    }
    function k() {
      h == null || h();
    }
    function g() {
      i.value = !1;
    }
    function b({ code: w }) {
      w === co.esc && g();
    }
    return Te(() => {
      I(), s(), i.value = !0;
    }), Z(() => n.repeatNum, () => {
      k(), I();
    }), jt(document, "keydown", b), fi(u, () => {
      d.value = u.value.getBoundingClientRect().height;
    }), t({
      visible: i,
      bottom: A,
      close: g
    }), (w, T) => (E(), W(Yo, {
      name: S(r).b("fade"),
      onBeforeLeave: w.onClose,
      onAfterLeave: (_) => w.$emit("destroy"),
      persisted: ""
    }, {
      default: p(() => [
        Ot(B("div", {
          id: w.id,
          ref_key: "messageRef",
          ref: u,
          class: K([
            S(r).b(),
            { [S(r).m(w.type)]: w.type },
            S(r).is("center", w.center),
            S(r).is("closable", w.showClose),
            S(r).is("plain", w.plain),
            w.customClass
          ]),
          style: he(S(O)),
          role: "alert",
          onMouseenter: k,
          onMouseleave: I
        }, [
          w.repeatNum > 1 ? (E(), W(S(Rf), {
            key: 0,
            value: w.repeatNum,
            type: S(x),
            class: K(S(r).e("badge"))
          }, null, 8, ["value", "type", "class"])) : q("v-if", !0),
          S(f) ? (E(), W(S(Ze), {
            key: 1,
            class: K([S(r).e("icon"), S(m)])
          }, {
            default: p(() => [
              (E(), W(Me(S(f))))
            ]),
            _: 1
          }, 8, ["class"])) : q("v-if", !0),
          ye(w.$slots, "default", {}, () => [
            w.dangerouslyUseHTMLString ? (E(), F(me, { key: 1 }, [
              q(" Caution here, message could've been compromised, never use user's input as message "),
              B("p", {
                class: K(S(r).e("content")),
                innerHTML: w.message
              }, null, 10, ["innerHTML"])
            ], 2112)) : (E(), F("p", {
              key: 0,
              class: K(S(r).e("content"))
            }, pe(w.message), 3))
          ]),
          w.showClose ? (E(), W(S(Ze), {
            key: 2,
            class: K(S(r).e("closeBtn")),
            onClick: rt(g, ["stop"])
          }, {
            default: p(() => [
              c(S(o))
            ]),
            _: 1
          }, 8, ["class", "onClick"])) : q("v-if", !0)
        ], 46, ["id"]), [
          [zt, i.value]
        ])
      ]),
      _: 3
    }, 8, ["name", "onBeforeLeave", "onAfterLeave"]));
  }
});
var kp = /* @__PURE__ */ St(Cp, [["__file", "message.vue"]]);
let Ap = 1;
const Ui = (e) => {
  const t = !e || Fe(e) || to(e) || st(e) ? { message: e } : e, n = {
    ...Ve,
    ...t
  };
  if (!n.appendTo)
    n.appendTo = document.body;
  else if (Fe(n.appendTo)) {
    let o = document.querySelector(n.appendTo);
    Kt(o) || (qe("ElMessage", "the appendTo option is not an HTMLElement. Falling back to document.body."), o = document.body), n.appendTo = o;
  }
  return Lr(We.grouping) && !n.grouping && (n.grouping = We.grouping), ut(We.duration) && n.duration === 3e3 && (n.duration = We.duration), ut(We.offset) && n.offset === 16 && (n.offset = We.offset), Lr(We.showClose) && !n.showClose && (n.showClose = We.showClose), n;
}, Ip = (e) => {
  const t = Ge.indexOf(e);
  if (t === -1)
    return;
  Ge.splice(t, 1);
  const { handler: n } = e;
  n.close();
}, Tp = ({ appendTo: e, ...t }, n) => {
  const o = `message_${Ap++}`, r = t.onClose, a = document.createElement("div"), l = {
    ...t,
    id: o,
    onClose: () => {
      r == null || r(), Ip(d);
    },
    onDestroy: () => {
      $n(null, a);
    }
  }, s = c(kp, l, st(l.message) || to(l.message) ? {
    default: st(l.message) ? l.message : () => l.message
  } : null);
  s.appContext = n || Xt._context, $n(s, a), e.appendChild(a.firstElementChild);
  const u = s.component, d = {
    id: o,
    vnode: s,
    vm: u,
    handler: {
      close: () => {
        u.exposed.visible.value = !1;
      }
    },
    props: s.component.props
  };
  return d;
}, Xt = (e = {}, t) => {
  if (!Pe)
    return { close: () => {
    } };
  const n = Ui(e);
  if (n.grouping && Ge.length) {
    const r = Ge.find(({ vnode: a }) => {
      var l;
      return ((l = a.props) == null ? void 0 : l.message) === n.message;
    });
    if (r)
      return r.props.repeatNum += 1, r.props.type = n.type, r.handler;
  }
  if (ut(We.max) && Ge.length >= We.max)
    return { close: () => {
    } };
  const o = Tp(n, t);
  return Ge.push(o), o.handler;
};
$i.forEach((e) => {
  Xt[e] = (t = {}, n) => {
    const o = Ui(t);
    return Xt({ ...o, type: e }, n);
  };
});
function Vp(e) {
  for (const t of Ge)
    (!e || e === t.props.type) && t.handler.close();
}
Xt.closeAll = Vp;
Xt._context = null;
const bt = md(Xt, "$message"), No = "_trap-focus-children", Mt = [], da = (e) => {
  var t;
  if (Mt.length === 0)
    return;
  const n = Mt[Mt.length - 1][No];
  if (n.length > 0 && e.code === co.tab) {
    if (n.length === 1) {
      e.preventDefault(), document.activeElement !== n[0] && n[0].focus();
      return;
    }
    const o = e.shiftKey, r = e.target === n[0], a = e.target === n[n.length - 1];
    if (r && o && (e.preventDefault(), n[n.length - 1].focus()), a && !o && (e.preventDefault(), n[0].focus()), process.env.NODE_ENV === "test") {
      const l = n.indexOf(e.target);
      l !== -1 && ((t = n[o ? l - 1 : l + 1]) == null || t.focus());
    }
  }
}, Mp = {
  beforeMount(e) {
    e[No] = Qr(e), Mt.push(e), Mt.length <= 1 && document.addEventListener("keydown", da);
  },
  updated(e) {
    Ee(() => {
      e[No] = Qr(e);
    });
  },
  unmounted() {
    Mt.shift(), Mt.length === 0 && document.removeEventListener("keydown", da);
  }
}, Op = ee({
  name: "ElMessageBox",
  directives: {
    TrapFocus: Mp
  },
  components: {
    ElButton: up,
    ElFocusTrap: Of,
    ElInput: gf,
    ElOverlay: hp,
    ElIcon: Ze,
    ...Vi
  },
  inheritAttrs: !1,
  props: {
    buttonSize: {
      type: String,
      validator: yp
    },
    modal: {
      type: Boolean,
      default: !0
    },
    lockScroll: {
      type: Boolean,
      default: !0
    },
    showClose: {
      type: Boolean,
      default: !0
    },
    closeOnClickModal: {
      type: Boolean,
      default: !0
    },
    closeOnPressEscape: {
      type: Boolean,
      default: !0
    },
    closeOnHashChange: {
      type: Boolean,
      default: !0
    },
    center: Boolean,
    draggable: Boolean,
    overflow: Boolean,
    roundButton: {
      default: !1,
      type: Boolean
    },
    container: {
      type: String,
      default: "body"
    },
    boxType: {
      type: String,
      default: ""
    }
  },
  emits: ["vanish", "action"],
  setup(e, { emit: t }) {
    const {
      locale: n,
      zIndex: o,
      ns: r,
      size: a
    } = xi("message-box", P(() => e.buttonSize)), { t: l } = n, { nextZIndex: s } = o, u = J(!1), i = _e({
      autofocus: !0,
      beforeClose: null,
      callback: null,
      cancelButtonText: "",
      cancelButtonClass: "",
      confirmButtonText: "",
      confirmButtonClass: "",
      customClass: "",
      customStyle: {},
      dangerouslyUseHTMLString: !1,
      distinguishCancelAndClose: !1,
      icon: "",
      closeIcon: "",
      inputPattern: null,
      inputPlaceholder: "",
      inputType: "text",
      inputValue: "",
      inputValidator: void 0,
      inputErrorMessage: "",
      message: "",
      modalFade: !0,
      modalClass: "",
      showCancelButton: !1,
      showConfirmButton: !0,
      type: "",
      title: void 0,
      showInput: !1,
      action: "",
      confirmButtonLoading: !1,
      cancelButtonLoading: !1,
      confirmButtonLoadingIcon: Oo(Zn),
      cancelButtonLoadingIcon: Oo(Zn),
      confirmButtonDisabled: !1,
      editorErrorMessage: "",
      validateError: !1,
      zIndex: s()
    }), d = P(() => {
      const V = i.type;
      return { [r.bm("icon", V)]: V && Kn[V] };
    }), h = Do(), x = Do(), m = P(() => {
      const V = i.type;
      return i.icon || V && Kn[V] || "";
    }), f = P(() => !!i.message), v = J(), C = J(), A = J(), O = J(), I = J(), k = P(() => i.confirmButtonClass);
    Z(() => i.inputValue, async (V) => {
      await Ee(), e.boxType === "prompt" && V && y();
    }, { immediate: !0 }), Z(() => u.value, (V) => {
      var te, ie;
      V && (e.boxType !== "prompt" && (i.autofocus ? A.value = (ie = (te = I.value) == null ? void 0 : te.$el) != null ? ie : v.value : A.value = v.value), i.zIndex = s()), e.boxType === "prompt" && (V ? Ee().then(() => {
        var oe;
        O.value && O.value.$el && (i.autofocus ? A.value = (oe = $()) != null ? oe : v.value : A.value = v.value);
      }) : (i.editorErrorMessage = "", i.validateError = !1));
    });
    const g = P(() => e.draggable), b = P(() => e.overflow);
    gp(v, C, g, b), Te(async () => {
      await Ee(), e.closeOnHashChange && window.addEventListener("hashchange", w);
    }), Qe(() => {
      e.closeOnHashChange && window.removeEventListener("hashchange", w);
    });
    function w() {
      u.value && (u.value = !1, Ee(() => {
        i.action && t("action", i.action);
      }));
    }
    const T = () => {
      e.closeOnClickModal && M(i.distinguishCancelAndClose ? "close" : "cancel");
    }, _ = Fi(T), L = (V) => {
      if (i.inputType !== "textarea")
        return V.preventDefault(), M("confirm");
    }, M = (V) => {
      var te;
      e.boxType === "prompt" && V === "confirm" && !y() || (i.action = V, i.beforeClose ? (te = i.beforeClose) == null || te.call(i, V, i, w) : w());
    }, y = () => {
      if (e.boxType === "prompt") {
        const V = i.inputPattern;
        if (V && !V.test(i.inputValue || ""))
          return i.editorErrorMessage = i.inputErrorMessage || l("el.messagebox.error"), i.validateError = !0, !1;
        const te = i.inputValidator;
        if (st(te)) {
          const ie = te(i.inputValue);
          if (ie === !1)
            return i.editorErrorMessage = i.inputErrorMessage || l("el.messagebox.error"), i.validateError = !0, !1;
          if (Fe(ie))
            return i.editorErrorMessage = ie, i.validateError = !0, !1;
        }
      }
      return i.editorErrorMessage = "", i.validateError = !1, !0;
    }, $ = () => {
      var V, te;
      const ie = (V = O.value) == null ? void 0 : V.$refs;
      return (te = ie == null ? void 0 : ie.input) != null ? te : ie == null ? void 0 : ie.textarea;
    }, Y = () => {
      M("close");
    }, se = () => {
      e.closeOnPressEscape && Y();
    };
    return e.lockScroll && vp(u), {
      ...Zl(i),
      ns: r,
      overlayEvent: _,
      visible: u,
      hasMessage: f,
      typeClass: d,
      contentId: h,
      inputId: x,
      btnSize: a,
      iconComponent: m,
      confirmButtonClasses: k,
      rootRef: v,
      focusStartRef: A,
      headerRef: C,
      inputRef: O,
      confirmRef: I,
      doClose: w,
      handleClose: Y,
      onCloseRequested: se,
      handleWrapperClick: T,
      handleInputEnter: L,
      handleAction: M,
      t: l
    };
  }
});
function zp(e, t, n, o, r, a) {
  const l = R("el-icon"), s = R("el-input"), u = R("el-button"), i = R("el-focus-trap"), d = R("el-overlay");
  return E(), W(Yo, {
    name: "fade-in-linear",
    onAfterLeave: (h) => e.$emit("vanish"),
    persisted: ""
  }, {
    default: p(() => [
      Ot(c(d, {
        "z-index": e.zIndex,
        "overlay-class": [e.ns.is("message-box"), e.modalClass],
        mask: e.modal
      }, {
        default: p(() => [
          B("div", {
            role: "dialog",
            "aria-label": e.title,
            "aria-modal": "true",
            "aria-describedby": e.showInput ? void 0 : e.contentId,
            class: K(`${e.ns.namespace.value}-overlay-message-box`),
            onClick: e.overlayEvent.onClick,
            onMousedown: e.overlayEvent.onMousedown,
            onMouseup: e.overlayEvent.onMouseup
          }, [
            c(i, {
              loop: "",
              trapped: e.visible,
              "focus-trap-el": e.rootRef,
              "focus-start-el": e.focusStartRef,
              onReleaseRequested: e.onCloseRequested
            }, {
              default: p(() => [
                B("div", {
                  ref: "rootRef",
                  class: K([
                    e.ns.b(),
                    e.customClass,
                    e.ns.is("draggable", e.draggable),
                    { [e.ns.m("center")]: e.center }
                  ]),
                  style: he(e.customStyle),
                  tabindex: "-1",
                  onClick: rt(() => {
                  }, ["stop"])
                }, [
                  e.title !== null && e.title !== void 0 ? (E(), F("div", {
                    key: 0,
                    ref: "headerRef",
                    class: K([e.ns.e("header"), { "show-close": e.showClose }])
                  }, [
                    B("div", {
                      class: K(e.ns.e("title"))
                    }, [
                      e.iconComponent && e.center ? (E(), W(l, {
                        key: 0,
                        class: K([e.ns.e("status"), e.typeClass])
                      }, {
                        default: p(() => [
                          (E(), W(Me(e.iconComponent)))
                        ]),
                        _: 1
                      }, 8, ["class"])) : q("v-if", !0),
                      B("span", null, pe(e.title), 1)
                    ], 2),
                    e.showClose ? (E(), F("button", {
                      key: 0,
                      type: "button",
                      class: K(e.ns.e("headerbtn")),
                      "aria-label": e.t("el.messagebox.close"),
                      onClick: (h) => e.handleAction(e.distinguishCancelAndClose ? "close" : "cancel"),
                      onKeydown: An(rt((h) => e.handleAction(e.distinguishCancelAndClose ? "close" : "cancel"), ["prevent"]), ["enter"])
                    }, [
                      c(l, {
                        class: K(e.ns.e("close"))
                      }, {
                        default: p(() => [
                          (E(), W(Me(e.closeIcon || "close")))
                        ]),
                        _: 1
                      }, 8, ["class"])
                    ], 42, ["aria-label", "onClick", "onKeydown"])) : q("v-if", !0)
                  ], 2)) : q("v-if", !0),
                  B("div", {
                    id: e.contentId,
                    class: K(e.ns.e("content"))
                  }, [
                    B("div", {
                      class: K(e.ns.e("container"))
                    }, [
                      e.iconComponent && !e.center && e.hasMessage ? (E(), W(l, {
                        key: 0,
                        class: K([e.ns.e("status"), e.typeClass])
                      }, {
                        default: p(() => [
                          (E(), W(Me(e.iconComponent)))
                        ]),
                        _: 1
                      }, 8, ["class"])) : q("v-if", !0),
                      e.hasMessage ? (E(), F("div", {
                        key: 1,
                        class: K(e.ns.e("message"))
                      }, [
                        ye(e.$slots, "default", {}, () => [
                          e.dangerouslyUseHTMLString ? (E(), W(Me(e.showInput ? "label" : "p"), {
                            key: 1,
                            for: e.showInput ? e.inputId : void 0,
                            innerHTML: e.message
                          }, null, 8, ["for", "innerHTML"])) : (E(), W(Me(e.showInput ? "label" : "p"), {
                            key: 0,
                            for: e.showInput ? e.inputId : void 0
                          }, {
                            default: p(() => [
                              ae(pe(e.dangerouslyUseHTMLString ? "" : e.message), 1)
                            ]),
                            _: 1
                          }, 8, ["for"]))
                        ])
                      ], 2)) : q("v-if", !0)
                    ], 2),
                    Ot(B("div", {
                      class: K(e.ns.e("input"))
                    }, [
                      c(s, {
                        id: e.inputId,
                        ref: "inputRef",
                        modelValue: e.inputValue,
                        "onUpdate:modelValue": (h) => e.inputValue = h,
                        type: e.inputType,
                        placeholder: e.inputPlaceholder,
                        "aria-invalid": e.validateError,
                        class: K({ invalid: e.validateError }),
                        onKeydown: An(e.handleInputEnter, ["enter"])
                      }, null, 8, ["id", "modelValue", "onUpdate:modelValue", "type", "placeholder", "aria-invalid", "class", "onKeydown"]),
                      B("div", {
                        class: K(e.ns.e("errormsg")),
                        style: he({
                          visibility: e.editorErrorMessage ? "visible" : "hidden"
                        })
                      }, pe(e.editorErrorMessage), 7)
                    ], 2), [
                      [zt, e.showInput]
                    ])
                  ], 10, ["id"]),
                  B("div", {
                    class: K(e.ns.e("btns"))
                  }, [
                    e.showCancelButton ? (E(), W(u, {
                      key: 0,
                      loading: e.cancelButtonLoading,
                      "loading-icon": e.cancelButtonLoadingIcon,
                      class: K([e.cancelButtonClass]),
                      round: e.roundButton,
                      size: e.btnSize,
                      onClick: (h) => e.handleAction("cancel"),
                      onKeydown: An(rt((h) => e.handleAction("cancel"), ["prevent"]), ["enter"])
                    }, {
                      default: p(() => [
                        ae(pe(e.cancelButtonText || e.t("el.messagebox.cancel")), 1)
                      ]),
                      _: 1
                    }, 8, ["loading", "loading-icon", "class", "round", "size", "onClick", "onKeydown"])) : q("v-if", !0),
                    Ot(c(u, {
                      ref: "confirmRef",
                      type: "primary",
                      loading: e.confirmButtonLoading,
                      "loading-icon": e.confirmButtonLoadingIcon,
                      class: K([e.confirmButtonClasses]),
                      round: e.roundButton,
                      disabled: e.confirmButtonDisabled,
                      size: e.btnSize,
                      onClick: (h) => e.handleAction("confirm"),
                      onKeydown: An(rt((h) => e.handleAction("confirm"), ["prevent"]), ["enter"])
                    }, {
                      default: p(() => [
                        ae(pe(e.confirmButtonText || e.t("el.messagebox.confirm")), 1)
                      ]),
                      _: 1
                    }, 8, ["loading", "loading-icon", "class", "round", "disabled", "size", "onClick", "onKeydown"]), [
                      [zt, e.showConfirmButton]
                    ])
                  ], 2)
                ], 14, ["onClick"])
              ]),
              _: 3
            }, 8, ["trapped", "focus-trap-el", "focus-start-el", "onReleaseRequested"])
          ], 42, ["aria-label", "aria-describedby", "onClick", "onMousedown", "onMouseup"])
        ]),
        _: 3
      }, 8, ["z-index", "overlay-class", "mask"]), [
        [zt, e.visible]
      ])
    ]),
    _: 3
  }, 8, ["onAfterLeave"]);
}
var Pp = /* @__PURE__ */ St(Op, [["render", zp], ["__file", "index.vue"]]);
const wn = /* @__PURE__ */ new Map(), Lp = (e) => {
  let t = document.body;
  return e.appendTo && (Fe(e.appendTo) && (t = document.querySelector(e.appendTo)), Kt(e.appendTo) && (t = e.appendTo), Kt(t) || (qe("ElMessageBox", "the appendTo option is not an HTMLElement. Falling back to document.body."), t = document.body)), t;
}, Dp = (e, t, n = null) => {
  const o = c(Pp, e, st(e.message) || to(e.message) ? {
    default: st(e.message) ? e.message : () => e.message
  } : null);
  return o.appContext = n, $n(o, t), Lp(e).appendChild(t.firstElementChild), o.component;
}, Rp = () => document.createElement("div"), Bp = (e, t) => {
  const n = Rp();
  e.onVanish = () => {
    $n(null, n), wn.delete(r);
  }, e.onAction = (a) => {
    const l = wn.get(r);
    let s;
    e.showInput ? s = { value: r.inputValue, action: a } : s = a, e.callback ? e.callback(s, o.proxy) : a === "cancel" || a === "close" ? e.distinguishCancelAndClose && a !== "cancel" ? l.reject("close") : l.reject("cancel") : l.resolve(s);
  };
  const o = Dp(e, n, t), r = o.proxy;
  for (const a in e)
    Wn(e, a) && !Wn(r.$props, a) && (a === "closeIcon" && Zt(e[a]) ? r[a] = Oo(e[a]) : r[a] = e[a]);
  return r.visible = !0, r;
};
function tn(e, t = null) {
  if (!Pe)
    return Promise.reject();
  let n;
  return Fe(e) || to(e) ? e = {
    message: e
  } : n = e.callback, new Promise((o, r) => {
    const a = Bp(e, t ?? tn._context);
    wn.set(a, {
      options: e,
      callback: n,
      resolve: o,
      reject: r
    });
  });
}
const Np = ["alert", "confirm", "prompt"], Hp = {
  alert: { closeOnPressEscape: !1, closeOnClickModal: !1 },
  confirm: { showCancelButton: !0 },
  prompt: { showCancelButton: !0, showInput: !0 }
};
Np.forEach((e) => {
  tn[e] = Fp(e);
});
function Fp(e) {
  return (t, n, o, r) => {
    let a = "";
    return Zt(n) ? (o = n, a = "") : ui(n) ? a = "" : a = n, tn(Object.assign({
      title: a,
      message: t,
      type: "",
      ...Hp[e]
    }, o, {
      boxType: e
    }), r);
  };
}
tn.close = () => {
  wn.forEach((e, t) => {
    t.doClose();
  }), wn.clear();
};
tn._context = null;
const gt = tn;
gt.install = (e) => {
  gt._context = e._context, e.config.globalProperties.$msgbox = gt, e.config.globalProperties.$messageBox = gt, e.config.globalProperties.$alert = gt.alert, e.config.globalProperties.$confirm = gt.confirm, e.config.globalProperties.$prompt = gt.prompt;
};
const fo = gt, $p = ["onClick", "onDblclick"], Up = ["onClick"], jp = ["onClick"], Wp = /* @__PURE__ */ ee({
  __name: "LsLayout",
  props: {
    // 是否设置了组件背景
    backgroundComponent: String,
    screenBody: Object,
    screenLayout: Object
  },
  setup(e) {
    const t = xe("designContext"), n = xe("pageScales") || [1, 1], o = e, r = P(() => {
      let { backgroundMode: f, backgroundComponent: v } = o.screenBody;
      return f == "component" && !!v;
    }), a = P(() => {
      let { backgroundMode: f, backgroundColor: v, backgroundImage: C } = o.screenBody, A = {};
      return f == "normal" && (v && (A.backgroundColor = v), C && (A.backgroundImage = C, A.backgroundSize = "100% 100%")), A;
    }), l = P(() => {
      var f;
      return !!((f = t == null ? void 0 : t.designState) != null && f.designMode);
    }), s = P(() => {
      var f;
      return ((f = t == null ? void 0 : t.designState) == null ? void 0 : f.layoutKey) || 1;
    }), u = P(() => {
      var f;
      return (f = t == null ? void 0 : t.designState) == null ? void 0 : f.layoutItem;
    }), i = () => {
      l.value && t.openScreenDrawer();
    }, d = (f) => {
      l.value && (t.designState.layoutItem = /*designContext.designState.layoutItem == item ? null : */
      f);
    }, h = (f) => {
      l.value && t.openComponentDrawer(f);
    }, x = P(() => {
      if (o.screenLayout.verticalMargin > 0) {
        let f = o.screenLayout.verticalMargin / 2;
        return {
          height: `calc(100% - ${f}px)`,
          marginTop: `${f}px`
          // marginBottom: `${margin}px`
        };
      }
      return {};
    }), m = (f, v) => {
      fo.confirm("确定要删除吗?", {
        title: "提示",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        o.screenLayout.items.splice(v, 1);
      });
    };
    return (f, v) => {
      const C = R("grid-item"), A = R("grid-layout");
      return E(), F("div", {
        class: K(["ls-layout-wrap", {
          "ls-layout-design": l.value,
          "ls-animate-disabled": !l.value && !e.screenLayout.animation
        }]),
        onDblclick: i
      }, [
        (E(), W(A, {
          ref: "gridLayout",
          class: "ls-layout",
          key: s.value,
          layout: e.screenLayout.items,
          "onUpdate:layout": v[0] || (v[0] = (O) => e.screenLayout.items = O),
          "col-num": 120,
          "row-height": 1,
          "min-h": 60,
          "is-draggable": l.value,
          "is-resizable": l.value,
          "is-mirrored": !1,
          "vertical-compact": !0,
          margin: [o.screenLayout.horizontalMargin || 0, 0],
          "transform-scale": S(n)[0],
          "use-css-transforms": !0,
          style: he(a.value)
        }, {
          default: p(() => [
            r.value ? (E(), W(C, {
              key: 0,
              class: "background-item",
              static: ""
            }, {
              default: p(() => [
                (E(), W(Me("ls-" + e.screenBody.backgroundComponent)))
              ]),
              _: 1
            })) : q("", !0),
            (E(!0), F(me, null, Re(o.screenLayout.items, (O, I) => (E(), W(C, {
              class: "grid-item",
              x: O.x,
              y: O.y,
              w: O.w,
              h: O.h,
              i: O.i,
              key: O.i
            }, {
              default: p(() => [
                B("div", {
                  class: K(["ls-gi-wrap", { current: O == u.value }]),
                  style: he(x.value),
                  onClick: rt((k) => d(O), ["stop"]),
                  onDblclick: rt((k) => h(O), ["stop"])
                }, [
                  l.value ? (E(), F("div", {
                    key: 0,
                    class: "ls-component-delete",
                    onClick: (k) => m(O, I)
                  }, " x ", 8, Up)) : q("", !0),
                  l.value && !O.data.component ? (E(), F("div", {
                    key: 1,
                    class: "ls-component-placeholder",
                    onClick: rt((k) => h(O), ["stop"])
                  }, " + ", 8, jp)) : q("", !0),
                  c(Ms, {
                    data: O.data
                  }, null, 8, ["data"])
                ], 46, $p)
              ]),
              _: 2
            }, 1032, ["x", "y", "w", "h", "i"]))), 128))
          ]),
          _: 1
        }, 8, ["layout", "is-draggable", "is-resizable", "margin", "transform-scale", "style"]))
      ], 34);
    };
  }
}), Gp = /* @__PURE__ */ ct(Wp, [["__scopeId", "data-v-829cbfab"]]);
let qp = 1;
const vr = () => (/* @__PURE__ */ new Date()).getTime().toString(16) + "-" + qp++, ji = [
  { label: "YouSheBiaoTiHei", value: "YouSheBiaoTiHei" },
  { label: "DIN-Black", value: "DIN-Black" },
  { label: "DIN-Bold", value: "DIN-Bold" },
  { label: "DIN-Light", value: "DIN-Light" },
  { label: "DIN-Medium", value: "DIN-Medium" },
  { label: "DIN_Condensed_Bold", value: "DIN_Condensed_Bold" },
  { label: "微软雅黑", value: "Microsoft YaHei" },
  { label: "宋体", value: "SimSun" },
  { label: "黑体", value: "SimHei" },
  { label: "仿宋", value: "FangSong" },
  { label: "楷体", value: "KaiTi" },
  { label: "幼圆", value: "YouYuan" }
], xn = () => !1, Qt = (e) => JSON.parse(JSON.stringify(e)), Yp = ["src"], Zp = ["src"], Kp = ["src"], Xp = ["src"], Qp = { class: "ls-page-wrap-inner" }, Jp = { style: { position: "absolute", top: "0", right: "0", "z-index": "200" } }, em = { style: { position: "absolute", top: "0", left: "0", "z-index": "200" } }, tm = {
  key: 1,
  class: "ls-header-mask"
}, nm = /* @__PURE__ */ ee({
  __name: "LsPage",
  props: {
    screenStruc: Object,
    viewIndex: {
      type: Number,
      default: -1
    }
  },
  emits: ["dbl-click-header"],
  setup(e, { emit: t }) {
    const n = J(), o = J("none"), r = t, a = {}, l = /* @__PURE__ */ Object.assign({
      "./assets/fullScreen.png": Jl,
      "./assets/fullScreenSelect.png": ts,
      "./assets/leftRight.png": os,
      "./assets/leftRightSelect.png": as,
      "./assets/upDown.png": ls,
      "./assets/upDownSelect.png": us
    });
    for (const [N, G] of Object.entries(l)) {
      const le = N.split("/").pop().replaceAll(/\..*/g, "");
      a[le] = G.default;
    }
    const {
      fullScreen: s,
      fullScreenSelect: u,
      leftRight: i,
      leftRightSelect: d,
      upDown: h,
      upDownSelect: x
    } = a, m = xe("designContext"), f = P(() => m == null ? void 0 : m.designState.designMode), v = e, C = P(() => v.screenStruc), A = P(() => {
      let { layout: N, viewLayouts: G } = v.screenStruc.body;
      if (typeof N == "number") {
        let le = N;
        v.viewIndex > -1 && (le = v.viewIndex);
        try {
          return (G[le] || G[0]).layout;
        } catch (H) {
          console.error("数据错误", H);
        }
        return {
          items: []
        };
      } else
        return N;
    }), O = P(() => v.screenStruc.header.hidden), I = _e({
      pageWrap: {
        background: "rgba(3, 11, 36, 0.9)"
      },
      wrapWidth: 0,
      wrapHeight: 0,
      width: 0,
      height: 0,
      headerHeight: 0,
      bodyHeight: 0
    }), k = P(() => {
      let { hidden: N, height: G, float: le, bottom: H } = C.value.header;
      return N || le ? 0 : G + (H || 0);
    }), g = () => {
      let { pageWrap: N = {}, width: G, height: le, ...H } = C.value, { height: re } = H.header;
      Object.assign(I, {
        pageWrap: N,
        width: G,
        height: le,
        headerHeight: re,
        bodyHeight: le - k.value
      });
    }, b = _e([1, 1]), w = _e([1, 1]), T = J("auto");
    Ye("pageScales", w), Ye("screenScales", b);
    const _ = P(() => {
      var le;
      let N = b[0], { toolbar: G } = C.value;
      return ((le = G == null ? void 0 : G.scalebar) == null ? void 0 : le.align) == "left" ? {
        transform: `scale(${N}, ${N})`,
        transformOrigin: "left top",
        top: `${10 * N}px`,
        left: `${10 * N}px`
      } : {
        transform: `scale(${N}, ${N})`,
        top: `${10 * N}px`,
        right: `${10 * N}px`
      };
    }), L = P(() => ({
      background: I.pageWrap.background,
      overflow: T.value
    })), M = P(() => {
      let { backgroundColor: N, backgroundImage: G } = C.value, { wrapWidth: le, wrapHeight: H, width: re, height: ue } = I, Se = re * w[0], Ce = ue * w[1];
      const ke = {};
      return Se < le && (ke.marginLeft = `${(le - Se) / 2}px`), Ce < H && (ke.marginTop = `${(H - Ce) / 2}px`), {
        ...ke,
        transform: `scale(${w[0]}, ${w[1]})`,
        backgroundColor: N,
        backgroundImage: `url("${qa(G)}")`,
        width: `${I.width}px`,
        height: `${I.height}px`
      };
    }), y = P(() => {
      let {
        height: N,
        bottom: G,
        backgroundColor: le,
        backgroundImage: H
      } = C.value.header;
      return {
        backgroundColor: le,
        backgroundImage: `url("${no(H)}")`,
        height: `${N}px`,
        marginBottom: `${G || 0}px`
      };
    }), $ = P(() => ({
      height: `${I.bodyHeight}px`
    })), Y = () => {
      const le = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth, H = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight;
      b[0] = le / 1920, b[1] = H / 1080;
    }, se = () => {
      let N = o.value;
      n.value.scrollLeft = 0, n.value.scrollTop = 0, Ee(() => {
        switch (N) {
          case "none": {
            w[0] = w[1] = 1, T.value = "auto";
            break;
          }
          case "width": {
            w[0] = w[1] = I.wrapWidth / I.width, T.value = w[0] * I.height <= I.wrapHeight ? "hidden" : "hidden auto";
            break;
          }
          case "height": {
            w[0] = w[1] = I.wrapHeight / I.height, T.value = w[0] * I.width <= I.wrapWidth ? "hidden" : "auto hidden";
            break;
          }
          case "all":
            w[0] = I.wrapWidth / I.width, w[1] = I.wrapHeight / I.height, T.value = "hidden";
        }
      });
    }, V = () => {
      o.value = o.value == "width" ? "none" : "width", se();
    }, te = () => {
      o.value = o.value == "height" ? "none" : "height", se();
    }, ie = () => {
      o.value = o.value == "all" ? "none" : "all", se();
    }, oe = J(!1), ge = () => {
      oe.value = !oe.value, n.value.parentElement.classList.toggle("ls-is-fullscreen");
    }, $e = () => {
      let { width: N, height: G } = n.value.getBoundingClientRect();
      I.wrapWidth = N - (f.value ? 2 : 0), I.wrapHeight = G - (f.value ? 2 : 0);
    }, z = () => {
      $e(), se();
    }, X = () => {
      z(), Y();
    }, j = new ResizeObserver(() => {
      z();
    }), ce = () => {
      r("dbl-click-header");
    };
    return Te(async () => {
      j.observe(n.value), window.addEventListener("resize", X), X(), ie();
    }), Qe(() => {
      j.disconnect(), window.removeEventListener("resize", X);
    }), Z(
      () => k.value,
      () => {
        g();
      }
    ), Z(
      () => C,
      (N) => {
        g();
      },
      {
        immediate: !0
      }
    ), (N, G) => (E(), F("div", {
      class: K(["ls-page", [f.value ? "ls-page-design" : "", v.screenStruc.className]])
    }, [
      C.value.toolbar.scalebar.displayMode != "never" ? (E(), F("div", {
        key: 0,
        class: "ls-page-scalebar",
        style: he(_.value)
      }, [
        S(xn)() ? (E(), F("img", {
          key: 0,
          src: oe.value ? S(u) : S(s),
          title: "全屏",
          onClick: ge
        }, null, 8, Yp)) : q("", !0),
        B("img", {
          src: o.value == "width" ? S(d) : S(i),
          title: "宽度自适应",
          onClick: V
        }, null, 8, Zp),
        B("img", {
          src: o.value == "height" ? S(x) : S(h),
          title: "高度自适应",
          onClick: te
        }, null, 8, Kp),
        B("img", {
          src: o.value == "all" ? S(u) : S(s),
          title: "整体自适应",
          onClick: ie
        }, null, 8, Xp)
      ], 4)) : q("", !0),
      B("div", {
        ref_key: "lspageWrap",
        ref: n,
        class: "ls-page-wrap",
        style: he(L.value)
      }, [
        B("div", Qp, [
          B("div", {
            class: "ls-page-main",
            style: he(M.value)
          }, [
            B("div", Jp, [
              ye(N.$slots, "tr", Un(jn({ screenScales: b, pageScales: w })), void 0, !0)
            ]),
            B("div", em, [
              ye(N.$slots, "tl", Un(jn({ screenScales: b, pageScales: w })), void 0, !0)
            ]),
            O.value ? q("", !0) : (E(), F("div", {
              key: 0,
              class: K(["ls-header", [v.screenStruc.header.className]]),
              style: he(y.value),
              onDblclick: ce
            }, [
              v.screenStruc.header.title.hidden ? q("", !0) : (E(), W(Ka, {
                key: 0,
                "header-title": C.value.header.title,
                "default-text-style": {
                  color: "#fff",
                  // fontStyle: 'italic',
                  // fontWeight: 'bold',
                  fontSize: "48px",
                  textAlign: "center"
                }
              }, null, 8, ["header-title"])),
              f.value ? (E(), F("div", tm)) : q("", !0)
            ], 38)),
            B("div", {
              class: "ls-body",
              style: he($.value)
            }, [
              c(Gp, {
                "screen-body": C.value.body,
                "screen-layout": A.value
              }, null, 8, ["screen-body", "screen-layout"])
            ], 4),
            ye(N.$slots, "default", {}, void 0, !0)
          ], 4)
        ])
      ], 4)
    ], 2));
  }
}), Wi = /* @__PURE__ */ ct(nm, [["__scopeId", "data-v-e6170eb5"]]), om = { class: "ls-view" }, rm = /* @__PURE__ */ ee({
  __name: "LsView",
  props: {
    refId: String,
    viewIndex: {
      type: Number,
      default: -1
    }
  },
  setup(e, { expose: t }) {
    const n = e;
    Ye("designContext", null), Ye("designMode", !1);
    const o = _e({
      ok: !1,
      screenStruc: null
    }), r = () => {
      if (o.ok = !1, !n.refId) {
        console.error("lsview[refId] is null");
        return;
      }
      ja(n.refId, "LargeScreen").then((l) => {
        var u;
        let s = (u = l.data) == null ? void 0 : u.layout;
        try {
          s && (o.ok = !0, o.screenStruc = JSON.parse(s.content));
        } catch {
        }
      });
    };
    return Te(() => {
      r();
    }), Z(
      () => n.refId,
      () => {
        r();
      }
    ), t({}), (a, l) => (E(), F("div", om, [
      o.ok ? (E(), W(Wi, {
        key: 0,
        "view-index": e.viewIndex,
        "screen-struc": o.screenStruc
      }, {
        tr: p((s) => [
          ye(a.$slots, "tr", Un(jn(s)), void 0, !0)
        ]),
        tl: p((s) => [
          ye(a.$slots, "tl", Un(jn(s)), void 0, !0)
        ]),
        default: p(() => [
          ye(a.$slots, "default", {}, void 0, !0)
        ]),
        _: 3
      }, 8, ["view-index", "screen-struc"])) : q("", !0)
    ]));
  }
}), am = /* @__PURE__ */ ct(rm, [["__scopeId", "data-v-27573aff"]]), im = (e) => ({
  height: 45,
  left: 10,
  float: !0,
  hidden: e,
  style: {},
  iconStyle: {
    width: "16px",
    height: "16px",
    marginRight: "8px"
  }
}), lm = (e) => {
  let t = e.toolbar;
  t ? t.scalebar || (t.scalebar = {
    align: "right",
    displayMode: "hover"
  }) : e.toolbar = {
    scalebar: {
      align: "right",
      displayMode: "hover"
    }
  }, e.viewMode || (e.viewMode = "single"), e.body.backgroundMode || (e.body.backgroundMode = "none");
}, sm = (e) => {
  let t = e.height / 1080;
  return {
    defaultComponent: {
      style: {},
      props: {},
      title: im(!1)
    },
    pageWrap: {
      background: "rgba(3, 11, 36, 0.9)"
    },
    width: e.width,
    height: e.height,
    style: {},
    header: {
      height: 95 * t,
      style: {},
      title: {
        hidden: !1,
        style: {},
        iconStyle: {}
      }
    },
    viewMode: "single",
    body: {
      backgroundMode: "none",
      style: {},
      layout: {
        items: []
      }
    },
    toolbar: {
      scalebar: {
        align: "right"
      }
    }
  };
}, um = ["src"], cm = { class: "img-list" }, dm = { style: { margin: "10px 0", display: "flex" } }, fm = ["title"], pm = ["src"], mm = { class: "img-label" }, hm = { class: "img-list" }, gm = { style: { margin: "10px 0", display: "flex", gap: "6px" } }, vm = ["title"], ym = ["src"], bm = { class: "img-label" }, wm = /* @__PURE__ */ ee({
  __name: "LsImageSelect",
  props: {
    modelValue: String,
    type: String
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = J(), o = t, r = Za(), a = Ya(), l = Ss(), s = xs(), u = e, i = J("local"), d = _e({
      searchValue: "",
      dialogVisible: !1,
      currentValue: "",
      onlineImages: []
    }), h = () => {
      d.dialogVisible = !0, d.searchValue = "", T();
    }, x = () => {
      o("update:modelValue", d.currentValue = null);
    }, m = P(() => {
      switch (u.type) {
        case "TITLE_ICON":
          return "标题图标";
        case "COMPONENT":
          return "组件背景图片";
        case "HEADER":
          return "头背景图片";
        default:
          return "大屏背景";
      }
    }), f = P(() => {
      switch (u.type) {
        case "TITLE_ICON":
        case "COMPONENT":
          return "75%";
        default:
          return "90%";
      }
    }), v = P(() => {
      switch (u.type) {
        case "TITLE_ICON":
          return 3;
        case "COMPONENT":
          return 6;
        case "HEADER":
          return 12;
        default:
          return 8;
      }
    }), C = P(() => {
      let _ = null;
      switch (u.type) {
        case "TITLE_ICON":
          _ = "54px";
          break;
        case "COMPONENT":
          _ = "240px";
          break;
        case "HEADER": {
          _ = "95px";
          break;
        }
        default:
          _ = "480px";
          break;
      }
      return {
        width: "100%",
        height: _
      };
    }), A = () => {
      switch (u.type) {
        case "TITLE_ICON":
          return s;
        case "COMPONENT":
          return l;
        case "HEADER":
          return a;
        default:
          return r;
      }
    }, O = P(() => A().filter(
      (_) => _.label.indexOf(d.searchValue) > -1
    )), I = (_) => {
      if (_) {
        if (_.startsWith("/"))
          return Yt() + _;
        switch (u.type) {
          case "TITLE_ICON":
            return Ga(_);
          case "COMPONENT":
            return Jo(_);
          case "HEADER":
            return no(_);
          default:
            return qa(_);
        }
      }
    }, k = (_) => {
      d.currentValue = _;
    }, g = () => {
      o("update:modelValue", d.currentValue), d.dialogVisible = !1;
    }, b = () => {
      n.value.click();
    }, w = (_) => {
      let L = _.target.files;
      if (((L == null ? void 0 : L.length) || 0) == 0) {
        bt.warning("文件列表为空");
        return;
      }
      if (L && L.length > 0) {
        const M = new FormData();
        M.append("category", u.type);
        for (let y of L)
          M.append("files", y);
        bs(M).then((y) => {
          y.data == "success" && (bt.success("上传成功"), T());
        });
      }
      _.target.value = null;
    }, T = () => {
      ws(u.type).then((_) => {
        d.onlineImages = (_.data || []).map((L) => {
          let { id: M, name: y } = L;
          return {
            label: y || "",
            value: "/dev-platform/resource/img/" + M
          };
        });
      });
    };
    return Z(
      () => u.modelValue,
      (_) => {
        d.currentValue = _, _ && (_.startsWith("/") ? i.value = "online" : i.value = "local");
      },
      {
        immediate: !0
      }
    ), (_, L) => {
      const M = R("el-icon"), y = R("el-input"), $ = R("el-button"), Y = R("el-col"), se = R("el-row"), V = R("el-tab-pane"), te = R("el-tabs"), ie = R("el-dialog");
      return E(), F(me, null, [
        c(y, {
          "model-value": d.currentValue,
          placeholder: "请选择",
          readonly: "",
          onClick: h
        }, {
          prefix: p(() => [
            B("img", {
              src: I(d.currentValue),
              style: { width: "16px", height: "16px" }
            }, null, 8, um)
          ]),
          suffix: p(() => [
            d.currentValue ? (E(), W(M, {
              key: 0,
              onClick: x,
              style: { cursor: "pointer", "margin-right": "2px" }
            }, {
              default: p(() => [
                c(S(so))
              ]),
              _: 1
            })) : q("", !0),
            c(M, {
              onClick: h,
              style: { cursor: "pointer" }
            }, {
              default: p(() => [
                c(S(Ai))
              ]),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["model-value"]),
        c(ie, {
          title: m.value,
          width: f.value,
          modelValue: d.dialogVisible,
          "onUpdate:modelValue": L[3] || (L[3] = (oe) => d.dialogVisible = oe),
          "append-to-body": "",
          class: "ls-image-select-dialog"
        }, {
          default: p(() => [
            c(te, {
              modelValue: i.value,
              "onUpdate:modelValue": L[2] || (L[2] = (oe) => i.value = oe)
            }, {
              default: p(() => [
                c(V, {
                  label: "本地",
                  name: "local"
                }, {
                  default: p(() => [
                    B("div", cm, [
                      B("div", dm, [
                        c(y, {
                          modelValue: d.searchValue,
                          "onUpdate:modelValue": L[0] || (L[0] = (oe) => d.searchValue = oe),
                          style: { width: "200px" },
                          placeholder: "请输入关键字"
                        }, null, 8, ["modelValue"]),
                        c($, {
                          type: "primary",
                          onClick: g,
                          style: { "margin-left": "10px" }
                        }, {
                          default: p(() => L[4] || (L[4] = [
                            ae("确定")
                          ])),
                          _: 1
                        })
                      ]),
                      c(se, null, {
                        default: p(() => [
                          (E(!0), F(me, null, Re(O.value, (oe) => (E(), W(Y, {
                            class: K(["img-col", { current: d.currentValue == oe.label }]),
                            span: v.value,
                            key: oe.label,
                            onClick: (ge) => k(oe.label),
                            onDblclick: () => {
                              k(oe.label), g();
                            }
                          }, {
                            default: p(() => [
                              B("div", {
                                class: "img-wrap",
                                title: oe.label
                              }, [
                                B("img", {
                                  src: I(oe.label),
                                  style: he(C.value)
                                }, null, 12, pm),
                                B("span", mm, pe(oe.label), 1)
                              ], 8, fm)
                            ]),
                            _: 2
                          }, 1032, ["span", "class", "onClick", "onDblclick"]))), 128))
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  _: 1
                }),
                c(V, {
                  label: "在线",
                  name: "online"
                }, {
                  default: p(() => [
                    B("div", hm, [
                      B("div", gm, [
                        c(y, {
                          modelValue: d.searchValue,
                          "onUpdate:modelValue": L[1] || (L[1] = (oe) => d.searchValue = oe),
                          style: { width: "200px" },
                          placeholder: "请输入关键字"
                        }, null, 8, ["modelValue"]),
                        c($, {
                          type: "primary",
                          onClick: g
                        }, {
                          default: p(() => L[5] || (L[5] = [
                            ae("确定")
                          ])),
                          _: 1
                        }),
                        c($, {
                          icon: S(Ud),
                          type: "success",
                          onClick: b
                        }, {
                          default: p(() => L[6] || (L[6] = [
                            ae("上传")
                          ])),
                          _: 1
                        }, 8, ["icon"]),
                        B("input", {
                          ref_key: "fileInput",
                          ref: n,
                          style: { display: "none" },
                          multiple: "",
                          type: "file",
                          accept: ".png,.jpg,.jpeg",
                          onChange: w
                        }, null, 544)
                      ]),
                      c(se, null, {
                        default: p(() => [
                          (E(!0), F(me, null, Re(d.onlineImages, (oe) => Ot((E(), W(Y, {
                            class: K(["img-col", { current: d.currentValue == oe.value }]),
                            span: v.value,
                            key: oe.value,
                            onClick: (ge) => k(oe.value),
                            onDblclick: () => {
                              k(oe.value), g();
                            }
                          }, {
                            default: p(() => [
                              B("div", {
                                class: "img-wrap",
                                title: oe.label
                              }, [
                                B("img", {
                                  src: S(Yt)() + oe.value,
                                  style: he(C.value)
                                }, null, 12, ym),
                                B("span", bm, pe(oe.label), 1)
                              ], 8, vm)
                            ]),
                            _: 2
                          }, 1032, ["span", "class", "onClick", "onDblclick"])), [
                            [zt, oe.label.indexOf(d.searchValue) > -1]
                          ])), 128))
                        ]),
                        _: 1
                      })
                    ])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["modelValue"])
          ]),
          _: 1
        }, 8, ["title", "width", "modelValue"])
      ], 64);
    };
  }
}), qt = /* @__PURE__ */ ct(wm, [["__scopeId", "data-v-1e200a00"]]), xm = ["src"], Sm = /* @__PURE__ */ ee({
  __name: "LsHeaderDrawer",
  props: {
    screenHeader: Object,
    modelValue: Boolean
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = e, o = t, r = _e({
      drawerVisible: !1
    }), a = P({
      get() {
        var s;
        return ((s = n.screenHeader.title) == null ? void 0 : s.hidden) == !1;
      },
      set(s) {
        n.screenHeader.title ? n.screenHeader.title.hidden = !s : n.screenHeader.title = {
          hidden: !s,
          style: {}
        };
      }
    }), l = () => {
      o("update:modelValue", r.drawerVisible = !1);
    };
    return Z(
      () => n.modelValue,
      (s) => {
        r.drawerVisible = s;
      },
      {
        immediate: !0
      }
    ), (s, u) => {
      const i = R("el-color-picker"), d = R("el-form-item"), h = R("el-col"), x = R("el-input-number"), m = R("el-row"), f = R("el-input"), v = R("el-tab-pane"), C = R("el-switch"), A = R("el-option"), O = R("el-select"), I = R("el-tabs"), k = R("el-form"), g = R("el-drawer");
      return E(), W(g, {
        modelValue: r.drawerVisible,
        "onUpdate:modelValue": u[18] || (u[18] = (b) => r.drawerVisible = b),
        title: "头部设置",
        size: "50%",
        class: "ls-drawer ls-header-drawer",
        direction: "btt",
        "append-to-body": "",
        "before-close": l
      }, {
        default: p(() => [
          c(k, { "label-width": "120px" }, {
            default: p(() => [
              c(I, null, {
                default: p(() => [
                  c(v, { label: "基本" }, {
                    default: p(() => [
                      c(m, null, {
                        default: p(() => [
                          c(h, { span: 8 }, {
                            default: p(() => [
                              c(d, { label: "背景颜色" }, {
                                default: p(() => [
                                  c(i, {
                                    "show-alpha": "",
                                    modelValue: e.screenHeader.backgroundColor,
                                    "onUpdate:modelValue": u[0] || (u[0] = (b) => e.screenHeader.backgroundColor = b)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(h, { span: 8 }, {
                            default: p(() => [
                              c(d, { label: "高度" }, {
                                default: p(() => [
                                  c(x, {
                                    min: 50,
                                    modelValue: e.screenHeader.height,
                                    "onUpdate:modelValue": u[1] || (u[1] = (b) => e.screenHeader.height = b)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(h, { span: 8 }, {
                            default: p(() => [
                              c(d, { label: "下边距" }, {
                                default: p(() => [
                                  c(x, {
                                    min: -e.screenHeader.height,
                                    modelValue: e.screenHeader.bottom,
                                    "onUpdate:modelValue": u[2] || (u[2] = (b) => e.screenHeader.bottom = b),
                                    placeholder: "请输入"
                                  }, null, 8, ["min", "modelValue"]),
                                  u[19] || (u[19] = B("span", { style: { "margin-left": "10px", color: "rgba(0, 0, 0, 0.5)" } }, "(头部与内容区的间距,支持负数)", -1))
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      c(d, { label: "背景图片" }, {
                        default: p(() => [
                          c(qt, {
                            type: "HEADER",
                            modelValue: e.screenHeader.backgroundImage,
                            "onUpdate:modelValue": u[3] || (u[3] = (b) => e.screenHeader.backgroundImage = b)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      c(d, { label: "背景预览" }, {
                        default: p(() => [
                          e.screenHeader.backgroundImage ? (E(), F("img", {
                            key: 0,
                            src: S(no)(e.screenHeader.backgroundImage)
                          }, null, 8, xm)) : q("", !0)
                        ]),
                        _: 1
                      }),
                      c(d, { label: "自定义类名" }, {
                        default: p(() => [
                          c(f, {
                            modelValue: e.screenHeader.className,
                            "onUpdate:modelValue": u[4] || (u[4] = (b) => e.screenHeader.className = b),
                            placeholder: "自定义类样式名称(研发使用)",
                            clearable: ""
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }),
                  c(v, { label: "标题" }, {
                    default: p(() => [
                      c(m, null, {
                        default: p(() => [
                          c(h, { span: 6 }, {
                            default: p(() => [
                              c(d, { label: "是否显示" }, {
                                default: p(() => [
                                  c(C, {
                                    modelValue: a.value,
                                    "onUpdate:modelValue": u[5] || (u[5] = (b) => a.value = b)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          a.value ? (E(), F(me, { key: 0 }, [
                            c(h, { span: 6 }, {
                              default: p(() => [
                                c(d, { label: "宽度" }, {
                                  default: p(() => [
                                    c(x, {
                                      min: 0,
                                      modelValue: e.screenHeader.title.width,
                                      "onUpdate:modelValue": u[6] || (u[6] = (b) => e.screenHeader.title.width = b)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 6 }, {
                              default: p(() => [
                                c(d, { label: "高度" }, {
                                  default: p(() => [
                                    c(x, {
                                      min: 0,
                                      modelValue: e.screenHeader.title.height,
                                      "onUpdate:modelValue": u[7] || (u[7] = (b) => e.screenHeader.title.height = b)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 6 }, {
                              default: p(() => [
                                c(d, { label: "背景颜色" }, {
                                  default: p(() => [
                                    c(i, {
                                      "show-alpha": "",
                                      modelValue: e.screenHeader.title.backgroundColor,
                                      "onUpdate:modelValue": u[8] || (u[8] = (b) => e.screenHeader.title.backgroundColor = b)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ], 64)) : q("", !0)
                        ]),
                        _: 1
                      }),
                      a.value ? (E(), F(me, { key: 0 }, [
                        c(d, { label: "标题文本" }, {
                          default: p(() => [
                            c(f, {
                              type: "textarea",
                              placeholder: "请输入文本",
                              clearable: "",
                              modelValue: e.screenHeader.title.text,
                              "onUpdate:modelValue": u[9] || (u[9] = (b) => e.screenHeader.title.text = b)
                            }, null, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        c(m, null, {
                          default: p(() => [
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "文本字体" }, {
                                  default: p(() => [
                                    c(O, {
                                      modelValue: e.screenHeader.title.style.fontFamily,
                                      "onUpdate:modelValue": u[10] || (u[10] = (b) => e.screenHeader.title.style.fontFamily = b),
                                      "allow-create": "",
                                      filterable: "",
                                      clearable: "",
                                      placeholder: "请选择或输入"
                                    }, {
                                      default: p(() => [
                                        (E(!0), F(me, null, Re(S(ji), (b) => (E(), W(A, lt({ ref_for: !0 }, b, {
                                          key: b.value
                                        }), null, 16))), 128))
                                      ]),
                                      _: 1
                                    }, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "字体大小" }, {
                                  default: p(() => [
                                    c(f, {
                                      modelValue: e.screenHeader.title.style.fontSize,
                                      "onUpdate:modelValue": u[11] || (u[11] = (b) => e.screenHeader.title.style.fontSize = b),
                                      placeholder: "支持像素px例如: 32px",
                                      clearable: ""
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "字体颜色" }, {
                                  default: p(() => [
                                    c(i, {
                                      "show-alpha": "",
                                      modelValue: e.screenHeader.title.style.color,
                                      "onUpdate:modelValue": u[12] || (u[12] = (b) => e.screenHeader.title.style.color = b)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "字重" }, {
                                  default: p(() => [
                                    c(f, {
                                      modelValue: e.screenHeader.title.style.fontWeight,
                                      "onUpdate:modelValue": u[13] || (u[13] = (b) => e.screenHeader.title.style.fontWeight = b),
                                      placeholder: "请输入字重取值100-900",
                                      clearable: ""
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "风格" }, {
                                  default: p(() => [
                                    c(O, {
                                      modelValue: e.screenHeader.title.style.fontStyle,
                                      "onUpdate:modelValue": u[14] || (u[14] = (b) => e.screenHeader.title.style.fontStyle = b),
                                      placeholder: "斜体/正体切换",
                                      clearable: ""
                                    }, {
                                      default: p(() => [
                                        c(A, {
                                          label: "斜体",
                                          value: "italic"
                                        }),
                                        c(A, {
                                          label: "正体",
                                          value: "normal"
                                        })
                                      ]),
                                      _: 1
                                    }, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 4 }, {
                              default: p(() => [
                                c(d, { label: "字距" }, {
                                  default: p(() => [
                                    c(f, {
                                      placeholder: "请输入数字+px, 例如: 10px",
                                      modelValue: e.screenHeader.title.style.letterSpacing,
                                      "onUpdate:modelValue": u[15] || (u[15] = (b) => e.screenHeader.title.style.letterSpacing = b)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        }),
                        c(m, null, {
                          default: p(() => [
                            c(h, { span: 6 }, {
                              default: p(() => [
                                c(d, { label: "水平偏移量(px)" }, {
                                  default: p(() => [
                                    c(x, {
                                      modelValue: e.screenHeader.title.left,
                                      "onUpdate:modelValue": u[16] || (u[16] = (b) => e.screenHeader.title.left = b),
                                      placeholder: "请输入"
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(h, { span: 6 }, {
                              default: p(() => [
                                c(d, { label: "垂直偏移量(px)" }, {
                                  default: p(() => [
                                    c(x, {
                                      modelValue: e.screenHeader.title.top,
                                      "onUpdate:modelValue": u[17] || (u[17] = (b) => e.screenHeader.title.top = b),
                                      placeholder: "请输入"
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        })
                      ], 64)) : q("", !0)
                    ]),
                    _: 1
                  }),
                  S(xn)() ? (E(), W(v, {
                    key: 0,
                    label: "配置数据"
                  }, {
                    default: p(() => [
                      c(f, {
                        type: "textarea",
                        rows: 20,
                        "model-value": JSON.stringify(e.screenHeader, null, 4)
                      }, null, 8, ["model-value"])
                    ]),
                    _: 1
                  })) : q("", !0)
                ]),
                _: 1
              })
            ]),
            _: 1
          })
        ]),
        _: 1
      }, 8, ["modelValue"]);
    };
  }
}), _m = /* @__PURE__ */ ee({
  __name: "ComponentFitWrap",
  props: {
    is: String,
    componentProps: Object
  },
  setup(e) {
    const t = J(), n = J(), o = P(() => {
      if (!r.wrapSized)
        return {};
      let u = r.width / (r.wrapWidth || 1), i = r.height / (r.wrapHeight || 1);
      return {
        transform: `scale(${u}, ${i})`
      };
    }), r = _e({
      width: 0,
      height: 0,
      wrapWidth: 1,
      wrapHeight: 1,
      wrapSized: !1
    }), a = new ResizeObserver(() => {
      s();
    }), l = () => {
      if (!n.value) return;
      let { width: u, height: i } = n.value.getBoundingClientRect();
      u > 20 && i > 20 && (r.wrapSized || (r.wrapWidth = u, r.wrapHeight = i, r.wrapSized = !0));
    }, s = () => {
      if (r.wrapSized || !t.value) return;
      let { width: u, height: i } = t.value.getBoundingClientRect();
      r.width = u, r.height = i, l();
    };
    return Te(async () => {
      a.observe(n.value), window.addEventListener("resize", s), setTimeout(s, 100);
    }), Ba(() => {
      setTimeout(s, 50);
    }), Qe(() => {
      a.disconnect(), window.removeEventListener("resize", s);
    }), (u, i) => (E(), F("div", {
      ref_key: "mainEl",
      ref: t,
      class: "component-fit-wrap"
    }, [
      B("div", {
        ref_key: "wrapEl",
        ref: n,
        class: "ls-component-wrap",
        style: he(o.value)
      }, [
        (E(), W(Me(e.is), lt(e.componentProps, { "selection-mode": !0 }), null, 16))
      ], 4)
    ], 512));
  }
}), Em = /* @__PURE__ */ ct(_m, [["__scopeId", "data-v-cae7608d"]]), Cm = { class: "component-list" }, km = { style: { margin: "10px 0", display: "flex", "justify-content": "space-between" } }, Am = {
  class: "component-label",
  style: { height: "36px" }
}, Im = {
  class: "component-wrap",
  style: { height: "calc(100% - 36px)" }
}, Tm = /* @__PURE__ */ ee({
  __name: "LsComponentSelect",
  props: {
    modelValue: String,
    type: String,
    placeholder: {
      type: String,
      default: "请选择组件"
    },
    data: Object
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = t, o = er(), r = _s(), a = e, l = [
      { label: "全部组件", value: "" },
      ...r.filter((f) => !!f).map((f) => ({
        label: f,
        value: f
      }))
    ], s = _e({
      groupLabel: "",
      searchValue: "",
      dialogVisible: !1,
      currentValue: "",
      currentLabel: ""
    }), u = () => {
      s.dialogVisible = !0, s.searchValue = "";
    }, i = () => {
      n("update:modelValue", s.currentValue = null), s.currentLabel = null;
    }, d = P(() => {
      switch (a.type) {
        case "icon":
        case "component":
          return "75%";
        default:
          return "90%";
      }
    }), h = P(() => o.filter((f) => (!s.groupLabel || s.groupLabel == f.group) && f.name.indexOf(s.searchValue) > -1)), x = (f) => {
      s.currentValue = f.key, s.currentLabel = f.name;
    }, m = () => {
      var f;
      n("update:modelValue", s.currentValue), (f = a.data) != null && f.title && (a.data.title.text = s.currentLabel), s.dialogVisible = !1;
    };
    return Z(
      () => a.modelValue,
      (f) => {
        if (s.currentValue = f, f) {
          const v = o.find(
            (C) => C.key == f
          );
          s.currentLabel = v == null ? void 0 : v.name;
        } else
          s.currentLabel = null;
      },
      {
        immediate: !0
      }
    ), (f, v) => {
      const C = R("el-icon"), A = R("el-input"), O = R("el-segmented"), I = R("el-divider"), k = R("el-col"), g = R("el-row"), b = R("el-dialog");
      return E(), F(me, null, [
        c(A, {
          "model-value": s.currentLabel,
          placeholder: e.placeholder,
          readonly: "",
          onClick: u
        }, {
          suffix: p(() => [
            s.currentValue ? (E(), W(C, {
              key: 0,
              onClick: i,
              style: { cursor: "pointer", "margin-right": "2px" }
            }, {
              default: p(() => [
                c(S(so))
              ]),
              _: 1
            })) : q("", !0),
            c(C, {
              onClick: u,
              style: { cursor: "pointer" }
            }, {
              default: p(() => [
                c(S(Ai))
              ]),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["model-value", "placeholder"]),
        s.dialogVisible ? (E(), W(b, {
          key: 0,
          title: "选择组件",
          width: d.value,
          modelValue: s.dialogVisible,
          "onUpdate:modelValue": v[2] || (v[2] = (w) => s.dialogVisible = w),
          "append-to-body": "",
          class: "ls-component-select-dialog"
        }, {
          default: p(() => [
            B("div", Cm, [
              B("div", km, [
                c(O, {
                  modelValue: s.groupLabel,
                  "onUpdate:modelValue": v[0] || (v[0] = (w) => s.groupLabel = w),
                  options: l
                }, null, 8, ["modelValue"]),
                B("div", null, [
                  c(A, {
                    modelValue: s.searchValue,
                    "onUpdate:modelValue": v[1] || (v[1] = (w) => s.searchValue = w),
                    style: { width: "200px" },
                    placeholder: "请输入关键字过滤"
                  }, null, 8, ["modelValue"])
                ])
              ]),
              c(I),
              c(g, null, {
                default: p(() => [
                  (E(!0), F(me, null, Re(h.value, (w) => (E(), W(k, {
                    class: "component-col",
                    span: 8,
                    key: w.key,
                    onClick: (T) => x(w),
                    onDblclick: () => {
                      x(w), m();
                    }
                  }, {
                    default: p(() => [
                      B("div", {
                        class: K(["component-item", { current: s.currentValue == w.key }])
                      }, [
                        B("div", Am, [
                          B("span", null, pe(w.name), 1)
                        ]),
                        B("div", Im, [
                          c(Em, {
                            is: w.component,
                            "component-props": {}
                          }, null, 8, ["is"])
                        ])
                      ], 2)
                    ]),
                    _: 2
                  }, 1032, ["onClick", "onDblclick"]))), 128))
                ]),
                _: 1
              })
            ])
          ]),
          _: 1
        }, 8, ["width", "modelValue"])) : q("", !0)
      ], 64);
    };
  }
}), Gi = /* @__PURE__ */ ct(Tm, [["__scopeId", "data-v-2e6c618a"]]), Vm = ["onClick"], Mm = /* @__PURE__ */ ee({
  __name: "LsScreenDrawer",
  props: {
    screenStruc: Object,
    screenLayout: Object,
    modelValue: Boolean
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = e, o = t, r = _e({
      drawerVisible: !1,
      tabActive: "base",
      viewEditIndex: -1
    }), a = () => {
      let { screenStruc: i } = n;
      if (i.viewMode == "single") {
        let { layout: d } = i.body;
        typeof d == "number" && (i.body.layout = 0);
      } else {
        let { layout: d, viewLayouts: h } = i.body;
        (!h || h.length == 0) && (i.body.viewLayouts = [
          {
            name: "默认视图",
            layout: d
          }
        ], i.body.layout = 0);
      }
    }, l = () => {
      let { viewLayouts: i } = n.screenStruc.body;
      i.push({
        name: "视图名称",
        layout: Qt(i[i.length - 1].layout)
      }), r.viewEditIndex = i.length - 1;
    }, s = (i) => {
      let { viewLayouts: d } = n.screenStruc.body;
      d.splice(i, 1);
    }, u = () => {
      o("update:modelValue", r.drawerVisible = !1);
    };
    return Z(
      () => n.modelValue,
      (i) => {
        r.drawerVisible = i, r.tabActive = "base", r.viewEditIndex = -1;
      },
      {
        immediate: !0
      }
    ), (i, d) => {
      const h = R("el-form-item"), x = R("el-color-picker"), m = R("el-input"), f = R("el-radio"), v = R("el-radio-group"), C = R("el-table-column"), A = R("el-checkbox"), O = R("el-button"), I = R("el-table"), k = R("el-tab-pane"), g = R("el-switch"), b = R("el-col"), w = R("el-row"), T = R("el-input-number"), _ = R("el-tabs"), L = R("el-form"), M = R("el-drawer");
      return E(), W(M, {
        modelValue: r.drawerVisible,
        "onUpdate:modelValue": d[15] || (d[15] = (y) => r.drawerVisible = y),
        title: "大屏设置",
        size: "50%",
        class: "ls-drawer ls-screen-drawer",
        direction: "rtl",
        "append-to-body": "",
        "before-close": u
      }, {
        default: p(() => [
          c(L, { "label-width": "150px" }, {
            default: p(() => [
              c(_, {
                modelValue: r.tabActive,
                "onUpdate:modelValue": d[14] || (d[14] = (y) => r.tabActive = y)
              }, {
                default: p(() => [
                  c(k, {
                    label: "基本",
                    name: "base"
                  }, {
                    default: p(() => [
                      c(h, { label: "分辨率" }, {
                        default: p(() => [
                          B("div", null, pe(e.screenStruc.width) + " * " + pe(e.screenStruc.height), 1)
                        ]),
                        _: 1
                      }),
                      c(h, { label: "背景颜色" }, {
                        default: p(() => [
                          c(x, {
                            "show-alpha": "",
                            modelValue: e.screenStruc.backgroundColor,
                            "onUpdate:modelValue": d[0] || (d[0] = (y) => e.screenStruc.backgroundColor = y)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      c(h, { label: "背景图片" }, {
                        default: p(() => [
                          c(qt, {
                            type: "PAGE",
                            modelValue: e.screenStruc.backgroundImage,
                            "onUpdate:modelValue": d[1] || (d[1] = (y) => e.screenStruc.backgroundImage = y)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      S(xn)() ? (E(), W(h, {
                        key: 0,
                        label: "自定义类名"
                      }, {
                        default: p(() => [
                          c(m, {
                            modelValue: e.screenStruc.className,
                            "onUpdate:modelValue": d[2] || (d[2] = (y) => e.screenStruc.className = y),
                            placeholder: "自定义类样式名称(研发使用)",
                            clearable: ""
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      })) : q("", !0),
                      c(h, { label: "视图模式" }, {
                        default: p(() => [
                          c(v, {
                            modelValue: e.screenStruc.viewMode,
                            "onUpdate:modelValue": d[3] || (d[3] = (y) => e.screenStruc.viewMode = y),
                            onChange: a
                          }, {
                            default: p(() => [
                              c(f, {
                                label: "单视图",
                                value: "single"
                              }),
                              c(f, {
                                label: "多视图",
                                value: "multiple"
                              })
                            ]),
                            _: 1
                          }, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      e.screenStruc.viewMode == "multiple" ? (E(), W(h, { key: 1 }, {
                        default: p(() => [
                          e.screenStruc.body.viewLayouts ? (E(), W(I, {
                            key: 0,
                            "empty-text": "暂无视图",
                            border: "",
                            data: e.screenStruc.body.viewLayouts
                          }, {
                            default: p(() => [
                              c(C, {
                                label: "视图名称",
                                prop: "name",
                                align: "center"
                              }, {
                                default: p(({ $index: y, row: $ }) => [
                                  y != r.viewEditIndex ? (E(), F("div", {
                                    key: 0,
                                    onClick: (Y) => r.viewEditIndex = y
                                  }, pe($.name), 9, Vm)) : (E(), W(m, {
                                    key: 1,
                                    modelValue: $.name,
                                    "onUpdate:modelValue": (Y) => $.name = Y,
                                    placeholder: "请输入视图名称",
                                    autofocus: "",
                                    onBlur: d[4] || (d[4] = (Y) => r.viewEditIndex = -1)
                                  }, null, 8, ["modelValue", "onUpdate:modelValue"]))
                                ]),
                                _: 1
                              }),
                              c(C, {
                                label: "默认",
                                prop: "name",
                                align: "center"
                              }, {
                                default: p(({ $index: y }) => [
                                  c(A, {
                                    "model-value": e.screenStruc.body.layout == y,
                                    onChange: ($) => e.screenStruc.body.layout = y
                                  }, null, 8, ["model-value", "onChange"])
                                ]),
                                _: 1
                              }),
                              c(C, {
                                width: "160px",
                                label: "操作",
                                align: "center"
                              }, {
                                default: p(({ $index: y }) => [
                                  c(O, {
                                    disabled: e.screenStruc.body.viewLayouts.length == 1 || e.screenStruc.body.layout == y,
                                    type: "primary",
                                    link: "",
                                    onClick: ($) => s(y)
                                  }, {
                                    default: p(() => d[16] || (d[16] = [
                                      ae("移除")
                                    ])),
                                    _: 2
                                  }, 1032, ["disabled", "onClick"])
                                ]),
                                header: p(() => [
                                  c(O, {
                                    type: "primary",
                                    style: { "font-size": "16px" },
                                    link: "",
                                    icon: S(fr),
                                    onClick: l
                                  }, null, 8, ["icon"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }, 8, ["data"])) : q("", !0)
                        ]),
                        _: 1
                      })) : q("", !0)
                    ]),
                    _: 1
                  }),
                  c(k, {
                    label: "内容布局",
                    name: "layout"
                  }, {
                    default: p(() => [
                      c(w, null, {
                        default: p(() => [
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "隐藏头部" }, {
                                default: p(() => [
                                  c(g, {
                                    modelValue: e.screenStruc.header.hidden,
                                    "onUpdate:modelValue": d[5] || (d[5] = (y) => e.screenStruc.header.hidden = y)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "布局动画" }, {
                                default: p(() => [
                                  c(g, {
                                    modelValue: e.screenLayout.animation,
                                    "onUpdate:modelValue": d[6] || (d[6] = (y) => e.screenLayout.animation = y)
                                  }, null, 8, ["modelValue"]),
                                  d[17] || (d[17] = B("span", { style: { opacity: "0.3", "margin-left": "8px" } }, "只有运行态生效,设计态忽略", -1))
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      c(w, null, {
                        default: p(() => [
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "水平间距" }, {
                                default: p(() => [
                                  c(T, {
                                    min: 0,
                                    modelValue: e.screenLayout.horizontalMargin,
                                    "onUpdate:modelValue": d[7] || (d[7] = (y) => e.screenLayout.horizontalMargin = y)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "垂直间距" }, {
                                default: p(() => [
                                  c(T, {
                                    min: 0,
                                    modelValue: e.screenLayout.verticalMargin,
                                    "onUpdate:modelValue": d[8] || (d[8] = (y) => e.screenLayout.verticalMargin = y)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      c(h, { label: "背景模式" }, {
                        default: p(() => [
                          c(v, {
                            modelValue: e.screenStruc.body.backgroundMode,
                            "onUpdate:modelValue": d[9] || (d[9] = (y) => e.screenStruc.body.backgroundMode = y)
                          }, {
                            default: p(() => [
                              c(f, {
                                label: "无背景",
                                value: "none"
                              }),
                              c(f, {
                                label: "普通背景",
                                value: "normal"
                              }),
                              c(f, {
                                label: "组件背景",
                                value: "component"
                              })
                            ]),
                            _: 1
                          }, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      e.screenStruc.body.backgroundMode == "normal" ? (E(), W(w, { key: 0 }, {
                        default: p(() => [
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "背景颜色" }, {
                                default: p(() => [
                                  c(x, {
                                    "show-alpha": "",
                                    modelValue: e.screenStruc.body.backgroundColor,
                                    "onUpdate:modelValue": d[10] || (d[10] = (y) => e.screenStruc.body.backgroundColor = y)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(b, { span: 12 }, {
                            default: p(() => [
                              c(h, { label: "背景图片" }, {
                                default: p(() => [
                                  c(qt, {
                                    type: "PAGE",
                                    modelValue: e.screenStruc.body.backgroundImage,
                                    "onUpdate:modelValue": d[11] || (d[11] = (y) => e.screenStruc.body.backgroundImage = y)
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      })) : e.screenStruc.body.backgroundMode == "component" ? (E(), W(h, {
                        key: 1,
                        label: "背景组件"
                      }, {
                        default: p(() => [
                          c(Gi, {
                            modelValue: e.screenStruc.body.backgroundComponent,
                            "onUpdate:modelValue": d[12] || (d[12] = (y) => e.screenStruc.body.backgroundComponent = y),
                            placeholder: "请选择一个组件作为背景"
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      })) : q("", !0)
                    ]),
                    _: 1
                  }),
                  c(k, { label: "工具栏" }, {
                    default: p(() => [
                      c(h, { label: "缩放栏位置" }, {
                        default: p(() => [
                          c(v, {
                            modelValue: e.screenStruc.toolbar.scalebar.align,
                            "onUpdate:modelValue": d[13] || (d[13] = (y) => e.screenStruc.toolbar.scalebar.align = y)
                          }, {
                            default: p(() => [
                              c(f, {
                                label: "居左",
                                value: "left"
                              }),
                              c(f, {
                                label: "居右",
                                value: "right"
                              })
                            ]),
                            _: 1
                          }, 8, ["modelValue"])
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  }),
                  S(xn)() ? (E(), W(k, {
                    key: 0,
                    label: "配置数据",
                    name: "setdata"
                  }, {
                    default: p(() => [
                      c(m, {
                        type: "textarea",
                        rows: 30,
                        "model-value": JSON.stringify(e.screenLayout.items, null, 4)
                      }, null, 8, ["model-value"])
                    ]),
                    _: 1
                  })) : q("", !0)
                ]),
                _: 1
              }, 8, ["modelValue"])
            ]),
            _: 1
          })
        ]),
        _: 1
      }, 8, ["modelValue"]);
    };
  }
}), Om = ["src"], zm = { class: "question-desc" }, Pm = ["onClick"], Lm = ["onClick"], Dm = { style: { opacity: "0.8", "font-size": "14px", "margin-left": "5px", display: "flex", "align-items": "center", color: "var(--el-color-primary)" } }, Rm = /* @__PURE__ */ ee({
  __name: "LsComponentDrawer",
  props: {
    title: {
      type: String,
      default: "组件设置"
    },
    items: Array,
    item: {
      type: Object
    },
    isDefault: Boolean,
    modelValue: Boolean
  },
  emits: ["update:modelValue", "delete-layout-item"],
  setup(e, { emit: t }) {
    const n = e, o = t, r = _e({
      drawerVisible: !1,
      tableActive: "base",
      viewEditIndex: -1
    }), a = () => {
      o("update:modelValue", r.drawerVisible = !1);
    }, l = () => {
      let { item: x, items: m } = n, f = m.find((v) => v.i == x.i);
      f && f != x && Object.assign(f, x);
    }, s = () => {
      let { item: x, items: m } = n;
      if (m.length > 0) {
        let { backgroundColor: f, backgroundImage: v, style: C } = x.data;
        for (let A of m) {
          Object.assign(A.data, {
            backgroundColor: f,
            backgroundImage: v,
            style: Qt(C)
          });
          {
            let {
              icon: O,
              height: I,
              left: k,
              top: g,
              style: b,
              backgroundImage: w,
              backgroundColor: T
            } = x.data.title;
            Object.assign(A.data.title, {
              icon: O,
              height: I,
              left: k,
              top: g,
              style: Qt(b),
              backgroundImage: w,
              backgroundColor: T
            });
          }
        }
      }
    }, u = () => {
      o("delete-layout-item", n.item, () => {
        a();
      });
    }, i = P({
      get() {
        var x;
        return ((x = n.item.data.title) == null ? void 0 : x.hidden) == !1;
      },
      set(x) {
        n.item.data.title ? n.item.data.title.hidden = !x : n.item.data.title = {
          height: 45,
          left: 10,
          hidden: !x,
          style: {},
          iconStyle: {
            width: "16px",
            height: "16px",
            marginRight: "8px"
          }
        };
      }
    }), d = (x) => {
      if (!n.item) return;
      let m = n.item.data.textContexts;
      m && m.splice(x, 1);
    }, h = () => {
      if (!n.item) return;
      let x = n.item.data.textContexts;
      x || (x = [], n.item.data.textContexts = x), x.push({
        key: "",
        value: ""
      });
    };
    return Z(
      () => n.modelValue,
      (x) => {
        r.drawerVisible = x, n.isDefault && (r.tableActive = "base");
      },
      {
        immediate: !0
      }
    ), (x, m) => {
      const f = R("el-form-item"), v = R("el-color-picker"), C = R("el-input"), A = R("el-col"), O = R("el-option"), I = R("el-select"), k = R("el-row"), g = R("el-divider"), b = R("el-switch"), w = R("el-icon"), T = R("el-table-column"), _ = R("el-button"), L = R("el-table"), M = R("el-tab-pane"), y = R("el-input-number"), $ = R("el-tabs"), Y = R("el-form"), se = R("el-drawer");
      return E(), W(se, {
        modelValue: r.drawerVisible,
        "onUpdate:modelValue": m[35] || (m[35] = (V) => r.drawerVisible = V),
        title: e.isDefault ? "默认组件配置" : "组件配置",
        size: "50%",
        class: "ls-component-drawer ls-drawer",
        direction: "rtl",
        "append-to-body": "",
        "before-close": a
      }, {
        default: p(() => [
          c(Y, { "label-width": "120px" }, {
            default: p(() => [
              c($, {
                modelValue: r.tableActive,
                "onUpdate:modelValue": m[34] || (m[34] = (V) => r.tableActive = V)
              }, {
                default: p(() => [
                  c(M, {
                    label: "基本",
                    name: "base"
                  }, {
                    default: p(() => [
                      e.isDefault ? q("", !0) : (E(), W(f, {
                        key: 0,
                        label: "选择组件"
                      }, {
                        default: p(() => [
                          c(Gi, {
                            modelValue: e.item.data.component,
                            "onUpdate:modelValue": m[0] || (m[0] = (V) => e.item.data.component = V),
                            data: e.item.data
                          }, null, 8, ["modelValue", "data"])
                        ]),
                        _: 1
                      })),
                      c(f, { label: "背景色" }, {
                        default: p(() => [
                          c(v, {
                            "show-alpha": "",
                            modelValue: e.item.data.backgroundColor,
                            "onUpdate:modelValue": m[1] || (m[1] = (V) => e.item.data.backgroundColor = V)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      c(f, { label: "背景图片" }, {
                        default: p(() => [
                          c(qt, {
                            type: "COMPONENT",
                            modelValue: e.item.data.backgroundImage,
                            "onUpdate:modelValue": m[2] || (m[2] = (V) => e.item.data.backgroundImage = V)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      c(f, { label: "背景预览" }, {
                        default: p(() => [
                          e.item.data.backgroundImage ? (E(), F("img", {
                            key: 0,
                            src: S(Jo)(e.item.data.backgroundImage),
                            style: { height: "240px", width: "100%" }
                          }, null, 8, Om)) : q("", !0)
                        ]),
                        _: 1
                      }),
                      c(f, { label: "自定义类名" }, {
                        default: p(() => [
                          c(C, {
                            modelValue: e.item.data.className,
                            "onUpdate:modelValue": m[3] || (m[3] = (V) => e.item.data.className = V),
                            placeholder: "自定义类样式名称(研发使用)",
                            clearable: ""
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      c(k, null, {
                        default: p(() => [
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "边框宽度" }, {
                                default: p(() => [
                                  c(C, {
                                    modelValue: e.item.data.style.borderWidth,
                                    "onUpdate:modelValue": m[4] || (m[4] = (V) => e.item.data.style.borderWidth = V),
                                    placeholder: "例如: 2px",
                                    clearable: ""
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "边框颜色" }, {
                                default: p(() => [
                                  c(v, {
                                    modelValue: e.item.data.style.borderColor,
                                    "onUpdate:modelValue": m[5] || (m[5] = (V) => e.item.data.style.borderColor = V),
                                    "show-alpha": "",
                                    clearable: ""
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "边框样式" }, {
                                default: p(() => [
                                  c(I, {
                                    modelValue: e.item.data.style.borderStyle,
                                    "onUpdate:modelValue": m[6] || (m[6] = (V) => e.item.data.style.borderStyle = V),
                                    placeholder: "请选择",
                                    clearable: ""
                                  }, {
                                    default: p(() => [
                                      c(O, {
                                        label: "实线",
                                        value: "solid"
                                      }),
                                      c(O, {
                                        label: "虚线",
                                        value: "dashed"
                                      })
                                    ]),
                                    _: 1
                                  }, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "圆角" }, {
                                default: p(() => [
                                  c(C, {
                                    modelValue: e.item.data.style.borderRadius,
                                    "onUpdate:modelValue": m[7] || (m[7] = (V) => e.item.data.style.borderRadius = V),
                                    placeholder: "例如: 2px",
                                    clearable: ""
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      e.item.data.component ? (E(), F(me, { key: 1 }, [
                        c(g, null, {
                          default: p(() => m[36] || (m[36] = [
                            ae("组件映射信息")
                          ])),
                          _: 1
                        }),
                        c(f, { label: "提示信息" }, {
                          default: p(() => [
                            c(C, {
                              modelValue: e.item.data.tip,
                              "onUpdate:modelValue": m[8] || (m[8] = (V) => e.item.data.tip = V),
                              type: "textarea",
                              rows: 4,
                              placeholder: "鼠标悬浮在组件上提示信息",
                              clearable: ""
                            }, null, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        c(f, { label: "关闭提示" }, {
                          default: p(() => [
                            c(b, {
                              modelValue: e.item.data.disableAutoTip,
                              "onUpdate:modelValue": m[9] || (m[9] = (V) => e.item.data.disableAutoTip = V)
                            }, null, 8, ["modelValue"]),
                            B("span", zm, [
                              c(w, null, {
                                default: p(() => [
                                  c(S(Kr))
                                ]),
                                _: 1
                              }),
                              m[37] || (m[37] = B("span", null, "关闭后在绑定的组件中可以读取信息，进行自定义提示", -1))
                            ])
                          ]),
                          _: 1
                        }),
                        c(f, { label: "文本映射" }, {
                          default: p(() => [
                            c(L, {
                              "empty-text": "暂无信息",
                              size: "small",
                              border: "",
                              data: e.item.data.textContexts || []
                            }, {
                              default: p(() => [
                                c(T, {
                                  label: "键",
                                  prop: "key",
                                  align: "center"
                                }, {
                                  default: p(({ $index: V, row: te }) => [
                                    V != r.viewEditIndex ? (E(), F("div", {
                                      key: 0,
                                      style: { "min-height": "1em" },
                                      onClick: (ie) => r.viewEditIndex = V
                                    }, pe(te.key), 9, Pm)) : (E(), W(C, {
                                      key: 1,
                                      size: "small",
                                      modelValue: te.key,
                                      "onUpdate:modelValue": (ie) => te.key = ie,
                                      autofocus: "",
                                      placeholder: "请输入键名称"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]))
                                  ]),
                                  _: 1
                                }),
                                c(T, {
                                  label: "值",
                                  prop: "value",
                                  align: "center"
                                }, {
                                  default: p(({ $index: V, row: te }) => [
                                    V != r.viewEditIndex ? (E(), F("div", {
                                      key: 0,
                                      style: { "min-height": "1em" },
                                      onClick: (ie) => r.viewEditIndex = V
                                    }, pe(te.value), 9, Lm)) : (E(), W(C, {
                                      key: 1,
                                      size: "small",
                                      modelValue: te.value,
                                      "onUpdate:modelValue": (ie) => te.value = ie,
                                      autofocus: "",
                                      placeholder: "请输入值名称"
                                    }, null, 8, ["modelValue", "onUpdate:modelValue"]))
                                  ]),
                                  _: 1
                                }),
                                c(T, {
                                  width: "160px",
                                  label: "操作",
                                  align: "left"
                                }, {
                                  default: p(({ $index: V }) => [
                                    c(_, {
                                      type: "primary",
                                      size: "small",
                                      link: "",
                                      onClick: (te) => d(V)
                                    }, {
                                      default: p(() => m[38] || (m[38] = [
                                        ae("移除")
                                      ])),
                                      _: 2
                                    }, 1032, ["onClick"]),
                                    V == r.viewEditIndex ? (E(), W(_, {
                                      key: 0,
                                      type: "success",
                                      size: "small",
                                      link: "",
                                      onClick: m[10] || (m[10] = (te) => r.viewEditIndex = -1)
                                    }, {
                                      default: p(() => m[39] || (m[39] = [
                                        ae("确定")
                                      ])),
                                      _: 1
                                    })) : q("", !0)
                                  ]),
                                  header: p(() => [
                                    c(_, {
                                      type: "primary",
                                      size: "small",
                                      style: { "font-size": "16px" },
                                      link: "",
                                      icon: S(fr),
                                      onClick: h
                                    }, null, 8, ["icon"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }, 8, ["data"])
                          ]),
                          _: 1
                        })
                      ], 64)) : q("", !0)
                    ]),
                    _: 1
                  }),
                  c(M, {
                    label: "标题",
                    name: "title"
                  }, {
                    default: p(() => [
                      c(f, { label: "显示标题" }, {
                        default: p(() => [
                          c(b, {
                            modelValue: i.value,
                            "onUpdate:modelValue": m[11] || (m[11] = (V) => i.value = V)
                          }, null, 8, ["modelValue"])
                        ]),
                        _: 1
                      }),
                      i.value ? (E(), F(me, { key: 0 }, [
                        c(k, null, {
                          default: p(() => [
                            c(A, { span: 8 }, {
                              default: p(() => [
                                c(f, { label: "标题高度" }, {
                                  default: p(() => [
                                    c(y, {
                                      modelValue: e.item.data.title.height,
                                      "onUpdate:modelValue": m[12] || (m[12] = (V) => e.item.data.title.height = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 8 }, {
                              default: p(() => [
                                c(f, { label: "位置" }, {
                                  default: p(() => [
                                    c(I, {
                                      modelValue: e.item.data.title.style.justifyContent,
                                      "onUpdate:modelValue": m[13] || (m[13] = (V) => e.item.data.title.style.justifyContent = V),
                                      placeholder: "请选择",
                                      clearable: ""
                                    }, {
                                      default: p(() => [
                                        c(O, {
                                          label: "居左",
                                          value: "flex-start"
                                        }),
                                        c(O, {
                                          label: "居中",
                                          value: "center"
                                        }),
                                        c(O, {
                                          label: "居右",
                                          value: "flex-end"
                                        })
                                      ]),
                                      _: 1
                                    }, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        }),
                        c(f, { label: "标题文本" }, {
                          default: p(() => [
                            c(C, {
                              placeholder: "请输入文本",
                              clearable: "",
                              modelValue: e.item.data.title.text,
                              "onUpdate:modelValue": m[14] || (m[14] = (V) => e.item.data.title.text = V)
                            }, null, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        c(k, null, {
                          default: p(() => [
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "背景图片" }, {
                                  default: p(() => [
                                    c(qt, {
                                      type: "TITLE_ICON",
                                      modelValue: e.item.data.title.backgroundImage,
                                      "onUpdate:modelValue": m[15] || (m[15] = (V) => e.item.data.title.backgroundImage = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "背景色" }, {
                                  default: p(() => [
                                    c(v, {
                                      "show-alpha": "",
                                      modelValue: e.item.data.title.backgroundColor,
                                      "onUpdate:modelValue": m[16] || (m[16] = (V) => e.item.data.title.backgroundColor = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        }),
                        c(k, null, {
                          default: p(() => [
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "图标" }, {
                                  default: p(() => [
                                    c(qt, {
                                      type: "TITLE_ICON",
                                      modelValue: e.item.data.title.icon,
                                      "onUpdate:modelValue": m[17] || (m[17] = (V) => e.item.data.title.icon = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            e.item.data.title.icon ? (E(), F(me, { key: 0 }, [
                              c(A, { span: 12 }, {
                                default: p(() => [
                                  c(f, { label: "图标字距" }, {
                                    default: p(() => [
                                      c(C, {
                                        modelValue: e.item.data.title.iconStyle.marginRight,
                                        "onUpdate:modelValue": m[18] || (m[18] = (V) => e.item.data.title.iconStyle.marginRight = V),
                                        clearable: "",
                                        placeholder: "请输入例如: 8px"
                                      }, null, 8, ["modelValue"])
                                    ]),
                                    _: 1
                                  })
                                ]),
                                _: 1
                              }),
                              c(A, { span: 12 }, {
                                default: p(() => [
                                  c(f, { label: "图标宽度" }, {
                                    default: p(() => [
                                      c(C, {
                                        modelValue: e.item.data.title.iconStyle.width,
                                        "onUpdate:modelValue": m[19] || (m[19] = (V) => e.item.data.title.iconStyle.width = V),
                                        placeholder: "请输入宽度例如: 16px",
                                        clearable: ""
                                      }, null, 8, ["modelValue"])
                                    ]),
                                    _: 1
                                  })
                                ]),
                                _: 1
                              }),
                              c(A, { span: 12 }, {
                                default: p(() => [
                                  c(f, { label: "图标高度" }, {
                                    default: p(() => [
                                      c(C, {
                                        modelValue: e.item.data.title.iconStyle.height,
                                        "onUpdate:modelValue": m[20] || (m[20] = (V) => e.item.data.title.iconStyle.height = V),
                                        placeholder: "请输入高度例如: 16px",
                                        clearable: ""
                                      }, null, 8, ["modelValue"])
                                    ]),
                                    _: 1
                                  })
                                ]),
                                _: 1
                              })
                            ], 64)) : q("", !0)
                          ]),
                          _: 1
                        }),
                        c(k, null, {
                          default: p(() => [
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "文本字体" }, {
                                  default: p(() => [
                                    c(I, {
                                      modelValue: e.item.data.title.style.fontFamily,
                                      "onUpdate:modelValue": m[21] || (m[21] = (V) => e.item.data.title.style.fontFamily = V),
                                      "allow-create": "",
                                      filterable: "",
                                      clearable: "",
                                      placeholder: "请选择或输入"
                                    }, {
                                      default: p(() => [
                                        (E(!0), F(me, null, Re(S(ji), (V) => (E(), W(O, lt({ ref_for: !0 }, V, {
                                          key: V.value
                                        }), null, 16))), 128))
                                      ]),
                                      _: 1
                                    }, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "字体大小" }, {
                                  default: p(() => [
                                    c(C, {
                                      modelValue: e.item.data.title.style.fontSize,
                                      "onUpdate:modelValue": m[22] || (m[22] = (V) => e.item.data.title.style.fontSize = V),
                                      placeholder: "支持像素px例如: 32px",
                                      clearable: ""
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 24 }, {
                              default: p(() => [
                                c(f, { label: "字体渐变色" }, {
                                  default: p(() => [
                                    c(C, {
                                      placeholder: "请输入渐变色",
                                      clearable: "",
                                      modelValue: e.item.data.title.style.background,
                                      "onUpdate:modelValue": m[23] || (m[23] = (V) => e.item.data.title.style.background = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "字体颜色" }, {
                                  default: p(() => [
                                    c(v, {
                                      "show-alpha": "",
                                      modelValue: e.item.data.title.style.color,
                                      "onUpdate:modelValue": m[24] || (m[24] = (V) => e.item.data.title.style.color = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "字重" }, {
                                  default: p(() => [
                                    c(C, {
                                      modelValue: e.item.data.title.style.fontWeight,
                                      "onUpdate:modelValue": m[25] || (m[25] = (V) => e.item.data.title.style.fontWeight = V),
                                      placeholder: "请输入字重取值100-900",
                                      clearable: ""
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "风格" }, {
                                  default: p(() => [
                                    c(I, {
                                      modelValue: e.item.data.title.style.fontStyle,
                                      "onUpdate:modelValue": m[26] || (m[26] = (V) => e.item.data.title.style.fontStyle = V),
                                      placeholder: "斜体/正体切换",
                                      clearable: ""
                                    }, {
                                      default: p(() => [
                                        c(O, {
                                          label: "斜体",
                                          value: "italic"
                                        }),
                                        c(O, {
                                          label: "正体",
                                          value: "normal"
                                        })
                                      ]),
                                      _: 1
                                    }, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "字距" }, {
                                  default: p(() => [
                                    c(C, {
                                      placeholder: "请输入数字+px, 例如: 10px",
                                      modelValue: e.item.data.title.style.letterSpacing,
                                      "onUpdate:modelValue": m[27] || (m[27] = (V) => e.item.data.title.style.letterSpacing = V)
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "水平偏移" }, {
                                  default: p(() => [
                                    c(y, {
                                      modelValue: e.item.data.title.left,
                                      "onUpdate:modelValue": m[28] || (m[28] = (V) => e.item.data.title.left = V),
                                      placeholder: "请输入"
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            }),
                            c(A, { span: 12 }, {
                              default: p(() => [
                                c(f, { label: "垂直偏移" }, {
                                  default: p(() => [
                                    c(y, {
                                      modelValue: e.item.data.title.top,
                                      "onUpdate:modelValue": m[29] || (m[29] = (V) => e.item.data.title.top = V),
                                      placeholder: "请输入"
                                    }, null, 8, ["modelValue"])
                                  ]),
                                  _: 1
                                })
                              ]),
                              _: 1
                            })
                          ]),
                          _: 1
                        })
                      ], 64)) : q("", !0)
                    ]),
                    _: 1
                  }),
                  e.isDefault ? q("", !0) : (E(), W(M, {
                    key: 0,
                    label: "布局",
                    name: "layout"
                  }, {
                    default: p(() => [
                      c(k, null, {
                        default: p(() => [
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "宽度(比例)" }, {
                                default: p(() => [
                                  c(y, {
                                    min: 10,
                                    max: 120,
                                    modelValue: e.item.w,
                                    "onUpdate:modelValue": m[30] || (m[30] = (V) => e.item.w = V),
                                    placeholder: "请输入",
                                    onChange: l
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "高度(px)" }, {
                                default: p(() => [
                                  c(y, {
                                    min: 100,
                                    step: 10,
                                    modelValue: e.item.h,
                                    "onUpdate:modelValue": m[31] || (m[31] = (V) => e.item.h = V),
                                    placeholder: "请输入",
                                    onChange: l
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      c(k, null, {
                        default: p(() => [
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "水平x" }, {
                                default: p(() => [
                                  c(y, {
                                    min: 0,
                                    max: 100,
                                    modelValue: e.item.x,
                                    "onUpdate:modelValue": m[32] || (m[32] = (V) => e.item.x = V),
                                    placeholder: "请输入",
                                    onChange: l
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          }),
                          c(A, { span: 12 }, {
                            default: p(() => [
                              c(f, { label: "垂直y" }, {
                                default: p(() => [
                                  c(y, {
                                    min: 0,
                                    disabled: "",
                                    modelValue: e.item.y,
                                    "onUpdate:modelValue": m[33] || (m[33] = (V) => e.item.y = V),
                                    placeholder: "请输入"
                                  }, null, 8, ["modelValue"])
                                ]),
                                _: 1
                              })
                            ]),
                            _: 1
                          })
                        ]),
                        _: 1
                      }),
                      c(f, { label: "操作" }, {
                        default: p(() => [
                          c(_, {
                            type: "danger",
                            onClick: u
                          }, {
                            default: p(() => m[40] || (m[40] = [
                              ae("删除")
                            ])),
                            _: 1
                          })
                        ]),
                        _: 1
                      })
                    ]),
                    _: 1
                  })),
                  S(xn)() ? (E(), W(M, {
                    key: 1,
                    label: "配置数据"
                  }, {
                    default: p(() => [
                      c(C, {
                        type: "textarea",
                        rows: 30,
                        "model-value": JSON.stringify(e.item, null, 4),
                        style: { "margin-bottom": "20px" }
                      }, null, 8, ["model-value"])
                    ]),
                    _: 1
                  })) : q("", !0)
                ]),
                _: 1
              }, 8, ["modelValue"]),
              e.isDefault ? (E(), W(f, {
                key: 0,
                label: "操作"
              }, {
                default: p(() => [
                  c(_, {
                    type: "success",
                    onClick: s
                  }, {
                    default: p(() => m[41] || (m[41] = [
                      ae("同步更新")
                    ])),
                    _: 1
                  }),
                  B("span", Dm, [
                    c(w, null, {
                      default: p(() => [
                        c(S(Kr))
                      ]),
                      _: 1
                    }),
                    m[42] || (m[42] = ae(" 操作会将当前修改快速同步更新到所有栏目(注: 仅限当前状态下的所有栏目) "))
                  ])
                ]),
                _: 1
              })) : q("", !0)
            ]),
            _: 1
          })
        ]),
        _: 1
      }, 8, ["modelValue", "title"]);
    };
  }
}), Ho = [
  {
    name: "3-1-3",
    data: [
      { x: 0, y: 0, w: 30, h: 40 },
      { x: 0, y: 40, w: 30, h: 40 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 0, w: 60, h: 120 },
      { x: 90, y: 0, w: 30, h: 40 },
      { x: 90, y: 40, w: 30, h: 40 },
      { x: 90, y: 80, w: 30, h: 40 }
    ]
  },
  {
    name: "3-1-2-1",
    data: [
      { x: 0, y: 0, w: 30, h: 40 },
      { x: 0, y: 40, w: 30, h: 40 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 0, w: 60, h: 120 },
      { x: 90, y: 0, w: 30, h: 40 },
      { x: 90, y: 40, w: 30, h: 80 }
    ]
  },
  {
    name: "3-1-2-2",
    data: [
      { x: 0, y: 0, w: 30, h: 40 },
      { x: 0, y: 40, w: 30, h: 40 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 0, w: 60, h: 120 },
      { x: 90, y: 0, w: 30, h: 80 },
      { x: 90, y: 80, w: 30, h: 40 }
    ]
  },
  {
    name: "3-2-3-1",
    data: [
      { x: 0, y: 0, w: 40, h: 40 },
      { x: 0, y: 40, w: 40, h: 40 },
      { x: 0, y: 80, w: 40, h: 40 },
      { x: 40, y: 0, w: 40, h: 60 },
      { x: 40, y: 60, w: 40, h: 60 },
      { x: 80, y: 0, w: 40, h: 40 },
      { x: 80, y: 40, w: 40, h: 40 },
      { x: 80, y: 80, w: 40, h: 40 }
    ]
  },
  {
    name: "3-2-3-2",
    data: [
      { x: 0, y: 0, w: 30, h: 40 },
      { x: 0, y: 40, w: 30, h: 40 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 0, w: 60, h: 80 },
      { x: 30, y: 80, w: 60, h: 40 },
      { x: 90, y: 0, w: 30, h: 40 },
      { x: 90, y: 40, w: 30, h: 40 },
      { x: 90, y: 80, w: 30, h: 40 }
    ]
  },
  {
    name: "3-2-3-3",
    data: [
      { x: 0, y: 0, w: 30, h: 40 },
      { x: 0, y: 40, w: 30, h: 40 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 0, w: 60, h: 40 },
      { x: 30, y: 40, w: 60, h: 80 },
      { x: 90, y: 0, w: 30, h: 40 },
      { x: 90, y: 40, w: 30, h: 40 },
      { x: 90, y: 80, w: 30, h: 40 }
    ]
  },
  {
    name: "1-3",
    data: [
      { x: 0, y: 0, w: 80, h: 120 },
      { x: 80, y: 0, w: 40, h: 40 },
      { x: 80, y: 40, w: 40, h: 40 },
      { x: 80, y: 80, w: 40, h: 40 }
    ]
  },
  {
    name: "2-1-1",
    data: [
      { x: 0, y: 0, w: 30, h: 60 },
      { x: 0, y: 60, w: 30, h: 60 },
      { x: 30, y: 0, w: 60, h: 120 },
      { x: 90, y: 0, w: 30, h: 120 }
    ]
  },
  {
    name: "-",
    data: [
      { x: 0, y: 0, w: 80, h: 80 },
      { x: 0, y: 80, w: 40, h: 40 },
      { x: 40, y: 80, w: 40, h: 40 },
      { x: 80, y: 0, w: 40, h: 40 },
      { x: 80, y: 40, w: 40, h: 40 },
      { x: 80, y: 80, w: 40, h: 40 }
    ]
  },
  {
    name: "-",
    data: [
      { x: 0, y: 0, w: 40, h: 80 },
      { x: 40, y: 0, w: 40, h: 80 },
      { x: 80, y: 0, w: 40, h: 80 },
      { x: 0, y: 80, w: 40, h: 40 },
      { x: 40, y: 80, w: 40, h: 40 },
      { x: 80, y: 80, w: 40, h: 40 }
    ]
  },
  {
    name: "-",
    data: [
      { x: 0, y: 0, w: 90, h: 30 },
      { x: 0, y: 30, w: 60, h: 50 },
      { x: 60, y: 30, w: 30, h: 50 },
      { x: 0, y: 80, w: 30, h: 40 },
      { x: 30, y: 80, w: 60, h: 40 },
      { x: 90, y: 0, w: 30, h: 60 },
      { x: 90, y: 60, w: 30, h: 60 }
    ]
  },
  {
    name: "2-1-1",
    data: [
      { x: 0, y: 0, w: 30, h: 120 },
      { x: 30, y: 0, w: 60, h: 120 },
      { x: 90, y: 0, w: 30, h: 60 },
      { x: 90, y: 60, w: 30, h: 60 }
    ]
  }
], qi = (e, t, n, o) => {
  o || (o = Ho[0]);
  let r = n.height - ((e.header.bottom || 0) + e.header.height) - 4;
  t.items = o.data.map((a) => {
    let { x: l, y: s, w: u, h: i } = a;
    return {
      x: l,
      w: u,
      y: Math.floor(s * r / 120),
      h: Math.floor(i * r / 120),
      minW: 5,
      minH: 60,
      i: vr(),
      data: Qt(e.defaultComponent)
    };
  });
}, Bm = { class: "ls-quick-layout-dialog-body" }, Nm = { class: "layout-template" }, Hm = ["onClick"], Fm = { style: { height: "100%", "background-color": "#cccccc", display: "flex", "align-items": "center", "justify-content": "center", position: "relative" } }, $m = ["onClick"], Um = { style: { "text-align": "center" } }, jm = /* @__PURE__ */ ee({
  __name: "LsQuickLayoutDialog",
  props: {
    record: Object,
    screenStruc: Object,
    screenLayout: Object,
    modelValue: Boolean
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = J(), o = J("template"), r = e, a = t, l = J(0), s = _e({
      dialogVisible: !1,
      rows: 3,
      cols: 3,
      customLayouts: [],
      customLayoutHeight: 0
    }), u = (f) => ({
      left: `${f.x * 100 / 120}%`,
      top: `${f.y * 100 / 120}%`,
      width: `${f.w * 100 / 120}%`,
      height: `${f.h * 100 / 120}%`
    }), i = P(() => s.customLayoutHeight ? {
      // height: `${state.customLayoutHeight}px`
    } : {}), d = () => {
      a("update:modelValue", s.dialogVisible = !1);
    }, h = () => {
      let { rows: f, cols: v } = s;
      const C = 120 / v, A = 120 / f;
      let O = [];
      for (let I = 0; I < f; ++I)
        for (let k = 0; k < v; ++k) {
          let g = I * v + k;
          O.push({ x: k * C, y: I * A, w: C, h: A, i: g });
        }
      s.customLayouts = O, n.value && (Ee(() => {
        s.customLayoutHeight = n.value.children.item(0).getBoundingClientRect().height;
      }), console.log("state.customLayoutHeight", s.customLayoutHeight));
    }, x = (f) => {
      let v = s.customLayouts.findIndex((C) => C == f);
      v > -1 && s.customLayouts.splice(v, 1);
    }, m = async () => {
      const { screenStruc: f, record: v } = r;
      let C = v.height - ((f.header.bottom || 0) + f.header.height) - 4, A = r.screenLayout, O = !0;
      A.items.find((k) => !!k.data.component) && (O = await fo.confirm("检查到当前大屏已配置过组件，是否清除覆盖?", {
        title: "提示",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }) == "confirm"), O && (o.value == "template" ? qi(
        f,
        A,
        v,
        Ho[l.value]
      ) : (console.log(JSON.stringify(s.customLayouts, null, 4)), A.items = s.customLayouts.map((k) => {
        let { x: g, y: b, w, h: T } = k;
        return {
          x: g,
          w,
          y: Math.floor(b * C / 120),
          h: Math.floor(T * C / 120),
          minW: 5,
          minH: 60,
          i: vr(),
          data: Qt(f.defaultComponent)
        };
      })), d());
    };
    return Z(
      () => r.modelValue,
      (f) => {
        s.dialogVisible = f, f && (o.value = "template", l.value = 0, h());
      },
      {
        immediate: !0
      }
    ), (f, v) => {
      const C = R("el-col"), A = R("el-row"), O = R("el-tab-pane"), I = R("el-radio-button"), k = R("el-radio-group"), g = R("el-form-item"), b = R("el-button"), w = R("el-form"), T = R("grid-item"), _ = R("grid-layout"), L = R("el-tabs"), M = R("el-dialog");
      return E(), W(M, {
        modelValue: s.dialogVisible,
        "onUpdate:modelValue": v[4] || (v[4] = (y) => s.dialogVisible = y),
        "append-to-body": "",
        top: "5vh",
        width: "75%",
        "before-close": d,
        title: "快速布局",
        "z-index": 1600
      }, {
        footer: p(() => [
          B("div", Um, [
            c(b, {
              type: "primary",
              onClick: m
            }, {
              default: p(() => v[7] || (v[7] = [
                ae("确定")
              ])),
              _: 1
            }),
            c(b, { onClick: d }, {
              default: p(() => v[8] || (v[8] = [
                ae("取消")
              ])),
              _: 1
            })
          ])
        ]),
        default: p(() => [
          B("div", Bm, [
            c(L, {
              modelValue: o.value,
              "onUpdate:modelValue": v[3] || (v[3] = (y) => o.value = y)
            }, {
              default: p(() => [
                c(O, {
                  label: "布局模版",
                  name: "template"
                }, {
                  default: p(() => [
                    c(A, { class: "layout-templates" }, {
                      default: p(() => [
                        (E(!0), F(me, null, Re(S(Ho), (y, $) => (E(), W(C, {
                          span: 6,
                          key: $
                        }, {
                          default: p(() => [
                            B("div", Nm, [
                              B("div", {
                                class: K(["layout-template-wrap", { selected: l.value == $ }]),
                                onClick: (Y) => l.value = $
                              }, [
                                (E(!0), F(me, null, Re(y.data, (Y, se) => (E(), F("div", {
                                  class: "layout-item",
                                  key: se,
                                  style: he(u(Y))
                                }, v[5] || (v[5] = [
                                  B("div", { class: "item-wrap" }, null, -1)
                                ]), 4))), 128))
                              ], 10, Hm)
                            ])
                          ]),
                          _: 2
                        }, 1024))), 128))
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }),
                c(O, {
                  label: "自定义",
                  name: "custom"
                }, {
                  default: p(() => [
                    c(w, { inline: "" }, {
                      default: p(() => [
                        c(g, { label: "行数" }, {
                          default: p(() => [
                            c(k, {
                              modelValue: s.rows,
                              "onUpdate:modelValue": v[0] || (v[0] = (y) => s.rows = y)
                            }, {
                              default: p(() => [
                                (E(), F(me, null, Re(6, (y) => c(I, {
                                  key: y,
                                  value: y
                                }, {
                                  default: p(() => [
                                    ae(pe(y), 1)
                                  ]),
                                  _: 2
                                }, 1032, ["value"])), 64))
                              ]),
                              _: 1
                            }, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        c(g, { label: "列数" }, {
                          default: p(() => [
                            c(k, {
                              modelValue: s.cols,
                              "onUpdate:modelValue": v[1] || (v[1] = (y) => s.cols = y)
                            }, {
                              default: p(() => [
                                (E(), F(me, null, Re(6, (y) => c(I, {
                                  key: y,
                                  value: y
                                }, {
                                  default: p(() => [
                                    ae(pe(y), 1)
                                  ]),
                                  _: 2
                                }, 1032, ["value"])), 64))
                              ]),
                              _: 1
                            }, 8, ["modelValue"])
                          ]),
                          _: 1
                        }),
                        c(g, null, {
                          default: p(() => [
                            c(b, {
                              type: "primary",
                              onClick: h
                            }, {
                              default: p(() => v[6] || (v[6] = [
                                ae("生成布局 ")
                              ])),
                              _: 1
                            })
                          ]),
                          _: 1
                        })
                      ]),
                      _: 1
                    }),
                    B("div", {
                      ref_key: "customLayout",
                      ref: n,
                      class: "custom-layout",
                      style: he(i.value)
                    }, [
                      c(_, {
                        layout: s.customLayouts,
                        "onUpdate:layout": v[2] || (v[2] = (y) => s.customLayouts = y),
                        "col-num": 120,
                        "row-height": 3,
                        "is-draggable": !0,
                        "is-resizable": !0,
                        "is-mirrored": !1,
                        "vertical-compact": !0,
                        margin: [2, 2],
                        "use-css-transforms": !0
                      }, {
                        default: p(() => [
                          (E(!0), F(me, null, Re(s.customLayouts, (y) => (E(), W(T, {
                            ref_for: !0,
                            ref: "gridItem",
                            x: y.x,
                            y: y.y,
                            w: y.w,
                            h: y.h,
                            i: y.i,
                            key: y.i
                          }, {
                            default: p(() => [
                              B("div", Fm, [
                                B("span", {
                                  style: { color: "red", float: "right", top: "0", right: "5px", cursor: "pointer", position: "absolute" },
                                  onClick: ($) => x(y)
                                }, "x", 8, $m),
                                ae(" " + pe(y.i), 1)
                              ])
                            ]),
                            _: 2
                          }, 1032, ["x", "y", "w", "h", "i"]))), 128))
                        ]),
                        _: 1
                      }, 8, ["layout"])
                    ], 4)
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["modelValue"])
          ])
        ]),
        _: 1
      }, 8, ["modelValue"]);
    };
  }
}), Wm = /* @__PURE__ */ ct(jm, [["__scopeId", "data-v-a07a6ac7"]]), Gm = { class: "ls-design-header" }, qm = { class: "ls-title" }, Ym = {
  key: 0,
  class: "ls-viewselect"
}, Zm = { class: "ls-toolbar" }, Km = { class: "ls-design-body" }, fa = "LargeScreen", Xm = /* @__PURE__ */ ee({
  __name: "LsDesign",
  props: {
    record: Object,
    modelValue: Boolean
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = e, o = P(() => n.record), r = t, a = _e({
      designMode: !0,
      dialogVisible: !1,
      quickDialogVisible: !1,
      title: "",
      screenStruc: null,
      currentViewIndex: 0,
      pageKey: 1,
      layoutKey: 1,
      isDefaultComponent: !1
    }), l = () => {
      const g = o.value;
      let { id: b, name: w } = g;
      a.title = w, ja(b, fa).then((T) => {
        var L;
        let _ = (L = T.data) == null ? void 0 : L.layout;
        console.log("layout", _);
        try {
          _ ? (a.dataLayout = _, a.screenStruc = JSON.parse(_.content), lm(a.screenStruc)) : (a.screenStruc = sm(g), s());
        } catch {
        }
      });
    }, s = () => {
      var g, b;
      a.screenStruc.header.backgroundImage = (g = Ya()[0]) == null ? void 0 : g.label, a.screenStruc.backgroundImage = (b = Za()[0]) == null ? void 0 : b.label, a.screenStruc.header.title.text = o.value.name, qi(
        a.screenStruc,
        v.value,
        o.value
      );
    }, u = () => {
      r("update:modelValue", a.dialogVisible = !1);
    }, i = () => {
      a.layoutItem = {
        data: a.screenStruc.defaultComponent
      }, a.isDefaultComponent = !0, a.componentDrawer = !0;
    };
    Ye("designContext", {
      designState: a,
      openComponentDrawer: (g) => {
        a.designMode && (a.layoutItem = g, a.isDefaultComponent = !1, a.componentDrawer = !0);
      },
      openScreenDrawer: () => {
        a.designMode && (a.screenDrawer = !0);
      }
    }), Ye(
      "designMode",
      P(() => a.designMode)
    );
    const m = () => {
      const g = o.value;
      let b = {
        ...a.dataLayout || {},
        refId: g.id,
        category: fa,
        content: JSON.stringify(a.screenStruc, null, 4)
      };
      ys(b).then((w) => {
        bt.success("保存成功"), a.dataLayout = w.data, console.log(w);
      }).catch((w) => {
        console.error(w);
      });
    }, f = P(() => {
      var b, w;
      let g;
      return !Array.isArray(g = (w = (b = a.screenStruc) == null ? void 0 : b.body) == null ? void 0 : w.viewLayouts) || g.length == 0 ? [
        {
          label: "默认视图",
          value: 0
        }
      ] : g.map((T, _) => ({
        label: T.name,
        value: _
      }));
    }), v = P(() => {
      var _;
      let { viewMode: g, body: b } = a.screenStruc, { layout: w, viewLayouts: T } = b;
      return g == "multiple" ? T ? a.currentViewIndex > T.length - 1 ? (a.currentViewIndex = T.length - 1, T[T.length - 1].layout) : T[a.currentViewIndex].layout : w : typeof w == "number" ? (_ = T[0]) == null ? void 0 : _.layout : w;
    }), C = () => {
      let g = v.value;
      g.items = [];
    }, A = () => {
    }, O = () => {
      let b = o.value.height / 1080, w = { x: 0, y: 0, w: 120, h: 300, minW: 5, minH: 60, i: vr() }, { y: T, h: _, ...L } = w, M = {
        ...L,
        y: T * b,
        h: _ * b,
        data: Qt(a.screenStruc.defaultComponent)
      };
      v.value.items.push(M);
    }, I = () => {
      a.quickDialogVisible = !0;
    }, k = (g, b) => {
      let w = v.value.items, T = w.findIndex((_) => _ == g || _.i == g.i);
      if (T == -1) {
        console.log("布局item选项不存在 item", g, w), bt.error("布局item选项不存在");
        return;
      }
      fo.confirm("确定要删除吗?", {
        title: "提示",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        w.splice(T, 1), b();
      });
    };
    return Z(
      () => n.modelValue,
      (g) => {
        a.dialogVisible = g, g && l();
      },
      {
        immediate: !0
      }
    ), (g, b) => {
      const w = R("el-divider"), T = R("el-option"), _ = R("el-select"), L = R("el-button"), M = R("el-dialog");
      return E(), F(me, null, [
        c(M, {
          class: "layout-dialog",
          modelValue: a.dialogVisible,
          "onUpdate:modelValue": b[4] || (b[4] = (y) => a.dialogVisible = y),
          "close-on-press-escape": !1,
          "close-on-click-modal": !1,
          modal: !1,
          fullscreen: "",
          "show-close": !1,
          "before-close": u,
          "append-to-body": "",
          "z-index": 1500,
          "modal-class": "ls-design-dialog"
        }, {
          header: p(() => b[9] || (b[9] = [])),
          default: p(() => [
            B("div", Gm, [
              B("div", qm, pe(a.title), 1),
              c(w, { direction: "vertical" }),
              a.screenStruc && a.screenStruc.viewMode == "multiple" ? (E(), F("div", Ym, [
                c(_, {
                  placeholder: "请选择视图",
                  size: "default",
                  "no-data-text": "暂无数据",
                  style: { width: "150px" },
                  modelValue: a.currentViewIndex,
                  "onUpdate:modelValue": b[0] || (b[0] = (y) => a.currentViewIndex = y)
                }, {
                  default: p(() => [
                    (E(!0), F(me, null, Re(f.value, (y, $) => (E(), W(T, lt({ ref_for: !0 }, y, { key: $ }), null, 16))), 128))
                  ]),
                  _: 1
                }, 8, ["modelValue"]),
                c(L, {
                  title: "视图设置",
                  style: { "font-size": "24px", "margin-left": "4px" },
                  type: "primary",
                  link: "",
                  icon: S(wo),
                  onClick: b[1] || (b[1] = (y) => a.screenDrawer = !0)
                }, null, 8, ["icon"])
              ])) : q("", !0),
              B("div", Zm, [
                a.screenStruc ? (E(), W(L, {
                  key: 0,
                  type: "success",
                  icon: S(xd),
                  onClick: m
                }, {
                  default: p(() => b[10] || (b[10] = [
                    ae("保存 ")
                  ])),
                  _: 1
                }, 8, ["icon"])) : q("", !0),
                a.screenStruc ? (E(), W(L, {
                  key: 1,
                  disabled: !a.designMode,
                  icon: S(Bd),
                  onClick: C
                }, {
                  default: p(() => b[11] || (b[11] = [
                    ae("清空")
                  ])),
                  _: 1
                }, 8, ["disabled", "icon"])) : q("", !0),
                a.screenStruc ? (E(), W(L, {
                  key: 2,
                  disabled: !a.designMode,
                  title: "还原所有修改内容",
                  icon: S(Dd),
                  onClick: A
                }, {
                  default: p(() => b[12] || (b[12] = [
                    ae("还原")
                  ])),
                  _: 1
                }, 8, ["disabled", "icon"])) : q("", !0),
                c(L, {
                  disabled: !a.designMode,
                  type: "primary",
                  icon: S(fr),
                  onClick: O
                }, {
                  default: p(() => b[13] || (b[13] = [
                    ae("添加板块")
                  ])),
                  _: 1
                }, 8, ["disabled", "icon"]),
                c(L, {
                  type: "primary",
                  icon: S(wo),
                  onClick: b[2] || (b[2] = (y) => a.screenDrawer = !0)
                }, {
                  default: p(() => b[14] || (b[14] = [
                    ae("大屏设置")
                  ])),
                  _: 1
                }, 8, ["icon"]),
                c(L, {
                  type: "primary",
                  icon: S(wo),
                  onClick: i
                }, {
                  default: p(() => b[15] || (b[15] = [
                    ae("默认组件配置")
                  ])),
                  _: 1
                }, 8, ["icon"]),
                c(L, {
                  disabled: !a.designMode,
                  type: "primary",
                  icon: S(Id),
                  onClick: I
                }, {
                  default: p(() => b[16] || (b[16] = [
                    ae("快速布局")
                  ])),
                  _: 1
                }, 8, ["disabled", "icon"]),
                c(L, {
                  type: "danger",
                  icon: S(Ci),
                  onClick: u
                }, {
                  default: p(() => b[17] || (b[17] = [
                    ae("关闭")
                  ])),
                  _: 1
                }, 8, ["icon"])
              ])
            ]),
            c(w, { class: "ls-divider" }),
            B("div", Km, [
              a.screenStruc ? (E(), W(Wi, {
                key: a.pageKey,
                "view-index": a.currentViewIndex,
                "screen-struc": a.screenStruc,
                onDblClickHeader: b[3] || (b[3] = (y) => a.headerdDrawer = !0)
              }, null, 8, ["view-index", "screen-struc"])) : q("", !0)
            ])
          ]),
          _: 1
        }, 8, ["modelValue"]),
        a.screenStruc ? (E(), W(Mm, {
          key: 0,
          modelValue: a.screenDrawer,
          "onUpdate:modelValue": b[5] || (b[5] = (y) => a.screenDrawer = y),
          "screen-struc": a.screenStruc,
          "screen-layout": v.value
        }, null, 8, ["modelValue", "screen-struc", "screen-layout"])) : q("", !0),
        a.screenStruc ? (E(), W(Sm, {
          key: 1,
          modelValue: a.headerdDrawer,
          "onUpdate:modelValue": b[6] || (b[6] = (y) => a.headerdDrawer = y),
          "screen-header": a.screenStruc.header
        }, null, 8, ["modelValue", "screen-header"])) : q("", !0),
        a.layoutItem ? (E(), W(Rm, {
          key: 2,
          items: v.value.items,
          item: a.layoutItem,
          "is-default": a.isDefaultComponent,
          modelValue: a.componentDrawer,
          "onUpdate:modelValue": b[7] || (b[7] = (y) => a.componentDrawer = y),
          onDeleteLayoutItem: k
        }, null, 8, ["items", "item", "is-default", "modelValue"])) : q("", !0),
        a.screenStruc ? (E(), W(Wm, {
          key: 3,
          modelValue: a.quickDialogVisible,
          "onUpdate:modelValue": b[8] || (b[8] = (y) => a.quickDialogVisible = y),
          record: e.record,
          "screen-struc": a.screenStruc,
          "screen-layout": v.value
        }, null, 8, ["modelValue", "record", "screen-struc", "screen-layout"])) : q("", !0)
      ], 64);
    };
  }
}), Qm = { style: { margin: "10px 0" } }, Jm = /* @__PURE__ */ ee({
  __name: "LsManage",
  setup(e) {
    const t = J(), n = J(), o = J(), r = [
      { label: "选择", type: "selection" },
      { label: "序号", slot: "index", align: "center", width: "120px" },
      { label: "大屏名称", prop: "name", slot: "name" },
      { label: "大屏标识", prop: "id" },
      { label: "创建时间", prop: "createDate" },
      { label: "操作", width: 400, slot: "ops" }
    ], a = [
      {
        label: "1920 * 1080",
        value: 0,
        width: 1920,
        height: 1080
      },
      {
        label: "2560 * 1440",
        value: 1,
        width: 2560,
        height: 1440
      },
      {
        label: "3840 * 2160",
        value: 2,
        width: 3840,
        height: 2160
      },
      {
        label: "5120 * 1440",
        value: 3,
        width: 5120,
        height: 1440
      },
      {
        label: "5120 * 2160",
        value: 4,
        width: 5120,
        height: 2160
      },
      {
        label: "7680 * 2160",
        value: 5,
        width: 7680,
        height: 2160
      }
    ], l = () => ({
      id: null,
      name: "",
      remark: "",
      width: 1920,
      height: 1080,
      dataLayoutId: null
    }), s = _e({
      query: {
        id: "",
        name: ""
      },
      currentPage: 1,
      total: 0,
      pageSize: 10,
      tableData: [],
      // 批量删除
      deleteBatch: !1,
      // 当前行
      dialogVisible: !1,
      currentRow: l(),
      // 设计态
      designRow: {},
      designDialogVisible: !1,
      // 克隆信息
      copyDialogVisible: !1,
      copyRecord: {
        name: "",
        remark: ""
      }
    }), u = P(() => (s.currentPage - 1) * s.pageSize), i = P(() => !!s.currentRow.dataLayoutId), d = () => {
      s.currentPage = 1, g();
    }, h = () => {
      Object.assign(s.query, {
        id: "",
        name: ""
      }), d();
    }, x = () => {
      s.currentRow = l(), s.dialogVisible = !0;
    }, m = (T) => {
      let { width: _, height: L } = a[T];
      Object.assign(s.currentRow, { width: _, height: L });
    }, f = () => {
      let T = t.value.getSelectionRows();
      k(T.map((_) => _.id));
    }, v = () => {
      console.log(n), console.log(n.value), n.value.validate((T, _) => {
        T && gs(s.currentRow).then((L) => {
          s.currentRow.id ? bt.success("修改成功") : bt.success("添加成功"), s.dialogVisible = !1, d();
        }).catch((L) => {
          console.error(L);
        });
      });
    }, C = (T) => {
      s.currentRow = { ...T }, s.dialogVisible = !0;
    }, A = (T) => {
      k(T.id);
    }, O = (T) => {
      s.copyRecord = { ...T }, s.copyDialogVisible = !0;
    }, I = () => {
      vs(s.copyRecord).then(() => {
        bt.success("克隆成功"), d(), s.copyDialogVisible = !1;
      }).catch(() => {
      });
    }, k = (T) => {
      fo.confirm("确定要删除吗?", {
        title: "提示",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        hs(typeof T == "string" ? T : T.join(",")).then((_) => {
          bt.success("删除成功"), d();
        }).catch((_) => {
        });
      });
    }, g = () => {
      ms({
        ...s.query,
        page: s.currentPage,
        pageSize: s.pageSize
      }).then((T) => {
        console.log(T);
        let { total: _, rows: L } = T.data;
        s.tableData = L || [], s.total = _ || 0;
      });
    }, b = (T) => {
      g();
    }, w = (T) => {
      s.designDialogVisible = !0, s.designRow = T;
    };
    return Te(() => {
      d();
    }), (T, _) => {
      const L = R("el-input"), M = R("el-form-item"), y = R("el-button"), $ = R("el-form"), Y = R("el-table-column"), se = R("el-table"), V = R("el-pagination"), te = R("el-input-number"), ie = R("el-col"), oe = R("el-dropdown-item"), ge = R("el-dropdown-menu"), $e = R("el-dropdown"), z = R("el-row"), X = R("el-dialog");
      return E(), F("div", null, [
        c($, { inline: "" }, {
          default: p(() => [
            c(M, { label: "大屏名称" }, {
              default: p(() => [
                c(L, {
                  modelValue: s.query.name,
                  "onUpdate:modelValue": _[0] || (_[0] = (j) => s.query.name = j),
                  clearable: "",
                  placeholder: "请输入大屏名称",
                  style: { width: "250px" }
                }, null, 8, ["modelValue"])
              ]),
              _: 1
            }),
            c(M, { label: "大屏id" }, {
              default: p(() => [
                c(L, {
                  modelValue: s.query.id,
                  "onUpdate:modelValue": _[1] || (_[1] = (j) => s.query.id = j),
                  clearable: "",
                  placeholder: "请输入大屏id",
                  style: { width: "250px" }
                }, null, 8, ["modelValue"])
              ]),
              _: 1
            }),
            c(M, null, {
              default: p(() => [
                c(y, {
                  type: "primary",
                  onClick: _[2] || (_[2] = (j) => d())
                }, {
                  default: p(() => _[20] || (_[20] = [
                    ae("查询")
                  ])),
                  _: 1
                }),
                c(y, {
                  onClick: _[3] || (_[3] = (j) => h())
                }, {
                  default: p(() => _[21] || (_[21] = [
                    ae("重置")
                  ])),
                  _: 1
                })
              ]),
              _: 1
            })
          ]),
          _: 1
        }),
        B("div", Qm, [
          c(y, {
            type: "success",
            onClick: _[4] || (_[4] = (j) => x())
          }, {
            default: p(() => _[22] || (_[22] = [
              ae("新增")
            ])),
            _: 1
          }),
          c(y, {
            type: "danger",
            disabled: !s.deleteBatch,
            onClick: _[5] || (_[5] = (j) => f())
          }, {
            default: p(() => _[23] || (_[23] = [
              ae("批量删除 ")
            ])),
            _: 1
          }, 8, ["disabled"])
        ]),
        c(se, {
          ref_key: "table",
          ref: t,
          border: "",
          "empty-text": "暂无数据",
          data: s.tableData,
          onSelectionChange: _[6] || (_[6] = (j) => {
            s.deleteBatch = j.length > 0;
          })
        }, {
          default: p(() => [
            (E(), F(me, null, Re(r, (j, ce) => c(Y, {
              key: ce,
              label: j.label,
              type: j.type,
              align: "center",
              width: j.width,
              prop: j.prop
            }, Kl({ _: 2 }, [
              j.slot == "index" ? {
                name: "default",
                fn: p(({ $index: N }) => [
                  B("div", null, pe(u.value + N + 1), 1)
                ]),
                key: "0"
              } : j.slot == "ops" ? {
                name: "default",
                fn: p(({ row: N }) => [
                  c(y, {
                    onClick: (G) => C(N),
                    type: "primary",
                    title: "编辑"
                  }, {
                    default: p(() => _[24] || (_[24] = [
                      ae("编辑 ")
                    ])),
                    _: 2
                  }, 1032, ["onClick"]),
                  c(y, {
                    onClick: (G) => O(N),
                    type: "primary",
                    title: "快速复制一个大屏进行修改"
                  }, {
                    default: p(() => _[25] || (_[25] = [
                      ae("克隆")
                    ])),
                    _: 2
                  }, 1032, ["onClick"]),
                  c(y, {
                    onClick: (G) => A(N),
                    type: "danger",
                    title: "删除"
                  }, {
                    default: p(() => _[26] || (_[26] = [
                      ae("删除 ")
                    ])),
                    _: 2
                  }, 1032, ["onClick"]),
                  c(y, {
                    onClick: (G) => w(N),
                    type: "success",
                    title: "设计"
                  }, {
                    default: p(() => _[27] || (_[27] = [
                      ae("设计 ")
                    ])),
                    _: 2
                  }, 1032, ["onClick"])
                ]),
                key: "1"
              } : void 0
            ]), 1032, ["label", "type", "width", "prop"])), 64))
          ]),
          _: 1
        }, 8, ["data"]),
        c(V, {
          style: { "margin-top": "10px", float: "right" },
          "hide-on-single-page": "",
          background: "",
          "current-page": s.currentPage,
          "onUpdate:currentPage": [
            _[7] || (_[7] = (j) => s.currentPage = j),
            b
          ],
          total: s.total,
          "page-size": s.pageSize,
          layout: "prev, pager, next"
        }, null, 8, ["current-page", "total", "page-size"]),
        c(X, {
          title: s.currentRow.id ? "编辑大屏" : "新增大屏",
          modelValue: s.dialogVisible,
          "onUpdate:modelValue": _[14] || (_[14] = (j) => s.dialogVisible = j),
          "before-close": () => s.dialogVisible = !1,
          center: "",
          "close-on-click-modal": !1,
          width: "40%"
        }, {
          footer: p(() => [
            c(y, {
              onClick: _[13] || (_[13] = (j) => s.dialogVisible = !1)
            }, {
              default: p(() => _[29] || (_[29] = [
                ae("关 闭")
              ])),
              _: 1
            }),
            c(y, {
              type: "primary",
              onClick: v
            }, {
              default: p(() => _[30] || (_[30] = [
                ae("确 定")
              ])),
              _: 1
            })
          ]),
          default: p(() => [
            c($, {
              ref_key: "form",
              ref: n,
              model: s.currentRow,
              "label-width": "120px"
            }, {
              default: p(() => [
                c(M, {
                  label: "大屏名称",
                  prop: "name",
                  rules: [
                    { required: !0, message: "请输入大屏名称", trigger: "blur" }
                  ]
                }, {
                  default: p(() => [
                    c(L, {
                      modelValue: s.currentRow.name,
                      "onUpdate:modelValue": _[8] || (_[8] = (j) => s.currentRow.name = j),
                      clearable: "",
                      placeholder: "请输入大屏名称"
                    }, null, 8, ["modelValue"])
                  ]),
                  _: 1
                }),
                c(z, null, {
                  default: p(() => [
                    c(ie, { span: 10 }, {
                      default: p(() => [
                        c(M, {
                          label: "屏幕宽度",
                          prop: "width",
                          rules: [
                            { required: !0, message: "请输入屏幕宽度", trigger: "blur" }
                          ]
                        }, {
                          default: p(() => [
                            c(te, {
                              disabled: i.value,
                              modelValue: s.currentRow.width,
                              "onUpdate:modelValue": _[9] || (_[9] = (j) => s.currentRow.width = j),
                              min: 0,
                              clearable: "",
                              placeholder: "请输入屏幕宽度"
                            }, null, 8, ["disabled", "modelValue"])
                          ]),
                          _: 1
                        })
                      ]),
                      _: 1
                    }),
                    c(ie, { span: 10 }, {
                      default: p(() => [
                        c(M, {
                          label: "屏幕高度",
                          prop: "height",
                          rules: [
                            { required: !0, message: "请输入屏幕高度", trigger: "blur" }
                          ]
                        }, {
                          default: p(() => [
                            c(te, {
                              disabled: i.value,
                              modelValue: s.currentRow.height,
                              "onUpdate:modelValue": _[10] || (_[10] = (j) => s.currentRow.height = j),
                              min: 0,
                              clearable: "",
                              placeholder: "请输入屏幕高度"
                            }, null, 8, ["disabled", "modelValue"])
                          ]),
                          _: 1
                        })
                      ]),
                      _: 1
                    }),
                    c(ie, { span: 4 }, {
                      default: p(() => [
                        i.value ? q("", !0) : (E(), W($e, {
                          key: 0,
                          onCommand: m
                        }, {
                          dropdown: p(() => [
                            c(ge, null, {
                              default: p(() => [
                                (E(), F(me, null, Re(a, (j, ce) => c(oe, {
                                  command: j.value,
                                  key: ce
                                }, {
                                  default: p(() => [
                                    ae(pe(j.label), 1)
                                  ]),
                                  _: 2
                                }, 1032, ["command"])), 64))
                              ]),
                              _: 1
                            })
                          ]),
                          default: p(() => [
                            c(y, {
                              type: "primary",
                              onClick: _[11] || (_[11] = () => o.value.handleOpen())
                            }, {
                              default: p(() => _[28] || (_[28] = [
                                ae("选择分辨率 ")
                              ])),
                              _: 1
                            })
                          ]),
                          _: 1
                        }))
                      ]),
                      _: 1
                    })
                  ]),
                  _: 1
                }),
                c(M, { label: "备注" }, {
                  default: p(() => [
                    c(L, {
                      modelValue: s.currentRow.remark,
                      "onUpdate:modelValue": _[12] || (_[12] = (j) => s.currentRow.remark = j),
                      type: "textarea",
                      clearable: "",
                      placeholder: "请输入备注"
                    }, null, 8, ["modelValue"])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            }, 8, ["model"])
          ]),
          _: 1
        }, 8, ["title", "modelValue", "before-close"]),
        c(X, {
          title: "大屏-克隆",
          modelValue: s.copyDialogVisible,
          "onUpdate:modelValue": _[18] || (_[18] = (j) => s.copyDialogVisible = j),
          "before-close": () => s.copyDialogVisible = !1,
          center: "",
          "close-on-click-modal": !1,
          width: "40%"
        }, {
          footer: p(() => [
            c(y, {
              onClick: _[17] || (_[17] = (j) => s.copyDialogVisible = !1)
            }, {
              default: p(() => _[31] || (_[31] = [
                ae("关 闭")
              ])),
              _: 1
            }),
            c(y, {
              type: "primary",
              onClick: I
            }, {
              default: p(() => _[32] || (_[32] = [
                ae("确 定")
              ])),
              _: 1
            })
          ]),
          default: p(() => [
            c($, { "label-width": "120px" }, {
              default: p(() => [
                c(M, { label: "新的名称" }, {
                  default: p(() => [
                    c(L, {
                      modelValue: s.copyRecord.name,
                      "onUpdate:modelValue": _[15] || (_[15] = (j) => s.copyRecord.name = j)
                    }, null, 8, ["modelValue"])
                  ]),
                  _: 1
                }),
                c(M, { label: "备注" }, {
                  default: p(() => [
                    c(L, {
                      modelValue: s.copyRecord.remark,
                      "onUpdate:modelValue": _[16] || (_[16] = (j) => s.copyRecord.remark = j),
                      type: "textarea"
                    }, null, 8, ["modelValue"])
                  ]),
                  _: 1
                })
              ]),
              _: 1
            })
          ]),
          _: 1
        }, 8, ["modelValue", "before-close"]),
        s.designDialogVisible ? (E(), W(Xm, {
          key: 0,
          modelValue: s.designDialogVisible,
          "onUpdate:modelValue": _[19] || (_[19] = (j) => s.designDialogVisible = j),
          record: s.designRow
        }, null, 8, ["modelValue", "record"])) : q("", !0)
      ]);
    };
  }
}), ve = {
  init: eh,
  document: null,
  DocumentFragment: null,
  SVGElement: null,
  SVGSVGElement: null,
  SVGElementInstance: null,
  Element: null,
  HTMLElement: null,
  Event: null,
  Touch: null,
  PointerEvent: null
};
function Ut() {
}
function eh(e) {
  const t = e;
  ve.document = t.document, ve.DocumentFragment = t.DocumentFragment || Ut, ve.SVGElement = t.SVGElement || Ut, ve.SVGSVGElement = t.SVGSVGElement || Ut, ve.SVGElementInstance = t.SVGElementInstance || Ut, ve.Element = t.Element || Ut, ve.HTMLElement = t.HTMLElement || ve.Element, ve.Event = t.Event, ve.Touch = t.Touch || Ut, ve.PointerEvent = t.PointerEvent || t.MSPointerEvent;
}
var Yi = (e) => !!(e && e.Window) && e instanceof e.Window;
let Zi, _t;
function Ki(e) {
  Zi = e;
  const t = e.document.createTextNode("");
  t.ownerDocument !== e.document && typeof e.wrap == "function" && e.wrap(t) === t && (e = e.wrap(e)), _t = e;
}
typeof window < "u" && window && Ki(window);
function Rt(e) {
  return Yi(e) ? e : (e.ownerDocument || e).defaultView || _t.window;
}
const th = (e) => e === _t || Yi(e), nh = (e) => po(e) && e.nodeType === 11, po = (e) => !!e && typeof e == "object", Xi = (e) => typeof e == "function", oh = (e) => typeof e == "number", rh = (e) => typeof e == "boolean", ah = (e) => typeof e == "string", ih = (e) => {
  if (!e || typeof e != "object")
    return !1;
  const t = Rt(e) || _t;
  return /object|function/.test(typeof Element) ? e instanceof Element || e instanceof t.Element : e.nodeType === 1 && typeof e.nodeName == "string";
}, lh = (e) => po(e) && !!e.constructor && /function Object\b/.test(e.constructor.toString()), sh = (e) => po(e) && typeof e.length < "u" && Xi(e.splice);
var D = {
  window: th,
  docFrag: nh,
  object: po,
  func: Xi,
  number: oh,
  bool: rh,
  string: ah,
  element: ih,
  plainObject: lh,
  array: sh
};
const be = {
  init: uh,
  supportsTouch: null,
  supportsPointerEvent: null,
  isIOS7: null,
  isIOS: null,
  isIe9: null,
  isOperaMobile: null,
  prefixedMatchesSelector: null,
  pEventTypes: null,
  wheelEvent: null
};
function uh(e) {
  const t = ve.Element, n = e.navigator || {};
  be.supportsTouch = "ontouchstart" in e || D.func(e.DocumentTouch) && ve.document instanceof e.DocumentTouch, be.supportsPointerEvent = n.pointerEnabled !== !1 && !!ve.PointerEvent, be.isIOS = /iP(hone|od|ad)/.test(n.platform), be.isIOS7 = /iP(hone|od|ad)/.test(n.platform) && /OS 7[^\d]/.test(n.appVersion), be.isIe9 = /MSIE 9/.test(n.userAgent), be.isOperaMobile = n.appName === "Opera" && be.supportsTouch && /Presto/.test(n.userAgent), be.prefixedMatchesSelector = "matches" in t.prototype ? "matches" : "webkitMatchesSelector" in t.prototype ? "webkitMatchesSelector" : "mozMatchesSelector" in t.prototype ? "mozMatchesSelector" : "oMatchesSelector" in t.prototype ? "oMatchesSelector" : "msMatchesSelector", be.pEventTypes = be.supportsPointerEvent ? ve.PointerEvent === e.MSPointerEvent ? {
    up: "MSPointerUp",
    down: "MSPointerDown",
    over: "mouseover",
    out: "mouseout",
    move: "MSPointerMove",
    cancel: "MSPointerCancel"
  } : {
    up: "pointerup",
    down: "pointerdown",
    over: "pointerover",
    out: "pointerout",
    move: "pointermove",
    cancel: "pointercancel"
  } : null, be.wheelEvent = ve.document && "onmousewheel" in ve.document ? "mousewheel" : "wheel";
}
const Qi = (e, t) => {
  for (const n of t)
    e.push(n);
  return e;
}, Ji = (e) => Qi([], e), mo = (e, t) => {
  for (let n = 0; n < e.length; n++)
    if (t(e[n], n, e))
      return n;
  return -1;
}, Nn = (e, t) => e[mo(e, t)];
function Jt(e) {
  const t = {};
  for (const n in e) {
    const o = e[n];
    D.plainObject(o) ? t[n] = Jt(o) : D.array(o) ? t[n] = Ji(o) : t[n] = o;
  }
  return t;
}
function Q(e, t) {
  for (const n in t)
    e[n] = t[n];
  return e;
}
let pa = 0, ot, It;
function ch(e) {
  if (ot = e.requestAnimationFrame, It = e.cancelAnimationFrame, !ot) {
    const t = ["ms", "moz", "webkit", "o"];
    for (const n of t)
      ot = e[`${n}RequestAnimationFrame`], It = e[`${n}CancelAnimationFrame`] || e[`${n}CancelRequestAnimationFrame`];
  }
  ot = ot && ot.bind(e), It = It && It.bind(e), ot || (ot = (t) => {
    const n = Date.now(), o = Math.max(0, 16 - (n - pa)), r = e.setTimeout(() => {
      t(n + o);
    }, o);
    return pa = n + o, r;
  }, It = (t) => clearTimeout(t));
}
var Gt = {
  request: (e) => ot(e),
  cancel: (e) => It(e),
  init: ch
};
function Pt(e, t) {
  let n = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : (r) => !0, o = arguments.length > 3 ? arguments[3] : void 0;
  if (o = o || {}, D.string(e) && e.search(" ") !== -1 && (e = ma(e)), D.array(e))
    return e.forEach((r) => Pt(r, t, n, o)), o;
  if (D.object(e) && (t = e, e = ""), D.func(t) && n(e))
    o[e] = o[e] || [], o[e].push(t);
  else if (D.array(t))
    for (const r of t)
      Pt(e, r, n, o);
  else if (D.object(t))
    for (const r in t) {
      const a = ma(r).map((l) => `${e}${l}`);
      Pt(a, t[r], n, o);
    }
  return o;
}
function ma(e) {
  return e.trim().split(/ +/);
}
function ha(e, t) {
  for (const n of t) {
    if (e.immediatePropagationStopped)
      break;
    n(e);
  }
}
class el {
  constructor(t) {
    this.options = void 0, this.types = {}, this.propagationStopped = !1, this.immediatePropagationStopped = !1, this.global = void 0, this.options = Q({}, t || {});
  }
  fire(t) {
    let n;
    const o = this.global;
    (n = this.types[t.type]) && ha(t, n), !t.propagationStopped && o && (n = o[t.type]) && ha(t, n);
  }
  on(t, n) {
    const o = Pt(t, n);
    for (t in o)
      this.types[t] = Qi(this.types[t] || [], o[t]);
  }
  off(t, n) {
    const o = Pt(t, n);
    for (t in o) {
      const r = this.types[t];
      if (!(!r || !r.length))
        for (const a of o[t]) {
          const l = r.indexOf(a);
          l !== -1 && r.splice(l, 1);
        }
    }
  }
  getRect(t) {
    return null;
  }
}
function Lt(e, t) {
  if (e.contains)
    return e.contains(t);
  for (; t; ) {
    if (t === e)
      return !0;
    t = t.parentNode;
  }
  return !1;
}
function tl(e, t) {
  for (; D.element(e); ) {
    if (Bt(e, t))
      return e;
    e = Et(e);
  }
  return null;
}
function Et(e) {
  let t = e.parentNode;
  if (D.docFrag(t)) {
    for (; (t = t.host) && D.docFrag(t); )
      ;
    return t;
  }
  return t;
}
function Bt(e, t) {
  return _t !== Zi && (t = t.replace(/\/deep\//g, " ")), e[be.prefixedMatchesSelector](t);
}
function Fo(e, t, n) {
  for (; D.element(e); ) {
    if (Bt(e, t))
      return !0;
    if (e = Et(e), e === n)
      return Bt(e, t);
  }
  return !1;
}
function ga(e) {
  return e.correspondingUseElement || e;
}
function dh(e) {
  return e = e || _t, {
    x: e.scrollX || e.document.documentElement.scrollLeft,
    y: e.scrollY || e.document.documentElement.scrollTop
  };
}
function yr(e) {
  const t = e instanceof ve.SVGElement ? e.getBoundingClientRect() : e.getClientRects()[0];
  return t && {
    left: t.left,
    right: t.right,
    top: t.top,
    bottom: t.bottom,
    width: t.width || t.right - t.left,
    height: t.height || t.bottom - t.top
  };
}
function br(e) {
  const t = yr(e);
  if (!be.isIOS7 && t) {
    const n = dh(Rt(e));
    t.left += n.x, t.right += n.x, t.top += n.y, t.bottom += n.y;
  }
  return t;
}
function va(e) {
  return D.string(e) ? (ve.document.querySelector(e), !0) : !1;
}
const fh = ["webkit", "moz"];
function nl(e, t) {
  e.__set || (e.__set = {});
  for (const n in t)
    fh.some((o) => n.indexOf(o) === 0) || typeof e[n] != "function" && n !== "__set" && Object.defineProperty(e, n, {
      get() {
        return n in e.__set ? e.__set[n] : e.__set[n] = t[n];
      },
      set(o) {
        e.__set[n] = o;
      },
      configurable: !0
    });
  return e;
}
var ho = (e, t) => Math.sqrt(e * e + t * t);
function ko(e, t) {
  e.page = e.page || {}, e.page.x = t.page.x, e.page.y = t.page.y, e.client = e.client || {}, e.client.x = t.client.x, e.client.y = t.client.y, e.timeStamp = t.timeStamp;
}
function ph(e, t, n) {
  e.page.x = n.page.x - t.page.x, e.page.y = n.page.y - t.page.y, e.client.x = n.client.x - t.client.x, e.client.y = n.client.y - t.client.y, e.timeStamp = n.timeStamp - t.timeStamp;
}
function mh(e, t) {
  const n = Math.max(t.timeStamp / 1e3, 1e-3);
  e.page.x = t.page.x / n, e.page.y = t.page.y / n, e.client.x = t.client.x / n, e.client.y = t.client.y / n, e.timeStamp = n;
}
function hh(e) {
  e.page.x = 0, e.page.y = 0, e.client.x = 0, e.client.y = 0;
}
function ol(e) {
  return e instanceof ve.Event || e instanceof ve.Touch;
}
function Xn(e, t, n) {
  return n = n || {}, e = e || "page", n.x = t[e + "X"], n.y = t[e + "Y"], n;
}
function gh(e, t) {
  return t = t || {
    x: 0,
    y: 0
  }, be.isOperaMobile && ol(e) ? (Xn("screen", e, t), t.x += window.scrollX, t.y += window.scrollY) : Xn("page", e, t), t;
}
function vh(e, t) {
  return t = t || {}, be.isOperaMobile && ol(e) ? Xn("screen", e, t) : Xn("client", e, t), t;
}
function Qn(e) {
  return D.number(e.pointerId) ? e.pointerId : e.identifier;
}
function yh(e, t, n) {
  const o = t.length > 1 ? rl(t) : t[0];
  gh(o, e.page), vh(o, e.client), e.timeStamp = n;
}
function wr(e) {
  const t = [];
  return D.array(e) ? (t[0] = e[0], t[1] = e[1]) : e.type === "touchend" ? e.touches.length === 1 ? (t[0] = e.touches[0], t[1] = e.changedTouches[0]) : e.touches.length === 0 && (t[0] = e.changedTouches[0], t[1] = e.changedTouches[1]) : (t[0] = e.touches[0], t[1] = e.touches[1]), t;
}
function rl(e) {
  const t = {
    pageX: 0,
    pageY: 0,
    clientX: 0,
    clientY: 0,
    screenX: 0,
    screenY: 0
  };
  for (const n of e)
    for (const o in t)
      t[o] += n[o];
  for (const n in t)
    t[n] /= e.length;
  return t;
}
function bh(e) {
  if (!e.length)
    return null;
  const t = wr(e), n = Math.min(t[0].pageX, t[1].pageX), o = Math.min(t[0].pageY, t[1].pageY), r = Math.max(t[0].pageX, t[1].pageX), a = Math.max(t[0].pageY, t[1].pageY);
  return {
    x: n,
    y: o,
    left: n,
    top: o,
    right: r,
    bottom: a,
    width: r - n,
    height: a - o
  };
}
function wh(e, t) {
  const n = t + "X", o = t + "Y", r = wr(e), a = r[0][n] - r[1][n], l = r[0][o] - r[1][o];
  return ho(a, l);
}
function xh(e, t) {
  const n = t + "X", o = t + "Y", r = wr(e), a = r[1][n] - r[0][n], l = r[1][o] - r[0][o];
  return 180 * Math.atan2(l, a) / Math.PI;
}
function Sh(e) {
  return D.string(e.pointerType) ? e.pointerType : D.number(e.pointerType) ? [void 0, void 0, "touch", "pen", "mouse"][e.pointerType] : (
    // if the PointerEvent API isn't available, then the "pointer" must
    // be either a MouseEvent, TouchEvent, or Touch object
    /touch/.test(e.type || "") || e instanceof ve.Touch ? "touch" : "mouse"
  );
}
function al(e) {
  const t = D.func(e.composedPath) ? e.composedPath() : e.path;
  return [ga(t ? t[0] : e.target), ga(e.currentTarget)];
}
function an() {
  return {
    page: {
      x: 0,
      y: 0
    },
    client: {
      x: 0,
      y: 0
    },
    timeStamp: 0
  };
}
function _h(e) {
  var t;
  const n = [], o = {}, r = [], a = {
    add: l,
    remove: s,
    addDelegate: u,
    removeDelegate: i,
    delegateListener: d,
    delegateUseCapture: h,
    delegatedEvents: o,
    documents: r,
    targets: n,
    supportsOptions: !1,
    supportsPassive: !1
  };
  (t = e.document) == null || t.createElement("div").addEventListener("test", null, {
    get capture() {
      return a.supportsOptions = !0;
    },
    get passive() {
      return a.supportsPassive = !0;
    }
  }), e.events = a;
  function l(x, m, f, v) {
    if (!x.addEventListener) return;
    const C = ln(v);
    let A = Nn(n, (O) => O.eventTarget === x);
    A || (A = {
      eventTarget: x,
      events: {}
    }, n.push(A)), A.events[m] || (A.events[m] = []), Nn(A.events[m], (O) => O.func === f && Pn(O.options, C)) || (x.addEventListener(m, f, a.supportsOptions ? C : C.capture), A.events[m].push({
      func: f,
      options: C
    }));
  }
  function s(x, m, f, v) {
    if (!x.addEventListener || !x.removeEventListener) return;
    const C = mo(n, (k) => k.eventTarget === x), A = n[C];
    if (!A || !A.events)
      return;
    if (m === "all") {
      for (m in A.events)
        A.events.hasOwnProperty(m) && s(x, m, "all");
      return;
    }
    let O = !1;
    const I = A.events[m];
    if (I)
      if (f === "all") {
        for (let k = I.length - 1; k >= 0; k--) {
          const g = I[k];
          s(x, m, g.func, g.options);
        }
        return;
      } else {
        const k = ln(v);
        for (let g = 0; g < I.length; g++) {
          const b = I[g];
          if (b.func === f && Pn(b.options, k)) {
            x.removeEventListener(m, f, a.supportsOptions ? k : k.capture), I.splice(g, 1), I.length === 0 && (delete A.events[m], O = !0);
            break;
          }
        }
      }
    O && !Object.keys(A.events).length && n.splice(C, 1);
  }
  function u(x, m, f, v, C) {
    const A = ln(C);
    if (!o[f]) {
      o[f] = [];
      for (const k of r)
        l(k, f, d), l(k, f, h, !0);
    }
    const O = o[f];
    let I = Nn(O, (k) => k.selector === x && k.context === m);
    I || (I = {
      selector: x,
      context: m,
      listeners: []
    }, O.push(I)), I.listeners.push({
      func: v,
      options: A
    });
  }
  function i(x, m, f, v, C) {
    const A = ln(C), O = o[f];
    let I = !1, k;
    if (O)
      for (k = O.length - 1; k >= 0; k--) {
        const g = O[k];
        if (g.selector === x && g.context === m) {
          const {
            listeners: b
          } = g;
          for (let w = b.length - 1; w >= 0; w--) {
            const T = b[w];
            if (T.func === v && Pn(T.options, A)) {
              b.splice(w, 1), b.length || (O.splice(k, 1), s(m, f, d), s(m, f, h, !0)), I = !0;
              break;
            }
          }
          if (I)
            break;
        }
      }
  }
  function d(x, m) {
    const f = ln(m), v = new Eh(x), C = o[x.type], [A] = al(x);
    let O = A;
    for (; D.element(O); ) {
      for (let I = 0; I < C.length; I++) {
        const k = C[I], {
          selector: g,
          context: b
        } = k;
        if (Bt(O, g) && Lt(b, A) && Lt(b, O)) {
          const {
            listeners: w
          } = k;
          v.currentTarget = O;
          for (const T of w)
            Pn(T.options, f) && T.func(v);
        }
      }
      O = Et(O);
    }
  }
  function h(x) {
    return d.call(this, x, !0);
  }
  return a;
}
class Eh {
  constructor(t) {
    this.currentTarget = void 0, this.originalEvent = void 0, this.type = void 0, this.originalEvent = t, nl(this, t);
  }
  preventOriginalDefault() {
    this.originalEvent.preventDefault();
  }
  stopPropagation() {
    this.originalEvent.stopPropagation();
  }
  stopImmediatePropagation() {
    this.originalEvent.stopImmediatePropagation();
  }
}
function ln(e) {
  return D.object(e) ? {
    capture: !!e.capture,
    passive: !!e.passive
  } : {
    capture: !!e,
    passive: !1
  };
}
function Pn(e, t) {
  return e === t ? !0 : typeof e == "boolean" ? !!t.capture === e && !t.passive : !!e.capture == !!t.capture && !!e.passive == !!t.passive;
}
var Ch = {
  id: "events",
  install: _h
};
const kh = function(e) {
  return /^(always|never|auto)$/.test(e) ? (this.options.preventDefault = e, this) : D.bool(e) ? (this.options.preventDefault = e ? "always" : "never", this) : this.options.preventDefault;
};
function Ah(e, t, n) {
  const o = e.options.preventDefault;
  if (o !== "never") {
    if (o === "always") {
      n.preventDefault();
      return;
    }
    if (t.events.supportsPassive && /^touch(start|move)$/.test(n.type)) {
      const r = Rt(n.target).document, a = t.getDocOptions(r);
      if (!(a && a.events) || a.events.passive !== !1)
        return;
    }
    /^(mouse|pointer|touch)*(down|start)/i.test(n.type) || D.element(n.target) && Bt(n.target, "input,select,textarea,[contenteditable=true],[contenteditable=true] *") || n.preventDefault();
  }
}
function Ih(e) {
  let {
    interaction: t,
    event: n
  } = e;
  t.interactable && t.interactable.checkAndPreventDefault(n);
}
function Th(e) {
  const {
    Interactable: t
  } = e;
  t.prototype.preventDefault = kh, t.prototype.checkAndPreventDefault = function(n) {
    return Ah(this, e, n);
  }, e.interactions.docEvents.push({
    type: "dragstart",
    listener(n) {
      for (const o of e.interactions.list)
        if (o.element && (o.element === n.target || Lt(o.element, n.target))) {
          o.interactable.checkAndPreventDefault(n);
          return;
        }
    }
  });
}
var Vh = {
  id: "core/interactablePreventDefault",
  install: Th,
  listeners: ["down", "move", "up", "cancel"].reduce((e, t) => (e[`interactions:${t}`] = Ih, e), {})
};
function Sn(e, t) {
  let n = !1;
  return function() {
    return n || (_t.console.warn(t), n = !0), e.apply(this, arguments);
  };
}
function il(e, t) {
  return e.name = t.name, e.axis = t.axis, e.edges = t.edges, e;
}
function ll(e, t, n) {
  return e === "parent" ? Et(n) : e === "self" ? t.getRect(n) : tl(n, e);
}
function _n(e, t, n, o) {
  let r = e;
  return D.string(r) ? r = ll(r, t, n) : D.func(r) && (r = r(...o)), D.element(r) && (r = br(r)), r;
}
function go(e) {
  return e && {
    x: "x" in e ? e.x : e.left,
    y: "y" in e ? e.y : e.top
  };
}
function Mh(e) {
  return e && !("left" in e && "top" in e) && (e = Q({}, e), e.left = e.x || 0, e.top = e.y || 0, e.right = e.right || e.left + e.width, e.bottom = e.bottom || e.top + e.height), e;
}
function ya(e) {
  return e && !("x" in e && "y" in e) && (e = Q({}, e), e.x = e.left || 0, e.y = e.top || 0, e.width = e.width || (e.right || 0) - e.x, e.height = e.height || (e.bottom || 0) - e.y), e;
}
function xr(e, t, n) {
  e.left && (t.left += n.x), e.right && (t.right += n.x), e.top && (t.top += n.y), e.bottom && (t.bottom += n.y), t.width = t.right - t.left, t.height = t.bottom - t.top;
}
function Sr(e, t, n) {
  const o = n && e.options[n], r = o && o.origin || e.options.origin, a = _n(r, e, t, [e && t]);
  return go(a) || {
    x: 0,
    y: 0
  };
}
class sl {
  constructor(t) {
    this.immediatePropagationStopped = !1, this.propagationStopped = !1, this._interaction = t;
  }
  preventDefault() {
  }
  /**
   * Don't call any other listeners (even on the current target)
   */
  stopPropagation() {
    this.propagationStopped = !0;
  }
  /**
   * Don't call listeners on the remaining targets
   */
  stopImmediatePropagation() {
    this.immediatePropagationStopped = this.propagationStopped = !0;
  }
}
Object.defineProperty(sl.prototype, "interaction", {
  get() {
    return this._interaction._proxy;
  },
  set() {
  }
});
const ul = {
  base: {
    preventDefault: "auto",
    deltaSource: "page"
  },
  perAction: {
    enabled: !1,
    origin: {
      x: 0,
      y: 0
    }
  },
  actions: {}
};
class _r extends sl {
  constructor(t, n, o, r, a, l, s) {
    super(t), this.relatedTarget = null, this.screenX = void 0, this.screenY = void 0, this.button = void 0, this.buttons = void 0, this.ctrlKey = void 0, this.shiftKey = void 0, this.altKey = void 0, this.metaKey = void 0, this.page = void 0, this.client = void 0, this.delta = void 0, this.rect = void 0, this.x0 = void 0, this.y0 = void 0, this.t0 = void 0, this.dt = void 0, this.duration = void 0, this.clientX0 = void 0, this.clientY0 = void 0, this.velocity = void 0, this.speed = void 0, this.swipe = void 0, this.axes = void 0, this.preEnd = void 0, a = a || t.element;
    const u = t.interactable, i = (u && u.options || ul).deltaSource, d = Sr(u, a, o), h = r === "start", x = r === "end", m = h ? this : t.prevEvent, f = h ? t.coords.start : x ? {
      page: m.page,
      client: m.client,
      timeStamp: t.coords.cur.timeStamp
    } : t.coords.cur;
    this.page = Q({}, f.page), this.client = Q({}, f.client), this.rect = Q({}, t.rect), this.timeStamp = f.timeStamp, x || (this.page.x -= d.x, this.page.y -= d.y, this.client.x -= d.x, this.client.y -= d.y), this.ctrlKey = n.ctrlKey, this.altKey = n.altKey, this.shiftKey = n.shiftKey, this.metaKey = n.metaKey, this.button = n.button, this.buttons = n.buttons, this.target = a, this.currentTarget = a, this.preEnd = l, this.type = s || o + (r || ""), this.interactable = u, this.t0 = h ? t.pointers[t.pointers.length - 1].downTime : m.t0, this.x0 = t.coords.start.page.x - d.x, this.y0 = t.coords.start.page.y - d.y, this.clientX0 = t.coords.start.client.x - d.x, this.clientY0 = t.coords.start.client.y - d.y, h || x ? this.delta = {
      x: 0,
      y: 0
    } : this.delta = {
      x: this[i].x - m[i].x,
      y: this[i].y - m[i].y
    }, this.dt = t.coords.delta.timeStamp, this.duration = this.timeStamp - this.t0, this.velocity = Q({}, t.coords.velocity[i]), this.speed = ho(this.velocity.x, this.velocity.y), this.swipe = x || r === "inertiastart" ? this.getSwipe() : null;
  }
  getSwipe() {
    const t = this._interaction;
    if (t.prevEvent.speed < 600 || this.timeStamp - t.prevEvent.timeStamp > 150)
      return null;
    let n = 180 * Math.atan2(t.prevEvent.velocityY, t.prevEvent.velocityX) / Math.PI;
    const o = 22.5;
    n < 0 && (n += 360);
    const r = 135 - o <= n && n < 225 + o, a = 225 - o <= n && n < 315 + o, l = !r && (315 - o <= n || n < 45 + o), s = !a && 45 - o <= n && n < 135 + o;
    return {
      up: a,
      down: s,
      left: r,
      right: l,
      angle: n,
      speed: t.prevEvent.speed,
      velocity: {
        x: t.prevEvent.velocityX,
        y: t.prevEvent.velocityY
      }
    };
  }
  preventDefault() {
  }
  /**
   * Don't call listeners on the remaining targets
   */
  stopImmediatePropagation() {
    this.immediatePropagationStopped = this.propagationStopped = !0;
  }
  /**
   * Don't call any other listeners (even on the current target)
   */
  stopPropagation() {
    this.propagationStopped = !0;
  }
}
Object.defineProperties(_r.prototype, {
  pageX: {
    get() {
      return this.page.x;
    },
    set(e) {
      this.page.x = e;
    }
  },
  pageY: {
    get() {
      return this.page.y;
    },
    set(e) {
      this.page.y = e;
    }
  },
  clientX: {
    get() {
      return this.client.x;
    },
    set(e) {
      this.client.x = e;
    }
  },
  clientY: {
    get() {
      return this.client.y;
    },
    set(e) {
      this.client.y = e;
    }
  },
  dx: {
    get() {
      return this.delta.x;
    },
    set(e) {
      this.delta.x = e;
    }
  },
  dy: {
    get() {
      return this.delta.y;
    },
    set(e) {
      this.delta.y = e;
    }
  },
  velocityX: {
    get() {
      return this.velocity.x;
    },
    set(e) {
      this.velocity.x = e;
    }
  },
  velocityY: {
    get() {
      return this.velocity.y;
    },
    set(e) {
      this.velocity.y = e;
    }
  }
});
class Oh {
  constructor(t, n, o, r, a) {
    this.id = void 0, this.pointer = void 0, this.event = void 0, this.downTime = void 0, this.downTarget = void 0, this.id = t, this.pointer = n, this.event = o, this.downTime = r, this.downTarget = a;
  }
}
let zh = /* @__PURE__ */ function(e) {
  return e.interactable = "", e.element = "", e.prepared = "", e.pointerIsDown = "", e.pointerWasMoved = "", e._proxy = "", e;
}({}), Ph = /* @__PURE__ */ function(e) {
  return e.start = "", e.move = "", e.end = "", e.stop = "", e.interacting = "", e;
}({}), Lh = 0;
class Dh {
  /** @internal */
  get pointerMoveTolerance() {
    return 1;
  }
  constructor(t) {
    this.interactable = null, this.element = null, this.rect = null, this._rects = void 0, this.edges = null, this._scopeFire = void 0, this.prepared = {
      name: null,
      axis: null,
      edges: null
    }, this.pointerType = void 0, this.pointers = [], this.downEvent = null, this.downPointer = {}, this._latestPointer = {
      pointer: null,
      event: null,
      eventTarget: null
    }, this.prevEvent = null, this.pointerIsDown = !1, this.pointerWasMoved = !1, this._interacting = !1, this._ending = !1, this._stopped = !0, this._proxy = void 0, this.simulation = null, this.doMove = Sn(function(a) {
      this.move(a);
    }, "The interaction.doMove() method has been renamed to interaction.move()"), this.coords = {
      // Starting InteractEvent pointer coordinates
      start: an(),
      // Previous native pointer move event coordinates
      prev: an(),
      // current native pointer move event coordinates
      cur: an(),
      // Change in coordinates and time of the pointer
      delta: an(),
      // pointer velocity
      velocity: an()
    }, this._id = Lh++;
    let {
      pointerType: n,
      scopeFire: o
    } = t;
    this._scopeFire = o, this.pointerType = n;
    const r = this;
    this._proxy = {};
    for (const a in zh)
      Object.defineProperty(this._proxy, a, {
        get() {
          return r[a];
        }
      });
    for (const a in Ph)
      Object.defineProperty(this._proxy, a, {
        value: function() {
          return r[a](...arguments);
        }
      });
    this._scopeFire("interactions:new", {
      interaction: this
    });
  }
  pointerDown(t, n, o) {
    const r = this.updatePointer(t, n, o, !0), a = this.pointers[r];
    this._scopeFire("interactions:down", {
      pointer: t,
      event: n,
      eventTarget: o,
      pointerIndex: r,
      pointerInfo: a,
      type: "down",
      interaction: this
    });
  }
  /**
   * ```js
   * interact(target)
   *   .draggable({
   *     // disable the default drag start by down->move
   *     manualStart: true
   *   })
   *   // start dragging after the user holds the pointer down
   *   .on('hold', function (event) {
   *     var interaction = event.interaction
   *
   *     if (!interaction.interacting()) {
   *       interaction.start({ name: 'drag' },
   *                         event.interactable,
   *                         event.currentTarget)
   *     }
   * })
   * ```
   *
   * Start an action with the given Interactable and Element as tartgets. The
   * action must be enabled for the target Interactable and an appropriate
   * number of pointers must be held down - 1 for drag/resize, 2 for gesture.
   *
   * Use it with `interactable.<action>able({ manualStart: false })` to always
   * [start actions manually](https://github.com/taye/interact.js/issues/114)
   *
   * @param action - The action to be performed - drag, resize, etc.
   * @param target - The Interactable to target
   * @param element - The DOM Element to target
   * @returns Whether the interaction was successfully started
   */
  start(t, n, o) {
    return this.interacting() || !this.pointerIsDown || this.pointers.length < (t.name === "gesture" ? 2 : 1) || !n.options[t.name].enabled ? !1 : (il(this.prepared, t), this.interactable = n, this.element = o, this.rect = n.getRect(o), this.edges = this.prepared.edges ? Q({}, this.prepared.edges) : {
      left: !0,
      right: !0,
      top: !0,
      bottom: !0
    }, this._stopped = !1, this._interacting = this._doPhase({
      interaction: this,
      event: this.downEvent,
      phase: "start"
    }) && !this._stopped, this._interacting);
  }
  pointerMove(t, n, o) {
    !this.simulation && !(this.modification && this.modification.endResult) && this.updatePointer(t, n, o, !1);
    const r = this.coords.cur.page.x === this.coords.prev.page.x && this.coords.cur.page.y === this.coords.prev.page.y && this.coords.cur.client.x === this.coords.prev.client.x && this.coords.cur.client.y === this.coords.prev.client.y;
    let a, l;
    this.pointerIsDown && !this.pointerWasMoved && (a = this.coords.cur.client.x - this.coords.start.client.x, l = this.coords.cur.client.y - this.coords.start.client.y, this.pointerWasMoved = ho(a, l) > this.pointerMoveTolerance);
    const s = this.getPointerIndex(t), u = {
      pointer: t,
      pointerIndex: s,
      pointerInfo: this.pointers[s],
      event: n,
      type: "move",
      eventTarget: o,
      dx: a,
      dy: l,
      duplicate: r,
      interaction: this
    };
    r || mh(this.coords.velocity, this.coords.delta), this._scopeFire("interactions:move", u), !r && !this.simulation && (this.interacting() && (u.type = null, this.move(u)), this.pointerWasMoved && ko(this.coords.prev, this.coords.cur));
  }
  /**
   * ```js
   * interact(target)
   *   .draggable(true)
   *   .on('dragmove', function (event) {
   *     if (someCondition) {
   *       // change the snap settings
   *       event.interactable.draggable({ snap: { targets: [] }})
   *       // fire another move event with re-calculated snap
   *       event.interaction.move()
   *     }
   *   })
   * ```
   *
   * Force a move of the current action at the same coordinates. Useful if
   * snap/restrict has been changed and you want a movement with the new
   * settings.
   */
  move(t) {
    (!t || !t.event) && hh(this.coords.delta), t = Q({
      pointer: this._latestPointer.pointer,
      event: this._latestPointer.event,
      eventTarget: this._latestPointer.eventTarget,
      interaction: this
    }, t || {}), t.phase = "move", this._doPhase(t);
  }
  /**
   * @internal
   * End interact move events and stop auto-scroll unless simulation is running
   */
  pointerUp(t, n, o, r) {
    let a = this.getPointerIndex(t);
    a === -1 && (a = this.updatePointer(t, n, o, !1));
    const l = /cancel$/i.test(n.type) ? "cancel" : "up";
    this._scopeFire(`interactions:${l}`, {
      pointer: t,
      pointerIndex: a,
      pointerInfo: this.pointers[a],
      event: n,
      eventTarget: o,
      type: l,
      curEventTarget: r,
      interaction: this
    }), this.simulation || this.end(n), this.removePointer(t, n);
  }
  /** @internal */
  documentBlur(t) {
    this.end(t), this._scopeFire("interactions:blur", {
      event: t,
      type: "blur",
      interaction: this
    });
  }
  /**
   * ```js
   * interact(target)
   *   .draggable(true)
   *   .on('move', function (event) {
   *     if (event.pageX > 1000) {
   *       // end the current action
   *       event.interaction.end()
   *       // stop all further listeners from being called
   *       event.stopImmediatePropagation()
   *     }
   *   })
   * ```
   */
  end(t) {
    this._ending = !0, t = t || this._latestPointer.event;
    let n;
    this.interacting() && (n = this._doPhase({
      event: t,
      interaction: this,
      phase: "end"
    })), this._ending = !1, n === !0 && this.stop();
  }
  currentAction() {
    return this._interacting ? this.prepared.name : null;
  }
  interacting() {
    return this._interacting;
  }
  stop() {
    this._scopeFire("interactions:stop", {
      interaction: this
    }), this.interactable = this.element = null, this._interacting = !1, this._stopped = !0, this.prepared.name = this.prevEvent = null;
  }
  /** @internal */
  getPointerIndex(t) {
    const n = Qn(t);
    return this.pointerType === "mouse" || this.pointerType === "pen" ? this.pointers.length - 1 : mo(this.pointers, (o) => o.id === n);
  }
  /** @internal */
  getPointerInfo(t) {
    return this.pointers[this.getPointerIndex(t)];
  }
  /** @internal */
  updatePointer(t, n, o, r) {
    const a = Qn(t);
    let l = this.getPointerIndex(t), s = this.pointers[l];
    return r = r === !1 ? !1 : r || /(down|start)$/i.test(n.type), s ? s.pointer = t : (s = new Oh(a, t, n, null, null), l = this.pointers.length, this.pointers.push(s)), yh(this.coords.cur, this.pointers.map((u) => u.pointer), this._now()), ph(this.coords.delta, this.coords.prev, this.coords.cur), r && (this.pointerIsDown = !0, s.downTime = this.coords.cur.timeStamp, s.downTarget = o, nl(this.downPointer, t), this.interacting() || (ko(this.coords.start, this.coords.cur), ko(this.coords.prev, this.coords.cur), this.downEvent = n, this.pointerWasMoved = !1)), this._updateLatestPointer(t, n, o), this._scopeFire("interactions:update-pointer", {
      pointer: t,
      event: n,
      eventTarget: o,
      down: r,
      pointerInfo: s,
      pointerIndex: l,
      interaction: this
    }), l;
  }
  /** @internal */
  removePointer(t, n) {
    const o = this.getPointerIndex(t);
    if (o === -1) return;
    const r = this.pointers[o];
    this._scopeFire("interactions:remove-pointer", {
      pointer: t,
      event: n,
      eventTarget: null,
      pointerIndex: o,
      pointerInfo: r,
      interaction: this
    }), this.pointers.splice(o, 1), this.pointerIsDown = !1;
  }
  /** @internal */
  _updateLatestPointer(t, n, o) {
    this._latestPointer.pointer = t, this._latestPointer.event = n, this._latestPointer.eventTarget = o;
  }
  destroy() {
    this._latestPointer.pointer = null, this._latestPointer.event = null, this._latestPointer.eventTarget = null;
  }
  /** @internal */
  _createPreparedEvent(t, n, o, r) {
    return new _r(this, t, this.prepared.name, n, this.element, o, r);
  }
  /** @internal */
  _fireEvent(t) {
    var n;
    (n = this.interactable) == null || n.fire(t), (!this.prevEvent || t.timeStamp >= this.prevEvent.timeStamp) && (this.prevEvent = t);
  }
  /** @internal */
  _doPhase(t) {
    const {
      event: n,
      phase: o,
      preEnd: r,
      type: a
    } = t, {
      rect: l
    } = this;
    if (l && o === "move" && (xr(this.edges, l, this.coords.delta[this.interactable.options.deltaSource]), l.width = l.right - l.left, l.height = l.bottom - l.top), this._scopeFire(`interactions:before-action-${o}`, t) === !1)
      return !1;
    const s = t.iEvent = this._createPreparedEvent(n, o, r, a);
    return this._scopeFire(`interactions:action-${o}`, t), o === "start" && (this.prevEvent = s), this._fireEvent(s), this._scopeFire(`interactions:after-action-${o}`, t), !0;
  }
  /** @internal */
  _now() {
    return Date.now();
  }
}
const $o = {
  methodOrder: ["simulationResume", "mouseOrPen", "hasPointer", "idle"],
  search(e) {
    for (const t of $o.methodOrder) {
      const n = $o[t](e);
      if (n)
        return n;
    }
    return null;
  },
  // try to resume simulation with a new pointer
  simulationResume(e) {
    let {
      pointerType: t,
      eventType: n,
      eventTarget: o,
      scope: r
    } = e;
    if (!/down|start/i.test(n))
      return null;
    for (const a of r.interactions.list) {
      let l = o;
      if (a.simulation && a.simulation.allowResume && a.pointerType === t)
        for (; l; ) {
          if (l === a.element)
            return a;
          l = Et(l);
        }
    }
    return null;
  },
  // if it's a mouse or pen interaction
  mouseOrPen(e) {
    let {
      pointerId: t,
      pointerType: n,
      eventType: o,
      scope: r
    } = e;
    if (n !== "mouse" && n !== "pen")
      return null;
    let a;
    for (const l of r.interactions.list)
      if (l.pointerType === n) {
        if (l.simulation && !ba(l, t))
          continue;
        if (l.interacting())
          return l;
        a || (a = l);
      }
    if (a)
      return a;
    for (const l of r.interactions.list)
      if (l.pointerType === n && !(/down/i.test(o) && l.simulation))
        return l;
    return null;
  },
  // get interaction that has this pointer
  hasPointer(e) {
    let {
      pointerId: t,
      scope: n
    } = e;
    for (const o of n.interactions.list)
      if (ba(o, t))
        return o;
    return null;
  },
  // get first idle interaction with a matching pointerType
  idle(e) {
    let {
      pointerType: t,
      scope: n
    } = e;
    for (const o of n.interactions.list) {
      if (o.pointers.length === 1) {
        const r = o.interactable;
        if (r && !(r.options.gesture && r.options.gesture.enabled))
          continue;
      } else if (o.pointers.length >= 2)
        continue;
      if (!o.interacting() && t === o.pointerType)
        return o;
    }
    return null;
  }
};
function ba(e, t) {
  return e.pointers.some((n) => {
    let {
      id: o
    } = n;
    return o === t;
  });
}
const cl = ["pointerDown", "pointerMove", "pointerUp", "updatePointer", "removePointer", "windowBlur"];
function Rh(e) {
  const t = {};
  for (const a of cl)
    t[a] = dl(a, e);
  const n = be.pEventTypes;
  let o;
  ve.PointerEvent ? o = [{
    type: n.down,
    listener: r
  }, {
    type: n.down,
    listener: t.pointerDown
  }, {
    type: n.move,
    listener: t.pointerMove
  }, {
    type: n.up,
    listener: t.pointerUp
  }, {
    type: n.cancel,
    listener: t.pointerUp
  }] : o = [{
    type: "mousedown",
    listener: t.pointerDown
  }, {
    type: "mousemove",
    listener: t.pointerMove
  }, {
    type: "mouseup",
    listener: t.pointerUp
  }, {
    type: "touchstart",
    listener: r
  }, {
    type: "touchstart",
    listener: t.pointerDown
  }, {
    type: "touchmove",
    listener: t.pointerMove
  }, {
    type: "touchend",
    listener: t.pointerUp
  }, {
    type: "touchcancel",
    listener: t.pointerUp
  }], o.push({
    type: "blur",
    listener(a) {
      for (const l of e.interactions.list)
        l.documentBlur(a);
    }
  }), e.prevTouchTime = 0, e.Interaction = class extends Dh {
    get pointerMoveTolerance() {
      return e.interactions.pointerMoveTolerance;
    }
    set pointerMoveTolerance(a) {
      e.interactions.pointerMoveTolerance = a;
    }
    _now() {
      return e.now();
    }
  }, e.interactions = {
    // all active and idle interactions
    list: [],
    new(a) {
      a.scopeFire = (s, u) => e.fire(s, u);
      const l = new e.Interaction(a);
      return e.interactions.list.push(l), l;
    },
    listeners: t,
    docEvents: o,
    pointerMoveTolerance: 1
  };
  function r() {
    for (const a of e.interactions.list)
      if (!(!a.pointerIsDown || a.pointerType !== "touch" || a._interacting))
        for (const l of a.pointers)
          e.documents.some((s) => {
            let {
              doc: u
            } = s;
            return Lt(u, l.downTarget);
          }) || a.removePointer(l.pointer, l.event);
  }
  e.usePlugin(Vh);
}
function dl(e, t) {
  return function(n) {
    const o = t.interactions.list, r = Sh(n), [a, l] = al(n), s = [];
    if (/^touch/.test(n.type)) {
      t.prevTouchTime = t.now();
      for (const u of n.changedTouches) {
        const i = u, d = Qn(i), h = {
          pointer: i,
          pointerId: d,
          pointerType: r,
          eventType: n.type,
          eventTarget: a,
          curEventTarget: l,
          scope: t
        }, x = wa(h);
        s.push([h.pointer, h.eventTarget, h.curEventTarget, x]);
      }
    } else {
      let u = !1;
      if (!be.supportsPointerEvent && /mouse/.test(n.type)) {
        for (let i = 0; i < o.length && !u; i++)
          u = o[i].pointerType !== "mouse" && o[i].pointerIsDown;
        u = u || t.now() - t.prevTouchTime < 500 || // on iOS and Firefox Mobile, MouseEvent.timeStamp is zero if simulated
        n.timeStamp === 0;
      }
      if (!u) {
        const i = {
          pointer: n,
          pointerId: Qn(n),
          pointerType: r,
          eventType: n.type,
          curEventTarget: l,
          eventTarget: a,
          scope: t
        }, d = wa(i);
        s.push([i.pointer, i.eventTarget, i.curEventTarget, d]);
      }
    }
    for (const [u, i, d, h] of s)
      h[e](u, n, i, d);
  };
}
function wa(e) {
  const {
    pointerType: t,
    scope: n
  } = e, o = {
    interaction: $o.search(e),
    searchDetails: e
  };
  return n.fire("interactions:find", o), o.interaction || n.interactions.new({
    pointerType: t
  });
}
function Ao(e, t) {
  let {
    doc: n,
    scope: o,
    options: r
  } = e;
  const {
    interactions: {
      docEvents: a
    },
    events: l
  } = o, s = l[t];
  o.browser.isIOS && !r.events && (r.events = {
    passive: !1
  });
  for (const i in l.delegatedEvents)
    s(n, i, l.delegateListener), s(n, i, l.delegateUseCapture, !0);
  const u = r && r.events;
  for (const {
    type: i,
    listener: d
  } of a)
    s(n, i, d, u);
}
const Bh = {
  id: "core/interactions",
  install: Rh,
  listeners: {
    "scope:add-document": (e) => Ao(e, "add"),
    "scope:remove-document": (e) => Ao(e, "remove"),
    "interactable:unset": (e, t) => {
      let {
        interactable: n
      } = e;
      for (let o = t.interactions.list.length - 1; o >= 0; o--) {
        const r = t.interactions.list[o];
        r.interactable === n && (r.stop(), t.fire("interactions:destroy", {
          interaction: r
        }), r.destroy(), t.interactions.list.length > 2 && t.interactions.list.splice(o, 1));
      }
    }
  },
  onDocSignal: Ao,
  doOnInteractions: dl,
  methodNames: cl
};
function En(e, t) {
  if (t.phaselessTypes[e])
    return !0;
  for (const n in t.map)
    if (e.indexOf(n) === 0 && e.substr(n.length) in t.phases)
      return !0;
  return !1;
}
var vt = /* @__PURE__ */ function(e) {
  return e[e.On = 0] = "On", e[e.Off = 1] = "Off", e;
}(vt || {});
class Nh {
  /** @internal */
  get _defaults() {
    return {
      base: {},
      perAction: {},
      actions: {}
    };
  }
  constructor(t, n, o, r) {
    this.target = void 0, this.options = void 0, this._actions = void 0, this.events = new el(), this._context = void 0, this._win = void 0, this._doc = void 0, this._scopeEvents = void 0, this._actions = n.actions, this.target = t, this._context = n.context || o, this._win = Rt(va(t) ? this._context : t), this._doc = this._win.document, this._scopeEvents = r, this.set(n);
  }
  setOnEvents(t, n) {
    return D.func(n.onstart) && this.on(`${t}start`, n.onstart), D.func(n.onmove) && this.on(`${t}move`, n.onmove), D.func(n.onend) && this.on(`${t}end`, n.onend), D.func(n.oninertiastart) && this.on(`${t}inertiastart`, n.oninertiastart), this;
  }
  updatePerActionListeners(t, n, o) {
    var r;
    const a = (r = this._actions.map[t]) == null ? void 0 : r.filterEventType, l = (s) => (a == null || a(s)) && En(s, this._actions);
    (D.array(n) || D.object(n)) && this._onOff(vt.Off, t, n, void 0, l), (D.array(o) || D.object(o)) && this._onOff(vt.On, t, o, void 0, l);
  }
  setPerAction(t, n) {
    const o = this._defaults;
    for (const r in n) {
      const a = r, l = this.options[t], s = n[a];
      a === "listeners" && this.updatePerActionListeners(t, l.listeners, s), D.array(s) ? l[a] = Ji(s) : D.plainObject(s) ? (l[a] = Q(l[a] || {}, Jt(s)), D.object(o.perAction[a]) && "enabled" in o.perAction[a] && (l[a].enabled = s.enabled !== !1)) : D.bool(s) && D.object(o.perAction[a]) ? l[a].enabled = s : l[a] = s;
    }
  }
  /**
   * The default function to get an Interactables bounding rect. Can be
   * overridden using {@link Interactable.rectChecker}.
   *
   * @param {Element} [element] The element to measure.
   * @return {Rect} The object's bounding rectangle.
   */
  getRect(t) {
    return t = t || (D.element(this.target) ? this.target : null), D.string(this.target) && (t = t || this._context.querySelector(this.target)), br(t);
  }
  /**
   * Returns or sets the function used to calculate the interactable's
   * element's rectangle
   *
   * @param {function} [checker] A function which returns this Interactable's
   * bounding rectangle. See {@link Interactable.getRect}
   * @return {function | object} The checker function or this Interactable
   */
  rectChecker(t) {
    return D.func(t) ? (this.getRect = (n) => {
      const o = Q({}, t.apply(this, n));
      return "width" in o || (o.width = o.right - o.left, o.height = o.bottom - o.top), o;
    }, this) : t === null ? (delete this.getRect, this) : this.getRect;
  }
  /** @internal */
  _backCompatOption(t, n) {
    if (va(n) || D.object(n)) {
      this.options[t] = n;
      for (const o in this._actions.map)
        this.options[o][t] = n;
      return this;
    }
    return this.options[t];
  }
  /**
   * Gets or sets the origin of the Interactable's element.  The x and y
   * of the origin will be subtracted from action event coordinates.
   *
   * @param {Element | object | string} [origin] An HTML or SVG Element whose
   * rect will be used, an object eg. { x: 0, y: 0 } or string 'parent', 'self'
   * or any CSS selector
   *
   * @return {object} The current origin or this Interactable
   */
  origin(t) {
    return this._backCompatOption("origin", t);
  }
  /**
   * Returns or sets the mouse coordinate types used to calculate the
   * movement of the pointer.
   *
   * @param {string} [newValue] Use 'client' if you will be scrolling while
   * interacting; Use 'page' if you want autoScroll to work
   * @return {string | object} The current deltaSource or this Interactable
   */
  deltaSource(t) {
    return t === "page" || t === "client" ? (this.options.deltaSource = t, this) : this.options.deltaSource;
  }
  /** @internal */
  getAllElements() {
    const {
      target: t
    } = this;
    return D.string(t) ? Array.from(this._context.querySelectorAll(t)) : D.func(t) && t.getAllElements ? t.getAllElements() : D.element(t) ? [t] : [];
  }
  /**
   * Gets the selector context Node of the Interactable. The default is
   * `window.document`.
   *
   * @return {Node} The context Node of this Interactable
   */
  context() {
    return this._context;
  }
  inContext(t) {
    return this._context === t.ownerDocument || Lt(this._context, t);
  }
  /** @internal */
  testIgnoreAllow(t, n, o) {
    return !this.testIgnore(t.ignoreFrom, n, o) && this.testAllow(t.allowFrom, n, o);
  }
  /** @internal */
  testAllow(t, n, o) {
    return t ? D.element(o) ? D.string(t) ? Fo(o, t, n) : D.element(t) ? Lt(t, o) : !1 : !1 : !0;
  }
  /** @internal */
  testIgnore(t, n, o) {
    return !t || !D.element(o) ? !1 : D.string(t) ? Fo(o, t, n) : D.element(t) ? Lt(t, o) : !1;
  }
  /**
   * Calls listeners for the given InteractEvent type bound globally
   * and directly to this Interactable
   *
   * @param {InteractEvent} iEvent The InteractEvent object to be fired on this
   * Interactable
   * @return {Interactable} this Interactable
   */
  fire(t) {
    return this.events.fire(t), this;
  }
  /** @internal */
  _onOff(t, n, o, r, a) {
    D.object(n) && !D.array(n) && (r = o, o = null);
    const l = Pt(n, o, a);
    for (let s in l) {
      s === "wheel" && (s = be.wheelEvent);
      for (const u of l[s])
        En(s, this._actions) ? this.events[t === vt.On ? "on" : "off"](s, u) : D.string(this.target) ? this._scopeEvents[t === vt.On ? "addDelegate" : "removeDelegate"](this.target, this._context, s, u, r) : this._scopeEvents[t === vt.On ? "add" : "remove"](this.target, s, u, r);
    }
    return this;
  }
  /**
   * Binds a listener for an InteractEvent, pointerEvent or DOM event.
   *
   * @param {string | array | object} types The types of events to listen
   * for
   * @param {function | array | object} [listener] The event listener function(s)
   * @param {object | boolean} [options] options object or useCapture flag for
   * addEventListener
   * @return {Interactable} This Interactable
   */
  on(t, n, o) {
    return this._onOff(vt.On, t, n, o);
  }
  /**
   * Removes an InteractEvent, pointerEvent or DOM event listener.
   *
   * @param {string | array | object} types The types of events that were
   * listened for
   * @param {function | array | object} [listener] The event listener function(s)
   * @param {object | boolean} [options] options object or useCapture flag for
   * removeEventListener
   * @return {Interactable} This Interactable
   */
  off(t, n, o) {
    return this._onOff(vt.Off, t, n, o);
  }
  /**
   * Reset the options of this Interactable
   *
   * @param {object} options The new settings to apply
   * @return {object} This Interactable
   */
  set(t) {
    const n = this._defaults;
    D.object(t) || (t = {}), this.options = Jt(n.base);
    for (const o in this._actions.methodDict) {
      const r = o, a = this._actions.methodDict[r];
      this.options[r] = {}, this.setPerAction(r, Q(Q({}, n.perAction), n.actions[r])), this[a](t[r]);
    }
    for (const o in t) {
      if (o === "getRect") {
        this.rectChecker(t.getRect);
        continue;
      }
      D.func(this[o]) && this[o](t[o]);
    }
    return this;
  }
  /**
   * Remove this interactable from the list of interactables and remove it's
   * action capabilities and event listeners
   */
  unset() {
    if (D.string(this.target))
      for (const t in this._scopeEvents.delegatedEvents) {
        const n = this._scopeEvents.delegatedEvents[t];
        for (let o = n.length - 1; o >= 0; o--) {
          const {
            selector: r,
            context: a,
            listeners: l
          } = n[o];
          r === this.target && a === this._context && n.splice(o, 1);
          for (let s = l.length - 1; s >= 0; s--)
            this._scopeEvents.removeDelegate(this.target, this._context, t, l[s][0], l[s][1]);
        }
      }
    else
      this._scopeEvents.remove(this.target, "all");
  }
}
class Hh {
  constructor(t) {
    this.list = [], this.selectorMap = {}, this.scope = void 0, this.scope = t, t.addListeners({
      "interactable:unset": (n) => {
        let {
          interactable: o
        } = n;
        const {
          target: r
        } = o, a = D.string(r) ? this.selectorMap[r] : r[this.scope.id], l = mo(a, (s) => s === o);
        a.splice(l, 1);
      }
    });
  }
  new(t, n) {
    n = Q(n || {}, {
      actions: this.scope.actions
    });
    const o = new this.scope.Interactable(t, n, this.scope.document, this.scope.events);
    return this.scope.addDocument(o._doc), this.list.push(o), D.string(t) ? (this.selectorMap[t] || (this.selectorMap[t] = []), this.selectorMap[t].push(o)) : (o.target[this.scope.id] || Object.defineProperty(t, this.scope.id, {
      value: [],
      configurable: !0
    }), t[this.scope.id].push(o)), this.scope.fire("interactable:new", {
      target: t,
      options: n,
      interactable: o,
      win: this.scope._win
    }), o;
  }
  getExisting(t, n) {
    const o = n && n.context || this.scope.document, r = D.string(t), a = r ? this.selectorMap[t] : t[this.scope.id];
    if (a)
      return Nn(a, (l) => l._context === o && (r || l.inContext(t)));
  }
  forEachMatch(t, n) {
    for (const o of this.list) {
      let r;
      if ((D.string(o.target) ? (
        // target is a selector and the element matches
        D.element(t) && Bt(t, o.target)
      ) : (
        // target is the element
        t === o.target
      )) && // the element is in context
      o.inContext(t) && (r = n(o)), r !== void 0)
        return r;
    }
  }
}
function Fh(e) {
  const t = (n, o) => {
    let r = e.interactables.getExisting(n, o);
    return r || (r = e.interactables.new(n, o), r.events.global = t.globalEvents), r;
  };
  return t.getPointerAverage = rl, t.getTouchBBox = bh, t.getTouchDistance = wh, t.getTouchAngle = xh, t.getElementRect = br, t.getElementClientRect = yr, t.matchesSelector = Bt, t.closest = tl, t.globalEvents = {}, t.version = "1.10.27", t.scope = e, t.use = function(n, o) {
    return this.scope.usePlugin(n, o), this;
  }, t.isSet = function(n, o) {
    return !!this.scope.interactables.get(n, o && o.context);
  }, t.on = Sn(function(n, o, r) {
    if (D.string(n) && n.search(" ") !== -1 && (n = n.trim().split(/ +/)), D.array(n)) {
      for (const a of n)
        this.on(a, o, r);
      return this;
    }
    if (D.object(n)) {
      for (const a in n)
        this.on(a, n[a], o);
      return this;
    }
    return En(n, this.scope.actions) ? this.globalEvents[n] ? this.globalEvents[n].push(o) : this.globalEvents[n] = [o] : this.scope.events.add(this.scope.document, n, o, {
      options: r
    }), this;
  }, "The interact.on() method is being deprecated"), t.off = Sn(function(n, o, r) {
    if (D.string(n) && n.search(" ") !== -1 && (n = n.trim().split(/ +/)), D.array(n)) {
      for (const a of n)
        this.off(a, o, r);
      return this;
    }
    if (D.object(n)) {
      for (const a in n)
        this.off(a, n[a], o);
      return this;
    }
    if (En(n, this.scope.actions)) {
      let a;
      n in this.globalEvents && (a = this.globalEvents[n].indexOf(o)) !== -1 && this.globalEvents[n].splice(a, 1);
    } else
      this.scope.events.remove(this.scope.document, n, o, r);
    return this;
  }, "The interact.off() method is being deprecated"), t.debug = function() {
    return this.scope;
  }, t.supportsTouch = function() {
    return be.supportsTouch;
  }, t.supportsPointerEvent = function() {
    return be.supportsPointerEvent;
  }, t.stop = function() {
    for (const n of this.scope.interactions.list)
      n.stop();
    return this;
  }, t.pointerMoveTolerance = function(n) {
    return D.number(n) ? (this.scope.interactions.pointerMoveTolerance = n, this) : this.scope.interactions.pointerMoveTolerance;
  }, t.addDocument = function(n, o) {
    this.scope.addDocument(n, o);
  }, t.removeDocument = function(n) {
    this.scope.removeDocument(n);
  }, t;
}
class $h {
  constructor() {
    this.id = `__interact_scope_${Math.floor(Math.random() * 100)}`, this.isInitialized = !1, this.listenerMaps = [], this.browser = be, this.defaults = Jt(ul), this.Eventable = el, this.actions = {
      map: {},
      phases: {
        start: !0,
        move: !0,
        end: !0
      },
      methodDict: {},
      phaselessTypes: {}
    }, this.interactStatic = Fh(this), this.InteractEvent = _r, this.Interactable = void 0, this.interactables = new Hh(this), this._win = void 0, this.document = void 0, this.window = void 0, this.documents = [], this._plugins = {
      list: [],
      map: {}
    }, this.onWindowUnload = (n) => this.removeDocument(n.target);
    const t = this;
    this.Interactable = class extends Nh {
      get _defaults() {
        return t.defaults;
      }
      set(n) {
        return super.set(n), t.fire("interactable:set", {
          options: n,
          interactable: this
        }), this;
      }
      unset() {
        super.unset();
        const n = t.interactables.list.indexOf(this);
        n < 0 || (t.interactables.list.splice(n, 1), t.fire("interactable:unset", {
          interactable: this
        }));
      }
    };
  }
  addListeners(t, n) {
    this.listenerMaps.push({
      id: n,
      map: t
    });
  }
  fire(t, n) {
    for (const {
      map: {
        [t]: o
      }
    } of this.listenerMaps)
      if (o && o(n, this, t) === !1)
        return !1;
  }
  init(t) {
    return this.isInitialized ? this : Uh(this, t);
  }
  pluginIsInstalled(t) {
    const {
      id: n
    } = t;
    return n ? !!this._plugins.map[n] : this._plugins.list.indexOf(t) !== -1;
  }
  usePlugin(t, n) {
    if (!this.isInitialized)
      return this;
    if (this.pluginIsInstalled(t))
      return this;
    if (t.id && (this._plugins.map[t.id] = t), this._plugins.list.push(t), t.install && t.install(this, n), t.listeners && t.before) {
      let o = 0;
      const r = this.listenerMaps.length, a = t.before.reduce((l, s) => (l[s] = !0, l[xa(s)] = !0, l), {});
      for (; o < r; o++) {
        const l = this.listenerMaps[o].id;
        if (l && (a[l] || a[xa(l)]))
          break;
      }
      this.listenerMaps.splice(o, 0, {
        id: t.id,
        map: t.listeners
      });
    } else t.listeners && this.listenerMaps.push({
      id: t.id,
      map: t.listeners
    });
    return this;
  }
  addDocument(t, n) {
    if (this.getDocIndex(t) !== -1)
      return !1;
    const o = Rt(t);
    n = n ? Q({}, n) : {}, this.documents.push({
      doc: t,
      options: n
    }), this.events.documents.push(t), t !== this.document && this.events.add(o, "unload", this.onWindowUnload), this.fire("scope:add-document", {
      doc: t,
      window: o,
      scope: this,
      options: n
    });
  }
  removeDocument(t) {
    const n = this.getDocIndex(t), o = Rt(t), r = this.documents[n].options;
    this.events.remove(o, "unload", this.onWindowUnload), this.documents.splice(n, 1), this.events.documents.splice(n, 1), this.fire("scope:remove-document", {
      doc: t,
      window: o,
      scope: this,
      options: r
    });
  }
  getDocIndex(t) {
    for (let n = 0; n < this.documents.length; n++)
      if (this.documents[n].doc === t)
        return n;
    return -1;
  }
  getDocOptions(t) {
    const n = this.getDocIndex(t);
    return n === -1 ? null : this.documents[n].options;
  }
  now() {
    return (this.window.Date || Date).now();
  }
}
function Uh(e, t) {
  return e.isInitialized = !0, D.window(t) && Ki(t), ve.init(t), be.init(t), Gt.init(t), e.window = t, e.document = t.document, e.usePlugin(Bh), e.usePlugin(Ch), e;
}
function xa(e) {
  return e && e.replace(/\/.*$/, "");
}
const fl = new $h(), it = fl.interactStatic, jh = typeof globalThis < "u" ? globalThis : window;
fl.init(jh);
function Wh(e) {
  const {
    Interactable: t
    // tslint:disable-line no-shadowed-variable
  } = e;
  t.prototype.getAction = function(n, o, r, a) {
    const l = Gh(this, o, r, a, e);
    return this.options.actionChecker ? this.options.actionChecker(n, o, l, this, a, r) : l;
  }, t.prototype.ignoreFrom = Sn(function(n) {
    return this._backCompatOption("ignoreFrom", n);
  }, "Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue})."), t.prototype.allowFrom = Sn(function(n) {
    return this._backCompatOption("allowFrom", n);
  }, "Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue})."), t.prototype.actionChecker = Yh, t.prototype.styleCursor = qh;
}
function Gh(e, t, n, o, r) {
  const a = e.getRect(o), l = t.buttons || {
    0: 1,
    1: 4,
    3: 8,
    4: 16
  }[t.button], s = {
    action: null,
    interactable: e,
    interaction: n,
    element: o,
    rect: a,
    buttons: l
  };
  return r.fire("auto-start:check", s), s.action;
}
function qh(e) {
  return D.bool(e) ? (this.options.styleCursor = e, this) : e === null ? (delete this.options.styleCursor, this) : this.options.styleCursor;
}
function Yh(e) {
  return D.func(e) ? (this.options.actionChecker = e, this) : e === null ? (delete this.options.actionChecker, this) : this.options.actionChecker;
}
var Zh = {
  id: "auto-start/interactableMethods",
  install: Wh
};
function Kh(e) {
  const {
    interactStatic: t,
    defaults: n
  } = e;
  e.usePlugin(Zh), n.base.actionChecker = null, n.base.styleCursor = !0, Q(n.perAction, {
    manualStart: !1,
    max: 1 / 0,
    maxPerElement: 1,
    allowFrom: null,
    ignoreFrom: null,
    // only allow left button by default
    // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value
    mouseButtons: 1
  }), t.maxInteractions = (o) => gl(o, e), e.autoStart = {
    // Allow this many interactions to happen simultaneously
    maxInteractions: 1 / 0,
    withinInteractionLimit: vo,
    cursorElement: null
  };
}
function Xh(e, t) {
  let {
    interaction: n,
    pointer: o,
    event: r,
    eventTarget: a
  } = e;
  if (n.interacting()) return;
  const l = ml(n, o, r, a, t);
  hl(n, l, t);
}
function Qh(e, t) {
  let {
    interaction: n,
    pointer: o,
    event: r,
    eventTarget: a
  } = e;
  if (n.pointerType !== "mouse" || n.pointerIsDown || n.interacting()) return;
  const l = ml(n, o, r, a, t);
  hl(n, l, t);
}
function Jh(e, t) {
  const {
    interaction: n
  } = e;
  if (!n.pointerIsDown || n.interacting() || !n.pointerWasMoved || !n.prepared.name)
    return;
  t.fire("autoStart:before-start", e);
  const {
    interactable: o
  } = n, r = n.prepared.name;
  r && o && (o.options[r].manualStart || !vo(o, n.element, n.prepared, t) ? n.stop() : (n.start(n.prepared, o, n.element), vl(n, t)));
}
function eg(e, t) {
  let {
    interaction: n
  } = e;
  const {
    interactable: o
  } = n;
  o && o.options.styleCursor && Uo(n.element, "", t);
}
function pl(e, t, n, o, r) {
  return t.testIgnoreAllow(t.options[e.name], n, o) && t.options[e.name].enabled && vo(t, n, e, r) ? e : null;
}
function tg(e, t, n, o, r, a, l) {
  for (let s = 0, u = o.length; s < u; s++) {
    const i = o[s], d = r[s], h = i.getAction(t, n, e, d);
    if (!h)
      continue;
    const x = pl(h, i, d, a, l);
    if (x)
      return {
        action: x,
        interactable: i,
        element: d
      };
  }
  return {
    action: null,
    interactable: null,
    element: null
  };
}
function ml(e, t, n, o, r) {
  let a = [], l = [], s = o;
  function u(i) {
    a.push(i), l.push(s);
  }
  for (; D.element(s); ) {
    a = [], l = [], r.interactables.forEachMatch(s, u);
    const i = tg(e, t, n, a, l, o, r);
    if (i.action && !i.interactable.options[i.action.name].manualStart)
      return i;
    s = Et(s);
  }
  return {
    action: null,
    interactable: null,
    element: null
  };
}
function hl(e, t, n) {
  let {
    action: o,
    interactable: r,
    element: a
  } = t;
  o = o || {
    name: null
  }, e.interactable = r, e.element = a, il(e.prepared, o), e.rect = r && o.name ? r.getRect(a) : null, vl(e, n), n.fire("autoStart:prepared", {
    interaction: e
  });
}
function vo(e, t, n, o) {
  const r = e.options, a = r[n.name].max, l = r[n.name].maxPerElement, s = o.autoStart.maxInteractions;
  let u = 0, i = 0, d = 0;
  if (!(a && l && s))
    return !1;
  for (const h of o.interactions.list) {
    const x = h.prepared.name;
    if (h.interacting() && (u++, u >= s || h.interactable === e && (i += x === n.name ? 1 : 0, i >= a || h.element === t && (d++, x === n.name && d >= l))))
      return !1;
  }
  return s > 0;
}
function gl(e, t) {
  return D.number(e) ? (t.autoStart.maxInteractions = e, this) : t.autoStart.maxInteractions;
}
function Uo(e, t, n) {
  const {
    cursorElement: o
  } = n.autoStart;
  o && o !== e && (o.style.cursor = ""), e.ownerDocument.documentElement.style.cursor = t, e.style.cursor = t, n.autoStart.cursorElement = t ? e : null;
}
function vl(e, t) {
  const {
    interactable: n,
    element: o,
    prepared: r
  } = e;
  if (!(e.pointerType === "mouse" && n && n.options.styleCursor)) {
    t.autoStart.cursorElement && Uo(t.autoStart.cursorElement, "", t);
    return;
  }
  let a = "";
  if (r.name) {
    const l = n.options[r.name].cursorChecker;
    D.func(l) ? a = l(r, n, o, e._interacting) : a = t.actions.map[r.name].getCursor(r);
  }
  Uo(e.element, a || "", t);
}
const Er = {
  id: "auto-start/base",
  before: ["actions"],
  install: Kh,
  listeners: {
    "interactions:down": Xh,
    "interactions:move": (e, t) => {
      Qh(e, t), Jh(e, t);
    },
    "interactions:stop": eg
  },
  maxInteractions: gl,
  withinInteractionLimit: vo,
  validateAction: pl
};
function ng(e, t) {
  let {
    interaction: n,
    eventTarget: o,
    dx: r,
    dy: a
  } = e;
  if (n.prepared.name !== "drag") return;
  const l = Math.abs(r), s = Math.abs(a), u = n.interactable.options.drag, i = u.startAxis, d = l > s ? "x" : l < s ? "y" : "xy";
  if (n.prepared.axis = u.lockAxis === "start" ? d[0] : u.lockAxis, d !== "xy" && i !== "xy" && i !== d) {
    n.prepared.name = null;
    let h = o;
    const x = function(m) {
      if (m === n.interactable) return;
      const f = n.interactable.options.drag;
      if (!f.manualStart && m.testIgnoreAllow(f, h, o)) {
        const v = m.getAction(n.downPointer, n.downEvent, n, h);
        if (v && v.name === "drag" && og(d, m) && Er.validateAction(v, m, h, o, t))
          return m;
      }
    };
    for (; D.element(h); ) {
      const m = t.interactables.forEachMatch(h, x);
      if (m) {
        n.prepared.name = "drag", n.interactable = m, n.element = h;
        break;
      }
      h = Et(h);
    }
  }
}
function og(e, t) {
  if (!t)
    return !1;
  const n = t.options.drag.startAxis;
  return e === "xy" || n === "xy" || n === e;
}
var rg = {
  id: "auto-start/dragAxis",
  listeners: {
    "autoStart:before-start": ng
  }
};
function ag(e) {
  const {
    defaults: t
  } = e;
  e.usePlugin(Er), t.perAction.hold = 0, t.perAction.delay = 0;
}
function Io(e) {
  const t = e.prepared && e.prepared.name;
  if (!t)
    return null;
  const n = e.interactable.options;
  return n[t].hold || n[t].delay;
}
const ig = {
  id: "auto-start/hold",
  install: ag,
  listeners: {
    "interactions:new": (e) => {
      let {
        interaction: t
      } = e;
      t.autoStartHoldTimer = null;
    },
    "autoStart:prepared": (e) => {
      let {
        interaction: t
      } = e;
      const n = Io(t);
      n > 0 && (t.autoStartHoldTimer = setTimeout(() => {
        t.start(t.prepared, t.interactable, t.element);
      }, n));
    },
    "interactions:move": (e) => {
      let {
        interaction: t,
        duplicate: n
      } = e;
      t.autoStartHoldTimer && t.pointerWasMoved && !n && (clearTimeout(t.autoStartHoldTimer), t.autoStartHoldTimer = null);
    },
    // prevent regular down->move autoStart
    "autoStart:before-start": (e) => {
      let {
        interaction: t
      } = e;
      Io(t) > 0 && (t.prepared.name = null);
    }
  },
  getHoldDuration: Io
};
var lg = {
  id: "auto-start",
  install(e) {
    e.usePlugin(Er), e.usePlugin(ig), e.usePlugin(rg);
  }
};
it.use(lg);
function sg(e) {
  const {
    defaults: t,
    actions: n
  } = e;
  e.autoScroll = ne, ne.now = () => e.now(), n.phaselessTypes.autoscroll = !0, t.perAction.autoScroll = ne.defaults;
}
const ne = {
  defaults: {
    enabled: !1,
    margin: 60,
    // the item that is scrolled (Window or HTMLElement)
    container: null,
    // the scroll speed in pixels per second
    speed: 300
  },
  now: Date.now,
  interaction: null,
  i: 0,
  // the handle returned by window.setInterval
  // Direction each pulse is to scroll in
  x: 0,
  y: 0,
  isScrolling: !1,
  prevTime: 0,
  margin: 0,
  speed: 0,
  start(e) {
    ne.isScrolling = !0, Gt.cancel(ne.i), e.autoScroll = ne, ne.interaction = e, ne.prevTime = ne.now(), ne.i = Gt.request(ne.scroll);
  },
  stop() {
    ne.isScrolling = !1, ne.interaction && (ne.interaction.autoScroll = null), Gt.cancel(ne.i);
  },
  // scroll the window by the values in scroll.x/y
  scroll() {
    const {
      interaction: e
    } = ne, {
      interactable: t,
      element: n
    } = e, o = e.prepared.name, r = t.options[o].autoScroll, a = Sa(r.container, t, n), l = ne.now(), s = (l - ne.prevTime) / 1e3, u = r.speed * s;
    if (u >= 1) {
      const i = {
        x: ne.x * u,
        y: ne.y * u
      };
      if (i.x || i.y) {
        const d = _a(a);
        D.window(a) ? a.scrollBy(i.x, i.y) : a && (a.scrollLeft += i.x, a.scrollTop += i.y);
        const h = _a(a), x = {
          x: h.x - d.x,
          y: h.y - d.y
        };
        (x.x || x.y) && t.fire({
          type: "autoscroll",
          target: n,
          interactable: t,
          delta: x,
          interaction: e,
          container: a
        });
      }
      ne.prevTime = l;
    }
    ne.isScrolling && (Gt.cancel(ne.i), ne.i = Gt.request(ne.scroll));
  },
  check(e, t) {
    var n;
    return (n = e.options[t].autoScroll) == null ? void 0 : n.enabled;
  },
  onInteractionMove(e) {
    let {
      interaction: t,
      pointer: n
    } = e;
    if (!(t.interacting() && ne.check(t.interactable, t.prepared.name)))
      return;
    if (t.simulation) {
      ne.x = ne.y = 0;
      return;
    }
    let o, r, a, l;
    const {
      interactable: s,
      element: u
    } = t, i = t.prepared.name, d = s.options[i].autoScroll, h = Sa(d.container, s, u);
    if (D.window(h))
      l = n.clientX < ne.margin, o = n.clientY < ne.margin, r = n.clientX > h.innerWidth - ne.margin, a = n.clientY > h.innerHeight - ne.margin;
    else {
      const x = yr(h);
      l = n.clientX < x.left + ne.margin, o = n.clientY < x.top + ne.margin, r = n.clientX > x.right - ne.margin, a = n.clientY > x.bottom - ne.margin;
    }
    ne.x = r ? 1 : l ? -1 : 0, ne.y = a ? 1 : o ? -1 : 0, ne.isScrolling || (ne.margin = d.margin, ne.speed = d.speed, ne.start(t));
  }
};
function Sa(e, t, n) {
  return (D.string(e) ? ll(e, t, n) : e) || Rt(n);
}
function _a(e) {
  return D.window(e) && (e = window.document.body), {
    x: e.scrollLeft,
    y: e.scrollTop
  };
}
const ug = {
  id: "auto-scroll",
  install: sg,
  listeners: {
    "interactions:new": (e) => {
      let {
        interaction: t
      } = e;
      t.autoScroll = null;
    },
    "interactions:destroy": (e) => {
      let {
        interaction: t
      } = e;
      t.autoScroll = null, ne.stop(), ne.interaction && (ne.interaction = null);
    },
    "interactions:stop": ne.stop,
    "interactions:action-move": (e) => ne.onInteractionMove(e)
  }
};
it.use(ug);
function cg(e) {
  const {
    actions: t,
    Interactable: n,
    defaults: o
  } = e;
  n.prototype.draggable = Hn.draggable, t.map.drag = Hn, t.methodDict.drag = "draggable", o.actions.drag = Hn.defaults;
}
function To(e) {
  let {
    interaction: t
  } = e;
  if (t.prepared.name !== "drag") return;
  const n = t.prepared.axis;
  n === "x" ? (t.coords.cur.page.y = t.coords.start.page.y, t.coords.cur.client.y = t.coords.start.client.y, t.coords.velocity.client.y = 0, t.coords.velocity.page.y = 0) : n === "y" && (t.coords.cur.page.x = t.coords.start.page.x, t.coords.cur.client.x = t.coords.start.client.x, t.coords.velocity.client.x = 0, t.coords.velocity.page.x = 0);
}
function Ea(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  if (n.prepared.name !== "drag") return;
  const o = n.prepared.axis;
  if (o === "x" || o === "y") {
    const r = o === "x" ? "y" : "x";
    t.page[r] = n.coords.start.page[r], t.client[r] = n.coords.start.client[r], t.delta[r] = 0;
  }
}
const dg = function(e) {
  return D.object(e) ? (this.options.drag.enabled = e.enabled !== !1, this.setPerAction("drag", e), this.setOnEvents("drag", e), /^(xy|x|y|start)$/.test(e.lockAxis) && (this.options.drag.lockAxis = e.lockAxis), /^(xy|x|y)$/.test(e.startAxis) && (this.options.drag.startAxis = e.startAxis), this) : D.bool(e) ? (this.options.drag.enabled = e, this) : this.options.drag;
}, Hn = {
  id: "actions/drag",
  install: cg,
  listeners: {
    "interactions:before-action-move": To,
    "interactions:action-resume": To,
    // dragmove
    "interactions:action-move": Ea,
    "auto-start:check": (e) => {
      const {
        interaction: t,
        interactable: n,
        buttons: o
      } = e, r = n.options.drag;
      if (!(!(r && r.enabled) || // check mouseButton setting if the pointer is down
      t.pointerIsDown && /mouse|pointer/.test(t.pointerType) && !(o & n.options.drag.mouseButtons)))
        return e.action = {
          name: "drag",
          axis: r.lockAxis === "start" ? r.startAxis : r.lockAxis
        }, !1;
    }
  },
  draggable: dg,
  beforeMove: To,
  move: Ea,
  defaults: {
    startAxis: "xy",
    lockAxis: "xy"
  },
  getCursor() {
    return "move";
  },
  filterEventType: (e) => e.search("drag") === 0
};
it.use(Hn);
function fg(e) {
  const {
    actions: t,
    browser: n,
    Interactable: o,
    // tslint:disable-line no-shadowed-variable
    defaults: r
  } = e;
  at.cursors = gg(n), at.defaultMargin = n.supportsTouch || n.supportsPointerEvent ? 20 : 10, o.prototype.resizable = function(a) {
    return mg(this, a, e);
  }, t.map.resize = at, t.methodDict.resize = "resizable", r.actions.resize = at.defaults;
}
function pg(e) {
  const {
    interaction: t,
    interactable: n,
    element: o,
    rect: r,
    buttons: a
  } = e;
  if (!r)
    return;
  const l = Q({}, t.coords.cur.page), s = n.options.resize;
  if (!(!(s && s.enabled) || // check mouseButton setting if the pointer is down
  t.pointerIsDown && /mouse|pointer/.test(t.pointerType) && !(a & s.mouseButtons))) {
    if (D.object(s.edges)) {
      const u = {
        left: !1,
        right: !1,
        top: !1,
        bottom: !1
      };
      for (const i in u)
        u[i] = hg(i, s.edges[i], l, t._latestPointer.eventTarget, o, r, s.margin || at.defaultMargin);
      u.left = u.left && !u.right, u.top = u.top && !u.bottom, (u.left || u.right || u.top || u.bottom) && (e.action = {
        name: "resize",
        edges: u
      });
    } else {
      const u = s.axis !== "y" && l.x > r.right - at.defaultMargin, i = s.axis !== "x" && l.y > r.bottom - at.defaultMargin;
      (u || i) && (e.action = {
        name: "resize",
        axes: (u ? "x" : "") + (i ? "y" : "")
      });
    }
    return e.action ? !1 : void 0;
  }
}
function mg(e, t, n) {
  return D.object(t) ? (e.options.resize.enabled = t.enabled !== !1, e.setPerAction("resize", t), e.setOnEvents("resize", t), D.string(t.axis) && /^x$|^y$|^xy$/.test(t.axis) ? e.options.resize.axis = t.axis : t.axis === null && (e.options.resize.axis = n.defaults.actions.resize.axis), D.bool(t.preserveAspectRatio) ? e.options.resize.preserveAspectRatio = t.preserveAspectRatio : D.bool(t.square) && (e.options.resize.square = t.square), e) : D.bool(t) ? (e.options.resize.enabled = t, e) : e.options.resize;
}
function hg(e, t, n, o, r, a, l) {
  if (!t)
    return !1;
  if (t === !0) {
    const s = D.number(a.width) ? a.width : a.right - a.left, u = D.number(a.height) ? a.height : a.bottom - a.top;
    if (l = Math.min(l, Math.abs((e === "left" || e === "right" ? s : u) / 2)), s < 0 && (e === "left" ? e = "right" : e === "right" && (e = "left")), u < 0 && (e === "top" ? e = "bottom" : e === "bottom" && (e = "top")), e === "left") {
      const i = s >= 0 ? a.left : a.right;
      return n.x < i + l;
    }
    if (e === "top") {
      const i = u >= 0 ? a.top : a.bottom;
      return n.y < i + l;
    }
    if (e === "right")
      return n.x > (s >= 0 ? a.right : a.left) - l;
    if (e === "bottom")
      return n.y > (u >= 0 ? a.bottom : a.top) - l;
  }
  return D.element(o) ? D.element(t) ? (
    // the value is an element to use as a resize handle
    t === o
  ) : (
    // otherwise check if element matches value as selector
    Fo(o, t, r)
  ) : !1;
}
function gg(e) {
  return e.isIe9 ? {
    x: "e-resize",
    y: "s-resize",
    xy: "se-resize",
    top: "n-resize",
    left: "w-resize",
    bottom: "s-resize",
    right: "e-resize",
    topleft: "se-resize",
    bottomright: "se-resize",
    topright: "ne-resize",
    bottomleft: "ne-resize"
  } : {
    x: "ew-resize",
    y: "ns-resize",
    xy: "nwse-resize",
    top: "ns-resize",
    left: "ew-resize",
    bottom: "ns-resize",
    right: "ew-resize",
    topleft: "nwse-resize",
    bottomright: "nwse-resize",
    topright: "nesw-resize",
    bottomleft: "nesw-resize"
  };
}
function vg(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  if (n.prepared.name !== "resize" || !n.prepared.edges)
    return;
  const o = t, r = n.rect;
  n._rects = {
    start: Q({}, r),
    corrected: Q({}, r),
    previous: Q({}, r),
    delta: {
      left: 0,
      right: 0,
      width: 0,
      top: 0,
      bottom: 0,
      height: 0
    }
  }, o.edges = n.prepared.edges, o.rect = n._rects.corrected, o.deltaRect = n._rects.delta;
}
function yg(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  if (n.prepared.name !== "resize" || !n.prepared.edges) return;
  const o = t, r = n.interactable.options.resize.invert, a = r === "reposition" || r === "negate", l = n.rect, {
    start: s,
    corrected: u,
    delta: i,
    previous: d
  } = n._rects;
  if (Q(d, u), a) {
    if (Q(u, l), r === "reposition") {
      if (u.top > u.bottom) {
        const h = u.top;
        u.top = u.bottom, u.bottom = h;
      }
      if (u.left > u.right) {
        const h = u.left;
        u.left = u.right, u.right = h;
      }
    }
  } else
    u.top = Math.min(l.top, s.bottom), u.bottom = Math.max(l.bottom, s.top), u.left = Math.min(l.left, s.right), u.right = Math.max(l.right, s.left);
  u.width = u.right - u.left, u.height = u.bottom - u.top;
  for (const h in u)
    i[h] = u[h] - d[h];
  o.edges = n.prepared.edges, o.rect = u, o.deltaRect = i;
}
function bg(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  if (n.prepared.name !== "resize" || !n.prepared.edges) return;
  const o = t;
  o.edges = n.prepared.edges, o.rect = n._rects.corrected, o.deltaRect = n._rects.delta;
}
function Ca(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  if (n.prepared.name !== "resize" || !n.resizeAxes) return;
  const o = n.interactable.options, r = t;
  o.resize.square ? (n.resizeAxes === "y" ? r.delta.x = r.delta.y : r.delta.y = r.delta.x, r.axes = "xy") : (r.axes = n.resizeAxes, n.resizeAxes === "x" ? r.delta.y = 0 : n.resizeAxes === "y" && (r.delta.x = 0));
}
const at = {
  id: "actions/resize",
  before: ["actions/drag"],
  install: fg,
  listeners: {
    "interactions:new": (e) => {
      let {
        interaction: t
      } = e;
      t.resizeAxes = "xy";
    },
    "interactions:action-start": (e) => {
      vg(e), Ca(e);
    },
    "interactions:action-move": (e) => {
      yg(e), Ca(e);
    },
    "interactions:action-end": bg,
    "auto-start:check": pg
  },
  defaults: {
    square: !1,
    preserveAspectRatio: !1,
    axis: "xy",
    // use default margin
    margin: NaN,
    // object with props left, right, top, bottom which are
    // true/false values to resize when the pointer is over that edge,
    // CSS selectors to match the handles for each direction
    // or the Elements for each handle
    edges: null,
    // a value of 'none' will limit the resize rect to a minimum of 0x0
    // 'negate' will alow the rect to have negative width/height
    // 'reposition' will keep the width/height positive by swapping
    // the top and bottom edges and/or swapping the left and right edges
    invert: "none"
  },
  cursors: null,
  getCursor(e) {
    let {
      edges: t,
      axis: n,
      name: o
    } = e;
    const r = at.cursors;
    let a = null;
    if (n)
      a = r[o + n];
    else if (t) {
      let l = "";
      for (const s of ["top", "bottom", "left", "right"])
        t[s] && (l += s);
      a = r[l];
    }
    return a;
  },
  filterEventType: (e) => e.search("resize") === 0,
  defaultMargin: null
};
it.use(at);
var wg = () => {
}, xg = () => {
}, Sg = (e) => {
  const t = [["x", "y"], ["left", "top"], ["right", "bottom"], ["width", "height"]].filter((o) => {
    let [r, a] = o;
    return r in e || a in e;
  }), n = (o, r) => {
    const {
      range: a,
      limits: l = {
        left: -1 / 0,
        right: 1 / 0,
        top: -1 / 0,
        bottom: 1 / 0
      },
      offset: s = {
        x: 0,
        y: 0
      }
    } = e, u = {
      range: a,
      grid: e,
      x: null,
      y: null
    };
    for (const [i, d] of t) {
      const h = Math.round((o - s.x) / e[i]), x = Math.round((r - s.y) / e[d]);
      u[i] = Math.max(l.left, Math.min(l.right, h * e[i] + s.x)), u[d] = Math.max(l.top, Math.min(l.bottom, x * e[d] + s.y));
    }
    return u;
  };
  return n.grid = e, n.coordFields = t, n;
}, _g = /* @__PURE__ */ Object.freeze({
  __proto__: null,
  edgeTarget: wg,
  elements: xg,
  grid: Sg
});
const Eg = {
  id: "snappers",
  install(e) {
    const {
      interactStatic: t
    } = e;
    t.snappers = Q(t.snappers || {}, _g), t.createSnapGrid = t.snappers.grid;
  }
};
class yl {
  constructor(t) {
    this.states = [], this.startOffset = {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    }, this.startDelta = void 0, this.result = void 0, this.endResult = void 0, this.startEdges = void 0, this.edges = void 0, this.interaction = void 0, this.interaction = t, this.result = Ln(), this.edges = {
      left: !1,
      right: !1,
      top: !1,
      bottom: !1
    };
  }
  start(t, n) {
    let {
      phase: o
    } = t;
    const {
      interaction: r
    } = this, a = Cg(r);
    this.prepareStates(a), this.startEdges = Q({}, r.edges), this.edges = Q({}, this.startEdges), this.startOffset = kg(r.rect, n), this.startDelta = {
      x: 0,
      y: 0
    };
    const l = this.fillArg({
      phase: o,
      pageCoords: n,
      preEnd: !1
    });
    return this.result = Ln(), this.startAll(l), this.result = this.setAll(l);
  }
  fillArg(t) {
    const {
      interaction: n
    } = this;
    return t.interaction = n, t.interactable = n.interactable, t.element = n.element, t.rect || (t.rect = n.rect), t.edges || (t.edges = this.startEdges), t.startOffset = this.startOffset, t;
  }
  startAll(t) {
    for (const n of this.states)
      n.methods.start && (t.state = n, n.methods.start(t));
  }
  setAll(t) {
    const {
      phase: n,
      preEnd: o,
      skipModifiers: r,
      rect: a,
      edges: l
    } = t;
    t.coords = Q({}, t.pageCoords), t.rect = Q({}, a), t.edges = Q({}, l);
    const s = r ? this.states.slice(r) : this.states, u = Ln(t.coords, t.rect);
    for (const x of s) {
      var i;
      const {
        options: m
      } = x, f = Q({}, t.coords);
      let v = null;
      (i = x.methods) != null && i.set && this.shouldDo(m, o, n) && (t.state = x, v = x.methods.set(t), xr(t.edges, t.rect, {
        x: t.coords.x - f.x,
        y: t.coords.y - f.y
      })), u.eventProps.push(v);
    }
    Q(this.edges, t.edges), u.delta.x = t.coords.x - t.pageCoords.x, u.delta.y = t.coords.y - t.pageCoords.y, u.rectDelta.left = t.rect.left - a.left, u.rectDelta.right = t.rect.right - a.right, u.rectDelta.top = t.rect.top - a.top, u.rectDelta.bottom = t.rect.bottom - a.bottom;
    const d = this.result.coords, h = this.result.rect;
    if (d && h) {
      const x = u.rect.left !== h.left || u.rect.right !== h.right || u.rect.top !== h.top || u.rect.bottom !== h.bottom;
      u.changed = x || d.x !== u.coords.x || d.y !== u.coords.y;
    }
    return u;
  }
  applyToInteraction(t) {
    const {
      interaction: n
    } = this, {
      phase: o
    } = t, r = n.coords.cur, a = n.coords.start, {
      result: l,
      startDelta: s
    } = this, u = l.delta;
    o === "start" && Q(this.startDelta, l.delta);
    for (const [h, x] of [[a, s], [r, u]])
      h.page.x += x.x, h.page.y += x.y, h.client.x += x.x, h.client.y += x.y;
    const {
      rectDelta: i
    } = this.result, d = t.rect || n.rect;
    d.left += i.left, d.right += i.right, d.top += i.top, d.bottom += i.bottom, d.width = d.right - d.left, d.height = d.bottom - d.top;
  }
  setAndApply(t) {
    const {
      interaction: n
    } = this, {
      phase: o,
      preEnd: r,
      skipModifiers: a
    } = t, l = this.setAll(this.fillArg({
      preEnd: r,
      phase: o,
      pageCoords: t.modifiedCoords || n.coords.cur.page
    }));
    if (this.result = l, !l.changed && (!a || a < this.states.length) && n.interacting())
      return !1;
    if (t.modifiedCoords) {
      const {
        page: s
      } = n.coords.cur, u = {
        x: t.modifiedCoords.x - s.x,
        y: t.modifiedCoords.y - s.y
      };
      l.coords.x += u.x, l.coords.y += u.y, l.delta.x += u.x, l.delta.y += u.y;
    }
    this.applyToInteraction(t);
  }
  beforeEnd(t) {
    const {
      interaction: n,
      event: o
    } = t, r = this.states;
    if (!r || !r.length)
      return;
    let a = !1;
    for (const l of r) {
      t.state = l;
      const {
        options: s,
        methods: u
      } = l, i = u.beforeEnd && u.beforeEnd(t);
      if (i)
        return this.endResult = i, !1;
      a = a || !a && this.shouldDo(s, !0, t.phase, !0);
    }
    a && n.move({
      event: o,
      preEnd: !0
    });
  }
  stop(t) {
    const {
      interaction: n
    } = t;
    if (!this.states || !this.states.length)
      return;
    const o = Q({
      states: this.states,
      interactable: n.interactable,
      element: n.element,
      rect: null
    }, t);
    this.fillArg(o);
    for (const r of this.states)
      o.state = r, r.methods.stop && r.methods.stop(o);
    this.states = null, this.endResult = null;
  }
  prepareStates(t) {
    this.states = [];
    for (let n = 0; n < t.length; n++) {
      const {
        options: o,
        methods: r,
        name: a
      } = t[n];
      this.states.push({
        options: o,
        methods: r,
        index: n,
        name: a
      });
    }
    return this.states;
  }
  restoreInteractionCoords(t) {
    let {
      interaction: {
        coords: n,
        rect: o,
        modification: r
      }
    } = t;
    if (!r.result) return;
    const {
      startDelta: a
    } = r, {
      delta: l,
      rectDelta: s
    } = r.result, u = [[n.start, a], [n.cur, l]];
    for (const [i, d] of u)
      i.page.x -= d.x, i.page.y -= d.y, i.client.x -= d.x, i.client.y -= d.y;
    o.left -= s.left, o.right -= s.right, o.top -= s.top, o.bottom -= s.bottom;
  }
  shouldDo(t, n, o, r) {
    return (
      // ignore disabled modifiers
      !(!t || t.enabled === !1 || // check if we require endOnly option to fire move before end
      r && !t.endOnly || // don't apply endOnly modifiers when not ending
      t.endOnly && !n || // check if modifier should run be applied on start
      o === "start" && !t.setStart)
    );
  }
  copyFrom(t) {
    this.startOffset = t.startOffset, this.startDelta = t.startDelta, this.startEdges = t.startEdges, this.edges = t.edges, this.states = t.states.map((n) => Jt(n)), this.result = Ln(Q({}, t.result.coords), Q({}, t.result.rect));
  }
  destroy() {
    for (const t in this)
      this[t] = null;
  }
}
function Ln(e, t) {
  return {
    rect: t,
    coords: e,
    delta: {
      x: 0,
      y: 0
    },
    rectDelta: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    eventProps: [],
    changed: !0
  };
}
function Cg(e) {
  const t = e.interactable.options[e.prepared.name], n = t.modifiers;
  return n && n.length ? n : ["snap", "snapSize", "snapEdges", "restrict", "restrictEdges", "restrictSize"].map((o) => {
    const r = t[o];
    return r && r.enabled && {
      options: r,
      methods: r._methods
    };
  }).filter((o) => !!o);
}
function kg(e, t) {
  return e ? {
    left: t.x - e.left,
    top: t.y - e.top,
    right: e.right - t.x,
    bottom: e.bottom - t.y
  } : {
    left: 0,
    top: 0,
    right: 0,
    bottom: 0
  };
}
function Ct(e, t) {
  const {
    defaults: n
  } = e, o = {
    start: e.start,
    set: e.set,
    beforeEnd: e.beforeEnd,
    stop: e.stop
  }, r = (a) => {
    const l = a || {};
    l.enabled = l.enabled !== !1;
    for (const u in n)
      u in l || (l[u] = n[u]);
    const s = {
      options: l,
      methods: o,
      name: t,
      enable: () => (l.enabled = !0, s),
      disable: () => (l.enabled = !1, s)
    };
    return s;
  };
  return t && typeof t == "string" && (r._defaults = n, r._methods = o), r;
}
function Vo(e) {
  let {
    iEvent: t,
    interaction: n
  } = e;
  const o = n.modification.result;
  o && (t.modifiers = o.eventProps);
}
const Ag = {
  id: "modifiers/base",
  before: ["actions"],
  install: (e) => {
    e.defaults.perAction.modifiers = [];
  },
  listeners: {
    "interactions:new": (e) => {
      let {
        interaction: t
      } = e;
      t.modification = new yl(t);
    },
    "interactions:before-action-start": (e) => {
      const {
        interaction: t
      } = e, n = e.interaction.modification;
      n.start(e, t.coords.start.page), t.edges = n.edges, n.applyToInteraction(e);
    },
    "interactions:before-action-move": (e) => {
      const {
        interaction: t
      } = e, {
        modification: n
      } = t, o = n.setAndApply(e);
      return t.edges = n.edges, o;
    },
    "interactions:before-action-end": (e) => {
      const {
        interaction: t
      } = e, {
        modification: n
      } = t, o = n.beforeEnd(e);
      return t.edges = n.startEdges, o;
    },
    "interactions:action-start": Vo,
    "interactions:action-move": Vo,
    "interactions:action-end": Vo,
    "interactions:after-action-start": (e) => e.interaction.modification.restoreInteractionCoords(e),
    "interactions:after-action-move": (e) => e.interaction.modification.restoreInteractionCoords(e),
    "interactions:stop": (e) => e.interaction.modification.stop(e)
  }
}, Ig = {
  start(e) {
    const {
      state: t,
      rect: n,
      edges: o,
      pageCoords: r
    } = e;
    let {
      ratio: a,
      enabled: l
    } = t.options;
    const {
      equalDelta: s,
      modifiers: u
    } = t.options;
    a === "preserve" && (a = n.width / n.height), t.startCoords = Q({}, r), t.startRect = Q({}, n), t.ratio = a, t.equalDelta = s;
    const i = t.linkedEdges = {
      top: o.top || o.left && !o.bottom,
      left: o.left || o.top && !o.right,
      bottom: o.bottom || o.right && !o.top,
      right: o.right || o.bottom && !o.left
    };
    if (t.xIsPrimaryAxis = !!(o.left || o.right), t.equalDelta) {
      const h = (i.left ? 1 : -1) * (i.top ? 1 : -1);
      t.edgeSign = {
        x: h,
        y: h
      };
    } else
      t.edgeSign = {
        x: i.left ? -1 : 1,
        y: i.top ? -1 : 1
      };
    if (l !== !1 && Q(o, i), !(u != null && u.length)) return;
    const d = new yl(e.interaction);
    d.copyFrom(e.interaction.modification), d.prepareStates(u), t.subModification = d, d.startAll({
      ...e
    });
  },
  set(e) {
    const {
      state: t,
      rect: n,
      coords: o
    } = e, {
      linkedEdges: r
    } = t, a = Q({}, o), l = t.equalDelta ? Tg : Vg;
    if (Q(e.edges, r), l(t, t.xIsPrimaryAxis, o, n), !t.subModification)
      return null;
    const s = Q({}, n);
    xr(r, s, {
      x: o.x - a.x,
      y: o.y - a.y
    });
    const u = t.subModification.setAll({
      ...e,
      rect: s,
      edges: r,
      pageCoords: o,
      prevCoords: o,
      prevRect: s
    }), {
      delta: i
    } = u;
    if (u.changed) {
      const d = Math.abs(i.x) > Math.abs(i.y);
      l(t, d, u.coords, u.rect), Q(o, u.coords);
    }
    return u.eventProps;
  },
  defaults: {
    ratio: "preserve",
    equalDelta: !1,
    modifiers: [],
    enabled: !1
  }
};
function Tg(e, t, n) {
  let {
    startCoords: o,
    edgeSign: r
  } = e;
  t ? n.y = o.y + (n.x - o.x) * r.y : n.x = o.x + (n.y - o.y) * r.x;
}
function Vg(e, t, n, o) {
  let {
    startRect: r,
    startCoords: a,
    ratio: l,
    edgeSign: s
  } = e;
  if (t) {
    const u = o.width / l;
    n.y = a.y + (u - r.height) * s.y;
  } else {
    const u = o.height * l;
    n.x = a.x + (u - r.width) * s.x;
  }
}
var Mg = Ct(Ig, "aspectRatio");
function Og(e) {
  let {
    rect: t,
    startOffset: n,
    state: o,
    interaction: r,
    pageCoords: a
  } = e;
  const {
    options: l
  } = o, {
    elementRect: s
  } = l, u = Q({
    left: 0,
    top: 0,
    right: 0,
    bottom: 0
  }, l.offset || {});
  if (t && s) {
    const i = Nt(l.restriction, r, a);
    if (i) {
      const d = i.right - i.left - t.width, h = i.bottom - i.top - t.height;
      d < 0 && (u.left += d, u.right += d), h < 0 && (u.top += h, u.bottom += h);
    }
    u.left += n.left - t.width * s.left, u.top += n.top - t.height * s.top, u.right += n.right - t.width * (1 - s.right), u.bottom += n.bottom - t.height * (1 - s.bottom);
  }
  o.offset = u;
}
function zg(e) {
  let {
    coords: t,
    interaction: n,
    state: o
  } = e;
  const {
    options: r,
    offset: a
  } = o, l = Nt(r.restriction, n, t);
  if (!l) return;
  const s = Mh(l);
  t.x = Math.max(Math.min(s.right - a.right, t.x), s.left + a.left), t.y = Math.max(Math.min(s.bottom - a.bottom, t.y), s.top + a.top);
}
function Nt(e, t, n) {
  return D.func(e) ? _n(e, t.interactable, t.element, [n.x, n.y, t]) : _n(e, t.interactable, t.element);
}
const Pg = {
  restriction: null,
  elementRect: null,
  offset: null,
  endOnly: !1,
  enabled: !1
}, Jn = {
  start: Og,
  set: zg,
  defaults: Pg
};
var Lg = Ct(Jn, "restrict");
const bl = {
  top: 1 / 0,
  left: 1 / 0,
  bottom: -1 / 0,
  right: -1 / 0
}, wl = {
  top: -1 / 0,
  left: -1 / 0,
  bottom: 1 / 0,
  right: 1 / 0
};
function Dg(e) {
  let {
    interaction: t,
    startOffset: n,
    state: o
  } = e;
  const {
    options: r
  } = o;
  let a;
  if (r) {
    const l = Nt(r.offset, t, t.coords.start.page);
    a = go(l);
  }
  a = a || {
    x: 0,
    y: 0
  }, o.offset = {
    top: a.y + n.top,
    left: a.x + n.left,
    bottom: a.y - n.bottom,
    right: a.x - n.right
  };
}
function Rg(e) {
  let {
    coords: t,
    edges: n,
    interaction: o,
    state: r
  } = e;
  const {
    offset: a,
    options: l
  } = r;
  if (!n)
    return;
  const s = Q({}, t), u = Nt(l.inner, o, s) || {}, i = Nt(l.outer, o, s) || {};
  ka(u, bl), ka(i, wl), n.top ? t.y = Math.min(Math.max(i.top + a.top, s.y), u.top + a.top) : n.bottom && (t.y = Math.max(Math.min(i.bottom + a.bottom, s.y), u.bottom + a.bottom)), n.left ? t.x = Math.min(Math.max(i.left + a.left, s.x), u.left + a.left) : n.right && (t.x = Math.max(Math.min(i.right + a.right, s.x), u.right + a.right));
}
function ka(e, t) {
  for (const n of ["top", "left", "bottom", "right"])
    n in e || (e[n] = t[n]);
  return e;
}
const Bg = {
  inner: null,
  outer: null,
  offset: null,
  endOnly: !1,
  enabled: !1
}, mn = {
  noInner: bl,
  noOuter: wl,
  start: Dg,
  set: Rg,
  defaults: Bg
};
var Ng = Ct(mn, "restrictEdges");
const Hg = Q({
  get elementRect() {
    return {
      top: 0,
      left: 0,
      bottom: 1,
      right: 1
    };
  },
  set elementRect(e) {
  }
}, Jn.defaults), Fg = {
  start: Jn.start,
  set: Jn.set,
  defaults: Hg
};
var $g = Ct(Fg, "restrictRect");
const Ug = {
  width: -1 / 0,
  height: -1 / 0
}, jg = {
  width: 1 / 0,
  height: 1 / 0
};
function Wg(e) {
  return mn.start(e);
}
function Gg(e) {
  const {
    interaction: t,
    state: n,
    rect: o,
    edges: r
  } = e, {
    options: a
  } = n;
  if (!r)
    return;
  const l = ya(Nt(a.min, t, e.coords)) || Ug, s = ya(Nt(a.max, t, e.coords)) || jg;
  n.options = {
    endOnly: a.endOnly,
    inner: Q({}, mn.noInner),
    outer: Q({}, mn.noOuter)
  }, r.top ? (n.options.inner.top = o.bottom - l.height, n.options.outer.top = o.bottom - s.height) : r.bottom && (n.options.inner.bottom = o.top + l.height, n.options.outer.bottom = o.top + s.height), r.left ? (n.options.inner.left = o.right - l.width, n.options.outer.left = o.right - s.width) : r.right && (n.options.inner.right = o.left + l.width, n.options.outer.right = o.left + s.width), mn.set(e), n.options = a;
}
const qg = {
  min: null,
  max: null,
  endOnly: !1,
  enabled: !1
}, Yg = {
  start: Wg,
  set: Gg,
  defaults: qg
};
var Zg = Ct(Yg, "restrictSize");
function Kg(e) {
  const {
    interaction: t,
    interactable: n,
    element: o,
    rect: r,
    state: a,
    startOffset: l
  } = e, {
    options: s
  } = a, u = s.offsetWithOrigin ? Qg(e) : {
    x: 0,
    y: 0
  };
  let i;
  if (s.offset === "startCoords")
    i = {
      x: t.coords.start.page.x,
      y: t.coords.start.page.y
    };
  else {
    const h = _n(s.offset, n, o, [t]);
    i = go(h) || {
      x: 0,
      y: 0
    }, i.x += u.x, i.y += u.y;
  }
  const {
    relativePoints: d
  } = s;
  a.offsets = r && d && d.length ? d.map((h, x) => ({
    index: x,
    relativePoint: h,
    x: l.left - r.width * h.x + i.x,
    y: l.top - r.height * h.y + i.y
  })) : [{
    index: 0,
    relativePoint: null,
    x: i.x,
    y: i.y
  }];
}
function Xg(e) {
  const {
    interaction: t,
    coords: n,
    state: o
  } = e, {
    options: r,
    offsets: a
  } = o, l = Sr(t.interactable, t.element, t.prepared.name), s = Q({}, n), u = [];
  r.offsetWithOrigin || (s.x -= l.x, s.y -= l.y);
  for (const d of a) {
    const h = s.x - d.x, x = s.y - d.y;
    for (let m = 0, f = r.targets.length; m < f; m++) {
      const v = r.targets[m];
      let C;
      D.func(v) ? C = v(h, x, t._proxy, d, m) : C = v, C && u.push({
        x: (D.number(C.x) ? C.x : h) + d.x,
        y: (D.number(C.y) ? C.y : x) + d.y,
        range: D.number(C.range) ? C.range : r.range,
        source: v,
        index: m,
        offset: d
      });
    }
  }
  const i = {
    target: null,
    inRange: !1,
    distance: 0,
    range: 0,
    delta: {
      x: 0,
      y: 0
    }
  };
  for (const d of u) {
    const h = d.range, x = d.x - s.x, m = d.y - s.y, f = ho(x, m);
    let v = f <= h;
    h === 1 / 0 && i.inRange && i.range !== 1 / 0 && (v = !1), (!i.target || (v ? (
      // is the closest target in range?
      i.inRange && h !== 1 / 0 ? (
        // the pointer is relatively deeper in this target
        f / h < i.distance / i.range
      ) : (
        // this target has Infinite range and the closest doesn't
        h === 1 / 0 && i.range !== 1 / 0 || // OR this target is closer that the previous closest
        f < i.distance
      )
    ) : (
      // The other is not in range and the pointer is closer to this target
      !i.inRange && f < i.distance
    ))) && (i.target = d, i.distance = f, i.range = h, i.inRange = v, i.delta.x = x, i.delta.y = m);
  }
  return i.inRange && (n.x = i.target.x, n.y = i.target.y), o.closest = i, i;
}
function Qg(e) {
  const {
    element: t
  } = e.interaction;
  return go(_n(e.state.options.origin, null, null, [t])) || Sr(e.interactable, t, e.interaction.prepared.name);
}
const Jg = {
  range: 1 / 0,
  targets: null,
  offset: null,
  offsetWithOrigin: !0,
  origin: null,
  relativePoints: null,
  endOnly: !1,
  enabled: !1
}, Cr = {
  start: Kg,
  set: Xg,
  defaults: Jg
};
var e0 = Ct(Cr, "snap");
function t0(e) {
  const {
    state: t,
    edges: n
  } = e, {
    options: o
  } = t;
  if (!n)
    return null;
  e.state = {
    options: {
      targets: null,
      relativePoints: [{
        x: n.left ? 0 : 1,
        y: n.top ? 0 : 1
      }],
      offset: o.offset || "self",
      origin: {
        x: 0,
        y: 0
      },
      range: o.range
    }
  }, t.targetFields = t.targetFields || [["width", "height"], ["x", "y"]], Cr.start(e), t.offsets = e.state.offsets, e.state = t;
}
function n0(e) {
  const {
    interaction: t,
    state: n,
    coords: o
  } = e, {
    options: r,
    offsets: a
  } = n, l = {
    x: o.x - a[0].x,
    y: o.y - a[0].y
  };
  n.options = Q({}, r), n.options.targets = [];
  for (const u of r.targets || []) {
    let i;
    if (D.func(u) ? i = u(l.x, l.y, t) : i = u, !!i) {
      for (const [d, h] of n.targetFields)
        if (d in i || h in i) {
          i.x = i[d], i.y = i[h];
          break;
        }
      n.options.targets.push(i);
    }
  }
  const s = Cr.set(e);
  return n.options = r, s;
}
const o0 = {
  range: 1 / 0,
  targets: null,
  offset: null,
  endOnly: !1,
  enabled: !1
}, eo = {
  start: t0,
  set: n0,
  defaults: o0
};
var r0 = Ct(eo, "snapSize");
function a0(e) {
  const {
    edges: t
  } = e;
  return t ? (e.state.targetFields = e.state.targetFields || [[t.left ? "left" : "right", t.top ? "top" : "bottom"]], eo.start(e)) : null;
}
const i0 = {
  start: a0,
  set: eo.set,
  defaults: Q(Jt(eo.defaults), {
    targets: void 0,
    range: void 0,
    offset: {
      x: 0,
      y: 0
    }
  })
};
var l0 = Ct(i0, "snapEdges");
const un = () => {
};
un._defaults = {};
var Mo = {
  aspectRatio: Mg,
  restrictEdges: Ng,
  restrict: Lg,
  restrictRect: $g,
  restrictSize: Zg,
  snapEdges: l0,
  snap: e0,
  snapSize: r0,
  spring: un,
  avoid: un,
  transform: un,
  rubberband: un
};
const s0 = {
  id: "modifiers",
  install(e) {
    const {
      interactStatic: t
    } = e;
    e.usePlugin(Ag), e.usePlugin(Eg), t.modifiers = Mo;
    for (const n in Mo) {
      const {
        _defaults: o,
        _methods: r
      } = Mo[n];
      o._methods = r, e.defaults.perAction[n] = o;
    }
  }
};
it.use(s0);
var hn = /* @__PURE__ */ function(e) {
  return e.touchAction = "touchAction", e.boxSizing = "boxSizing", e.noListeners = "noListeners", e;
}(hn || {});
const jo = "[interact.js] ", Wo = {
  touchAction: "https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action",
  boxSizing: "https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing"
};
function u0(e) {
  let {
    logger: t
  } = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
  const {
    Interactable: n,
    defaults: o
  } = e;
  e.logger = t || console, o.base.devTools = {
    ignore: {}
  }, n.prototype.devTools = function(a) {
    return a ? (Q(this.options.devTools, a), this) : this.options.devTools;
  };
  const {
    _onOff: r
  } = n.prototype;
  n.prototype._onOff = function(a, l, s, u, i) {
    if (D.string(this.target) || this.target.addEventListener)
      return r.call(this, a, l, s, u, i);
    D.object(l) && !D.array(l) && (u = s, s = null);
    const d = Pt(l, s, i);
    for (const h in d)
      En(h, e.actions) || e.logger.warn(jo + `Can't add native "${h}" event listener to target without \`addEventListener(type, listener, options)\` prop.`);
    return r.call(this, a, d, u);
  };
}
const Aa = [{
  name: hn.touchAction,
  perform(e) {
    let {
      element: t
    } = e;
    return !!t && !c0(t, "touchAction", /pan-|pinch|none/);
  },
  getInfo(e) {
    let {
      element: t
    } = e;
    return [t, Wo.touchAction];
  },
  text: `Consider adding CSS "touch-action: none" to this element
`
}, {
  name: hn.boxSizing,
  perform(e) {
    const {
      element: t
    } = e;
    return e.prepared.name === "resize" && t instanceof ve.HTMLElement && !xl(t, "boxSizing", /border-box/);
  },
  text: 'Consider adding CSS "box-sizing: border-box" to this resizable element',
  getInfo(e) {
    let {
      element: t
    } = e;
    return [t, Wo.boxSizing];
  }
}, {
  name: hn.noListeners,
  perform(e) {
    var t;
    const n = e.prepared.name;
    return !(((t = e.interactable) == null ? void 0 : t.events.types[`${n}move`]) || []).length;
  },
  getInfo(e) {
    return [e.prepared.name, e.interactable];
  },
  text: "There are no listeners set for this action"
}];
function xl(e, t, n) {
  const o = e.style[t] || _t.getComputedStyle(e)[t];
  return n.test((o || "").toString());
}
function c0(e, t, n) {
  let o = e;
  for (; D.element(o); ) {
    if (xl(o, t, n))
      return !0;
    o = Et(o);
  }
  return !1;
}
const d0 = "dev-tools", f0 = {
  id: d0,
  install: u0,
  listeners: {
    "interactions:action-start": (e, t) => {
      let {
        interaction: n
      } = e;
      for (const o of Aa) {
        const r = n.interactable && n.interactable.options;
        !(r && r.devTools && r.devTools.ignore[o.name]) && o.perform(n) && t.logger.warn(jo + o.text, ...o.getInfo(n));
      }
    }
  },
  checks: Aa,
  CheckName: hn,
  links: Wo,
  prefix: jo
};
it.use(f0);
function p0(e) {
  let t = 0, n;
  for (let o = 0, r = e.length; o < r; o++)
    n = e[o].y + e[o].h, n > t && (t = n);
  return t;
}
function Tt(e) {
  return (e || []).map((t) => ({ ...t }));
}
function Sl(e, t) {
  return !(e === t || e.x + e.w <= t.x || e.x >= t.x + t.w || e.y + e.h <= t.y || e.y >= t.y + t.h);
}
function cn(e, t, n) {
  const o = El(e), r = Cl(e), a = Array(e.length);
  for (let l = 0, s = r.length; l < s; l++) {
    let u = r[l];
    u.static || (u = m0(o, u, t, n), o.push(u)), a[e.indexOf(u)] = u, delete u.moved;
  }
  return a;
}
function m0(e, t, n, o) {
  if (n)
    for (; t.y > 0 && !gn(e, t); )
      t.y--;
  else if (o) {
    const a = o[t.i].y;
    for (; t.y > a && !gn(e, t); )
      t.y--;
  }
  let r;
  for (; r = gn(e, t); )
    t.y = r.y + r.h;
  return t;
}
function h0(e, t) {
  const n = El(e);
  for (let o = 0, r = e.length; o < r; o++) {
    const a = e[o];
    if (a.x + a.w > t.cols && (a.x = t.cols - a.w), a.x < 0 && (a.x = 0, a.w = t.cols), !a.static) n.push(a);
    else
      for (; gn(n, a); )
        a.y++;
  }
  return e;
}
function Ia(e, t) {
  for (let n = 0, o = e.length; n < o; n++)
    if (e[n].i === t) return e[n];
}
function gn(e, t) {
  for (let n = 0, o = e.length; n < o; n++)
    if (Sl(e[n], t)) return e[n];
}
function _l(e, t) {
  return e.filter((n) => Sl(n, t));
}
function El(e) {
  return e.filter((t) => t.static);
}
function Go(e, t, n, o, r, a) {
  if (t.static) return e;
  const l = t.x, s = t.y, u = o && t.y > o;
  typeof n == "number" && (t.x = n), typeof o == "number" && (t.y = o), t.moved = !0;
  let i = Cl(e);
  u && (i = i.reverse());
  const d = _l(i, t);
  if (a && d.length)
    return t.x = l, t.y = s, t.moved = !1, e;
  for (let h = 0, x = d.length; h < x; h++) {
    const m = d[h];
    m.moved || t.y > m.y && t.y - m.y > m.h / 4 || (m.static ? e = Ta(e, m, t, r) : e = Ta(e, t, m, r));
  }
  return e;
}
function Ta(e, t, n, o) {
  if (o) {
    const r = {
      x: n.x,
      y: n.y,
      w: n.w,
      h: n.h
    };
    if (r.y = Math.max(t.y - n.h, 0), !gn(e, r))
      return Go(e, n, void 0, r.y, !1);
  }
  return Go(e, n, void 0, n.y + 1, !1);
}
function g0(e, t, n, o) {
  const r = `translate3d(${t}px,${e}px, 0)`;
  return {
    transform: r,
    WebkitTransform: r,
    MozTransform: r,
    msTransform: r,
    OTransform: r,
    width: `${n}px`,
    height: `${o}px`,
    position: "absolute"
  };
}
function v0(e, t, n, o) {
  const r = `translate3d(${t * -1}px,${e}px, 0)`;
  return {
    transform: r,
    WebkitTransform: r,
    MozTransform: r,
    msTransform: r,
    OTransform: r,
    width: `${n}px`,
    height: `${o}px`,
    position: "absolute"
  };
}
function y0(e, t, n, o) {
  return {
    top: `${e}px`,
    left: `${t}px`,
    width: `${n}px`,
    height: `${o}px`,
    position: "absolute"
  };
}
function b0(e, t, n, o) {
  return {
    top: `${e}px`,
    right: `${t}px`,
    width: `${n}px`,
    height: `${o}px`,
    position: "absolute"
  };
}
function Cl(e) {
  return [].concat(e).sort((t, n) => t.y === n.y && t.x === n.x ? 0 : t.y > n.y || t.y === n.y && t.x > n.x ? 1 : -1);
}
function w0(e, t) {
  t = t || "Layout";
  const n = ["x", "y", "w", "h"], o = [];
  if (!Array.isArray(e)) throw new Error(`${t} must be an array!`);
  for (let r = 0, a = e.length; r < a; r++) {
    const l = e[r];
    for (let s = 0; s < n.length; s++)
      if (typeof l[n[s]] != "number")
        throw new Error(`VueGridLayout: ${t}[${r}].${n[s]} must be a number!`);
    if (l.i === void 0 || l.i === null)
      throw new Error(`VueGridLayout: ${t}[${r}].i cannot be null!`);
    if (typeof l.i != "number" && typeof l.i != "string")
      throw new Error(`VueGridLayout: ${t}[${r}].i must be a string or number!`);
    if (o.indexOf(l.i) >= 0)
      throw new Error(`VueGridLayout: ${t}[${r}].i must be unique!`);
    if (o.push(l.i), l.static !== void 0 && typeof l.static != "boolean")
      throw new Error(`VueGridLayout: ${t}[${r}].static must be a boolean!`);
  }
}
function Va(e) {
  return x0(e);
}
function x0(e) {
  const t = e.target.offsetParent || document.body, n = e.offsetParent === document.body ? { left: 0, top: 0 } : t.getBoundingClientRect(), o = e.clientX + t.scrollLeft - n.left, r = e.clientY + t.scrollTop - n.top;
  return { x: o, y: r };
}
function Ma(e, t, n, o) {
  return S0(e) ? {
    deltaX: n - e,
    deltaY: o - t,
    lastX: e,
    lastY: t,
    x: n,
    y: o
  } : {
    deltaX: 0,
    deltaY: 0,
    lastX: n,
    lastY: o,
    x: n,
    y: o
  };
}
function S0(e) {
  return typeof e == "number" && !Number.isNaN(e);
}
function _0(e, t) {
  const n = Al(e);
  let o = n[0];
  for (let r = 1, a = n.length; r < a; r++) {
    const l = n[r];
    t > e[l] && (o = l);
  }
  return o;
}
function kl(e, t) {
  if (!t[e])
    throw new Error(`ResponsiveGridLayout: \`cols\` entry for breakpoint ${e} is missing!`);
  return t[e];
}
function E0(e, t, n, o, r, a) {
  if (t[o]) return Tt(t[o]);
  let l = e;
  const s = Al(n), u = s.slice(s.indexOf(o));
  for (let i = 0, d = u.length; i < d; i++) {
    const h = u[i];
    if (t[h]) {
      l = t[h];
      break;
    }
  }
  return l = Tt(l || []), cn(h0(l, { cols: r }), a);
}
function Al(e) {
  return Object.keys(e).sort((t, n) => e[t] - e[n]);
}
const Il = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
}, C0 = ["id"], k0 = {
  __name: "grid-item",
  props: {
    i: {
      required: !0
    },
    x: {
      type: Number,
      required: !0
    },
    y: {
      type: Number,
      required: !0
    },
    w: {
      type: Number,
      required: !0
    },
    h: {
      type: Number,
      required: !0
    },
    minW: {
      type: Number,
      required: !1,
      default: 1
    },
    minH: {
      type: Number,
      required: !1,
      default: 1
    },
    maxW: {
      type: Number,
      required: !1,
      default: 1 / 0
    },
    maxH: {
      type: Number,
      required: !1,
      default: 1 / 0
    },
    // cols: {
    //   type: Number,
    //   required: true
    // },
    // containerWidth: {
    //   type: Number,
    //   required: true
    // },
    // rowHeight: {
    //   type: Number,
    //   required: true
    // },
    // margin: {
    //   type: Array,
    //   required: true
    // },
    // maxRows: {
    //   type: Number,
    //   required: true
    // },
    isDraggable: {
      type: Boolean,
      required: !1,
      default: null
    },
    isResizable: {
      type: Boolean,
      required: !1,
      default: null
    },
    isBounded: {
      type: Boolean,
      required: !1,
      default: null
    },
    // useCssTransforms: {
    //   type: Boolean,
    //   required: true
    // },
    static: {
      type: Boolean,
      required: !1,
      default: !1
    },
    dragIgnoreFrom: {
      type: String,
      required: !1,
      default: "a, button"
    },
    dragAllowFrom: {
      type: String,
      required: !1,
      default: null
    },
    resizeIgnoreFrom: {
      type: String,
      required: !1,
      default: "a, button"
    },
    preserveAspectRatio: {
      type: Boolean,
      required: !1,
      default: !1
    },
    dragOption: {
      type: Object,
      required: !1,
      default: () => ({})
    },
    resizeOption: {
      type: Object,
      required: !1,
      default: () => ({})
    }
  },
  emits: [
    "container-resized",
    "resize",
    "resized",
    "move",
    "moved"
  ],
  setup(e, { expose: t, emit: n }) {
    const o = e, r = n, a = {
      el: void 0,
      calcXY: $
    };
    t(a);
    const l = xe("emitter"), s = xe("gridLayout");
    let u;
    const i = _e({
      cols: 1,
      containerWidth: 100,
      rowHeight: 30,
      margin: [10, 10],
      maxRows: 1 / 0,
      draggable: null,
      resizable: null,
      bounded: null,
      transformScale: 1,
      useCssTransforms: !0,
      useStyleCursor: !0,
      isDragging: !1,
      dragging: null,
      isResizing: !1,
      resizing: null,
      lastX: NaN,
      lastY: NaN,
      lastW: NaN,
      lastH: NaN,
      style: {},
      dragEventSet: !1,
      resizeEventSet: !1,
      previousW: null,
      previousH: null,
      previousX: null,
      previousY: null,
      innerX: o.x,
      innerY: o.y,
      innerW: o.w,
      innerH: o.h
    }), d = P(() => ({
      "vue-resizable": S(h),
      static: o.static,
      resizing: i.isResizing,
      "vue-draggable-dragging": i.isDragging,
      cssTransforms: i.useCssTransforms,
      "render-rtl": S(m),
      "disable-userselect": i.isDragging,
      // 'no-touch': unref(isAndroid) && unref(draggableOrResizableAndNotStatic),
      "no-touch": S(x)
    })), h = P(() => i.resizable && !o.static), x = P(() => (i.draggable || i.resizable) && !o.static), m = P(() => s.props.isMirrored), f = P(() => S(m) ? "vue-resizable-handle vue-rtl-resizable-handle" : "vue-resizable-handle");
    function v() {
      l.on("updateWidth", ie), l.on("setDraggable", C), l.on("setResizable", A), l.on("setBounded", O), l.on("setTransformScale", I), l.on("setRowHeight", k), l.on("setMaxRows", g), l.on("directionchange", b), l.on("setColNum", w);
    }
    v(), Qe(() => {
      l.off("updateWidth", ie), l.off("setDraggable", C), l.off("setResizable", A), l.off("setBounded", O), l.off("setTransformScale", I), l.off("setRowHeight", k), l.off("setMaxRows", g), l.off("directionchange", b), l.off("setColNum", w), u && u.unset();
    }), Te(() => {
      const z = { ...s.props, ...s.state };
      z.responsive && z.lastBreakpoint ? i.cols = kl(z.lastBreakpoint, z.cols) : i.cols = z.colNum, i.rowHeight = z.rowHeight, i.containerWidth = z.width !== null ? z.width : 100, i.margin = z.margin !== void 0 ? z.margin : [10, 10], i.maxRows = z.maxRows, o.isDraggable === null ? i.draggable = z.isDraggable : i.draggable = o.isDraggable, o.isResizable === null ? i.resizable = z.isResizable : i.resizable = o.isResizable, o.isBounded === null ? i.bounded = z.isBounded : i.bounded = o.isBounded, i.transformScale = z.transformScale, i.useCssTransforms = z.useCssTransforms, i.useStyleCursor = z.useStyleCursor, T();
    }), Z(() => o.isDraggable, () => {
      i.draggable = o.isDraggable;
    }), Z(() => o.static, () => {
      oe(), ge();
    }), Z(() => i.draggable, () => {
      oe();
    }), Z(() => o.isResizable, () => {
      i.resizable = o.isResizable;
    }), Z(() => o.isBounded, () => {
      i.bounded = o.isBounded;
    }), Z(() => i.resizable, () => {
      ge();
    }), Z(() => i.rowHeight, () => {
      T(), _();
    }), Z(() => i.cols, () => {
      ge(), T(), _();
    }), Z(() => i.containerWidth, () => {
      ge(), T(), _();
    }), Z(() => o.x, (z) => {
      i.innerX = z, T();
    }), Z(() => o.y, (z) => {
      i.innerY = z, T();
    }), Z(() => o.h, (z) => {
      i.innerH = z, T();
    }), Z(() => o.w, (z) => {
      i.innerW = z, T();
    }), Z(m, () => {
      ge(), T();
    }), Z(() => o.minH, () => {
      ge();
    }), Z(() => o.maxH, () => {
      ge();
    }), Z(() => o.minW, () => {
      ge();
    }), Z(() => o.maxW, () => {
      ge();
    }), Z(() => s.props.margin, (z) => {
      !z || Number(z[0]) === Number(i.margin[0]) && Number(z[1]) === Number(i.margin[1]) || (i.margin = z.map((X) => Number(X) || 0), T(), _());
    });
    function C(z) {
      o.isDraggable === null && (i.draggable = z);
    }
    function A(z) {
      o.isResizable === null && (i.resizable = z);
    }
    function O(z) {
      o.isBounded === null && (i.bounded = z);
    }
    function I(z) {
      i.transformScale = z;
    }
    function k(z) {
      i.rowHeight = z;
    }
    function g(z) {
      i.maxRows = z;
    }
    function b() {
      T();
    }
    function w(z) {
      i.cols = parseInt(z);
    }
    function T() {
      o.x + o.w > i.cols ? (i.innerX = 0, i.innerW = o.w > i.cols ? i.cols : o.w) : (i.innerX = o.x, i.innerW = o.w);
      const z = y(i.innerX, i.innerY, i.innerW, i.innerH);
      i.isDragging && (z.top = i.dragging.top, S(m) ? z.right = i.dragging.left : z.left = i.dragging.left), i.isResizing && (z.width = i.resizing.width, z.height = i.resizing.height);
      let X;
      i.useCssTransforms ? S(m) ? X = v0(z.top, z.right, z.width, z.height) : X = g0(z.top, z.left, z.width, z.height) : S(m) ? X = b0(z.top, z.right, z.width, z.height) : X = y0(z.top, z.left, z.width, z.height), i.style = X;
    }
    function _() {
      const z = {};
      for (const X of ["width", "height"]) {
        const j = i.style[X].match(/^(\d+)px$/);
        if (!j) return;
        z[X] = j[1];
      }
      r("container-resized", o.i, o.h, o.w, z.height, z.width);
    }
    function L(z) {
      if (o.static) return;
      const X = Va(z);
      if (X == null) return;
      const { x: j, y: ce } = X, N = { width: 0, height: 0 };
      let G;
      switch (z.type) {
        case "resizestart": {
          ge(), i.previousW = i.innerW, i.previousH = i.innerH, G = y(i.innerX, i.innerY, i.innerW, i.innerH), N.width = G.width, N.height = G.height, i.resizing = N, i.isResizing = !0;
          break;
        }
        case "resizemove": {
          const le = Ma(i.lastW, i.lastH, j, ce);
          S(m) ? N.width = i.resizing.width - le.deltaX / i.transformScale : N.width = i.resizing.width + le.deltaX / i.transformScale, N.height = i.resizing.height + le.deltaY / i.transformScale, i.resizing = N;
          break;
        }
        case "resizeend": {
          G = y(i.innerX, i.innerY, i.innerW, i.innerH), N.width = G.width, N.height = G.height, i.resizing = null, i.isResizing = !1;
          break;
        }
      }
      T(), G = te(N.height, N.width), G.w < o.minW && (G.w = o.minW), G.w > o.maxW && (G.w = o.maxW), G.h < o.minH && (G.h = o.minH), G.h > o.maxH && (G.h = o.maxH), G.h < 1 && (G.h = 1), G.w < 1 && (G.w = 1), i.lastW = j, i.lastH = ce, (i.innerW !== G.w || i.innerH !== G.h) && r("resize", o.i, G.h, G.w, N.height, N.width), z.type === "resizeend" && (i.previousW !== i.innerW || i.previousH !== i.innerH) && r("resized", o.i, G.h, G.w, N.height, N.width), l.emit("resizeEvent", [
        z.type,
        o.i,
        i.innerX,
        i.innerY,
        G.h,
        G.w
      ]);
    }
    function M(z) {
      if (o.static || i.isResizing) return;
      const X = Va(z);
      if (X === null) return;
      const { x: j, y: ce } = X, N = { top: 0, left: 0 };
      switch (z.type) {
        case "dragstart": {
          i.previousX = i.innerX, i.previousY = i.innerY;
          const le = z.target.offsetParent.getBoundingClientRect(), H = z.target.getBoundingClientRect(), re = H.left / i.transformScale, ue = le.left / i.transformScale, Se = H.right / i.transformScale, Ce = le.right / i.transformScale, ke = H.top / i.transformScale, Be = le.top / i.transformScale;
          S(m) ? N.left = (Se - Ce) * -1 : N.left = re - ue, N.top = ke - Be, i.dragging = N, i.isDragging = !0;
          break;
        }
        case "dragend": {
          if (!i.isDragging) return;
          const le = z.target.offsetParent.getBoundingClientRect(), H = z.target.getBoundingClientRect(), re = H.left / i.transformScale, ue = le.left / i.transformScale, Se = H.right / i.transformScale, Ce = le.right / i.transformScale, ke = H.top / i.transformScale, Be = le.top / i.transformScale;
          S(m) ? N.left = (Se - Ce) * -1 : N.left = re - ue, N.top = ke - Be, i.dragging = null, i.isDragging = !1;
          break;
        }
        case "dragmove": {
          const le = Ma(i.lastX, i.lastY, j, ce);
          if (S(m) ? N.left = i.dragging.left - le.deltaX / i.transformScale : N.left = i.dragging.left + le.deltaX / i.transformScale, N.top = i.dragging.top + le.deltaY / i.transformScale, i.bounded) {
            const H = z.target.offsetParent.clientHeight - se(o.h, i.rowHeight, i.margin[1]);
            N.top = V(N.top, 0, H);
            const re = Y(), ue = i.containerWidth - se(o.w, re, i.margin[0]);
            N.left = V(N.left, 0, ue);
          }
          i.dragging = N;
          break;
        }
      }
      T();
      let G;
      S(m), G = $(N.top, N.left), i.lastX = j, i.lastY = ce, (i.innerX !== G.x || i.innerY !== G.y) && r("move", o.i, G.x, G.y), z.type === "dragend" && (i.previousX !== i.innerX || i.previousY !== i.innerY) && r("moved", o.i, G.x, G.y), l.emit("dragEvent", [
        z.type,
        o.i,
        G.x,
        G.y,
        i.innerH,
        i.innerW
      ]);
    }
    function y(z, X, j, ce) {
      const N = Y();
      let G;
      return S(m) ? G = {
        right: Math.round(N * z + (z + 1) * i.margin[0]),
        top: Math.round(i.rowHeight * X + (X + 1) * i.margin[1]),
        // 0 * Infinity === NaN, which causes problems with resize constriants;
        // Fix this if it occurs.
        // Note we do it here rather than later because Math.round(Infinity) causes deopt
        width: j === 1 / 0 ? j : Math.round(N * j + Math.max(0, j - 1) * i.margin[0]),
        height: ce === 1 / 0 ? ce : Math.round(i.rowHeight * ce + Math.max(0, ce - 1) * i.margin[1])
      } : G = {
        left: Math.round(N * z + (z + 1) * i.margin[0]),
        top: Math.round(i.rowHeight * X + (X + 1) * i.margin[1]),
        // 0 * Infinity === NaN, which causes problems with resize constriants;
        // Fix this if it occurs.
        // Note we do it here rather than later because Math.round(Infinity) causes deopt
        width: j === 1 / 0 ? j : Math.round(N * j + Math.max(0, j - 1) * i.margin[0]),
        height: ce === 1 / 0 ? ce : Math.round(i.rowHeight * ce + Math.max(0, ce - 1) * i.margin[1])
      }, G;
    }
    function $(z, X) {
      const j = Y();
      let ce = Math.round((X - i.margin[0]) / (j + i.margin[0])), N = Math.round((z - i.margin[1]) / (i.rowHeight + i.margin[1]));
      return ce = Math.max(Math.min(ce, i.cols - i.innerW), 0), N = Math.max(Math.min(N, i.maxRows - i.innerH), 0), { x: ce, y: N };
    }
    function Y() {
      return (i.containerWidth - i.margin[0] * (i.cols + 1)) / i.cols;
    }
    function se(z, X, j) {
      return Number.isFinite(z) ? Math.round(
        X * z + Math.max(0, z - 1) * j
      ) : z;
    }
    function V(z, X, j) {
      return Math.max(Math.min(z, j), X);
    }
    function te(z, X, j = !1) {
      const ce = Y();
      let N = Math.round((X + i.margin[0]) / (ce + i.margin[0])), G = 0;
      return j ? G = Math.ceil((z + i.margin[1]) / (i.rowHeight + i.margin[1])) : G = Math.round((z + i.margin[1]) / (i.rowHeight + i.margin[1])), N = Math.max(Math.min(N, i.cols - i.innerX), 0), G = Math.max(Math.min(G, i.maxRows - i.innerY), 0), { w: N, h: G };
    }
    function ie(z, X) {
      i.containerWidth = z, X != null && (i.cols = X);
    }
    function oe() {
      if (u == null && (u = it(a.el), i.useStyleCursor || u.styleCursor(!1)), i.draggable && !o.static) {
        const z = {
          ignoreFrom: o.dragIgnoreFrom,
          allowFrom: o.dragAllowFrom,
          ...o.dragOption
        };
        u.draggable(z), i.dragEventSet || (i.dragEventSet = !0, u.on("dragstart dragmove dragend", (X) => {
          M(X);
        }));
      } else
        u.draggable({
          enabled: !1
        });
    }
    function ge() {
      if (u == null && (u = it(a.el), i.useStyleCursor || u.styleCursor(!1)), i.resizable && !o.static) {
        const z = y(0, 0, o.maxW, o.maxH), X = y(0, 0, o.minW, o.minH), j = {
          // allowFrom: "." + unref(resizableHandleClass).trim().replace(" ", "."),
          edges: {
            left: !1,
            right: `.${S(f).trim().replace(" ", ".")}`,
            bottom: `.${S(f).trim().replace(" ", ".")}`,
            top: !1
          },
          ignoreFrom: o.resizeIgnoreFrom,
          restrictSize: {
            min: {
              height: X.height * i.transformScale,
              width: X.width * i.transformScale
            },
            max: {
              height: z.height * i.transformScale,
              width: z.width * i.transformScale
            }
          },
          ...o.resizeOption
        };
        o.preserveAspectRatio && (j.modifiers = [
          it.modifiers.aspectRatio({
            ratio: "preserve"
          })
        ]), u.resizable(j), i.resizeEventSet || (i.resizeEventSet = !0, u.on("resizestart resizemove resizeend", (ce) => {
          L(ce);
        }));
      } else
        u.resizable({
          enabled: !1
        });
    }
    function $e(z) {
      a.el = z;
    }
    return (z, X) => (E(), F("div", {
      ref: $e,
      class: K(["vue-grid-item", d.value]),
      style: he(i.style),
      id: e.i,
      key: e.i
    }, [
      ye(z.$slots, "default", {
        class: K({ rtl: m.value })
      }, void 0, !0),
      h.value ? (E(), F("span", {
        key: 0,
        class: K(f.value)
      }, null, 2)) : q("", !0)
    ], 14, C0));
  }
}, Tl = /* @__PURE__ */ Il(k0, [["__scopeId", "data-v-99aff433"]]);
function A0(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var Vl = { exports: {} }, I0 = Vl.exports = {};
I0.forEach = function(e, t) {
  for (var n = 0; n < e.length; n++) {
    var o = t(e[n]);
    if (o)
      return o;
  }
};
var Ml = Vl.exports, T0 = function(e) {
  var t = e.stateHandler.getState;
  function n(l) {
    var s = t(l);
    return s && !!s.isDetectable;
  }
  function o(l) {
    t(l).isDetectable = !0;
  }
  function r(l) {
    return !!t(l).busy;
  }
  function a(l, s) {
    t(l).busy = !!s;
  }
  return {
    isDetectable: n,
    markAsDetectable: o,
    isBusy: r,
    markBusy: a
  };
}, V0 = function(e) {
  var t = {};
  function n(l) {
    var s = e.get(l);
    return s === void 0 ? [] : t[s] || [];
  }
  function o(l, s) {
    var u = e.get(l);
    t[u] || (t[u] = []), t[u].push(s);
  }
  function r(l, s) {
    for (var u = n(l), i = 0, d = u.length; i < d; ++i)
      if (u[i] === s) {
        u.splice(i, 1);
        break;
      }
  }
  function a(l) {
    var s = n(l);
    s && (s.length = 0);
  }
  return {
    get: n,
    add: o,
    removeListener: r,
    removeAllListeners: a
  };
}, M0 = function() {
  var e = 1;
  function t() {
    return e++;
  }
  return {
    generate: t
  };
}, O0 = function(e) {
  var t = e.idGenerator, n = e.stateHandler.getState;
  function o(a) {
    var l = n(a);
    return l && l.id !== void 0 ? l.id : null;
  }
  function r(a) {
    var l = n(a);
    if (!l)
      throw new Error("setId required the element to have a resize detection state.");
    var s = t.generate();
    return l.id = s, s;
  }
  return {
    get: o,
    set: r
  };
}, z0 = function(e) {
  function t() {
  }
  var n = {
    log: t,
    warn: t,
    error: t
  };
  if (!e && window.console) {
    var o = function(r, a) {
      r[a] = function() {
        var l = console[a];
        if (l.apply)
          l.apply(console, arguments);
        else
          for (var s = 0; s < arguments.length; s++)
            l(arguments[s]);
      };
    };
    o(n, "log"), o(n, "warn"), o(n, "error");
  }
  return n;
}, Ol = { exports: {} }, zl = Ol.exports = {};
zl.isIE = function(e) {
  function t() {
    var o = navigator.userAgent.toLowerCase();
    return o.indexOf("msie") !== -1 || o.indexOf("trident") !== -1 || o.indexOf(" edge/") !== -1;
  }
  if (!t())
    return !1;
  if (!e)
    return !0;
  var n = function() {
    var o, r = 3, a = document.createElement("div"), l = a.getElementsByTagName("i");
    do
      a.innerHTML = "<!--[if gt IE " + ++r + "]><i></i><![endif]-->";
    while (l[0]);
    return r > 4 ? r : o;
  }();
  return e === n;
};
zl.isLegacyOpera = function() {
  return !!window.opera;
};
var Pl = Ol.exports, Ll = { exports: {} }, P0 = Ll.exports = {};
P0.getOption = L0;
function L0(e, t, n) {
  var o = e[t];
  return o == null && n !== void 0 ? n : o;
}
var D0 = Ll.exports, Oa = D0, R0 = function(e) {
  e = e || {};
  var t = e.reporter, n = Oa.getOption(e, "async", !0), o = Oa.getOption(e, "auto", !0);
  o && !n && (t && t.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."), n = !0);
  var r = za(), a, l = !1;
  function s(m, f) {
    !l && o && n && r.size() === 0 && d(), r.add(m, f);
  }
  function u() {
    for (l = !0; r.size(); ) {
      var m = r;
      r = za(), m.process();
    }
    l = !1;
  }
  function i(m) {
    l || (m === void 0 && (m = n), a && (h(a), a = null), m ? d() : u());
  }
  function d() {
    a = x(u);
  }
  function h(m) {
    var f = clearTimeout;
    return f(m);
  }
  function x(m) {
    var f = function(v) {
      return setTimeout(v, 0);
    };
    return f(m);
  }
  return {
    add: s,
    force: i
  };
};
function za() {
  var e = {}, t = 0, n = 0, o = 0;
  function r(s, u) {
    u || (u = s, s = 0), s > n ? n = s : s < o && (o = s), e[s] || (e[s] = []), e[s].push(u), t++;
  }
  function a() {
    for (var s = o; s <= n; s++)
      for (var u = e[s], i = 0; i < u.length; i++) {
        var d = u[i];
        d();
      }
  }
  function l() {
    return t;
  }
  return {
    add: r,
    process: a,
    size: l
  };
}
var kr = "_erd";
function B0(e) {
  return e[kr] = {}, Dl(e);
}
function Dl(e) {
  return e[kr];
}
function N0(e) {
  delete e[kr];
}
var H0 = {
  initState: B0,
  getState: Dl,
  cleanState: N0
}, sn = Pl, F0 = function(e) {
  e = e || {};
  var t = e.reporter, n = e.batchProcessor, o = e.stateHandler.getState;
  if (!t)
    throw new Error("Missing required dependency: reporter.");
  function r(i, d) {
    function h() {
      d(i);
    }
    if (sn.isIE(8))
      o(i).object = {
        proxy: h
      }, i.attachEvent("onresize", h);
    else {
      var x = s(i);
      if (!x)
        throw new Error("Element is not detectable by this strategy.");
      x.contentDocument.defaultView.addEventListener("resize", h);
    }
  }
  function a(i) {
    var d = e.important ? " !important; " : "; ";
    return (i.join(d) + d).trim();
  }
  function l(i, d, h) {
    h || (h = d, d = i, i = null), i = i || {}, i.debug;
    function x(m, f) {
      var v = a(["display: block", "position: absolute", "top: 0", "left: 0", "width: 100%", "height: 100%", "border: none", "padding: 0", "margin: 0", "opacity: 0", "z-index: -1000", "pointer-events: none"]), C = !1, A = window.getComputedStyle(m), O = m.offsetWidth, I = m.offsetHeight;
      o(m).startSize = {
        width: O,
        height: I
      };
      function k() {
        function g() {
          if (A.position === "static") {
            m.style.setProperty("position", "relative", i.important ? "important" : "");
            var T = function(_, L, M, y) {
              function $(se) {
                return se.replace(/[^-\d\.]/g, "");
              }
              var Y = M[y];
              Y !== "auto" && $(Y) !== "0" && (_.warn("An element that is positioned static has style." + y + "=" + Y + " which is ignored due to the static positioning. The element will need to be positioned relative, so the style." + y + " will be set to 0. Element: ", L), L.style.setProperty(y, "0", i.important ? "important" : ""));
            };
            T(t, m, A, "top"), T(t, m, A, "right"), T(t, m, A, "bottom"), T(t, m, A, "left");
          }
        }
        function b() {
          C || g();
          function T(L, M) {
            if (!L.contentDocument) {
              var y = o(L);
              y.checkForObjectDocumentTimeoutId && window.clearTimeout(y.checkForObjectDocumentTimeoutId), y.checkForObjectDocumentTimeoutId = setTimeout(function() {
                y.checkForObjectDocumentTimeoutId = 0, T(L, M);
              }, 100);
              return;
            }
            M(L.contentDocument);
          }
          var _ = this;
          T(_, function(L) {
            f(m);
          });
        }
        A.position !== "" && (g(), C = !0);
        var w = document.createElement("object");
        w.style.cssText = v, w.tabIndex = -1, w.type = "text/html", w.setAttribute("aria-hidden", "true"), w.onload = b, sn.isIE() || (w.data = "about:blank"), o(m) && (m.appendChild(w), o(m).object = w, sn.isIE() && (w.data = "about:blank"));
      }
      n ? n.add(k) : k();
    }
    sn.isIE(8) ? h(d) : x(d, h);
  }
  function s(i) {
    return o(i).object;
  }
  function u(i) {
    if (o(i)) {
      var d = s(i);
      d && (sn.isIE(8) ? i.detachEvent("onresize", d.proxy) : i.removeChild(d), o(i).checkForObjectDocumentTimeoutId && window.clearTimeout(o(i).checkForObjectDocumentTimeoutId), delete o(i).object);
    }
  }
  return {
    makeDetectable: l,
    addListener: r,
    uninstall: u
  };
}, $0 = Ml.forEach, U0 = function(e) {
  e = e || {};
  var t = e.reporter, n = e.batchProcessor, o = e.stateHandler.getState;
  e.stateHandler.hasState;
  var r = e.idHandler;
  if (!n)
    throw new Error("Missing required dependency: batchProcessor");
  if (!t)
    throw new Error("Missing required dependency: reporter.");
  var a = d(), l = "erd_scroll_detection_scrollbar_style", s = "erd_scroll_detection_container";
  function u(k) {
    h(k, l, s);
  }
  u(window.document);
  function i(k) {
    var g = e.important ? " !important; " : "; ";
    return (k.join(g) + g).trim();
  }
  function d() {
    var k = 500, g = 500, b = document.createElement("div");
    b.style.cssText = i(["position: absolute", "width: " + k * 2 + "px", "height: " + g * 2 + "px", "visibility: hidden", "margin: 0", "padding: 0"]);
    var w = document.createElement("div");
    w.style.cssText = i(["position: absolute", "width: " + k + "px", "height: " + g + "px", "overflow: scroll", "visibility: none", "top: " + -500 * 3 + "px", "left: " + -500 * 3 + "px", "visibility: hidden", "margin: 0", "padding: 0"]), w.appendChild(b), document.body.insertBefore(w, document.body.firstChild);
    var T = k - w.clientWidth, _ = g - w.clientHeight;
    return document.body.removeChild(w), {
      width: T,
      height: _
    };
  }
  function h(k, g, b) {
    function w(M, y) {
      y = y || function(Y) {
        k.head.appendChild(Y);
      };
      var $ = k.createElement("style");
      return $.innerHTML = M, $.id = g, y($), $;
    }
    if (!k.getElementById(g)) {
      var T = b + "_animation", _ = b + "_animation_active", L = `/* Created by the element-resize-detector library. */
`;
      L += "." + b + " > div::-webkit-scrollbar { " + i(["display: none"]) + ` }

`, L += "." + _ + " { " + i(["-webkit-animation-duration: 0.1s", "animation-duration: 0.1s", "-webkit-animation-name: " + T, "animation-name: " + T]) + ` }
`, L += "@-webkit-keyframes " + T + ` { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }
`, L += "@keyframes " + T + " { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }", w(L);
    }
  }
  function x(k) {
    k.className += " " + s + "_animation_active";
  }
  function m(k, g, b) {
    if (k.addEventListener)
      k.addEventListener(g, b);
    else if (k.attachEvent)
      k.attachEvent("on" + g, b);
    else
      return t.error("[scroll] Don't know how to add event listeners.");
  }
  function f(k, g, b) {
    if (k.removeEventListener)
      k.removeEventListener(g, b);
    else if (k.detachEvent)
      k.detachEvent("on" + g, b);
    else
      return t.error("[scroll] Don't know how to remove event listeners.");
  }
  function v(k) {
    return o(k).container.childNodes[0].childNodes[0].childNodes[0];
  }
  function C(k) {
    return o(k).container.childNodes[0].childNodes[0].childNodes[1];
  }
  function A(k, g) {
    var b = o(k).listeners;
    if (!b.push)
      throw new Error("Cannot add listener to an element that is not detectable.");
    o(k).listeners.push(g);
  }
  function O(k, g, b) {
    b || (b = g, g = k, k = null), k = k || {};
    function w() {
      if (k.debug) {
        var H = Array.prototype.slice.call(arguments);
        if (H.unshift(r.get(g), "Scroll: "), t.log.apply)
          t.log.apply(null, H);
        else
          for (var re = 0; re < H.length; re++)
            t.log(H[re]);
      }
    }
    function T(H) {
      function re(ue) {
        var Se = ue.getRootNode && ue.getRootNode().contains(ue);
        return ue === ue.ownerDocument.body || ue.ownerDocument.body.contains(ue) || Se;
      }
      return !re(H) || window.getComputedStyle(H) === null;
    }
    function _(H) {
      var re = o(H).container.childNodes[0], ue = window.getComputedStyle(re);
      return !ue.width || ue.width.indexOf("px") === -1;
    }
    function L() {
      var H = window.getComputedStyle(g), re = {};
      return re.position = H.position, re.width = g.offsetWidth, re.height = g.offsetHeight, re.top = H.top, re.right = H.right, re.bottom = H.bottom, re.left = H.left, re.widthCSS = H.width, re.heightCSS = H.height, re;
    }
    function M() {
      var H = L();
      o(g).startSize = {
        width: H.width,
        height: H.height
      }, w("Element start size", o(g).startSize);
    }
    function y() {
      o(g).listeners = [];
    }
    function $() {
      if (w("storeStyle invoked."), !o(g)) {
        w("Aborting because element has been uninstalled");
        return;
      }
      var H = L();
      o(g).style = H;
    }
    function Y(H, re, ue) {
      o(H).lastWidth = re, o(H).lastHeight = ue;
    }
    function se(H) {
      return v(H).childNodes[0];
    }
    function V() {
      return 2 * a.width + 1;
    }
    function te() {
      return 2 * a.height + 1;
    }
    function ie(H) {
      return H + 10 + V();
    }
    function oe(H) {
      return H + 10 + te();
    }
    function ge(H) {
      return H * 2 + V();
    }
    function $e(H) {
      return H * 2 + te();
    }
    function z(H, re, ue) {
      var Se = v(H), Ce = C(H), ke = ie(re), Be = oe(ue), ze = ge(re), fe = $e(ue);
      Se.scrollLeft = ke, Se.scrollTop = Be, Ce.scrollLeft = ze, Ce.scrollTop = fe;
    }
    function X() {
      var H = o(g).container;
      if (!H) {
        H = document.createElement("div"), H.className = s, H.style.cssText = i(["visibility: hidden", "display: inline", "width: 0px", "height: 0px", "z-index: -1", "overflow: hidden", "margin: 0", "padding: 0"]), o(g).container = H, x(H), g.appendChild(H);
        var re = function() {
          o(g).onRendered && o(g).onRendered();
        };
        m(H, "animationstart", re), o(g).onAnimationStart = re;
      }
      return H;
    }
    function j() {
      function H() {
        var we = o(g).style;
        if (we.position === "static") {
          g.style.setProperty("position", "relative", k.important ? "important" : "");
          var tt = function($t, kt, Nl, kn) {
            function Hl(Fl) {
              return Fl.replace(/[^-\d\.]/g, "");
            }
            var yo = Nl[kn];
            yo !== "auto" && Hl(yo) !== "0" && ($t.warn("An element that is positioned static has style." + kn + "=" + yo + " which is ignored due to the static positioning. The element will need to be positioned relative, so the style." + kn + " will be set to 0. Element: ", kt), kt.style[kn] = 0);
          };
          tt(t, g, we, "top"), tt(t, g, we, "right"), tt(t, g, we, "bottom"), tt(t, g, we, "left");
        }
      }
      function re(we, tt, $t, kt) {
        return we = we ? we + "px" : "0", tt = tt ? tt + "px" : "0", $t = $t ? $t + "px" : "0", kt = kt ? kt + "px" : "0", ["left: " + we, "top: " + tt, "right: " + kt, "bottom: " + $t];
      }
      if (w("Injecting elements"), !o(g)) {
        w("Aborting because element has been uninstalled");
        return;
      }
      H();
      var ue = o(g).container;
      ue || (ue = X());
      var Se = a.width, Ce = a.height, ke = i(["position: absolute", "flex: none", "overflow: hidden", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%", "left: 0px", "top: 0px"]), Be = i(["position: absolute", "flex: none", "overflow: hidden", "z-index: -1", "visibility: hidden"].concat(re(-(1 + Se), -(1 + Ce), -Ce, -Se))), ze = i(["position: absolute", "flex: none", "overflow: scroll", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%"]), fe = i(["position: absolute", "flex: none", "overflow: scroll", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%"]), Le = i(["position: absolute", "left: 0", "top: 0"]), Je = i(["position: absolute", "width: 200%", "height: 200%"]), Ue = document.createElement("div"), He = document.createElement("div"), Ft = document.createElement("div"), nn = document.createElement("div"), U = document.createElement("div"), de = document.createElement("div");
      Ue.dir = "ltr", Ue.style.cssText = ke, Ue.className = s, He.className = s, He.style.cssText = Be, Ft.style.cssText = ze, nn.style.cssText = Le, U.style.cssText = fe, de.style.cssText = Je, Ft.appendChild(nn), U.appendChild(de), He.appendChild(Ft), He.appendChild(U), Ue.appendChild(He), ue.appendChild(Ue);
      function et() {
        var we = o(g);
        we && we.onExpand ? we.onExpand() : w("Aborting expand scroll handler: element has been uninstalled");
      }
      function on() {
        var we = o(g);
        we && we.onShrink ? we.onShrink() : w("Aborting shrink scroll handler: element has been uninstalled");
      }
      m(Ft, "scroll", et), m(U, "scroll", on), o(g).onExpandScroll = et, o(g).onShrinkScroll = on;
    }
    function ce() {
      function H(ze, fe, Le) {
        var Je = se(ze), Ue = ie(fe), He = oe(Le);
        Je.style.setProperty("width", Ue + "px", k.important ? "important" : ""), Je.style.setProperty("height", He + "px", k.important ? "important" : "");
      }
      function re(ze) {
        var fe = g.offsetWidth, Le = g.offsetHeight, Je = fe !== o(g).lastWidth || Le !== o(g).lastHeight;
        w("Storing current size", fe, Le), Y(g, fe, Le), n.add(0, function() {
          if (Je) {
            if (!o(g)) {
              w("Aborting because element has been uninstalled");
              return;
            }
            if (!ue()) {
              w("Aborting because element container has not been initialized");
              return;
            }
            if (k.debug) {
              var Ue = g.offsetWidth, He = g.offsetHeight;
              (Ue !== fe || He !== Le) && t.warn(r.get(g), "Scroll: Size changed before updating detector elements.");
            }
            H(g, fe, Le);
          }
        }), n.add(1, function() {
          if (!o(g)) {
            w("Aborting because element has been uninstalled");
            return;
          }
          if (!ue()) {
            w("Aborting because element container has not been initialized");
            return;
          }
          z(g, fe, Le);
        }), Je && ze && n.add(2, function() {
          if (!o(g)) {
            w("Aborting because element has been uninstalled");
            return;
          }
          if (!ue()) {
            w("Aborting because element container has not been initialized");
            return;
          }
          ze();
        });
      }
      function ue() {
        return !!o(g).container;
      }
      function Se() {
        function ze() {
          return o(g).lastNotifiedWidth === void 0;
        }
        w("notifyListenersIfNeeded invoked");
        var fe = o(g);
        if (ze() && fe.lastWidth === fe.startSize.width && fe.lastHeight === fe.startSize.height)
          return w("Not notifying: Size is the same as the start size, and there has been no notification yet.");
        if (fe.lastWidth === fe.lastNotifiedWidth && fe.lastHeight === fe.lastNotifiedHeight)
          return w("Not notifying: Size already notified");
        w("Current size not notified, notifying..."), fe.lastNotifiedWidth = fe.lastWidth, fe.lastNotifiedHeight = fe.lastHeight, $0(o(g).listeners, function(Le) {
          Le(g);
        });
      }
      function Ce() {
        if (w("startanimation triggered."), _(g)) {
          w("Ignoring since element is still unrendered...");
          return;
        }
        w("Element rendered.");
        var ze = v(g), fe = C(g);
        (ze.scrollLeft === 0 || ze.scrollTop === 0 || fe.scrollLeft === 0 || fe.scrollTop === 0) && (w("Scrollbars out of sync. Updating detector elements..."), re(Se));
      }
      function ke() {
        if (w("Scroll detected."), _(g)) {
          w("Scroll event fired while unrendered. Ignoring...");
          return;
        }
        re(Se);
      }
      if (w("registerListenersAndPositionElements invoked."), !o(g)) {
        w("Aborting because element has been uninstalled");
        return;
      }
      o(g).onRendered = Ce, o(g).onExpand = ke, o(g).onShrink = ke;
      var Be = o(g).style;
      H(g, Be.width, Be.height);
    }
    function N() {
      if (w("finalizeDomMutation invoked."), !o(g)) {
        w("Aborting because element has been uninstalled");
        return;
      }
      var H = o(g).style;
      Y(g, H.width, H.height), z(g, H.width, H.height);
    }
    function G() {
      b(g);
    }
    function le() {
      w("Installing..."), y(), M(), n.add(0, $), n.add(1, j), n.add(2, ce), n.add(3, N), n.add(4, G);
    }
    w("Making detectable..."), T(g) ? (w("Element is detached"), X(), w("Waiting until element is attached..."), o(g).onRendered = function() {
      w("Element is now attached"), le();
    }) : le();
  }
  function I(k) {
    var g = o(k);
    g && (g.onExpandScroll && f(v(k), "scroll", g.onExpandScroll), g.onShrinkScroll && f(C(k), "scroll", g.onShrinkScroll), g.onAnimationStart && f(g.container, "animationstart", g.onAnimationStart), g.container && k.removeChild(g.container));
  }
  return {
    makeDetectable: O,
    addListener: A,
    uninstall: I,
    initDocument: u
  };
}, dn = Ml.forEach, j0 = T0, W0 = V0, G0 = M0, q0 = O0, Y0 = z0, Pa = Pl, Z0 = R0, pt = H0, K0 = F0, X0 = U0;
function La(e) {
  return Array.isArray(e) || e.length !== void 0;
}
function Da(e) {
  if (Array.isArray(e))
    return e;
  var t = [];
  return dn(e, function(n) {
    t.push(n);
  }), t;
}
function Ra(e) {
  return e && e.nodeType === 1;
}
var Q0 = function(e) {
  e = e || {};
  var t;
  if (e.idHandler)
    t = {
      get: function(O) {
        return e.idHandler.get(O, !0);
      },
      set: e.idHandler.set
    };
  else {
    var n = G0(), o = q0({
      idGenerator: n,
      stateHandler: pt
    });
    t = o;
  }
  var r = e.reporter;
  if (!r) {
    var a = r === !1;
    r = Y0(a);
  }
  var l = mt(e, "batchProcessor", Z0({ reporter: r })), s = {};
  s.callOnAdd = !!mt(e, "callOnAdd", !0), s.debug = !!mt(e, "debug", !1);
  var u = W0(t), i = j0({
    stateHandler: pt
  }), d, h = mt(e, "strategy", "object"), x = mt(e, "important", !1), m = {
    reporter: r,
    batchProcessor: l,
    stateHandler: pt,
    idHandler: t,
    important: x
  };
  if (h === "scroll" && (Pa.isLegacyOpera() ? (r.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."), h = "object") : Pa.isIE(9) && (r.warn("Scroll strategy is not supported on IE9. Changing to object strategy."), h = "object")), h === "scroll")
    d = X0(m);
  else if (h === "object")
    d = K0(m);
  else
    throw new Error("Invalid strategy name: " + h);
  var f = {};
  function v(O, I, k) {
    function g(M) {
      var y = u.get(M);
      dn(y, function($) {
        $(M);
      });
    }
    function b(M, y, $) {
      u.add(y, $), M && $(y);
    }
    if (k || (k = I, I = O, O = {}), !I)
      throw new Error("At least one element required.");
    if (!k)
      throw new Error("Listener required.");
    if (Ra(I))
      I = [I];
    else if (La(I))
      I = Da(I);
    else
      return r.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");
    var w = 0, T = mt(O, "callOnAdd", s.callOnAdd), _ = mt(O, "onReady", function() {
    }), L = mt(O, "debug", s.debug);
    dn(I, function(M) {
      pt.getState(M) || (pt.initState(M), t.set(M));
      var y = t.get(M);
      if (L && r.log("Attaching listener to element", y, M), !i.isDetectable(M)) {
        if (L && r.log(y, "Not detectable."), i.isBusy(M)) {
          L && r.log(y, "System busy making it detectable"), b(T, M, k), f[y] = f[y] || [], f[y].push(function() {
            w++, w === I.length && _();
          });
          return;
        }
        return L && r.log(y, "Making detectable..."), i.markBusy(M, !0), d.makeDetectable({ debug: L, important: x }, M, function($) {
          if (L && r.log(y, "onElementDetectable"), pt.getState($)) {
            i.markAsDetectable($), i.markBusy($, !1), d.addListener($, g), b(T, $, k);
            var Y = pt.getState($);
            if (Y && Y.startSize) {
              var se = $.offsetWidth, V = $.offsetHeight;
              (Y.startSize.width !== se || Y.startSize.height !== V) && g($);
            }
            f[y] && dn(f[y], function(te) {
              te();
            });
          } else
            L && r.log(y, "Element uninstalled before being detectable.");
          delete f[y], w++, w === I.length && _();
        });
      }
      L && r.log(y, "Already detecable, adding listener."), b(T, M, k), w++;
    }), w === I.length && _();
  }
  function C(O) {
    if (!O)
      return r.error("At least one element is required.");
    if (Ra(O))
      O = [O];
    else if (La(O))
      O = Da(O);
    else
      return r.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");
    dn(O, function(I) {
      u.removeAllListeners(I), d.uninstall(I), pt.cleanState(I);
    });
  }
  function A(O) {
    d.initDocument && d.initDocument(O);
  }
  return {
    listenTo: v,
    removeListener: u.removeListener,
    removeAllListeners: u.removeAllListeners,
    uninstall: C,
    initDocument: A
  };
};
function mt(e, t, n) {
  var o = e[t];
  return o == null && n !== void 0 ? n : o;
}
const J0 = /* @__PURE__ */ A0(Q0);
function ev(e) {
  return { all: e = e || /* @__PURE__ */ new Map(), on: function(t, n) {
    var o = e.get(t);
    o ? o.push(n) : e.set(t, [n]);
  }, off: function(t, n) {
    var o = e.get(t);
    o && (n ? o.splice(o.indexOf(n) >>> 0, 1) : e.set(t, []));
  }, emit: function(t, n) {
    var o = e.get(t);
    o && o.slice().map(function(r) {
      r(n);
    }), (o = e.get("*")) && o.slice().map(function(r) {
      r(t, n);
    });
  } };
}
function Rl() {
  return typeof window < "u";
}
const Bl = () => {
};
function tv(e, t = Bl) {
  if (!Rl) {
    t();
    return;
  }
  window.addEventListener(e, t);
}
function nv(e, t = Bl) {
  Rl && window.removeEventListener(e, t);
}
const ov = {
  __name: "grid-layout",
  props: {
    layout: {
      type: Array,
      required: !0
    },
    responsiveLayouts: {
      type: Object,
      default() {
        return {};
      }
    },
    colNum: {
      type: Number,
      default: 12
    },
    rowHeight: {
      type: Number,
      default: 150
    },
    maxRows: {
      type: Number,
      default: 1 / 0
    },
    margin: {
      type: Array,
      default() {
        return [10, 10];
      }
    },
    isDraggable: {
      type: Boolean,
      default: !0
    },
    isResizable: {
      type: Boolean,
      default: !0
    },
    isMirrored: {
      type: Boolean,
      default: !1
    },
    isBounded: {
      type: Boolean,
      default: !1
    },
    // If true, the container height swells and contracts to fit contents
    autoSize: {
      type: Boolean,
      default: !0
    },
    verticalCompact: {
      type: Boolean,
      default: !0
    },
    restoreOnDrag: {
      type: Boolean,
      default: !1
    },
    preventCollision: {
      type: Boolean,
      default: !1
    },
    useCssTransforms: {
      type: Boolean,
      default: !0
    },
    responsive: {
      type: Boolean,
      default: !1
    },
    breakpoints: {
      type: Object,
      default() {
        return { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 };
      }
    },
    cols: {
      type: Object,
      default() {
        return { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 };
      }
    },
    useStyleCursor: {
      type: Boolean,
      default: !0
    },
    transformScale: {
      type: Number,
      default: 1
    }
  },
  emits: [
    "update:layout",
    "layout-ready",
    "layout-created",
    "layout-before-mount",
    "layout-mounted",
    "layout-updated",
    "breakpoint-changed"
  ],
  setup(e, { expose: t, emit: n }) {
    const o = e, r = n, a = ev(), l = {
      el: void 0,
      placeholderEl: void 0,
      placeholder: {},
      emitter: a
    };
    t(l);
    const s = J0({
      strategy: "scroll",
      // <- For ultra performance.
      // See https://github.com/wnr/element-resize-detector/issues/110 about callOnAdd.
      callOnAdd: !1
    }), u = _e({
      width: null,
      mergedStyle: {},
      isDragging: !1,
      placeholder: {
        x: 0,
        y: 0,
        w: 0,
        h: 0,
        i: -1
      },
      layout: [],
      layouts: {},
      // array to store all layouts from different breakpoints
      lastBreakpoint: null,
      // store last active breakpoint
      originalLayout: null
      // store original Layout
    });
    Ye("emitter", a), Ye("gridLayout", { props: o, state: u });
    function i() {
      a.on("resizeEvent", C), a.on("dragEvent", v), r("layout-created", o.layout), tv("resize", m);
    }
    i(), Xl(() => {
      r("layout-before-mount", u.layout);
    }), Te(() => {
      r("layout-mounted", u.layout);
    }), Qe(() => {
      a.off("resizeEvent", C), a.off("dragEvent", v), nv("resize", m), s && l.el && s.uninstall(l.el);
    }), Z(() => u.width, async (g, b) => {
      a.emit("updateWidth", u.width), x(), b === null && (await Ee(), r("layout-ready", u.layout));
    }), Z(() => o.layout, () => {
      w0(o.layout), u.originalLayout = o.layout, h();
    }, { deep: !0, immediate: !0 }), Z(() => o.colNum, () => {
      u.colNum = o.colNum, a.emit("setColNum", o.colNum);
    }, { immediate: !0 }), Z(() => o.rowHeight, () => {
      a.emit("setRowHeight", o.rowHeight);
    }), Z(() => o.isDraggable, () => {
      a.emit("setDraggable", o.isDraggable);
    }), Z(() => o.isResizable, () => {
      a.emit("setResizable", o.isResizable);
    }), Z(() => o.isBounded, () => {
      a.emit("setBounded", o.isBounded);
    }), Z(() => o.transformScale, () => {
      a.emit("setTransformScale", o.transformScale);
    }), Z(() => o.responsive, () => {
      o.responsive || (u.layout = Tt(u.originalLayout), a.emit("setColNum", o.colNum)), m();
    }), Z(() => o.maxRows, () => {
      a.emit("setMaxRows", o.maxRows);
    }), Z(() => o.margin, () => {
      x();
    }, { deep: !0 });
    function d(g, b) {
      if ((g == null ? void 0 : g.length) !== (b == null ? void 0 : b.length))
        return !0;
      let w = !1, T = 0;
      for (; T < g.length; ) {
        const _ = g[T], L = Object.keys(_), M = b[T], y = Object.keys(M), $ = [.../* @__PURE__ */ new Set([...L, ...y])];
        let Y = 0;
        for (; Y < $.length; ) {
          const se = $[Y];
          if (_[se] !== M[se]) {
            w = !0;
            break;
          }
          Y += 1;
        }
        if (w)
          break;
        T += 1;
      }
      return w;
    }
    function h() {
      if (d(o.layout, u.layout) && (O(), u.layout = Tt(o.layout), cn(u.layout, o.verticalCompact), a.emit("updateWidth", u.width), x(), d(o.layout, u.layout))) {
        const g = Tt(u.layout);
        r("layout-updated", g), r("update:layout", g);
      }
    }
    function x() {
      u.mergedStyle = {
        height: f()
      };
    }
    function m() {
      l.el && (u.width = l.el.offsetWidth), a.emit("resizeEvent");
    }
    function f() {
      var g;
      if (!o.autoSize) return;
      const b = ((g = o.margin) == null ? void 0 : g[1]) || 0;
      return `${p0(u.layout) * (o.rowHeight + b) + b}px`;
    }
    function v([g, b, w, T, _, L] = []) {
      let M = Ia(u.layout, b);
      if (M == null && (M = { x: 0, y: 0 }), g === "dragstart" && !o.verticalCompact && (u.positionsBeforeDrag = u.layout.reduce((y, { i: $, x: Y, y: se }) => ({
        ...y,
        [$]: { x: Y, y: se }
      }), {})), g === "dragmove" || g === "dragstart" ? (u.placeholder.x = M.x, u.placeholder.y = M.y, u.placeholder.w = L, u.placeholder.h = _, u.isDragging = !0) : u.isDragging = !1, u.layout = Go(u.layout, M, w, T, !0, o.preventCollision), o.restoreOnDrag ? (M.static = !0, cn(u.layout, o.verticalCompact, u.positionsBeforeDrag), M.static = !1) : cn(u.layout, o.verticalCompact), x(), g === "dragend" && delete u.positionsBeforeDrag, d(o.layout, u.layout)) {
        const y = Tt(u.layout);
        r("layout-updated", y), r("update:layout", y);
      }
    }
    function C([g, b, w, T, _, L] = []) {
      let M = Ia(u.layout, b);
      M == null && (M = { h: 0, w: 0 });
      let y;
      if (o.preventCollision) {
        const $ = _l(u.layout, { ...M, w: L, h: _ }).filter(
          (Y) => Y.i !== M.i
        );
        if (y = $.length > 0, y) {
          let Y = 1 / 0, se = 1 / 0;
          $.forEach((V) => {
            V.x > M.x && (Y = Math.min(Y, V.x)), V.y > M.y && (se = Math.min(se, V.y));
          }), Number.isFinite(Y) && (M.w = Y - M.x), Number.isFinite(se) && (M.h = se - M.y);
        }
      }
      if (y || (M.w = L, M.h = _), g === "resizestart" || g === "resizemove" ? (u.placeholder.x = w, u.placeholder.y = T, u.placeholder.w = M.w, u.placeholder.h = M.h, u.isDragging = !0) : u.isDragging = !1, o.responsive && A(), cn(u.layout, o.verticalCompact), x(), d(o.layout, u.layout)) {
        const $ = Tt(u.layout);
        r("layout-updated", $), r("update:layout", $);
      }
    }
    function A() {
      const g = _0(o.breakpoints, u.width), b = kl(g, o.cols);
      b !== u.colNum && (u.colNum = b, u.layout = E0(
        u.layout,
        u.layouts,
        o.breakpoints,
        g,
        b,
        o.verticalCompact
      ), u.layouts[g] = u.layout, a.emit("setColNum", b)), u.lastBreakpoint !== g && (u.lastBreakpoint = g, r("breakpoint-changed", g, u.layout));
    }
    function O() {
      u.layouts = { ...o.responsiveLayouts };
    }
    function I(g) {
      !g || g === l.el || (l.el = g, s && s.listenTo(l.el, () => {
        m();
      }), u.width = l.el.offsetWidth);
    }
    function k(g) {
      g && (l.placeholder = g, l.placeholderEl = g.el);
    }
    return (g, b) => (E(), F("div", {
      ref: I,
      class: "vue-grid-layout",
      style: he(u.mergedStyle)
    }, [
      ye(g.$slots, "default", {}, void 0, !0),
      Ot(c(Tl, {
        ref: k,
        class: "vue-grid-placeholder",
        x: u.placeholder.x,
        y: u.placeholder.y,
        w: u.placeholder.w,
        h: u.placeholder.h,
        i: "placeholder"
      }, null, 8, ["x", "y", "w", "h"]), [
        [zt, u.isDragging]
      ])
    ], 4));
  }
}, rv = /* @__PURE__ */ Il(ov, [["__scopeId", "data-v-a10aee95"]]);
function qo(e) {
  qo.installed || (qo.installed = !0, e.component("GridLayout", rv), e.component("GridItem", Tl));
}
const av = "0.0.1-beta4", fv = {
  install: (e) => {
    e.use(Cs), e.use(qo), e.component("ls-view", am), e.component("ls-manage", Jm);
  }
};
try {
  typeof window == "object" && Object.assign(window, {
    __DDLS_VERSION: av
  });
} catch (e) {
  console.warn(e);
}
export {
  Jm as LsManage,
  am as LsView,
  Ie as apiProvider,
  fv as default,
  lv as registerComponent,
  dv as registerComponentBackground,
  uv as registerHeaderBackground,
  cv as registerPageBackground,
  sv as registerTitleIcon
};
