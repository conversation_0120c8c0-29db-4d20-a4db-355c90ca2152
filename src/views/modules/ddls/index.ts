import { http } from "@/utils/http";
import ddls, { apiProvider, registerComponent } from "./lib/index.mjs";
import "./lib/index.css";
import { ServerNames } from "@/utils/http/serverNames";
import { DefineComponent } from "vue";
// 注入大屏的相关api
// 注意: 不同的项目架构需要做适配
Object.assign(apiProvider, {
  // axios请求拼接的前缀
  basePath: ServerNames.devplatformService,
  // 资源路径不会像axios那样在前面拼rest，这里需要手动拼接
  resourcePath: "/rest" + ServerNames.devplatformService,
  get: (url, params, config) => {
    return http.get(url, params, config);
  },
  post: (url, data, config) => {
    return http.post(url, { data }, config);
  }
});
// 大屏组件
export interface ScreenComponent {
  name: string;
  key: string;
  group?: string;
  component: DefineComponent<{}, {}, any>;
}

export default ddls;
export * from "./lib/index.mjs";
// 导出公共的小组件
export * from "./components";
// 上面的*已经包含了registerComponent，这里显示导出方便ide工具识别（有的ide识别不了）
export { registerComponent };
