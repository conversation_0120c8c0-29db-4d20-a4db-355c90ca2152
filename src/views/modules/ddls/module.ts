import { PropType } from "vue";
/**
 * 属性基本属性
 */
export const baseComponentProps = {
  // 是否显示了标题
  showTitle: Boolean,
  // 标题高度
  titleHeight: Number,
  // 是否设计模式
  designMode: Boolean,
  // 是否设计模式下组件选择模式
  selectionMode: Boolean,
  // tip信息
  tip: String,
  // 获取配置的文本信息
  getContextText: {
    type: Function as PropType<(key: string, defaultValue: string) => string>,
    default: () => {
      return "";
    }
  }
};
