<template>
  <el-tabs tab-position="left">
    <el-tab-pane label="表格">
      <tables />
    </el-tab-pane>
    <el-tab-pane label="表单">
      <forms />
    </el-tab-pane>
    <el-tab-pane label="拖动排序">
      <sortable-demo></sortable-demo>
    </el-tab-pane>
    <el-tab-pane label="echarts关系拓扑图">
      <echart-topo-demo></echart-topo-demo>
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import Tables from "./tables/index.vue";
import Forms from "./form/index.vue";
import EchartTopoDemo from "./echart-topo/EchartTopoDemo.vue";
import SortableDemo from "./sortable/SortableDemo.vue";
</script>
