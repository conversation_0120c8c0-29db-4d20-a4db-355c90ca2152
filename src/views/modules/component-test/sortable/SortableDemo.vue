<template>
  <div>
    <el-row>
      <el-col :span="12">
        <sortable v-model="state.items">
          <!--          <template #item="{ item, index }">-->
          <!--            <div>{{ item.label }} - {{ index }}</div>-->
          <!--          </template>-->
          <div
            class="w-full h-[40px] flex-c mt-2"
            style="border: 1px solid rgba(188, 185, 185, 0.65)"
            v-for="(item, index) in state.items"
            :key="index"
          >
            <div>{{ item.label }} - {{ index }}</div>
          </div>
        </sortable>
      </el-col>
      <el-col :span="12">
        <p style="margin-left: 20px">{{ state.items }}</p>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import Sortable from "@/components/ItsmCommon/sortable/Sortable.vue";
import { reactive } from "vue";

const state = reactive({
  items: Array.from({ length: 5 }, (_, index) => {
    return {
      label: "标题 - " + index,
      value: index
    };
  })
});
</script>
<style scoped lang="scss"></style>
