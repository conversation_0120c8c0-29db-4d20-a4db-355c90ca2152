<template>
  <div class="mb-20">
    <el-card>
      <template #header> 例1.常规使用</template>
      <p class="mb-4">
        im-table只是在el-table基础上做了功能增强，如果im-table关闭所有增强配置选项基本上和el-table完全一样,例如下面的例子来自官网，右边的表格只是把el-table改成了im-table
        注: im-table默认开启了边框(border)和条纹(stripe)
        el-table基本上所有的方法/属性/事件/插槽在im-table都可以使用,常用的开发工具例如idea也能智能识别补全,文档查询element-plus官网API即可
        喜欢使用标签风格的也可以使用im-table-column替代el-table-column，能配置更多增强属性
      </p>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="Date" width="180" />
            <el-table-column prop="name" label="Name" width="180" />
            <el-table-column prop="address" label="Address" />
          </el-table>
        </el-col>
        <el-col :span="12">
          <im-table :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="Date" width="180" />
            <el-table-column prop="name" label="Name" width="180" />
            <el-table-column prop="address" label="Address" />
          </im-table>
        </el-col>
      </el-row>
    </el-card>

    <el-card>
      <template #header> 例2.动态字段</template>
      <p class="mb-4">
        和v2封装的表格一致，对columns封装提供动态字段能力，支持字段分组和以及自定义插槽使用，columns配置和2.0基本一致；
        在3.0的im-table如果要使用插槽不再强制字段声明slot属性，默认以字段的prop作为插槽名，如果声明了slot则使用slot(兼容v2)
        同样列头也不在强制声明headerSlot属性,默认以${prop}-header作为插槽名,
        如果声明了headerSlot则使用headerSlot(兼容v2)
      </p>
      <im-table
        center
        :loading="state.loading"
        :data="state.userFactData"
        :columns="baseColumns"
        :height="tableHeight"
      >
        <template #ops>
          <el-button type="primary">这是操作列 插槽: ops</el-button>
        </template>

        <!-- 这里自定义类型列，#type为prop的名字 用按钮或者标签 -->
        <template #type="{ row, $index }">
          <el-button type="primary">{{ row.type }}</el-button>
        </template>

        <!-- 这里自定义类型列头，#type为prop的名字，type-header即列头插槽 用按钮或者标签 -->
        <template #type-header="{ column, $index }">
          <el-tag type="primary">这个列头自定义了</el-tag>
        </template>
      </im-table>
    </el-card>

    <el-card>
      <template #header> 例3.增强功能</template>
      <p class="mb-4">
        增强功能参考了antd table pro使用习惯部分也参考了avue(工具栏插槽)
        crud功能，下面是一个完整的demo 注:
        获取当前选择行可以直接使用ref对象的checkedRows即可,可以省去一个选择事件，某些批量操作设置禁用时很有用；
        工具栏定义的输入框也可以直接使用插槽提供的toolbarState可以省去一些定义；
        表格的操作列可以直接在columns里面定义，也可以使用:operator="true"(或者:operator="{prop:
        'operatorCustom'}")声明，然后使用插槽#operator(当operator设置为true时默认operator，
        否则为prop设置的值)
      </p>
      <im-table
        ref="table"
        center
        :toolbar="{
          initialState: {
            aa: 2
          }
        }"
        table-alert
        :loading="state.loading"
        :data="state.userFactData"
        :columns="state.userFactColumns"
        :height="tableHeight"
        :pagination="state.pagination"
        :operator="{ label: '自定义的操作列名', prop: 'operatorCustom1' }"
        @on-reload="handlePageCurrentChange"
        @on-page-current-change="handlePageCurrentChange"
      >
        <template #toolbar-left="toolbarSlot: ImToolbarSlot">
          <el-radio-group v-model="toolbarSlot.toolbarState.aa">
            <el-radio-button label="全部" :value="1"></el-radio-button>
            <el-radio-button label="高危" :value="2"></el-radio-button>
          </el-radio-group>
          <el-button type="danger" :disabled="table?.checkedRows.length == 0"
            >批量删除1</el-button
          >
          <el-button
            type="danger"
            :disabled="toolbarSlot.checkedRows.length == 0"
            >批量删除2</el-button
          >
        </template>
        <template #toolbar-right>
          <div class="flex-c gap-2">
            <el-select style="width: 150px" placeholder="请选择类型1">
            </el-select>
            <el-select style="width: 200px" placeholder="请选择类型2">
            </el-select>
            <el-button type="primary" @click="resetQuery">查询</el-button>
          </div>
        </template>

        <template #operator1>
          <el-button type="primary">这是操作列1</el-button>
        </template>

        <template #operatorCustom1>
          <el-button type="primary">这是操作列2</el-button>
        </template>
      </im-table>
    </el-card>

    <el-card>
      <template #header> 例4.使用request加载数据</template>
      <p class="mb-4">
        参考了antd table pro request使用方法,
        使用request方法不需要注册分页事件和刷新事件以及loading控制，组件内部处理
      </p>
      <im-table
        ref="table"
        center
        :toolbar="{
          initialState: {
            aa: 2
          }
        }"
        table-alert
        :columns="state.userFactColumns"
        :height="tableHeight"
        :pagination="state.pagination"
        :operator="{ label: '自定义的操作列名', prop: 'operatorCustom1' }"
        :request="tableRequest"
      >
        <template #toolbar-left="toolbarSlot: ImToolbarSlot">
          <el-radio-group v-model="toolbarSlot.toolbarState.aa">
            <el-radio-button label="全部" :value="1"></el-radio-button>
            <el-radio-button label="高危" :value="2"></el-radio-button>
          </el-radio-group>
          <el-button type="danger" :disabled="table?.checkedRows.length == 0"
            >批量删除1</el-button
          >
          <el-button
            type="danger"
            :disabled="toolbarSlot.checkedRows.length == 0"
            >批量删除2</el-button
          >
        </template>
        <template #toolbar-right>
          <div class="flex-c gap-2">
            <el-select style="width: 150px" placeholder="请选择类型1">
            </el-select>
            <el-select style="width: 200px" placeholder="请选择类型2">
            </el-select>
            <el-button type="primary" @click="resetQuery">查询</el-button>
          </div>
        </template>

        <template #operator1>
          <el-button type="primary">这是操作列1</el-button>
        </template>

        <template #operatorCustom1>
          <el-button type="primary">这是操作列2</el-button>
        </template>
      </im-table>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import {
  ImTableColumnProps,
  ImTableInstance,
  ImTablePaginationProps,
  ImToolbarSlot
} from "@/components/ItsmCommon";
import { RequestResult } from "@/components/ItsmCommon/table/props";
import ColumnSetting from "@/views/system/columnmanage/ColumnSetting.vue";

const table = ref<ImTableInstance>();

const tableData = [
  {
    date: "2016-05-03",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-02",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-04",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  },
  {
    date: "2016-05-01",
    name: "Tom",
    address: "No. 189, Grove St, Los Angeles"
  }
];

interface UserFact {
  id: string;
  name: string;
  type: string;
  createTime: string;
  usage: number;
}

const baseColumns = <ImTableColumnProps<UserFact>[]>[
  {
    label: "选择",
    type: "selection",
    width: "60px"
  },
  {
    label: "编号",
    align: "left",
    prop: "id",
    width: "120px"
  },
  {
    label: "名称",
    prop: "name",
    filterable: true,
    width: "180px"
  },
  {
    label: "类型",
    prop: "type",
    meta: {
      filterable: true
    }
  },
  {
    label: "创建时间",
    prop: "createTime"
  },
  {
    label: "计数",
    prop: "usage"
  },
  {
    label: "操作列",
    slot: "ops"
  }
];

const tableHeight = ref(400);
const state = reactive({
  loading: false,
  // 快捷设置所有的列居中
  center: false,
  pagination: <ImTablePaginationProps>{
    align: "right",
    total: 123,
    currentPage: 1,
    pageSize: 20,
    pageSizes: [20, 40, 60],
    props: {
      // 更多page属性请参考官网配置
    }
  },
  userFactColumns: <ImTableColumnProps<any>[]>[
    {
      label: "选择",
      type: "selection",
      width: "60px"
    },
    {
      label: "编号",
      align: "left",
      prop: "id",
      width: "120px"
    },
    {
      label: "名称",
      prop: "name",
      filterable: true,
      width: "180px"
    },
    {
      label: "类型",
      prop: "type",
      width: "180px",
      filterable: true,
      meta: {
        filterMode: "local",
        options: [
          { label: "a", value: "a" },
          { label: "b", value: "b" },
          { label: "c", value: "c" },
          { label: "d", value: "d" }
        ]
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: "180px",
      filterable: true
    },
    {
      label: "计数",
      prop: "usage",
      width: "180px",
      filterable: true
    },
    {
      label: "操作列1",
      slot: "operator1"
    }
  ],
  userFactData: []
});

const onColumnsUpdate = () => {};

const mockData = (page: number, pageSize: number) => {
  const total = state.pagination.total;
  const offset = (page - 1) * pageSize;
  const length = Math.min(offset + pageSize, total) - offset;
  return Array.from(
    {
      length
    },
    (_, index) => {
      const xh = offset + index;
      return {
        id: xh + "",
        name: "测试名称-" + (index % 5),
        type: ["a", "b", "c"][index % 3],
        createTime: new Date().toDateString(),
        usage: Math.round(Math.random() * 10000)
      };
    }
  );
};

// 模拟分页查询接口
const handlePageCurrentChange = (page: number, pageSize: number) => {
  console.log("page pageSize", page, pageSize);
  state.loading = true;
  setTimeout(() => {
    state.loading = false;
    state.userFactData = mockData(page, pageSize);
  }, 500);
};

const resetQuery = () => {
  state.pagination.currentPage = 1;
  handlePageCurrentChange(1, state.pagination.pageSize);
};

handlePageCurrentChange(1, 20);

const tableRequest = async (params, sort, filter) => {
  // 注 request 需要返回promise对象
  let { currentPage, pageSize } = params;
  console.log("request", currentPage, pageSize);
  // 这里模拟发送一个500毫秒延时的请求
  let res = await new Promise<RequestResult>(resolve => {
    setTimeout(() => {
      resolve({
        rows: mockData(currentPage, pageSize),
        total: state.pagination.total,
        success: true
      });
    }, 500);
  });

  // 最后组装结构: {rows, total, success}
  // success需要设置为true
  return res;
};

const testTableApi = () => {
  console.log(table.value);
  console.log(table.value.getSelectionRows());
};
</script>
