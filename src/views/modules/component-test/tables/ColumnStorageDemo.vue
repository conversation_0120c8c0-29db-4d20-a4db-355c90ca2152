<template>
  <div class="mb-20">
    <p>表头列拖拽排序指定属性: column-sortable值为true即可，后续拓展更多配置</p>
    <p>支持表格行的排序: sortable值为true</p>
    <im-table
      ref="table"
      center
      :toolbar="{
        initialState: {
          aa: 2
        }
      }"
      :column-storage="createColumnStorage('vul-detail', 'remote')"
      :table-alert="true"
      column-sortable
      sortable
      :loading="state.loading"
      :data="state.userFactData"
      :columns="state.userFactColumns"
      :height="tableHeight"
      :pagination="state.pagination"
      :operator="state.operator"
      :filter-data-provider="filterDataProvider"
      @on-columns-storage="handleColumnsStorage"
    >
      <!-- 自定义 alert 提示内容 -->
      <template #tip>
        <span class="statistics-tip" style="margin-left: 100px">
          <span
            class="statistics-item mr-2 ml-2"
            v-for="(item, index) in vulnerabilityStatisticsData"
            :key="index"
          >
            <span class="label">{{ item.label }}: </span>
            <span class="count text-[16px] font-bold ml-1">{{
              item.value || 0
            }}</span>
          </span>
        </span>
      </template>

      <!-- 表格右侧菜单 -->
      <template #toolbar-right="{ size }">
        <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
          <!-- <div>{{ state.selectedEvents }}</div> -->

          dfdf
        </div>
      </template>
      <!-- 表格操作按钮 -->
      <template #operator="{ row, size, type }">
        <!-- 处置按钮 -->
        <!-- <el-button :size="size" :type="type" text :icon="useRenderIcon('EP-Checked')"
          @click="eventDealHandler(row)" v-if="row.deal_status === '1'">
          处置
        </el-button> -->
        <!-- 详情按钮 -->
        <el-button type="primary" text :icon="useRenderIcon('EP-View')">
          详情2 {{ row }}
        </el-button>
      </template>
      <!-- 风险级别列 -->
      <template #vullevel="{ row }">
        <!-- <div>{{ row }}</div> -->
        <el-tag
          :color="getRiskLevelColor(row.vullevel + '')"
          class="text-white border-none"
        >
          {{ getRiskLevelLabel(row.vullevel + "") }}
        </el-tag>
      </template>
      <!-- 事件处理状态列 -->
      <template #dealStatus="{ row }">
        <!-- <div>{{ row }}</div> -->
        <el-tag effect="light" :type="getDealStatusType(row.dealStatus)">
          {{ getSegmentLabel(row.dealStatus) }}
        </el-tag>
      </template>
      <template #event_subtype_name="{ row }">
        <el-tag>{{ row.event_subtype_name }}</el-tag>
      </template>
    </im-table>

    <el-input
      type="textarea"
      :rows="100"
      :model-value="JSON.stringify(state.userFactColumns, null, 4)"
    ></el-input>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import {
  ColumnMetaProps,
  createColumnStorage,
  HeaderFilterValue,
  ImTableColumnProps,
  ImTableInstance,
  ImTablePaginationProps,
  ImToolbarSlot,
  IndexRender
} from "@/components/ItsmCommon";
import { queryTableSet, saveTableSet } from "@/views/system/columnmanage/api";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { ElMessage } from "element-plus";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData
} from "@/views/modules/security/securityVulnerability/util/vulnerability_data";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const table = ref<ImTableInstance>();
const tableHeight = ref(450);
const assetTypes = reactive([]);
const treeData = reactive([]);
const state = reactive({
  loading: false,
  // 快捷设置所有的列居中
  center: false,
  pagination: <ImTablePaginationProps>{
    align: "right",
    total: 123,
    currentPage: 1,
    pageSize: 20,
    pageSizes: [20, 40, 60],
    props: {
      // 更多page属性请参考官网配置
    }
  },
  // 操作
  operator: <ImTableColumnProps<any>>{
    fixed: "right",
    label: "操作"
  },
  userFactColumns: <ImTableColumnProps<any>[]>[
    {
      label: "选择",
      type: "selection",
      width: "60px",
      disableColumnSortable: true
    },
    {
      label: "序号",
      render: IndexRender,
      width: "80px"
    },
    {
      label: "编号",
      align: "left",
      prop: "id",
      width: "120px"
    },
    {
      label: "名称",
      prop: "name",
      width: "180px",
      filterable: true,
      meta: {
        filterType: "input" // 使用简单的文本框
      }
    },
    {
      label: "下拉过滤",
      prop: "type",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "select", // 或者el-select
        // 定义静态数组
        options: [
          { label: "a", value: "a" },
          { label: "b", value: "b" }
        ],
        props: <ColumnMetaProps>{
          placeholder: "请选择类型a",
          // 设置多选
          multiple: true
        }
      }
    },
    {
      label: "下拉过滤2",
      prop: "type2",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "select", // 或者el-select
        // 定义一个可响应的变量，可在接口返回后能更新变化注意结构[{label, value}]
        options: assetTypes,
        props: <ColumnMetaProps>{
          placeholder: "请选择类型2a",
          // 设置是否多选
          multiple: true
        }
      }
    },
    {
      label: "下拉树过滤",
      prop: "type2",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "tree", // 或者el-tree-select
        // 注意tree不使用options，而是使用props的data,请使用可响应数组
        props: <ColumnMetaProps>{
          placeholder: "请选择类型2a",
          // 设置是否多选
          multiple: true,
          data: treeData
        }
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: "180px",
      filterable: true,
      meta: {
        filterType: "date", // 或者el-date-picker
        // 请查看官网属性api
        props: <ColumnMetaProps>{
          startPlaceholder: "开始",
          endPlaceholder: "结束",
          type: "daterange"
        }
      }
    },
    {
      label: "计数",
      prop: "usage",
      width: "180px",
      filterable: true
    }
  ],
  userFactData: []
});

// 模拟分页查询接口
const handlePageCurrentChange = (page: number, pageSize: number) => {
  console.log("page pageSize", page, pageSize);
  state.loading = true;
  const total = state.pagination.total;
  const offset = (page - 1) * pageSize;
  const length = Math.min(offset + pageSize, total) - offset;
  setTimeout(() => {
    state.loading = false;
    state.userFactData = [
      {
        work_num: "",
        vulType: "加密问题",
        assetTypeName: "",
        dispatch_status: "未派单",
        deviceName: "",
        syscode: "深信服云镜系统",
        is_poc_validata: "0",
        orgCode: "",
        supplier: "",
        vulsuggest: "该漏洞仅仅是一个信息获取的漏洞，可以不做修复",
        id: 38394,
        refsys: "",
        vul_number_cve: "-",
        vuldetail: "检测到目标服务加密通信使用的SSL加密算法。",
        wlist_rule_flag: false,
        phyPosition: "",
        vulName: "加密问题",
        refDept: "",
        refOrgNameTree: "",
        vullevel: 2,
        ip: "*************",
        impact: "远程攻击者可以利用此漏洞收集信息，方便下一步的攻击行为",
        cnt: 1,
        is_poc_matching: "0",
        refOrgName: "",
        vul_id: "0462dfd1e35eb71d117d828f70976a31",
        macAddress: "",
        createTime: "2025-04-01 10:26:39",
        duty: "",
        status: "无需处理",
        lastUpdateTime: "2025-04-01 10:26:39"
      },
      {
        work_num: "",
        vulType: "其它",
        assetTypeName: "",
        dispatch_status: "未派单",
        deviceName: "",
        syscode: "深信服云镜系统",
        is_poc_validata: "0",
        orgCode: "",
        supplier: "",
        vulsuggest: "仅用来做信息收集，无影响",
        id: 38396,
        refsys: "",
        vul_number_cve: "-",
        vuldetail:
          "SSL证书是用于建立安全连接的数字证书，它使得数据在用户的计算机和服务器之间加密传输成为可能。SSL证书由几个主要部分组成，包括：公钥，私钥，证书颁发机构，证书主题，有效期等。通过SSL证书可以获取到目标使用的主机名。",
        wlist_rule_flag: false,
        phyPosition: "",
        vulName: "其它",
        refDept: "",
        refOrgNameTree: "",
        vullevel: 2,
        ip: "*************",
        impact: "远程攻击者可以通过此漏洞获取敏感信息",
        cnt: 1,
        is_poc_matching: "0",
        refOrgName: "",
        vul_id: "1ffc99e7089045812423fefbff3f5fc9",
        macAddress: "",
        createTime: "2025-04-01 10:26:39",
        duty: "",
        status: "无需处理",
        lastUpdateTime: "2025-04-01 10:26:39"
      }
    ];
  }, 500);
};

const resetQuery = () => {
  state.pagination.currentPage = 1;
  handlePageCurrentChange(1, state.pagination.pageSize);
};

const resetFilters = () => {
  table.value.clearAllFilters();
};

handlePageCurrentChange(1, 20);

// 模拟10秒后台响应后更新assetTypes
setTimeout(() => {
  assetTypes.splice(
    0,
    assetTypes.length,
    ...[
      { label: "类型1", value: "类型1" },
      { label: "类型2", value: "类型2" },
      { label: "类型3", value: "类型3" },
      { label: "类型4", value: "类型4" }
    ]
  );

  // 下拉树结构参考官网: https://cn.element-plus.org/zh-CN/component/tree-select.html
  treeData.splice(
    0,
    treeData.length,
    ...[
      {
        value: "1",
        label: "Level one 1",
        children: [
          {
            value: "1-1",
            label: "Level two 1-1",
            children: [
              {
                value: "1-1-1",
                label: "Level three 1-1-1"
              }
            ]
          }
        ]
      },
      {
        value: "2",
        label: "Level one 2",
        children: [
          {
            value: "2-1",
            label: "Level two 2-1",
            children: [
              {
                value: "2-1-1",
                label: "Level three 2-1-1"
              }
            ]
          },
          {
            value: "2-2",
            label: "Level two 2-2",
            children: [
              {
                value: "2-2-1",
                label: "Level three 2-2-1"
              }
            ]
          }
        ]
      },
      {
        value: "3",
        label: "Level one 3",
        children: [
          {
            value: "3-1",
            label: "Level two 3-1",
            children: [
              {
                value: "3-1-1",
                label: "Level three 3-1-1"
              }
            ]
          },
          {
            value: "3-2",
            label: "Level two 3-2",
            children: [
              {
                value: "3-2-1",
                label: "Level three 3-2-1"
              }
            ]
          }
        ]
      }
    ]
  );
}, 3000);

const vulnerabilityStatisticsData = [
  {
    label: "漏洞总数",
    code: "total",
    value: 0
  },
  {
    label: "未修复数",
    code: "undisposed",
    value: 0
  },
  {
    label: "已修复数",
    code: "disposalof",
    value: 0
  },
  {
    label: "无需处理数",
    code: "noNeedHandle",
    value: 0
  }
];

const filterDataProvider: TableFilterDataProvider = {
  // 方式1： 提供固定uri
  // uri: "/",
  // 方式2(优先级高)： 提供方法返回Promise，可以根据column灵活处理
  options: (prop, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      setTimeout(() => {
        resolve({
          total: 300,
          options: Array.from({ length: 1000 }, (_, index) => {
            return {
              label: "类型2_" + index,
              value: "v2_" + index,
              count: 180
            };
          })
        });
      }, 200);
    });
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    console.log("filters", filters);
  }
};

const handleColumnsStorage = columns => {
  console.log("on columns storage", columns);
};
</script>
