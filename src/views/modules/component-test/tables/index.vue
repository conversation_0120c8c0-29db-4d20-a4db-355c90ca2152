<template>
  <el-tabs>
    <el-tab-pane label="表格列存储">
      <column-storage-demo />
    </el-tab-pane>
    <el-tab-pane label="ImTable基础功能">
      <base-table />
    </el-tab-pane>
    <el-tab-pane label="ImTable过滤器案例">
      <filter-table-demo></filter-table-demo>
    </el-tab-pane>
    <el-tab-pane label="表头列拖拽排序">
      <column-sortable-demo />
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import BaseTable from "./BaseTable.vue";
import FilterTableDemo from "./FilterTableDemo.vue";
import ColumnSortableDemo from "./ColumnSortableDemo.vue";
import ColumnStorageDemo from "./ColumnStorageDemo.vue";
</script>
