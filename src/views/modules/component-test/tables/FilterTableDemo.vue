<template>
  <div class="mb-20">
    <p>例子包含: 文本框, 下拉框， 下拉树</p>

    <im-table
      ref="table"
      center
      :toolbar="{
        initialState: {
          aa: 2
        }
      }"
      table-alert
      :loading="state.loading"
      :data="state.userFactData"
      :columns="state.userFactColumns"
      :height="tableHeight"
      :pagination="state.pagination"
      :operator="state.operator"
      @on-reload="handlePageCurrentChange"
      @on-page-current-change="handlePageCurrentChange"
      @on-filter="handleFilter"
    >
      <template #toolbar-left="toolbarSlot: ImToolbarSlot">
        <el-radio-group v-model="toolbarSlot.toolbarState.type">
          <el-radio-button label="全部" :value="1"></el-radio-button>
          <el-radio-button label="高危" :value="2"></el-radio-button>
        </el-radio-group>
        <el-button type="danger" :disabled="toolbarSlot.checkedRows.length == 0"
          >批量删除</el-button
        >
      </template>
      <template #toolbar-right>
        <div class="flex-c gap-2">
          <el-select style="width: 150px" placeholder="请选择类型1">
          </el-select>
          <el-select style="width: 200px" placeholder="请选择类型2">
          </el-select>
          <el-button type="primary" @click="resetQuery">查询</el-button>
        </div>
      </template>
      <template #operator>
        <el-button type="primary">查看详情</el-button>
      </template>
    </im-table>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import {
  ColumnMetaProps,
  ImTableColumnProps,
  ImTableInstance,
  ImTablePaginationProps,
  ImToolbarSlot,
  IndexRender
} from "@/components/ItsmCommon";
import { ElMessage } from "element-plus";

const table = ref<ImTableInstance>();
const tableHeight = ref(400);
const assetTypes = reactive([]);
const treeData = reactive([]);
const state = reactive({
  loading: false,
  // 快捷设置所有的列居中
  center: false,
  pagination: <ImTablePaginationProps>{
    align: "right",
    total: 123,
    currentPage: 1,
    pageSize: 20,
    pageSizes: [20, 40, 60],
    props: {
      // 更多page属性请参考官网配置
    }
  },
  // 操作
  operator: <ImTableColumnProps<any>>{
    label: "操作"
  },
  userFactColumns: <ImTableColumnProps<any>[]>[
    {
      label: "选择",
      type: "selection",
      width: "60px"
    },
    {
      label: "序号",
      render: IndexRender,
      width: "80px"
    },
    {
      label: "编号",
      align: "left",
      prop: "id",
      width: "120px"
    },
    {
      label: "名称",
      prop: "name",
      width: "180px",
      filterable: true,
      meta: {
        filterType: "input" // 使用简单的文本框
      }
    },
    {
      label: "下拉过滤",
      prop: "type",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "select", // 或者el-select
        // 定义静态数组
        options: [
          { label: "a", value: "a" },
          { label: "b", value: "b" }
        ],
        props: <ColumnMetaProps>{
          placeholder: "请选择类型a",
          // 设置多选
          multiple: true
        }
      }
    },
    {
      label: "下拉过滤2",
      prop: "type2",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "select", // 或者el-select
        // 定义一个可响应的变量，可在接口返回后能更新变化注意结构[{label, value}]
        options: assetTypes,
        props: <ColumnMetaProps>{
          placeholder: "请选择类型2a",
          // 设置是否多选
          multiple: true
        }
      }
    },
    {
      label: "下拉树过滤",
      prop: "type2",
      width: "180px",
      filterable: true,
      meta: {
        // 设置类型为下拉
        filterType: "tree", // 或者el-tree-select
        // 注意tree不使用options，而是使用props的data,请使用可响应数组
        props: <ColumnMetaProps>{
          placeholder: "请选择类型2a",
          // 设置是否多选
          multiple: true,
          data: treeData
        }
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      width: "180px",
      filterable: true,
      meta: {
        filterType: "date", // 或者el-date-picker
        // 请查看官网属性api
        props: <ColumnMetaProps>{
          startPlaceholder: "开始",
          endPlaceholder: "结束",
          type: "daterange"
        }
      }
    },
    {
      label: "计数",
      prop: "usage",
      width: "180px",
      filterable: true
    }
  ],
  userFactData: []
});

// 模拟分页查询接口
const handlePageCurrentChange = (page: number, pageSize: number) => {
  console.log("page pageSize", page, pageSize);
  state.loading = true;
  const total = state.pagination.total;
  const offset = (page - 1) * pageSize;
  const length = Math.min(offset + pageSize, total) - offset;
  setTimeout(() => {
    state.loading = false;
    state.userFactData = Array.from(
      {
        length
      },
      (_, index) => {
        const xh = offset + index;
        return {
          id: xh + "",
          name: "测试名称-" + (index % 5),
          type: ["a", "b", "c"][index % 3],
          createTime: new Date(
            Date.now() + parseInt(Math.random() * 100000000)
          ).toLocaleDateString(),
          usage: Math.round(Math.random() * 10000)
        };
      }
    );
  }, 500);
};

const resetQuery = () => {
  state.pagination.currentPage = 1;
  handlePageCurrentChange(1, state.pagination.pageSize);
};

handlePageCurrentChange(1, 20);

// 模拟10秒后台响应后更新assetTypes
setTimeout(() => {
  assetTypes.splice(
    0,
    assetTypes.length,
    ...[
      { label: "类型1", value: "类型1" },
      { label: "类型2", value: "类型2" },
      { label: "类型3", value: "类型3" },
      { label: "类型4", value: "类型4" }
    ]
  );

  // 下拉树结构参考官网: https://cn.element-plus.org/zh-CN/component/tree-select.html
  treeData.splice(
    0,
    treeData.length,
    ...[
      {
        value: "1",
        label: "Level one 1",
        children: [
          {
            value: "1-1",
            label: "Level two 1-1",
            children: [
              {
                value: "1-1-1",
                label: "Level three 1-1-1"
              }
            ]
          }
        ]
      },
      {
        value: "2",
        label: "Level one 2",
        children: [
          {
            value: "2-1",
            label: "Level two 2-1",
            children: [
              {
                value: "2-1-1",
                label: "Level three 2-1-1"
              }
            ]
          },
          {
            value: "2-2",
            label: "Level two 2-2",
            children: [
              {
                value: "2-2-1",
                label: "Level three 2-2-1"
              }
            ]
          }
        ]
      },
      {
        value: "3",
        label: "Level one 3",
        children: [
          {
            value: "3-1",
            label: "Level two 3-1",
            children: [
              {
                value: "3-1-1",
                label: "Level three 3-1-1"
              }
            ]
          },
          {
            value: "3-2",
            label: "Level two 3-2",
            children: [
              {
                value: "3-2-1",
                label: "Level three 3-2-1"
              }
            ]
          }
        ]
      }
    ]
  );
}, 3000);

/**
 * 这里处理过滤操作
 *
 * @param params  {[prop]: value}
 * @param value   当前过滤选中的值
 * @param column  当前操作过滤的字段
 */
const handleFilter = (params, value, column: ImTableColumnProps<any>) => {
  state.pagination.currentPage = 1;
  console.log("handleFilter, params, value", params, value);
  ElMessage.info("当前过滤参数: " + JSON.stringify(params));
  handlePageCurrentChange(1, 10);
  // table.value.currentPage(1).reload();
};
</script>
