<template>
  <im-search-form
    ref="searchForm"
    class="search-form"
    select-width="210px"
    :columns="searchColumns"
    :col-num-per-row="1"
    :search-style="{ width: '70%' }"
  ></im-search-form>
</template>

<script setup lang="ts">
const searchColumns = [
  { field: "f1", name: "字段1", component: { type: "Input" } },
  { field: "f10", name: "字段10", component: { type: "Input" } },
  { field: "f11", name: "字段11", component: { type: "Input" } },
  { field: "f12", name: "字段12", component: { type: "Input" } },
  { field: "f13", name: "字段13", component: { type: "Input" } },
  { field: "f14", name: "字段14", component: { type: "Input" } },
  { field: "f15", name: "字段15", component: { type: "Input" } },
  { field: "f16", name: "字段16", component: { type: "Input" } },
  {
    field: "f2",
    name: "字段2",
    component: {
      type: "Select",
      options: [
        {
          label: "l1",
          value: "1"
        },
        {
          label: "l2",
          value: "2"
        }
      ]
    }
  },
  {
    field: "f3",
    name: "字段3",
    component: {
      type: "SelectTree",
      props: {
        multiple: true,
        data: [
          {
            value: "1",
            label: "Level one 1",
            children: [
              {
                value: "1-1",
                label: "Level two 1-1",
                children: [
                  {
                    value: "1-1-1",
                    label: "Level three 1-1-1"
                  }
                ]
              }
            ]
          },
          {
            value: "2",
            label: "Level one 2",
            children: [
              {
                value: "2-1",
                label: "Level two 2-1",
                children: [
                  {
                    value: "2-1-1",
                    label: "Level three 2-1-1"
                  }
                ]
              },
              {
                value: "2-2",
                label: "Level two 2-2",
                children: [
                  {
                    value: "2-2-1",
                    label: "Level three 2-2-1"
                  }
                ]
              }
            ]
          },
          {
            value: "3",
            label: "Level one 3",
            children: [
              {
                value: "3-1",
                label: "Level two 3-1",
                children: [
                  {
                    value: "3-1-1",
                    label: "Level three 3-1-1"
                  }
                ]
              },
              {
                value: "3-2",
                label: "Level two 3-2",
                children: [
                  {
                    value: "3-2-1",
                    label: "Level three 3-2-1"
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }
];
</script>

<style scoped lang="scss"></style>
