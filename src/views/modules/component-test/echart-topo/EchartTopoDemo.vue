<template>
  <div style="width: 100%; height: 750px">
    <ChartComponent :option="state.option" renderer="canvas"></ChartComponent>
  </div>
</template>
<script lang="ts" setup>
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { onMounted, reactive } from "vue";
import { EChartsOption } from "echarts";
const state = reactive({
  option: <EChartsOption>{
    xAxis: {
      type: "category",
      data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        data: [150, 230, 224, 218, 135, 147, 260],
        type: "line"
      }
    ]
  }
});

onMounted(() => {});
</script>
