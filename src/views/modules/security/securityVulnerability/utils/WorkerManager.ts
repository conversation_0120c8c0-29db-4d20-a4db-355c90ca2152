/**
 * Web Worker 管理器
 * 统一管理所有 Worker 实例，提供高级 API
 */

import { LRUCache } from './LRUCache';

// Worker 类型定义
export type WorkerType = 'table' | 'export' | 'stats';

// Worker 消息接口
interface WorkerMessage {
  id: string;
  type: string;
  payload: any;
}

interface WorkerResponse {
  id: string;
  type: string;
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
}

// Worker 任务接口
interface WorkerTask {
  id: string;
  type: WorkerType;
  message: WorkerMessage;
  resolve: (value: any) => void;
  reject: (error: Error) => void;
  timeout?: NodeJS.Timeout;
  startTime: number;
}

/**
 * 高性能 Worker 管理器
 * 支持任务队列、负载均衡、错误恢复等企业级特性
 */
export class WorkerManager {
  private workers = new Map<WorkerType, Worker[]>();
  private workerPaths = new Map<WorkerType, string>();
  private pendingTasks = new Map<string, WorkerTask>();
  private taskQueue = new Map<WorkerType, WorkerTask[]>();
  private workerStats = new Map<WorkerType, WorkerStats>();
  private resultCache = new LRUCache<string, any>(100);
  
  // 配置选项
  private options = {
    maxWorkers: navigator.hardwareConcurrency || 4,
    taskTimeout: 30000, // 30秒超时
    retryAttempts: 3,
    enableCache: true,
    enableStats: true
  };

  constructor(options?: Partial<typeof WorkerManager.prototype.options>) {
    if (options) {
      Object.assign(this.options, options);
    }
    
    this.initializeWorkerPaths();
    this.initializeStats();
  }

  /**
   * 初始化 Worker 路径
   */
  private initializeWorkerPaths(): void {
    this.workerPaths.set('table', '/src/views/modules/security/securityVulnerability/workers/TableDataWorker.ts');
    this.workerPaths.set('export', '/src/views/modules/security/securityVulnerability/workers/ExportWorker.ts');
    this.workerPaths.set('stats', '/src/views/modules/security/securityVulnerability/workers/StatsWorker.ts');
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    for (const type of this.workerPaths.keys()) {
      this.workerStats.set(type, {
        totalTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
        averageDuration: 0,
        totalDuration: 0,
        activeWorkers: 0
      });
    }
  }

  /**
   * 获取或创建 Worker
   */
  private async getWorker(type: WorkerType): Promise<Worker> {
    if (!this.workers.has(type)) {
      this.workers.set(type, []);
    }

    const workers = this.workers.get(type)!;
    const stats = this.workerStats.get(type)!;

    // 查找空闲的 Worker
    for (const worker of workers) {
      if (!this.isWorkerBusy(worker)) {
        return worker;
      }
    }

    // 如果没有空闲 Worker 且未达到最大数量，创建新的
    if (workers.length < this.options.maxWorkers) {
      const worker = await this.createWorker(type);
      workers.push(worker);
      stats.activeWorkers = workers.length;
      return worker;
    }

    // 返回负载最轻的 Worker
    return this.getLeastBusyWorker(workers);
  }

  /**
   * 创建新的 Worker
   */
  private async createWorker(type: WorkerType): Promise<Worker> {
    const workerPath = this.workerPaths.get(type);
    if (!workerPath) {
      throw new Error(`Unknown worker type: ${type}`);
    }

    const worker = new Worker(workerPath, { type: 'module' });
    
    // 设置消息处理器
    worker.onmessage = (event) => {
      this.handleWorkerMessage(event.data);
    };

    // 设置错误处理器
    worker.onerror = (error) => {
      console.error(`Worker error (${type}):`, error);
      this.handleWorkerError(type, error);
    };

    // 等待 Worker 初始化完成
    await this.waitForWorkerReady(worker);

    return worker;
  }

  /**
   * 等待 Worker 准备就绪
   */
  private waitForWorkerReady(worker: Worker): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Worker initialization timeout'));
      }, 5000);

      const messageHandler = (event: MessageEvent) => {
        if (event.data.type === 'WORKER_READY') {
          clearTimeout(timeout);
          worker.removeEventListener('message', messageHandler);
          resolve();
        }
      };

      worker.addEventListener('message', messageHandler);
    });
  }

  /**
   * 执行 Worker 任务
   */
  public async executeTask<T = any>(
    type: WorkerType,
    taskType: string,
    payload: any,
    options?: {
      timeout?: number;
      enableCache?: boolean;
      cacheKey?: string;
    }
  ): Promise<T> {
    const taskId = this.generateTaskId();
    const cacheKey = options?.cacheKey || this.generateCacheKey(type, taskType, payload);
    
    // 检查缓存
    if (options?.enableCache !== false && this.options.enableCache) {
      const cached = this.resultCache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const stats = this.workerStats.get(type)!;
    stats.totalTasks++;

    return new Promise(async (resolve, reject) => {
      try {
        const worker = await this.getWorker(type);
        
        const task: WorkerTask = {
          id: taskId,
          type,
          message: {
            id: taskId,
            type: taskType,
            payload
          },
          resolve: (result) => {
            // 缓存结果
            if (options?.enableCache !== false && this.options.enableCache) {
              this.resultCache.set(cacheKey, result);
            }
            resolve(result);
          },
          reject,
          startTime: Date.now()
        };

        // 设置超时
        const timeout = options?.timeout || this.options.taskTimeout;
        task.timeout = setTimeout(() => {
          this.handleTaskTimeout(task);
        }, timeout);

        // 记录任务
        this.pendingTasks.set(taskId, task);

        // 发送消息给 Worker
        worker.postMessage(task.message);

      } catch (error) {
        stats.failedTasks++;
        reject(error);
      }
    });
  }

  /**
   * 处理 Worker 消息
   */
  private handleWorkerMessage(response: WorkerResponse): void {
    const task = this.pendingTasks.get(response.id);
    if (!task) {
      return;
    }

    // 清理任务
    this.pendingTasks.delete(response.id);
    if (task.timeout) {
      clearTimeout(task.timeout);
    }

    // 更新统计信息
    const stats = this.workerStats.get(task.type)!;
    const duration = Date.now() - task.startTime;
    
    if (response.success) {
      stats.completedTasks++;
      stats.totalDuration += duration;
      stats.averageDuration = stats.totalDuration / stats.completedTasks;
      task.resolve(response.data);
    } else {
      stats.failedTasks++;
      task.reject(new Error(response.error || 'Worker task failed'));
    }
  }

  /**
   * 处理任务超时
   */
  private handleTaskTimeout(task: WorkerTask): void {
    this.pendingTasks.delete(task.id);
    
    const stats = this.workerStats.get(task.type)!;
    stats.failedTasks++;
    
    task.reject(new Error(`Task timeout after ${this.options.taskTimeout}ms`));
  }

  /**
   * 处理 Worker 错误
   */
  private handleWorkerError(type: WorkerType, error: ErrorEvent): void {
    console.error(`Worker error (${type}):`, error);
    
    // 重启 Worker
    this.restartWorkers(type);
  }

  /**
   * 重启指定类型的所有 Worker
   */
  private async restartWorkers(type: WorkerType): Promise<void> {
    const workers = this.workers.get(type) || [];
    
    // 终止现有 Worker
    workers.forEach(worker => worker.terminate());
    
    // 清空 Worker 列表
    this.workers.set(type, []);
    
    // 更新统计信息
    const stats = this.workerStats.get(type)!;
    stats.activeWorkers = 0;
  }

  /**
   * 获取统计信息
   */
  public getStats(): Map<WorkerType, WorkerStats> {
    return new Map(this.workerStats);
  }

  /**
   * 清理资源
   */
  public destroy(): void {
    // 终止所有 Worker
    for (const workers of this.workers.values()) {
      workers.forEach(worker => worker.terminate());
    }
    
    // 清理任务
    for (const task of this.pendingTasks.values()) {
      if (task.timeout) {
        clearTimeout(task.timeout);
      }
      task.reject(new Error('WorkerManager destroyed'));
    }
    
    // 清理缓存
    this.resultCache.clear();
    
    // 重置状态
    this.workers.clear();
    this.pendingTasks.clear();
    this.taskQueue.clear();
  }

  // 私有辅助方法
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCacheKey(type: WorkerType, taskType: string, payload: any): string {
    return `${type}_${taskType}_${JSON.stringify(payload)}`;
  }

  private isWorkerBusy(worker: Worker): boolean {
    // 简化实现：检查是否有待处理的任务
    for (const task of this.pendingTasks.values()) {
      // 这里需要更复杂的逻辑来跟踪 Worker 状态
    }
    return false;
  }

  private getLeastBusyWorker(workers: Worker[]): Worker {
    // 简化实现：返回第一个 Worker
    return workers[0];
  }
}

// Worker 统计信息接口
interface WorkerStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageDuration: number;
  totalDuration: number;
  activeWorkers: number;
}

// 单例模式 - 全局 Worker 管理器
export const workerManager = new WorkerManager();
