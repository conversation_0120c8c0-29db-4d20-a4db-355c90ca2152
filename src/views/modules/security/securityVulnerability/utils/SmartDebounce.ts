/**
 * 智能防抖算法
 * 实现自适应延迟的智能防抖，根据用户行为模式动态调整延迟时间
 */

interface DebounceOptions {
  delay: number;
  maxWait?: number;
  leading?: boolean;
  trailing?: boolean;
  adaptive?: boolean;
  minDelay?: number;
  maxDelay?: number;
}

interface UserBehaviorPattern {
  averageInterval: number;
  burstCount: number;
  lastCallTime: number;
  callHistory: number[];
  adaptiveDelay: number;
}

/**
 * 智能防抖类
 * 根据用户行为模式自动调整防抖延迟
 */
export class SmartDebounce<T extends (...args: any[]) => any> {
  private func: T;
  private options: Required<DebounceOptions>;
  private timerId: NodeJS.Timeout | null = null;
  private lastCallTime = 0;
  private lastInvokeTime = 0;
  private leadingInvoked = false;
  private behaviorPattern: UserBehaviorPattern;
  private readonly maxHistorySize = 20;

  constructor(func: T, options: DebounceOptions) {
    this.func = func;
    this.options = {
      delay: options.delay,
      maxWait: options.maxWait || options.delay * 10,
      leading: options.leading || false,
      trailing: options.trailing !== false,
      adaptive: options.adaptive || false,
      minDelay: options.minDelay || options.delay * 0.1,
      maxDelay: options.maxDelay || options.delay * 5
    };

    this.behaviorPattern = {
      averageInterval: options.delay,
      burstCount: 0,
      lastCallTime: 0,
      callHistory: [],
      adaptiveDelay: options.delay
    };
  }

  /**
   * 防抖函数调用
   */
  public invoke = (...args: Parameters<T>): Promise<ReturnType<T>> => {
    const now = Date.now();
    const isInvoking = this.shouldInvoke(now);

    this.lastCallTime = now;
    this.updateBehaviorPattern(now);

    return new Promise((resolve, reject) => {
      const executeFunction = () => {
        try {
          const result = this.func.apply(this, args);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      if (isInvoking) {
        if (this.timerId) {
          clearTimeout(this.timerId);
          this.timerId = null;
        }

        if (this.options.leading && !this.leadingInvoked) {
          this.lastInvokeTime = now;
          this.leadingInvoked = true;
          executeFunction();
          return;
        }
      }

      this.leadingInvoked = false;

      if (this.timerId) {
        clearTimeout(this.timerId);
      }

      const delay = this.calculateDelay();
      
      this.timerId = setTimeout(() => {
        this.timerId = null;
        this.lastInvokeTime = Date.now();
        
        if (this.options.trailing) {
          executeFunction();
        }
      }, delay);
    });
  };

  /**
   * 立即执行并清除定时器
   */
  public flush(): ReturnType<T> | undefined {
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
      this.lastInvokeTime = Date.now();
      return this.func();
    }
    return undefined;
  }

  /**
   * 取消防抖
   */
  public cancel(): void {
    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
    this.lastCallTime = 0;
    this.lastInvokeTime = 0;
    this.leadingInvoked = false;
  }

  /**
   * 获取当前状态
   */
  public getState() {
    return {
      isPending: this.timerId !== null,
      lastCallTime: this.lastCallTime,
      lastInvokeTime: this.lastInvokeTime,
      behaviorPattern: { ...this.behaviorPattern },
      currentDelay: this.calculateDelay()
    };
  }

  /**
   * 更新配置
   */
  public updateOptions(newOptions: Partial<DebounceOptions>): void {
    Object.assign(this.options, newOptions);
  }

  // 私有方法

  private shouldInvoke(now: number): boolean {
    const timeSinceLastCall = now - this.lastCallTime;
    const timeSinceLastInvoke = now - this.lastInvokeTime;

    // 首次调用
    if (this.lastCallTime === 0) {
      return true;
    }

    // 超过最大等待时间
    if (this.options.maxWait && timeSinceLastInvoke >= this.options.maxWait) {
      return true;
    }

    // 超过防抖延迟时间
    if (timeSinceLastCall >= this.calculateDelay()) {
      return true;
    }

    return false;
  }

  private updateBehaviorPattern(now: number): void {
    if (!this.options.adaptive) {
      return;
    }

    const { behaviorPattern } = this;
    
    // 记录调用时间
    if (behaviorPattern.lastCallTime > 0) {
      const interval = now - behaviorPattern.lastCallTime;
      behaviorPattern.callHistory.push(interval);
      
      // 限制历史记录大小
      if (behaviorPattern.callHistory.length > this.maxHistorySize) {
        behaviorPattern.callHistory.shift();
      }
      
      // 计算平均间隔
      behaviorPattern.averageInterval = 
        behaviorPattern.callHistory.reduce((sum, interval) => sum + interval, 0) / 
        behaviorPattern.callHistory.length;
    }

    // 检测突发调用
    if (behaviorPattern.lastCallTime > 0 && now - behaviorPattern.lastCallTime < 100) {
      behaviorPattern.burstCount++;
    } else {
      behaviorPattern.burstCount = Math.max(0, behaviorPattern.burstCount - 1);
    }

    behaviorPattern.lastCallTime = now;
    
    // 计算自适应延迟
    this.calculateAdaptiveDelay();
  }

  private calculateAdaptiveDelay(): void {
    if (!this.options.adaptive) {
      return;
    }

    const { behaviorPattern, options } = this;
    let adaptiveDelay = options.delay;

    // 基于平均间隔调整
    if (behaviorPattern.callHistory.length >= 3) {
      const avgInterval = behaviorPattern.averageInterval;
      
      if (avgInterval < options.delay * 0.5) {
        // 用户输入很快，增加延迟
        adaptiveDelay = Math.min(options.maxDelay, options.delay * 1.5);
      } else if (avgInterval > options.delay * 2) {
        // 用户输入较慢，减少延迟
        adaptiveDelay = Math.max(options.minDelay, options.delay * 0.7);
      }
    }

    // 基于突发调用调整
    if (behaviorPattern.burstCount > 5) {
      // 检测到突发调用，增加延迟
      adaptiveDelay = Math.min(options.maxDelay, adaptiveDelay * 1.3);
    } else if (behaviorPattern.burstCount === 0) {
      // 没有突发调用，可以减少延迟
      adaptiveDelay = Math.max(options.minDelay, adaptiveDelay * 0.9);
    }

    behaviorPattern.adaptiveDelay = adaptiveDelay;
  }

  private calculateDelay(): number {
    if (this.options.adaptive) {
      return this.behaviorPattern.adaptiveDelay;
    }
    return this.options.delay;
  }
}

/**
 * 创建智能防抖函数
 */
export function smartDebounce<T extends (...args: any[]) => any>(
  func: T,
  options: DebounceOptions
): SmartDebounce<T> {
  return new SmartDebounce(func, options);
}

/**
 * 防抖函数工厂
 * 提供不同场景的预设配置
 */
export class DebounceFactory {
  /**
   * 搜索输入防抖
   */
  static forSearch<T extends (...args: any[]) => any>(func: T, delay = 300) {
    return smartDebounce(func, {
      delay,
      adaptive: true,
      minDelay: 150,
      maxDelay: 800,
      trailing: true
    });
  }

  /**
   * API请求防抖
   */
  static forApiCall<T extends (...args: any[]) => any>(func: T, delay = 500) {
    return smartDebounce(func, {
      delay,
      maxWait: 2000,
      adaptive: true,
      minDelay: 200,
      maxDelay: 1500,
      trailing: true
    });
  }

  /**
   * 窗口大小调整防抖
   */
  static forResize<T extends (...args: any[]) => any>(func: T, delay = 250) {
    return smartDebounce(func, {
      delay,
      maxWait: 1000,
      trailing: true,
      leading: false
    });
  }

  /**
   * 滚动事件防抖
   */
  static forScroll<T extends (...args: any[]) => any>(func: T, delay = 100) {
    return smartDebounce(func, {
      delay,
      maxWait: 300,
      trailing: true,
      leading: true
    });
  }

  /**
   * 表单验证防抖
   */
  static forValidation<T extends (...args: any[]) => any>(func: T, delay = 400) {
    return smartDebounce(func, {
      delay,
      adaptive: true,
      minDelay: 200,
      maxDelay: 1000,
      trailing: true
    });
  }

  /**
   * 自动保存防抖
   */
  static forAutoSave<T extends (...args: any[]) => any>(func: T, delay = 2000) {
    return smartDebounce(func, {
      delay,
      maxWait: 10000,
      trailing: true,
      leading: false
    });
  }
}

/**
 * 节流函数
 * 确保函数在指定时间间隔内最多执行一次
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): (...args: Parameters<T>) => void {
  let lastExecTime = 0;
  let timerId: NodeJS.Timeout | null = null;
  const { leading = true, trailing = true } = options;

  return function (...args: Parameters<T>) {
    const now = Date.now();
    const timeSinceLastExec = now - lastExecTime;

    const execute = () => {
      lastExecTime = Date.now();
      func.apply(this, args);
    };

    if (leading && timeSinceLastExec >= delay) {
      execute();
    } else if (trailing) {
      if (timerId) {
        clearTimeout(timerId);
      }
      
      timerId = setTimeout(() => {
        timerId = null;
        if (Date.now() - lastExecTime >= delay) {
          execute();
        }
      }, delay - timeSinceLastExec);
    }
  };
}
