/**
 * 高性能事件发布订阅系统
 * 实现观察者模式，优化组件间通信
 */

type EventHandler<T = any> = (data: T) => void | Promise<void>;
type EventHandlerWithPriority<T = any> = {
  handler: EventHandler<T>;
  priority: number;
  once: boolean;
  context?: any;
};

interface EventStats {
  totalEvents: number;
  totalListeners: number;
  eventCounts: Map<string, number>;
}

/**
 * 企业级事件发射器
 * 支持优先级、一次性监听、错误处理、性能监控等特性
 */
export class EventEmitter {
  private events = new Map<string, EventHandlerWithPriority[]>();
  private maxListeners = 100;
  private stats: EventStats = {
    totalEvents: 0,
    totalListeners: 0,
    eventCounts: new Map()
  };
  private errorHandler?: (error: Error, event: string, data: any) => void;
  private middleware: Array<(event: string, data: any, next: () => void) => void> = [];

  /**
   * 添加事件监听器
   */
  public on<T = any>(
    event: string,
    handler: EventHandler<T>,
    options: {
      priority?: number;
      context?: any;
    } = {}
  ): () => void {
    return this.addListener(event, handler, {
      priority: options.priority || 0,
      once: false,
      context: options.context
    });
  }

  /**
   * 添加一次性事件监听器
   */
  public once<T = any>(
    event: string,
    handler: EventHandler<T>,
    options: {
      priority?: number;
      context?: any;
    } = {}
  ): () => void {
    return this.addListener(event, handler, {
      priority: options.priority || 0,
      once: true,
      context: options.context
    });
  }

  /**
   * 移除事件监听器
   */
  public off<T = any>(event: string, handler?: EventHandler<T>): void {
    const listeners = this.events.get(event);
    if (!listeners) return;

    if (!handler) {
      // 移除所有监听器
      this.events.delete(event);
      this.stats.totalListeners -= listeners.length;
    } else {
      // 移除特定监听器
      const index = listeners.findIndex(l => l.handler === handler);
      if (index !== -1) {
        listeners.splice(index, 1);
        this.stats.totalListeners--;
        
        if (listeners.length === 0) {
          this.events.delete(event);
        }
      }
    }
  }

  /**
   * 发射事件
   */
  public async emit<T = any>(event: string, data?: T): Promise<void> {
    this.stats.totalEvents++;
    this.stats.eventCounts.set(event, (this.stats.eventCounts.get(event) || 0) + 1);

    // 执行中间件
    await this.executeMiddleware(event, data);

    const listeners = this.events.get(event);
    if (!listeners || listeners.length === 0) {
      return;
    }

    // 按优先级排序（高优先级先执行）
    const sortedListeners = [...listeners].sort((a, b) => b.priority - a.priority);

    // 并行执行所有监听器
    const promises = sortedListeners.map(async (listenerInfo) => {
      try {
        const { handler, once, context } = listenerInfo;
        
        // 绑定上下文
        const boundHandler = context ? handler.bind(context) : handler;
        
        // 执行处理器
        await boundHandler(data);
        
        // 如果是一次性监听器，执行后移除
        if (once) {
          this.off(event, handler);
        }
      } catch (error) {
        this.handleError(error as Error, event, data);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 同步发射事件
   */
  public emitSync<T = any>(event: string, data?: T): void {
    this.stats.totalEvents++;
    this.stats.eventCounts.set(event, (this.stats.eventCounts.get(event) || 0) + 1);

    const listeners = this.events.get(event);
    if (!listeners || listeners.length === 0) {
      return;
    }

    // 按优先级排序
    const sortedListeners = [...listeners].sort((a, b) => b.priority - a.priority);

    for (const listenerInfo of sortedListeners) {
      try {
        const { handler, once, context } = listenerInfo;
        
        // 绑定上下文
        const boundHandler = context ? handler.bind(context) : handler;
        
        // 同步执行
        const result = boundHandler(data);
        
        // 如果返回 Promise，警告用户应该使用 emit
        if (result instanceof Promise) {
          console.warn(`Event handler for "${event}" returned a Promise. Consider using emit() instead of emitSync().`);
        }
        
        // 如果是一次性监听器，执行后移除
        if (once) {
          this.off(event, handler);
        }
      } catch (error) {
        this.handleError(error as Error, event, data);
      }
    }
  }

  /**
   * 添加中间件
   */
  public use(middleware: (event: string, data: any, next: () => void) => void): void {
    this.middleware.push(middleware);
  }

  /**
   * 设置错误处理器
   */
  public onError(handler: (error: Error, event: string, data: any) => void): void {
    this.errorHandler = handler;
  }

  /**
   * 设置最大监听器数量
   */
  public setMaxListeners(max: number): void {
    this.maxListeners = max;
  }

  /**
   * 获取事件的监听器数量
   */
  public listenerCount(event: string): number {
    const listeners = this.events.get(event);
    return listeners ? listeners.length : 0;
  }

  /**
   * 获取所有事件名称
   */
  public eventNames(): string[] {
    return Array.from(this.events.keys());
  }

  /**
   * 获取统计信息
   */
  public getStats(): EventStats {
    return {
      ...this.stats,
      eventCounts: new Map(this.stats.eventCounts)
    };
  }

  /**
   * 清除所有监听器
   */
  public removeAllListeners(event?: string): void {
    if (event) {
      const listeners = this.events.get(event);
      if (listeners) {
        this.stats.totalListeners -= listeners.length;
        this.events.delete(event);
      }
    } else {
      this.stats.totalListeners = 0;
      this.events.clear();
    }
  }

  /**
   * 等待事件发生
   */
  public waitFor<T = any>(
    event: string,
    timeout?: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      let timeoutId: NodeJS.Timeout | undefined;
      
      const cleanup = this.once(event, (data: T) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve(data);
      });

      if (timeout) {
        timeoutId = setTimeout(() => {
          cleanup();
          reject(new Error(`Event "${event}" timeout after ${timeout}ms`));
        }, timeout);
      }
    });
  }

  /**
   * 创建命名空间
   */
  public namespace(prefix: string): EventEmitter {
    const namespacedEmitter = new EventEmitter();
    
    // 代理所有方法，添加前缀
    const originalOn = namespacedEmitter.on.bind(namespacedEmitter);
    const originalEmit = namespacedEmitter.emit.bind(namespacedEmitter);
    
    namespacedEmitter.on = (event: string, handler: EventHandler, options?: any) => {
      return this.on(`${prefix}:${event}`, handler, options);
    };
    
    namespacedEmitter.emit = (event: string, data?: any) => {
      return this.emit(`${prefix}:${event}`, data);
    };
    
    return namespacedEmitter;
  }

  /**
   * 销毁事件发射器
   */
  public destroy(): void {
    this.removeAllListeners();
    this.middleware = [];
    this.errorHandler = undefined;
    this.stats = {
      totalEvents: 0,
      totalListeners: 0,
      eventCounts: new Map()
    };
  }

  // 私有方法

  private addListener<T = any>(
    event: string,
    handler: EventHandler<T>,
    options: {
      priority: number;
      once: boolean;
      context?: any;
    }
  ): () => void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    const listeners = this.events.get(event)!;
    
    // 检查监听器数量限制
    if (listeners.length >= this.maxListeners) {
      console.warn(`Event "${event}" has reached the maximum number of listeners (${this.maxListeners})`);
    }

    const listenerInfo: EventHandlerWithPriority<T> = {
      handler,
      priority: options.priority,
      once: options.once,
      context: options.context
    };

    listeners.push(listenerInfo);
    this.stats.totalListeners++;

    // 返回取消订阅函数
    return () => this.off(event, handler);
  }

  private async executeMiddleware(event: string, data: any): Promise<void> {
    let index = 0;
    
    const next = async (): Promise<void> => {
      if (index >= this.middleware.length) {
        return;
      }
      
      const middleware = this.middleware[index++];
      await new Promise<void>((resolve) => {
        middleware(event, data, () => {
          resolve();
        });
      });
      
      await next();
    };
    
    await next();
  }

  private handleError(error: Error, event: string, data: any): void {
    if (this.errorHandler) {
      try {
        this.errorHandler(error, event, data);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
        console.error('Original error:', error);
      }
    } else {
      console.error(`Error in event handler for "${event}":`, error);
    }
  }
}
