/**
 * LRU (Least Recently Used) 缓存算法实现
 * 智能缓存管理，自动淘汰最少使用的数据
 */

interface CacheNode<K, V> {
  key: K;
  value: V;
  prev: CacheNode<K, V> | null;
  next: CacheNode<K, V> | null;
  timestamp: number;
  accessCount: number;
  size: number; // 数据大小估算
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalSize: number;
  maxSize: number;
}

/**
 * 高性能 LRU 缓存实现
 * 支持容量限制、TTL、统计信息等企业级特性
 */
export class LRUCache<K, V> {
  private capacity: number;
  private maxMemorySize: number; // 最大内存占用（字节）
  private ttl: number; // 生存时间（毫秒）
  private cache = new Map<K, CacheNode<K, V>>();
  private head: CacheNode<K, V>;
  private tail: CacheNode<K, V>;
  private stats: CacheStats;
  private cleanupTimer: NodeJS.Timeout | null = null;

  constructor(
    capacity: number = 100,
    options: {
      maxMemorySize?: number;
      ttl?: number;
      autoCleanup?: boolean;
    } = {}
  ) {
    this.capacity = capacity;
    this.maxMemorySize = options.maxMemorySize || 50 * 1024 * 1024; // 默认50MB
    this.ttl = options.ttl || 30 * 60 * 1000; // 默认30分钟

    // 创建虚拟头尾节点
    this.head = this.createNode(null as any, null as any);
    this.tail = this.createNode(null as any, null as any);
    this.head.next = this.tail;
    this.tail.prev = this.head;

    // 初始化统计信息
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalSize: 0,
      maxSize: this.maxMemorySize
    };

    // 启动自动清理
    if (options.autoCleanup !== false) {
      this.startAutoCleanup();
    }
  }

  /**
   * 获取缓存值
   */
  public get(key: K): V | undefined {
    const node = this.cache.get(key);
    
    if (!node) {
      this.stats.misses++;
      return undefined;
    }

    // 检查是否过期
    if (this.isExpired(node)) {
      this.delete(key);
      this.stats.misses++;
      return undefined;
    }

    // 更新访问信息
    node.accessCount++;
    node.timestamp = Date.now();
    
    // 移动到头部（最近使用）
    this.moveToHead(node);
    
    this.stats.hits++;
    return node.value;
  }

  /**
   * 设置缓存值
   */
  public set(key: K, value: V): void {
    const existingNode = this.cache.get(key);
    
    if (existingNode) {
      // 更新现有节点
      const oldSize = existingNode.size;
      existingNode.value = value;
      existingNode.timestamp = Date.now();
      existingNode.accessCount++;
      existingNode.size = this.estimateSize(value);
      
      this.stats.totalSize += existingNode.size - oldSize;
      this.moveToHead(existingNode);
    } else {
      // 创建新节点
      const newNode = this.createNode(key, value);
      
      // 检查容量限制
      this.ensureCapacity();
      
      // 添加到缓存
      this.cache.set(key, newNode);
      this.addToHead(newNode);
      this.stats.totalSize += newNode.size;
    }

    // 检查内存限制
    this.ensureMemoryLimit();
  }

  /**
   * 删除缓存项
   */
  public delete(key: K): boolean {
    const node = this.cache.get(key);
    
    if (!node) {
      return false;
    }

    this.cache.delete(key);
    this.removeNode(node);
    this.stats.totalSize -= node.size;
    
    return true;
  }

  /**
   * 检查是否存在
   */
  public has(key: K): boolean {
    const node = this.cache.get(key);
    return node !== undefined && !this.isExpired(node);
  }

  /**
   * 清空缓存
   */
  public clear(): void {
    this.cache.clear();
    this.head.next = this.tail;
    this.tail.prev = this.head;
    this.stats.totalSize = 0;
    this.stats.evictions = 0;
  }

  /**
   * 获取缓存大小
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * 获取统计信息
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 获取命中率
   */
  public getHitRate(): number {
    const total = this.stats.hits + this.stats.misses;
    return total === 0 ? 0 : this.stats.hits / total;
  }

  /**
   * 获取所有键
   */
  public keys(): K[] {
    const keys: K[] = [];
    let current = this.head.next;
    
    while (current && current !== this.tail) {
      if (!this.isExpired(current)) {
        keys.push(current.key);
      }
      current = current.next;
    }
    
    return keys;
  }

  /**
   * 预热缓存
   */
  public async warmup(dataLoader: (key: K) => Promise<V>, keys: K[]): Promise<void> {
    const promises = keys.map(async (key) => {
      try {
        const value = await dataLoader(key);
        this.set(key, value);
      } catch (error) {
        console.warn(`Failed to warmup cache for key ${key}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 批量获取
   */
  public getBatch(keys: K[]): Map<K, V> {
    const result = new Map<K, V>();
    
    for (const key of keys) {
      const value = this.get(key);
      if (value !== undefined) {
        result.set(key, value);
      }
    }
    
    return result;
  }

  /**
   * 批量设置
   */
  public setBatch(entries: Map<K, V>): void {
    for (const [key, value] of entries) {
      this.set(key, value);
    }
  }

  /**
   * 销毁缓存
   */
  public destroy(): void {
    this.clear();
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
  }

  // 私有方法

  private createNode(key: K, value: V): CacheNode<K, V> {
    return {
      key,
      value,
      prev: null,
      next: null,
      timestamp: Date.now(),
      accessCount: 1,
      size: this.estimateSize(value)
    };
  }

  private addToHead(node: CacheNode<K, V>): void {
    node.prev = this.head;
    node.next = this.head.next;
    
    if (this.head.next) {
      this.head.next.prev = node;
    }
    this.head.next = node;
  }

  private removeNode(node: CacheNode<K, V>): void {
    if (node.prev) {
      node.prev.next = node.next;
    }
    if (node.next) {
      node.next.prev = node.prev;
    }
  }

  private moveToHead(node: CacheNode<K, V>): void {
    this.removeNode(node);
    this.addToHead(node);
  }

  private removeTail(): CacheNode<K, V> | null {
    const lastNode = this.tail.prev;
    if (lastNode && lastNode !== this.head) {
      this.removeNode(lastNode);
      return lastNode;
    }
    return null;
  }

  private ensureCapacity(): void {
    while (this.cache.size >= this.capacity) {
      const tail = this.removeTail();
      if (tail) {
        this.cache.delete(tail.key);
        this.stats.totalSize -= tail.size;
        this.stats.evictions++;
      } else {
        break;
      }
    }
  }

  private ensureMemoryLimit(): void {
    while (this.stats.totalSize > this.maxMemorySize) {
      const tail = this.removeTail();
      if (tail) {
        this.cache.delete(tail.key);
        this.stats.totalSize -= tail.size;
        this.stats.evictions++;
      } else {
        break;
      }
    }
  }

  private isExpired(node: CacheNode<K, V>): boolean {
    return Date.now() - node.timestamp > this.ttl;
  }

  private estimateSize(value: V): number {
    try {
      return JSON.stringify(value).length * 2; // 粗略估算（UTF-16）
    } catch {
      return 1024; // 默认1KB
    }
  }

  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }

  private cleanup(): void {
    const keysToDelete: K[] = [];
    
    for (const [key, node] of this.cache) {
      if (this.isExpired(node)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.delete(key);
    }
  }
}
