/**
 * 工厂模式 - API请求统一创建和管理
 * 提供统一的API请求接口，支持缓存、重试、错误处理等特性
 */

import { LRUCache } from '../utils/LRUCache';

// API请求配置接口
export interface ApiConfig {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  timeout?: number;
  retries?: number;
  cache?: boolean;
  cacheTTL?: number;
  headers?: Record<string, string>;
  transformRequest?: (data: any) => any;
  transformResponse?: (data: any) => any;
  validateStatus?: (status: number) => boolean;
}

// API响应接口
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: ApiConfig;
  cached?: boolean;
  duration?: number;
}

// API错误接口
export interface ApiError extends Error {
  config: ApiConfig;
  status?: number;
  statusText?: string;
  response?: any;
  isTimeout?: boolean;
  isNetworkError?: boolean;
}

// 请求拦截器类型
export type RequestInterceptor = (config: ApiConfig) => ApiConfig | Promise<ApiConfig>;

// 响应拦截器类型
export type ResponseInterceptor = (response: ApiResponse) => ApiResponse | Promise<ApiResponse>;

// 错误拦截器类型
export type ErrorInterceptor = (error: ApiError) => Promise<never> | Promise<ApiResponse>;

/**
 * API请求客户端
 * 提供统一的HTTP请求接口
 */
class ApiClient {
  private cache = new LRUCache<string, any>(100, { ttl: 5 * 60 * 1000 });
  private requestInterceptors: RequestInterceptor[] = [];
  private responseInterceptors: ResponseInterceptor[] = [];
  private errorInterceptors: ErrorInterceptor[] = [];
  private defaultConfig: Partial<ApiConfig> = {
    timeout: 30000,
    retries: 3,
    cache: true,
    cacheTTL: 5 * 60 * 1000,
    validateStatus: (status) => status >= 200 && status < 300
  };

  /**
   * 发送请求
   */
  public async request<T = any>(config: ApiConfig): Promise<ApiResponse<T>> {
    const startTime = Date.now();
    
    try {
      // 合并默认配置
      const finalConfig = { ...this.defaultConfig, ...config };
      
      // 执行请求拦截器
      const interceptedConfig = await this.executeRequestInterceptors(finalConfig);
      
      // 检查缓存
      if (interceptedConfig.cache && interceptedConfig.method === 'GET') {
        const cacheKey = this.generateCacheKey(interceptedConfig);
        const cachedResponse = this.cache.get(cacheKey);
        
        if (cachedResponse) {
          return {
            ...cachedResponse,
            cached: true,
            duration: Date.now() - startTime
          };
        }
      }
      
      // 发送请求
      const response = await this.sendRequest<T>(interceptedConfig);
      
      // 执行响应拦截器
      const interceptedResponse = await this.executeResponseInterceptors(response);
      
      // 缓存响应
      if (interceptedConfig.cache && interceptedConfig.method === 'GET') {
        const cacheKey = this.generateCacheKey(interceptedConfig);
        this.cache.set(cacheKey, {
          ...interceptedResponse,
          cached: false
        });
      }
      
      return {
        ...interceptedResponse,
        duration: Date.now() - startTime
      };
      
    } catch (error) {
      // 执行错误拦截器
      return await this.executeErrorInterceptors(error as ApiError);
    }
  }

  /**
   * GET请求
   */
  public get<T = any>(url: string, config?: Partial<ApiConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'GET' });
  }

  /**
   * POST请求
   */
  public post<T = any>(url: string, data?: any, config?: Partial<ApiConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'POST', data });
  }

  /**
   * PUT请求
   */
  public put<T = any>(url: string, data?: any, config?: Partial<ApiConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'PUT', data });
  }

  /**
   * DELETE请求
   */
  public delete<T = any>(url: string, config?: Partial<ApiConfig>): Promise<ApiResponse<T>> {
    return this.request<T>({ ...config, url, method: 'DELETE' });
  }

  /**
   * 添加请求拦截器
   */
  public addRequestInterceptor(interceptor: RequestInterceptor): void {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * 添加响应拦截器
   */
  public addResponseInterceptor(interceptor: ResponseInterceptor): void {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * 添加错误拦截器
   */
  public addErrorInterceptor(interceptor: ErrorInterceptor): void {
    this.errorInterceptors.push(interceptor);
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取缓存统计
   */
  public getCacheStats() {
    return this.cache.getStats();
  }

  // 私有方法

  private async sendRequest<T>(config: ApiConfig): Promise<ApiResponse<T>> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout);

    try {
      // 转换请求数据
      const requestData = config.transformRequest 
        ? config.transformRequest(config.data)
        : config.data;

      // 构建请求选项
      const fetchOptions: RequestInit = {
        method: config.method,
        headers: {
          'Content-Type': 'application/json',
          ...config.headers
        },
        signal: controller.signal,
        body: requestData ? JSON.stringify(requestData) : undefined
      };

      // 发送请求
      const response = await fetch(config.url, fetchOptions);
      
      clearTimeout(timeoutId);

      // 检查状态码
      if (!config.validateStatus!(response.status)) {
        throw new Error(`Request failed with status ${response.status}`);
      }

      // 解析响应
      const responseData = await response.json();
      
      // 转换响应数据
      const transformedData = config.transformResponse
        ? config.transformResponse(responseData)
        : responseData;

      return {
        data: transformedData,
        status: response.status,
        statusText: response.statusText,
        headers: this.parseHeaders(response.headers),
        config
      };

    } catch (error) {
      clearTimeout(timeoutId);
      
      const apiError: ApiError = error as ApiError;
      apiError.config = config;
      
      if (error.name === 'AbortError') {
        apiError.isTimeout = true;
      }
      
      throw apiError;
    }
  }

  private async executeRequestInterceptors(config: ApiConfig): Promise<ApiConfig> {
    let result = config;
    
    for (const interceptor of this.requestInterceptors) {
      result = await interceptor(result);
    }
    
    return result;
  }

  private async executeResponseInterceptors(response: ApiResponse): Promise<ApiResponse> {
    let result = response;
    
    for (const interceptor of this.responseInterceptors) {
      result = await interceptor(result);
    }
    
    return result;
  }

  private async executeErrorInterceptors(error: ApiError): Promise<ApiResponse> {
    for (const interceptor of this.errorInterceptors) {
      try {
        return await interceptor(error);
      } catch (interceptorError) {
        // 继续执行下一个拦截器
        continue;
      }
    }
    
    throw error;
  }

  private generateCacheKey(config: ApiConfig): string {
    const key = {
      url: config.url,
      method: config.method,
      data: config.data,
      headers: config.headers
    };
    
    return JSON.stringify(key);
  }

  private parseHeaders(headers: Headers): Record<string, string> {
    const result: Record<string, string> = {};
    
    headers.forEach((value, key) => {
      result[key] = value;
    });
    
    return result;
  }
}

/**
 * API工厂类
 * 创建和管理不同类型的API客户端
 */
export class ApiFactory {
  private static clients = new Map<string, ApiClient>();
  
  /**
   * 创建API客户端
   */
  public static createClient(name: string, baseConfig?: Partial<ApiConfig>): ApiClient {
    if (this.clients.has(name)) {
      return this.clients.get(name)!;
    }
    
    const client = new ApiClient();
    
    // 添加基础配置拦截器
    if (baseConfig) {
      client.addRequestInterceptor((config) => ({
        ...baseConfig,
        ...config,
        headers: {
          ...baseConfig.headers,
          ...config.headers
        }
      }));
    }
    
    this.clients.set(name, client);
    return client;
  }
  
  /**
   * 获取API客户端
   */
  public static getClient(name: string): ApiClient | undefined {
    return this.clients.get(name);
  }
  
  /**
   * 移除API客户端
   */
  public static removeClient(name: string): boolean {
    return this.clients.delete(name);
  }
  
  /**
   * 清除所有客户端
   */
  public static clearClients(): void {
    this.clients.clear();
  }
}

// 预定义的API客户端
export const vulnerabilityApiClient = ApiFactory.createClient('vulnerability', {
  headers: {
    'X-Requested-With': 'XMLHttpRequest'
  },
  timeout: 30000,
  retries: 3
});

// 添加通用拦截器
vulnerabilityApiClient.addRequestInterceptor((config) => {
  // 添加认证头
  const token = localStorage.getItem('token');
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    };
  }
  
  return config;
});

vulnerabilityApiClient.addResponseInterceptor((response) => {
  // 统一处理响应格式
  if (response.data && typeof response.data === 'object') {
    if (response.data.code === 0 || response.data.status === '0') {
      response.data = response.data.data || response.data;
    } else {
      throw new Error(response.data.message || response.data.msg || '请求失败');
    }
  }
  
  return response;
});

vulnerabilityApiClient.addErrorInterceptor(async (error) => {
  // 处理认证错误
  if (error.status === 401) {
    // 清除token并跳转到登录页
    localStorage.removeItem('token');
    window.location.href = '/login';
  }
  
  // 处理网络错误
  if (!error.status) {
    error.isNetworkError = true;
    error.message = '网络连接失败，请检查网络设置';
  }
  
  throw error;
});
