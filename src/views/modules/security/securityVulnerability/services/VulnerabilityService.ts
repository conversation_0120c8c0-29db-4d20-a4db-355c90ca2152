/**
 * 漏洞管理业务逻辑层
 * 实现分层架构，分离业务逻辑和数据访问
 */

import { vulnerabilityStore, ActionType } from '../store/VulnerabilityStore';
import { filterStrategyManager } from '../strategies/FilterStrategy';
import { vulnerabilityApiClient } from './ApiFactory';
import { QueryDecorator, withEnhancements } from '../decorators/QueryDecorators';
import { DebounceFactory } from '../utils/SmartDebounce';

// 业务实体接口
export interface VulnerabilityItem {
  vulId: string;
  vulName: string;
  vulLevel: string;
  dealStatus: string;
  assetName: string;
  assetIp: string;
  firstFoundTime: string;
  lastFoundTime: string;
  solution?: string;
  description?: string;
}

export interface VulnerabilityTag {
  tagId: string;
  tagName: string;
  tagCount: number;
  checked?: boolean;
}

export interface QueryParams {
  conditions?: any[];
  dateRange?: string;
  dealStatus?: string;
  orgId?: string;
  assetApp?: string;
  vulLevel?: string;
  vulType?: string;
  syscode?: string;
  pageNum?: number;
  pageSize?: number;
  headerFilter?: any;
}

export interface QueryResult<T> {
  rows: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

export interface StatisticsData {
  total: number;
  undisposed: number;
  disposalof: number;
  noNeedHandle: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
}

/**
 * 漏洞管理业务服务类
 * 封装所有业务逻辑，提供高级API
 */
export class VulnerabilityService {
  private static instance: VulnerabilityService;
  
  // 装饰后的查询函数
  private queryVulnerabilityData = withEnhancements({
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000,
      maxSize: 20
    },
    debounce: {
      enabled: true,
      delay: 500
    },
    retry: {
      enabled: true,
      maxAttempts: 3,
      retryCondition: (error) => !error.status || error.status >= 500
    },
    logging: {
      enabled: true,
      logLevel: 'info',
      logDuration: true
    },
    errorHandling: {
      enabled: true,
      transform: (error) => ({
        ...error,
        userMessage: this.getUserFriendlyErrorMessage(error)
      })
    }
  })(this.fetchVulnerabilityData.bind(this));

  private queryTagData = withEnhancements({
    cache: {
      enabled: true,
      ttl: 10 * 60 * 1000,
      maxSize: 10
    },
    debounce: {
      enabled: true,
      delay: 300
    }
  })(this.fetchTagData.bind(this));

  private queryStatisticsData = withEnhancements({
    cache: {
      enabled: true,
      ttl: 2 * 60 * 1000,
      maxSize: 5
    }
  })(this.fetchStatisticsData.bind(this));

  /**
   * 单例模式
   */
  public static getInstance(): VulnerabilityService {
    if (!VulnerabilityService.instance) {
      VulnerabilityService.instance = new VulnerabilityService();
    }
    return VulnerabilityService.instance;
  }

  /**
   * 加载漏洞数据
   */
  public async loadVulnerabilityData(forceRefresh = false): Promise<void> {
    try {
      vulnerabilityStore.dispatch({
        type: ActionType.LOAD_TABLE_DATA_START,
        meta: { source: 'VulnerabilityService' }
      });

      if (forceRefresh) {
        QueryDecorator.clearCache(this.queryVulnerabilityData);
      }

      const state = vulnerabilityStore.getState();
      const queryParams = this.buildQueryParams(state);
      
      const result = await this.queryVulnerabilityData(queryParams);

      vulnerabilityStore.dispatch({
        type: ActionType.LOAD_TABLE_DATA_SUCCESS,
        payload: result,
        meta: { source: 'VulnerabilityService' }
      });

      // 同时加载统计数据
      this.loadStatisticsData(queryParams);

    } catch (error) {
      vulnerabilityStore.dispatch({
        type: ActionType.LOAD_TABLE_DATA_ERROR,
        payload: { message: error.userMessage || error.message },
        meta: { source: 'VulnerabilityService' }
      });
      throw error;
    }
  }

  /**
   * 加载标签数据
   */
  public async loadTagData(vulViewParams?: any): Promise<void> {
    try {
      const state = vulnerabilityStore.getState();
      const queryParams = vulViewParams || this.buildTagQueryParams(state);
      
      const result = await this.queryTagData(queryParams);

      // 检查当前选中标签的有效性
      const currentTag = state.searchCondition.event_type_tag;
      let shouldResetTag = false;

      if (currentTag) {
        const tagExists = result.data.some((tag: VulnerabilityTag) => 
          tag.tagId === currentTag.tagId
        );
        
        if (!tagExists) {
          shouldResetTag = true;
        } else {
          // 更新标签选中状态
          result.data.forEach((tag: VulnerabilityTag) => {
            if (tag.tagId === currentTag.tagId) {
              tag.checked = true;
            }
          });
        }
      }

      vulnerabilityStore.dispatch({
        type: ActionType.LOAD_TAG_DATA_SUCCESS,
        payload: {
          data: result.data,
          totalCount: this.calculateTotalTagCount(result.data)
        },
        meta: { source: 'VulnerabilityService' }
      });

      // 如果标签无效，重置选择并重新加载数据
      if (shouldResetTag) {
        await this.resetTagSelection();
      }

    } catch (error) {
      console.error('Failed to load tag data:', error);
      throw error;
    }
  }

  /**
   * 加载统计数据
   */
  public async loadStatisticsData(queryParams?: QueryParams): Promise<void> {
    try {
      const state = vulnerabilityStore.getState();
      const params = queryParams || this.buildQueryParams(state);
      
      const result = await this.queryStatisticsData(params);

      vulnerabilityStore.dispatch({
        type: ActionType.UPDATE_STATISTICS,
        payload: result,
        meta: { source: 'VulnerabilityService' }
      });

    } catch (error) {
      console.error('Failed to load statistics data:', error);
    }
  }

  /**
   * 更新搜索条件
   */
  public updateSearchCondition(updates: Partial<any>): void {
    vulnerabilityStore.dispatch({
      type: ActionType.UPDATE_SEARCH_CONDITION,
      payload: updates,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 更新日期范围
   */
  public updateDateRange(type: 'segment' | 'custom', value: any): void {
    vulnerabilityStore.dispatch({
      type: ActionType.UPDATE_DATE_RANGE,
      payload: { type, value },
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 重置搜索条件
   */
  public resetSearchConditions(): void {
    vulnerabilityStore.dispatch({
      type: ActionType.RESET_SEARCH_CONDITIONS,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 重置标签选择
   */
  public async resetTagSelection(): Promise<void> {
    vulnerabilityStore.dispatch({
      type: ActionType.RESET_TAG_SELECTION,
      meta: { source: 'VulnerabilityService' }
    });

    // 重新加载数据
    await this.loadVulnerabilityData(true);
  }

  /**
   * 更新表格分页
   */
  public updateTablePage(pageInfo: Partial<any>): void {
    vulnerabilityStore.dispatch({
      type: ActionType.UPDATE_TABLE_PAGE,
      payload: pageInfo,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 更新选中行
   */
  public updateSelectedRows(rows: any[]): void {
    vulnerabilityStore.dispatch({
      type: ActionType.UPDATE_SELECTED_ROWS,
      payload: rows,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 切换标签页
   */
  public switchTab(tabName: string): void {
    vulnerabilityStore.dispatch({
      type: ActionType.SWITCH_TAB,
      payload: tabName,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    QueryDecorator.clearCache(this.queryVulnerabilityData);
    QueryDecorator.clearCache(this.queryTagData);
    QueryDecorator.clearCache(this.queryStatisticsData);
    
    vulnerabilityStore.dispatch({
      type: ActionType.INVALIDATE_CACHE,
      meta: { source: 'VulnerabilityService' }
    });
  }

  /**
   * 获取服务统计信息
   */
  public getServiceStats() {
    return {
      vulnerabilityQuery: QueryDecorator.getStats(this.queryVulnerabilityData),
      tagQuery: QueryDecorator.getStats(this.queryTagData),
      statisticsQuery: QueryDecorator.getStats(this.queryStatisticsData),
      store: vulnerabilityStore.getState()
    };
  }

  // 私有方法

  private async fetchVulnerabilityData(params: QueryParams): Promise<QueryResult<VulnerabilityItem>> {
    const response = await vulnerabilityApiClient.post('/api/vulnerability/list', params);
    return response.data;
  }

  private async fetchTagData(params: QueryParams): Promise<{ data: VulnerabilityTag[] }> {
    const response = await vulnerabilityApiClient.post('/api/vulnerability/tags', params);
    return response.data;
  }

  private async fetchStatisticsData(params: QueryParams): Promise<StatisticsData> {
    const response = await vulnerabilityApiClient.post('/api/vulnerability/statistics', params);
    return response.data;
  }

  private buildQueryParams(state: any): QueryParams {
    const conditions = filterStrategyManager.applyFilters(state.searchCondition);
    
    return {
      conditions: state.columnCondition.value 
        ? [state.columnCondition, ...conditions]
        : conditions,
      dateRange: vulnerabilityStore.computedDateRange.value,
      dealStatus: state.searchCondition.dealStatus,
      orgId: state.searchCondition.orgId,
      assetApp: state.searchCondition.asset_app_name,
      vulLevel: state.searchCondition.reseverity?.join(','),
      vulType: state.searchCondition.event_type_tag?.tagId,
      syscode: state.searchCondition.syscode?.join(','),
      pageNum: state.tablePage.currentPage,
      pageSize: state.tablePage.pageSize
    };
  }

  private buildTagQueryParams(state: any): QueryParams {
    const conditions = filterStrategyManager.applyFilters({
      ...state.searchCondition,
      event_type_tag: null // 排除标签条件
    });

    return {
      conditions: state.columnCondition.value 
        ? [state.columnCondition, ...conditions]
        : conditions,
      dateRange: vulnerabilityStore.computedDateRange.value,
      dealStatus: state.searchCondition.dealStatus,
      orgId: state.searchCondition.orgId,
      assetApp: state.searchCondition.asset_app_name,
      vulLevel: state.searchCondition.reseverity?.join(','),
      syscode: state.searchCondition.syscode?.join(','),
      viewType: 'asset'
    };
  }

  private calculateTotalTagCount(tags: VulnerabilityTag[]): number {
    return tags.reduce((total, tag) => total + (tag.tagCount || 0), 0);
  }

  private getUserFriendlyErrorMessage(error: any): string {
    if (error.isNetworkError) {
      return '网络连接失败，请检查网络设置后重试';
    }
    
    if (error.isTimeout) {
      return '请求超时，请稍后重试';
    }
    
    if (error.status === 403) {
      return '权限不足，请联系管理员';
    }
    
    if (error.status === 404) {
      return '请求的资源不存在';
    }
    
    if (error.status >= 500) {
      return '服务器内部错误，请稍后重试';
    }
    
    return error.message || '操作失败，请重试';
  }
}

// 导出单例实例
export const vulnerabilityService = VulnerabilityService.getInstance();
