/**
 * 装饰器模式 - 查询函数增强器
 * 为查询函数添加缓存、防抖、错误处理等横切关注点
 */

import { LRUCache } from '../utils/LRUCache';

// 装饰器配置接口
export interface DecoratorConfig {
  cache?: {
    enabled: boolean;
    ttl?: number;
    maxSize?: number;
    keyGenerator?: (...args: any[]) => string;
  };
  debounce?: {
    enabled: boolean;
    delay?: number;
    maxWait?: number;
    leading?: boolean;
    trailing?: boolean;
  };
  retry?: {
    enabled: boolean;
    maxAttempts?: number;
    delay?: number;
    backoff?: 'linear' | 'exponential';
    retryCondition?: (error: any) => boolean;
  };
  timeout?: {
    enabled: boolean;
    duration?: number;
  };
  logging?: {
    enabled: boolean;
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    logArgs?: boolean;
    logResult?: boolean;
    logDuration?: boolean;
  };
  errorHandling?: {
    enabled: boolean;
    fallback?: (...args: any[]) => any;
    transform?: (error: any) => any;
  };
}

// 查询函数类型
export type QueryFunction<T extends any[], R> = (...args: T) => Promise<R>;

// 装饰器元数据
interface DecoratorMetadata {
  originalFunction: Function;
  config: DecoratorConfig;
  cache?: LRUCache<string, any>;
  debounceTimer?: NodeJS.Timeout;
  pendingPromises?: Map<string, Promise<any>>;
  stats: {
    calls: number;
    cacheHits: number;
    cacheMisses: number;
    errors: number;
    totalDuration: number;
  };
}

/**
 * 查询装饰器类
 * 提供函数增强功能
 */
export class QueryDecorator {
  private static metadata = new WeakMap<Function, DecoratorMetadata>();

  /**
   * 装饰查询函数
   */
  public static decorate<T extends any[], R>(
    fn: QueryFunction<T, R>,
    config: DecoratorConfig
  ): QueryFunction<T, R> {
    const metadata: DecoratorMetadata = {
      originalFunction: fn,
      config,
      stats: {
        calls: 0,
        cacheHits: 0,
        cacheMisses: 0,
        errors: 0,
        totalDuration: 0
      }
    };

    // 初始化缓存
    if (config.cache?.enabled) {
      metadata.cache = new LRUCache(
        config.cache.maxSize || 50,
        { ttl: config.cache.ttl || 5 * 60 * 1000 }
      );
    }

    // 初始化防重复请求映射
    if (config.cache?.enabled) {
      metadata.pendingPromises = new Map();
    }

    this.metadata.set(fn, metadata);

    // 创建装饰后的函数
    const decoratedFunction = async (...args: T): Promise<R> => {
      const startTime = Date.now();
      metadata.stats.calls++;

      try {
        // 生成缓存键
        const cacheKey = this.generateCacheKey(config, args);

        // 检查缓存
        if (config.cache?.enabled && metadata.cache) {
          const cachedResult = metadata.cache.get(cacheKey);
          if (cachedResult !== undefined) {
            metadata.stats.cacheHits++;
            this.log(config, 'debug', 'Cache hit', { cacheKey, args });
            return cachedResult;
          }
          metadata.stats.cacheMisses++;
        }

        // 检查是否有相同的请求正在进行
        if (metadata.pendingPromises?.has(cacheKey)) {
          this.log(config, 'debug', 'Reusing pending promise', { cacheKey });
          return await metadata.pendingPromises.get(cacheKey)!;
        }

        // 应用防抖
        if (config.debounce?.enabled) {
          await this.applyDebounce(metadata, config.debounce);
        }

        // 创建执行Promise
        const executePromise = this.executeWithEnhancements(fn, args, config);
        
        // 缓存Promise（防止重复请求）
        if (metadata.pendingPromises) {
          metadata.pendingPromises.set(cacheKey, executePromise);
        }

        try {
          const result = await executePromise;

          // 缓存结果
          if (config.cache?.enabled && metadata.cache) {
            metadata.cache.set(cacheKey, result);
          }

          // 记录统计信息
          metadata.stats.totalDuration += Date.now() - startTime;
          
          this.log(config, 'debug', 'Function executed successfully', {
            args: config.logging?.logArgs ? args : undefined,
            result: config.logging?.logResult ? result : undefined,
            duration: Date.now() - startTime
          });

          return result;

        } finally {
          // 清理pending promise
          if (metadata.pendingPromises) {
            metadata.pendingPromises.delete(cacheKey);
          }
        }

      } catch (error) {
        metadata.stats.errors++;
        
        this.log(config, 'error', 'Function execution failed', {
          error,
          args: config.logging?.logArgs ? args : undefined
        });

        // 应用错误处理
        if (config.errorHandling?.enabled) {
          return this.handleError(error, args, config);
        }

        throw error;
      }
    };

    return decoratedFunction;
  }

  /**
   * 获取函数统计信息
   */
  public static getStats(fn: Function) {
    const metadata = this.metadata.get(fn);
    return metadata ? { ...metadata.stats } : null;
  }

  /**
   * 清除函数缓存
   */
  public static clearCache(fn: Function): void {
    const metadata = this.metadata.get(fn);
    if (metadata?.cache) {
      metadata.cache.clear();
    }
  }

  /**
   * 重置函数统计
   */
  public static resetStats(fn: Function): void {
    const metadata = this.metadata.get(fn);
    if (metadata) {
      metadata.stats = {
        calls: 0,
        cacheHits: 0,
        cacheMisses: 0,
        errors: 0,
        totalDuration: 0
      };
    }
  }

  // 私有方法

  private static async executeWithEnhancements<T extends any[], R>(
    fn: QueryFunction<T, R>,
    args: T,
    config: DecoratorConfig
  ): Promise<R> {
    // 应用超时
    if (config.timeout?.enabled) {
      return await this.withTimeout(fn, args, config.timeout.duration || 30000);
    }

    // 应用重试
    if (config.retry?.enabled) {
      return await this.withRetry(fn, args, config.retry);
    }

    return await fn(...args);
  }

  private static async withTimeout<T extends any[], R>(
    fn: QueryFunction<T, R>,
    args: T,
    timeout: number
  ): Promise<R> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Function execution timeout after ${timeout}ms`));
      }, timeout);

      fn(...args)
        .then(resolve)
        .catch(reject)
        .finally(() => clearTimeout(timer));
    });
  }

  private static async withRetry<T extends any[], R>(
    fn: QueryFunction<T, R>,
    args: T,
    retryConfig: NonNullable<DecoratorConfig['retry']>
  ): Promise<R> {
    const maxAttempts = retryConfig.maxAttempts || 3;
    const delay = retryConfig.delay || 1000;
    const backoff = retryConfig.backoff || 'exponential';
    
    let lastError: any;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn(...args);
      } catch (error) {
        lastError = error;

        // 检查是否应该重试
        if (retryConfig.retryCondition && !retryConfig.retryCondition(error)) {
          throw error;
        }

        // 如果是最后一次尝试，抛出错误
        if (attempt === maxAttempts) {
          throw error;
        }

        // 计算延迟时间
        const currentDelay = backoff === 'exponential' 
          ? delay * Math.pow(2, attempt - 1)
          : delay * attempt;

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, currentDelay));
      }
    }

    throw lastError;
  }

  private static async applyDebounce(
    metadata: DecoratorMetadata,
    debounceConfig: NonNullable<DecoratorConfig['debounce']>
  ): Promise<void> {
    return new Promise((resolve) => {
      if (metadata.debounceTimer) {
        clearTimeout(metadata.debounceTimer);
      }

      metadata.debounceTimer = setTimeout(() => {
        resolve();
      }, debounceConfig.delay || 300);
    });
  }

  private static generateCacheKey(config: DecoratorConfig, args: any[]): string {
    if (config.cache?.keyGenerator) {
      return config.cache.keyGenerator(...args);
    }

    try {
      return JSON.stringify(args);
    } catch {
      return args.map(arg => String(arg)).join('|');
    }
  }

  private static handleError<T extends any[], R>(
    error: any,
    args: T,
    config: DecoratorConfig
  ): R {
    // 应用错误转换
    if (config.errorHandling?.transform) {
      error = config.errorHandling.transform(error);
    }

    // 应用fallback
    if (config.errorHandling?.fallback) {
      return config.errorHandling.fallback(...args);
    }

    throw error;
  }

  private static log(
    config: DecoratorConfig,
    level: string,
    message: string,
    data?: any
  ): void {
    if (!config.logging?.enabled) return;

    const logLevel = config.logging.logLevel || 'info';
    const levels = ['debug', 'info', 'warn', 'error'];
    
    if (levels.indexOf(level) >= levels.indexOf(logLevel)) {
      console[level as keyof Console](`[QueryDecorator] ${message}`, data);
    }
  }
}

/**
 * 便捷装饰器函数
 */

// 缓存装饰器
export function withCache<T extends any[], R>(
  ttl: number = 5 * 60 * 1000,
  maxSize: number = 50
) {
  return (fn: QueryFunction<T, R>): QueryFunction<T, R> => {
    return QueryDecorator.decorate(fn, {
      cache: {
        enabled: true,
        ttl,
        maxSize
      }
    });
  };
}

// 防抖装饰器
export function withDebounce<T extends any[], R>(
  delay: number = 300
) {
  return (fn: QueryFunction<T, R>): QueryFunction<T, R> => {
    return QueryDecorator.decorate(fn, {
      debounce: {
        enabled: true,
        delay
      }
    });
  };
}

// 重试装饰器
export function withRetry<T extends any[], R>(
  maxAttempts: number = 3,
  delay: number = 1000
) {
  return (fn: QueryFunction<T, R>): QueryFunction<T, R> => {
    return QueryDecorator.decorate(fn, {
      retry: {
        enabled: true,
        maxAttempts,
        delay,
        retryCondition: (error) => {
          // 只对网络错误和5xx错误重试
          return !error.status || error.status >= 500;
        }
      }
    });
  };
}

// 组合装饰器
export function withEnhancements<T extends any[], R>(
  config: DecoratorConfig
) {
  return (fn: QueryFunction<T, R>): QueryFunction<T, R> => {
    return QueryDecorator.decorate(fn, config);
  };
}
