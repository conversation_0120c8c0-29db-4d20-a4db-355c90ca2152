/**
 * 漏洞管理状态管理系统
 * 基于 Flux 架构的单向数据流状态管理
 */

import { reactive, readonly, computed, ComputedRef } from 'vue';
import { LRUCache } from '../utils/LRUCache';
import { EventEmitter } from '../utils/EventEmitter';

// 状态接口定义
export interface VulnerabilityState {
  // 表格数据
  tableData: any[];
  tableLoading: boolean;
  tablePage: {
    currentPage: number;
    pageSize: number;
    total: number;
  };
  
  // 筛选条件
  searchCondition: {
    dealStatus: string | null;
    reseverity: string[];
    assetTypeName: string | null;
    syscode: string[];
    event_type_tag: any | null;
    orgId: string | null;
    asset_app_name: string | null;
  };
  
  // 时间范围
  dateRangeSign: string;
  dateTimeRange: Date[];
  
  // 标签数据
  eventTagData: any[];
  totalTagCount: number;
  checkAll: boolean;
  
  // 选中数据
  selectedEventRows: any[];
  
  // UI 状态
  activeTabName: string;
  columnCondition: {
    value: string | null;
    field: string;
    fuzzyable: boolean;
    operator: string;
  };
  
  // 统计数据
  vulnerabilityStatisticsData: any[];
  
  // 错误状态
  error: string | null;
  
  // 缓存状态
  cacheStatus: {
    lastUpdate: number;
    isStale: boolean;
  };
}

// Action 类型定义
export enum ActionType {
  // 数据加载
  LOAD_TABLE_DATA_START = 'LOAD_TABLE_DATA_START',
  LOAD_TABLE_DATA_SUCCESS = 'LOAD_TABLE_DATA_SUCCESS',
  LOAD_TABLE_DATA_ERROR = 'LOAD_TABLE_DATA_ERROR',
  
  // 标签数据
  LOAD_TAG_DATA_SUCCESS = 'LOAD_TAG_DATA_SUCCESS',
  RESET_TAG_SELECTION = 'RESET_TAG_SELECTION',
  
  // 筛选条件
  UPDATE_SEARCH_CONDITION = 'UPDATE_SEARCH_CONDITION',
  UPDATE_DATE_RANGE = 'UPDATE_DATE_RANGE',
  RESET_SEARCH_CONDITIONS = 'RESET_SEARCH_CONDITIONS',
  
  // 表格操作
  UPDATE_TABLE_PAGE = 'UPDATE_TABLE_PAGE',
  UPDATE_SELECTED_ROWS = 'UPDATE_SELECTED_ROWS',
  
  // UI 状态
  SWITCH_TAB = 'SWITCH_TAB',
  UPDATE_COLUMN_CONDITION = 'UPDATE_COLUMN_CONDITION',
  
  // 统计数据
  UPDATE_STATISTICS = 'UPDATE_STATISTICS',
  
  // 错误处理
  SET_ERROR = 'SET_ERROR',
  CLEAR_ERROR = 'CLEAR_ERROR',
  
  // 缓存管理
  INVALIDATE_CACHE = 'INVALIDATE_CACHE',
  UPDATE_CACHE_STATUS = 'UPDATE_CACHE_STATUS'
}

// Action 接口定义
export interface Action {
  type: ActionType;
  payload?: any;
  meta?: {
    timestamp: number;
    source: string;
  };
}

// 初始状态
const initialState: VulnerabilityState = {
  tableData: [],
  tableLoading: false,
  tablePage: {
    currentPage: 1,
    pageSize: 20,
    total: 0
  },
  searchCondition: {
    dealStatus: null,
    reseverity: [],
    assetTypeName: null,
    syscode: [],
    event_type_tag: null,
    orgId: null,
    asset_app_name: null
  },
  dateRangeSign: '1d',
  dateTimeRange: [],
  eventTagData: [],
  totalTagCount: 0,
  checkAll: true,
  selectedEventRows: [],
  activeTabName: 'assetPerspective',
  columnCondition: {
    value: null,
    field: 'assetName',
    fuzzyable: true,
    operator: 'fuzzy'
  },
  vulnerabilityStatisticsData: [],
  error: null,
  cacheStatus: {
    lastUpdate: 0,
    isStale: false
  }
};

/**
 * 漏洞管理状态管理器
 * 实现 Flux 架构的 Store
 */
class VulnerabilityStore {
  private state = reactive<VulnerabilityState>({ ...initialState });
  private eventEmitter = new EventEmitter();
  private cache = new LRUCache<string, any>(50); // 最多缓存50个查询结果
  
  // 只读状态访问
  public readonly getState = () => readonly(this.state);
  
  // 计算属性
  public readonly computedDateRange: ComputedRef<string> = computed(() => {
    if (this.state.dateRangeSign) {
      return this.state.dateRangeSign;
    } else if (this.state.dateTimeRange && this.state.dateTimeRange.length === 2) {
      return `${this.state.dateTimeRange[0].toISOString()},${this.state.dateTimeRange[1].toISOString()}`;
    }
    return 'all';
  });
  
  public readonly hasSelectedRows: ComputedRef<boolean> = computed(() => {
    return this.state.selectedEventRows.length > 0;
  });
  
  public readonly isDataStale: ComputedRef<boolean> = computed(() => {
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    return now - this.state.cacheStatus.lastUpdate > fiveMinutes;
  });
  
  /**
   * 分发 Action
   */
  public dispatch(action: Action): void {
    // 添加元数据
    action.meta = {
      timestamp: Date.now(),
      source: action.meta?.source || 'unknown'
    };
    
    // 执行 Reducer
    this.reduce(action);
    
    // 发布状态变更事件
    this.eventEmitter.emit('stateChange', {
      action,
      state: this.getState()
    });
    
    // 记录日志（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔄 Action: ${action.type}`);
      console.log('Payload:', action.payload);
      console.log('New State:', this.getState());
      console.groupEnd();
    }
  }
  
  /**
   * Reducer - 处理状态变更
   */
  private reduce(action: Action): void {
    switch (action.type) {
      case ActionType.LOAD_TABLE_DATA_START:
        this.state.tableLoading = true;
        this.state.error = null;
        break;
        
      case ActionType.LOAD_TABLE_DATA_SUCCESS:
        this.state.tableLoading = false;
        this.state.tableData = action.payload.rows || [];
        this.state.tablePage.total = action.payload.totalElements || 0;
        this.state.cacheStatus.lastUpdate = Date.now();
        this.state.cacheStatus.isStale = false;
        break;
        
      case ActionType.LOAD_TABLE_DATA_ERROR:
        this.state.tableLoading = false;
        this.state.error = action.payload.message || '数据加载失败';
        break;
        
      case ActionType.LOAD_TAG_DATA_SUCCESS:
        this.state.eventTagData = action.payload.data || [];
        this.state.totalTagCount = action.payload.totalCount || 0;
        break;
        
      case ActionType.RESET_TAG_SELECTION:
        this.state.searchCondition.event_type_tag = null;
        this.state.checkAll = true;
        break;
        
      case ActionType.UPDATE_SEARCH_CONDITION:
        Object.assign(this.state.searchCondition, action.payload);
        this.state.cacheStatus.isStale = true;
        break;
        
      case ActionType.UPDATE_DATE_RANGE:
        if (action.payload.type === 'segment') {
          this.state.dateRangeSign = action.payload.value;
          this.state.dateTimeRange = [];
        } else {
          this.state.dateRangeSign = '';
          this.state.dateTimeRange = action.payload.value;
        }
        this.state.cacheStatus.isStale = true;
        break;
        
      case ActionType.RESET_SEARCH_CONDITIONS:
        Object.assign(this.state.searchCondition, initialState.searchCondition);
        this.state.dateRangeSign = '1d';
        this.state.dateTimeRange = [];
        this.state.tablePage.currentPage = 1;
        this.state.cacheStatus.isStale = true;
        break;
        
      case ActionType.UPDATE_TABLE_PAGE:
        Object.assign(this.state.tablePage, action.payload);
        break;
        
      case ActionType.UPDATE_SELECTED_ROWS:
        this.state.selectedEventRows = action.payload || [];
        break;
        
      case ActionType.SWITCH_TAB:
        this.state.activeTabName = action.payload;
        break;
        
      case ActionType.UPDATE_COLUMN_CONDITION:
        Object.assign(this.state.columnCondition, action.payload);
        break;
        
      case ActionType.UPDATE_STATISTICS:
        this.state.vulnerabilityStatisticsData = action.payload || [];
        break;
        
      case ActionType.SET_ERROR:
        this.state.error = action.payload;
        break;
        
      case ActionType.CLEAR_ERROR:
        this.state.error = null;
        break;
        
      case ActionType.INVALIDATE_CACHE:
        this.cache.clear();
        this.state.cacheStatus.isStale = true;
        break;
        
      case ActionType.UPDATE_CACHE_STATUS:
        Object.assign(this.state.cacheStatus, action.payload);
        break;
        
      default:
        console.warn(`Unknown action type: ${action.type}`);
    }
  }
  
  /**
   * 订阅状态变更
   */
  public subscribe(callback: (event: any) => void): () => void {
    this.eventEmitter.on('stateChange', callback);
    return () => this.eventEmitter.off('stateChange', callback);
  }
  
  /**
   * 获取缓存
   */
  public getCache(key: string): any {
    return this.cache.get(key);
  }
  
  /**
   * 设置缓存
   */
  public setCache(key: string, value: any): void {
    this.cache.set(key, value);
  }
  
  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.cache.clear();
    this.dispatch({
      type: ActionType.INVALIDATE_CACHE,
      meta: { source: 'store', timestamp: Date.now() }
    });
  }
  
  /**
   * 重置状态
   */
  public reset(): void {
    Object.assign(this.state, { ...initialState });
    this.cache.clear();
  }
}

// 单例模式 - 全局状态管理器
export const vulnerabilityStore = new VulnerabilityStore();
