import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.securityVulServer}`;
const basePath03 = `${ServerNames.securityVulServer}/sum`;
const basePath02 = `${ServerNames.securityVulServer}/vul`;

const pathList = {
    vulDetailByAssetUrl: "/vulDetails", // 资产视角表格数据
    vulDetailByVulUrl: "/v3/suba/vulDetailByVul", // 3.0资产视角表格数据
    importVulDetailByAssetUrl: "/v3/sum/import/vulDetails", // 资产视角表格数据导出
    templateTypesUrl: "/excel/templateTypes", // 资产视角ip详情导入种类
};

const vulDetailByAssetMethod = (params: any) => {
    return http.postJson(`${basePath}${pathList.vulDetailByVulUrl}`, params);
}

const importVulDetailByAssetMethod = (params: any) => {
    return http.postBlob(`${basePath}${pathList.importVulDetailByAssetUrl}`, params);
}

const templateTypesMethod = () => {
    return http.get(`${basePath02}${pathList.templateTypesUrl}`);
}

const getVulAssetDetailsData = (params: any) =>
    http.postJson<any>(
        `${basePath}/v3/sum/vulAssetDetails`,
        params
    );

const dispatchEventProcess = data => {
    return http.post<any, any>(
        `${ServerNames.eamPmServer}/sheets/autoSecurityVulDeal/start/vul/manual`,
        { data },
        {
            timeout: 60000
        }
    );
};

const countFraAssetIp = data => {
    return http.postJson<any>(
        `${ServerNames.securityVulServer}/v3/sum/countFraAssetIp`,
        data
    );
};

export {
    vulDetailByAssetMethod,
    importVulDetailByAssetMethod,
    templateTypesMethod,
    getVulAssetDetailsData,
    dispatchEventProcess,
    countFraAssetIp
};
