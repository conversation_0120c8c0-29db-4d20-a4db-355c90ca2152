import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.securityVulServer}/suba`;
const basePath04 = `${ServerNames.securityVulServer}`;
const basePath02 = `${ServerNames.eamCoreServer}`;
const basePath03 = `/framework/sysmanage`;

const pathList = {
  // /v3/suba/vulDetailByAsset
  vulDetailByAssetUrl: "/vulDetailByAsset", // 资产视角表格数据
  vulDetailByAssetUrlV3: "/v3/suba/vulDetailByAsset", // 资产视角表格数据
  importVulDetailByAssetUrl: "/v3/suba/import/vulDetailByAsset", // 资产视角表格数据导出
  queryAllCategoryUrl: "/category/getAllCategory", // 得到资产类别下拉树
  getUserDeptTreeUrl: "/dept/getUserDeptTree", // 得到所属组织下拉树
  vulAddNewTaskForVulUrl: "/v3/vul/addNewTaskForVul" // 漏洞验证 - 任务下发
};

// const vulDetailByAssetMethod = (params: any) => {
//     return http.postJson(`${basePath}${pathList.vulDetailByAssetUrl}`, params);
// }
const vulDetailByAssetMethod = (params: any) => {
  return http.postJson(
    `${basePath04}${pathList.vulDetailByAssetUrlV3}`,
    params
  );
};

// 漏洞验证 - 任务下发
const vulAddNewTaskForVulMethod = (params: any) => {
  return http.postJson(
    `${basePath04}${pathList.vulAddNewTaskForVulUrl}`,
    params
  );
};

// 统计
export const vulTotalByStatus = (params: any) => {
  return http.postJson(`${basePath04}/v3/suba/vulTotalByStatus`, params);
};

const importVulDetailByAssetMethod = (params: any) => {
  return http.postBlob(
    `${basePath04}${pathList.importVulDetailByAssetUrl}`,
    params
  );
};

const queryAllCategoryAxios = () => {
  return http.get(`${basePath02}${pathList.queryAllCategoryUrl}`);
};

const getUserDeptTreeAxios = () => {
  return http.get(`${basePath03}${pathList.getUserDeptTreeUrl}`);
};

//查询业务系统数据
const getBusinessSystemData = () =>
  http.postJson<any>(`${basePath04}/v3/suba/queryAssetAppByVul`, {});

export const queryEventDeptTree = () =>
  http.postJson<any>(`${basePath04}/v3/suba/queryOrgTreeByVul`, {});

//查询标签数据
const getTagData = (params: any) =>
  http.postJson<any>(`${basePath04}/v3/suba/queryVulTag`, params);

//数据来源列表
const getQuerySyscodeList = () =>
  http.get(`${basePath04}/v3/suba/querySyscodeList`);

/**
 * 安全漏洞查询列头过滤选项数据(下拉分组)
 *
 * @param params
 */
export const queryVulTableHeadGroup = (params: any) =>
  http.postJson<any>(`${basePath04}/v3/queryVulTableHeadGroup`, params);

export {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  queryAllCategoryAxios,
  getUserDeptTreeAxios,
  getBusinessSystemData,
  getTagData,
  vulAddNewTaskForVulMethod,
  getQuerySyscodeList
};
