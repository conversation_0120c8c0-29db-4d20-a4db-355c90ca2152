/**
 * 数据导出 Web Worker
 * 处理大文件导出和格式转换，避免阻塞主线程
 */

// 导出格式类型
type ExportFormat = 'excel' | 'csv' | 'pdf' | 'json';

// 导出配置
interface ExportConfig {
  format: ExportFormat;
  filename: string;
  columns: ExportColumn[];
  options: ExportOptions;
}

interface ExportColumn {
  field: string;
  title: string;
  width?: number;
  formatter?: string; // 格式化函数名
}

interface ExportOptions {
  includeHeaders: boolean;
  dateFormat: string;
  numberFormat: string;
  maxRows?: number;
  compression?: boolean;
}

// Worker 消息类型
interface ExportWorkerMessage {
  id: string;
  type: 'EXPORT_DATA' | 'PROGRESS_UPDATE';
  payload: {
    data: any[];
    config: ExportConfig;
  };
}

interface ExportWorkerResponse {
  id: string;
  type: 'EXPORT_COMPLETE' | 'EXPORT_PROGRESS' | 'EXPORT_ERROR';
  success: boolean;
  data?: {
    blob?: Blob;
    url?: string;
    progress?: number;
  };
  error?: string;
}

/**
 * CSV 导出处理
 */
function exportToCSV(data: any[], config: ExportConfig): Blob {
  const { columns, options } = config;
  let csvContent = '';
  
  // 添加表头
  if (options.includeHeaders) {
    const headers = columns.map(col => `"${col.title}"`).join(',');
    csvContent += headers + '\n';
  }
  
  // 处理数据行
  const totalRows = Math.min(data.length, options.maxRows || data.length);
  
  for (let i = 0; i < totalRows; i++) {
    const row = data[i];
    const csvRow = columns.map(col => {
      let value = row[col.field];
      
      // 格式化处理
      if (value instanceof Date) {
        value = formatDate(value, options.dateFormat);
      } else if (typeof value === 'number') {
        value = formatNumber(value, options.numberFormat);
      } else if (value === null || value === undefined) {
        value = '';
      } else {
        value = String(value).replace(/"/g, '""'); // 转义双引号
      }
      
      return `"${value}"`;
    }).join(',');
    
    csvContent += csvRow + '\n';
    
    // 发送进度更新
    if (i % 1000 === 0) {
      sendProgressUpdate(config.filename, (i / totalRows) * 100);
    }
  }
  
  return new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
}

/**
 * Excel 导出处理（简化版，实际项目中可使用 SheetJS）
 */
function exportToExcel(data: any[], config: ExportConfig): Blob {
  const { columns, options } = config;
  
  // 创建工作表数据
  const worksheetData: any[][] = [];
  
  // 添加表头
  if (options.includeHeaders) {
    worksheetData.push(columns.map(col => col.title));
  }
  
  // 添加数据行
  const totalRows = Math.min(data.length, options.maxRows || data.length);
  
  for (let i = 0; i < totalRows; i++) {
    const row = data[i];
    const excelRow = columns.map(col => {
      let value = row[col.field];
      
      if (value instanceof Date) {
        return formatDate(value, options.dateFormat);
      } else if (typeof value === 'number') {
        return formatNumber(value, options.numberFormat);
      }
      
      return value;
    });
    
    worksheetData.push(excelRow);
    
    // 发送进度更新
    if (i % 1000 === 0) {
      sendProgressUpdate(config.filename, (i / totalRows) * 100);
    }
  }
  
  // 转换为 CSV 格式（简化实现）
  const csvContent = worksheetData.map(row => 
    row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
  ).join('\n');
  
  return new Blob([csvContent], { 
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
  });
}

/**
 * JSON 导出处理
 */
function exportToJSON(data: any[], config: ExportConfig): Blob {
  const { columns, options } = config;
  
  // 过滤和格式化数据
  const exportData = data.slice(0, options.maxRows || data.length).map(row => {
    const exportRow: any = {};
    
    columns.forEach(col => {
      let value = row[col.field];
      
      if (value instanceof Date) {
        value = formatDate(value, options.dateFormat);
      } else if (typeof value === 'number') {
        value = formatNumber(value, options.numberFormat);
      }
      
      exportRow[col.field] = value;
    });
    
    return exportRow;
  });
  
  const jsonContent = JSON.stringify(exportData, null, 2);
  
  return new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
}

/**
 * 日期格式化
 */
function formatDate(date: Date, format: string): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 数字格式化
 */
function formatNumber(num: number, format: string): string {
  if (format === 'integer') {
    return Math.round(num).toString();
  } else if (format.startsWith('decimal:')) {
    const decimals = parseInt(format.split(':')[1]) || 2;
    return num.toFixed(decimals);
  } else if (format === 'percentage') {
    return (num * 100).toFixed(2) + '%';
  }
  
  return num.toString();
}

/**
 * 发送进度更新
 */
function sendProgressUpdate(filename: string, progress: number) {
  const response: ExportWorkerResponse = {
    id: 'progress',
    type: 'EXPORT_PROGRESS',
    success: true,
    data: {
      progress: Math.round(progress)
    }
  };
  
  self.postMessage(response);
}

/**
 * Worker 消息处理
 */
self.onmessage = function(event: MessageEvent<ExportWorkerMessage>) {
  const { id, type, payload } = event.data;
  
  try {
    if (type === 'EXPORT_DATA') {
      const { data, config } = payload;
      let blob: Blob;
      
      // 根据格式选择导出方法
      switch (config.format) {
        case 'csv':
          blob = exportToCSV(data, config);
          break;
        case 'excel':
          blob = exportToExcel(data, config);
          break;
        case 'json':
          blob = exportToJSON(data, config);
          break;
        default:
          throw new Error(`Unsupported export format: ${config.format}`);
      }
      
      // 创建下载 URL
      const url = URL.createObjectURL(blob);
      
      const response: ExportWorkerResponse = {
        id,
        type: 'EXPORT_COMPLETE',
        success: true,
        data: {
          blob,
          url
        }
      };
      
      self.postMessage(response);
    }
    
  } catch (error) {
    const response: ExportWorkerResponse = {
      id,
      type: 'EXPORT_ERROR',
      success: false,
      error: error.message
    };
    
    self.postMessage(response);
  }
};

// Worker 初始化完成通知
self.postMessage({
  id: 'init',
  type: 'WORKER_READY',
  success: true,
  data: 'Export Worker initialized successfully'
});
