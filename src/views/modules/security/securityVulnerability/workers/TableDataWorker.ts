/**
 * 表格数据处理 Web Worker
 * 处理大数据量的排序、筛选、聚合等计算密集型操作
 */

// Worker 消息类型定义
interface WorkerMessage {
  id: string;
  type: 'SORT_DATA' | 'FILTER_DATA' | 'AGGREGATE_DATA' | 'SEARCH_DATA';
  payload: any;
}

interface WorkerResponse {
  id: string;
  type: string;
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
}

// 排序配置
interface SortConfig {
  field: string;
  order: 'asc' | 'desc';
  type: 'string' | 'number' | 'date';
}

// 筛选配置
interface FilterConfig {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

// 聚合配置
interface AggregateConfig {
  groupBy: string[];
  metrics: {
    field: string;
    operation: 'count' | 'sum' | 'avg' | 'min' | 'max';
  }[];
}

/**
 * 高性能数据排序
 */
function sortData(data: any[], config: SortConfig): any[] {
  const startTime = performance.now();
  
  const sorted = [...data].sort((a, b) => {
    const aVal = a[config.field];
    const bVal = b[config.field];
    
    let comparison = 0;
    
    switch (config.type) {
      case 'number':
        comparison = Number(aVal) - Number(bVal);
        break;
      case 'date':
        comparison = new Date(aVal).getTime() - new Date(bVal).getTime();
        break;
      case 'string':
      default:
        comparison = String(aVal).localeCompare(String(bVal));
        break;
    }
    
    return config.order === 'desc' ? -comparison : comparison;
  });
  
  const duration = performance.now() - startTime;
  console.log(`排序完成，耗时: ${duration.toFixed(2)}ms，数据量: ${data.length}`);
  
  return sorted;
}

/**
 * 高性能数据筛选
 */
function filterData(data: any[], filters: FilterConfig[]): any[] {
  const startTime = performance.now();
  
  const filtered = data.filter(item => {
    return filters.every(filter => {
      const value = item[filter.field];
      const filterValue = filter.value;
      
      switch (filter.operator) {
        case 'eq':
          return value === filterValue;
        case 'ne':
          return value !== filterValue;
        case 'gt':
          return Number(value) > Number(filterValue);
        case 'lt':
          return Number(value) < Number(filterValue);
        case 'contains':
          return String(value).toLowerCase().includes(String(filterValue).toLowerCase());
        case 'startsWith':
          return String(value).toLowerCase().startsWith(String(filterValue).toLowerCase());
        case 'endsWith':
          return String(value).toLowerCase().endsWith(String(filterValue).toLowerCase());
        default:
          return true;
      }
    });
  });
  
  const duration = performance.now() - startTime;
  console.log(`筛选完成，耗时: ${duration.toFixed(2)}ms，结果: ${filtered.length}/${data.length}`);
  
  return filtered;
}

/**
 * 数据聚合计算
 */
function aggregateData(data: any[], config: AggregateConfig): any[] {
  const startTime = performance.now();
  
  // 按分组字段分组
  const groups = new Map<string, any[]>();
  
  data.forEach(item => {
    const groupKey = config.groupBy.map(field => item[field]).join('|');
    if (!groups.has(groupKey)) {
      groups.set(groupKey, []);
    }
    groups.get(groupKey)!.push(item);
  });
  
  // 计算聚合指标
  const results: any[] = [];
  
  groups.forEach((groupData, groupKey) => {
    const groupValues = config.groupBy.reduce((obj, field, index) => {
      obj[field] = groupKey.split('|')[index];
      return obj;
    }, {} as any);
    
    const metrics = config.metrics.reduce((obj, metric) => {
      const values = groupData.map(item => Number(item[metric.field]) || 0);
      
      switch (metric.operation) {
        case 'count':
          obj[`${metric.field}_count`] = groupData.length;
          break;
        case 'sum':
          obj[`${metric.field}_sum`] = values.reduce((sum, val) => sum + val, 0);
          break;
        case 'avg':
          obj[`${metric.field}_avg`] = values.reduce((sum, val) => sum + val, 0) / values.length;
          break;
        case 'min':
          obj[`${metric.field}_min`] = Math.min(...values);
          break;
        case 'max':
          obj[`${metric.field}_max`] = Math.max(...values);
          break;
      }
      
      return obj;
    }, {} as any);
    
    results.push({ ...groupValues, ...metrics });
  });
  
  const duration = performance.now() - startTime;
  console.log(`聚合完成，耗时: ${duration.toFixed(2)}ms，分组数: ${results.length}`);
  
  return results;
}

/**
 * 全文搜索
 */
function searchData(data: any[], searchTerm: string, searchFields: string[]): any[] {
  const startTime = performance.now();
  
  if (!searchTerm.trim()) {
    return data;
  }
  
  const term = searchTerm.toLowerCase();
  
  const results = data.filter(item => {
    return searchFields.some(field => {
      const value = String(item[field] || '').toLowerCase();
      return value.includes(term);
    });
  });
  
  const duration = performance.now() - startTime;
  console.log(`搜索完成，耗时: ${duration.toFixed(2)}ms，结果: ${results.length}/${data.length}`);
  
  return results;
}

/**
 * Worker 消息处理
 */
self.onmessage = function(event: MessageEvent<WorkerMessage>) {
  const { id, type, payload } = event.data;
  const startTime = performance.now();
  
  try {
    let result: any;
    
    switch (type) {
      case 'SORT_DATA':
        result = sortData(payload.data, payload.config);
        break;
        
      case 'FILTER_DATA':
        result = filterData(payload.data, payload.filters);
        break;
        
      case 'AGGREGATE_DATA':
        result = aggregateData(payload.data, payload.config);
        break;
        
      case 'SEARCH_DATA':
        result = searchData(payload.data, payload.searchTerm, payload.searchFields);
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
    
    const duration = performance.now() - startTime;
    
    const response: WorkerResponse = {
      id,
      type,
      success: true,
      data: result,
      duration
    };
    
    self.postMessage(response);
    
  } catch (error) {
    const response: WorkerResponse = {
      id,
      type,
      success: false,
      error: error.message,
      duration: performance.now() - startTime
    };
    
    self.postMessage(response);
  }
};

// Worker 初始化完成通知
self.postMessage({
  id: 'init',
  type: 'WORKER_READY',
  success: true,
  data: 'Table Data Worker initialized successfully'
});
