/**
 * 策略模式 - 筛选条件处理策略
 * 使不同筛选策略可插拔，提高代码可维护性和扩展性
 */

import { vulnerabilityStore, ActionType } from '../store/VulnerabilityStore';

// 筛选策略接口
export interface FilterStrategy {
  name: string;
  priority: number;
  validate(value: any): boolean;
  transform(value: any): any;
  apply(conditions: any[], value: any): void;
  reset(): void;
  getDescription(): string;
}

// 基础筛选策略抽象类
export abstract class BaseFilterStrategy implements FilterStrategy {
  public abstract name: string;
  public abstract priority: number;

  public validate(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }

  public transform(value: any): any {
    return value;
  }

  public abstract apply(conditions: any[], value: any): void;

  public reset(): void {
    // 默认重置逻辑
  }

  public abstract getDescription(): string;
}

/**
 * 处置状态筛选策略
 */
export class DealStatusFilterStrategy extends BaseFilterStrategy {
  public name = 'dealStatus';
  public priority = 10;

  public validate(value: any): boolean {
    return super.validate(value) && typeof value === 'string';
  }

  public transform(value: any): string {
    const statusMap: Record<string, string> = {
      '1': 'undisposed',
      '2': 'disposalof', 
      '3': 'noNeedHandle',
      'undisposed': 'undisposed',
      'disposalof': 'disposalof',
      'noNeedHandle': 'noNeedHandle'
    };
    return statusMap[value] || value;
  }

  public apply(conditions: any[], value: any): void {
    if (this.validate(value)) {
      const transformedValue = this.transform(value);
      conditions.push({
        field: 'dealStatus',
        operator: 'exact',
        value: transformedValue
      });
    }
  }

  public getDescription(): string {
    return '处置状态筛选：支持未处置、已处置、无需处理状态筛选';
  }
}

/**
 * 风险级别筛选策略
 */
export class RiskLevelFilterStrategy extends BaseFilterStrategy {
  public name = 'riskLevel';
  public priority = 9;

  public validate(value: any): boolean {
    return Array.isArray(value) && value.length > 0;
  }

  public transform(value: any[]): string {
    // 风险级别映射
    const levelMap: Record<string, string> = {
      'critical': '严重',
      'high': '高危',
      'medium': '中危', 
      'low': '低危',
      'info': '信息'
    };
    
    return value.map(level => levelMap[level] || level).join(',');
  }

  public apply(conditions: any[], value: any): void {
    if (this.validate(value)) {
      const transformedValue = this.transform(value);
      conditions.push({
        field: 'vulLevel',
        operator: 'in',
        value: transformedValue
      });
    }
  }

  public getDescription(): string {
    return '风险级别筛选：支持严重、高危、中危、低危、信息级别筛选';
  }
}

/**
 * 资产类别筛选策略
 */
export class AssetTypeFilterStrategy extends BaseFilterStrategy {
  public name = 'assetType';
  public priority = 8;

  public validate(value: any): boolean {
    return super.validate(value) && typeof value === 'string';
  }

  public apply(conditions: any[], value: any): void {
    if (this.validate(value)) {
      conditions.push({
        field: 'assetTypeName',
        operator: 'exact',
        value: value
      });
    }
  }

  public getDescription(): string {
    return '资产类别筛选：按资产类型进行筛选';
  }
}

/**
 * 数据来源筛选策略
 */
export class DataSourceFilterStrategy extends BaseFilterStrategy {
  public name = 'dataSource';
  public priority = 7;

  public validate(value: any): boolean {
    return Array.isArray(value) && value.length > 0;
  }

  public transform(value: any[]): string {
    return value.join(',');
  }

  public apply(conditions: any[], value: any): void {
    if (this.validate(value)) {
      const transformedValue = this.transform(value);
      conditions.push({
        field: 'syscode',
        operator: 'in',
        value: transformedValue
      });
    }
  }

  public getDescription(): string {
    return '数据来源筛选：按数据来源系统进行筛选';
  }
}

/**
 * 时间范围筛选策略
 */
export class DateRangeFilterStrategy extends BaseFilterStrategy {
  public name = 'dateRange';
  public priority = 6;

  public validate(value: any): boolean {
    return super.validate(value);
  }

  public transform(value: any): string {
    if (typeof value === 'string') {
      return value;
    }
    
    if (Array.isArray(value) && value.length === 2) {
      return `${value[0].toISOString()},${value[1].toISOString()}`;
    }
    
    return 'all';
  }

  public apply(conditions: any[], value: any): void {
    // 时间范围通常作为查询参数而不是条件
    // 这里可以根据具体需求实现
  }

  public getDescription(): string {
    return '时间范围筛选：支持预设时间段和自定义时间范围';
  }
}

/**
 * 漏洞标签筛选策略
 */
export class VulTagFilterStrategy extends BaseFilterStrategy {
  public name = 'vulTag';
  public priority = 5;

  public validate(value: any): boolean {
    return value && typeof value === 'object' && value.tagId;
  }

  public apply(conditions: any[], value: any): void {
    if (this.validate(value)) {
      conditions.push({
        field: 'vulType',
        operator: 'exact',
        value: value.tagId
      });
    }
  }

  public getDescription(): string {
    return '漏洞标签筛选：按漏洞类型标签进行筛选';
  }
}

/**
 * 筛选策略管理器
 * 管理所有筛选策略，提供统一的筛选接口
 */
export class FilterStrategyManager {
  private strategies = new Map<string, FilterStrategy>();
  private enabledStrategies = new Set<string>();

  constructor() {
    this.registerDefaultStrategies();
  }

  /**
   * 注册筛选策略
   */
  public registerStrategy(strategy: FilterStrategy): void {
    this.strategies.set(strategy.name, strategy);
    this.enabledStrategies.add(strategy.name);
  }

  /**
   * 注销筛选策略
   */
  public unregisterStrategy(name: string): void {
    this.strategies.delete(name);
    this.enabledStrategies.delete(name);
  }

  /**
   * 启用策略
   */
  public enableStrategy(name: string): void {
    if (this.strategies.has(name)) {
      this.enabledStrategies.add(name);
    }
  }

  /**
   * 禁用策略
   */
  public disableStrategy(name: string): void {
    this.enabledStrategies.delete(name);
  }

  /**
   * 获取策略
   */
  public getStrategy(name: string): FilterStrategy | undefined {
    return this.strategies.get(name);
  }

  /**
   * 获取所有启用的策略
   */
  public getEnabledStrategies(): FilterStrategy[] {
    return Array.from(this.enabledStrategies)
      .map(name => this.strategies.get(name))
      .filter(Boolean) as FilterStrategy[]
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * 应用筛选条件
   */
  public applyFilters(searchCondition: any): any[] {
    const conditions: any[] = [];
    const enabledStrategies = this.getEnabledStrategies();

    for (const strategy of enabledStrategies) {
      try {
        const value = this.getValueForStrategy(strategy, searchCondition);
        strategy.apply(conditions, value);
      } catch (error) {
        console.error(`Error applying filter strategy ${strategy.name}:`, error);
      }
    }

    return conditions;
  }

  /**
   * 验证筛选条件
   */
  public validateFilters(searchCondition: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const enabledStrategies = this.getEnabledStrategies();

    for (const strategy of enabledStrategies) {
      try {
        const value = this.getValueForStrategy(strategy, searchCondition);
        if (value !== null && value !== undefined && !strategy.validate(value)) {
          errors.push(`Invalid value for ${strategy.name}: ${value}`);
        }
      } catch (error) {
        errors.push(`Validation error for ${strategy.name}: ${error}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 重置所有策略
   */
  public resetAllStrategies(): void {
    for (const strategy of this.strategies.values()) {
      try {
        strategy.reset();
      } catch (error) {
        console.error(`Error resetting strategy ${strategy.name}:`, error);
      }
    }
  }

  /**
   * 获取策略描述
   */
  public getStrategyDescriptions(): Record<string, string> {
    const descriptions: Record<string, string> = {};
    
    for (const [name, strategy] of this.strategies) {
      descriptions[name] = strategy.getDescription();
    }
    
    return descriptions;
  }

  // 私有方法

  private registerDefaultStrategies(): void {
    this.registerStrategy(new DealStatusFilterStrategy());
    this.registerStrategy(new RiskLevelFilterStrategy());
    this.registerStrategy(new AssetTypeFilterStrategy());
    this.registerStrategy(new DataSourceFilterStrategy());
    this.registerStrategy(new DateRangeFilterStrategy());
    this.registerStrategy(new VulTagFilterStrategy());
  }

  private getValueForStrategy(strategy: FilterStrategy, searchCondition: any): any {
    const fieldMap: Record<string, string> = {
      'dealStatus': 'dealStatus',
      'riskLevel': 'reseverity',
      'assetType': 'assetTypeName',
      'dataSource': 'syscode',
      'dateRange': 'dateRange',
      'vulTag': 'event_type_tag'
    };

    const field = fieldMap[strategy.name];
    return field ? searchCondition[field] : null;
  }
}

// 单例模式 - 全局筛选策略管理器
export const filterStrategyManager = new FilterStrategyManager();
