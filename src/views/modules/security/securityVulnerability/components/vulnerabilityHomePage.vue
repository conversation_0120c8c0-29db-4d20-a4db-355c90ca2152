<template>
  <div>
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs v-model="activeTabName" class="ml-2 mr-2" @tab-change="viewTabChangeHandler">
          <!-- 组织视图标签页 -->
          <el-tab-pane name="deptView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-TeamFill" />
                <span class="ml-1">组织视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索组织 -->
              <div class="pb-1.5 flex items-center">
                <el-input placeholder="组织名称" :suffix-icon="useRenderIcon('EP-Search')" clearable
                  v-model="deptKeyWord" />
                <el-icon @click="queryDeptTree" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示组织结构
              :load="loadNode"
                lazy
              -->
              <!-- <div>{{ state.deptData[0]['deptId'] }}</div> -->
              <el-tree ref="deptTree" :data="state.deptData" :props="treeProps" node-key="deptId"
                :render-after-expand="false" :expand-on-click-node="false" highlight-current
                :default-expanded-keys="expendTreeOrg" :filter-node-method="filterDeptNode" :style="treeStyle"
                @current-change="deptSelectChange">
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.vulCount || 0) > 0 ? 'red' : 'unset'
                  }">{{ node.label
                  }}<!--({{ data.eventCount || 0 }})--></span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
          <!-- 应用视图标签页 -->
          <el-tab-pane name="appView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-AppsFill" />
                <span class="ml-1">应用视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索应用 -->
              <div class="pb-1.5 flex items-center">
                <el-input placeholder="应用名称" :suffix-icon="useRenderIcon('EP-Search')" clearable v-model="appKeyWord" />
                <el-icon @click="loadAppData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示应用结构 -->
              <el-tree ref="appTreeRef" :data="appData" node-key="assetApp" :expand-on-click-node="false" :props="{
                label: 'assetApp'
              }" highlight-current default-expand-all :filter-node-method="filterAppNode" :style="treeStyle"
                @current-change="appChangeHandler">
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.count || 0) > 0 ? 'red' : 'unset'
                  }">{{ node.label
                  }}<!--({{ data.count || 0 }})--></span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 使用search-with-column组件，用于搜索事件数据 -->
        <search-with-column v-model="columnCondition.value" v-model:fuzzy-enable="columnCondition.fuzzyable"
          v-model:column-val="columnCondition.field" :column-options="columnSearchOptions" :column-select-width="90"
          @search="resetTablePageAndQuery('', '', true)" @reset="resetSearchHandler" class="flex-c w-full"
          input-class-name="w-1/2" />
        <!-- 风险标签 -->
        <div ref="vulTagRef" class="flex mt-6 mb-6">
          <span style="
              width: 5em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 10px;
              text-align: right;
            ">漏洞标签:</span>
          <!-- style="white-space: nowrap; width: 69rem;overflow: scroll;overflow-x: scroll;" -->
          <!-- <el-scrollbar ref="scrollbarRef" always> -->
          <!-- width: 69rem;white-space: nowrap; -->
          <div style="padding-bottom: 4px">
            <div ref="innerRef">
              <el-check-tag class="mr-2 tag-container" :checked="state.checkAll" @change="tagChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in eventTagData" :key="item.tagId"
                :checked="!state.checkAll && item.checked" class="mr-2" @change="tagChangeHandler(item)">
                {{ item.tagCount > 0 ? `${item.tagName}` : `${item.tagName}` }} {{ item.tagCount > 0 ? `(${item.tagCount})` : '' }}
              </el-check-tag>
            </div>
          </div>
          <!-- </el-scrollbar> -->
        </div>
        <!-- <el-slider style="width: 69rem;" v-model="value" :max="max" :format-tooltip="formatTooltip"
          @input="inputSlider" /> -->

        <el-tabs style="padding-left: 0.6rem; padding-right: 0.6rem" v-model="activeName" class="demo-tabs">
          <el-tab-pane label="资产视角" name="assetPerspective">
            <template #label>
              <img style="width: 1.5rem; height: 1.3rem" :src="assetPhoto" mode="scaleToFill" /><span
                style="line-height: 1.3rem">资产视角</span>
            </template>
            <!-- 表格 -->
            <div class="ml-2 mr-2">
              <im-table class="im-table-asset-view-table" ref="tableRef" :data="tableData" center toolbar :table-alert="{
                closable: false
              }" operator :height="tableOption.height" :stripe="tableOption.stripe" show-checkbox
                :column-storage="createColumnStorage('vul-asset', 'remote')" :pagination="tablePage"
                :loading="tableLoading" :filter-data-provider="filterDataProvider" @on-reload="resetTablePageAndQuery"
                @selection-change="selectionChangeHandler" @on-page-change="queryEventData">
                <!-- 自定义 alert 提示内容 -->
                <template #tip>
                  <span class="statistics-tip" style="margin-left: 100px">
                    <span class="statistics-item mr-2 ml-2" v-for="(item, index) in vulnerabilityStatisticsData"
                      :key="index">
                      <span class="label">{{ item.label }}: </span>
                      <span class="count text-[16px] font-bold ml-1">{{
                        item.value || 0
                      }}</span>
                    </span>
                  </span>
                </template>

                <!-- 表格右侧菜单 -->
                <template #toolbar-right="{ size }">
                  <div class="float-left flex-sc pr-3 gap-3">
                    <el-segmented v-model="state.dateRangeSign" :options="state.timeSegmentOptions" @change="timeSegmentChangeHandler">
                    </el-segmented>
                    <!-- 日期选择器，用于选择事件时间范围 -->
                    <el-date-picker clearable v-model="dateTimeRange" type="daterange" range-separator="到"
                      start-placeholder="开始日期" end-placeholder="结束日期" style="width: 200px"
                      @change="dateChangeFunction" />

                    <!-- 所属组织 -->
                    <!-- <el-tree-select clearable v-model="searchCondition.deptName" placeholder="所属组织" :data="deptNameData"
                      check-strictly :render-after-expand="false" style="width:160px" node-key="id"
                      @change="resetTablePageAndQuery" /> -->

                    <!-- 资产类别 -->
                    <el-tree-select clearable v-model="searchCondition.assetTypeName" placeholder="资产类别"
                      :data="assetTypeName" check-strictly :render-after-expand="false" style="width: 160px"
                      node-key="id" @change="assetTypeChangeHandler" />

                    <!-- 处置状态 -->
                    <!-- dealStatus -->
                    <el-select v-model="searchCondition.dealStatus" placeholder="处置状态" clearable collapse-tags
                      style="width: 160px" @change="dealStatusChangeHandler">
                      <el-option v-for="item in state.dsSelData" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>

                    <!-- 数据来源选择器 -->
                    <el-select v-model="searchCondition.syscode" placeholder="数据来源" clearable collapse-tags
                      multiple style="width: 160px" @change="syscodeChangeHandler">
                      <el-option v-for="item in state.syscodeData" :key="item" :label="item" :value="item" />
                    </el-select>

                    <!-- 风险级别选择器，用于选择事件风险级别 -->
                    <el-select v-model="searchCondition.reseverity" placeholder="漏洞级别" multiple collapse-tags clearable
                      style="width: 150px" @change="riskLevelChangeHandler">
                      <el-option v-for="item in riskLevelData" :key="item.id" :label="item.label" :value="item.id" />
                    </el-select>

                    <!-- <div>{{ state.selectedEvents }}</div> -->

                    <el-dropdown style="margin: 0 6px 0 10px">
                      <el-button type="primary"> 批量操作 </el-button>
                      <template #dropdown>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item :disabled="state.selectedEventRows.length == 0" style="padding: 0.4rem 1rem"
                            @click.native="
                              handleCommand('批量派单', '批量派单')
                              ">
                            批量派单
                          </el-dropdown-item>
                          <el-dropdown-item style="padding: 0.4rem 1rem" :disabled="state.selectedEventRows.length == 0"
                            @click="
                              openEditTaskDialog({
                                operationDataList: state.selectedEvents
                              })
                              " size="small" type="primary">批量验证</el-dropdown-item>
                          <!-- <el-dropdown-item :disabled="(state.selectedEventRows.length == 0)"
                            style="padding: 0.4rem 1rem;" @click.native="handleCommand('批量处置', 'moreFunction')">
                            批量处置
                          </el-dropdown-item> -->
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>

                    <!-- 导入 -->
                    <el-dropdown @command="handleCommand" placement="bottom">
                      <el-button
                        >导入<svg
                          style="
                            width: 17px;
                            height: 16px;
                            margin-bottom: 2px;
                            margin-left: 2px;
                            color: #666;
                          "
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 1024 1024"
                        >
                          <path
                            fill="currentColor"
                            d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z"
                          ></path>
                        </svg>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item
                            style="padding: 0.4rem 1rem"
                            v-for="item in importTemplate"
                            :key="item['type']"
                            :command="item['type']"
                            >{{ item["name"] }}</el-dropdown-item
                          >
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>

                    <!-- 导出事件数据 -->
                    <el-tooltip content="导出数据" placement="top" :open-delay="1000">
                      <el-button style="margin-left: 0.1rem" :icon="useRenderIcon('EP-Download')" circle :size="size"
                        :disabled="tableData.length == 0" @click="exportEventHandler" />
                    </el-tooltip>
                  </div>
                </template>
                <!-- 表格操作按钮 -->
                <template #operator="{ row, size }">
                  <!-- 处置按钮 -->
                  <!-- <el-button :size="size" :type="type" text :icon="useRenderIcon('EP-Checked')"
                    @click="eventDealHandler(row)" v-if="row.deal_status === '1'">
                    处置
                  </el-button> -->
                  <!-- 详情按钮 -->
                  <el-button :size="size" type="primary" text :icon="useRenderIcon('EP-View')"
                    @click="detailViewHandler(row)">
                    详情
                  </el-button>
                </template>
                <!-- 风险级别列 -->
                <template #vullevel="{ row }">
                  <!-- <div>{{ row }}</div> -->
                  <el-tag :color="getRiskLevelColor(row.vullevel + '')" class="text-white border-none">
                    {{ getRiskLevelLabel(row.vullevel + "") }}
                  </el-tag>
                </template>
                <!-- 事件处理状态列 -->
                <template #dealStatus="{ row }">
                  <!-- <div>{{ row }}</div> -->
                  <el-tag effect="light" :type="getDealStatusType(row.dealStatus)">
                    {{ getSegmentLabel(row.dealStatus) }}
                  </el-tag>
                </template>
                <!-- 源IP列 -->
                <!-- <template #src_ip="{ row }">
                  <el-popover placement="right-start" trigger="hover" width="270" @show="querySingleAsset(row.src_ip)">
                    <assets-popover-content :key="getAssetInfo(row.src_ip).key" :event-info="row"
                      :asset-info="getAssetInfo(row.src_ip).data" direction="src" @ip-block="ipBlockHandler" />
                    <template #reference>
                      <el-text class="text-primary">{{ row.src_asset_name }}
                      </el-text>
                    </template>
                  </el-popover>
                </template> -->
                <!-- 目标IP列 -->
                <!-- <template #dst_ip="{ row }">
                  <el-popover placement="right-start" trigger="hover" width="270">
                    <assets-popover-content :key="getAssetInfo(row.dst_ip).key" :event-info="row"
                      :asset-info="getAssetInfo(row.dst_ip).data" direction="dst" @ip-block="ipBlockHandler" />
                    <template #reference>
                      <el-text class="text-primary">{{ row.dst_asset_name }}
                      </el-text>
                    </template>
                  </el-popover>
                </template> -->

                <template #event_subtype_name="{ row }">
                  <el-tag>{{ row.event_subtype_name }}</el-tag>
                </template>
              </im-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="漏洞视角" name="vulnerabilityPerspective">
            <template #label>
              <img style="width: 1.5rem; height: 1.3rem" :src="vulPhoto" mode="scaleToFill" /><span
                style="line-height: 1.3rem">漏洞视角</span>
            </template>
            <assetLineDataDetails_vulView @getChangeTag="loadEventTagData" :heightTable="tableHeight"
              :triggerVulViewTable="triggerVulViewTable" @searchQuery="searchQuery" v-if="markTabChange"
              :markTabChange="markTabChange" />
          </el-tab-pane>
        </el-tabs>
      </template>
    </splitpane>

    <EventDispatchModal @success="queryEventData" :form="eventDispatchModalForm" :modelValue="eventDispatchModalVisable"
      @update:modelValue="eventDispatchModalVisable = false" />

    <el-drawer
      title="导入"
      size="45%"
      destroy-on-close
      append-to-body
      v-model="showImport"
    >
      <SecurityVulImportDrawer
        v-if="showImport"
        @refresh="queryEventData"
        :template-type="commandImportType"
      />
    </el-drawer>

    <!-- <event-deal-modal v-model:visible="deal.visible" :title="deal.title" :event-unit-ids="deal.unitIds"
      :default-action="deal.defaultAction" @deal-success="queryEventData" />
    <ip-block-modal v-model:visible="ipBlock.visible" title="IP封堵" :ip-address="ipBlock.ipAddress"
      :default-plug-label="ipBlock.defaultPlugLabel" /> -->
    <!-- 批量派单-->
    <!-- <event-dispatch-modal v-model="eventDispatchContext.dispatchVisible" :form="eventDispatchContext.dispatchForm"
      @success="resetTablePageAndQuery"></event-dispatch-modal> -->
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties
} from "vue";
import dayjs from "dayjs";
import { Refresh } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import EventDispatchModal from "@/views/modules/security/securityVulnerability/components/EventDispatchModal.vue";
import SecurityVulImportDrawer from "@/views/modules/security/securityVulnerability/components/SecurityVulImportDrawer.vue";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData,
  segmentData
} from "@/views/modules/security/securityVulnerability/util/vulnerability_data";

import {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  getBusinessSystemData,
  getUserDeptTreeAxios,
  queryAllCategoryAxios,
  queryEventDeptTree,
  getTagData,
  vulTotalByStatus,
  vulAddNewTaskForVulMethod,
  queryVulTableHeadGroup,
  getQuerySyscodeList
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityHomeInterface";
import { templateTypesMethod } from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface_vulView";
import assetLineDataDetails_vulView from "@/views/modules/security/securityVulnerability/components/assetLineDataDetails_vulView.vue";
import VulTaskEditor from "@/views/modules/security/securityVulnerability/components/batchVerification/VulTaskEditor.vue";
import { useRoute } from "vue-router";

const route = useRoute();
const tableRef = ref<ImTableInstance>();
import { ElScrollbar } from "element-plus";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { queryEventTableHeadGroup } from "@/views/modules/security/event/api/SecurityEventApi";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree>>();
const appTreeRef = ref<InstanceType<typeof ElTree>>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

const activeName = ref("assetPerspective");

const deptNameData = ref([]);

const assetTypeName = ref([]);

const assetPhoto = ref(
  `data:image/svg+xml;base64,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`
);
const vulPhoto = ref(
  `data:image/svg+xml;base64,PHN2ZyB0PSIxNzMzNzk3MjM5NDcyIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE1OTMwIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxNC4zNDY2NjcgNTA5LjQ0bS00MjYuNjY2NjY3IDBhNDI2LjY2NjY2NyA0MjYuNjY2NjY3IDAgMSAwIDg1My4zMzMzMzMgMCA0MjYuNjY2NjY3IDQyNi42NjY2NjcgMCAxIDAtODUzLjMzMzMzMyAwWiIgZmlsbD0iI0VEN0QzMSIgcC1pZD0iMTU5MzEiPjwvcGF0aD48cGF0aCBkPSJNNTE0LjM0NjY2NyA4NTAuNzczMzMzYTM0MS4zMzMzMzMgMzQxLjMzMzMzMyAwIDEgMSAzNDEuMzMzMzMzLTM0MS4zMzMzMzMgMzQxLjMzMzMzMyAzNDEuMzMzMzMzIDAgMCAxLTM0MS4zMzMzMzMgMzQxLjMzMzMzM3ogbTAtNjQwYTI5OC42NjY2NjcgMjk4LjY2NjY2NyAwIDEgMCAyOTguNjY2NjY2IDI5OC42NjY2NjcgMjk4LjY2NjY2NyAyOTguNjY2NjY3IDAgMCAwLTI5OC42NjY2NjYtMjk4LjY2NjY2N3oiIGZpbGw9IiNGQkFDNDgiIHAtaWQ9IjE1OTMyIj48L3BhdGg+PHBhdGggZD0iTTUxNC4zNDY2NjcgNzAxLjQ0YTE5MiAxOTIgMCAxIDEgMTkyLTE5MiAxOTIgMTkyIDAgMCAxLTE5MiAxOTJ6IG0wLTM0MS4zMzMzMzNhMTQ5LjMzMzMzMyAxNDkuMzMzMzMzIDAgMSAwIDE0OS4zMzMzMzMgMTQ5LjMzMzMzMyAxNDkuMzMzMzMzIDE0OS4zMzMzMzMgMCAwIDAtMTQ5LjMzMzMzMy0xNDkuMzMzMzMzeiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMTU5MzMiPjwvcGF0aD48cGF0aCBkPSJNMzQ1LjM4NjY2NyA0OTZtLTg1LjMzMzMzNCAwYTg1LjMzMzMzMyA4NS4zMzMzMzMgMCAxIDAgMTcwLjY2NjY2NyAwIDg1LjMzMzMzMyA4NS4zMzMzMzMgMCAxIDAtMTcwLjY2NjY2NyAwWiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMTU5MzQiPjwvcGF0aD48cGF0aCBkPSJNNjMxLjY4IDY4MC4xMDY2NjdtLTQyLjY2NjY2NyAwYTQyLjY2NjY2NyA0Mi42NjY2NjcgMCAxIDAgODUuMzMzMzM0IDAgNDIuNjY2NjY3IDQyLjY2NjY2NyAwIDEgMC04NS4zMzMzMzQgMFoiIGZpbGw9IiNGRkZGRkYiIHAtaWQ9IjE1OTM1Ij48L3BhdGg+PHBhdGggZD0iTTY3NC4zNDY2NjcgNDAwbS0zMiAwYTMyIDMyIDAgMSAwIDY0IDAgMzIgMzIgMCAxIDAtNjQgMFoiIGZpbGw9IiNGRkZGRkYiIHAtaWQ9IjE1OTM2Ij48L3BhdGg+PC9zdmc+`
);

/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value!.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};

// const loadEventEnum = async (stateKey, field: string) => {
//   const res = await getEventEnum(field);
//   if (res.data && res.data.length > 0) {
//     const dataArray = [];
//     res.data.forEach((item: string) =>
//       dataArray.push({
//         label: item,
//         value: item
//       })
//     );
//     state[stateKey] = dataArray;
//   }
// };

// 处置状态
// loadEventEnum("dsSelData", "dealStatus");

// 所属组织
// deptNameData
// const getUserDeptTreeMethod = async () => {
//   const res = await getUserDeptTreeAxios();
//   console.log(res['data']);
//   deptNameData.value = res['data'];
// }
// getUserDeptTreeMethod();

// 滚动条
// const max = ref(0)
// const value = ref(0)
// const innerRef = ref<HTMLDivElement>()
// const scrollbarRef = ref<InstanceType<typeof ElScrollbar>>()

//   const vulTagScroll = () => {
//   nextTick(() => {
//     nextTick(() => {
//       max.value = innerRef.value!.clientWidth - 380
//       // tableOption.height = tmpWidth;
//     })
//   })
// }

// const inputSlider = (value: number) => {
//   scrollbarRef.value!.setScrollLeft(value)
// }
// const scroll = ({ scrollLeft }) => {
//   value.value = scrollLeft
// }
// const formatTooltip = (value: number) => {
//   return `${value} px`
// }

// 打开编辑任务窗口

//编辑任务 Ref
const editTaskRef = ref<InstanceType<typeof VulTaskEditor>>();
const openEditTaskDialog = (t: any) => {
  console.log(t);
  vulAddNewTaskForVulMethod(t)
    .then(res => {
      $message({
        type: "success",
        message: "开始验证"
      });
      resetTablePageAndQuery();
    })
    .catch(err => {
      $message({
        type: "error",
        message: "验证失败"
      });
    });
  // addDialog({
  //   title: "漏洞探测新建任务",
  //   width: "80%",
  //   fullscreenIcon: true,
  //   closeOnClickModal: false,
  //   props: { taskInfo: t },
  //   contentRenderer: () =>
  //     h(VulTaskEditor, {
  //       ref: editTaskRef,
  //       onSuccess: () => {
  //         closeAllDialog();
  //         // loadTaskData();
  //       }
  //     }),
  //   beforeSure(done) {
  // editTaskRef.value.submitData();
  //   }
  // });
};

// 处理树形选择器数据
const buildTree = (
  data: any[],
  deptId: string | number,
  parentId: string | number
) => {
  const map = new Map();
  const rootNodes = [];
  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, label: item["name"], children: [] });
  });
  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      } else {
        // 如果找不到parentId，则将该节点作为顶级父节点
        rootNodes.push(map.get(item[deptId]));
      }
    }
  });

  return rootNodes;
};
// 资产类别
// queryAllCategoryAxios
const queryAllCategoryAxiosMethod = async () => {
  const res = await queryAllCategoryAxios();
  console.log(buildTree(res["data"], "id", "parentId"));

  assetTypeName.value = [...buildTree(res["data"], "id", "parentId")];
};
queryAllCategoryAxiosMethod();

// 数据来源
const loadSyscodeData = async () => {
  try {
    const res = await getQuerySyscodeList() as any;
    if (res.status === "0" && res.data && Array.isArray(res.data)) {
      state.syscodeData = res.data;
    }
  } catch (error) {
    console.error("加载数据来源失败:", error);
  }
};
loadSyscodeData();

const expendTreeOrg = ref([]);
const queryDeptTree = () => {
  state.deptData = [];
  // 清空关键字
  state.deptKeyWord = null;
  queryEventDeptTree().then(res => {
    // console.log("queryEventDeptTree", res.data);
    const resDeptData = Array.isArray(res.data)
      ? [...buildTree(res.data, "deptId", "parentId")]
      : [res.data || {}];
    state.deptData = [
      {
        deptiD: "",
        deptName: "全部",
        children: resDeptData
      }
    ];

    expendTreeOrg.value.splice(
      0,
      expendTreeOrg.value.push.length,
      ...["", "0", "-1"]
    );
    // if (
    //   state.deptData &&
    //   state.deptData[0] &&
    //   state.deptData[0]["deptId"] &&
    //   state.deptData[0]["deptId"].length > 0
    // ) {
    //   expendTreeOrg.value.push(state.deptData[0]["deptId"]);
    // }

    // state.deptData =[];
    // loadEventTagData();
    deptTree.value.setCurrentKey(null);
    deptSelectChange({ deptId: "" });
    queryEventData();
  });
};

//首层组织数据 计算属性
const firstLevelDeptData = computed(() => {
  return parseFirstLevelDept(
    useDeptStoreHook()?.deptData.filter(
      (d: SimpleDeptInfo) => d.parentId === "-1"
    )
  );
});

//数据对象
const state = reactive({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "deptView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "assetName",
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateRangeSign: "1d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    orgId: "",
    asset_app_name: "",
    syscode: [] as string[],
    dealStatus: "",
    reseverity: [] as string[],
    assetTypeName: "",
    event_type_tag: null
    // dealWith: "1",
    // model: "event"
  },
  // "conditions": [
  //   { "field": "assetName", "fuzzyable": true, "operator": "fuzzy", "value": "name" },
  //   { "field": "ip", "fuzzyable": true, "operator": "fuzzy", "value": "ip" },
  //   { "field": "refOrgNameTree", "fuzzyable": false, "value": "zXCu-EN-Pn_2CISvf9V" },
  //   { "field": "assetTypeName", "fuzzyable": false, "value": 259 },
  //   { "field": "refsys", "fuzzyable": true, "operator": "fuzzy", "value": "system" },
  //   { "field": "vullevel", "fuzzyable": false, "value": "4,3,2" },
  //   { "field": "dealStatus", "fuzzyable": false, "value": "1" }
  // ]
  columnSearchOptions: [],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: [
    {
      value: "undisposed",
      label: "未修复"
    },
    {
      value: "disposalof",
      label: "已修复"
    }
  ],

  // 数据来源选项数据
  syscodeData: [],

  vulnerabilityStatisticsData: [
    {
      label: "漏洞总数",
      code: "total",
      value: 0
    },
    {
      label: "未修复数",
      code: "undisposed",
      value: 0
    },
    {
      label: "已修复数",
      code: "disposalof",
      value: 0
    },
    {
      label: "无需处理数",
      code: "noNeedHandle",
      value: 0
    }
  ],

  filters: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  vulnerabilityStatisticsData,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const eventDispatchContext = reactive({
  dispatchVisible: false,
  dispatchForm: {
    alertIds: "",
    userId: [],
    title: "",
    deptId: ""
  }
});

// 导入相关状态
const showImport = ref(false);
const commandImportType = ref("");
const importTemplate = ref([]);

const markTabChange = ref(false);
watch(activeName, val => {
  // activeName.value = val;
  if (val == "assetPerspective") {
    state.columnSearchOptions = [];
    // state.columnSearchOptions = [
    //   {
    //     label: "资产名称",
    //     value: "assetName",
    //   },
    //   {
    //     label: "资产IP",
    //     value: "ip"
    //   },
    //   {
    //     label: "业务系统",
    //     value: "refsys"
    //   }
    // ]
    state.columnCondition.value = "";
    state.columnCondition.field = "assetName";
    markTabChange.value = false;
    queryEventData();
  } else {
    state.columnSearchOptions = [];
    state.columnCondition.value = "";
    state.columnCondition.field = "";
    markTabChange.value = true;
  }
});

const searchQuery = val => {
  state.columnSearchOptions = val;
  state.columnCondition.field = val[0]["value"];
};

// const unDealEvents = computed(() => {
//   return state.selectedEventRows.filter(
//     selectedEvent =>
//       selectedEvent["deal_status"] == "1" && !selectedEvent["wo_id"]
//   );
// });

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

// 监听关键筛选条件变化，自动标记需要检查标签有效性
watch(
  [
    () => state.searchCondition.dealStatus,
    () => state.searchCondition.reseverity,
    () => state.searchCondition.assetTypeName,
    () => state.searchCondition.syscode,
    () => state.dateRangeSign,
    () => state.dateTimeRange
  ],
  () => {
    // 只有当前有选中标签时才需要检查
    if (state.searchCondition["event_type_tag"]) {
      needCheckTagValidity.value = true;
    }
  },
  { deep: true }
);

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const vulTagRef = ref(null);
const tableKey = ref(0);
const vulTagHeight = () => {
  nextTick(() => {
    nextTick(() => {
      tmpWidth.value = vulTagRef.value.offsetHeight;
      console.log(tmpWidth.value);
      tableHeight.value =
        document.documentElement.offsetHeight - 380 - tmpWidth.value;
      tableOption.height = tableHeight.value;
      // tableOption.value = document.documentElement.offsetHeight - 400 - tmpWidth.value;
      console.log(tableOption);
      if (tableKey.value == 0) {
        queryEventData();
      }
      tableKey.value = tableKey.value + 1;
    });
  });
};

//根据页面高度设置表格高度
// const tableHeight = computed(() => {
//   console.log(document.documentElement.offsetHeight - 400);
//   return document.documentElement.offsetHeight - 400 - tmpWidth.value;
// });
const tableHeight = ref(0);

// const tableOption = computed(() => {
//   return {
//     align: "center",
//     menuAlign: "center",
//     border: true,
//     stripe: true,
//     selection: true,
//     addBtn: false,
//     editBtn: false,
//     delBtn: false,
//     menuWidth: 130,
//     height: tableHeight.value,
//     rowKey: "ip",
//     column: []
//   }
// })
const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: tableHeight.value,
  rowKey: "ip",
  column: []
});

const dateChangeFunction = query => {
  state.dateRangeSign = null;
  if (!query) {
    state.dateRangeSign = "1d";
  }
  resetTablePageAndQuery();
};

//加载组织树节点数据
// const loadNode = (node, resolve) => {
//   if (node.isLeaf) return resolve([]);
//   resolve(useDeptStoreHook().getChildren(node.data.deptId));
// };

const treeStyle = computed((): CSSProperties => {
  return {
    height: tableHeight.value + 80 + "px",
    overflow: "auto"
  };
});

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (!data.deptId) {
    return true;
  }

  return data.deptName.indexOf(value) > -1;
};

//应用数据搜索
const filterAppNode = (value: string, data: any) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (data.assetApp == "全部") return true;

  return data.assetApp.indexOf(value) > -1;
};

//组织树选择改变触发
const deptSelectChange = (data: any) => {
  if (data && data.deptId) {
    state.searchCondition.orgId = data.deptId;
  } else {
    state.searchCondition.orgId = "";
  }

  triggerVulViewTable["orgId"] = state.searchCondition.orgId;
  triggerVulViewTable["assetApp"] = "";
  triggerVulViewTable["vulType"] = "";
  state.searchCondition["event_type_tag"] = "";
  state.checkAll = true;
  resetTablePageAndQuery();
};

//应用选择改变
const appChangeHandler = (data: any) => {
  if (!data) {
    // console.log("aaaaaaaaaa")
    appTreeRef.value.setCurrentKey(null);
  }

  state.searchCondition.asset_app_name = data?.assetApp;
  if (state.searchCondition.asset_app_name == "全部") {
    state.searchCondition.asset_app_name = "";
  }

  triggerVulViewTable["assetApp"] = state.searchCondition.asset_app_name;
  triggerVulViewTable["orgId"] = "";
  triggerVulViewTable["vulType"] = "";
  state.searchCondition["event_type_tag"] = "";
  state.checkAll = true;
  resetTablePageAndQuery();
};

//视图标签改变触发
const viewTabChangeHandler = (activeName: string) => {
  console.log(activeName);
  if (activeName === "deptView") {
    //处理组织视图初始化
    state.searchCondition.asset_app_name = "";
    state.searchCondition.orgId = "";
    nextTick(() => {
      if ("-1" == deptTree.value!.getCurrentKey()) {
        state.searchCondition.orgId = "-1";
        resetTablePageAndQuery();
      }
      deptTree.value!.setCurrentKey("-1");
      queryDeptTree();
    });
    // loadAppData();
    resetTablePageAndQuery();
  } else if (activeName === "appView") {
    //处理应用视图初始化
    state.searchCondition.asset_app_name = "";
    state.searchCondition.orgId = "";
    // state.deptData = [];
    if (state.appData && state.appData.length > 0) {
      const firstData = state.appData[0];
      nextTick(() => {
        if (firstData.assetApp == appTreeRef.value!.getCurrentKey()) {
          state.searchCondition.asset_app_name = firstData.assetApp;
          resetTablePageAndQuery();
        }
        appTreeRef.value!.setCurrentKey(firstData.assetApp);
      });
    }
  }
};

//加载业务系统数据
const loadAppData = async () => {
  // console.log("aaa")
  const appRes = await getBusinessSystemData();
  state.appData = [
    {
      assetApp: "全部",
      children: appRes.data
    }
  ];
  // console.log(state.appData)
  appChangeHandler(null);
};
loadAppData();

const triggerVulViewTable = reactive({
  number: "1",
  query: {},
  orgId: "",
  assetApp: "",
  vulType: "",
  toggleQueryCriteria: ""
});
//重置分页后查询事件数据
const resetTablePageAndQuery = (
  info = "",
  toggleQueryCriteria = "",
  keepFiltersFlag?: boolean
) => {
  console.log(activeName.value);
  if (activeName.value == "assetPerspective") {
    if (keepFiltersFlag !== true) {
      // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
      state.filters = [];
      tableRef.value.clearAllFilters();
    }
    state.tablePage.currentPage = 1;
    queryEventData();
  } else {
    triggerVulViewTable["number"] = Math.random() * Math.random() + "";
    if (info != "漏洞重置") {
      triggerVulViewTable["query"] = {
        field: columnCondition.value.field,
        fuzzyable: columnCondition.value.fuzzyable,
        operator: state.columnCondition.operator,
        value: columnCondition.value.value
      };
      triggerVulViewTable["orgId"] = state.searchCondition.orgId;
      triggerVulViewTable["assetApp"] = state.searchCondition.asset_app_name;
      triggerVulViewTable["vulType"] =
        state.searchCondition?.event_type_tag?.tagName;
      if (toggleQueryCriteria == "toggleQueryCriteria") {
        triggerVulViewTable["toggleQueryCriteria"] = "toggleQueryCriteria";
      } else {
        triggerVulViewTable["toggleQueryCriteria"] = "";
      }
    } else {
      triggerVulViewTable["query"] = {};
      triggerVulViewTable["orgId"] = "";
      triggerVulViewTable["assetApp"] = "";
      triggerVulViewTable["vulType"] = "";
      triggerVulViewTable["toggleQueryCriteria"] = "";
      state.columnCondition.field = "vulName";
      if (toggleQueryCriteria == "toggleQueryCriteria") {
        triggerVulViewTable["toggleQueryCriteria"] = "toggleQueryCriteria";
      } else {
        triggerVulViewTable["toggleQueryCriteria"] = "";
      }
    }

    // console.log(triggerVulViewTable['number'])
  }
};

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange;
  if (state?.dateRangeSign) {
    dateRange = state?.dateRangeSign;
  } else {
    if (state?.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state?.dateTimeRange?.[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state?.dateTimeRange?.[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

const searchTmpData = tmpConditions => {
  // if (state.searchCondition['reseverity'] && state.searchCondition['reseverity'].length > 0) {
  //   tmpConditions.push({
  //     "field": "vullevel",
  //     "fuzzyable": columnCondition.value.fuzzyable,
  //     "value": state.searchCondition['reseverity'].join(',')
  //   })
  // }
  // console.log(state.searchCondition['dealStatus'])
  // if (state.searchCondition['dealStatus']) {
  //   tmpConditions.push({
  //     "field": "dealStatus",
  //     "fuzzyable": columnCondition.value.fuzzyable,
  //     "value": state.searchCondition['dealStatus']
  //   })
  // }
  try {
    if (state?.searchCondition?.["deptName"]) {
      tmpConditions.push({
        field: "refOrgNameTree",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["deptName"]
      });
    }
  } catch (error) {

  }
  try {
    if (state?.searchCondition?.["assetTypeName"]) {
      tmpConditions.push({
        field: "assetTypeName",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["assetTypeName"]
      });
    }
  } catch (error) {

  }

  // 数据来源筛选条件
  try {
    if (state?.searchCondition?.["syscode"] && state.searchCondition["syscode"].length > 0) {
      tmpConditions.push({
        field: "syscode",
        fuzzyable: false,
        value: state.searchCondition["syscode"].join(",")
      });
    }
  } catch (error) {

  }

};

// 查询漏洞统计数据
const queryVulnerabilityStatisticsData = queryParams => {
  vulTotalByStatus(queryParams).then((res: any) => {
    let resData = res.data || {};
    for (let item of state.vulnerabilityStatisticsData) {
      let { code } = item;
      item.value = resData[code] || 0;
    }
  });
};

const buildQueryCondition = () => {
  const tmpConditions = [];
  searchTmpData(tmpConditions);
  return {
    //查询条件
    conditions: state?.columnCondition.value
      ? [state?.columnCondition, ...tmpConditions]
      : [...tmpConditions],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    // ...state.searchCondition,
    dealStatus: state?.searchCondition["dealStatus"],
    //当前页码
    pageNum: state?.tablePage?.currentPage,
    //每页显示条数
    pageSize: state?.tablePage?.pageSize,
    orgId: state?.searchCondition?.orgId,
    assetApp: state?.searchCondition?.asset_app_name,
    vulLevel:
      state?.searchCondition?.["reseverity"] &&
      state?.searchCondition?.["reseverity"].join(","),
    vulType:
      state?.searchCondition?.["event_type_tag"] &&
      state?.searchCondition?.["event_type_tag"]?.tagName,

    // 视角标识
    viewType: "asset",
    // 列头过滤器
    headerFilter: {
      filters: state?.filters
    }
  };
};

//查询事件数据
const queryEventData = async () => {
  state.selectedEventRows = [];
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    // 查询条件
    const queryParams = buildQueryCondition();
    console.log("queryParams=========================>", queryParams);

    // 查询统计数据(异步)
    queryVulnerabilityStatisticsData(queryParams);

    //根据条件查询事件列表
    const res = await vulDetailByAssetMethod(queryParams);
    //设置表格数据
    state.tableData = res["data"].rows;

    // 处理动态表头
    tableOption.column.length = 0;
    res["data"]["columns"].forEach(item => {
      // console.log(res['data']['showList'].includes(item['field']));
      if (res["data"]["showList"].includes(item["field"])) {
        tableOption.column.push({
          hide: item["field"] == "notHandledYet",
          prop: item["field"],
          label: item["name"],
          fieldType: item["fieldType"],
          width:
            item["name"] == "最新发现时间" ||
              item["name"] == "首次发现时间" ||
              item["name"] == "解决方案" ||
              item["name"] == "漏洞名称" ||
              item["name"] == "资产名称"
              ? item["name"] == "解决方案"
                ? 300
                : 200
              : "",
          unit: item["unit"],
          component: item["component"],
          filters: true,
          sortable: true
        });
      }
    });

    const tmpQuerySearch = [];
    res["data"]["columns"].forEach(item => {
      const obj = { ...item["component"] };
      if (!("disableSearch" in obj)) {
        if (item["component"]["type"] == "Input") {
          console.log(
            `item["field"] == 'vulLevel' ? 'Select' : "Input"`,
            item["field"]
          );
          tmpQuerySearch.push({
            value: item["field"],
            label: item["name"],
            type: item["field"] == "vulLevel" ? "Select" : "Input"
          });
        }
      }
    });
    columnSearchOptions.value = tmpQuerySearch;

    // 查询关联的资产
    // queryAssetData(state.tableData);
    //设置表格总条数
    state.tablePage.total = res["data"].totalElements;
    //加载标签数据
    await loadEventTagData();
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};

// const queryAssetData = rows => {
//   let ipList = [];
//   for (let row of rows) {
//     let { src_ip, dst_ip } = row;
//     if (src_ip && !ipList.includes(src_ip)) {
//       ipList.push(src_ip);
//       state.assetByIpMap[src_ip] = {
//         key: 1
//       };
//     }
//     if (dst_ip && !ipList.includes(dst_ip)) {
//       ipList.push(dst_ip);
//       state.assetByIpMap[dst_ip] = {
//         key: 1
//       };
//     }
//   }
//   state.assetByIpMap = {};
//   queryAssetsByIps(ipList.join(","))
//     .then(res => {
//       let assets = res['data'] || [];
//       for (let asset of assets) {
//         // console.log("asset ", asset);
//         // state.assetByIpMap
//         let { ipAddress } = asset;
//         state.assetByIpMap[ipAddress] = {
//           key: ipAddress,
//           data: asset
//         };
//       }
//     })
//     .finally(() => {
//     });
// };

// const getAssetInfo = ip => {
//   let assetInfo = state.assetByIpMap[ip];
//   return (
//     assetInfo || {
//       key: 1
//     }
//   );
// };

// 查询当个ip绑定的资产（防止重复查询）
// const querySingleAsset = ipAddress => {
//   let assetInfo = state.assetByIpMap[ipAddress];
//   if (assetInfo && assetInfo.data) {
//     return;
//   }
//   // 防止重复查询
//   state.assetByIpMap[ipAddress] = {
//     key: 1,
//     data: {}
//   };
//   queryAssetsByIps(ipAddress)
//     .then(res => {
//       let assets = res['data'] || [];
//       if (assets.length > 0) {
//         state.assetByIpMap[ipAddress] = {
//           key: ipAddress,
//           data: assets[0]
//         };
//       }
//     })
//     .finally(() => {
//     });
// };

//加载标签数据
const loadEventTagData = async (vulViewParams = null) => {
  dealFuzzEnable();

  let condition;

  if (vulViewParams) {
    // 如果是漏洞视角传递的参数，直接使用（已经排除了vulType）
    condition = vulViewParams;
  } else {
    // 资产视角：构建与表格查询一致的筛选条件，但排除vulType参数
    const tmpConditions = [];
    searchTmpData(tmpConditions);

    condition = {
      //查询条件 - 与表格查询保持一致
      conditions: state?.columnCondition.value
        ? [state?.columnCondition, ...tmpConditions]
        : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      //搜索条件 - 与表格查询保持一致，但排除vulType
      dealStatus: state?.searchCondition["dealStatus"],
      orgId: state?.searchCondition?.orgId,
      assetApp: state?.searchCondition?.asset_app_name,
      vulLevel:
        state?.searchCondition?.["reseverity"] &&
        state?.searchCondition?.["reseverity"].join(","),
      // 注意：这里不包含vulType参数，避免逻辑冲突

      // 视角标识
      viewType: "asset",
      // 列头过滤器
      headerFilter: {
        filters: state?.filters
      }
    };
  }

  //查询数据
  const tagRes = await getTagData(condition);
  const resData = tagRes.data || [];

  // 检查当前选中的标签是否还在新的标签列表中
  let shouldResetTag = false;

  // 检查标签有效性的条件：
  // 1. 资产视角：标记需要检查时且有选中标签
  // 2. 漏洞视角：传递了参数且有选中标签
  const shouldCheckTag =
    (needCheckTagValidity.value && state.searchCondition["event_type_tag"]) ||
    (vulViewParams && state.searchCondition["event_type_tag"]);

  if (shouldCheckTag) {
    //查找已选中的标签
    const matchData = resData.find(
      (item: any) =>
        item.tagId === state.searchCondition?.["event_type_tag"].tagId
    );
    if (!matchData) {
      // 如果当前选中的标签在新的标签列表中不存在，标记需要重置
      shouldResetTag = true;
    }
  }

  // 处理标签选中状态
  if (state.searchCondition["event_type_tag"] && !shouldResetTag) {
    //查找已选中的标签并设置选中状态
    const matchData = resData.find(
      (item: any) =>
        item.tagId === state.searchCondition?.["event_type_tag"].tagId
    );
    if (matchData) {
      matchData.checked = true;
    }
  }

  //设置标签数据
  state.eventTagData = resData;

  // 如果需要重置标签，自动切换到"全部"并重新加载数据
  if (shouldResetTag) {
    state.searchCondition["event_type_tag"] = null;
    triggerVulViewTable["vulType"] = "";
    state.checkAll = true;
    console.log("当前选中的漏洞标签在新的筛选条件下不存在，已自动重置为'全部'");

    // 重置标记
    needCheckTagValidity.value = false;

    // 重新加载表格数据
    if (activeName.value === "assetPerspective") {
      // 资产视角：直接重新查询表格数据
      queryEventData();
      return; // 提前返回，避免重复设置标签数据
    } else {
      // 漏洞视角：触发漏洞视角表格重新加载
      triggerVulViewTable["number"] = Math.random() * Math.random() + "";
      triggerVulViewTable["query"] = {
        field: columnCondition.value.field,
        fuzzyable: columnCondition.value.fuzzyable,
        operator: state.columnCondition.operator,
        value: columnCondition.value.value
      };
      triggerVulViewTable["orgId"] = state.searchCondition.orgId;
      triggerVulViewTable["assetApp"] = state.searchCondition.asset_app_name;
      triggerVulViewTable["vulType"] = "";
    }
  }

  // 重置检查标记
  needCheckTagValidity.value = false;

  vulTagHeight();
  // triggerVulViewTable['vulType'] = "";
  // state.checkAll = true;
  // 全部数量
  state.totalTagCount =
    resData.length == 0
      ? 0
      : resData
        .map(r => r?.tagCount || 0)
        .reduce((c1, c2) => {
          return c1 + c2;
        });
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

// 标记是否需要检查标签有效性
const needCheckTagValidity = ref(false);

//风险标签选中改变
const tagChangeHandler = (tag: any) => {
  console.log(tag);
  state.searchCondition["event_type_tag"] = tag ? tag : "";
  triggerVulViewTable["vulType"] =
    state.searchCondition["event_type_tag"]?.tagName;
  state.checkAll = !tag;
  state.eventTagData.forEach((item: any) => {
    item.checked = item.tagId === tag?.tagId;
  });
  resetTablePageAndQuery();
};

//处置状态改变
// const segmentChangeHandler = () => resetTablePageAndQuery();

//日期范围改变触发
// const dateRangeChangeHandler = (range: Array<Date>) => {
//   resetTablePageAndQuery();
// };

//风险级别改变触发
const riskLevelChangeHandler = (level: string) => {
  resetTablePageAndQuery();
};

//数据来源改变触发
const syscodeChangeHandler = (selectedValues: string[]) => {
  console.log("数据来源选择变化:", selectedValues);
  resetTablePageAndQuery();
};

//资产类别改变触发
const assetTypeChangeHandler = (selectedValue: string) => {
  console.log("资产类别选择变化:", selectedValue);
  resetTablePageAndQuery();
};

//处置状态改变触发
const dealStatusChangeHandler = (selectedValue: string) => {
  console.log("处置状态选择变化:", selectedValue);
  resetTablePageAndQuery();
};

//时间段选择器改变触发
const timeSegmentChangeHandler = () => {
  dateTimeRange.value = [];
  resetTablePageAndQuery();
};

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "assetName",
    fuzzyable: true,
    operator: "fuzzy"
  };
  //重置标签选择状态
  state.eventTagData.forEach((item: any) => (item.checked = false));
  state.searchCondition["event_type_tag"] = null;
  triggerVulViewTable["vulType"] = "";
  state.checkAll = true;
  //重置处置状态
  state.searchCondition["dealWith"] = "1";
  //重置时间范围
  state.dateTimeRange = [];
  state.dateRangeSign = "1d";
  //重置风险级别
  state.searchCondition["reseverity"] = [];
  state.searchCondition["deptName"] = "";
  state.searchCondition["assetTypeName"] = "";
  state.searchCondition["dealStatus"] = "";
  state.searchCondition["syscode"] = [];
  resetTablePageAndQuery("漏洞重置", "toggleQueryCriteria");
};

//查看事件详情触发
const detailViewHandler = (evt: any) => {
  emit("event-select", evt);
  let needsPassedData = {};
  needsPassedData["timeSelect"] = state.dateRangeSign;
  needsPassedData["dealStatus"] = state.searchCondition["dealStatus"];
  needsPassedData["reseverity"] = state.searchCondition["reseverity"];
  // if (state.dateRangeSign != '' && state.dateRangeSign != '1d') {
  //   needsPassedData['timeSelect'] = state.dateRangeSign;
  // }
  console.log(state.searchCondition["reseverity"]);
  switch (evt?.["dealStatus"]) {
    case "未修复":
      needsPassedData["dealStatus"] = "undisposed";
      break;
    case "已修复":
      needsPassedData["dealStatus"] = "disposalof";
      break;
    case "无需处理":
      needsPassedData["dealStatus"] = "noNeedHandle";
    default:
      break;
  }
  jumpTo("assetLineDataInfo", needsPassedData);
};

//事件处置触发-单条
const eventDealHandler = (evt: any) => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "";
  state.deal.unitIds = [evt.ip];
  state.deal.visible = true;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.ip));
  state.selectedEvents = selectIds;
  state.selectedEventRows = selRows;
};

//批量误报触发
// const batchFalsePositivesHandler = () => {
//   state.deal.title = "批量事件处置";
//   state.deal.defaultAction = "人工误报";
//   state.deal.unitIds = state.selectedEvents;
//   state.deal.visible = true;
// };

// 批量派单
// const batchDispatchList = () => {
//   eventDispatchContext.dispatchVisible = true;
//   eventDispatchContext.dispatchForm = {
//     alertIds: unDealEvents.value
//       .filter(unDealEvent => !!unDealEvent.ip)
//       .map(unDealEvent => unDealEvent.ip)
//       .join(",")
//   };
// };

//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    const tmpConditions = [];
    searchTmpData(tmpConditions);
    await importVulDetailByAssetMethod({
      //查询条件
      conditions: state.columnCondition.value
        ? [state.columnCondition, ...tmpConditions]
        : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      //搜索条件
      // ...state.searchCondition,
      dealStatus: state.searchCondition["dealStatus"],
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize,
      orgId: state.searchCondition.orgId,
      assetApp: state.searchCondition.asset_app_name,
      vulLevel:
        state.searchCondition["reseverity"] &&
        state.searchCondition["reseverity"].join(","),
      vulType:
        state.searchCondition["event_type_tag"] &&
        state.searchCondition["event_type_tag"].tagName
    });
  });
};

//IP地址封堵触发
// const ipBlockHandler = (ip: string) => {
//   state.ipBlock.ipAddress = ip;
//   state.ipBlock.visible = true;
// };

// 导入种类
const templateTypesAxios = async () => {
  const res = await templateTypesMethod();
  importTemplate.value = res["data"];
};

//挂载后初始化
onMounted(() => {
  // 接收路由传递的参数，这里简单处理下
  if (route.query.dateRange == "") {
    state.dateRangeSign = "";
  } else {
    state.dateRangeSign = (route.query.dateRange || "1d") as string;
  }

  // initTimeRange();
  // 查询事件组织树
  queryDeptTree();
  // queryEventData();
  // 加载导入模板类型
  templateTypesAxios();
});

const eventDispatchModalVisable = ref(false);
const eventDispatchModalForm = reactive({
  title: "",
  deptId: "",
  userId: [],
  params: "",
  currentView: "assetView"
});
const initEventDispatchModalForm = () => {
  eventDispatchModalForm.title = "";
  eventDispatchModalForm.deptId = "";
  eventDispatchModalForm.userId = [];
  eventDispatchModalForm.params = "";
  eventDispatchModalForm.currentView = "assetView";
};
const handleCommand = (command, info = "", onlyOne = "") => {
  initEventDispatchModalForm();
  console.log(command, state.selectedEventRows);
  if (info == "批量派单") {
    eventDispatchModalVisable.value = true;
    const tmpArray = state.selectedEventRows.map(item => {
      return item["ip"];
    });
    eventDispatchModalForm.params = tmpArray.join();
    console.log(eventDispatchModalForm.params);
    return;
  }
  // 处理导入命令
  showImport.value = true;
  commandImportType.value = command;
};

const filterDataProvider: TableFilterDataProvider = {
  options: (prop, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryVulTableHeadGroup(query)
        .then(res => {
          console.log(res);
          resolve(res.data);
        })
        .catch(err => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery("", "", true);
  }
};

//跳转
const jumpTo = (sign: string, needsPassedData: Object) => {
  emit("jump-to", sign, needsPassedData);
};
</script>

<style lang="scss" scoped>
:deep(.im-table-asset-view-table){
  .im-table-toolbar{
    padding-top: 3px;
  }
}
</style>