<template>
  <el-dialog v-model="state.visible" :before-close="beforeClose" :destroy-on-close="true" width="50%"
    :close-on-click-modal="false" center title="漏洞派单">
    <el-form ref="formRef" :model="form" size="default" label-position="top" style="padding: 20px 0">
      <el-form-item label="工单标题" prop="title" :rules="[{ required: true, message: '请输入工单标题' }]">
        <el-input v-model="form.title" placeholder="请输入工单标题"></el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="组织" prop="deptId" :rules="[{ required: true, message: '请选择组织' }]">
            <dept-tree-select v-model:dept-id="form.deptId" clearable filterable custom-style="width: 100%;"
              placeholder="请选择组织" default-expand-root @change="loadUsers(form.deptId)">
            </dept-tree-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="受理人" prop="userId" :rules="[{ required: true, message: '请选择受理人' }]">
            <el-tree-select v-model="form.userId" multiple filterable clearable :props="{}" node-key="id"
              :data="state.userTreeData" placeholder="请选择受理人"></el-tree-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button :loading="state.submitLoading" type="primary" @click="handleOk">提交</el-button>
        <el-button @click="beforeClose">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { ElMessage, ElTree, FormInstance } from "element-plus";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import {
  // dispatchEventProcess,
  queryRoleUsers
} from "@/views/modules/security/event/api/SecurityEventApi";
import {
  dispatchEventProcess,
  countFraAssetIp
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface_vulView";
const formRef = ref<FormInstance>();
const props = defineProps({
  modelValue: Boolean,
  form: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emits = defineEmits(["update:modelValue", "success"]);
const state = reactive({
  visible: false,
  submitLoading: false,
  userTreeData: [],
  defaultExpandedDeptIds: []
});
const beforeClose = () => {
  if (state.submitLoading) {
    ElMessage.info("正在派单，请等待结果返回再关闭");
    return;
  }
  emits("update:modelValue", (state.visible = false));
};

// 统一派单
const requestCountFraAssetIpDispatchEventProcess = (mark: String) => {
  let params = {};
  if (mark == 'assetView') {
    params = {
      "ips": props.form.params,
      "status": "undisposed",
      "dispatchStatus": "未派单"
    };
  } else {
    params = {
      vulIds: props.form.params
    }
  }
  countFraAssetIp(params).then(res => {
    // console.log(res['data']);
    if(res?.['data'].length == 0){
      ElMessage.info("所选IP对应漏洞ID未查询到，查看是否已经派单");
      state.submitLoading = false;
      return ;
    }
    const tmpParams = res['data'].map(item => {
      return {
        businessId: item['vulId'],
        capitalId: item['ip']
      }
    })
    // console.log(tmpParams);

    dispatchEventProcess({
      ...props.form,
      params: JSON.stringify(tmpParams)
    })
      .then(res => {
        let { code, msg } = res;
        if (code == 200) {
          ElMessage.success("派单成功 ");
          emits("success");
          state.submitLoading = false;
          beforeClose();
        } else {
          ElMessage.error("派单失败: " + msg);
        }
      })
      .catch(err => {
        console.error(err);
        ElMessage.error("提交失败: " + err);
      })
      .finally(() => {
        state.submitLoading = false;
      });
  }).catch(err => {
    console.error(err);
    ElMessage.error("提交失败: " + err);
  })
}

const handleOk = () => {
  formRef.value.validate(ok => {
    if (ok) {

      if (state.submitLoading) return;
      state.submitLoading = true;

      if (Array.isArray(props.form.params)) {
        dispatchEventProcess({
          ...props.form,
          params: JSON.stringify(props.form.params)
        })
          .then(res => {
            let { code, msg } = res;
            if (code == 200) {
              ElMessage.success("派单成功 ");
              emits("success");
              state.submitLoading = false;
              beforeClose();
            } else {
              ElMessage.error("派单失败: " + msg);
            }
          })
          .catch(err => {
            console.error(err);
            ElMessage.error("提交失败: " + err);
          })
          .finally(() => {
            state.submitLoading = false;
          });
        return;
      }
      console.log(props.form.params)
      if (props.form.params && props.form.currentView && props.form.currentView == 'assetView') {
        requestCountFraAssetIpDispatchEventProcess('assetView');
      } else {
        requestCountFraAssetIpDispatchEventProcess('');
      }

    }
  });
};

const loadUsers = (deptId?: string) => {
  state.userTreeData = [];
  queryRoleUsers(deptId).then(res => {
    let { treeData } = res.data || {};
    if (Array.isArray(treeData)) {
      try {
        state.userTreeData = treeData[0].children || [];
        props.form["userId"] = [];
        console.log("state.userTreeData ", state.userTreeData);
      } catch (e) { }
    }
  });
};

watch(
  () => props.modelValue,
  val => {
    state.visible = val;
    state.userTreeData = [];
    loadUsers();
  }
);
</script>
<style scoped lang="scss"></style>
