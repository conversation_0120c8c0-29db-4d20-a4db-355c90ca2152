<template>
  <!-- style=" height: calc(100vh - 90px) " -->
  <div class="security-vul-monitor-detail">
    <div style="
          /* height: 64px; */
          margin: -4px 0px 0 0px;
          background: #fafafa;
          /* border: 1px solid #eee; */
          padding-left: 8px;
          display: flex;
          align-items: center;
          width: 100%;
          padding-bottom: 1rem;
        ">
      <!-- <img class="icon"
        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAYAAACohjseAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAP9SURBVHgB7ZtNVhpBEMermgGTl403CLkBnkA4QfQE0RPEbBTMQlxE1I14AvEEmhOAJwi5ATfQvJf3CF9dqW6cwMB8NMwHQx7/hUpPM92/meru6q4SIU7VBkWB8JEACgCYB6C8Lkd4AYI2ArQlwXeoZFsQkxDiUK1/gCjO/gEFd6NDJM+hkmtAxIoWsNbNI1p3/FcRlpICHZSg8rYDEUlAVLoY7qGwfsDScEqU1/fge0FEiuYNajh6gAhFEvfh1HqEkAoPqMxSPXWCbYhSPBGRHO6ENdfQJoqYbUYOp8T3fB3PoRQOkGdL85lyKRXHbSyvUIDjpSBehW1jeUBexGN+e6/iNnRby8kdUHkgl4MbNYF4fhMpsqk8WMFtiavBmZs5W3M1r/sFJHggPcize1Trui+8KHlRthqQhAQ8e16r0jZuDZvc3wIiAl0NCU6se/uyc5nQngjPig7TY+9iJPfha64NaZNrf/kTQcn2b50mKnBvflyxd2FhE66GnyBN8oAba2LSTsCTN3X2/G/n67O5EjW0nadBahhpt9AFjncp0MtW7Y/zk0w5e8Sv+NztvlxeXTVkhi0JJbp6TkR0Tz2rBFV8scu8XbWL/hEKvPG42kIem5CwCGmbwVxnVH74tzzujmbL/X1RXi5QwEMsrliE0hZXmZjltIKdbd/BvHqxWR76bZSDPRleA4lGT5A2qd2GXg78TwEsWFMR4SNUrFZQveh29CnVfw+YkIm+nprZEnIbIXOWxOycCCDB6CdUthqOslqPnWMRu/uXkIlmnmFF2kwy664N4LrLEtf9zyBFwa8S7xF39c8UiZ3oIl4O/c9NhWxbfEReT1vnzUR57vWBbxWJmzG49hKwgp15csKORWXrQ2C9Wq+RhFu1iAiwAWXrMKjeZgyuuzaAWjg5Z0yNSP4yqWYGqE6L0yYhO0bVTCqBTCGgzBj1yQxQRZZUdlJapPpimB1lBsjBDkiTFhgywYA6IIrNlB3fF02DQP6A6YTTMo10eQOmGM6WCaQ7oC9cupzzIMh5QB84FaaiP5md1UCqw2MOtri07QfpBAyA0zE4jp6SkPuJLhs6kjTQiQX69wKQE0ATOFvHuTZHW0uJQCo41ZadyqLDeeaQY8BF4GzZkHGaqw13PJPCsgCkBsQR3i0EZ0tBqoZi8FX5QKml0ymPPfJzAiDh22/tnIhxAd27VPKHm26okt3RoeQo3ub4rX2BcjY4tdkLUj3wwTtdNjbR01x9OnXEGM7RWK6hjj+WBeU223qW7vIRykmubvzFWUh1n6lUkplUrv4BkHiJIpVY+68kixxZ2gUpVfis6rh+2a/zPvM9wOhJ7wzC/muBShxUmVpdDtNN5cn8BVkMAtLUyYvHAAAAAElFTkSuQmCC"
        width="36" /> -->
      <img style="width: 3.3rem;height: 3rem" :src="vulPhoto" mode="scaleToFill" />
      <div style="
            display: flex;
            flex-direction: column;
            align-items: start;
            margin-left: 15px;
            width: 100%;
          ">
        <span class="vul-name" style="margin-bottom: 10px;width: 100%;">
          <!-- <el-tooltip class="box-item" effect="dark" :content="props.vulRecord.vulName" placement="top">
            <span style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;width: 26rem;display: inline-block;">{{ props.vulRecord.vulName }}</span>
          </el-tooltip> -->
          <el-tooltip class="box-item" effect="dark" :content="props.vulRecord.vulName" placement="top">
            <span style="
          padding-top: 1rem; 
          overflow: hidden;
          white-space: wrap;
          text-overflow: ellipsis;
          display: inline-block;
          width: 100%;
          /* 允许文本换行 */
          word-break: break-word;  /* 强制在任意字符间换行 */
          white-space: normal;     /* 恢复默认换行规则 */
          /* 多行截断配置 */
          display: -webkit-box;     /* 启用弹性伸缩盒子模型 */
          -webkit-box-orient: vertical;  /* 文本垂直排列 */
          -webkit-line-clamp: 3;    /* 限制显示3行 */
          overflow: hidden;         /* 溢出部分隐藏 */
          text-overflow: ellipsis;  /* 溢出文本显示省略号 */
          ">{{
            props.vulRecord.vulName }}</span>
          </el-tooltip>
        </span>
        <el-tag style="color: #fff;border-color: transparent;" :color="getRiskLevelColor(vulRecord.vullevel + '')"
          class="text-white border-none">
          {{ getRiskLevelLabel03(vulRecord.vullevel + '') }}
        </el-tag>
        <!-- <el-tag v-if="vulRecord.vullevel == 5" color="#B51994"
          style="color: #fff;border-color: transparent;">严重</el-tag>
        <el-tag v-if="vulRecord.vullevel == 4" color="#DD1515"
          style="color: #fff;border-color: transparent;">高危</el-tag>
        <el-tag v-else-if="vulRecord.vullevel == 3" color="#F98C3E"
          style="color: #fff;border-color: transparent;">中危</el-tag>
        <el-tag v-else-if="vulRecord.vullevel == 2" color="#67C23A"
          style="color: #fff;border-color: transparent;">低危</el-tag>
        <el-tag v-else color="#909399" style="color: #fff;border-color: transparent;">信息</el-tag> -->
      </div>
    </div>

    <el-descriptions style="margin-top: 20px" :column="3" :size="''" border>
      <el-descriptions-item :label-style="{ width: '120px' }" :content-style="{ width: 'calc(50% - 120px)' }">
        <template #label> 漏洞类型 </template>
        {{ props.vulRecord.vulType }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 漏洞编号CVE </template>
        {{ props.vulRecord.vul_number_cve }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 漏洞编号CNNVD </template>
        {{ props.vulRecord.vulnumber_cnnvd }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 弱密码类型 </template>
        {{ props.vulRecord.weak_type }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 账号 </template>
        {{ props.vulRecord.user }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 明文传输URL </template>
        {{ props.vulRecord.url }}
      </el-descriptions-item>

      <el-descriptions-item :label-style="{ width: '120px' }" :content-style="{ width: 'calc(50% - 120px)' }">
        <template #label> 资产IP </template>
        {{ props.vulRecord.ip }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> 资产名称 </template>
        {{ props.vulRecord.deviceName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> 所属组织 </template>
        {{ props.vulRecord.refOrgNameTree }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 端口 </template>
        {{ props.vulRecord.vjlPort }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 协议 </template>
        {{ props.vulRecord.vjlProtocol }}
      </el-descriptions-item>


      <el-descriptions-item>
        <template #label> 服务名称 </template>
        {{ props.vulRecord.appType }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 首次发现时间 </template>
        {{ props.vulRecord.createTime }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> 最新发现时间 </template>
        {{ props.vulRecord.lastUpdateTime }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template #label> 重复次数 </template>
        {{ props.vulRecord.cnt }}
      </el-descriptions-item>

      <el-descriptions-item>
        <template #label> 漏洞来源 </template>
        {{ props.vulRecord.syscode }}
      </el-descriptions-item>
    </el-descriptions>

    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <div class="asset-item-card box-card el-card">
          <div class="clearfix title">
            <div class="title-icon">
              <img class="icon"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAC/SURBVHgB1ZKxDcIwEEX/RWGP7AFItKQFerNFRmAL0kOdOhIMkj2QfJwNhXF0gK00eUVs50vf/l8HRKzqPa/rwxkKml5gIsjdgAko3YeZhoLQ+z3YhOeYWLdsN0RUeSP389Zdjm4vLzThOSbWl9tdK8vLKOTeXQlf0PQZlJ2KWnYqatl0evwVlZvFR+kjIwa1yGBkhKbMilhqghYxjvTTKDWiapQa0Q+k3D7IiPfIwELmCO85InAlhRhkICZ+fQLJJFH2MFrFHAAAAABJRU5ErkJggg==" />
              <span class="label" style="margin: 0 5px; font-weight: bold; font-size: 18px">处置建议</span>
            </div>
          </div>
          <el-form label-width="80px">
            <el-form-item label="漏洞描述">
              <!-- <div slot="label" class="lineheight">漏洞描述</div> -->
              <div class="lineheight" style="white-space: pre-line;font-weight: 400;">
                {{ props.vulRecord.vuldetail }}
              </div>
            </el-form-item>
            <el-form-item label="漏洞影响">
              <!-- <div slot="label" class="lineheight">漏洞影响</div> -->
              <div class="lineheight" style="white-space: pre-line;font-weight: 400;">
                {{ props.vulRecord.impact }}
              </div>
            </el-form-item>
            <el-form-item label="解决方案">
              <!-- <div slot="label" class="lineheight">解决方案</div> -->
              <div class="lineheight" style="white-space: pre-line;font-weight: 400;">
                {{ props.vulRecord.vulsuggest }}
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>

    <div class="asset-item-card box-card el-card" style="margin-top: 20px">
      <div class="clearfix title">
        <div class="title-icon">
          <img class="icon"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAACJSURBVHgB7ZExDoJAFERndGPtVew9gbW9XsHKWElH6DgC9NScgJ6rUBOyA0sCIYSFA8BrdvMzf/7PH8LDOWpeFoohViICfE2KFcz98dS0UOQZ3WuFQGpu7k+ashP1RnP9aEQwWRxBXTG0dFuNZY+e8BHWb/L0d4ay+uB3SbBPvDc64t8DR/zb8bc/X2zKFw9JSwAAAABJRU5ErkJggg==" />
          <span class="label" style="margin: 0 5px; font-weight: bold; font-size: 18px">处置情况</span>
          <el-tag v-if="props.vulRecord.status == '2'" effect="dark" type="success">已处置</el-tag>
          <el-tag v-else effect="dark" type="warning">未处置</el-tag>
        </div>
      </div>
      <el-descriptions style="margin-top: 20px" :column="2" :size="''" border>
        <el-descriptions-item :label-style="{ width: '20px' }">
          <template #label> 处置时间 </template>
          {{ props.vulRecord.deal_time }}
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '20px' }">
          <template #label> 处置人 </template>
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 666px;">{{ props.vulRecord.dealUser }}</div>
            </template>
            <span style="width: 20rem !important;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            ">
              {{ props.vulRecord.dealUser }}
            </span>
          </el-tooltip>
          <!-- {{ props.vulRecord.dealUser }} -->

        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '20px' }">
          <template #label> 工单号 </template>
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 666px;">{{ props.vulRecord.work_num }}</div>
            </template>
            <span style="width: 20rem !important;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            ">
              {{ props.vulRecord.work_num }}
            </span>
          </el-tooltip>
          <!-- {{ props.vulRecord.work_num }} -->
        </el-descriptions-item>
        <el-descriptions-item :label-style="{ width: '20px' }">
          <template #label> 处置说明 </template>
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 666px;">
                <div style="max-width: 666px;">{{ props.vulRecord.deal_idea }}</div>
                <!-- <div class="lineheight" style="white-space: pre-line" v-sec-html="props.vulRecord.deal_idea">
                </div> -->
              </div>
            </template>
            <span style="
                width: 20rem !important;
                display: inline-block;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
            ">
              {{ props.vulRecord.deal_idea }}
            </span>
          </el-tooltip>

        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script lang="ts" setup>
/** 安全事件详情 */
import { reactive, ref, watch, defineEmits } from "vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  getDealStatusType02,
  getSegmentLabel02,
  riskLevelData,
  segmentData,
  getRiskLevelColor02,
  getRiskLevelLabel02,
  riskLevelData02,
  getRiskLevelLabel03
} from "@/views/modules/security/securityVulnerability/util/vulnerability_data";

const props = defineProps({
  vulRecord: Object,
})
const vulPhoto = ref(`data:image/svg+xml;base64,PHN2ZyB0PSIxNzMzNzk3MjM5NDcyIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE1OTMwIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxNC4zNDY2NjcgNTA5LjQ0bS00MjYuNjY2NjY3IDBhNDI2LjY2NjY2NyA0MjYuNjY2NjY3IDAgMSAwIDg1My4zMzMzMzMgMCA0MjYuNjY2NjY3IDQyNi42NjY2NjcgMCAxIDAtODUzLjMzMzMzMyAwWiIgZmlsbD0iI0VEN0QzMSIgcC1pZD0iMTU5MzEiPjwvcGF0aD48cGF0aCBkPSJNNTE0LjM0NjY2NyA4NTAuNzczMzMzYTM0MS4zMzMzMzMgMzQxLjMzMzMzMyAwIDEgMSAzNDEuMzMzMzMzLTM0MS4zMzMzMzMgMzQxLjMzMzMzMyAzNDEuMzMzMzMzIDAgMCAxLTM0MS4zMzMzMzMgMzQxLjMzMzMzM3ogbTAtNjQwYTI5OC42NjY2NjcgMjk4LjY2NjY2NyAwIDEgMCAyOTguNjY2NjY2IDI5OC42NjY2NjcgMjk4LjY2NjY2NyAyOTguNjY2NjY3IDAgMCAwLTI5OC42NjY2NjYtMjk4LjY2NjY2N3oiIGZpbGw9IiNGQkFDNDgiIHAtaWQ9IjE1OTMyIj48L3BhdGg+PHBhdGggZD0iTTUxNC4zNDY2NjcgNzAxLjQ0YTE5MiAxOTIgMCAxIDEgMTkyLTE5MiAxOTIgMTkyIDAgMCAxLTE5MiAxOTJ6IG0wLTM0MS4zMzMzMzNhMTQ5LjMzMzMzMyAxNDkuMzMzMzMzIDAgMSAwIDE0OS4zMzMzMzMgMTQ5LjMzMzMzMyAxNDkuMzMzMzMzIDE0OS4zMzMzMzMgMCAwIDAtMTQ5LjMzMzMzMy0xNDkuMzMzMzMzeiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMTU5MzMiPjwvcGF0aD48cGF0aCBkPSJNMzQ1LjM4NjY2NyA0OTZtLTg1LjMzMzMzNCAwYTg1LjMzMzMzMyA4NS4zMzMzMzMgMCAxIDAgMTcwLjY2NjY2NyAwIDg1LjMzMzMzMyA4NS4zMzMzMzMgMCAxIDAtMTcwLjY2NjY2NyAwWiIgZmlsbD0iI0ZGRkZGRiIgcC1pZD0iMTU5MzQiPjwvcGF0aD48cGF0aCBkPSJNNjMxLjY4IDY4MC4xMDY2NjdtLTQyLjY2NjY2NyAwYTQyLjY2NjY2NyA0Mi42NjY2NjcgMCAxIDAgODUuMzMzMzM0IDAgNDIuNjY2NjY3IDQyLjY2NjY2NyAwIDEgMC04NS4zMzMzMzQgMFoiIGZpbGw9IiNGRkZGRkYiIHAtaWQ9IjE1OTM1Ij48L3BhdGg+PHBhdGggZD0iTTY3NC4zNDY2NjcgNDAwbS0zMiAwYTMyIDMyIDAgMSAwIDY0IDAgMzIgMzIgMCAxIDAtNjQgMFoiIGZpbGw9IiNGRkZGRkYiIHAtaWQ9IjE1OTM2Ij48L3BhdGg+PC9zdmc+`);


</script>

<style lang="scss" scoped>
.security-vul-monitor-detail {
  overflow: auto;
  padding: 0 20px;

  .vul-name {
    font-size: 18px;
    font-family: Source Han Sans CN-Medium, Source Han Sans CN;
    font-weight: bold;
    color: #474d66;
    line-height: 18px;
  }

  :deep(.el-descriptions-item__cell) {
    line-height: 25px !important;
  }

  .asset-item-card {
    width: 100%;
    height: 100%;
    border: unset;
    box-shadow: unset;

    :deep(.el-divider--horizontal) {
      margin: 10px 0px;
      width: unset;
    }

    .asset-name {
      color: #2563eb;
      font-size: 18px;
      margin-left: 10px;
      font-weight: bold;
    }

    .title {
      cursor: pointer;
      font-size: 18px;
      font-family: PingFang SC-Bold, PingFang SC;
      font-weight: bold;
      line-height: 18px;
      display: flex;
      align-items: center;
      margin: 20px 0;

      .title-icon {
        display: flex;
        align-items: center;
        margin-right: 5px;

        .icon {
          margin-right: 8px;
        }

        .label {
          margin: 0 5px;
          font-weight: bold;
          font-size: 18px;
        }
      }
    }

    .property-form {
      margin: 20px 0;
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    &:hover {
      //box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    :deep(.el-form-item) {
      margin-bottom: 20px !important;

      .el-form-item__label {
        line-height: 30px !important;
        font-weight: 500;
        font-size: 14px;
        color: #6f6e89;
      }

      .el-form-item__content {
        line-height: 14px !important;
        font-weight: bold;
        font-size: 14px;
        color: #3c4a54;
      }
    }

    .alert {
      padding: 5px 0;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      line-height: 16px;
    }
  }

  .lineheight {
    line-height: 30px;
  }
}
</style>
