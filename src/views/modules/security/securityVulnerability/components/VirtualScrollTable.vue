<template>
  <div class="virtual-scroll-table" ref="containerRef" @scroll="handleScroll">
    <!-- 表头 -->
    <div class="table-header" :style="{ width: `${totalWidth}px` }">
      <div
        v-for="column in columns"
        :key="column.field"
        class="header-cell"
        :style="{ width: `${column.width}px` }"
      >
        {{ column.title }}
      </div>
    </div>

    <!-- 虚拟滚动容器 -->
    <div
      class="table-body"
      :style="{
        height: `${containerHeight}px`,
        position: 'relative',
        overflow: 'auto'
      }"
    >
      <!-- 占位元素，用于撑开滚动条 -->
      <div
        class="scroll-placeholder"
        :style="{ height: `${totalHeight}px` }"
      ></div>

      <!-- 可见行容器 -->
      <div
        class="visible-rows"
        :style="{
          position: 'absolute',
          top: `${offsetY}px`,
          left: '0',
          right: '0'
        }"
      >
        <div
          v-for="(item, index) in visibleItems"
          :key="getRowKey(item, startIndex + index)"
          class="table-row"
          :class="{
            'row-selected': selectedRows.includes(getRowKey(item, startIndex + index)),
            'row-even': (startIndex + index) % 2 === 0,
            'row-odd': (startIndex + index) % 2 === 1
          }"
          :style="{ height: `${itemHeight}px` }"
          @click="handleRowClick(item, startIndex + index)"
        >
          <!-- 复选框列 -->
          <div
            v-if="showCheckbox"
            class="cell checkbox-cell"
            :style="{ width: '50px' }"
          >
            <el-checkbox
              :model-value="selectedRows.includes(getRowKey(item, startIndex + index))"
              @change="handleRowSelect(item, startIndex + index, $event)"
            />
          </div>

          <!-- 数据列 -->
          <div
            v-for="column in columns"
            :key="column.field"
            class="cell"
            :style="{ width: `${column.width}px` }"
          >
            <slot
              :name="column.field"
              :row="item"
              :column="column"
              :index="startIndex + index"
            >
              {{ getCellValue(item, column.field) }}
            </slot>
          </div>

          <!-- 操作列 -->
          <div
            v-if="showOperator"
            class="cell operator-cell"
            :style="{ width: `${operatorWidth}px` }"
          >
            <slot
              name="operator"
              :row="item"
              :index="startIndex + index"
            >
              <el-button size="small" type="primary" text>操作</el-button>
            </slot>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading :loading="loading" />
    </div>
  </div>
</template>

<script lang="ts" setup>
/**
 * 虚拟滚动表格组件
 * 支持大数据量渲染，只渲染可视区域的数据
 */

import { computed, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';

// 组件属性
interface Props {
  data: any[];
  columns: TableColumn[];
  itemHeight?: number;
  containerHeight?: number;
  showCheckbox?: boolean;
  showOperator?: boolean;
  operatorWidth?: number;
  loading?: boolean;
  rowKey?: string | ((row: any, index: number) => string);
  selectedRows?: string[];
  bufferSize?: number; // 缓冲区大小
}

interface TableColumn {
  field: string;
  title: string;
  width: number;
  sortable?: boolean;
  formatter?: (value: any, row: any) => string;
}

// 组件事件
interface Emits {
  (e: 'row-click', row: any, index: number): void;
  (e: 'selection-change', selectedRows: string[]): void;
  (e: 'scroll', scrollTop: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  itemHeight: 50,
  containerHeight: 400,
  showCheckbox: false,
  showOperator: false,
  operatorWidth: 120,
  loading: false,
  selectedRows: () => [],
  bufferSize: 5
});

const emit = defineEmits<Emits>();

// 响应式引用
const containerRef = ref<HTMLElement>();
const scrollTop = ref(0);

// 计算属性
const totalHeight = computed(() => props.data.length * props.itemHeight);
const totalWidth = computed(() => {
  let width = 0;
  if (props.showCheckbox) width += 50;
  width += props.columns.reduce((sum, col) => sum + col.width, 0);
  if (props.showOperator) width += props.operatorWidth;
  return width;
});

// 可视区域计算
const visibleCount = computed(() => 
  Math.ceil(props.containerHeight / props.itemHeight) + props.bufferSize * 2
);

const startIndex = computed(() => 
  Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.bufferSize)
);

const endIndex = computed(() => 
  Math.min(props.data.length, startIndex.value + visibleCount.value)
);

const visibleItems = computed(() => 
  props.data.slice(startIndex.value, endIndex.value)
);

const offsetY = computed(() => startIndex.value * props.itemHeight);

// 事件处理
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  scrollTop.value = target.scrollTop;
  emit('scroll', scrollTop.value);
};

const handleRowClick = (row: any, index: number) => {
  emit('row-click', row, index);
};

const handleRowSelect = (row: any, index: number, selected: boolean) => {
  const rowKey = getRowKey(row, index);
  const newSelectedRows = [...props.selectedRows];
  
  if (selected) {
    if (!newSelectedRows.includes(rowKey)) {
      newSelectedRows.push(rowKey);
    }
  } else {
    const keyIndex = newSelectedRows.indexOf(rowKey);
    if (keyIndex > -1) {
      newSelectedRows.splice(keyIndex, 1);
    }
  }
  
  emit('selection-change', newSelectedRows);
};

// 辅助函数
const getRowKey = (row: any, index: number): string => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index);
  } else if (typeof props.rowKey === 'string') {
    return row[props.rowKey];
  } else {
    return `row_${index}`;
  }
};

const getCellValue = (row: any, field: string): any => {
  return row[field];
};

// 公共方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0;
  }
};

const scrollToIndex = (index: number) => {
  if (containerRef.value) {
    const targetScrollTop = index * props.itemHeight;
    containerRef.value.scrollTop = targetScrollTop;
  }
};

// 暴露方法给父组件
defineExpose({
  scrollToTop,
  scrollToIndex
});

// 监听数据变化，重置滚动位置
watch(() => props.data, () => {
  nextTick(() => {
    scrollToTop();
  });
});
</script>

<style scoped>
.virtual-scroll-table {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 12px 8px;
  border-right: 1px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.table-body {
  position: relative;
}

.scroll-placeholder {
  width: 1px;
  pointer-events: none;
}

.visible-rows {
  width: 100%;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  transition: background-color 0.2s;
  cursor: pointer;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.row-selected {
  background-color: #ecf5ff;
}

.row-even {
  background-color: #fafafa;
}

.cell {
  padding: 8px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.checkbox-cell {
  justify-content: center;
}

.operator-cell {
  justify-content: center;
  gap: 8px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}
</style>
