<template>
    <div ref="container">
        <el-form style="margin-top: 1rem;" :model="batchOperationForm" :rules="rules01" ref="batchOperationFormRef" class="demo-ruleForm">
            <el-form-item label="处置动作" prop="region">
                <el-select style="width: 16rem;"  clearable v-model="batchOperationForm.region" placeholder="请选择处置动作">
                    <el-option label="已修复" value="disposalof" />
                    <el-option label="无需处理" value="noNeedHandle" />
                    <!-- <el-option label="暂不处理" value="notHandledYet" /> -->
                </el-select>
            </el-form-item>
            <el-form-item label="处置意见" prop="deal_idea">
                <el-input placeholder="请输入处置意见" maxlength="200" show-word-limit v-model="batchOperationForm.deal_idea" type="textarea" :rows="4"></el-input>
            </el-form-item>
            <!-- <el-form-item v-if="Array.isArray(props.batchOperationModelData)" label="处置内容" prop="batchOperationModel">
                <im-table :style="{ width: tableWidth + 'px' }" ref="tableDrawer" style="height: 500px;overflow: auto;"
                    id-key="_row_id" :columns="batchOperationColumns" :data="batchOperationForm.batchOperationModel"
                    fit-height>
                    <template #vullevel="{ row }">
                        <reseverity-tag :reseverity="row.vullevel" type="vul"></reseverity-tag>
                    </template>
<template #dispatch_status="{ row }">
                        <div>
                            <el-tag v-if="row.dispatch_status == '已派单'" class="tag" size="small"
                                type="success">已派单</el-tag>
                            <el-tag v-else class="tag" size="small" type="danger">未派单</el-tag>
                        </div>
                    </template>
<template #status="{ row }">
                        <div>
                            <el-tag v-if="row.status == '1'" class="tag" size="small" type="warning">未处置
                            </el-tag>
                            <el-tag v-else class="tag" size="small" type="success">已处置</el-tag>
                        </div>
                    </template>

<template #deviceName="{ row }">
                        <div>{{ row.deviceName || "-" }}</div>
                    </template>

<template #ops="{ row }">
                        <div>
                            <el-button type="text" @click="deleteTable(row)">删除</el-button>
                        </div>
                    </template>
</im-table>
</el-form-item> -->
        </el-form>
        <div class="demo-drawer__footer" style="text-align: center;">
            <el-button type="primary" @click="submit">提 交</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { http } from "@/utils/http";

const props = defineProps({
    batchOperationModelData: Array
})

const batchOperationForm = reactive({
    deal_idea: "",
    batchOperationModel: [],
    region: ""
});

watch(props.batchOperationModelData, val => {
    if (val.length > 0) {
        console.log(val);
        batchOperationForm.batchOperationModel = val;
    }
})
if (props.batchOperationModelData.length > 0) {
    console.log(props.batchOperationModelData);
    batchOperationForm.batchOperationModel = props.batchOperationModelData;
}



const rules01 = reactive({
    deal_idea: [
        { required: true, message: "请输入处置意见", trigger: "blur" },
    ],
    // batchOperationModel: [
    //     { required: true, trigger: "change" },
    // ],
    region: [
        {
            required: true,
            message: '请选择处置动作',
            trigger: 'change',
        },
    ],
})
const batchOperationColumns = ref([
    {
        label: "资产IP",
        prop: "ip",
        width: 120,
    },
    {
        label: "风险等级",
        slot: "vullevel",
        width: 80,
        prop: "vullevel",
    },
    {
        label: "漏洞名称",
        showOverflowTooltip: true,
        prop: "vulName",
    },
    {
        label: "漏洞类型",
        showOverflowTooltip: true,
        prop: "vulType",
    },
    {
        label: "漏洞来源",
        showOverflowTooltip: true,
        prop: "syscode",
    },
    {
        label: "资产名称",
        prop: "deviceName",
        slot: "deviceName",
        showOverflowTooltip: true,
    },
    {
        label: "资产类别",
        prop: "asset_type_name",
        align: "center",
    },
    {
        label: "所属组织",
        prop: "refOrgNameTree",
        showOverflowTooltip: true,
        align: "center",
    },
    {
        label: "主机安装系统",
        prop: "res_status",
        width: 120,
        showOverflowTooltip: true,
        align: "center",
    },
    {
        label: "重复次数",
        prop: "cnt",
    },
    {
        label: "处置状态",
        slot: "status",
        width: 90,
        prop: "status",
    },
    {
        label: "工单号",
        prop: "work_num",
        showOverflowTooltip: true,
        hidden: true,
    },
    {
        label: "处置人",
        prop: "dealUser",
        showOverflowTooltip: true,
        hidden: true,
    },
    {
        label: "处置说明",
        prop: "deal_idea",
        showOverflowTooltip: true,
        hidden: true,
    },
    {
        label: "处置时间",
        prop: "deal_time",
        showOverflowTooltip: true,
        width: 140,
        hidden: true,
    },
    {
        label: "首次发现时间",
        prop: "createTime",
        showOverflowTooltip: true,
        width: 140,
    },
    {
        label: "最新发现时间",
        prop: "lastUpdateTime",
        showOverflowTooltip: true,
        width: 140,
    },
    {
        label: "派单状态",
        prop: "dispatch_status",
        showOverflowTooltip: true,
        slot: "dispatch_status",
    },
    {
        label: "操作",
        fixed: "right",
        width: 120,
        slot: "ops",
    },
])

const deleteTable = (row) => {
    if (batchOperationForm.batchOperationModel.length == 1) {
        return ElMessage.error("须保留一条及以上的漏洞数据！");
    }
    console.log(row);
    batchOperationForm.batchOperationModel = batchOperationForm.batchOperationModel.filter(item => item.vulId != row.vulId);
}

const emit = defineEmits(['closeDraw'])
const batchOperationFormRef = ref(null);
const submit = () => {
    if (Array.isArray(props.batchOperationModelData)) {
        batchOperationFormRef.value.validate((valid) => {
            if (valid) {
                let data = [];
                // for (let item of batchOperationForm.batchOperationModel) {
                //     data.push({
                //         ip: item.ip,
                //         vulId: item.vulId
                //     });
                // }
                const vulIds = batchOperationForm.batchOperationModel.map(item => item['vulId']);
                const result = vulIds.join(',');
                console.log(result);
                http
                    .postJson("security-vul/v3/sum/disposalBatch", {
                        "dealIdea": batchOperationForm.deal_idea,
                        "vulIds": result,
                        status: batchOperationForm.region,
                    })
                    .then(res => {
                        if (!res['errors']) {
                            ElMessage.success("批量处置提交成功！");
                        }
                        emit('closeDraw');
                    })
                    .catch(() => {
                    });
            } else {
                return false;
            }
        });
    } else {
        batchOperationFormRef.value.validate((valid) => {
            if (valid) {
                http
                    .postJson("security-vul/v3/sum/disposal", {
                        "dealIdea": batchOperationForm.deal_idea,
                        "vulId": props.batchOperationModelData['vulId'],
                        ip: props.batchOperationModelData['ip'],
                        status: batchOperationForm.region,
                    })
                    .then(res => {
                        ElMessage.success("提交成功！");
                        emit('closeDraw');
                    })
                    .catch(() => {
                    });
            } else {
                return false;
            }
        });
    }

}

const cancel = () => {
    emit('closeDraw');
}

const container = ref(null);
const tableWidth = ref("");
onMounted(() => {
    nextTick(() => {
        console.log(container.value.offsetWidth);
        tableWidth.value = container.value.offsetWidth - 100 + '';
    })
})
</script>

<style lang="scss" scoped></style>