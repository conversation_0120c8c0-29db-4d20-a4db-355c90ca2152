<template>
  <div class="attackTemplate">
    <el-row type="flex" align="center" justify="center">
      <div class="leftLayout">
        <div style="height: 45px; width: 100%" class="bottomLayout">
          <div class="circleLayout">{{ props.status }}</div>
        </div>
        <div style="margin-top: 20px; width: 100%; text-align: center">
          <div style="font-size: 15px; font-weight: bold">
            <slot></slot>
          </div>
        </div>
      </div>
      <div class="leftLayout" ref="rightAreaRef">
        <div class="rightTitle" v-if="props.status == '外网'">{{ props.attackData && props.attackData.ipAddress ?
          props.attackData.ipAddress :
          '--' }}</div>
        <template v-else>
          <myTooltip :rowIp="props.attackData.ipAddress">
            <div class="rightTitle">{{ props.attackData && props.attackData.ipAddress ? (props.attackData.ipAddress ==
              '0.0.0.0' ?
              'DNS' : props.attackData.ipAddress) : '--' }}</div>
          </myTooltip>
        </template>

        <div class="rightTitle beyondShow">{{ props.attackData && props.attackData.zoneName__label ?
          props.attackData.zoneName__label :
          '--' }}
        </div>
        <div class="rightTitle">端口：{{ props.attackData && props.attackData.netPort ? props.attackData.netPort : '--' }}
        </div>
      </div>
    </el-row>

  </div>
</template>

<script lang="ts" setup>
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
const props = defineProps({
  attackData: Object,
  eventContext: Object,
  status: String
});

const showStatus = ref(false);
const leaveStatus = ref(false);
const rightAreaRef = ref(null);
const x = ref(0);
const y = ref(0);
const x1 = ref(0);
const y1 = ref(0);

// const enterShow = (el) => {
//   showStatus.value = true
// }
// const leaveShow = (el) => {
//   if (el.clientX >= x.value && el.clientX <= x1.value && el.clientY >= y.value && el.clientY <= y1.value) {
//     showStatus.value = true
//   } else {
//     showStatus.value = false
//   }
// }
showStatus.value = false;

onMounted(() => {
  x.value = rightAreaRef.value.getBoundingClientRect().x
  y.value = rightAreaRef.value.getBoundingClientRect().y
  x1.value = rightAreaRef.value.getBoundingClientRect().x + 123
  y1.value = rightAreaRef.value.getBoundingClientRect().y + 78
})
</script>

<style scoped lang="scss">
.attackTemplate {
  .circleLayout {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45px;
    width: 45px;
    border-radius: 50%;
    border: 2px solid #a5d6f1;
    font-size: 14px;
  }

  .leftLayout {
    display: flex;
    align-items: center;
    justify-content: left;
    flex-wrap: wrap;
  }

  .bottomLayout {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .beyondShow {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .rightTitle {
    width: 100%;
    font-size: 14px;
    margin-bottom: 10px
  }

  .hoverShow {
    position: absolute;
    width: 250px;
    height: 202px;
  }

  .titleSize {
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>
