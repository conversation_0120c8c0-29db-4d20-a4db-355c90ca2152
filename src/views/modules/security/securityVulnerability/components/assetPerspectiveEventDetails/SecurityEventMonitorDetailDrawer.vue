<template>
  <!-- <el-drawer title="安全事件监控详情" size="75%" append-to-body v-model="drawer" :before-close="handleDrawerClose"> -->
  <div class="security-event-monitor-detail" :style="{ height: bodyHeight }">
    <el-row type="flex" align="center">
      <div class="riskLayout titleSize"
        :style="{ backgroundColor: riskType[eventContext.reseverity].color, color: riskType[eventContext.reseverity].titleColor }">
        {{ riskType[eventContext.reseverity].value }}</div>
      <el-row style="margin-left: 15px; margin-top: 4px">
        <div class="titleSize" style="font-weight: bold">{{ getLabelValue('event_name', eventContext) }}</div>
        <el-row type="flex" align="center" style="margin-top: 15px">
          <div class="titleSize">攻击者 <span class="titleColor">{{ getLabelValue('src_ip', eventContext) }}</span>
          </div>
          <span v-sec-html="'&nbsp;&nbsp;'"></span>
          <div v-if="dealWith == 3 || dealWith == 4" class="labelNumberLayout titleSize"> {{ labelNumber[dealWith] }}
          </div>
          <span v-sec-html="'&nbsp;&nbsp;'"></span>
          <div class="titleSize">使用 <span class="titleColor">{{ getLabelValue('event_name', eventContext) }}
            </span>攻击方式， </div>
          <div class="titleSize">对受害者 <span class="titleColor">{{ getLabelValue('dst_ip', eventContext) }}</span> 发起
            <span class="titleColor">{{ getLabelValue('event_cnt', eventContext) }} </span>次攻击
          </div>
        </el-row>
      </el-row>
    </el-row>
    <div
      style="width: 100%; background-color: #dcdfe6; height: 1px; margin: 10px 0px 15px;position: relative;text-align: center">
      <i class="el-icon-arrow-down" title="收缩" v-if="isTrue" style="cursor: pointer;font-size: 16px;"
        @click="isTrue = !isTrue"></i>
      <i class="el-icon-arrow-up" title="展开" v-if="!isTrue" style="cursor: pointer;font-size: 16px;"
        @click="isTrue = !isTrue"></i>
    </div>
    <el-row type="flex" v-show="isTrue" align="center">
      <attackTemplate :attackData="sourceAsset.model" v-if="isSourceAsset" :eventContext="eventContext"
        :status="eventRecord.src_ip_online" style="width: 35%">
        攻击者
      </attackTemplate>
      <div class="flexLayout" style="width: 30%">
        <div style="width: 100%; font-size: 12px; text-align: center;">发生{{ getLabelValue('event_cnt', eventContext)
          }}
          次攻击 <span v-sec-html="'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'"></span> 攻击结果：<span
            :style="{ color: '#f59a23' }">{{ getLabelValue('event_cnt', eventContext) == 1 ? '成功' : '失败' }}</span>
        </div>
        <div style="width: 100%; position: relative">
          <div class="lineLayout"></div>
        </div>
        <div style="width: 100%; font-size: 12px; text-align: center">
          协议：{{ getLabelValue('protocol', eventContext) }}
        </div>
      </div>
      <attackTemplate :attackData="targetAsset.model" v-if="isTargetAsset" :eventContext="eventContext"
        :status="eventRecord.dst_ip_online" style="width: 35%">
        受害者
      </attackTemplate>
    </el-row>
    <el-row type="flex" v-show="isTrue" align="center" style="margin-top: 40px">
      <el-row style="width: 35%">
        <el-col class="titleSize1">
          <div class="labelStyle1">攻击阶段: </div>
          <div class="titleStyle1">{{ getLabelValue('attack_stage', eventContext) }}</div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">事件分类: </div>
          <div class="titleStyle1">{{ getLabelValue('event_subtype_name', eventContext) }}</div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">风险等级: </div>
          <div class="titleStyle1">{{ riskType[eventContext.reseverity].value }}</div>
        </el-col>
      </el-row>
      <el-row class="" style="width: 30%">
        <el-col class="titleSize1">
          <div class="labelStyle1">处置状态: </div>
          <div class="titleStyle1"><el-tag :type="dealWithTypes[eventContext.deal_status || '1']">{{
            dealWithTypes1[eventContext.deal_status ||
            '1'] }}</el-tag></div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">首次发现时间: </div>
          <div class="titleStyle1">{{ getLabelValue('first_time', eventContext) }}</div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">最新发现时间: </div>
          <div class="titleStyle1">{{ getLabelValue('last_time', eventContext) }}</div>
        </el-col>
      </el-row>
      <el-row style="width: 35%">
        <el-col class="titleSize1">
          <div class="labelStyle1">事件ID: </div>
          <div class="titleStyle1">{{ getLabelValue('event_uid', eventContext) }}</div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">协议: </div>
          <div class="titleStyle1">{{ getLabelValue('protocol', eventContext) }}</div>
        </el-col>
        <el-col class="titleSize1">
          <div class="labelStyle1">url路径: </div>
          <div class="titleStyle1">{{ getLabelValue('key_url', eventContext) }}</div>
        </el-col>
      </el-row>
    </el-row>

    <el-row>
      <el-tabs v-model="activeName">
        <el-tab-pane label="处置情况" name="first">
          <el-row style="margin-top: 10px;padding-left: 20px">
            <el-col class="titleSize1">
              <div class="labelStyle">处置方式: </div>
              <div class="titleStyle">{{ dealMethods[getLabelValue('deal_model', eventContext)] }}</div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">处置结果: </div>
              <div class="titleStyle"><el-tag :type="dealWithTypes[eventContext.deal_status || '1']">{{
                dealWithTypes1[eventContext.deal_status ||
                '1'] }}</el-tag></div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">派单状态: </div>
              <div class="titleStyle"><el-tag :type="dealWithTexts[eventContext.dispatch_status || 1]">{{
                eventContext.dispatch_status }}</el-tag>
              </div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">工单号: </div>
              <div class="titleStyle">{{ getLabelValue('wo_no', eventContext) }}</div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">处置时间: </div>
              <div class="titleStyle">{{ getLabelValue('deal_time', eventContext) }}</div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">工单关闭时间: </div>
              <div class="titleStyle">{{ getLabelValue('close_time', eventContext) }}</div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">是否发微信: </div>
              <div class="titleStyle">{{ getLabelValue('webchat_forward_flag', eventContext) ? '是' : '否' }}</div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">处置说明: </div>
              <div class="titleStyle" v-sec-html="getLabelValue('deal_idea', eventContext)"></div>
            </el-col>
            <el-col class="titleSize1">
              <div class="labelStyle">处置建议: </div>
              <div class="titleStyle">{{ getLabelValue('solution', eventContext) }}</div>
            </el-col>
          </el-row>
        </el-tab-pane>

      </el-tabs>
    </el-row>

  </div>
  <!-- </el-drawer> -->
</template>

<script lang="ts" setup>
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { useRoute } from 'vue-router';
import { http } from "@/utils/http";
/** 安全事件详情 */
import attackTemplate from "@/views/modules/security/securityVulnerability/components/assetPerspectiveEventDetails/attackTemplate.vue";


const props = defineProps({
  eventRecord: Object,
  dealWith: Number,
})

const isTargetAsset = ref(true);
const isSourceAsset = ref(true);
const isTrue = ref(true);

const labelNumber = reactive({
  4: '封堵',
  3: '误报',
});

const activeName = ref('first');

const daterangeType = ref('3m');

const riskType = reactive({
  '1': {
    color: '#909399',
    value: '信息',
    titleColor: '#fff'
  },
  '2': {
    color: '#67C23A',
    value: '低危',
    titleColor: '#fff'
  },
  '3': {
    color: '#F98C3E',
    value: '中危',
    titleColor: '#fff'
  },
  '4': {
    color: '#DD1515',
    value: '高危',
    titleColor: '#fff'
  },
  '5': {
    color: '#B51994',
    value: '危急',
    titleColor: '#fff'
  },
});

const vulTypeTexts = reactive({
  5: "严重",
  4: "高危",
  3: "中危",
  2: "低危",
  1: "信息",
}); // Assuming reseverityTexts is defined somewhere in your code.

const dealWithTypes = reactive({
  '1': "warning",
  '2': "success",
});

const dealWithTypes1 = reactive({
  '1': "未处置",
  '2': "已处置",
});

const dealWithTexts = reactive({
  '未派单': "danger",
  '已派单': "success",
});

const dealMethods = reactive({
  0: "忽略告警",
  1: "工单处理",
  2: "设备自动阻拦",
  3: "调整预警级别",
  4: "手工处理",
  5: "重点关注",
  'misreport': "误报",
});

const item = reactive({});

const sourceAsset = reactive({
  model: {}
});

const targetAsset = reactive({
  model: {}
});

const bodyHeight = computed(() => {
  return `calc(100vh - 90px)`;
})
const event_id = computed(() => {
  return eventModel['event_id'];
})
const eventContext = computed(() => {
  const route = useRoute();
  return props.eventRecord || route.params || {};
})
const eventModel = computed(() => {
  let eventModel = { ...eventContext };
  eventModel['reseverityName'] = vulTypeTexts[eventModel['reseverity'] || 0];
  eventModel['webchat'] = eventModel['webchat_forward_flag'] ? "是" : "否";
  eventModel['deal_desc'] = dealMethods[eventModel['deal_model'] || 0];
  return eventModel;
})




const getLabelValue = (val, obj) => {
  let result = null
  if (val === "webchat_forward_flag") {
    result = obj[val]
  } else {
    result = (obj || '--') && (obj[val] || '--')
  }

  return result
}
const clear = () => {
  Object.keys(sourceAsset).forEach(key => {
    delete sourceAsset[key];
  });
  Object.keys(targetAsset).forEach(key => {
    delete targetAsset[key];
  });
}
const queryAll = () => {
  queryAssetInfo();
}
const queryAssetInfo = () => {
  let url = `eam-core/zcgl-common/redis/getIndexDataByIp`;
  http.get(url, {
    ipAddress: `${eventContext['src_ip']},${eventContext['dst_ip']}`,
  })
    .then(res => {
      let assets = res['data'];
      if (assets && assets.length != 0) {
        for (let asset of assets) {
          // if (asset.model) {
          if (asset) {
            // let { ipAddress } = asset.model;
            let { ipAddress } = asset;
            let ipAddressStr = `,${ipAddress},`;
            if (
              ipAddressStr.indexOf(`,${eventContext['src_ip']},`) > -1
            ) {

              asset.ipAddress = asset && asset.ipAddress ? asset.ipAddress : eventContext['src_ip']
              asset.zoneName__label = asset && asset.zoneName__label ? asset.zoneName__label : eventContext['src_org_name']
              asset.netPort = eventContext?.['src_port'] || ''
              asset.name = asset && asset.namse ? asset.name : eventContext['src_asset_name']
              asset.assetType__label = asset && asset.assetType__label ? asset.assetType__label : eventContext['src_asset_subtype_name']
              sourceAsset.model = asset;
            } else {
              sourceAsset["model"]['ipAddress'] = eventContext['src_ip'];
              sourceAsset["model"]['zoneName__label'] = eventContext['src_org_name'];
            }

            if (
              ipAddressStr.indexOf(`,${eventContext['dst_ip']},`) > -1
            ) {
              // targetAsset = asset;
              asset.ipAddress = asset && asset.ipAddress ? asset.ipAddress : eventContext['dst_ip']
              asset.zoneName__label = asset && asset.zoneName__label ? asset.zoneName__label : eventContext['dst_org_name']
              asset.netPort = eventContext?.['dst_port'] || ''
              asset.name = asset && asset.name ? asset.name : eventContext['dst_asset_name']
              asset.assetType__label = asset && asset.assetType__label ? asset.assetType__label : eventContext['dst_asset_subtype_name']
              targetAsset.model = asset;
            } else {

              targetAsset["model"]['ipAddress'] = eventContext['dst_ip'];
              targetAsset["model"]['zoneName__label'] = eventContext['dst_org_name'];
            }
          }
        }
      } else {

        sourceAsset.model = {
          ipAddress: eventContext['src_ip'],
          zoneName__label: eventContext['src_org_name'],
          netPort: eventContext?.['netPort'] || '',
          name: eventContext['src_asset_name'],
          assetType__label: eventContext['src_asset_subtype_name']
        };
        targetAsset.model = {
          ipAddress: eventContext['dst_ip'],
          zoneName__label: eventContext['dst_org_name'],
          netPort: eventContext?.['netPort'] || '',
          name: eventContext['dst_asset_name'],
          assetType__label: eventContext['dst_asset_subtype_name']
        };
      }
      isSourceAsset.value = false;
      isTargetAsset.value = false;

      setTimeout(() => {
        isSourceAsset.value = true;
        isTargetAsset.value = true;
      }, 1);


      sourceAsset["model"]['netPort'] = eventContext?.['src_port'] || '';
      targetAsset["model"]['netPort'] = eventContext?.['dst_port'] || '';

    })
    .catch(err => {
      console.error(err);
    });
}



onMounted(() => {
  queryAll();
})

watch(event_id, val => {
  if (val) {
    clear();
    queryAll();
  }
})


</script>

<style lang="scss" scoped>
.security-event-monitor-detail {
  overflow: auto;
  padding: 0px 10px;

  ::v-deep .el-radio__inner {
    display: none;
  }

  ::v-deep .el-radio__label {
    font-size: 12px;
  }

  .riskLayout {
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 50px;
    box-shadow: 5px 5px 2px 1px #d3d3d3;
  }

  .labelNumberLayout {
    border-radius: 5px;
    width: 38px;
    height: 24px;
    line-height: 22px;
    text-align: center;
    margin-top: -4px;
    border: 2px solid #f59a23;
    color: #f59a23;
  }

  .flexLayout {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }

  .titleColor {
    color: #00a0e9
  }

  .titleSize {
    font-size: 12px;
  }

  .titleSize1 {
    display: flex;
    font-size: 12px;
    margin-bottom: 30px;
  }

  .lineLayout {
    height: 1px;
    width: 100%;
    background-color: darkgrey;
  }

  .labelStyle {
    width: 120px;
    color: #565656;
    text-align: left;
    font-size: 12px;
  }

  .formLayout {
    padding-left: 50px;

    ::v-deep .el-form-item--mini.el-form-item {
      margin-bottom: 0px;
    }
  }

  .labelStyle1 {
    width: 110px;
    color: #565656;
    text-align: right;
    font-size: 12px;
  }

  .titleStyle1 {
    width: calc(100% - 110px);
    color: #565656;
    font-size: 12px;
    padding-left: 5px;
    font-weight: bold;
  }

  .titleStyle {
    width: calc(100% - 120px);
    color: #565656;
    padding-left: 5px;
    font-size: 12px;
    font-weight: bold;
  }

  .lineLayout::after {
    content: '';
    height: 12px;
    width: 12px;
    top: -6px;
    border-width: 2px 2px 0 0;
    border-color: darkgrey;
    border-style: solid;
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: absolute;
    left: 100%;
  }

  .formLayout ::v-deep .el-form-item__label {
    font-size: 12px;
    color: #aaa;
  }
}
</style>
