<template>
  <div class="p-3">
    <el-divider content-position="left">
      <sign-title title="任务信息" />
    </el-divider>
    <div class="flex-sc p-2 gap-1 pt-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20">任务名称：</el-text>
      <el-input v-model="form.policyName" placeholder="请输入任务名称" clearable maxlength="64" show-word-limit />
    </div>
    <div class="flex w-full">
      <div class="flex-sc gap-1 p-2 w-1/2">
        <span class="text-red-600">*</span>
        <el-text class="w-20 mr-1">任务类型：</el-text>
        <el-select v-model="form.vulScanTool" placeholder="请选择任务类型" clearable filterable>
          <el-option v-for="item in vulTaskTypeData" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
      <div class="flex-sc gap-1 p-2 w-1/2" v-if="form.vulScanTool == 'hillStone'">
        <span class="text-red-600">*</span>
        <el-text class="w-20">漏扫类型：</el-text>
        <el-select v-model="selectedScanTypes" placeholder="请选择任务类型" clearable filterable multiple>
          <el-option v-for="item in hillStoneVulTypeData" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="flex-sc p-2 gap-1 pb-5">
      <span class="text-red-600">*</span>
      <el-text class="w-20">执行规则：</el-text>
      <el-radio-group v-model="form.scheduleType" class="mr-5 w-40">
        <el-radio-button label="立即执行" value="0" />
        <el-radio-button label="定期执行" value="1" />
      </el-radio-group>
      <simple-schedule v-if="form.scheduleType == '1'" ref="simpleScheduleRef" v-model:exp="form.schedulingCycle"
        :enable-minute="false" :enable-hour="false" action-type="WM" />
    </div>
    <el-divider content-position="left">
      <sign-title title="执行范围" />
    </el-divider>
    <!-- <el-radio-group v-model="form.operationType" class="mt-5 mb-2">
      <el-radio label="自定义" value="1"/>
      <el-radio label="资产类型" value="2"/>
      <el-radio label="IP资源池" value="3"/>
    </el-radio-group> -->
    <div style="padding: 0.6rem 0;"></div>
    <ip-express-input
      :isAssetView="props.taskInfo['operationVulDataList'] && props.taskInfo['operationVulDataList'].length > 0"
      ref="ipExpInputRef" v-model:list="customIpData" />
    <!-- <asset-type-selector ref="assetTypeRef" v-model:checked-keys="checkedAssetTypes" v-if="form.operationType=='2'"/> -->
    <!-- <ip-pool-selector ref="ipPoolRef" v-model:checked-keys="checkedIpPools" v-if="form.operationType=='3'"/> -->
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, onMounted, reactive, ref, toRefs, watch } from 'vue';
import SignTitle from "@/components/SignTitle/SignTitle.vue";
import { addVulTask, updateVulTask, VulTaskFormData } from "@/views/modules/eam/assetMap/taskManage/api/vulTaskApi";
import { hillStoneVulTypeData, vulTaskTypeData } from "@/views/modules/eam/assetMap/taskManage/util/task_data";
import SimpleSchedule from "@/components/SimpleSchedule/SimpleSchedule.vue";
// import IpExpressInput from "@/views/modules/eam/assetMap/components/IpExpressInput.vue";
import IpExpressInput from "@/views/modules/security/securityVulnerability/components/batchVerification/IpExpressInput.vue";
import AssetTypeSelector from "@/views/modules/eam/assetMap/components/AssetTypeSelector.vue";
import IpPoolSelector from "@/views/modules/eam/assetMap/components/IpPoolSelector.vue";
import { ResultStatus } from "@/utils/http/types";
import {
  countFraAssetIp
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface_vulView";

const { $message } = getCurrentInstance().appContext.config.globalProperties;

const ipExpInputRef = ref(null);
const assetTypeRef = ref(null);
const ipPoolRef = ref(null);
const simpleScheduleRef = ref(null);

//组件属性
const props = defineProps({
  taskInfo: {
    type: Object,
    default: () => {
    }
  }
});

// watch(props, val => {
//   if (val.taskInfo['operationVulDataList'] && val.taskInfo['operationVulDataList'].length > 0) {
//     countFraAssetIp({
//       vulIds: (val.taskInfo['operationVulDataList']).join()
//     }).then(res => {
//       if (res['data'] && res['data'].length > 0) {
//         const tmpIpArray = res['data'].map(item => {
//           return item['ip'];
//         })
//         const ipArr = new Array(...new Set(tmpIpArray));
//         console.log(ipArr);
//         if (state.form.operationType === '1') {
//           state.form['relatedVulIds'] = ipArr.join();
//           ipExpInputRef.value.initText(ipArr);
//         }
//       }
//     })
//   }
// }, { deep: true, immediate: true })

//声明事件
const emit = defineEmits(["success"]);

//数据对象
const state = reactive({
  form: {
    scheduleType: "1",
    operationType: "1"
  } as VulTaskFormData,
  customIpData: [] as Array<string>,
  selectedScanTypes: [],
  checkedAssetTypes: [],
  checkedIpPools: []
})

const {
  form,
  customIpData,
  selectedScanTypes,
  checkedAssetTypes,
  checkedIpPools
} = toRefs(state)

//提交表单数据
const submitData = async () => {
  if (validateForm()) {
    //处理漏扫类型
    if (state.form.vulScanTool === 'hillStone') {
      state.form.taskType = state.selectedScanTypes.join(',');
    }

    //处理执行范围
    if (state.form.operationType === '1') {
      state.form.operationDataList = state.customIpData;
      if (state.form['relatedVulIds'] && state.form['relatedVulIds'].length > 0) {
        state.form['relatedVulIds'] = state.form['relatedVulIds'];
      }

    } else if (state.form.operationType === '2') {
      state.form.operationDataList = state.checkedAssetTypes;
    } else if (state.form.operationType === '3') {
      state.form.operationDataList = state.checkedIpPools;
    }

    //提交数据到服务端
    if (state.form.taskallid != null) {
      //更新
      const { status } = await updateVulTask(state.form);
      if (status == ResultStatus.Success) {
        $message({
          type: "success",
          message: "已成功更新任务！"
        });
        emit("success");
      }

    } else {
      //新增
      const { status } = await addVulTask(state.form);
      if (status == ResultStatus.Success) {
        $message({
          type: "success",
          message: "已成功创建任务！"
        });
        emit("success");
      }
    }
  }
}

//校验表单
const validateForm = (): boolean => {
  //校验任务名称
  if (!(state.form.policyName && state.form.policyName.trim().length > 0)) {
    $message({
      type: 'error',
      message: '请输入任务名称！'
    });
    return false;
  }

  //校验任务类型
  if (!state.form.vulScanTool) {
    $message({
      type: 'error',
      message: '请选择任务类型！'
    });
    return false;
  }

  //校验漏洞类型
  if (state.form.vulScanTool === 'hillStone') {
    if (!(state.selectedScanTypes && state.selectedScanTypes.length > 0)) {
      $message({
        type: 'error',
        message: '请选择漏洞类型！'
      });
      return false;
    }
  }

  //校验执行规则
  if (state.form.scheduleType === '1') {
    simpleScheduleRef.value.generateExp();
  }

  //校验执行范围
  let validateResult = true;
  if (!(props.taskInfo['operationVulDataList'] && props.taskInfo['operationVulDataList'].length > 0)) {
    validateResult = handleOperationTypeChange();
  }

  if (!validateResult) {
    return false;
  }
  return true;
}

//校验执行范围
const handleOperationTypeChange = (): boolean => {
  if (true || state.form.operationType === '1') {
    console.log(ipExpInputRef.value.validateIpText());
    return ipExpInputRef.value.validateIpText();
  }
  // else if (state.form.operationType === '2') {
  //   return assetTypeRef.value.validateChecks();
  // } else if (state.form.operationType === '3') {
  //   return ipPoolRef.value.validateChecks();
  // }
  return false;
}

//挂载初始化
onMounted(() => {
  if (state.form.operationType === '1') {
    state.customIpData = props.taskInfo.operationDataList;
    ipExpInputRef.value.initText(props.taskInfo.operationDataList);
    if (props.taskInfo['operationVulDataList'] && props.taskInfo['operationVulDataList'].length > 0) {
      state.form['relatedVulIds'] = props.taskInfo['operationVulDataList'].join();
      ipExpInputRef.value.initText(props.taskInfo['operationVulDataList']);
    }
  }
  // if (props.taskInfo.taskallid) {
  //编辑初始化
  // state.form = props.taskInfo as VulTaskFormData;
  if (state.form.taskType && state.form.taskType.length > 0) {
    state.selectedScanTypes = state.form.taskType.split(",");
  }

  //定时周期
  if (state.form.scheduleType === '1') {
    simpleScheduleRef.value.initExp(state.form.schedulingCycle);
  }

  //执行范围
  if (state.form.operationType === '1') {
    state.customIpData = props.taskInfo.operationDataList;
    ipExpInputRef.value.initText(props.taskInfo.operationDataList);
  } else if (state.form.operationType === '2') {
    state.checkedAssetTypes = props.taskInfo.operationDataList;
  } else if (state.form.operationType === '3') {
    state.checkedIpPools = props.taskInfo.operationDataList;
  }
  // } else {
  //   if (state.form.scheduleType === '1') {
  //     simpleScheduleRef?.value.initExp();
  //   }
  // }
})

//对外暴露可调用方法
defineExpose({ submitData });

</script>
