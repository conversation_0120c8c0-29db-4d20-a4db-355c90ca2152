//风险等级数据
const riskLevelData = [
  {
    id: "5",
    label: "严重"
  },
  {
    id: "4",
    label: "高危"
  },
  {
    id: "3",
    label: "中危"
  },
  {
    id: "2",
    label: "低危"
  },
  {
    id: "1",
    label: "信息"
  },
]

//事件处置状态数据
const segmentData = [
  {
    label: "未处置",
    value: "1",
  },
  {
    label: "误报",
    value: "3",
  },
  {
    label: "全部",
    value: "0",
  },
]

//获取指定级别的Label
const getRiskLevelLabel = (id: string) => {
  const matchData = riskLevelData.find((item) => item.id === id);
  if (matchData) {
    return matchData.label;
  }
  return `未知(${id})`;
}

//根据风险级别id获取颜色
const getRiskLevelColor = (id: string) => {
  switch (id) {
    case "5":
      return "#A50003";
    case "4":
      return "#FF0408";
    case "3":
      return "#FF964B";
    case "2":
      return "#0091FF";
    case "1":
      return "#028015";
    default:
      return "#000000";
  }
}

//获取处置状态Label
const getSegmentLabel = (id: string) => {
  switch (id) {
    case "1":
      return "未处置";
    case "2":
      return "已处置";
    default:
      return `未知(${id})`;
  }
}

//获取处置状态颜色
const getDealStatusType = (id: string) => {
  switch (id) {
    case "1":
      return "warning";
    case "2":
      return "success";
    default:
      return "info";
  }
}

export {
  riskLevelData,
  segmentData,
  getRiskLevelLabel,
  getRiskLevelColor,
  getSegmentLabel,
  getDealStatusType
}
