<template>
  <div>
    <div class="flex-bc pl-1 pr-1">
      <div class="flex-sc gap-1">
        <headSign class="w-4" />
        <el-badge :value="timeLineInfo.count">
          <el-text
            size="default"
            class="font-medium hover:cursor-pointer hover:text-primary"
            @click="drawerVisible = true"
          >
            风险告警
          </el-text>
        </el-badge>
        <div class="flex-sc ml-16 gap-2">
          <el-tag type="info" v-if="timeLineInfo.attack_result" effect="dark">{{
            getAttackResult(timeLineInfo.attack_result)
          }}</el-tag>
          <el-tag
            type="warning"
            v-if="timeLineInfo.event_subtype_name"
            effect="dark"
            >{{ timeLineInfo.event_subtype_name }}
          </el-tag>
          <el-tag
            v-if="timeLineInfo.reseverity"
            :color="getRiskLevelColor(timeLineInfo.reseverity)"
            class="text-white border-none"
          >
            {{ getRiskLevelLabel(timeLineInfo.reseverity) }}
          </el-tag>
        </div>
      </div>
      <div>
        <el-text type="primary"
          >数据来源：{{ timeLineInfo.event_agent }}</el-text
        >
      </div>
    </div>
    <el-divider border-style="dashed" />
    <div class="p-2">
      <el-descriptions :column="1">
        <el-descriptions-item label="攻击IP：" label-class-name="font-medium">
          {{ timeLineInfo.src_ip }}
        </el-descriptions-item>
        <el-descriptions-item label="受害IP：" label-class-name="font-medium">
          {{ timeLineInfo.dst_ip }}
        </el-descriptions-item>
        <el-descriptions-item label="事件类型：" label-class-name="font-medium">
          {{ timeLineInfo.event_subtype_name }}
        </el-descriptions-item>
        <el-descriptions-item label="影响：" label-class-name="font-medium">
          {{ timeLineInfo.event_detail }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <alarm-detail-info-with-event
      v-model:visible="drawerVisible"
      :time-line-info="timeLineInfo"
    />
  </div>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from "vue";
import headSign from "@/assets/security/u4403.svg?component";
import AlarmDetailInfoWithEvent from "@/views/modules/security/event/components/AlarmDetailInfoWithEvent.vue";
import {
  getRiskLevelColor,
  getRiskLevelLabel
} from "@/views/modules/security/event/util/event_data";
import { getAttackResult } from "@/views/modules/security/event/api/SecurityEventApi";

// 组件属性
const props = defineProps({
  timeLineInfo: {
    type: Object,
    default: () => {}
  }
});

//数据对象
const state = reactive({
  drawerVisible: false,
  data: []
});

const { drawerVisible, data } = toRefs(state);
</script>
