<template>
  <div>
    <div ref="barChartRef" style="width: 100%; height: 200px"/>
  </div>
</template>

<script lang="ts" setup>
import {computed, type Ref, ref, watch} from 'vue';
import {delay, EchartOptions, useDark, useECharts, UtilsEChartsOption} from "@pureadmin/utils";
import {getEventWeekDistributeData} from "@/views/modules/security/event/api/SecurityEventApi";

const {isDark} = useDark();
const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

// 组件属性
const props = defineProps({
  eventInfo: {
    type: Object,
    default: () => {
    }
  },
  dateRangeSign: {
    type: String,
    default: () => {
    }
  }
});

// 监听组件属性
watch(() => props.dateRangeSign, (newValue) => {
  if (newValue && newValue.length > 0) {
    delay(300).then(() => loadChartData());
  }
});

const chartOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  grid: {
    left: "2%",
    right: "4%",
    bottom: "3%",
    top: "5%",
    containLabel: true
  },
  xAxis: [
    {
      type: "category",
      data: []
    }
  ],
  yAxis: [
    {
      type: "value"
    }
  ],
  series: [
    {
      name: "事件数量：",
      type: "bar",
      data: [],
      color: ["#E37F3C"],
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(180, 180, 180, 0.2)'
      }
    }
  ]
};

const barChartRef = ref<HTMLDivElement | null>(null);
const {setOptions, resize, getInstance} = useECharts(
  barChartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);
setOptions(chartOption as UtilsEChartsOption);

watch(theme, () => {
  delay(300).then(() => loadChartData());
});

//加载图形数据
const loadChartData = async () => {
  const weekRes = await getEventWeekDistributeData({
    dateRange: props.dateRangeSign,
    event_id: props.eventInfo.event_id
  });
  const data = weekRes.data.rows;
  //更新Chart的数据
  if (data && data.length > 0) {
    const xData = [];
    data.forEach((item: any) => xData.push(item.time));

    const sData = [];
    data.forEach((item: any) => sData.push(item.cnt));

    const chart = getInstance();
    const option = chart!.getOption();
    if (option) {
      option.xAxis[0].data = xData;
      option.series[0].data = sData;
      chart!.setOption(option);
      chart!.resize();
    }
  }
}

// 重置图像大小
const resizeChart = () => {
  delay(300).then(() => {
    resize();
  });
}

defineExpose({resizeChart});

</script>
