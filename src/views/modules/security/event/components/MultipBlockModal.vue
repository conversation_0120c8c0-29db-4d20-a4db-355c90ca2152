<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    :destroy-on-close="true"
    width="45%"
  >
    <!--    <avue-form-->
    <!--      ref="blockFormRef"-->
    <!--      :option="option"-->
    <!--      v-model="form"-->
    <!--      class="mt-2"-->
    <!--    />-->
    <el-form ref="blockFormRef" label-width="90px" :model="form">
      <el-row>
        <el-col :span="12">
          <el-form-item
            label="封堵IP类型"
            prop="ipType"
            :rules="[{ required: true, message: '请选择封堵IP类型' }]"
          >
            <el-select
              v-model="form.ipType"
              placeholder="请选择封堵IP类型"
              @change="handleBlockTypeChange"
            >
              <el-option label="源IP" value="src"></el-option>
              <el-option label="目的IP" value="dst"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="封堵IP地址"
            prop="ipList"
            :rules="[{ required: true }]"
          >
            <el-select
              v-model="form.ipList"
              readonly
              multiple
              collapse-tags
              :multiple-limit="2"
              placeholder="请选择封堵IP地址"
            >
              <el-option
                v-for="ip in mergeSrcAndDstIps"
                :key="ip"
                :value="ip"
                :label="ip"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="防火墙"
            prop="firewallIds"
            :rules="[{ required: true, message: '请选择防火墙' }]"
          >
            <el-tree-select
              ref="firewallTree"
              v-model="form.firewallIds"
              :data="fireWallTreeData"
              node-key="id"
              :props="{
                label: 'name',
                value: 'id'
              }"
              multiple
              show-checkbox
              collapse-tags
              :multiple-limit="2"
              placeholder="请选择防火墙"
            >
            </el-tree-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="封堵时间"
            prop="expireTime"
            :rules="[{ required: true, message: '请选择封堵时间' }]"
          >
            <el-select v-model="form.expireTime" placeholder="请选择封堵时间">
              <el-option
                v-for="(expireTimeOption, index) in blockTimeData"
                :key="index"
                v-bind="expireTimeOption"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="封堵原因"
            prop="plugDescribe"
            :rules="[{ required: true, message: '请输入封堵原因' }]"
          >
            <el-input
              type="textarea"
              v-model="form.plugDescribe"
              placeholder="请输入封堵原因"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-CircleCheck')"
        @click="submitHandler"
      >
        确定
      </el-button>
      <el-button :icon="useRenderIcon('EP-CircleClose')" @click="cancel()">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getBlockSource,
  getFireWallTree,
  submitBlockRequest
} from "@/views/modules/security/event/api/IpBlockApi";
import { validIPAddress } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";
import { dealEvent } from "@/views/modules/security/event/api/SecurityEventApi";

const { $message } = getCurrentInstance().appContext.config.globalProperties;
const blockFormRef = ref(null);
const firewallTree = ref(null);

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  title: {
    type: String,
    default: ""
  },
  ipAddress: {
    type: String,
    default: ""
  },
  extParams: {
    type: Object,
    default: () => {
      return {};
    }
  },
  defaultPlugLabel: String,
  eventUids: Array as PropType<string[]>,
  srcIps: Array as PropType<string[]>,
  dstIps: Array as PropType<string[]>
});

const mergeSrcAndDstIps = computed(() => {
  let ips = [];
  for (let ip of props.srcIps || []) {
    if (!ips.includes(ip)) {
      ips.push(ip);
    }
  }
  for (let ip of props.dstIps || []) {
    if (!ips.includes(ip)) {
      ips.push(ip);
    }
  }
  return ips;
});

//定义事件
const emit = defineEmits(["update:visible", "deal-success"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  fireWallTreeData: [],
  blockSourceData: [],
  form: {
    ipList: [],
    ipType: "src",
    ipAddress: "",
    plugLabel: "",
    plugDescribe: "-",
    firewall: "",
    expireTime: "0",
    firewallIds: []
  }
});
const { form, dialogVisible, fireWallTreeData, blockSourceData } =
  toRefs(state);

const fillFirewallIds = (items: any[], firewallIds: string[]) => {
  for (let item of items) {
    let { id, children = [] } = item;
    if (!firewallIds.includes(id)) {
      firewallIds.push(id);
    }
    if (children) {
      fillFirewallIds(children, firewallIds);
    }
  }
};

const selectAllFireWall = () => {
  // 默认全选
  let firewallIds = [];
  fillFirewallIds(state.fireWallTreeData, firewallIds);
  state.form.firewallIds = firewallIds;
};

//加载防火墙数据
const loadFireWallData = async () => {
  const treeRes = await getFireWallTree();
  state.fireWallTreeData = treeRes.data || [];
  selectAllFireWall();
};
loadFireWallData();

//加载封堵来源数据
const loadBlockSourceData = async () => {
  const res = await getBlockSource({ queryDate: "all" });
  state.blockSourceData = res.data;
};
loadBlockSourceData();

const blockTimeData = [
  {
    label: " 1天",
    value: "1"
  },
  {
    label: " 2天",
    value: "2"
  },
  {
    label: " 3天",
    value: "3"
  },
  {
    label: " 4天",
    value: "4"
  },
  {
    label: " 5天",
    value: "5"
  },
  {
    label: " 6天",
    value: "6"
  },
  {
    label: " 7天",
    value: "7"
  },
  {
    label: " 15天",
    value: "15"
  },
  {
    label: " 30天",
    value: "30"
  },
  {
    label: "永久",
    value: "0"
  }
];

// const option = reactive({
//   menuPosition: "right",
//   submitBtn: false,
//   emptyBtn: false,
//   column: [
//     {
//       label: "IP地址：",
//       prop: "ipAddress",
//       span: 12,
//       maxlength: 64,
//       showWordLimit: true,
//       type: "input",
//       rules: [
//         {
//           required: true,
//           message: "请输入IP地址",
//           trigger: "blur"
//         },
//         { validator: validateIpFormat, trigger: "blur" }
//       ]
//     },
//     {
//       label: "封堵来源",
//       prop: "plugLabel",
//       span: 12,
//       type: "select",
//       dicData: blockSourceData,
//       props: {
//         label: "plugLabel",
//         value: "plugLabel"
//       },
//       rules: [
//         {
//           required: true,
//           message: "请选择封堵来源",
//           trigger: "blur"
//         }
//       ]
//     },
//     {
//       label: "防火墙",
//       prop: "firewallIds",
//       span: 12,
//       type: "tree",
//       multiple: true,
//       dicData: fireWallTreeData,
//       props: {
//         label: "name",
//         value: "id"
//       },
//       rules: []
//     },
//     {
//       label: "封堵时间",
//       prop: "expireTime",
//       span: 12,
//       type: "select",
//       dicData: blockTimeData
//     },
//     {
//       label: "封堵原因",
//       prop: "plugDescribe",
//       span: 24,
//       type: "textarea",
//       rules: [
//         {
//           required: true,
//           message: "请填写封堵原因",
//           trigger: "blur"
//         }
//       ]
//     }
//   ]
// });

const handleBlockTypeChange = () => {
  let val = state.form.ipType;
  let ipList = [];
  if (val == "src") {
    ipList.push(...props.srcIps);
  } else {
    ipList.push(...props.dstIps);
  }
  state.form.ipList = ipList;
};

watch(
  () => props.visible,
  async newValue => {
    state.dialogVisible = newValue;
    state.form.ipList = props.srcIps;
    state.form.ipType = "src";
    selectAllFireWall();
  }
);

//提交封堵请求触发
const submitHandler = () => {
  blockFormRef.value.validate(async valid => {
    if (valid) {
      let { expireTime, ipType, plugDescribe: deal_idea } = state.form;
      let firewallNodes = firewallTree.value.getCheckedNodes(true);
      // 调用处理事件函数
      await dealEvent({
        expireTime,
        ipType,
        deal_idea,
        firewall: firewallNodes,
        deal_model: "forbid",
        deal_status: "disposed",
        event_uid: props.eventUids.join(","),
        ...props.extParams
      });
      // 弹出成功提示
      $message({
        type: "success",
        message: `操作成功！`
      });
      emit("deal-success");
      cancel();
      // const res = await submitBlockRequest(state.form);
      // if (res.status === ResultStatus.Success) {
      //   // $message({
      //   //   message: `已成功封堵 IP 地址：${props.ipAddress}!`,
      //   //   type: "success"
      //   // });
      //   $message({
      //     message: `操作成功!`,
      //     type: "success"
      //   });
      //   cancel();
      // }
    }
  });
};

const cancel = () => {
  state.form = {
    ipType: "src",
    ipAddress: props.ipAddress,
    plugLabel: "",
    plugDescribe: "-",
    firewall: "",
    expireTime: "0",
    firewallIds: [],
    ipList: []
  };
  emit("update:visible", false);
};
</script>
