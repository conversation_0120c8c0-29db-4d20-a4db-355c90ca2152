<template>
  <div>
    <div class="flex-bc">
      <div class="flex-sc gap-3">
        <el-tag
          :color="getRiskLevelColor(alarmInfo.reseverity)"
          class="text-white border-none"
        >
          {{ getRiskLevelLabel(alarmInfo.reseverity) }}
        </el-tag>
        <h2>{{ alarmInfo.event_subtype_name }}</h2>
        <el-tag type="primary">{{
          getSegmentLabel(alarmInfo.deal_status)
        }}</el-tag>
      </div>
      <div class="flex-sc gap-2">
        <el-tag>{{ alarmInfo.event_agent }}</el-tag>
        <el-text>最近一次告警时间：{{ alarmInfo.last_time }}</el-text>
      </div>
    </div>
    <el-divider />
    <el-descriptions
      :column="3"
      label-width="120px"
      direction="horizontal"
      border
      class="pt-2"
    >
      <el-descriptions-item label="源IP">{{
        alarmInfo.src_ip
      }}</el-descriptions-item>
      <el-descriptions-item label="目的IP">{{
        alarmInfo.dst_ip
      }}</el-descriptions-item>
      <el-descriptions-item label="攻击阶段" width="15%">
        {{ alarmInfo.attack_stage }}
      </el-descriptions-item>
      <el-descriptions-item label="源端口">{{
        alarmInfo.src_port
      }}</el-descriptions-item>
      <el-descriptions-item label="目的端口">{{
        alarmInfo.dst_port
      }}</el-descriptions-item>
      <el-descriptions-item label="状态码">{{
        alarmInfo.status_code
      }}</el-descriptions-item>
      <el-descriptions-item label="攻击结果">{{
        getAttackResult(alarmInfo.attack_result_text)
      }}</el-descriptions-item>
      <el-descriptions-item label="攻击次数">{{
        alarmInfo.attack_cnt
      }}</el-descriptions-item>
      <el-descriptions-item label="攻击国家">{{
        alarmInfo.src_country_name
      }}</el-descriptions-item>
      <el-descriptions-item label="URL" :span="3">
        <div style="word-break: break-all; word-wrap: break-word">
          {{ alarmInfo.key_url }}
        </div></el-descriptions-item
      >
      <el-descriptions-item label="告警描述" :span="3">{{
        alarmInfo.event_detail
      }}</el-descriptions-item>
      <el-descriptions-item label="告警影响" :span="3">{{
        alarmInfo.damage_event_content
      }}</el-descriptions-item>
      <el-descriptions-item label="分析建议" :span="3">{{
        alarmInfo.analyze_suggest
      }}</el-descriptions-item>
      <el-descriptions-item label="解决方案" :span="3">{{
        alarmInfo.solution
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import {
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel
} from "@/views/modules/security/event/util/event_data";
import { getAttackResult } from "@/views/modules/security/event/api/SecurityEventApi";

//组件属性
const props = defineProps({
  alarmInfo: {
    type: Object,
    default: () => {}
  }
});
</script>
