<template>
  <div>
    <div class="flex justify-end pr-5">
      <el-segmented
        v-model="dateRangeSign"
        :options="timeSegmentOptions"
        @change="segmentChangeHandler"
      >
      </el-segmented>
      <div class="ml-3">
        <el-date-picker
          v-model="dateTimeRange"
          type="daterange"
          range-separator="到"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
    </div>
    <div class="p-5">
      <el-timeline style="min-width: 700px">
        <el-timeline-item
          v-for="(item, index) in timeLineData"
          :key="index"
          :timestamp="item.time + ':00'"
          :hollow="true"
          type="primary"
          placement="top"
        >
          <alarms-time-line-content :time-line-info="item" />
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, toRefs } from "vue";
import AlarmsTimeLineContent from "@/views/modules/security/event/components/AlarmsTimeLineContent.vue";
import dayjs from "dayjs";
import { getAlarmTimelineData } from "@/views/modules/security/event/api/SecurityEventApi";

// 组件属性
const props = defineProps({
  eventInfo: {
    type: Object,
    default: () => {}
  }
});

//数据对象
const state = reactive({
  dateRangeSign: "1",
  dateTimeRange: [],
  timeSegmentOptions: [
    {
      label: "近24小时",
      value: "1"
    },
    {
      label: "近7天",
      value: "7"
    },
    {
      label: "近30天",
      value: "30"
    }
  ],
  timeLineData: []
});

const { dateRangeSign, dateTimeRange, timeSegmentOptions, timeLineData } =
  toRefs(state);

//初始化默认时间范围
const initTimeRange = (day: number) => {
  state.dateTimeRange = [dayjs().add(-day, "d"), dayjs().endOf("day")];
};

//时间范围改变
const segmentChangeHandler = (value: string) => {
  initTimeRange(parseInt(value));
  queryTimeLineData();
};

//查询时间线数据
const queryTimeLineData = async () => {
  const timeLineRes = await getAlarmTimelineData({
    dateRange: [
      dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
      dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
    ],
    src_ip: props.eventInfo.src_ip,
    dst_ip: props.eventInfo.dst_ip
  });
  state.timeLineData = timeLineRes.data;
};

onMounted(() => {
  initTimeRange(parseInt(state.dateRangeSign));
  queryTimeLineData();
});
</script>
