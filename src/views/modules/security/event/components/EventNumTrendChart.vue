<template>
  <div ref="trendChartRef" style="width: 100%; height: 200px" />
</template>

<script lang="ts" setup>
import { computed, Ref, ref, watch } from "vue";
import {
  delay,
  EchartOptions,
  useDark,
  useECharts,
  UtilsEChartsOption
} from "@pureadmin/utils";
import { getEventTrendData } from "@/views/modules/security/event/api/SecurityEventApi";

const { isDark } = useDark();
const theme: EchartOptions["theme"] = computed(() => {
  return isDark.value ? "dark" : "light";
});

// 组件属性
const props = defineProps({
  eventInfo: {
    type: Object,
    default: () => {}
  },
  dateRangeSign: {
    type: String,
    default: () => {}
  }
});

// 监听组件属性
watch(
  () => props.dateRangeSign,
  newValue => {
    if (newValue && newValue.length > 0) {
      delay(300).then(() => loadTrendChartData());
    }
  }
);

//事件趋势图
const trendChartOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  xAxis: [
    {
      type: "category",
      data: []
    }
  ],
  yAxis: [
    {
      type: "value"
    }
  ],
  series: [
    {
      data: [],
      name: "事件数量",
      type: "line"
    }
  ],
  grid: {
    left: "3%",
    right: "2%",
    top: "5%",
    bottom: "10%"
  }
};
const trendChartRef = ref<HTMLDivElement | null>(null);
const { setOptions, resize, getInstance } = useECharts(
  trendChartRef as Ref<HTMLDivElement>,
  {
    theme
  }
);
setOptions(trendChartOption as UtilsEChartsOption);

//加载数量趋势图数据
const loadTrendChartData = async () => {
  const trendRes = await getEventTrendData({
    dateRange: props.dateRangeSign,
    event_id: props.eventInfo.event_id
  });
  const data = trendRes.data.rows;
  //更新Chart的数据
  if (data && data.length > 0) {
    const xData = [];
    data.forEach((item: any) => xData.push(item.time));

    const sData = [];
    data.forEach((item: any) => sData.push(item.cnt));

    const chart = getInstance();
    const option = chart!.getOption();
    if (option) {
      option.xAxis[0].data = xData;
      option.series[0].data = sData;
      chart!.setOption(option);
      chart!.resize();
    }
  }
};

watch(theme, () => {
  delay(300).then(() => loadTrendChartData());
});

// 重置图像大小
const resizeChart = () => {
  delay(300).then(() => {
    const chart = getInstance();
    chart!.resize();
  });
};

defineExpose({ resizeChart });
</script>
