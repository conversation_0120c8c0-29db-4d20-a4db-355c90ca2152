<template>
  <div>
    <div class="flex-bc">
      <el-text class="font-bold">资产信息</el-text>
      <div class="flex-sc">
        <el-button
          link
          type="primary"
          :icon="useRenderIcon('RI-CloseCircleFill')"
          @click="ipBlockHandler"
          >封堵
        </el-button>
        <!--        <el-button link type="primary" :icon="useRenderIcon('EP-View')"-->
        <!--          >详情</el-button-->
        <!--        >-->
      </div>
    </div>
    <el-divider />
    <div>
      <template v-if="existAsset">
        <div class="flex-sc">
          <el-text class="font-medium">资产名称：</el-text>
          <el-text>{{ asset.name }}</el-text>
        </div>
        <div class="flex-sc">
          <el-text class="font-medium">资产所属组织：</el-text>
          <el-text>{{ asset.zoneName__label }}</el-text>
        </div>
        <div class="flex-sc">
          <el-text class="font-medium">归属业务系统：</el-text>
          <el-text>{{ asset.operationSystem__label }}</el-text>
        </div>
        <div class="flex-sc">
          <el-text class="font-medium"> 所在位置：</el-text>
          <el-text>{{ asset.phyPosition__label }}</el-text>
        </div>
        <div class="flex-sc">
          <el-text class="font-medium">责任人：</el-text>
          <el-text>{{ asset.assetDuty }}</el-text>
        </div>
        <div class="flex-sc">
          <el-text class="font-medium">MAC地址：</el-text>
          <el-text>{{ asset.macAddress }}</el-text>
          <!--        <el-text v-if="direction === 'src'">{{ eventInfo.src_mac }}</el-text>-->
          <!--        <el-text v-if="direction === 'dst'">{{ eventInfo.dst_mac }}</el-text>-->
        </div>
        <div class="flex-sc">
          <el-text class="font-medium">最后一次在线时间：</el-text>
          <el-text>{{ asset.lastOnlineTime }}</el-text>
        </div>
      </template>
      <div class="flex-sc">
        <el-text class="font-medium">IP归属：</el-text>
        <el-text>{{
          direction == "src"
            ? eventInfo.src_country_name
            : eventInfo.dst_country_name
        }}</el-text>
      </div>
      <!--      <el-empty description="未关联到资产" :image-size="25" v-else></el-empty>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { computed, PropType } from "vue";
// 组件属性
const props = defineProps({
  eventInfo: {
    type: Object,
    default: () => {}
  },
  assetInfo: {
    type: Object as PropType<any>,
    default: () => {}
  },
  direction: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["ipBlock"]);
const asset = computed(() => props.assetInfo || {});
const existAsset = computed(() => !!asset.value.name);
const ipBlockHandler = () => {
  const ipAddress =
    props.direction === "src" ? props.eventInfo.src_ip : props.eventInfo.dst_ip;
  emit("ipBlock", ipAddress);
};
</script>
