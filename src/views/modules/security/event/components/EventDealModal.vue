<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    :destroy-on-close="true"
    width="45%"
  >
    <avue-form ref="dealFormRef" :option="option" v-model="deal" />
    <div class="pl-5">
      <el-text class="text-primary"
        >注：本次共处置 {{ eventUnitIds.length }} 条事件</el-text
      >
    </div>
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-CircleCheck')"
        @click="submitHandler"
      >
        确定
      </el-button>
      <el-button :icon="useRenderIcon('EP-CircleClose')" @click="cancel()">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, toRefs, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { dealEvent } from "@/views/modules/security/event/api/SecurityEventApi";

const { $message } = getCurrentInstance().appContext.config.globalProperties;
const dealFormRef = ref(null);

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  title: {
    type: String,
    default: ""
  },
  eventUnitIds: {
    type: Array<string>,
    default: () => []
  },
  defaultAction: {
    type: String,
    default: null
  }
});

//定义事件
const emit = defineEmits(["update:visible", "dealSuccess"]);

const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "处理说明",
      prop: "deal_idea",
      span: 24,
      maxlength: 200,
      showWordLimit: true,
      type: "textarea",
      rules: [
        {
          required: true,
          message: "请输入处理说明",
          trigger: "blur"
        }
      ]
    },
    {
      label: "处置动作",
      prop: "deal_model",
      span: 8,
      type: "select",
      dicData: [
        {
          label: "误报",
          value: "misreport"
        },
        {
          label: "人工处置",
          value: "manual"
        }
      ],
      rules: [
        {
          required: true,
          message: "请选择处置动作",
          trigger: "blur"
        }
      ]
    }
  ]
});

//数据对象
const state = reactive({
  dialogVisible: false,
  deal: {
    deal_idea: "",
    deal_model: "",
    deal_status: "disposed"
  }
});
const { deal, dialogVisible } = toRefs(state);

watch(
  () => props.visible,
  async newValue => {
    state.dialogVisible = newValue;
    state.deal.deal_idea = "";
    state.deal.deal_model = props.defaultAction;
  }
);

watch(
  () => props.defaultAction,
  async newValue => {
    state.deal.deal_model = newValue;
  }
);

// 定义提交处理函数
const submitHandler = () => {
  dealFormRef.value.validate(async (valid: boolean, done) => {
    if (valid) {
      // 调用处理事件函数
      await dealEvent({
        ...state.deal,
        event_uid:
          props.eventUnitIds.length == 1
            ? props.eventUnitIds[0]
            : props.eventUnitIds.join(",")
      });
      // 弹出成功提示
      $message({
        type: "success",
        message: `已成功处置 ${props.eventUnitIds.length} 条事件！`
      });
      // 触发dealSuccess事件
      emit("dealSuccess");
      // 执行done函数
      done();
      // 取消操作
      cancel();
    }
  });
};

const cancel = () => {
  emit("update:visible", false);
};
</script>
