<template>
  <div ref="chartWrapEl" class="w-full flex-c h-full">
    <ChartComponent
      renderer="canvas"
      :option="state.chartOption"
      @chart-click="handleChartClick"
      @resize="handleResize"
    >
    </ChartComponent>
  </div>
</template>
<script lang="ts" setup>
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import { EChartsOption } from "echarts";
import { PropType, reactive, ref, toRefs, watch } from "vue";
import { getRiskLevelColor } from "@/views/modules/security/event/util/event_data";
const emits = defineEmits(["on-click-attackline"]);
interface TreeNode {
  name: string;
  children?: TreeNode[];
  tag: "src" | "dst";

  [key: string]: any;
}

interface AttackData {
  src: TreeNode;
  dst: TreeNode;
}

const chartWrapEl = ref<HTMLDivElement>();
// 定义
const props = defineProps({
  attackData: Object as PropType<AttackData>
});

const state: {
  chartOption: EChartsOption;
} = reactive({
  chartOption: {} as EChartsOption
});

const { srccx, dstcx } = toRefs({
  srccx: 400,
  dstcx: 1200
});

const option: EChartsOption = reactive({});
const updateLineColors = (children: TreeNode[]) => {
  for (let child of children) {
    let { reseverity, event_agent, count, event_subtype_name } = child;
    child.lineStyle = {
      color: getRiskLevelColor(reseverity)
    };
    if (child.children) {
      updateLineColors(child.children);
    }
  }
};

const buildGraphData = (
  sourceIndex,
  fromFlag,
  childNodes: TreeNode[],
  graphData
) => {
  if (childNodes) {
    for (let child of childNodes) {
      let len = graphData.nodes.length;
      let { children, name, ...props } = child;
      // 存在name重复的使用序号确保name唯一，使用label存储之前的name
      let childNode = { name: name + "-" + len, label: name, data: child };
      graphData.nodes.push(childNode);
      let childIndex = len;
      let link = {
        source: fromFlag ? childIndex : sourceIndex,
        target: fromFlag ? sourceIndex : childIndex,
        value: child.event_subtype_name + "" + child.count + "次",
        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 2,
            curveness: 0.15,
            color: getRiskLevelColor(props["reseverity"])
          }
        },
        data: { ...props }
      };
      graphData.links.push(link);
      if (children) {
        buildGraphData(childIndex, fromFlag, children, graphData);
      }
    }
  }
};

const handleChartClick = params => {
  // 点击连线触发
  if (params.dataType == "edge") {
    let data = params.data?.data;
    if (data) {
      emits("on-click-attackline", data);
    }
  }
};

const buildOption = graphData => {
  return {
    tooltip: {
      trigger: "item",
      borderWidth: 1,
      padding: 5,
      textStyle: {
        color: "#4d555e"
      },
      formatter: params => {
        let { data } = params || {};
        data = data.data || {};
        if (!data || data.tag == "src" || data.tag == "dst") return null;
        let {
          reseverity,
          reseverityName,
          event_agent,
          count,
          event_subtype_name
        } = data || {};
        return `
          <div style='padding-left: 10px;display: flex;'>
            <div style='width: 6em; text-align: left;'>攻击方式</div>
            <span>${event_subtype_name}</span>
          </div>
          <div style='padding-left: 10px;display: flex;'>
            <div style='width: 6em; text-align: left;'>攻击次数</div>
            <span>${count}</span>
          </div>
          <div style='padding-left: 10px;display: flex;'>
            <div style='width: 6em; text-align: left;'>风险级别</div>
            <span style="color: ${getRiskLevelColor(reseverity)}">${reseverityName}</span>
          </div>
          <div style='padding-left: 10px;display: flex;'>
            <div style='width: 6em; text-align: left;'>数据来源</div>
            <span>${event_agent}</span>
          </div>`;
      }
    },
    series: [
      {
        type: "graph",
        layout: "force",
        force: {
          repulsion: 4200,
          edgeLength: 150,
          layoutAnimation: true
        },
        symbol: "circle",
        symbolSize: [90, 60],
        // nodeScaleRatio: 1, //图标大小是否随鼠标滚动而变
        roam: true, //缩放
        draggable: true, //节点是否可以拖拽
        edgeSymbol: ["circle", "arrow"], //线2头标记
        edgeSymbolSize: [0, 15],
        label: {
          normal: {
            show: true,
            // position: "inside",
            position: "inside",
            // color: "#FFF",
            formatter: params => {
              return params.data.label;
            }
          }
        },
        edgeLabel: {
          normal: {
            show: true,
            textStyle: {
              fontSize: 12
            },
            formatter: "{c}"
          }
        },
        symbolKeepAspect: false,
        focusNodeAdjacency: false, // 指定的节点以及其所有邻接节点高亮
        itemStyle: {
          normal: {
            opacity: 0.75,
            borderColor: "#FCBB5B",
            borderWidth: 2,
            shadowColor: "#FCBB5B",
            color: "#FCBB5B",
            curveness: 0.08
          }
        },
        lineStyle: {
          normal: {
            opacity: 0.9,
            width: 2,
            curveness: 0.15
          }
        },
        nodes: graphData.nodes,
        links: graphData.links
      }
    ]
  };
};

const updateOption = () => {
  let { src, dst } = props.attackData;
  if (!src || !dst) return;

  const fixedSrcAndDst = src.children?.length > 0 || dst.children?.length > 0;

  // 标记
  src.tag = "src";
  dst.tag = "dst";
  // 构建nodes
  const graphData = {
    nodes: [],
    links: []
  };
  // 源
  let srcNode = {
    name: src.name + "-0-src",
    label: {
      normal: {
        color: "#fff",
        formatter: "[" + src.name + "]"
      }
    },
    fixed: fixedSrcAndDst,
    x: srccx.value,
    y: 300,
    itemStyle: {
      normal: {
        color: "rgba(203,51,25)",
        borderColor: "#FCBB5B",
        shadowColor: "#FCBB5B"
      }
    },
    data: { tag: "src" }
  };
  let dstNode = {
    name: dst.name + "-1-dst",
    label: {
      normal: {
        color: "#fff",
        formatter: "[" + dst.name + "]"
      }
    },
    fixed: fixedSrcAndDst,
    x: dstcx.value,
    y: 300,
    // symbolOffset: [20, 0],
    lineStyle: {
      transform: "scale(0.1)"
    },
    itemStyle: {
      normal: {
        color: "rgba(203,51,25)",
        borderColor: "#FCBB5B",
        shadowColor: "#FCBB5B"
      }
    },
    data: { tag: "dst" }
  };
  graphData.nodes.push(srcNode);
  graphData.nodes.push(dstNode);
  graphData.links.push({
    source: 0,
    target: 1,
    value: "攻击 ->",
    lineStyle: {
      normal: {
        opacity: 0.9,
        width: 2,
        curveness: 0,
        color: "red"
      }
    },
    data: {
      tag: "dst",
      global: true
    }
  });
  buildGraphData(0, true, src.children, graphData);
  buildGraphData(1, false, dst.children, graphData);
  state.chartOption = buildOption(graphData) as EChartsOption;
};

// 重新计算中心位置
const handleResize = () => {
  const { width } = chartWrapEl.value.getBoundingClientRect();
  srccx.value = width / 4 + width / 10;
  dstcx.value = (width / 4) * 3 - width / 10;
  updateOption();
};

watch(
  () => props.attackData,
  val => {
    if (val.src && val.dst) {
      updateOption();
    }
  }
);
</script>
<style lang="scss" scoped>
//.line {
//  height: 8px;
//
//  border-top: 10px solid transparent;
//  border-bottom: 10px solid transparent;
//  border-left: 10px solid red;
//
//  transform: translateY(-50%);
//  background: linear-gradient(
//    to right,
//    red,
//    orange,
//    yellow,
//    #803a00,
//    #ffae00,
//    #943806,
//    violet
//  );
//  background-size: 200% 100%; /* 使背景图宽度为容器的两倍 */
//  animation: line-animate 2s linear infinite; /* 动画持续时间、速度和循环方式 */
//}
//@keyframes line-animate {
//  0% {
//    background-position: 100% 50%; /* 动画开始时背景图的位置 */
//  }
//  10% {
//    background-position: 90% 50%; /* 动画开始时背景图的位置 */
//  }
//  20% {
//    background-position: 80% 50%; /* 动画开始时背景图的位置 */
//  }
//  50% {
//    background-position: 50% 50%; /* 动画开始时背景图的位置 */
//  }
//  to {
//    background-position: 0% 50%; /* 动画结束时背景图的位置 */
//  }
//}
</style>
