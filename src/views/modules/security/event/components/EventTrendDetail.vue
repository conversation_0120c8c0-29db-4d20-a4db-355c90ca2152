<template>
  <div class="pb-10">
    <div class="flex justify-end pr-5">
      <el-segmented
        v-model="dateRangeSign"
        :options="timeSegmentOptions"
        @change="segmentChangeHandler"
      >
      </el-segmented>
    </div>
    <el-tabs
      v-model="activeTabName"
      tab-position="left"
      class="h-52 mt-2 mb-2"
      @tab-change="tabChangeHandler"
    >
      <el-tab-pane label="数量趋势" name="numTrend">
        <event-num-trend-chart
          ref="numTrendRef"
          :event-info="eventInfo"
          :date-range-sign="dateRangeSign"
        />
      </el-tab-pane>
      <el-tab-pane label="时分布" name="hourTrend">
        <event-hour-trend-chart
          ref="hourTrendRef"
          :event-info="eventInfo"
          :date-range-sign="dateRangeSign"
        />
      </el-tab-pane>
      <el-tab-pane label="周分布" name="weekTrend">
        <event-week-trend-chart
          ref="weekTrendRef"
          :event-info="eventInfo"
          :date-range-sign="dateRangeSign"
        />
      </el-tab-pane>
    </el-tabs>
    <avue-crud
      :data="tableData"
      :option="tableOption"
      v-model:page="tablePage"
      :table-loading="tableLoading"
      @refresh-change="resetTablePageAndQuery"
      @size-change="queryEventHistoryData"
      @current-change="queryEventHistoryData"
    >
    </avue-crud>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, toRefs } from "vue";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import { getEventHistoryDetail } from "@/views/modules/security/event/api/SecurityEventApi";
import EventNumTrendChart from "@/views/modules/security/event/components/EventNumTrendChart.vue";
import EventHourTrendChart from "@/views/modules/security/event/components/EventHourTrendChart.vue";
import EventWeekTrendChart from "@/views/modules/security/event/components/EventWeekTrendChart.vue";

const numTrendRef = ref(null);
const hourTrendRef = ref(null);
const weekTrendRef = ref(null);

// 组件属性
const props = defineProps({
  eventInfo: {
    type: Object,
    default: () => {}
  }
});

//数据对象
const state = reactive({
  dateRangeSign: null,
  activeTabName: "numTrend",
  tableLoading: false,
  tableData: [],
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  timeSegmentOptions: [
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ]
});
const {
  dateRangeSign,
  activeTabName,
  tableLoading,
  tableData,
  tablePage,
  timeSegmentOptions
} = toRefs(state);

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  rowKey: "event_uid",
  column: [
    {
      label: "事件发生时间",
      prop: "last_time"
    },
    {
      label: "源资产",
      prop: "src_ip"
    },
    {
      label: "目的资产",
      prop: "dst_ip"
    },
    {
      label: "事件名称",
      prop: "event_name"
    },
    {
      label: "事件类型",
      prop: "event_subtype_name"
    },
    {
      label: "源资产组织",
      prop: "src_org_name"
    },
    {
      label: "目的资产组织",
      prop: "dst_org_name"
    }
  ]
});

//时间范围改变
const segmentChangeHandler = (value: string) => {
  resetTablePageAndQuery();
};

//重置分页后查询事件数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  queryEventHistoryData();
};

//查询事件历史数据
const queryEventHistoryData = async () => {
  state.tableLoading = true;
  const hisRes = await getEventHistoryDetail({
    dateRange: state.dateRangeSign,
    event_id: props.eventInfo.event_id,
    pageNum: state.tablePage.currentPage,
    pageSize: state.tablePage.pageSize
  });
  state.tableData = hisRes.data.rows;
  state.tableLoading = false;
};

//tab切换
const tabChangeHandler = (name: string) => {
  if (name === "numTrend") {
    numTrendRef.value.resizeChart();
  } else if (name === "hourTrend") {
    hourTrendRef.value.resizeChart();
  } else if (name === "weekTrend") {
    weekTrendRef.value.resizeChart();
  }
};

//初始化数据
const initData = () => {
  state.dateRangeSign = "1d";
  resetTablePageAndQuery();
};

defineExpose({ initData });
</script>
