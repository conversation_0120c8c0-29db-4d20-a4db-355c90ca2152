<template>
  <div>
    <el-drawer
      v-model="drawerVisible"
      :before-close="cancel"
      :show-close="false"
      :destroy-on-close="true"
      size="60%"
    >
      <template #header>
        <div class="flex-bc">
          <el-page-header @back="cancel">
            <template #content>
              <span class="mr-3 font-bold"> 风险告警详情 </span>
            </template>
          </el-page-header>
        </div>
      </template>
      <alarm-basic-detail :alarm-info="alarmInfo" />
      <el-tabs
        v-model="activeTabName"
        class="mt-2"
        @tab-change="tabChangeHandler"
      >
        <el-tab-pane label="告警佐证信息" name="corroboration">
          <monaco-editor
            ref="corroborationRef"
            v-model="corroborationContent"
            language="text"
            :height="contentHeight + 'px'"
            :options="monacoOptions"
          />
        </el-tab-pane>
        <el-tab-pane label="原始日志" name="rawLogs">
          <monaco-editor
            ref="rawLogsRef"
            v-model="rawLogContent"
            language="text"
            :height="contentHeight + 'px'"
            :options="monacoOptions"
          />
        </el-tab-pane>

        <el-tab-pane label="历史告警信息" name="historyInfo">
          <avue-crud :data="filterTableData" :option="tableOption">
            <template #reseverity="{ row }">
              <el-tag
                :color="getRiskLevelColor(row.reseverity)"
                class="text-white border-none"
              >
                {{ getRiskLevelLabel(row.reseverity) }}
              </el-tag>
            </template>

            <template #menu="{ row }">
              <el-button type="primary" link @click="showAlarmDetail(row)"
                >查看详情</el-button
              >
            </template>

            <template #menu-right>
              <div class="float-left flex-sc pr-6 gap-2">
                <el-select
                  v-model="state.filterReseverity"
                  placeholder="风险级别"
                  multiple
                  collapse-tags
                  clearable
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in riskLevelData"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
                <el-input
                  v-model="state.filterAttachResult"
                  placeholder="输入攻击结果关键字过滤"
                  style="width: 150px"
                ></el-input>
              </div>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <alarm-detail-modal
      v-model:visible="detail.visible"
      :alarm-info="detail.selAlarmInfo"
    ></alarm-detail-modal>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, Ref, ref, toRefs, watch } from "vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import AlarmBasicDetail from "@/views/modules/security/event/components/AlarmBasicDetail.vue";
import {
  getRiskLevelColor,
  getRiskLevelLabel,
  riskLevelData
} from "@/views/modules/security/event/util/event_data";
import {
  getAlarmHistoryDetail,
  toEventAlarmRows
} from "@/views/modules/security/event/api/SecurityEventApi";
import AlarmDetailModal from "@/views/modules/security/event/components/AlarmDetailModal.vue";

const corroborationRef = ref();
const rawLogsRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  alarmInfo: {
    type: Object,
    default: () => {}
  },
  size: {
    type: String,
    default: "60%"
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  activeTabName: "corroboration",
  monacoOptions: {
    readOnly: true,
    wordWrap: "on"
  },
  corroborationContent: "",
  rawLogContent: "",

  // 历史告警列表信息
  filterReseverity: [],
  filterAttachResult: null,
  historyAlarmTableData: []
});
const {
  drawerVisible,
  activeTabName,
  monacoOptions,
  corroborationContent,
  rawLogContent
} = toRefs(state);

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // viewBtn: true,
  menuWidth: 130,
  // header: false,
  column: [
    {
      label: "告警发生时间",
      prop: "first_time"
    },
    {
      label: "告警名称",
      prop: "event_name"
    },
    {
      label: "源IP",
      prop: "src_ip"
    },
    {
      label: "源端口",
      prop: "src_port"
    },
    {
      label: "目的IP",
      prop: "dst_ip"
    },
    {
      label: "目的端口",
      prop: "dst_port"
    },
    {
      label: "风险级别",
      prop: "reseverity"
    },
    {
      label: "攻击结果",
      prop: "attack_result_text"
    }
  ]
});

const queryHistoryDetailData = async () => {
  const hisRes = await getAlarmHistoryDetail({
    ...props.alarmInfo,
    pageSize: 1000,
    pageNum: 1
  });
  state.historyAlarmTableData = toEventAlarmRows(hisRes.data);

  // if (state.tableData && state.tableData.length > 0) {
  //   state.lastData = state.tableData[0];
  //   if (state.lastData.raw_log && state.lastData.raw_log.length > 0) {
  //     state.alarmContent = state.lastData.raw_log;
  //   } else {
  //     state.alarmContent = "暂无数据";
  //   }
  // }
};

// 历史告警信息
const filterTableData = computed(() => {
  let tableData = state.historyAlarmTableData;
  return tableData.filter(row => {
    let { reseverity, attack_result_text } = row;
    if (
      state.filterReseverity.length > 0 &&
      !state.filterReseverity.includes(reseverity)
    ) {
      return false;
    }
    if (
      state.filterAttachResult &&
      (attack_result_text || "").indexOf(state.filterAttachResult) == -1
    ) {
      return false;
    }
    return true;
  });
});

//监听组件属性
watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  //处理佐证
  if (newValue.alarmInfo.event_evidence) {
    state.corroborationContent = newValue.alarmInfo.event_evidence;
  } else {
    state.corroborationContent = "暂无数据";
  }

  //处理原始日志
  if (newValue.alarmInfo.raw_log) {
    state.rawLogContent = newValue.alarmInfo.raw_log;
  } else {
    state.rawLogContent = "暂无数据";
  }

  state.activeTabName = "corroboration";

  // 查看历史告警列表
  queryHistoryDetailData();
});

//关闭并返回父界面
const cancel = () => {
  emit("update:visible", false);
};

//根据页面高度设置表格高度
const contentHeight = computed(() => {
  return document.documentElement.offsetHeight - 560;
});

//格式化Monaco的内容ßßß
const formatMonacoContent = (monacoRef: Ref) => {
  monacoRef.value.format();
  setTimeout(() => {
    monacoRef.value.getEditor().updateOptions({ readOnly: true });
  }, 300);
};

// tab切换事件
const tabChangeHandler = (tabName: string) => {
  if (tabName === "rawLogs") {
    formatMonacoContent(rawLogsRef);
  }
  if (tabName === "corroboration") {
    formatMonacoContent(corroborationRef);
  }
};

// 单个告警详情
const detail = reactive({
  visible: false,
  selAlarmInfo: null,
  size: "100%"
});

const showAlarmDetail = row => {
  detail.visible = true;
  detail.selAlarmInfo = row;
};

onMounted(() => {
  //todo:初始化
});
</script>
