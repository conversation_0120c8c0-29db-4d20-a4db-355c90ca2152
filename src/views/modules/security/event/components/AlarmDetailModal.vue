<template>
  <div>
    <el-dialog
      v-model="drawerVisible"
      :before-close="cancel"
      :destroy-on-close="true"
      top="10vh"
      width="60%"
      title="风险告警详情"
    >
      <!--      <template #header>-->
      <!--        <div class="flex-bc">-->
      <!--          <el-page-header @back="cancel">-->
      <!--            <template #content>-->
      <!--              <span class="mr-3 font-bold"> 风险告警详情 </span>-->
      <!--            </template>-->
      <!--          </el-page-header>-->
      <!--        </div>-->
      <!--      </template>-->

      <div style="height: 75vh; overflow: auto">
        <alarm-basic-detail :alarm-info="alarmInfo" />
        <el-tabs
          v-model="activeTabName"
          class="mt-2"
          @tab-change="tabChangeHandler"
        >
          <el-tab-pane label="告警佐证信息" name="corroboration">
            <monaco-editor
              ref="corroborationRef"
              v-model="corroborationContent"
              language="text"
              :height="contentHeight"
              :options="monacoOptions"
            />
          </el-tab-pane>
          <el-tab-pane label="原始日志" name="rawLogs">
            <monaco-editor
              ref="rawLogsRef"
              v-model="rawLogContent"
              language="text"
              :height="contentHeight"
              :options="monacoOptions"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, Ref, ref, toRefs, watch } from "vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import AlarmBasicDetail from "@/views/modules/security/event/components/AlarmBasicDetail.vue";

const corroborationRef = ref();
const rawLogsRef = ref();

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  alarmInfo: {
    type: Object,
    default: () => {}
  },
  size: {
    type: String,
    default: "60%"
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  activeTabName: "corroboration",
  monacoOptions: {
    readOnly: true,
    wordWrap: "on"
  },
  corroborationContent: "",
  rawLogContent: ""
});
const {
  drawerVisible,
  activeTabName,
  monacoOptions,
  corroborationContent,
  rawLogContent
} = toRefs(state);

//监听组件属性
watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  if (!state.drawerVisible) {
    return;
  }
  //处理佐证
  if (newValue.alarmInfo.event_evidence) {
    state.corroborationContent = newValue.alarmInfo.event_evidence;
  } else {
    state.corroborationContent = "暂无数据";
  }

  //处理原始日志
  if (newValue.alarmInfo.raw_log) {
    state.rawLogContent = newValue.alarmInfo.raw_log;
  } else {
    state.rawLogContent = "暂无数据";
  }

  state.activeTabName = "corroboration";
});

//关闭并返回父界面
const cancel = () => {
  emit("update:visible", false);
};

//根据页面高度设置表格高度
const contentHeight = computed(() => {
  return `calc(75vh - 490px)`;
});

//格式化Monaco的内容ßßß
const formatMonacoContent = (monacoRef: Ref) => {
  monacoRef.value.format();
  setTimeout(() => {
    monacoRef.value.getEditor().updateOptions({ readOnly: true });
  }, 300);
};

// tab切换事件
const tabChangeHandler = (tabName: string) => {
  if (tabName === "rawLogs") {
    formatMonacoContent(rawLogsRef);
  }
  if (tabName === "corroboration") {
    formatMonacoContent(corroborationRef);
  }
};

onMounted(() => {
  //todo:初始化
});
</script>
