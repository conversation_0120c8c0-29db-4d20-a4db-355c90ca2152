<template>
  <div>
    <el-drawer
      v-model="drawerVisible"
      :before-close="cancel"
      :show-close="false"
      :destroy-on-close="true"
      size="60%"
    >
      <template #header>
        <div class="flex-bc">
          <el-page-header @back="cancel">
            <template #content>
              <span class="mr-3 font-bold"> 风险告警详情 </span>
            </template>
          </el-page-header>
        </div>
      </template>
      <alarm-basic-detail :alarm-info="lastData" />
      <el-tabs v-model="activeTabName" class="mt-2">
        <el-tab-pane label="最近告警信息" name="alarmInfo">
          <monaco-editor
            ref="corroborationRef"
            v-model="alarmContent"
            language="text"
            :height="contentHeight + 'px'"
            :options="monacoOptions"
          />
        </el-tab-pane>
        <el-tab-pane label="历史告警信息" name="historyInfo">
          <avue-crud :data="filterTableData" :option="tableOption">
            <template #reseverity="{ row }">
              <el-tag
                :color="getRiskLevelColor(row.reseverity)"
                class="text-white border-none"
              >
                {{ getRiskLevelLabel(row.reseverity) }}
              </el-tag>
            </template>

            <template #menu="{ row }">
              <el-button type="primary" link @click="showAlarmDetail(row)"
                >查看详情</el-button
              >
            </template>

            <template #menu-right>
              <div class="float-left flex-sc pr-6 gap-2">
                <el-select
                  v-model="state.filterReseverity"
                  placeholder="风险级别"
                  multiple
                  collapse-tags
                  clearable
                  style="width: 120px"
                >
                  <el-option
                    v-for="item in riskLevelData"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  />
                </el-select>
                <el-input
                  v-model="state.filterAttachResult"
                  placeholder="输入攻击结果关键字过滤"
                  style="width: 150px"
                ></el-input>
              </div>
            </template>
          </avue-crud>
        </el-tab-pane>
      </el-tabs>

      <!-- 告警详情 -->
      <!--      <alarm-detail-info-->
      <!--        v-model:visible="detail.visible"-->
      <!--        :alarm-info="detail.selAlarmInfo"-->
      <!--        :size="detail.size"-->
      <!--      />-->

      <alarm-detail-modal
        v-model:visible="detail.visible"
        :alarm-info="detail.selAlarmInfo"
      ></alarm-detail-modal>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, toRefs, watch } from "vue";
import AlarmBasicDetail from "@/views/modules/security/event/components/AlarmBasicDetail.vue";
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import {
  getAlarmHistoryDetail,
  toEventAlarmRows
} from "@/views/modules/security/event/api/SecurityEventApi";
import {
  getRiskLevelColor,
  getRiskLevelLabel,
  riskLevelData
} from "@/views/modules/security/event/util/event_data";
import AlarmDetailInfo from "@/views/modules/security/event/components/AlarmDetailInfo.vue";
import AlarmDetailModal from "@/views/modules/security/event/components/AlarmDetailModal.vue";

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  timeLineInfo: {
    type: Object,
    default: () => {}
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  drawerVisible: false,
  activeTabName: "alarmInfo",
  monacoOptions: {
    readOnly: true,
    wordWrap: "on"
  },
  alarmContent: "",
  tableData: [],
  lastData: {
    raw_log: ""
  },
  filterReseverity: [],
  filterAttachResult: null
});
const {
  drawerVisible,
  activeTabName,
  monacoOptions,
  alarmContent,
  tableData,
  lastData
} = toRefs(state);

//监听组件属性
watch(props, async (newValue: any) => {
  state.drawerVisible = newValue.visible;
  state.filterReseverity = [];
  state.filterAttachResult = null;
  if (!state.drawerVisible) {
    return;
  }
  state.activeTabName = "alarmInfo";
  await queryDetailData(newValue.timeLineInfo);
});

//根据页面高度设置表格高度
const contentHeight = computed(() => {
  return document.documentElement.offsetHeight - 560;
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  // viewBtn: true,
  menuWidth: 130,
  // header: false,
  column: [
    {
      label: "告警发生时间",
      prop: "first_time"
    },
    {
      label: "告警名称",
      prop: "event_name"
    },
    {
      label: "源IP",
      prop: "src_ip"
    },
    {
      label: "源端口",
      prop: "src_port"
    },
    {
      label: "目的IP",
      prop: "dst_ip"
    },
    {
      label: "目的端口",
      prop: "dst_port"
    },
    {
      label: "风险级别",
      prop: "reseverity"
    },
    {
      label: "攻击结果",
      prop: "attack_result_text"
    }
  ]
});

const filterTableData = computed(() => {
  let tableData = state.tableData;
  return tableData.filter(row => {
    let { reseverity, attack_result_text } = row;
    if (
      state.filterReseverity.length > 0 &&
      !state.filterReseverity.includes(reseverity)
    ) {
      return false;
    }
    if (
      state.filterAttachResult &&
      (attack_result_text || "").indexOf(state.filterAttachResult) == -1
    ) {
      return false;
    }
    return true;
  });
});

//查询详细数据
const queryDetailData = async (row: any) => {
  const hisRes = await getAlarmHistoryDetail({
    ...props.timeLineInfo,
    pageSize: 1000,
    pageNum: 1
  });
  state.tableData = toEventAlarmRows(hisRes.data);

  if (state.tableData && state.tableData.length > 0) {
    state.lastData = state.tableData[0];
    if (state.lastData.raw_log && state.lastData.raw_log.length > 0) {
      state.alarmContent = state.lastData.raw_log;
    } else {
      state.alarmContent = "暂无数据";
    }
  }
};

// 单个告警详情
const detail = reactive({
  visible: false,
  selAlarmInfo: null,
  size: "100%"
});

const showAlarmDetail = row => {
  detail.visible = true;
  detail.selAlarmInfo = row;
};

//关闭并返回父界面
const cancel = () => {
  emit("update:visible", false);
};
</script>
