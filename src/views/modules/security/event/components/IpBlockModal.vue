<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :before-close="cancel"
    :destroy-on-close="true"
    width="45%"
  >
    <avue-form
      ref="blockFormRef"
      :option="option"
      v-model="form"
      class="mt-2"
    />
    <template #footer>
      <el-button
        type="primary"
        :icon="useRenderIcon('EP-CircleCheck')"
        @click="submitHandler"
      >
        确定
      </el-button>
      <el-button :icon="useRenderIcon('EP-CircleClose')" @click="cancel()">
        取 消
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getCurrentInstance, reactive, ref, toRefs, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getBlockSource,
  getFireWallTree,
  submitBlockRequest
} from "@/views/modules/security/event/api/IpBlockApi";
import { validIPAddress } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";

const { $message } = getCurrentInstance().appContext.config.globalProperties;
const blockFormRef = ref(null);

//组件属性
const props = defineProps({
  visible: {
    type: Boolean
  },
  title: {
    type: String,
    default: ""
  },
  ipAddress: {
    type: String,
    default: ""
  },
  defaultPlugLabel: String,
  extParams: {
    type: Object,
    default: () => {
      return {};
    }
  }
});

//定义事件
const emit = defineEmits(["update:visible"]);

//数据对象
const state = reactive({
  dialogVisible: false,
  fireWallTreeData: [],
  blockSourceData: [],
  form: {
    ipAddress: "",
    plugLabel: "",
    plugDescribe: "",
    firewall: "",
    expireTime: "",
    firewallIds: []
  }
});
const { form, dialogVisible, fireWallTreeData, blockSourceData } =
  toRefs(state);

//加载防火墙数据
const loadFireWallData = async () => {
  const treeRes = await getFireWallTree();
  state.fireWallTreeData = treeRes.data;
};
loadFireWallData();

//加载封堵来源数据
const loadBlockSourceData = async () => {
  const res = await getBlockSource({ queryDate: "all" });
  state.blockSourceData = res.data;
};
loadBlockSourceData();

const blockTimeData = [
  {
    label: " 1天",
    value: "1"
  },
  {
    label: " 2天",
    value: "2"
  },
  {
    label: " 3天",
    value: "3"
  },
  {
    label: " 4天",
    value: "4"
  },
  {
    label: " 5天",
    value: "5"
  },
  {
    label: " 6天",
    value: "6"
  },
  {
    label: " 7天",
    value: "7"
  },
  {
    label: " 15天",
    value: "15"
  },
  {
    label: " 30天",
    value: "30"
  },
  {
    label: "永久",
    value: "0"
  }
];

//IP地址格式校验
const validateIpFormat = (rule, value, callback) => {
  if (validIPAddress(value)) {
    callback();
  } else {
    callback(new Error("IP地址格式错误！"));
  }
};

const option = reactive({
  menuPosition: "right",
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: "IP地址：",
      prop: "ipAddress",
      span: 12,
      maxlength: 64,
      showWordLimit: true,
      type: "input",
      rules: [
        {
          required: true,
          message: "请输入IP地址",
          trigger: "blur"
        },
        { validator: validateIpFormat, trigger: "blur" }
      ]
    },
    {
      label: "封堵来源",
      prop: "plugLabel",
      span: 12,
      type: "select",
      dicData: blockSourceData,
      props: {
        label: "plugLabel",
        value: "plugLabel"
      },
      rules: [
        {
          required: true,
          message: "请选择封堵来源",
          trigger: "blur"
        }
      ]
    },
    {
      label: "防火墙",
      prop: "firewallIds",
      span: 12,
      type: "tree",
      multiple: true,
      dicData: fireWallTreeData,
      props: {
        label: "name",
        value: "id"
      },
      rules: []
    },
    {
      label: "封堵时间",
      prop: "expireTime",
      span: 12,
      type: "select",
      dicData: blockTimeData
    },
    {
      label: "封堵原因",
      prop: "plugDescribe",
      span: 24,
      type: "textarea",
      rules: [
        {
          required: true,
          message: "请填写封堵原因",
          trigger: "blur"
        }
      ]
    }
  ]
});

watch(
  () => props.visible,
  async newValue => {
    state.dialogVisible = newValue;
    state.form.plugLabel = props.defaultPlugLabel || "";
  }
);

watch(
  () => props.ipAddress,
  async newValue => {
    state.form.ipAddress = newValue;
    state.form.plugLabel = props.defaultPlugLabel || "";
  }
);

//提交封堵请求触发
const submitHandler = () => {
  blockFormRef.value.validate(async (valid, done) => {
    if (valid) {
      let postData = {
        ...props.extParams,
        ...state.form
      };
      const res = await submitBlockRequest(postData);
      if (res.status === ResultStatus.Success) {
        $message({
          message: `已成功封堵 IP 地址：${props.ipAddress}!`,
          type: "success"
        });
        cancel();
      }
      done();
    }
  });
};

const cancel = () => {
  state.form = {
    ipAddress: props.ipAddress,
    plugLabel: "",
    plugDescribe: "",
    firewall: "",
    expireTime: "",
    firewallIds: []
  };
  emit("update:visible", false);
};
</script>
