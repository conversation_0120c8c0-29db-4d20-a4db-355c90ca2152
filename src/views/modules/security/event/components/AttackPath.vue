<template>
  <div>
    <div class="flex justify-end pr-5">
      <el-segmented
        v-model="searchCondition.dateRangeSign"
        :options="timeSegmentOptions"
        @change="
          () => {
            searchCondition.dateRange = null;
            queryEventAttack();
          }
        "
      >
      </el-segmented>
      <div class="ml-3">
        <el-date-picker
          v-model="searchCondition.dateRange"
          type="daterange"
          range-separator="到"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="
            () => {
              searchCondition.dateRangeSign = null;
              queryEventAttack();
            }
          "
        />
      </div>
    </div>
    <div class="flex-c w-full mt-5 mb-3 h-[600px]">
      <!--      <img :src="attackPathImg" alt="attack-path" style="width: 900px;"/>-->
      <attack-path-chart
        :attack-data="state.attackData"
        @on-click-attackline="handleClickAttackLine"
      ></attack-path-chart>
    </div>
    <avue-crud
      v-loading="state.tableLoading"
      :data="tableData"
      :option="tableOption"
      class="mb-[25px]"
    >
      <template #menu-left>
        <el-segmented
          v-model="activeSegment"
          :options="segmentData"
          @change="queryAssets"
        ></el-segmented>
      </template>
      <template #menu="{ row, size, type }">
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('RI-CloseCircleFill')"
          @click="handleIpBlock(row)"
        >
          封堵
        </el-button>
        <!--        <el-button-->
        <!--          :size="size"-->
        <!--          :type="type"-->
        <!--          text-->
        <!--          :icon="useRenderIcon('EP-View')"-->
        <!--        >-->
        <!--          详情-->
        <!--        </el-button>-->
      </template>
    </avue-crud>

    <!-- ip封堵 -->
    <ip-block-modal
      v-model:visible="ipBlock.visible"
      title="IP封堵"
      :ip-address="ipBlock.ipAddress"
    />

    <!-- 事件告警详情 -->
    <alarm-detail-info-with-event
      v-model:visible="alarmDetail.visible"
      :time-line-info="alarmDetail.eventData"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, toRefs } from "vue"; //数据对象
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import AttackPathChart from "@/views/modules/security/event/components/AttackPathChart.vue";
import {
  eventAttack,
  queryAssetsByIpsByEvent,
  queryAssetsByIps
} from "@/views/modules/security/event/api/SecurityEventApi";
import IpBlockModal from "@/views/modules/security/event/components/IpBlockModal.vue";
import AlarmDetailInfo from "@/views/modules/security/event/components/AlarmDetailInfo.vue";
import AlarmDetailInfoWithEvent from "@/views/modules/security/event/components/AlarmDetailInfoWithEvent.vue";

const props = defineProps({
  // 事件对象
  eventInfo: Object,
  srcIp: String,
  dstIp: String
});

//数据对象
const state = reactive({
  searchCondition: {
    dateRangeSign: "1d",
    dateRange: []
  },
  timeSegmentOptions: [
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ],
  tableLoading: false,
  tableData: [],
  activeSegment: "1",
  segmentData: [
    {
      label: "攻击者",
      value: "1"
    },
    {
      label: "关联攻击者",
      value: "2"
    },
    {
      label: "受害者",
      value: "3"
    },
    {
      label: "关联受害者",
      value: "4"
    }
  ],

  attackData: {}
});
const {
  searchCondition,
  timeSegmentOptions,
  tableData,
  activeSegment,
  segmentData
} = toRefs(state);

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  rowKey: "id",
  column: [
    // {
    //   label: "最近发生时间",
    //   prop: "updated_time"
    // },
    {
      label: "所属组织",
      prop: "zoneName__label"
    },
    {
      label: "资产名称",
      prop: "name"
    },
    {
      label: "主机地址",
      prop: "ipAddress"
    },
    {
      label: "IP归属",
      prop: "ip_country"
    },
    {
      label: "MAC地址",
      prop: "macAddress"
    },
    {
      label: "所在位置",
      prop: "phyPosition__label"
    },
    {
      label: "资产责任人",
      prop: "assetDuty"
    }
    // {
    //   label: "资产标签",
    //   prop: "assetsTag"
    // }
  ]
});

const ipBlock = reactive({
  visible: false,
  ipAddress: null
});

const alarmDetail = reactive({
  visible: false,
  eventData: {}
});

const handleIpBlock = row => {
  ipBlock.visible = true;
  ipBlock.ipAddress = row.ipAddress;
};

const handleClickAttackLine = eventData => {
  alarmDetail.visible = true;
  alarmDetail.eventData = eventData?.global ? props.eventInfo : eventData;
};

const queryEventAttack = () => {
  let dateRange;
  if (state.searchCondition.dateRangeSign) {
    dateRange = state.searchCondition.dateRangeSign;
  } else {
    dateRange = state.searchCondition.dateRange;
  }
  let queryParams = {
    conditions: [],
    // dateRange: ["2022-12-01 00:00:00", "2026-01-29 00:00:00"],
    dateRange,
    orgId: "",
    src_ip: props.srcIp,
    dst_ip: props.dstIp
  };
  eventAttack(queryParams).then(res => {
    state.attackData = res.data;
    queryAssets();
  });
};

const refSrcIps = computed(() => {
  let { src } = state.attackData || {};
  if (!src) {
    return [];
  }
  let children = src.children || [];
  return children.map(child => child.name);
});

const refDstIps = computed(() => {
  let { dst } = state.attackData || {};
  if (!dst) {
    return [];
  }
  let children = dst.children || [];
  return children.map(child => child.name);
});

// 查询资产
const queryAssets = () => {
  let ipAddress = [];
  switch (activeSegment.value) {
    case "1": {
      ipAddress = [props.srcIp];
      break;
    }
    case "2": {
      ipAddress = refSrcIps.value;
      break;
    }
    case "3": {
      ipAddress = [props.dstIp];
      break;
    }
    case "4": {
      ipAddress = refDstIps.value;
      break;
    }
  }
  if (ipAddress.length == 0) return;
  state.tableLoading = true;
  queryAssetsByIpsByEvent(ipAddress)
    .then(res => {
      state.tableData = res.data;
    })
    .finally(() => {
      state.tableLoading = false;
    });
};

onMounted(() => {
  queryEventAttack();
});
</script>
