<template>
  <event-detail-info
    v-if="state.eventInfo"
    :key="state.key"
    hidden-jump
    readonly
    :event-info="state.eventInfo"
  ></event-detail-info>
<!--  <el-empty v-else description="事件对象为空或未关联到事件"></el-empty>-->
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import { useRoute } from "vue-router";
import EventDetailInfo from "../components/EventDetailInfo.vue";
import { getEventList } from "@/views/modules/security/event/api/SecurityEventApi";
const route = useRoute();

const state = reactive({
  key: 1,
  eventInfo: null
});

const queryEvent = business_id => {
  getEventList({
    pageNum: 1,
    pageSize: 10,
    conditions: [
      {
        field: "event_uid",
        value: business_id,
        fuzzy: false
      }
    ]
  }).then(res => {
    console.log("res", res);
    let { rows } = res.data || {};
    if (Array.isArray(rows)) {
      state.eventInfo = rows[0];
    }
  }).finally(() => {
    ++state.key;
  });
};

watch(
  () => route?.query.id,
  val => {
    queryEvent(val);
  },
  {
    immediate: true
  }
);
</script>
<style scoped lang="scss"></style>
<style lang="scss">
// 强制隐藏菜单
.sidebar-container {
  display: none !important;
}
.fixed-header {
  display: none !important;
}
.app-main {
  padding-top: 0 !important;
}
</style>
