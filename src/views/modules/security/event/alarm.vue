<template>
  <div>
    <!-- 搜索条件 -->
    <search-with-column
      v-model="columnCondition.value"
      v-model:fuzzy-enable="columnCondition.fuzzy"
      v-model:column-val="columnCondition.field"
      :column-options="columnSearchOptions"
      :column-select-width="90"
      @search="resetTablePageAndQuery"
      @reset="resetSearchHandler"
      class="flex-c w-full"
      input-class-name="w-1/2"
    />

    <div class="flex mb-2">
      <span
        style="
          width: 5em;
          opacity: 0.75;
          white-space: nowrap;
          margin-right: 10px;
          text-align: right;
          color: var(--el-text-color);
        "
        >日志类型:</span
      >
      <div>
        <el-check-tag
          class="mr-2"
          :checked="state.searchCondition.model == 'alarm'"
          @change="
            () => {
              state.searchCondition.model = 'alarm';
              resetTablePageAndQuery();
            }
          "
          >原始日志
        </el-check-tag>
        <el-check-tag
          :checked="state.searchCondition.model == 'warn'"
          class="mr-2"
          style="font-size: 0.9em; margin-bottom: 8px"
          @change="
            () => {
              state.searchCondition.model = 'warn';
              resetTablePageAndQuery();
            }
          "
          >标准化日志
        </el-check-tag>
      </div>
    </div>
    <!-- 告警类型 -->
    <div class="flex mt-2 mb-6">
      <span
        style="
          width: 5em;
          opacity: 0.75;
          white-space: nowrap;
          margin-right: 10px;
          text-align: right;
        "
        >告警类型:</span
      >
      <div>
        <el-check-tag
          class="mr-2"
          :checked="state.checkAll"
          @change="tagChangeHandler(null)"
          >全部（{{ state.totalTagCount }}）
        </el-check-tag>
        <el-check-tag
          v-for="item in typeTagData"
          :key="item.tagId"
          :checked="item.checked"
          class="mr-2"
          style="font-size: 0.9em; margin-bottom: 8px"
          @change="tagChangeHandler(item)"
        >
          {{
            item.tagCount > 0
              ? `${item.tagName}(${item.tagCount})`
              : `${item.tagName}`
          }}
        </el-check-tag>
      </div>
    </div>
    <!--    &lt;!&ndash; 所属组织、所属应用系统、数据来源 &ndash;&gt;-->
    <!--    <el-row justify="space-between" class="mt-3">-->
    <!--      <el-col :span="10">-->
    <!--        <div class="flex-sc gap-2">-->
    <!--          <el-text class="mr-3">所属组织:</el-text>-->
    <!--          <el-radio-group-->
    <!--            v-model="deptType"-->
    <!--            class="flex"-->
    <!--            :disabled="deptTreeDisabled"-->
    <!--          >-->
    <!--            <el-radio-button value="0">精确</el-radio-button>-->
    <!--            <el-radio-button value="1">关联</el-radio-button>-->
    <!--          </el-radio-group>-->
    <!--          <dept-tree-select-->
    <!--            v-model:dept-id="searchCondition.orgId"-->
    <!--            clearable-->
    <!--            filterable-->
    <!--            custom-style="width: 300px;"-->
    <!--            :disabled="deptTreeDisabled"-->
    <!--            @change="deptTreeChangeHandler"-->
    <!--          />-->
    <!--          <el-checkbox-->
    <!--            v-model="onlyMySelfDept"-->
    <!--            label="只看本单位"-->
    <!--            @change="onlyMySelfDeptChangeHandler"-->
    <!--          />-->
    <!--        </div>-->
    <!--      </el-col>-->
    <!--      <el-col :span="7">-->
    <!--        <el-text>所属应用系统:</el-text>-->
    <!--        <el-select-->
    <!--          v-model="searchCondition.asset_app_name"-->
    <!--          placeholder="请选择"-->
    <!--          clearable-->
    <!--          multiple-->
    <!--          filterable-->
    <!--          style="width: 300px"-->
    <!--          class="ml-3"-->
    <!--          @change="resetTablePageAndQuery"-->
    <!--        >-->
    <!--          <el-option-->
    <!--            v-for="item in appSelData"-->
    <!--            :key="item.value"-->
    <!--            :label="item.label"-->
    <!--            :value="item.value"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--      </el-col>-->
    <!--      <el-col :span="7">-->
    <!--        <el-text>数据来源:</el-text>-->
    <!--        <el-select-->
    <!--          v-model="searchCondition.event_agent"-->
    <!--          placeholder="请选择"-->
    <!--          clearable-->
    <!--          multiple-->
    <!--          filterable-->
    <!--          style="width: 300px"-->
    <!--          class="ml-3"-->
    <!--          @change="resetTablePageAndQuery"-->
    <!--        >-->
    <!--          <el-option-->
    <!--            v-for="item in dsSelData"-->
    <!--            :key="item.value"-->
    <!--            :label="item.label"-->
    <!--            :value="item.value"-->
    <!--          />-->
    <!--        </el-select>-->
    <!--      </el-col>-->
    <!--    </el-row>-->
    <!--    &lt;!&ndash; 时间范围 &ndash;&gt;-->
    <!--    <div class="flex-sc mt-3 mb-3">-->
    <!--      <el-text class="mr-1">时间范围：</el-text>-->
    <!--      <el-segmented-->
    <!--        v-model="dateRangeSign"-->
    <!--        :options="timeSegmentOptions"-->
    <!--        @change="segmentChangeHandler"-->
    <!--      >-->
    <!--      </el-segmented>-->
    <!--      <div class="ml-3">-->
    <!--        <el-date-picker-->
    <!--          v-model="dateTimeRange"-->
    <!--          type="daterange"-->
    <!--          range-separator="到"-->
    <!--          start-placeholder="开始日期"-->
    <!--          end-placeholder="结束日期"-->
    <!--        />-->
    <!--      </div>-->
    <!--    </div>-->
    <!-- 表格 -->
    <im-table
      ref="tableRef"
      :data="tableData"
      show-overflow-tooltip
      center
      toolbar
      table-alert
      :operator="{
        label: '操作',
        width: 210
      }"
      :height="tableOption.height"
      :stripe="tableOption.stripe"
      show-checkbox
      :column-storage="createColumnStorage('alarm', 'remote')"
      :filter-data-provider="filterDataProvider"
      :pagination="tablePage"
      :loading="tableLoading"
      @on-reload="resetTablePageAndQuery"
      @on-page-change="queryEventAlarmData"
      @selection-change="selectionChangeHandler"
    >
      <!--      <template #menu-left="{ size }">-->
      <!--        <div class="flex-sc grid-flow-row gap-2 h-full">-->
      <!--          <el-tooltip content="定时刷新" placement="top" :open-delay="1000">-->
      <!--            <IconifyIconOffline icon="EP-Timer" class="text-primary" />-->
      <!--          </el-tooltip>-->
      <!--          <el-select-->
      <!--            v-model="refreshTime"-->
      <!--            placeholder="Select"-->
      <!--            style="width: 80px"-->
      <!--            @change="refreshTimeChangeHandler"-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="item in refreshOptionData"-->
      <!--              :key="item.value"-->
      <!--              :label="item.label"-->
      <!--              :value="item.value"-->
      <!--            />-->
      <!--          </el-select>-->
      <!--        </div>-->
      <!--      </template>-->
      <template #operator="{ row, size, type }">
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('EP-View')"
          @click="detailHandler(row)"
        >
          详情
        </el-button>
      </template>
      <template #toolbar-left="{ size }">
        <div class="float-left flex-sc pr-6 gap-2">
          <el-tooltip content="定时刷新" placement="top" :open-delay="1000">
            <IconifyIconOffline icon="EP-Timer" class="text-primary" />
          </el-tooltip>
          <el-select
            v-model="refreshTime"
            placeholder="Select"
            style="width: 60px"
            @change="refreshTimeChangeHandler"
          >
            <el-option
              v-for="item in refreshOptionData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </template>
      <template #toolbar-right="{ size }">
        <div class="float-left flex-sc pr-6 gap-2">
          <el-segmented
            v-model="state.dateRangeSign"
            :options="state.timeSegmentOptions"
            @change="
              () => {
                dateTimeRange = [];
                resetTablePageAndQuery();
              }
            "
          >
          </el-segmented>
          <!-- 日期选择器，用于选择事件时间范围 -->
          <el-date-picker
            v-model="dateTimeRange"
            type="daterange"
            range-separator="到"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 200px"
            :clearable="false"
            @change="
              () => {
                state.dateRangeSign = null;
                resetTablePageAndQuery();
              }
            "
          />
          <!-- 所属组织 -->
          <div class="flex-sc gap-2">
            <el-select
              v-model="deptType"
              placeholder="精确/关联"
              style="width: 60px"
              :disabled="deptTreeDisabled"
              @change="
                () => {
                  if (searchCondition.orgId) {
                    resetTablePageAndQuery();
                  }
                }
              "
            >
              <el-option value="0" label="精确"></el-option>
              <el-option value="1" label="关联"></el-option>
            </el-select>
            <dept-tree-select
              v-model:dept-id="searchCondition.orgId"
              clearable
              filterable
              custom-style="width: 180px;"
              placeholder="所属组织"
              :disabled="deptTreeDisabled"
              @change="deptTreeChangeHandler"
            >
            </dept-tree-select>
            <el-checkbox
              v-model="onlyMySelfDept"
              label="只看本单位"
              @change="onlyMySelfDeptChangeHandler"
            />
          </div>

          <!-- 所属应用系统 -->
          <el-select
            v-model="searchCondition.asset_app_name"
            placeholder="所属应用系统"
            clearable
            multiple
            collapse-tags
            style="width: 150px"
            class="ml-3"
            @change="resetTablePageAndQuery"
          >
            <el-option
              v-for="item in appSelData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <!-- 数据来源 -->
          <el-select
            v-model="searchCondition.event_agent"
            placeholder="数据来源"
            clearable
            multiple
            collapse-tags
            style="width: 140px"
            class="ml-3"
            @change="resetTablePageAndQuery"
          >
            <el-option
              v-for="item in state.dsSelData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <el-select
            v-model="searchCondition.dev_name"
            placeholder="设备来源"
            clearable
            multiple
            collapse-tags
            style="width: 140px"
            class="ml-3"
            @change="resetTablePageAndQuery"
          >
            <el-option
              v-for="item in state.devNameSelData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>


          <!-- 风险级别选择器，用于选择事件风险级别 -->
          <el-select
            v-model="searchCondition.reseverity"
            placeholder="风险级别"
            multiple
            collapse-tags
            clearable
            style="width: 120px"
            @change="resetTablePageAndQuery"
          >
            <el-option
              v-for="item in riskLevelData"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>

          <!-- 批量操作下拉菜单 -->
          <el-dropdown>
            <span class="flex-sc text-primary font-bold">
              批量操作
              <IconifyIconOffline icon="EP-ArrowDown" />
            </span>
            <template #dropdown>
              <div class="pt-2 pb-2">
                <el-dropdown-menu>
                  <!-- 批量封堵 -->
                  <el-dropdown-item>
                    <el-link
                      plain
                      :underline="false"
                      :size="size"
                      type="primary"
                      :disabled="state.selectedRowIds.length == 0"
                      :icon="useRenderIcon('RI-MailcloseFill')"
                      @click="batchBlockIpList"
                    >
                      批量封堵
                    </el-link>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </div>
            </template>
          </el-dropdown>
        </div>
      </template>

      <!-- 风险级别列 -->
      <template #reseverity="{ row }">
        <el-tag
          :color="getRiskLevelColor(row.reseverity)"
          class="text-white border-none"
        >
          {{ getRiskLevelLabel(row.reseverity) }}
        </el-tag>
      </template>

      <template #src_ip="{ row }">
        <!--              <el-text>{{ row.src_ip }}</el-text>-->
        <!--              <br></br>-->
        <el-popover
          placement="right-start"
          trigger="hover"
          width="270"
          @show="querySingleAsset(row.src_ip)"
        >
          <assets-popover-content
            :key="getAssetInfo(row.src_ip).key"
            :event-info="row"
            :asset-info="getAssetInfo(row.src_ip).data"
            direction="src"
            @ip-block="ipBlockHandler"
          />
          <template #reference>
            <el-text class="text-primary">{{ row.src_asset_name }} </el-text>
          </template>
        </el-popover>
      </template>

      <template #dst_ip="{ row }">
        <!--              <el-text>{{ row.dst_ip }}</el-text>-->
        <!--              <br></br>-->
        <el-popover placement="right-start" trigger="hover" width="270">
          <assets-popover-content
            :key="getAssetInfo(row.dst_ip).key"
            :event-info="row"
            :asset-info="getAssetInfo(row.dst_ip).data"
            direction="dst"
            @ip-block="ipBlockHandler"
          />
          <template #reference>
            <el-text class="text-primary">{{ row.dst_asset_name }} </el-text>
          </template>
        </el-popover>
      </template>

      <!-- 攻击结果 -->
      <template #attack_result="{ row }">
        <span>{{ row.attack_result_text }}</span>
      </template>
    </im-table>
    <!-- 告警详情 -->
    <alarm-detail-info
      v-model:visible="detail.visible"
      :alarm-info="detail.selAlarmInfo"
    />

    <!-- ip封堵 -->
    <ip-block-modal
      v-model:visible="ipBlock.visible"
      title="IP封堵"
      :ip-address="ipBlock.ipAddress"
      :ext-params="extParams"
      :default-plug-label="ipBlock.defaultPlugLabel"
    />

    <!-- 批量ip封堵 -->
    <multip-block-modal
      v-model:visible="ipBlock.multiVisible"
      title="IP批量封堵"
      :src-ips="ipBlock.srcIps"
      :dst-ips="ipBlock.dstIps"
      :event-uids="ipBlock.eventUids"
      :ext-params="extParams"
      @deal-success="queryEventAlarmData"
    ></multip-block-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import { useRoute } from "vue-router";
import DeptTreeSelect from "@/views/system/deptmanage/components/DeptTreeSelect.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import AlarmDetailInfo from "@/views/modules/security/event/components/AlarmDetailInfo.vue";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {
  getAlarmTagData,
  getAttackResult,
  getEventAlarmList,
  getEventEnum,
  queryAssetsByIps,
  queryEventTableHeadGroup,
  toEventAlarmRows
} from "@/views/modules/security/event/api/SecurityEventApi";
import dayjs from "dayjs";
import {
  getRiskLevelColor,
  getRiskLevelLabel,
  riskLevelData
} from "@/views/modules/security/event/util/event_data";
import { useUserStoreHook } from "@/store/modules/user";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import AssetsPopoverContent from "@/views/modules/security/event/components/AssetsPopoverContent.vue";
import IpBlockModal from "@/views/modules/security/event/components/IpBlockModal.vue";
import MultipBlockModal from "@/views/modules/security/event/components/MultipBlockModal.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
const tableRef = ref<ImTableInstance>();
const route = useRoute();

// 封堵添加告警类型标识便于后台区分
const extParams = {
  type: "alarm"
};
//数据对象
const state = reactive({
  checkAll: true,
  totalTagCount: 0,
  columnCondition: {
    value: null,
    field: "event_subtype_name",
    fuzzy: true,
    operator: "fuzzy"
  },
  columnSearchOptions: [
    {
      label: "告警类型",
      value: "event_subtype_name"
    },
    {
      label: "攻击方向",
      value: "attack_type_tag"
    },
    {
      label: "攻击结果",
      value: "attack_result"
    },
    {
      label: "源IP",
      value: "src_ip"
    },
    {
      label: "目的IP",
      value: "dst_ip"
    },
    {
      label: "数据来源",
      value: "event_agent"
    }
  ],
  dateTimeRange: [],
  dateRangeSign: "1d",
  deptType: "1",
  deptTreeDisabled: false,
  onlyMySelfDept: false,
  searchCondition: {
    event_type_tag: null,
    orgId: "",
    asset_app_name: "",
    event_agent: "",
    dev_name: null,
    reseverity: "",
    model: "alarm"
  },
  typeTagData: [],
  appSelData: [],
  dsSelData: [],
  devNameSelData: [],
  refreshOptionData: [
    {
      label: "手动",
      value: "0"
    },
    {
      label: "10秒",
      value: "10"
    },
    {
      label: "1分钟",
      value: "60"
    },
    {
      label: "10分钟",
      value: "600"
    },
    {
      label: "30分钟",
      value: "1800"
    }
  ],
  refreshTime: "0",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    // {
    //   label: "近3天",
    //   value: "3d"
    // },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    },
    {
      label: "近6月",
      value: "6m"
    },
    {
      label: "近1年",
      value: "1y"
    }
  ],
  tableLoading: false,
  assetByIpMap: {},
  tableData: [],
  tablePage: {
    total: 0,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  detail: {
    visible: false,
    selAlarmInfo: null
  },

  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件",

    // 批量封堵
    multiVisible: false,
    eventUids: [],
    srcIps: [],
    dstIps: []
  },

  selectedRowIds: [],
  selectedRows: [],

  // 过滤器
  filters: []
});

const {
  columnCondition,
  columnSearchOptions,
  dateTimeRange,
  dateRangeSign,
  deptType,
  deptTreeDisabled,
  onlyMySelfDept,
  searchCondition,
  typeTagData,
  appSelData,
  dsSelData,
  refreshOptionData,
  refreshTime,
  timeSegmentOptions,
  tableLoading,
  tableData,
  tablePage,
  detail,
  ipBlock
} = toRefs(state);

//IP地址封堵触发
const ipBlockHandler = (ip: string) => {
  state.ipBlock.ipAddress = ip;
  state.ipBlock.visible = true;
};

const batchBlockIpList = () => {
  state.ipBlock.multiVisible = true;
  state.ipBlock.eventUids = state.selectedRowIds;
  state.ipBlock.srcIps = state.selectedRows.map(event => event.src_ip);
  state.ipBlock.dstIps = state.selectedRows.map(event => event.dst_ip);
};

//加载应用系统数据
const loadAppData = async (field: string) => {
  const res = await getEventEnum(field);
  if (res.data && res.data.length > 0) {
    const dataArray = [];
    res.data.forEach((item: string) =>
      dataArray.push({
        label: item,
        value: item
      })
    );
    if (field === "asset_app_name") {
      state.appSelData = dataArray;
    } else if (field === "event_agent") {
      state.dsSelData = dataArray;
    }else  if(field === "dev_name") {
      state.devNameSelData = dataArray;
    }
  }
};
loadAppData("asset_app_name");
loadAppData("event_agent");
loadAppData("dev_name");

//初始化默认时间范围
const initTimeRange = (day: number) => {
  state.dateTimeRange = [
    dayjs().add(-day, "d").startOf("day"),
    dayjs().endOf("day")
  ];
};
initTimeRange(1);

//时间范围改变
const segmentChangeHandler = (value: string) => {
  initTimeRange(parseInt(value));
  queryEventAlarmData();
};

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 400;
});

const stringToNumberSortMethod = (a, b) => {
  if (!a) return 0;
  if (!b) return 1;
  console.log(a, b);
  try {
    a = parseInt(a);
    b = parseInt(b);
    console.log("a", a, "b", b);
    return a > b ? 1 : -1;
  } catch (e) {}
  return a > b ? 1 : -1;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.event_uid));
  state.selectedRowIds = selectIds;
  state.selectedRows = selRows;
};

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 100,
  selection: true,
  height: tableHeight,
  rowKey: "event_uid",
  column: [
    {
      label: "告警名称",
      showOverflowTooltip: true,
      prop: "event_name"
    },
    {
      label: "告警发生时间",
      prop: "last_time"
    },
    {
      label: "告警类型",
      prop: "event_subtype_name"
    },
    {
      label: "攻击方向",
      prop: "attack_type_tag"
    },
    {
      label: "攻击结果",
      prop: "attack_result_text"
    },
    {
      label: "源IP",
      prop: "src_ip"
    },
    {
      label: "源端口",
      prop: "src_port",
      sortMethod: (row1, row2) =>
        stringToNumberSortMethod(row1.src_port, row2.src_port)
    },
    {
      label: "目的IP",
      prop: "dst_ip"
    },
    {
      label: "目的端口",
      prop: "dst_port",
      sortMethod: (row1, row2) =>
        stringToNumberSortMethod(row1.dst_port, row2.dst_port)
    },
    {
      label: "风险级别",
      prop: "reseverity"
    },
    {
      label: "数据来源",
      prop: "event_agent"
    }
  ]
});

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzy) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

//重置分页后查询事件数据
const resetTablePageAndQuery = (keepFiltersFlag?: boolean) => {
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
    state.filters = [];
    tableRef.value.clearAllFilters();
  }
  state.tablePage.currentPage = 1;
  queryEventAlarmData();
};

//构建查询条件
function buildQueryCondition() {
  let dateRange;
  if (state.dateRangeSign) {
    dateRange = state.dateRangeSign;
  } else {
    if (state.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }

  const condition = {
    //查询条件
    conditions: state.columnCondition.value ? [state.columnCondition] : [],
    //日期范围
    dateRange,
    //搜索条件
    ...state.searchCondition,
    // 列头过滤器
    headerFilter: {
      filters: state.filters
    },
    //当前页码
    pageNum: state.tablePage.currentPage,
    //每页显示条数
    pageSize: state.tablePage.pageSize
  };

  //处理关联的组织数据
  if (
    state.deptType === "1" &&
    state.searchCondition.orgId &&
    state.searchCondition.orgId.length > 0
  ) {
    const childrenIds = useDeptStoreHook().getChildrenIds(
      state.searchCondition.orgId
    );
    condition.orgId = childrenIds.join(",");
  }
  return condition;
}

const queryAssetData = rows => {
  let ipList = [];
  for (let row of rows) {
    let { src_ip, dst_ip } = row;
    if (src_ip && !ipList.includes(src_ip)) {
      ipList.push(src_ip);
      state.assetByIpMap[src_ip] = {
        key: 1
      };
    }
    if (dst_ip && !ipList.includes(dst_ip)) {
      ipList.push(dst_ip);
      state.assetByIpMap[dst_ip] = {
        key: 1
      };
    }
  }
  state.assetByIpMap = {};
  queryAssetsByIps(ipList.join(","))
    .then(res => {
      let assets = res.data || [];
      for (let asset of assets) {
        // console.log("asset ", asset);
        // state.assetByIpMap
        let { ipAddress } = asset;
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: asset
        };
      }
    })
    .finally(() => {});
};

const getAssetInfo = ip => {
  let assetInfo = state.assetByIpMap[ip];
  return (
    assetInfo || {
      key: 1
    }
  );
};

// 查询当个ip绑定的资产（防止重复查询）
const querySingleAsset = ipAddress => {
  let assetInfo = state.assetByIpMap[ipAddress];
  if (assetInfo && assetInfo.data) {
    return;
  }
  // 防止重复查询
  state.assetByIpMap[ipAddress] = {
    key: 1,
    data: {}
  };
  queryAssetsByIps(ipAddress)
    .then(res => {
      let assets = res.data || [];
      if (assets.length > 0) {
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: assets[0]
        };
      }
    })
    .finally(() => {});
};

//查询事件告警数据
const queryEventAlarmData = async () => {
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();
  //构建查询条件
  const condition = buildQueryCondition();
  //查询事件告警数据
  const res = await getEventAlarmList(condition);
  //设置表格加载状态为false
  state.tableLoading = false;
  //设置表格数据
  state.tableData = toEventAlarmRows(res.data.rows);

  // 查询关联的资产
  queryAssetData(state.tableData);
  //设置表格总条数
  state.tablePage.total = res.data.totalElements;
  //加载标签数据
  await loadAlarmTagData();
};

//加载告警类型标签数据
const loadAlarmTagData = async () => {
  dealFuzzEnable();
  //构建查询条件
  const condition = buildQueryCondition();
  condition.event_type_tag = "";

  const res = await getAlarmTagData(condition);
  const resData = res.data || [];

  //处理标签已选中状态
  if (state.searchCondition.event_type_tag) {
    //查找已选中的标签
    const matchData = resData.find(
      (item: any) => item.tagId === state.searchCondition.event_type_tag.tagId
    );
    //设置标签已选中状态
    if (matchData) {
      matchData.checked = true;
    }
  }
  //设置标签数据
  state.typeTagData = resData;
  // 全部数量
  state.totalTagCount =
    resData.length == 0
      ? 0
      : resData
          .map(r => r?.tagCount || 0)
          .reduce((c1, c2) => {
            return c1 + c2;
          });
};

//查看明细触发
const detailHandler = (row: any) => {
  state.detail.selAlarmInfo = row;
  state.detail.visible = true;
};

//风险标签选中改变
const tagChangeHandler = (tag: any) => {
  state.searchCondition.event_type_tag = tag;
  state.checkAll = !tag;
  state.typeTagData.forEach((item: any) => {
    item.checked = item.tagId === tag?.tagId;
  });
  resetTablePageAndQuery();
};

//只看本单位变化触发
const onlyMySelfDeptChangeHandler = (val: boolean) => {
  if (val) {
    state.deptType = "0";
    state.deptTreeDisabled = true;
    state.searchCondition.orgId = useUserStoreHook().getUserSessionInfo.deptId;
  } else {
    state.deptType = "1";
    state.deptTreeDisabled = false;
    state.searchCondition.orgId = "";
  }
  resetTablePageAndQuery();
};

//组织树选择改变触发
const deptTreeChangeHandler = (d: SimpleDeptInfo) => {
  // if (d) {
  resetTablePageAndQuery();
  // }
};

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "event_subtype_name",
    fuzzy: true,
    operator: "fuzzy"
  };
  state.onlyMySelfDept = false;
  deptTreeDisabled.value = false;
  //重置查询条件
  state.searchCondition = {
    event_type_tag: null,
    orgId: "",
    asset_app_name: "",
    event_agent: "",
    reseverity: "",
    model: "alarm"
  };
  //重置标签选择状态
  state.typeTagData.forEach((item: any) => (item.checked = false));
  //重置时间范围
  // initTimeRange(1);
  state.dateTimeRange = [];
  state.dateRangeSign = "1d";
  resetTablePageAndQuery();
};

//定时数据刷新
let timerId: number | undefined;
const refreshTimeChangeHandler = (val: string) => {
  const millisecond = parseInt(val) * 1000;
  clearInterval(timerId);
  if (millisecond > 0) {
    timerId = window.setInterval(() => {
      queryEventAlarmData();
    }, millisecond) as unknown as number;
  }
};

const filterDataProvider: TableFilterDataProvider = {
  options: (prop, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryEventTableHeadGroup(query)
        .then(res => {
          console.log(res);
          resolve(res.data);
        })
        .catch(err => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  // 转化攻击结果
  getOptionLabel: (prop, label, option) => {
    if (prop == "attack_result") {
      return getAttackResult(label);
    }
    return label;
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery(true);
  }
};

//挂载后初始化
onMounted(() => {
  queryEventAlarmData();
});

onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timerId) {
    clearInterval(timerId);
  }
});

watch(
  () => (route?.query?.dateRangeSign || route?.query?.dateRange) as string,
  val => {
    state.dateRangeSign = val || "1d";
    state.searchCondition.model = "alarm";
  },
  { immediate: true }
);
</script>
