import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.securityEventServer}/`;
const eamCoreServerPath = `${ServerNames.eamCoreServer}/`;

const attackResultMapping = {};

/**
 * 获取攻击结果文本映射
 *
 * @param callback 回调函数
 * @param reload 强制刷新
 */
export const eventAttackResultMapping = (callback, reload?) => {
  if (Object.keys(attackResultMapping).length == 0 || reload === true) {
    http
      .get<any, any>(`${basePath}safetyAlarm/v3/eventAttackResultMapping`)
      .then(res => {
        let resData = res.data || {};
        Object.assign(attackResultMapping, resData);
      })
      .finally(() => {
        callback();
      });
  } else {
    callback();
  }
};
eventAttackResultMapping(() => {});
/**
 * 攻击结果转化文本（如果没有映射到返回原文本）
 *
 * @param result
 */
export const getAttackResult = result => {
  if (!result) {
    if (attackResultMapping[result]) {
      return attackResultMapping[result];
    }
    try {
      let nullKey = null;
      return attackResultMapping["null"] || attackResultMapping[nullKey];
    } catch (e) {
      return "";
    }
  }
  return attackResultMapping[result] || result;
};

/**
 * 批量攻击结果转化文本（添加attack_result_text字段，如果没有映射到返回原文本）
 *
 * @param rows
 */
export const toEventAlarmRows = rows => {
  if (!Array.isArray(rows)) return [];
  for (let row of rows) {
    row = row || {};
    row.attack_result_text = getAttackResult(row.attack_result || "");
  }
  return rows;
};

//查询待处置事件
const getEventList = (params: any) =>
  http.postJson<any>(`${basePath}safetyEvent/v3/queryEventList`, params);

//查询标签数据
const getTagData = (params: any) =>
  http.postJson<any>(
    `${basePath}safetyEventAggregation/v3/queryEventTag`,
    params
  );

//查询业务系统数据
const getBusinessSystemData = () =>
  http.postJson<any>(
    `${basePath}safetyEventAggregation/v3/queryAssetAppByEvent`,
    {}
  );

//查询关联告警时间线数据
const getAlarmTimelineData = (params: any) =>
  http.postJson<any>(`${basePath}safetyEvent/v3/alarmTimeline`, params);

//查询告警历史详情
const getAlarmHistoryDetail = (params: any) =>
  http.postJson<any>(`${basePath}safetyEvent/v3/alarmTimelineDetail`, params);

//查询事件上报历史明细数据
const getEventHistoryDetail = (params: any) =>
  http.postJson<any>(`${basePath}sed/eventTrendDetail`, params);

//查询事件数量统计数据
const getEventTrendData = (params: any) =>
  http.postJson<any>(`${basePath}sed/eventTrend`, params);

//查询事件时分布数据
const getEventHourDistributeData = (params: any) =>
  http.postJson<any>(`${basePath}sed/eventTrendHour`, params);

//查询事件周分布数据
const getEventWeekDistributeData = (params: any) =>
  http.postJson<any>(`${basePath}sed/eventTrendWeek`, params);

//处置安全事件
const dealEvent = (params: any) =>
  http.postJson<any>(`${basePath}safetyEvent/v3/disposal/manual`, params);

//导出安全事件数据
const exportEventData = (params: any) =>
  http.postBlobWithJson(
    `${basePath}/safetyEvent/v3/exportEventDataList`,
    params
  );

//查询事件枚举
const getEventEnum = (field: string) =>
  http.get<any, any>(
    `${basePath}safetyEvent/v3/queryEventEnumList?field=${field}`
  );

//查询告警事件标签数据
const getAlarmTagData = (params: any) =>
  http.postJson<any>(`${basePath}safetyAlarm/v3/queryAlarmTag`, params);

//查询事件告警列表数据
const getEventAlarmList = (params: any) =>
  http.postJson<any>(`${basePath}safetyAlarm/v3/queryAlarmList`, params);

// 查询攻击路径
export const eventAttack = (params: any) =>
  http.postJson<any>(`${basePath}safetyEvent/v3/eventAttack`, params);

// 根据ip列表查询资产
export const queryAssetsByIps = (ipAddress: string) => {
  return http.get<any, any>(
    `${eamCoreServerPath}redis/getIndexDataByIp?ipAddress=${ipAddress || ""}`
  );
};

// 事件服务: 根据ip列表查询资产列表
export const queryAssetsByIpsByEvent = (ipAddress: string[]) => {
  return http.postJson<any>(
    `${basePath}safetyEventAggregation/v3/accessInfo`,
    ipAddress
  );
};

export const queryEventDeptTree = () =>
  http.postJson<any>(
    `${basePath}safetyEventAggregation/v3/queryOrgTreeByEvent`,
    {
      dealWith: "1"
    }
  );

/**
 * 查询流程受理人列表(流程服务)
 */
export const queryRoleUsers = (deptId: string) => {
  return http.get<any, any>(
    `${ServerNames.eamPmServer}/common/getRoleUsers?deptId=${deptId || ""}`
  );
};

/**
 * 提交流程
 *
 * @param data
 */
export const dispatchEventProcess = data => {
  return http.postJson<any>(
    `${ServerNames.eamPmServer}/sheets/autoSecurityEventDeal/start/event/manual`,
    data
  );
};

/**
 * 查询列头过滤选项数据(下拉分组)
 *
 * @param params
 */
export const queryEventTableHeadGroup = (params: any) =>
  http.postJson<any>(
    `${basePath}safetyEvent/v3/queryEventTableHeadGroup`,
    params
  );

export {
  getEventList,
  getTagData,
  getBusinessSystemData,
  getAlarmTimelineData,
  getAlarmHistoryDetail,
  getEventHistoryDetail,
  getEventTrendData,
  getEventHourDistributeData,
  getEventWeekDistributeData,
  dealEvent,
  exportEventData,
  getEventEnum,
  getAlarmTagData,
  getEventAlarmList
};
