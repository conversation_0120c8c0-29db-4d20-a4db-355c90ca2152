import {ServerNames} from "@/utils/http/serverNames";
import {http} from "@/utils/http";

const basePath = `${ServerNames.ipPlanServer}/`;

//查询防火墙 Tree 数据
const getFireWallTree = () =>
  http.get<any, any>(`${basePath}ipplan/plug/queryFirewallList`);

//查询封堵来源
const getBlockSource = (params: any) =>
  http.postJson<any>(`${basePath}ipplan/plug/plugDataSource`, params);

//提交封堵请求
const submitBlockRequest = (params: any) =>
  http.postJson<any>(`${basePath}ipplan/plug/savePlug`, params);

export {
  getFireWallTree,
  getBlockSource,
  submitBlockRequest
}

