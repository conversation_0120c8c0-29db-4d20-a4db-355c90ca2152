//风险等级数据
const riskLevelData03 = [
  {
    id: "5",
    label: "危急"
  },
  {
    id: "4",
    label: "高危"
  },
  {
    id: "3",
    label: "中危"
  },
  {
    id: "2",
    label: "低危"
  },
  {
    id: "1",
    label: "信息"
  },
]
const getRiskLevelLabel03 = (id: string) => {
  const matchData = riskLevelData03.find((item) => item.id === id);
  if (matchData) {
    return matchData.label;
  }
  return `未知(${id})`;
}
const riskLevelData = [
  {
    id: "危急",
    label: "危急"
  },
  {
    id: "高危",
    label: "高危"
  },
  {
    id: "中危",
    label: "中危"
  },
  {
    id: "低危",
    label: "低危"
  },
  {
    id: "信息",
    label: "信息"
  },
]
const riskLevelData02 = [
  {
    id: "危急",
    label: "危急"
  },
  {
    id: "高危",
    label: "高危"
  },
  {
    id: "中危",
    label: "中危"
  },
  {
    id: "低危",
    label: "低危"
  },
  {
    id: "信息",
    label: "信息"
  },
]

//事件处置状态数据
const segmentData = [

  {
    label: "未修复",
    value: "1",
  },
  {
    label: "已修复",
    value: "2",
  },
  {
    "label": "无需处理",
    value: "3",
  },
  {
    label: "全部",
    value: "0",
  },
]

//获取指定级别的Label
const getRiskLevelLabel = (id: string) => {
  const matchData = riskLevelData.find((item) => item.id === id);
  if (matchData) {
    return matchData.label;
  }
  return `未知(${id})`;
}
const getRiskLevelLabel02 = (id: string) => {
  const matchData = riskLevelData.find((item) => item.label === id);
  if (matchData) {
    return matchData.label;
  }
  return `未知(${id})`;
}

//根据风险级别id获取颜色
const getRiskLevelColor = (id: string) => {
  switch (id) {
    case "5":
      return "#A50003";
    case "4":
      return "#FF0408";
    case "3":
      return "#FF964B";
    case "2":
      return "#0091FF";
    case "1":
      return "#028015";
    default:
      return "#000000";
  }
}
const getRiskLevelColor02 = (id: string) => {
  switch (id) {
    case "危急":
      return "#A50003";
    case "高危":
      return "#FF0408";
    case "中危":
      return "#FF964B";
    case "低危":
      return "#0091FF";
    case "信息":
      return "#028015";
    default:
      return "#000000";
  }
}

//获取处置状态Label
const getSegmentLabel = (id: string) => {
  switch (id) {
    case "未修复":
      return "未修复";
    case "已修复":
      return "已修复";
    case "无需处理":
      return "无需处理";
    default:
      return `未知(${id})`;
  }
}
const getSegmentLabel02 = (id: string) => {
  switch (id) {
    case "1":
      return "未处置";
    case "2":
      return "已处置";
    default:
      return `未知(${id})`;
  }
}

//获取处置状态颜色
const getDealStatusType = (id: string) => {
  switch (id) {
    case "未修复":
      return "warning";
    case "已修复":
      return "success";
    default:
      return "info";
  }
}
const getDealStatusType02 = (id: string) => {
  switch (id) {
    case "1":
      return "warning";
    case "2":
      return "success";
    default:
      return "info";
  }
}

export {
  riskLevelData,
  segmentData,
  getRiskLevelLabel,
  getRiskLevelColor,
  getSegmentLabel,
  getDealStatusType,
  getDealStatusType02,
  getSegmentLabel02,
  getRiskLevelColor02,
  getRiskLevelLabel02,
  riskLevelData02,
  getRiskLevelLabel03
}
