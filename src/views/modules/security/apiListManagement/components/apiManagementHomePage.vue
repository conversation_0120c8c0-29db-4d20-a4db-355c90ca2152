<template>
  <div>
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs v-model="activeTabName" class="ml-2 mr-2" @tab-change="viewTabChangeHandler">
          <!-- 组织视图标签页（暂时不使用，但保留代码以便将来可能使用） -->
          <!-- 组织视图已经被注释掉，如需使用请取消注释 -->
          <!-- 应用视图标签页 -->
          <el-tab-pane name="appView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-AppsFill" />
                <span class="ml-1">应用视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索应用 -->
              <div class="pb-1.5 flex items-center">
                <el-input placeholder="应用名称" :suffix-icon="useRenderIcon('EP-Search')" clearable v-model="appKeyWord" />
                <el-icon @click="loadAppData" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示应用结构 -->
              <el-tree ref="appTreeRef" :data="appData" node-key="value" :expand-on-click-node="false" :props="{
                label: 'assetApp'
              }" highlight-current default-expand-all :filter-node-method="filterAppNode" :style="treeStyle"
                @current-change="appChangeHandler">
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.count || 0) > 0 ? 'red' : 'unset'
                  }">{{ node.label }}
                    <span v-if="data.count > 0" style="font-size: 12px; color: #909399;"> ({{ data.count
                    }})</span></span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 使用search-with-column组件，用于搜索事件数据 -->
        <div class="flex justify-center items-center mb-6 mr-6">
          <search-with-column v-model="columnCondition.value" v-model:fuzzy-enable="columnCondition.fuzzyable"
            v-model:column-val="columnCondition.field" :column-options="columnSearchOptions" :column-select-width="90"
            @search="resetTablePageAndQuery('', 'toggleQueryCriteria')" @reset="resetSearchHandler" class="flex-c w-4/5"
            input-class-name="w-3/5" />

          <!-- 右侧按钮组已移除 -->
        </div>
        <!-- API分组 -->
        <div class="flex mt-6 mb-2">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">API分组:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag class="mr-2 tag-container" :checked="state.apiGroupCheckAll"
                @change="apiGroupChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in state.apiGroupData" :key="item.value"
                :checked="!state.apiGroupCheckAll && item.checked" class="mr-2" @change="apiGroupChangeHandler(item)">
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- API分组下的虚线分割线 -->
        <div class="dashed-divider mx-4 my-1 pr-6"></div>

        <!-- 敏感级别 -->
        <div ref="vulTagRef" class="flex mt-2 mb-3">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">敏感级别:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag class="mr-2 tag-container" :checked="state.sensitivityLevelCheckAll"
                @change="sensitivityLevelChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in state.sensitivityLevelData" :key="item.value"
                :checked="!state.sensitivityLevelCheckAll && item.checked" class="mr-2"
                @change="sensitivityLevelChangeHandler(item)">
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 移除了处理状态标签 -->

        <!-- 表格 -->
        <div class="ml-2 mr-2">
          <im-table ref="tableRef" :data="tableData" center toolbar :table-alert="{
            closable: false
          }" operator :height="tableOption.height" :stripe="tableOption.stripe" show-checkbox
            :column-storage="createColumnStorage('api-list-management', 'remote')" :pagination="tablePage"
            :loading="tableLoading" :filter-data-provider="filterDataProvider" @on-reload="resetTablePageAndQuery"
            @selection-change="selectionChangeHandler" @on-page-change="queryEventData">
            <!-- 自定义 alert 提示内容 -->
            <template #tip>
              <!-- <span class="statistics-tip" style="margin-left: 100px">
                <span class="statistics-item mr-2 ml-2" v-for="(item, index) in vulnerabilityStatisticsData"
                  :key="index">
                  <span class="label">{{ item.label }}: </span>
                  <span class="count text-[16px] font-bold ml-1">{{ item.value || 0 }}</span>
                </span>
              </span> -->
            </template>

            <!-- 表格右侧菜单 -->
            <template #toolbar-right="{ size }">
              <div class="float-left flex-sc pr-3 gap-3">
                <el-segmented v-model="state.dateRangeSign" :options="state.timeSegmentOptions" @change="
                  () => {
                    dateTimeRange = [];
                    resetTablePageAndQuery();
                  }
                ">
                </el-segmented>
                <!-- 日期选择器，用于选择事件时间范围 -->
                <el-date-picker clearable v-model="dateTimeRange" type="daterange" range-separator="到"
                  start-placeholder="开始日期" end-placeholder="结束日期" style="width: 200px" @change="dateChangeFunction" />

                <!-- 资产类别 -->
                <!-- <el-tree-select clearable v-model="searchCondition.assetTypeName" placeholder="资产类别"
                  :data="assetTypeName" check-strictly :render-after-expand="false" style="width: 160px"
                  node-key="id" @change="resetTablePageAndQuery" /> -->

                <!-- 处置状态 -->
                <!-- <el-select v-model="searchCondition.dealStatus" placeholder="处置状态" clearable collapse-tags
                  style="width: 160px" @change="resetTablePageAndQuery">
                  <el-option v-for="item in state.dsSelData" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select> -->
                <!-- 风险级别选择器，用于选择事件风险级别 -->
                <!-- <el-select v-model="searchCondition.reseverity" placeholder="漏洞级别" multiple collapse-tags clearable
                  style="width: 150px" @change="riskLevelChangeHandler">
                  <el-option v-for="item in riskLevelData" :key="item.id" :label="item.label" :value="item.id" />
                </el-select> -->

                <!-- <el-dropdown style="margin: 0 10px">
                  <el-button type="primary"> 批量操作 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :disabled="state.selectedEventRows.length == 0" style="padding: 0.4rem 1rem"
                        @click.native="
                          handleCommand('批量派单', '批量派单')
                          ">
                        批量派单
                      </el-dropdown-item>
                      <el-dropdown-item style="padding: 0.4rem 1rem" :disabled="state.selectedEventRows.length == 0"
                        @click="
                          openEditTaskDialog({
                            operationDataList: state.selectedEvents
                          })
                          " size="small" type="primary">批量验证</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
</el-dropdown> -->

                <!-- 导出事件数据 -->
                <!-- <el-tooltip content="导出数据" placement="top" :open-delay="1000">
                  <el-button style="margin-left: 0.1rem" :icon="useRenderIcon('EP-Download')" circle :size="size"
                    :disabled="tableData.length == 0" @click="exportEventHandler" />
                </el-tooltip> -->
              </div>
            </template>
            <!-- 表格操作按钮 -->
            <template #operator="{ row, size }">
              <!-- 详情按钮 -->
              <el-button :size="size" type="primary" text :icon="useRenderIcon('EP-View')"
                @click="detailViewHandler(row)">
                详情
              </el-button>
            </template>
            <!-- 风险级别列 -->
            <template #vullevel="{ row }">
              <el-tag :color="getRiskLevelColor(row.vullevel + '')" class="text-white border-none">
                {{ getRiskLevelLabel(row.vullevel + "") }}
              </el-tag>
            </template>
            <!-- 事件处理状态列 -->
            <template #dealStatus="{ row }">
              <el-tag effect="light" :type="getDealStatusType(row.dealStatus)">
                {{ getSegmentLabel(row.dealStatus) }}
              </el-tag>
            </template>

            <template #event_subtype_name="{ row }">
              <el-tag>{{ row.event_subtype_name }}</el-tag>
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>

    <EventDispatchModal @success="queryEventData" :form="eventDispatchModalForm" :modelValue="eventDispatchModalVisable"
      @update:modelValue="eventDispatchModalVisable = false" />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties,
  markRaw
} from "vue";
import dayjs from "dayjs";
import { Refresh, Download } from "@element-plus/icons-vue";
// 移除了图标导入
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import EventDispatchModal from "@/views/modules/security/apiListManagement/components/EventDispatchModal.vue";
import apiAlertDetails from "@/views/modules/security/apiListManagement/components/apiAlertDetails.vue";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData,
  segmentData
} from "@/views/modules/security/apiListManagement/util/vulnerability_data";

import {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  getBusinessSystemData,
  getUserDeptTreeAxios,
  queryAllCategoryAxios,
  queryEventDeptTree,
  vulTotalByStatus,
  vulAddNewTaskForVulMethod,
  queryApiEventAssetGroup,
  getQueryApiEventTags
} from "@/views/modules/security/apiListManagement/api/vulnerabilityHomeInterface";
import { useRoute } from "vue-router";
getQueryApiEventTags();
const route = useRoute();
const tableRef = ref<ImTableInstance>();
import { ElScrollbar } from "element-plus";

// 移除了刷新相关的变量和常量
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { queryEventTableHeadGroup } from "@/views/modules/security/event/api/SecurityEventApi";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree> | null>(null);
const appTreeRef = ref<InstanceType<typeof ElTree> | null>(null);

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

// 移除了视角切换，只保留资产视角

const deptNameData = ref<any[]>([]);

const assetTypeName = ref<any[]>([]);

const assetPhoto = ref(
  `data:image/svg+xml;base64,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`
);
// 移除了漏洞视角图标

/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};



// 打开编辑任务窗口

const openEditTaskDialog = (t: any) => {
  // console.log(t);
  vulAddNewTaskForVulMethod(t)
    .then(res => {
      $message({
        type: "success",
        message: "开始验证"
      });
      resetTablePageAndQuery();
    })
    .catch(err => {
      $message({
        type: "error",
        message: "验证失败"
      });
    });

};

// 处理树形选择器数据
const buildTree = (
  data: any[],
  deptId: string | number,
  parentId: string | number
) => {
  const map = new Map();
  const rootNodes = [];
  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, label: item["name"], children: [] });
  });
  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      } else {
        // 如果找不到parentId，则将该节点作为顶级父节点
        rootNodes.push(map.get(item[deptId]));
      }
    }
  });

  return rootNodes;
};
// 资产类别
// queryAllCategoryAxios
const queryAllCategoryAxiosMethod = async () => {
  const res = await queryAllCategoryAxios();
  // console.log(buildTree(res["data"], "id", "parentId"));

  assetTypeName.value = [...buildTree(res["data"], "id", "parentId")];
};
queryAllCategoryAxiosMethod();

const expendTreeOrg = ref([]);
const queryDeptTree = () => {
  state.deptData = [];
  // 清空关键字
  state.deptKeyWord = null;
  queryEventDeptTree().then(res => {
    // console.log("queryEventDeptTree", res.data);
    const resDeptData = Array.isArray(res.data)
      ? [...buildTree(res.data, "deptId", "parentId")]
      : [res.data || {}];
    state.deptData = [
      {
        deptiD: "",
        deptName: "全部",
        children: resDeptData
      }
    ];

    expendTreeOrg.value.splice(
      0,
      expendTreeOrg.value.push.length,
      ...["", "0", "-1"]
    );

    deptTree.value.setCurrentKey(null);
    deptSelectChange({ deptId: "" });
    queryEventData();
  });
};

//首层组织数据 计算属性
const firstLevelDeptData = computed(() => {
  return parseFirstLevelDept(
    useDeptStoreHook()?.deptData.filter(
      (d: SimpleDeptInfo) => d.parentId === "-1"
    )
  );
});

//数据对象
interface StateType {
  checkAll: boolean;
  totalTagCount: number;
  activeTabName: string;
  tableLoading: boolean;
  deptKeyWord: string | null;
  deptData: any[];
  appKeyWord: string | null;
  appData: any[];
  // eventTagData 属性已移除，因为不再使用 getTagData 接口
  columnCondition: {
    value: string | null;
    field: string;
    fuzzyable: boolean;
    operator: string;
  };
  dateRangeSign: string;
  timeSegmentOptions: Array<{ label: string; value: string }>;
  dateTimeRange: any[];
  searchCondition: {
    orgId: string;
    asset_app_name: string;
    dealStatus: string;
    riskType: string;
    riskLevel: string;
    [key: string]: any;
  };
  apiGroupCheckAll: boolean;
  sensitivityLevelCheckAll: boolean;
  apiGroupData: Array<{ label: string; value: string; checked: boolean }>;
  sensitivityLevelData: Array<{ label: string; value: string; checked: boolean }>;
  columnSearchOptions: any[];
  tableData: any[];
  assetByIpMap: Record<string, any>;
  tablePage: {
    align: string;
    total: number;
    currentPage: number;
    pageSize: number;
    pageSizes: number[];
  };
  deal: {
    visible: boolean;
    title: string;
    unitIds: any[];
    defaultAction: any;
  };
  selectedEvents: string[];
  selectedEventRows: any[];
  ipBlock: {
    visible: boolean;
    ipAddress: string;
    defaultPlugLabel: string;
  };
  dsSelData: Array<{ value: string; label: string }>;
  vulnerabilityStatisticsData: Array<{ label: string; code: string; value: number }>;
  filters: any[];
}

const state = reactive<StateType>({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "appView", // 修改默认激活的标签页为应用视图
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  // eventTagData 属性已移除，因为不再使用 getTagData 接口
  columnCondition: {
    value: null,
    field: "apiName", // 使用 apiName 作为默认搜索字段，而不是 assetName
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateRangeSign: "30d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    },
    {
      label: "近6月",
      value: "6m"
    },
    {
      label: "近1年",
      value: "1y"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    orgId: "",
    asset_app_name: "",
    dealStatus: "undisposed",
    riskType: "",
    riskLevel: ""
  },

  apiGroupCheckAll: true,
  sensitivityLevelCheckAll: true,

  apiGroupData: [
    // {
    //   label: "全部",
    //   value: "all",
    //   checked: false
    // }
    // 其他选项将从 getQueryApiEventTags 接口动态加载
  ],

  sensitivityLevelData: [
    {
      label: "1级(低敏感级)",
      value: "1级(低敏感级)",
      checked: false
    },
    {
      label: "2级(一般敏感级)",
      value: "2级(一般敏感级)",
      checked: false
    },
    {
      label: "3级(故意有毒感级)",
      value: "3级(故意有毒感级)",
      checked: false
    }
  ],

  columnSearchOptions: [],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: [
    {
      value: "undisposed",
      label: "未修复"
    },
    {
      value: "disposalof",
      label: "已修复"
    }
  ],

  vulnerabilityStatisticsData: [
    {
      label: "漏洞总数",
      code: "total",
      value: 0
    },
    {
      label: "未修复数",
      code: "undisposed",
      value: 0
    },
    {
      label: "已修复数",
      code: "disposalof",
      value: 0
    },
    {
      label: "无需处理数",
      code: "noNeedHandle",
      value: 0
    }
  ],

  filters: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  // eventTagData 已移除，因为不再使用 getTagData 接口
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  vulnerabilityStatisticsData,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const eventDispatchContext = reactive({
  dispatchVisible: false,
  dispatchForm: {
    alertIds: "",
    userId: [],
    title: "",
    deptId: ""
  }
});

// 移除了视角切换相关的监听逻辑
const initColumnOptions = () => {
  state.columnSearchOptions = [];
  state.columnCondition.value = "";
  state.columnCondition.field = "assetName";
  queryEventData();
};

const searchQuery = val => {
  state.columnSearchOptions = val;
  state.columnCondition.field = val[0]["value"];
};



watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const vulTagRef = ref<HTMLElement | null>(null);
const tableKey = ref(0);
// 计算表格高度的函数，不再触发数据查询
const vulTagHeight = () => {
  nextTick(() => {
    nextTick(() => {
      // 获取敏感级别部分的高度
      if (!vulTagRef.value) return;
      const vulTagHeight = vulTagRef.value.offsetHeight;

      // 获取API分组部分的高度
      // 由于我们只有两个筛选条件，每个部分大约高度为60px
      // 增加了搜索框和API分组之间的间距，所以增加高度
      // 移除了处理状态标签，所以减少高度
      const additionalHeight = 40; // API分组的高度

      // 计算总高度
      tmpWidth.value = vulTagHeight + additionalHeight;
      // console.log("总高度:", tmpWidth.value);

      // 计算表格高度
      tableHeight.value =
        document.documentElement.offsetHeight - 350 - tmpWidth.value; // 减小了固定偏移量
      tableOption.height = tableHeight.value;

      // console.log("表格高度:", tableOption.height);

      // 不再在这里触发数据查询，避免重复请求
      // if (tableKey.value == 0) {
      //   queryEventData();
      // }
      tableKey.value = tableKey.value + 1;
    });
  });
};

//根据页面高度设置表格高度
// const tableHeight = computed(() => {
//   // console.log(document.documentElement.offsetHeight - 400);
//   return document.documentElement.offsetHeight - 400 - tmpWidth.value;
// });
const tableHeight = ref(0);


interface TableColumnType {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  [key: string]: any;
}

interface TableOptionType {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumnType[];
  [key: string]: any;
}

const tableOption = reactive<TableOptionType>({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: tableHeight.value,
  rowKey: "ip",
  column: []
});

const dateChangeFunction = (query: any) => {
  state.dateRangeSign = "";
  if (!query) {
    state.dateRangeSign = "30d";
  }
  resetTablePageAndQuery();
};

//加载组织树节点数据


const treeStyle = computed((): CSSProperties => {
  return {
    height: tableHeight.value + 80 + "px",
    overflow: "auto"
  };
});

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (!data.deptId) {
    return true;
  }

  return data.deptName.indexOf(value) > -1;
};

//应用数据搜索
const filterAppNode = (value: string, data: any) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (data.assetApp == "全部" || data.value == "全部") return true;

  // 适配新的数据结构，同时搜索 assetApp 和 label 属性
  return (data.assetApp && data.assetApp.indexOf(value) > -1) ||
    (data.label && data.label.indexOf(value) > -1) ||
    (data.value && data.value.indexOf(value) > -1);
};

//组织树选择改变触发
const deptSelectChange = (data: any) => {
  if (data && data.deptId) {
    state.searchCondition.orgId = data.deptId;
  } else {
    state.searchCondition.orgId = "";
  }

  triggerVulViewTable["orgId"] = state.searchCondition.orgId;
  triggerVulViewTable["assetApp"] = "";
  triggerVulViewTable["vulType"] = "";
  state.searchCondition["event_type_tag"] = "";
  state.checkAll = true;
  resetTablePageAndQuery();
};

//应用选择改变
const appChangeHandler = (data: any, shouldTriggerQuery = true) => {
  if (!data) {
    // console.log("aaaaaaaaaa")
    if (appTreeRef.value) {
      appTreeRef.value.setCurrentKey("");
    }
  }

  // 适配新的数据结构，使用 value 属性
  const appValue = data?.value || data?.assetApp;
  state.searchCondition.asset_app_name = appValue;

  // 将应用筛选值保存到一个单独的属性中，以便在 buildQueryCondition 中使用
  if (appValue && appValue != "全部") {
    state.searchCondition.appFilter = {
      field: 'appName', // field 值和 value 的值一样
      value: appValue,
      fuzzyable: false,
      operator: "exact"
    };
  } else {
    state.searchCondition.asset_app_name = "";
    state.searchCondition.appFilter = null;
  }

  triggerVulViewTable["assetApp"] = state.searchCondition.asset_app_name;
  triggerVulViewTable["orgId"] = "";
  triggerVulViewTable["vulType"] = "";
  state.searchCondition["event_type_tag"] = "";
  state.checkAll = true;

  // 只有当 shouldTriggerQuery 为 true 时才触发查询
  if (shouldTriggerQuery) {
    resetTablePageAndQuery();
  }
};

//视图标签改变触发
const viewTabChangeHandler = (activeName: string) => {
  // console.log(activeName);
  // 组织视图暂时不使用，已注释
  // if (activeName === "deptView") {
  //   //处理组织视图初始化
  //   state.searchCondition.asset_app_name = "";
  //   state.searchCondition.orgId = "";
  //   nextTick(() => {
  //     if ("-1" == deptTree.value!.getCurrentKey()) {
  //       state.searchCondition.orgId = "-1";
  //       resetTablePageAndQuery();
  //     }
  //     deptTree.value!.setCurrentKey("-1");
  //     queryDeptTree();
  //   });
  //   // loadAppData();
  //   resetTablePageAndQuery();
  // } else
  if (activeName === "appView") {
    //处理应用视图初始化
    state.searchCondition.asset_app_name = "";
    state.searchCondition.orgId = "";
    state.searchCondition.appFilter = null; // 清除应用筛选条件
    // state.deptData = [];
    if (state.appData && state.appData.length > 0) {
      const firstData = state.appData[0];
      nextTick(() => {
        // 适配新的数据结构，使用 value 属性
        const nodeKey = firstData.value || firstData.assetApp;
        if (nodeKey == appTreeRef.value!.getCurrentKey()) {
          // 设置应用筛选条件
          if (nodeKey && nodeKey != "全部") {
            state.searchCondition.asset_app_name = nodeKey;
            state.searchCondition.appFilter = {
              field: nodeKey,
              value: nodeKey,
              fuzzyable: false,
              operator: "exact"
            };
          }
          resetTablePageAndQuery();
        }
        appTreeRef.value!.setCurrentKey(nodeKey);
      });
    }
  }
};

//加载业务系统数据
const loadAppData = async (shouldTriggerQuery = false) => {
  // console.log("aaa")
  const appRes = await getBusinessSystemData({
    "conditions": [],
    "model": "",
    "field": "appName",
    "nullData": true,
    "headerFilter": {
      "filters": [
      ]
    }
  });

  // 适配新的数据结构，从 options 数组中获取应用数据
  const options = appRes.data?.options || [];

  // 将选项转换为应用数据格式
  const appOptions = options.map((option: { value: string, label: string, count: number }) => ({
    assetApp: option.label,
    value: option.value,
    count: option.count
  }));

  state.appData = [
    {
      assetApp: "全部",
      value: "全部",
      children: appOptions
    }
  ];

  // console.log(state.appData)
  // 只有当 shouldTriggerQuery 为 true 时才触发查询
  if (shouldTriggerQuery) {
    appChangeHandler(null, true); // 传递 true 表示触发查询
  } else {
    appChangeHandler(null, false); // 传递 false 表示不触发查询
  }
};
// 初始加载应用数据，但不触发查询
loadAppData(false);

const triggerVulViewTable = reactive({
  number: "1",
  query: {},
  orgId: "",
  assetApp: "",
  vulType: "",
  toggleQueryCriteria: ""
});
//重置分页后查询事件数据
const resetTablePageAndQuery = (
  info = "",
  toggleQueryCriteria = "",
  keepFiltersFlag?: boolean
) => {
  // 移除了视角切换相关的日志
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
    state.filters = [];
    tableRef.value.clearAllFilters();
  }
  state.tablePage.currentPage = 1;
  queryEventData();
};

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange: string | string[] | null;
  if (state?.dateRangeSign) {
    dateRange = state?.dateRangeSign;
  } else {
    if (state?.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state?.dateTimeRange?.[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state?.dateTimeRange?.[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

const searchTmpData = (tmpConditions: any[]) => {

  try {
    if (state?.searchCondition?.["deptName"]) {
      tmpConditions.push({
        field: "refOrgNameTree",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["deptName"]
      });
    }
  } catch (error) {

  }
  try {
    if (state?.searchCondition?.["assetTypeName"]) {
      tmpConditions.push({
        field: "assetTypeName",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["assetTypeName"]
      });
    }
  } catch (error) {

  }

};

// 查询漏洞统计数据
const queryVulnerabilityStatisticsData = (queryParams: any) => {
  vulTotalByStatus(queryParams).then((res: any) => {
    let resData = res.data || {};
    for (let item of state.vulnerabilityStatisticsData) {
      let { code } = item;
      item.value = resData[code] || 0;
    }
  });
};

const buildQueryCondition = () => {
  const tmpConditions = [];
  searchTmpData(tmpConditions);

  // 如果有应用筛选条件，将其添加到 conditions 数组中
  if (state?.searchCondition?.appFilter) {
    tmpConditions.push(state.searchCondition.appFilter);
  }

  // 如果有 API 分组筛选条件，将其添加到 conditions 数组中
  if (state?.searchCondition?.apiGroupFilter) {
    tmpConditions.push(state.searchCondition.apiGroupFilter);
  }

  // 如果有敏感级别筛选条件，将其添加到 conditions 数组中
  if (state?.searchCondition?.sensitivityLevelFilter) {
    tmpConditions.push(state.searchCondition.sensitivityLevelFilter);
  }

  return {
    //查询条件
    conditions: state?.columnCondition.value
      ? [state?.columnCondition, ...tmpConditions]
      : [...tmpConditions],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    // ...state.searchCondition,
    dealStatus: state?.searchCondition?.dealStatus,
    //当前页码
    pageNum: state?.tablePage?.currentPage,
    //每页显示条数
    pageSize: state?.tablePage?.pageSize,
    orgId: state?.searchCondition?.orgId,
    // 不再单独传递 assetApp 参数，而是将其放入 conditions 数组中
    // assetApp: state?.searchCondition?.asset_app_name,
    vulLevel:
      state?.searchCondition?.["reseverity"] &&
      state?.searchCondition?.["reseverity"].join(","),
    vulType:
      state?.searchCondition?.["event_type_tag"] &&
      state?.searchCondition?.["event_type_tag"]?.tagName,
    // API分组 - 不再单独传递 apiGroup 参数，而是将其放入 conditions 数组中
    // apiGroup: state?.searchCondition?.apiGroup || null,
    // 敏感级别
    sensitivityLevel: state?.searchCondition?.sensitivityLevel || null,

    // 视角标识
    viewType: "asset",
    // 列头过滤器
    headerFilter: {
      filters: state?.filters
    }
  };
};

//查询事件数据
const queryEventData = async () => {
  state.selectedEventRows = [];
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    // 查询条件
    const queryParams = buildQueryCondition();
    // console.log("queryParams=========================>", queryParams);

    // 查询统计数据(异步)
    // queryVulnerabilityStatisticsData(queryParams);

    //根据条件查询事件列表
    const res = await vulDetailByAssetMethod(queryParams);
    //设置表格数据 - 适配新的数据结构
    state.tableData = res["data"]?.list || [];

    // 不需要单独处理表头，表格会自动处理

    // 不再在查询时设置搜索字段，避免重置用户选择

    // 查询关联的资产
    // queryAssetData(state.tableData);
    //设置表格总条数 - 适配新的数据结构
    state.tablePage.total = res["data"]?.total || 0;

    // 不再加载标签数据，因为不再使用 getTagData 接口
    // 调用 vulTagHeight 以确保表格高度正确
    vulTagHeight();
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};



// 加载标签数据函数已移除，因为不再使用 getTagData 接口

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

// 风险标签选中改变函数已移除，因为不再使用 getTagData 接口

//API分组标签选中改变
const apiGroupChangeHandler = (tag: any) => {
  // 如果选中的是全部或者没有选中，则清空筛选条件
  if (!tag || tag.isCustomAll || tag.value === "ALL_GROUPS") {
    // 清空 API 分组筛选条件
    state.searchCondition["apiGroup"] = "";
    state.searchCondition["apiGroupFilter"] = null;
  } else {
    // 否则将筛选条件保存到 apiGroupFilter 中，以便在 buildQueryCondition 中使用
    state.searchCondition["apiGroup"] = tag.value; // 保留原有字段以便兼容
    state.searchCondition["apiGroupFilter"] = {
      field: "focusApi", // 使用 groupNames 作为 field
      value: tag.tagName, // 使用 tagName 作为 value
      fuzzyable: true,
      operator: "fuzzy"
    };
  }

  // 更新全选状态
  state.apiGroupCheckAll = !tag || (tag && tag.isCustomAll);

  // 更新各选项的选中状态
  state.apiGroupData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });

  // 重新查询数据
  resetTablePageAndQuery();
};

//敏感级别标签选中改变
const sensitivityLevelChangeHandler = (tag: any) => {
  // console.log(tag);
  state.searchCondition["sensitivityLevel"] = tag ? tag.value : "";

  // 如果选中了敏感级别，则创建一个筛选条件
  if (tag) {
    state.searchCondition["sensitivityLevelFilter"] = {
      field: "dataLevelName", // 使用 dataLevelName 作为字段名
      value: tag.value,
      fuzzyable: true,
      operator: "fuzzy"
    };
  } else {
    // 如果没有选中，则清除筛选条件
    state.searchCondition["sensitivityLevelFilter"] = null;
  }

  state.sensitivityLevelCheckAll = !tag;
  state.sensitivityLevelData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });
  resetTablePageAndQuery();
};

// 移除了处理状态变更处理函数

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "apiName", // 使用 apiName 作为默认搜索字段，而不是 assetName
    fuzzyable: true,
    operator: "fuzzy"
  };
  // 不再重置标签选择状态，因为不再使用 getTagData 接口
  state.searchCondition["event_type_tag"] = null;
  triggerVulViewTable["vulType"] = "";
  //重置处置状态
  state.searchCondition["dealWith"] = "1";
  //重置时间范围
  state.dateTimeRange = [];
  state.dateRangeSign = "30d";
  //重置风险级别
  state.searchCondition["reseverity"] = "";
  state.searchCondition["deptName"] = "";
  state.searchCondition["assetTypeName"] = "";
  //重置处理状态
  state.searchCondition["dealStatus"] = "undisposed";
  //重置API分组
  state.searchCondition["apiGroup"] = "";
  state.searchCondition["apiGroupFilter"] = null; // 清除 API 分组筛选条件
  state.apiGroupCheckAll = true;
  state.apiGroupData.forEach((item: any) => (item.checked = false));
  //重置敏感级别
  state.searchCondition["sensitivityLevel"] = "";
  state.searchCondition["sensitivityLevelFilter"] = null; // 清除敏感级别筛选条件
  state.sensitivityLevelCheckAll = true;
  state.sensitivityLevelData.forEach((item: any) => (item.checked = false));

  // 不重置应用视图的选中状态，保留已选中的应用
  // state.searchCondition.asset_app_name = "";
  // state.searchCondition.appFilter = null;
  // if (appTreeRef.value) {
  //   appTreeRef.value.setCurrentKey("");
  // }

  resetTablePageAndQuery("漏洞重置", "toggleQueryCriteria");
};

//查看事件详情触发
const detailViewHandler = (evt: any) => {
  emit("event-select", evt);
  let needsPassedData = {};
  needsPassedData["timeSelect"] = state.dateRangeSign;
  needsPassedData["dealStatus"] = state.searchCondition["dealStatus"];
  needsPassedData["reseverity"] = state.searchCondition["reseverity"];

  // 将当前行数据添加到传递的数据中
  needsPassedData["rowData"] = evt;

  switch (evt?.["dealStatus"]) {
    case "未修复":
      needsPassedData["dealStatus"] = "undisposed";
      break;
    case "已修复":
      needsPassedData["dealStatus"] = "disposalof";
      break;
    case "无需处理":
      needsPassedData["dealStatus"] = "noNeedHandle";
      break;
    default:
      // 如果事件没有处理状态，使用当前选中的处理状态
      needsPassedData["dealStatus"] = state.searchCondition["dealStatus"];
      break;
  }

  // 使用 apiAlertDetails 组件显示详情
  jumpTo("apiAlertInfo", needsPassedData);
};

//事件处置触发-单条
const eventDealHandler = (evt: any) => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "";
  state.deal.unitIds = [evt.ip];
  state.deal.visible = true;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds: string[] = [];
  selRows.forEach(row => selectIds.push(row.ip));
  state.selectedEvents = selectIds;
  state.selectedEventRows = selRows;
};



//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    const tmpConditions = [];
    searchTmpData(tmpConditions);
    await importVulDetailByAssetMethod({
      //查询条件
      conditions: state.columnCondition.value
        ? [state.columnCondition, ...tmpConditions]
        : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      //搜索条件
      // ...state.searchCondition,
      dealStatus: state.searchCondition["dealStatus"],
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize,
      orgId: state.searchCondition.orgId,
      assetApp: state.searchCondition.asset_app_name,
      vulLevel:
        state.searchCondition["reseverity"] &&
        state.searchCondition["reseverity"].join(","),
      vulType:
        state.searchCondition["event_type_tag"] &&
        state.searchCondition["event_type_tag"].tagName,
      // API分组
      apiGroup: state.searchCondition["apiGroup"] || null,
      // 敏感级别
      sensitivityLevel: state.searchCondition["sensitivityLevel"] || null
    });
  });
};



// 移除了刷新表格和处理刷新间隔变化的函数

// 窗口大小变化时重新计算表格高度
const handleResize = () => {
  vulTagHeight();
};

// 加载 API 分组数据
const loadApiGroupData = async () => {
  try {
    const res = await getQueryApiEventTags();
    if (res.status === "0" && res.data && Array.isArray(res.data)) {
      // 创建一个新的全部选项
      // const allOption = {
      //   label: "全部",
      //   value: "ALL_GROUPS",
      //   checked: false,
      //   isCustomAll: true // 标记为自定义的全部选项
      // };

      // 将接口返回的数据转换为所需的格式，并过滤掉接口返回的“全部”选项
      const apiGroupOptions = res.data
        .filter(item => item.tagName !== "全部") // 过滤掉接口返回的“全部”选项
        .map(item => {
          if (item.tagName == route?.query?.focusApi) {
            state.apiGroupCheckAll = false;
          }
          return {
            label: item.tagName,
            value: item.tagId,
            tagName: item.tagName, // 保存 tagName 以便在构建条件时使用
            checked: item.tagName == route?.query?.focusApi ? true : false
          }
        });

      // 更新 apiGroupData
      state.apiGroupData = [...apiGroupOptions];
    }
  } catch (error) {
    console.error("Failed to load API group data:", error);
  }
};

//挂载后初始化
onMounted(() => {
  // 接收路由传递的参数，这里简单处理下
  if (route.query.dateRange == "") {
    state.dateRangeSign = "";
  } else {
    state.dateRangeSign = (route.query.dateRange || "30d") as string;
  }

  if (route?.query?.focusApi) {
    state.searchCondition["apiGroupFilter"] = {
      field: "focusApi", // 使用 groupNames 作为 field
      value: route?.query?.focusApi, // 使用 tagName 作为 value
      fuzzyable: true,
      operator: "fuzzy"
    };
  }

  // console.log("route.query",route.query.focusApi)
  // initTimeRange();
  // 组织视图暂时不使用，已注释
  // queryDeptTree();

  // 设置默认搜索字段为 API名称
  state.columnCondition.field = "apiName";
  // state.columnCondition.label = "API名称";

  // 设置搜索条件下拉选项
  interface QuerySearchItem {
    value: string;
    label: string;
    type: string;
  }

  const tmpQuerySearch: QuerySearchItem[] = [
    { value: 'apiName', label: 'API名称', type: 'Input' },
    { value: 'clientIp', label: '客户端地址', type: 'Input' },
    { value: 'appName', label: '应用名称', type: 'Input' },
    { value: 'appDomain', label: '应用域名', type: 'Input' },
    { value: 'proxyType', label: '协议', type: 'Input' },
  ];

  columnSearchOptions.value = tmpQuerySearch;

  // 加载 API 分组数据
  loadApiGroupData();

  // 加载应用数据，但不触发查询
  // loadAppData() 已在全局调用，不需要在这里再次调用

  // 计算表格高度
  nextTick(() => {
    vulTagHeight();

    // 在所有初始化完成后，只触发一次数据查询
    queryEventData();
  });

  // 添加窗口大小变化事件监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清除事件监听
onUnmounted(() => {
  // 移除窗口大小变化事件监听
  window.removeEventListener('resize', handleResize);
});

const eventDispatchModalVisable = ref(false);
const eventDispatchModalForm = reactive({
  title: "",
  deptId: "",
  userId: [],
  params: "",
  currentView: "assetView"
});
const initEventDispatchModalForm = () => {
  eventDispatchModalForm.title = "";
  eventDispatchModalForm.deptId = "";
  eventDispatchModalForm.userId = [];
  eventDispatchModalForm.params = "";
  // 移除了视角切换，只保留资产视角
};
const handleCommand = (command: string, info = "", onlyOne = "") => {
  initEventDispatchModalForm();
  eventDispatchModalVisable.value = true;
  const tmpArray = state.selectedEventRows.map(item => {
    return item["ip"];
  });
  eventDispatchModalForm.params = tmpArray.join();
  // console.log(eventDispatchModalForm.params);
  return;
};

const filterDataProvider: TableFilterDataProvider = {
  options: (prop: string, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryApiEventAssetGroup(query)
        .then((res: any) => {
          // console.log(res);
          resolve(res.data);
        })
        .catch((err: any) => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery("", "", true);
  }
};

//跳转
const jumpTo = (sign: string, needsPassedData: Object) => {
  emit("jump-to", sign, needsPassedData);
};
</script>

<style scoped>
.deal-status-tabs :deep(.el-tabs__header),
.risk-type-tabs :deep(.el-tabs__header),
.risk-level-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

/* 保留灰色分割横线 */
.deal-status-tabs :deep(.el-tabs__nav-wrap::after),
.risk-type-tabs :deep(.el-tabs__nav-wrap::after),
.risk-level-tabs :deep(.el-tabs__nav-wrap::after) {
  /* 不再隐藏分割线 */
  height: 1px;
  background-color: #e4e7ed;
}

.deal-status-tabs :deep(.el-tabs__active-bar),
.risk-type-tabs :deep(.el-tabs__active-bar),
.risk-level-tabs :deep(.el-tabs__active-bar) {
  background-color: var(--el-color-primary);
}

.deal-status-tabs :deep(.el-tabs__item),
.risk-type-tabs :deep(.el-tabs__item),
.risk-level-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.deal-status-tabs :deep(.el-tabs__item.is-active),
.risk-type-tabs :deep(.el-tabs__item.is-active),
.risk-level-tabs :deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
  font-weight: bold;
}

/* 虚线分割线样式 */
.dashed-divider {
  height: 1px;
  border: none;
  border-top: 1px dashed #e4e7ed;
  width: 98%;
}
</style>

<!--
组织视图代码（暂时不使用，但保留以便将来可能使用）
如需恢复组织视图，请将以下代码放回到适当位置：

1. 在el-tabs中添加组织视图标签页：

el-tab-pane name="deptView"
  template #label
    div class="flex-c"
      IconifyIconOffline icon="RI-TeamFill" /
      span class="ml-1"组织视图/span
    /div
  /template
  div
    div class="pb-1.5 flex items-center"
      el-input placeholder="组织名称" :suffix-icon="useRenderIcon('EP-Search')" clearable
        v-model="deptKeyWord" /
      el-icon @click="queryDeptTree" style="
          cursor: pointer;
          margin-left: 5px;
          color: var(--el-color-primary);
        "
        Refresh/Refresh
      /el-icon
    /div
    el-tree ref="deptTree" :data="state.deptData" :props="treeProps" node-key="deptId"
      :render-after-expand="false" :expand-on-click-node="false" highlight-current
      :default-expanded-keys="expendTreeOrg" :filter-node-method="filterDeptNode" :style="treeStyle"
      @current-change="deptSelectChange"
      template #default="{ node, data }"
        span :style="{
          color: (data.vulCount || 0) > 0 ? 'red' : 'unset'
        }"节点标签/span
      /template
    /el-tree
  /div
/el-tab-pane

2. 将activeTabName改回为"deptView"（如果需要组织视图作为默认视图）

3. 取消注释 viewTabChangeHandler 函数中的组织视图相关代码

4. 取消注释 onMounted 函数中的 queryDeptTree() 调用
-->
