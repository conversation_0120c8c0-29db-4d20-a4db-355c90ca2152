<template>
  <div class="sensitive-info-section">
    <div class="section-title">
      <h3>敏感信息</h3>
    </div>
    <div class="table-container">
      <el-table :data="sensitiveData" style="width: 100%" border class="custom-table" empty-text="暂无敏感信息数据" row-key="riskType">
        <el-table-column prop="riskType" label="敏感标签名称">
          <template #default="{ row }">
            <span>{{ row.riskType || '未知标签' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskLevel" label="风险等级">
          <template #default="{ row }">
            <el-tag :style="{
              backgroundColor: getRiskLevelColor(row.riskLevel),
              color: '#fff',
              border: 'none'
            }">{{ getRiskLevelText(row.riskLevel) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="apiUrl" label="API url">
          <template #default="{ row }">
            <span>{{ row.apiUrl || '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="apiName" label="API名称" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.apiName || '无名称' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="primary" link @click="viewSensitiveContent(row)">查看数据内容</el-button>
          </template>
        </el-table-column>
        <template #empty>
          <div class="empty-state">
            <el-empty description="暂无敏感信息数据" :image-size="100" />
          </div>
        </template>
      </el-table>
    </div>
    <!-- 分页功能已移除 -->

    <!-- 敏感数据内容对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="null"
      width="650px"
      :show-close="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
      destroy-on-close
      center
      class="sensitive-data-dialog"
      @closed="handleDialogClose"
    >
      <template #header>
        <div class="dialog-header">
          <div class="dialog-title">敏感数据内容</div>
          <el-tag :style="{
            backgroundColor: getRiskLevelColor(selectedRow.riskLevel),
            color: '#fff',
            border: 'none',
            marginLeft: '10px'
          }">{{ getRiskLevelText(selectedRow.riskLevel) }}</el-tag>
        </div>
      </template>
      <div class="sensitive-content">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">敏感标签名称</div>
            <div class="info-value">{{ selectedRow.riskType }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">API url</div>
            <div class="info-value">{{ selectedRow.apiUrl }}</div>
          </div>
        </div>
        <div class="info-item content-item">
          <div class="info-label">API名称</div>
          <div class="content-box">
            <MonacoEditor
              v-model="selectedRow.apiName"
              :height="contentHeight + 'px'"
              :options="monacoOptions"
              language="plaintext"
              theme="vs"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";

// 定义props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
});

// 分页相关变量已移除

// 对话框相关
const dialogVisible = ref(false);
const selectedRow = reactive({
  riskType: '',
  riskLevel: '',
  apiUrl: '',
  apiName: ''
});

// Monaco编辑器配置
const monacoOptions = reactive({
  readOnly: true, // 设置为只读模式
  wordWrap: 'on',
  minimap: { enabled: false },
  automaticLayout: true,
  lineNumbers: 'on',
  scrollBeyondLastLine: false,
  renderLineHighlight: 'none',
  fontFamily: 'Consolas, "Courier New", monospace',
  fontSize: 14,
  fontWeight: '400',
  lineHeight: 20,
  letterSpacing: 0.5,
  theme: 'vs',
  padding: { top: 0, bottom: 0 }, // 移除内边距
  fixedOverflowWidgets: true, // 确保提示不会被编辑器容器裁剪
  hover: { enabled: true, delay: 300 }, // 启用悬停提示并设置延迟
  contextmenu: false, // 禁用右键菜单
  overviewRulerBorder: false, // 移除概览标尺边框
  overviewRulerLanes: 0, // 禁用概览标尺通道
  hideCursorInOverviewRuler: true, // 隐藏概览标尺中的光标
  renderOverviewRuler: false, // 禁用概览标尺渲染
  // 设置中文提示
  'semanticHighlighting.enabled': false,
  'accessibilitySupport': 'off',
  'suggest.localityBonus': true,
  'suggest.shareSuggestSelections': true
});

// 设置编辑器高度
const contentHeight = computed(() => {
  return 160; // 进一步减少编辑器高度，减少与关闭按钮的间距
});

// 模拟数据已移除

// 调试信息已移除

// 计算数据
const sensitiveData = computed(() => {
  console.log('传入的数据:', props.data);

  // 如果有传入数据，则使用传入的数据
  if (props.data) {
    // 如果是字符串，尝试解析为 JSON
    if (typeof props.data === 'string') {
      try {
        const parsedData = JSON.parse(props.data);
        console.log('解析字符串数据:', parsedData);
        if (Array.isArray(parsedData)) {
          return parsedData;
        }
      } catch (e) {
        console.error('解析数据失败:', e);
      }
    }

    // 如果是数组
    if (Array.isArray(props.data)) {
      console.log('使用传入的数组数据:', props.data);
      return props.data;
    } else {
      console.log('传入的数据不是数组:', typeof props.data);
    }
  } else {
    console.log('没有传入数据');
  }

  // 如果没有有效数据，返回空数组
  return [];
});

// 风险等级映射
const riskLevelMap = {
  '4': '危急',
  '3': '高危',
  '2': '中危',
  '1': '低危',
  '0': '信息'
};

// 获取风险等级对应的中文名称
const getRiskLevelText = (level: string | number) => {
  if (!level) return '未知等级';

  // 将等级转换为字符串
  const levelStr = String(level);

  // 如果是数字类型的等级，直接使用映射
  if (riskLevelMap[levelStr]) {
    return riskLevelMap[levelStr];
  }

  // 如果已经是中文等级，直接返回
  if (Object.values(riskLevelMap).includes(levelStr)) {
    return levelStr;
  }

  // 默认返回原始值
  return levelStr;
};

// 获取风险等级对应的颜色
const getRiskLevelColor = (level: string | number) => {
  if (!level) return '#909399'; // 如果没有等级，返回默认颜色

  // 将等级转换为字符串
  const levelStr = String(level);

  // 根据数字或中文返回对应颜色
  switch (levelStr) {
    case '4':
    case '危急':
    case '严重':
      return '#A50003'; // 危急/严重颜色
    case '3':
    case '高危':
      return '#FF0408'; // 高危颜色
    case '2':
    case '中危':
      return '#FF964B'; // 中危颜色
    case '1':
    case '低危':
      return '#0091FF'; // 低危颜色
    case '0':
    case '信息':
      return '#028015'; // 信息颜色
    default:
      return '#909399'; // 默认颜色
  }
};

// 查看敏感数据内容
const viewSensitiveContent = (row: any) => {
  selectedRow.riskType = row.riskType;
  selectedRow.riskLevel = row.riskLevel;
  selectedRow.apiUrl = row.apiUrl;
  selectedRow.apiName = row.apiName;
  dialogVisible.value = true;
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
};

// 分页相关函数已移除
</script>

<style scoped>
.sensitive-info-section {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.section-title {
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.section-title h3 {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.table-container {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.custom-table {
  --el-table-header-bg-color: #f5f7fa;
  --el-table-border-color: #ebeef5;
  font-size: 13px;
}

.empty-state {
  padding: 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

:deep(.el-empty__image) {
  width: 100px;
  height: 100px;
}

:deep(.el-empty__description) {
  margin-top: 10px;
  font-size: 14px;
  color: #909399;
}

/* 对话框样式 */
:deep(.sensitive-data-dialog .el-dialog) {
  border-radius: 8px;
  overflow: visible; /* 修改为visible，允许内容溢出显示 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5; /* 保留边框 */
}

:deep(.sensitive-data-dialog .el-dialog__header) {
  margin: 0;
  padding: 0;
}

:deep(.sensitive-data-dialog .el-dialog__headerbtn) {
  top: 20px;
  right: 20px;
  font-size: 18px;
  z-index: 10;
}

:deep(.sensitive-data-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #909399;
}

:deep(.sensitive-data-dialog .el-dialog__headerbtn:hover .el-dialog__close) {
  color: #409EFF;
}

:deep(.sensitive-data-dialog .el-dialog__body) {
  padding: 0 20px 10px; /* 减少底部内边距 */
}

:deep(.sensitive-data-dialog .el-dialog__footer) {
  padding: 0 20px 20px; /* 减少上方内边距 */
  border-top: none;
  margin-top: -10px; /* 使用负边距进一步减少间距 */
}

.dialog-header {
  display: flex;
  align-items: center;
  padding: 20px 20px 16px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.sensitive-content {
  padding: 10px 0;
}

.info-grid {
  display: flex;
  margin-bottom: 20px;
}

.info-item {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.info-grid .info-item {
  margin-right: 20px;
}

.info-grid .info-item:last-child {
  margin-right: 0;
}

.content-item {
  margin-bottom: 0; /* 移除底部外边距 */
}

.info-label {
  font-size: 15px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 8px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.info-value {
  font-size: 14px;
  color: #303133;
  word-break: break-all;
  line-height: 1.6;
  padding-left: 2px;
}

.content-box {
  border-radius: 4px;
  border: none; /* 移除边框 */
  /* border-bottom: 1px solid #dcdfe6; 只保留底部边框 */
  overflow: visible; /* 修改为visible，允许提示溢出显示 */
  height: auto;
  background-color: #ffffff; /* 改为白色背景 */
  position: relative; /* 添加相对定位，为内部元素提供定位上下文 */
  padding-bottom: 2px; /* 减少底部内边距 */
  display: flex;
  flex-direction: column;
  box-shadow: none; /* 移除额外的边框效果 */
  margin-bottom: 0; /* 移除底部外边距 */
  min-height: 180px; /* 减少最小高度 */
}

/* Monaco编辑器相关样式 - 完全重写 */
:deep(.monaco-editor) {
  padding: 0; /* 移除内边距 */
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  margin: 0 !important; /* 确保没有外边距 */
  max-height: 170px; /* 限制编辑器高度 */
}

/* 处理MonacoEditor组件容器 */
:deep(.monaco-editor-container) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 移除Monaco编辑器内部元素的边框，但不影响其他元素 */
:deep(.monaco-editor .monaco-editor-background *) {
  border-top: none !important;
  border-bottom: none !important;
  border-left: none !important;
  border-right: none !important;
  outline: none !important;
}

:deep(.monaco-editor .overflow-guard) {
  border-radius: 4px;
  border: none !important;
  box-shadow: none !important;
}

:deep(.monaco-editor .monaco-scrollable-element) {
  border-radius: 4px;
  border: none !important;
}

/* 移除编辑器顶部蓝色边框 */
:deep(.monaco-editor .decorationsOverviewRuler) {
  display: none !important;
}

:deep(.monaco-editor .editor-scrollable-view) {
  border: none !important;
}

/* 特别处理可能存在的蓝色边框 */
:deep(.monaco-editor .view-overlays) {
  border-top: none !important;
}

:deep(.monaco-editor .view-lines) {
  border-top: none !important;
}

:deep(.monaco-editor .lines-content) {
  border-top: none !important;
}

:deep(.monaco-editor .lines-content.monaco-editor-background) {
  border-top: none !important;
}

:deep(.monaco-editor-background) {
  background-color: #ffffff !important;
  border: none !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

:deep(.monaco-editor .margin) {
  background-color: #ffffff !important;
  border: none !important;
}

/* 确保编辑器提示能正常显示 */
:deep(.monaco-editor .monaco-hover) {
  z-index: 3000 !important;
}

:deep(.monaco-editor .context-view) {
  z-index: 3000 !important;
}

:deep(.monaco-editor .suggest-widget) {
  z-index: 3000 !important;
}

:deep(.monaco-editor .parameter-hints-widget) {
  z-index: 3000 !important;
}
</style>
