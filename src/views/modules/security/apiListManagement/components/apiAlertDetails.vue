<template>
  <div class="api-alert-details">
    <!-- 顶部区域：返回按钮、标题和操作按钮 -->
    <div class="header-container">
      <el-page-header @back="handleBack" class="page-header">
        <template #content>
          <span class="mr-3 font-bold">
            API告警详情
          </span>
        </template>
      </el-page-header>

      <div class="action-buttons">
<!--        <el-button type="primary" @click="handleProcess">处置</el-button>-->
<!--        <el-button @click="handleNoProcess">无需处理</el-button>-->
      </div>
    </div>

    <!-- 基本信息部分 -->
    <div class="basic-info-section">
      <div class="api-icon">
        <div class="icon-wrapper">
          <span>API</span>
        </div>
      </div>
      <div class="info-grid">
        <div class="info-row">
          <div class="info-label">应用主机：</div>
          <el-tooltip :content="formattedAppHost" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="info-value text-ellipsis">{{ formattedAppHost }}</span>
          </el-tooltip>
        </div>
        <div class="info-row">
          <div class="info-label">应用URL：</div>
          <el-tooltip :content="apiData.appUrl" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="info-value text-ellipsis">{{ apiData.appUrl }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="risk-buttons">
<!--        <el-button type="primary" class="risk-button" @click="handleProcess">服务性风险</el-button>-->
<!--        <el-button type="warning" class="risk-button" @click="handleNoProcess">合规性风险</el-button>-->
      </div>
      <div class="data-source">
        <span>数据来源：{{ apiData.dataSource }}</span>
        <span>更新时间：{{ apiData.updateTime }}</span>
      </div>
    </div>

    <!-- 详细信息部分 -->
    <div class="detail-info-section">
      <div class="section-header">
        <div class="section-title">
          <h3>API详情</h3>
        </div>
      </div>
      <div class="info-grid-detailed">
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">API名称</div>
            <el-tooltip :content="apiData.apiName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.apiName }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">应用名称</div>
            <el-tooltip :content="apiData.appName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.appName }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求方式</div>
            <div class="info-value">{{ apiData.requestMethod }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置时间</div>
            <div class="info-value">{{ apiData.processTime }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置说明</div>
            <el-tooltip :content="apiData.processDescription" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.processDescription }}</span>
            </el-tooltip>
          </div>
        </div>
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">API标签</div>
            <el-tooltip :content="apiData.apiTag" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.apiTag }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">应用域名</div>
            <el-tooltip :content="apiData.appDomain" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.appDomain }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">协议</div>
            <div class="info-value">{{ apiData.protocol }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置人员</div>
            <div class="info-value">{{ apiData.processor }}</div>
          </div>
        </div>
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">数据敏感级别</div>
            <el-tooltip :content="apiData.dataSensitivityLevel" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.dataSensitivityLevel }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求路径</div>
            <el-tooltip :content="apiData.requestPath" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.requestPath }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求类型</div>
            <el-tooltip :content="apiData.requestType" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.requestType }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- 敏感信息部分 -->
    <SensitiveInfoTable :data="apiData.risks" />

    <!-- 请求与响应信息部分 -->
    <div class="request-response-section">
      <div class="section-title">
        <h3>请求与响应信息</h3>
      </div>

      <!-- 请求头 -->
      <div class="section-subtitle">请求头</div>
      <div class="code-block">
        <MonacoEditor
          ref="requestHeaderRef"
          v-model="apiData.requestHeader"
          :height="(contentHeight + 100) + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 请求体 -->
      <div class="section-subtitle">请求体</div>
      <div class="code-block">
        <MonacoEditor
          ref="requestBodyRef"
          v-model="apiData.requestBody"
          :height="contentHeight + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 响应头 -->
      <div class="section-subtitle">响应头</div>
      <div class="code-block">
        <MonacoEditor
          ref="responseHeaderRef"
          v-model="apiData.responseHeader"
          :height="contentHeight + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 响应体 -->
      <div class="section-subtitle">响应体</div>
      <div class="code-block">
        <MonacoEditor
          ref="responseBodyRef"
          v-model="apiData.responseBody"
          :height="(contentHeight + 200) + 'px'"
          :options="monacoOptions"
          language="json"
        />
      </div>
    </div>

    <!-- 原有的敏感信息部分已移除，由SensitiveInfoTable组件替代 -->

    <!-- 文件信息部分 -->
    <div class="file-info-section">
      <div class="section-title">
        <h3>文件信息</h3>
      </div>

      <div class="file-info-grid">
        <div class="file-info-col">
          <div class="file-info-label">文件类型</div>
          <el-tooltip :content="apiData.fileType" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileType }}</span>
          </el-tooltip>
        </div>
        <div class="file-info-col">
          <div class="file-info-label">文件名称</div>
          <el-tooltip :content="apiData.fileName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileName }}</span>
          </el-tooltip>
        </div>
        <div class="file-info-col">
          <div class="file-info-label">文件大小</div>
          <el-tooltip :content="apiData.fileSize" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileSize }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose, computed, Ref, watch } from 'vue';
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
import SensitiveInfoTable from "./SensitiveInfoTable.vue";

// 创建 MonacoEditor 的引用
const requestHeaderRef = ref();
const requestBodyRef = ref();
const responseHeaderRef = ref();
const responseBodyRef = ref();

// 导出组件，使其可以被引用
defineExpose({});

// 定义props
const props = defineProps({
  alertId: {
    type: String,
    required: false,
    default: ''
  },
  needsPassedData: {
    type: Object,
    default: () => ({})
  }
});

// 定义emit
const emit = defineEmits(['close', 'process', 'noProcess', 'back']);

// 移除了风险信息和资产调用信息分页相关变量

// Monaco编辑器配置
const monacoOptions = reactive({
  readOnly: true,
  wordWrap: 'on',
  minimap: { enabled: false },
  automaticLayout: true
});

// 根据页面高度设置编辑器高度
const contentHeight = computed(() => {
  return 200; // 可以根据需要调整默认高度
});

// 计算应用主机显示内容，将 appName 和 appAddress 拼接起来
const formattedAppHost = computed(() => {
  if (apiData.appName && apiData.appAddress) {
    return `${apiData.appName}(${apiData.appAddress})`;
  } else if (apiData.appName) {
    return apiData.appName;
  } else if (apiData.appAddress) {
    return apiData.appAddress;
  } else {
    return '';
  }
});

// 格式化Monaco的内容
const formatMonacoContent = (monacoRef: Ref) => {
  if (monacoRef.value && monacoRef.value.format) {
    monacoRef.value.format();
    setTimeout(() => {
      monacoRef.value.getEditor().updateOptions({ readOnly: true });
    }, 300);
  }
};

// 模拟API数据
const apiData = reactive({
  appHost: '',
  appUrl: '',
  riskTags: [] as any[],
  dataSource: '',
  updateTime: '',
  apiName: '',
  apiTag: '',
  appName: '',
  appAddress: '', // 添加应用地址字段
  appDomain: '',
  requestMethod: '',
  protocol: '',
  processTime: '',
  processor: '',
  processDescription: '',
  dataSensitivityLevel: '',
  requestPath: '',
  requestType: '',

  // 风险信息
  riskInfo: [] as any[],

  // 敏感信息表格数据
  risks: [] as any[],

  // 资产调用信息
  assetInfo: [] as any[],

  // 请求与响应信息
  requestHeader: ``,
  requestBody: ``,
  responseHeader: ``,
  responseBody: ``,

  // 敏感信息
  requestSensitiveTag: '',
  responseSensitiveTag: '',

  // 文件信息
  fileType: '',
  fileName: '',
  fileSize: ''
});

// 模拟获取API告警详情数据
const fetchApiAlertDetails = async (id: string) => {
  console.log('获取告警详情，ID:', id);
  // 这里模拟异步请求
  return new Promise((resolve) => {
    setTimeout(() => {
      // 实际项目中，这里应该是真实的API请求
      // const response = await fetch(`/api/alerts/${id}`);
      // const data = await response.json();
      resolve(apiData);
    }, 500);
  });
};

// 移除了风险信息和资产调用信息分页相关函数

// 处理处置按钮点击
const handleProcess = () => {
  emit('process', props.alertId);
};

// 处理无需处理按钮点击
const handleNoProcess = () => {
  emit('noProcess', props.alertId);
};

// 处理关闭按钮点击 - 现在通过返回按钮实现
// 如果将来需要单独的关闭功能，可以取消注释并使用此函数
/*
const handleClose = () => {
  emit('close');
  emit('back'); // 关闭同时返回主页面
};
*/

// 处理返回按钮点击
const handleBack = () => {
  emit('back');
};

// 监听 risks 数据的变化功能已移除

// 组件挂载时获取数据
onMounted(async () => {
  // 检查是否有传递的行数据
  if (props.needsPassedData && props.needsPassedData.rowData) {
    console.log('使用传递的行数据:', props.needsPassedData.rowData);

    // 将传递的行数据映射到apiData中
    const rowData = props.needsPassedData.rowData;

    // 更新apiData中的字段，根据行数据的字段进行映射
    if (rowData.appName) apiData.appName = rowData.appName;
    if (rowData.appAddress) apiData.appAddress = rowData.appAddress; // 添加应用地址映射
    if (rowData.appHost) apiData.appHost = rowData.appHost;
    if (rowData.appDomain) apiData.appDomain = rowData.appDomain;
    if (rowData.reqUrl) {
      apiData.requestPath = rowData.reqUrl;
      apiData.appUrl = rowData.reqUrl;
    }
    if (rowData.apiName) apiData.apiName = rowData.apiName;

    // 使用 focusApi 作为 API 标签
    if (rowData.focusApi) apiData.apiTag = rowData.focusApi;

    // 使用 reqMethod 作为请求方式
    if (rowData.reqMethod) apiData.requestMethod = rowData.reqMethod;

    // 使用 proxyType 作为协议
    if (rowData.proxyType) apiData.protocol = rowData.proxyType;

    // 使用 dataLevelName 作为数据敏感级别
    if (rowData.dataLevelName) apiData.dataSensitivityLevel = rowData.dataLevelName;

    // 使用 reqMethod 作为请求类型
    if (rowData.reqMethod) apiData.requestType = rowData.reqMethod;

    // 使用 dealTime 作为处置时间
    if (rowData.dealTime) apiData.processTime = rowData.dealTime;

    // 使用 dealUserName 作为处置人员
    if (rowData.dealUserName) apiData.processor = rowData.dealUserName;

    // 使用 dealIdea 作为处置说明
    if (rowData.dealIdea) apiData.processDescription = rowData.dealIdea;

    // 使用 lastTime 作为更新时间
    if (rowData.lastTime) apiData.updateTime = rowData.lastTime;

    // 如果有请求和响应信息，也进行映射
    if (rowData.reqHeaders) {
      try {
        const headers = typeof rowData.reqHeaders === 'string' ? JSON.parse(rowData.reqHeaders) : rowData.reqHeaders;
        apiData.requestHeader = Object.entries(headers)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
      } catch (e) {
        console.error('解析reqHeaders失败:', e);
        apiData.requestHeader = rowData.reqHeaders;
      }
    }

    if (rowData.reqBody) apiData.requestBody = rowData.reqBody;

    if (rowData.rspHeaders) {
      try {
        const headers = typeof rowData.rspHeaders === 'string' ? JSON.parse(rowData.rspHeaders) : rowData.rspHeaders;
        apiData.responseHeader = Object.entries(headers)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
      } catch (e) {
        console.error('解析rspHeaders失败:', e);
        apiData.responseHeader = rowData.rspHeaders;
      }
    }

    if (rowData.rspBody) apiData.responseBody = rowData.rspBody;

    // 如果有敏感信息标签，也进行映射
    if (rowData.requestSensitiveTag) apiData.requestSensitiveTag = rowData.requestSensitiveTag;
    if (rowData.responseSensitiveTag) apiData.responseSensitiveTag = rowData.responseSensitiveTag;

    // 如果有敏感信息表格数据，也进行映射
    if (rowData.risks) {
      try {
        // 如果 risks 是字符串，尝试解析为 JSON
        const risksData = typeof rowData.risks === 'string' ? JSON.parse(rowData.risks) : rowData.risks;
        console.log('解析后的 risks 数据:', risksData);

        // 确保解析后的数据是数组
        if (Array.isArray(risksData)) {
          apiData.risks = risksData;
        } else {
          console.error('risks 数据不是数组:', risksData);
          apiData.risks = [];
        }
      } catch (e) {
        console.error('解析 risks 失败:', e);
        apiData.risks = [];
      }
    } else {
      apiData.risks = [];
    }

    // 如果有文件信息，也进行映射
    if (rowData.fileType !== undefined) apiData.fileType = rowData.fileType || '(为空表示无文件传输)';
    if (rowData.fileName !== undefined) apiData.fileName = rowData.fileName || '无';
    if (rowData.fileSize !== undefined) apiData.fileSize = rowData.fileSize || '0';

    // 如果有其他需要映射的字段，可以继续添加

  } else if (props.alertId) {
    // 如果没有传递行数据，则使用alertId获取数据
    await fetchApiAlertDetails(props.alertId);
  } else {
    // 如果既没有传递行数据，也没有alertId，则使用默认数据
    console.log('使用默认数据');
    // 默认数据已经在apiData中初始化
  }

  // 格式化所有Monaco编辑器内容
  setTimeout(() => {
    formatMonacoContent(requestHeaderRef);
    formatMonacoContent(requestBodyRef);
    formatMonacoContent(responseHeaderRef);
    formatMonacoContent(responseBodyRef);
  }, 500);
});
</script>

<style scoped>
.api-alert-details {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.back-button {
  margin-bottom: 15px;
}

.back-icon {
  margin-right: 5px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-header {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.basic-info-section {
  display: flex;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  position: relative;
  border-bottom: 1px solid #eee;
}

.api-icon {
  margin-right: 20px;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.info-grid {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
  font-size: 14px;
  color: #333;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.risk-buttons {
  position: absolute;
  right: 250px;
  top: 15px;
  display: flex;
  gap: 10px;
}

.risk-button {
  font-size: 12px;
  padding: 6px 12px;
  height: auto;
}

.data-source {
  position: absolute;
  right: 15px;
  top: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  color: #666;
}

.detail-info-section {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.section-header {
  margin-bottom: 15px;
}

.info-grid-detailed {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-col {
  flex: 1;
  min-width: 250px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
  font-weight: normal;
}

.info-item .info-value {
  font-size: 14px;
  color: #333;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

.custom-tooltip {
  max-width: 300px;
  word-break: break-all;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.section-title h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 10px;
}

.section-title h3:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #409EFF;
}

.risk-info-section,
.asset-info-section,
.request-response-section,
.sensitive-info-section,
.file-info-section {
  margin-bottom: 30px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.section-subtitle {
  font-weight: 500;
  margin: 15px 0 10px;
  padding: 8px;
  background-color: #f5f7fa;
  font-size: 14px;
  color: #333;
}

.code-block {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
}

.sensitive-info-grid,
.file-info-grid {
  display: flex;
  gap: 20px;
}

.sensitive-info-col,
.file-info-col {
  flex: 1;
}

.sensitive-info-label,
.file-info-label {
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.sensitive-info-value,
.file-info-value {
  font-size: 14px;
  color: #333;
  max-width: 100%;
  display: inline-block;
}

.fr {
  font-size: 12px;
  color: #999;
}



.code-block {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  width: 100%;
}

/* 表格容器样式 */
.table-container {
  margin-bottom: 10px;
}

/* 自定义表格样式 */
.custom-table {
  --el-table-header-bg-color: #f5f7fa;
  --el-table-border-color: #ebeef5;
  font-size: 13px;
}

.custom-table :deep(th) {
  font-weight: 500;
  color: #606266;
}

.custom-table :deep(td) {
  color: #333;
}

.custom-table :deep(.el-scrollbar__bar.is-horizontal) {
  height: 8px;
}

.custom-table :deep(.el-scrollbar__bar.is-vertical) {
  width: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .basic-info-section {
    flex-direction: column;
  }

  .risk-buttons,
  .data-source {
    position: static;
    margin-top: 15px;
  }

  .info-grid-detailed,
  .sensitive-info-grid,
  .file-info-grid {
    flex-direction: column;
  }
}
</style>
