<template>
  <div class="api-alert-details">
    <!-- 顶部区域：返回按钮、标题和操作按钮 -->
    <div class="header-container">
      <el-page-header @back="handleBack" class="page-header">
        <template #content>
          <span class="mr-3 font-bold">
            API告警详情
          </span>
        </template>
      </el-page-header>

      <div class="action-buttons">
        <el-button type="primary" @click="handleProcess">处置</el-button>
        <el-button @click="handleNoProcess">无需处理</el-button>
      </div>
    </div>

    <!-- 处置对话框 -->
    <el-dialog title="处置" width="33%" destroy-on-close append-to-body v-model="showHandle">
      <Disposal v-if="showHandle" @closeDraw="closeDraw"
        :batchOperationModelData="batchOperationRowsData" />
    </el-dialog>

    <!-- 基本信息部分 -->
    <div class="basic-info-section">
      <div class="api-icon">
        <div class="icon-wrapper">
          <span>API</span>
        </div>
      </div>
      <div class="info-grid">
        <div class="info-row">
          <div class="info-label">应用主机：</div>
          <el-tooltip :content="apiData.appHost" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="info-value text-ellipsis">{{ apiData.appHost }}</span>
          </el-tooltip>
        </div>
        <div class="info-row">
          <div class="info-label">应用URL：</div>
          <el-tooltip :content="apiData.appUrl" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="info-value text-ellipsis">{{ apiData.appUrl }}</span>
          </el-tooltip>
        </div>
      </div>
      <div class="risk-buttons">
        <!-- <el-button type="primary" class="risk-button" @click="handleProcess">服务性风险</el-button>
        <el-button type="warning" class="risk-button" @click="handleNoProcess">合规性风险</el-button> -->
      </div>
      <div class="data-source">
        <span>数据来源：{{ apiData.dataSource }}</span>
        <span>更新时间：{{ apiData.updateTime }}</span>
      </div>
    </div>

    <!-- 详细信息部分 -->
    <div class="detail-info-section">
      <div class="section-header">
        <div class="section-title">
          <h3>API详情</h3>
        </div>
      </div>
      <div class="info-grid-detailed">
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">API名称</div>
            <el-tooltip :content="apiData.apiName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.apiName }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">应用名称</div>
            <el-tooltip :content="apiData.appName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.appName }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求方式</div>
            <div class="info-value">{{ apiData.requestMethod }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置时间</div>
            <div class="info-value">{{ apiData.processTime }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置说明</div>
            <el-tooltip :content="apiData.processDescription" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.processDescription }}</span>
            </el-tooltip>
          </div>
        </div>
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">API标签</div>
            <el-tooltip :content="apiData.apiTag" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.apiTag }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">应用域名</div>
            <el-tooltip :content="apiData.appDomain" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.appDomain }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">协议</div>
            <div class="info-value">{{ apiData.protocol }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">处置人员</div>
            <div class="info-value">{{ apiData.dealUserName || apiData.processor }}</div>
          </div>
        </div>
        <div class="info-col">
          <div class="info-item">
            <div class="info-label">数据敏感级别</div>
            <el-tooltip :content="apiData.dataSensitivityLevel" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.dataSensitivityLevel }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求路径</div>
            <el-tooltip :content="apiData.requestPath" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.requestPath }}</span>
            </el-tooltip>
          </div>
          <div class="info-item">
            <div class="info-label">请求类型</div>
            <el-tooltip :content="apiData.requestType" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
              <span class="info-value text-ellipsis">{{ apiData.requestType }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <!-- 风险信息部分 -->
    <div class="risk-info-section">
      <div class="section-title">
        <h3>风险信息</h3>
      </div>
      <div class="table-container">
        <el-table :data="apiData.riskInfo" style="width: 100%" border class="custom-table" max-height="500">
          <el-table-column prop="riskTime" label="风险发生时间" width="180" />
          <el-table-column prop="riskLevel" label="风险等级" width="120" />
          <el-table-column prop="apiUrl" label="API url" width="180" />
          <el-table-column prop="riskType" label="风险类型" width="120" />
          <el-table-column prop="ruleName" label="触发规则名称" />
        </el-table>
      </div>
      <!-- 分页已移除，因为数据一次性获取 -->
    </div>

    <!-- 资产调用信息部分 -->
    <div class="asset-info-section">
      <div class="section-title">
        <h3>资产调用信息</h3>
      </div>
      <div class="table-container">
        <el-table :data="apiData.assetInfo" style="width: 100%" border class="custom-table" max-height="500">
          <el-table-column prop="callerApp" label="访问源应用" />
          <el-table-column prop="callerIp" label="访问源ip" />
          <el-table-column prop="callerPort" label="访问源端口" />
          <el-table-column prop="appAddress" label="应用地址" />
          <el-table-column prop="appPort" label="应用端口" />
          <el-table-column prop="requestUrl" label="请求URL" />
        </el-table>
      </div>
      <!-- 分页已移除，因为数据一次性获取 -->
    </div>

    <!-- 请求与响应信息部分 -->
    <div class="request-response-section">
      <div class="section-title">
        <h3>请求与响应信息</h3>
      </div>

      <!-- 请求头 -->
      <div class="section-subtitle">请求头</div>
      <div class="code-block">
        <MonacoEditor
          ref="requestHeaderRef"
          v-model="apiData.requestHeader"
          :height="(contentHeight + 100) + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 请求体 -->
      <div class="section-subtitle">请求体</div>
      <div class="code-block">
        <MonacoEditor
          ref="requestBodyRef"
          v-model="apiData.requestBody"
          :height="contentHeight + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 响应头 -->
      <div class="section-subtitle">响应头</div>
      <div class="code-block">
        <MonacoEditor
          ref="responseHeaderRef"
          v-model="apiData.responseHeader"
          :height="contentHeight + 'px'"
          :options="monacoOptions"
          language="plaintext"
        />
      </div>

      <!-- 响应体 -->
      <div class="section-subtitle">响应体</div>
      <div class="code-block">
        <MonacoEditor
          ref="responseBodyRef"
          v-model="apiData.responseBody"
          :height="(contentHeight + 200) + 'px'"
          :options="monacoOptions"
          language="json"
        />
      </div>
    </div>

    <!-- 敏感信息部分 -->
    <div class="sensitive-info-section">
      <div class="section-title">
        <h3>敏感信息</h3>
      </div>

      <div class="sensitive-info-grid">
        <div class="sensitive-info-col">
          <div class="sensitive-info-label">请求敏感标签内容</div>
          <el-tooltip :content="apiData.requestSensitiveTag" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="sensitive-info-value text-ellipsis">{{ apiData.requestSensitiveTag }}</span>
          </el-tooltip>
        </div>
        <div class="sensitive-info-col">
          <div class="sensitive-info-label">响应敏感标签内容</div>
          <el-tooltip :content="apiData.responseSensitiveTag" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="sensitive-info-value text-ellipsis">{{ apiData.responseSensitiveTag }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>

    <!-- 文件信息部分 -->
    <div class="file-info-section">
      <div class="section-title">
        <h3>文件信息</h3>
      </div>

      <div class="file-info-grid">
        <div class="file-info-col">
          <div class="file-info-label">文件类型</div>
          <el-tooltip :content="apiData.fileType" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileType }}</span>
          </el-tooltip>
        </div>
        <div class="file-info-col">
          <div class="file-info-label">文件名称</div>
          <el-tooltip :content="apiData.fileName" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileName }}</span>
          </el-tooltip>
        </div>
        <div class="file-info-col">
          <div class="file-info-label">文件大小</div>
          <el-tooltip :content="apiData.fileSize" placement="top" :show-after="500" :enterable="false" popper-class="custom-tooltip">
            <span class="file-info-value text-ellipsis">{{ apiData.fileSize }}</span>
          </el-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineExpose, computed, Ref } from 'vue';
// 导入 MonacoEditor 组件
import MonacoEditor from "@/components/monaco/MonacoEditor.vue";
// 导入处置组件
import Disposal from "@/views/modules/security/apiRiskManagement/components/Disposal.vue";
import { http } from "@/utils/http";
import { ElMessage } from "element-plus";

// 创建 MonacoEditor 的引用
const requestHeaderRef = ref();
const requestBodyRef = ref();
const responseHeaderRef = ref();
const responseBodyRef = ref();

// 导出组件，使其可以被引用
defineExpose({});

// 定义props
const props = defineProps({
  alertId: {
    type: String,
    required: true
  },
  needsPassedData: {
    type: Object,
    default: () => ({})
  }
});

// 定义emit
const emit = defineEmits(['close', 'process', 'noProcess', 'back']);

// 处置对话框显示状态
const showHandle = ref(false);

// 处置数据
const batchOperationRowsData = reactive({
  apiEventUid: ""
});

// 风险信息总数
const totalRiskItems = ref(3);

// 资产调用信息总数
const totalAssetItems = ref(1);

// Monaco编辑器配置
const monacoOptions = reactive({
  readOnly: true,
  wordWrap: 'on',
  minimap: { enabled: false },
  automaticLayout: true
});

// 根据页面高度设置编辑器高度
const contentHeight = computed(() => {
  return 200; // 可以根据需要调整默认高度
});

// 格式化Monaco的内容
const formatMonacoContent = (monacoRef: Ref) => {
  if (monacoRef.value && monacoRef.value.format) {
    monacoRef.value.format();
    setTimeout(() => {
      monacoRef.value.getEditor().updateOptions({ readOnly: true });
    }, 300);
  }
};

// 模拟API数据
const apiData = reactive({
  appHost: '',
  appUrl: '',
  riskTags: [] as any[],
  dataSource: '',
  updateTime: '',
  apiName: '',
  apiTag: '',
  appName: '',
  appDomain: '',
  requestMethod: '',
  protocol: '',
  processTime: '',
  processor: '',
  processDescription: '',
  dataSensitivityLevel: '',
  requestPath: '',
  requestType: '',

  // 风险信息
  riskInfo: [] as any[],

  // 资产调用信息
  assetInfo: [] as any[],

  // 请求与响应信息
  requestHeader: ``,
  requestBody: ``,
  responseHeader: ``,
  responseBody: ``,

  // 敏感信息
  requestSensitiveTag: '',
  responseSensitiveTag: '',

  // 文件信息
  fileType: '',
  fileName: '',
  fileSize: ''
});

// 模拟获取API告警详情数据
const fetchApiAlertDetails = async (id: string) => {
  console.log('获取告警详情，ID:', id);
  // 这里模拟异步请求
  return new Promise((resolve) => {
    setTimeout(() => {
      // 实际项目中，这里应该是真实的API请求
      // const response = await fetch(`/api/alerts/${id}`);
      // const data = await response.json();
      resolve(apiData);
    }, 500);
  });
};

// 分页相关函数已移除，因为数据一次性获取

// 处理处置按钮点击
const handleProcess = () => {
  // 如果有传递的行数据，使用行数据的apiEventUid
  if (props.needsPassedData && props.needsPassedData.rowData && props.needsPassedData.rowData.apiEventUid) {
    batchOperationRowsData.apiEventUid = props.needsPassedData.rowData.apiEventUid;
    showHandle.value = true;
  } else {
    // 如果没有行数据，使用alertId
    emit('process', props.alertId);
  }
};

// 处理无需处理按钮点击
const handleNoProcess = () => {
  emit('noProcess', props.alertId);
  try{
    http
      .postJson("security-event/apiEvent/disposal/manual", {
          "dealIdea": '无需处理',
          "apiEventUid": props.needsPassedData.rowData.apiEventUid,
          "dealStatus": 'nodisposed',
      })
      .then(res => {
          if (!res['errors']) {
              ElMessage.success("处置提交成功！");
          }
      })
      .catch(() => {
          ElMessage.error("处置提交失败！");
      });
  }catch(e){
    console.log(e);
  }
};

// 关闭处置抽屉
const closeDraw = () => {
  showHandle.value = false;
  // 刷新数据
  if (props.needsPassedData && props.needsPassedData.rowData) {
    const rowData = props.needsPassedData.rowData;
    // 更新处置状态
    const dealStatusMap = {
      'undisposed': '未处置',
      'disposed': '已处置',
      'nodisposed': '无需处置'
    };
    apiData.processDescription = dealStatusMap['disposed'] || '已处置';
    apiData.processTime = new Date().toLocaleString();
  }
};

// 处理关闭按钮点击 - 现在通过返回按钮实现
// 如果将来需要单独的关闭功能，可以取消注释并使用此函数
/*
const handleClose = () => {
  emit('close');
  emit('back'); // 关闭同时返回主页面
};
*/

// 处理返回按钮点击
const handleBack = () => {
  emit('back');
};

// 组件挂载时获取数据
onMounted(async () => {
  // 检查是否有传递的行数据
  if (props.needsPassedData && props.needsPassedData.rowData) {
    console.log('使用传递的行数据:', props.needsPassedData.rowData);

    // 将传递的行数据映射到apiData中
    const rowData = props.needsPassedData.rowData;

    // 更新apiData中的字段，根据新的数据结构进行映射
    if (rowData.appName) apiData.appName = rowData.appName;
    if (rowData.appDomain) apiData.appDomain = rowData.appDomain;
    if (rowData.reqUrl) {
      apiData.requestPath = rowData.reqUrl;
      apiData.appUrl = rowData.reqUrl;
    }
    if (rowData.apiName) apiData.apiName = rowData.apiName;
    if (rowData.focusApi) apiData.apiTag = rowData.focusApi;
    if (rowData.reqMethod) apiData.requestMethod = rowData.reqMethod;
    if (rowData.proxyType) apiData.protocol = rowData.proxyType;
    if (rowData.dataLevelName) apiData.dataSensitivityLevel = rowData.dataLevelName;
    if (rowData.clientAddress) apiData.appHost = rowData.clientAddress + (rowData.clientIp ? ` (${rowData.clientIp})` : '');
    if(rowData.reqLabel) apiData.requestType = rowData.reqLabel;

    // 定义风险等级映射
    const riskLevelMap = {
      '1': '低风险',
      '2': '中风险',
      '3': '高风险'
    };

    // 处理风险等级
    if (rowData.riskLevel) {
      // 根据风险等级数字设置对应的文本
      const riskLevelText = riskLevelMap[rowData.riskLevel] || '未知风险';

      // 更新风险信息表格
      if (apiData.riskInfo && apiData.riskInfo.length > 0) {
        apiData.riskInfo.forEach(item => {
          item.riskLevel = riskLevelText;
        });
      }
    }

    // 处理处置状态
    if (rowData.dealStatus) {
      const dealStatusMap = {
        'undisposed': '未处置',
        'disposed': '已处置',
        'nodisposed': '无需处置'
      };
      apiData.processDescription = dealStatusMap[rowData.dealStatus] || '未知状态';
      if (rowData.dealTime) apiData.processTime = rowData.dealTime;
      if (rowData.dealUserName) apiData.processor = rowData.dealUserName;
    }

    // 处理时间信息
    if (rowData.lastTime) apiData.updateTime = rowData.lastTime;

    // 处理风险类型和触发规则
    if (rowData.riskType || rowData.ruleNames) {
      // 尝试解析risks字段，如果存在
      let risksArray = [];
      if (rowData.risks) {
        try {
          risksArray = typeof rowData.risks === 'string' ? JSON.parse(rowData.risks) : rowData.risks;
        } catch (e) {
          console.error('解析risks字段失败:', e);
        }
      }

      // 如果有风险数据，使用风险数据替换现有的风险信息
      if (risksArray.length > 0) {
        apiData.riskInfo = risksArray.map(risk => ({
          riskTime: risk.riskTime ? new Date(risk.riskTime).toLocaleString() : rowData.lastTime,
          riskLevel: risk.riskLevel ? riskLevelMap[risk.riskLevel] || '未知风险' : '',
          apiUrl: risk.apiUrl || rowData.reqUrl,
          riskType: risk.riskType || rowData.riskType,
          ruleName: risk.ruleName || rowData.ruleNames
        }));
        totalRiskItems.value = risksArray.length;
      } else {
        // 如果没有风险数据，使用行数据更新现有的风险信息或创建新的风险信息
        if (apiData.riskInfo && apiData.riskInfo.length > 0) {
          apiData.riskInfo.forEach(item => {
            if (rowData.riskType) item.riskType = rowData.riskType;
            if (rowData.ruleNames) item.ruleName = rowData.ruleNames;
            if (rowData.reqUrl) item.apiUrl = rowData.reqUrl;
            item.riskTime = rowData.lastTime || item.riskTime;
          });
        } else {
          // 如果没有现有的风险信息，创建一个新的风险信息数组
          apiData.riskInfo = [{
            riskTime: rowData.lastTime || new Date().toLocaleString(),
            riskLevel: rowData.riskLevel ? riskLevelMap[rowData.riskLevel] || '未知风险' : '未知风险',
            apiUrl: rowData.reqUrl || '',
            riskType: rowData.riskType || '',
            ruleName: rowData.ruleNames || ''
          }];
          totalRiskItems.value = 1;
        }
      }
    }

    // 处理资产调用信息
    if (rowData.clientIp || rowData.appAddress) {
      apiData.assetInfo = [{
        callerApp: rowData.fromAppName || rowData.appName,
        callerIp: rowData.clientIp || '',
        callerPort: rowData.port || '',
        appAddress: rowData.appAddress || '',
        appPort: rowData.remotePort || '',
        requestUrl: rowData.reqUrl || ''
      }];
      totalAssetItems.value = 1;
    }

    // 处理请求和响应数据
    if (rowData.reqHeaders) {
      try {
        const headers = typeof rowData.reqHeaders === 'string' ? JSON.parse(rowData.reqHeaders) : rowData.reqHeaders;
        apiData.requestHeader = Object.entries(headers)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
      } catch (e) {
        console.error('解析reqHeaders失败:', e);
        apiData.requestHeader = rowData.reqHeaders;
      }
    }

    if (rowData.reqBody) apiData.requestBody = rowData.reqBody;

    if (rowData.rspHeaders) {
      try {
        const headers = typeof rowData.rspHeaders === 'string' ? JSON.parse(rowData.rspHeaders) : rowData.rspHeaders;
        apiData.responseHeader = Object.entries(headers)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
      } catch (e) {
        console.error('解析rspHeaders失败:', e);
        apiData.responseHeader = rowData.rspHeaders;
      }
    }

    if (rowData.rspBody) apiData.responseBody = rowData.rspBody;

    // 处理敏感信息
    if (rowData.sensitiveDataLabName) {
      apiData.requestSensitiveTag = rowData.sensitiveDataLabName;
      apiData.responseSensitiveTag = rowData.sensitiveDataLabName;
    }

    // 处理文件信息
    if (rowData.fileType !== undefined) apiData.fileType = rowData.fileType || '(为空表示无文件传输)';
    if (rowData.fileName !== undefined) apiData.fileName = rowData.fileName || '无';
    if (rowData.fileSize !== undefined) apiData.fileSize = rowData.fileSize || '0';

  } else if (props.alertId) {
    // 如果没有传递行数据，则使用alertId获取数据
    await fetchApiAlertDetails(props.alertId);
  }

  // 格式化所有Monaco编辑器内容
  setTimeout(() => {
    formatMonacoContent(requestHeaderRef);
    formatMonacoContent(requestBodyRef);
    formatMonacoContent(responseHeaderRef);
    formatMonacoContent(responseBodyRef);
  }, 500);
});
</script>

<style scoped>
.api-alert-details {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.back-button {
  margin-bottom: 15px;
}

.back-icon {
  margin-right: 5px;
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.page-header {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.basic-info-section {
  display: flex;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  position: relative;
  border-bottom: 1px solid #eee;
}

.api-icon {
  margin-right: 20px;
}

.icon-wrapper {
  width: 40px;
  height: 40px;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.info-grid {
  flex: 1;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
  font-size: 14px;
  color: #333;
}

.info-value {
  font-size: 14px;
  color: #333;
}

.risk-buttons {
  position: absolute;
  right: 250px;
  top: 15px;
  display: flex;
  gap: 10px;
}

.risk-button {
  font-size: 12px;
  padding: 6px 12px;
  height: auto;
}

.data-source {
  position: absolute;
  right: 15px;
  top: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
  color: #666;
}

.detail-info-section {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.section-header {
  margin-bottom: 15px;
}

.info-grid-detailed {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-col {
  flex: 1;
  min-width: 250px;
}

.info-item {
  margin-bottom: 15px;
}

.info-item .info-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
  font-weight: normal;
}

.info-item .info-value {
  font-size: 14px;
  color: #333;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
}

.custom-tooltip {
  max-width: 300px;
  word-break: break-all;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.section-title h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  position: relative;
  padding-left: 10px;
}

.section-title h3:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #409EFF;
}

.risk-info-section,
.asset-info-section,
.request-response-section,
.sensitive-info-section,
.file-info-section {
  margin-bottom: 30px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.section-subtitle {
  font-weight: 500;
  margin: 15px 0 10px;
  padding: 8px;
  background-color: #f5f7fa;
  font-size: 14px;
  color: #333;
}

.code-block {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}

.code-block pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
}

.sensitive-info-grid,
.file-info-grid {
  display: flex;
  gap: 20px;
}

.sensitive-info-col,
.file-info-col {
  flex: 1;
}

.sensitive-info-label,
.file-info-label {
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 14px;
  color: #606266;
}

.sensitive-info-value,
.file-info-value {
  font-size: 14px;
  color: #333;
  max-width: 100%;
  display: inline-block;
}

.fr {
  font-size: 12px;
  color: #999;
}



.code-block {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  width: 100%;
}

/* 表格容器样式 */
.table-container {
  margin-bottom: 10px;
}

/* 自定义表格样式 */
.custom-table {
  --el-table-header-bg-color: #f5f7fa;
  --el-table-border-color: #ebeef5;
  font-size: 13px;
}

.custom-table :deep(th) {
  font-weight: 500;
  color: #606266;
}

.custom-table :deep(td) {
  color: #333;
}

.custom-table :deep(.el-scrollbar__bar.is-horizontal) {
  height: 8px;
}

.custom-table :deep(.el-scrollbar__bar.is-vertical) {
  width: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .basic-info-section {
    flex-direction: column;
  }

  .risk-buttons,
  .data-source {
    position: static;
    margin-top: 15px;
  }

  .info-grid-detailed,
  .sensitive-info-grid,
  .file-info-grid {
    flex-direction: column;
  }
}
</style>
