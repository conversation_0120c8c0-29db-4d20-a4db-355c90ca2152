<template>
  <div>
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <splitpane :splitSet="settingLR">
      <!-- 左侧面板 -->
      <template #paneL>
        <!-- 使用el-tabs组件，设置默认激活的标签页 -->
        <el-tabs v-model="activeTabName" class="ml-2 mr-2" @tab-change="viewTabChangeHandler">
          <!-- 组织视图标签页 (已隐藏)
          <el-tab-pane name="deptView">
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-TeamFill" />
                <span class="ml-1">组织视图</span>
              </div>
            </template>
<div>
  <div class="pb-1.5 flex items-center">
    <el-input placeholder="组织名称" :suffix-icon="useRenderIcon('EP-Search')" clearable v-model="deptKeyWord" />
    <el-icon @click="queryDeptTree" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
      <Refresh></Refresh>
    </el-icon>
  </div>
  <el-tree ref="deptTree" :data="state.deptData" :props="treeProps" node-key="deptId" :render-after-expand="false"
    :expand-on-click-node="false" highlight-current :default-expanded-keys="expendTreeOrg"
    :filter-node-method="filterDeptNode" :style="treeStyle" @current-change="deptSelectChange">
    <template #default="{ node, data }">
                  <span :style="{
                    color: (data.vulCount || 0) > 0 ? 'red' : 'unset'
                  }">{{ node.label }}</span>
                </template>
  </el-tree>
</div>
</el-tab-pane>
-->
          <!-- 应用视图标签页 -->
          <el-tab-pane name="appView">
            <!-- 设置标签页的标题 -->
            <template #label>
              <div class="flex-c">
                <IconifyIconOffline icon="RI-AppsFill" />
                <span class="ml-1">应用视图</span>
              </div>
            </template>
            <div>
              <!-- 输入框，用于搜索应用 -->
              <div class="pb-1.5 flex items-center">
                <el-input placeholder="应用名称" :suffix-icon="useRenderIcon('EP-Search')" clearable v-model="appKeyWord" />
                <el-icon @click="loadAppData(false)" style="
                    cursor: pointer;
                    margin-left: 5px;
                    color: var(--el-color-primary);
                  ">
                  <Refresh></Refresh>
                </el-icon>
              </div>
              <!-- 树形控件，用于展示应用结构 -->
              <el-tree ref="appTreeRef" :data="appData" node-key="value" :expand-on-click-node="false" :props="{
                label: 'label'
              }" highlight-current default-expand-all :filter-node-method="filterAppNode" :style="treeStyle"
                @current-change="appChangeHandler">
                <template #default="{ node, data }">
                  <span :style="{
                    color: (data.count || 0) > 0 ? 'red' : 'unset'
                  }">{{ node.label
                  }}<!--({{ data.count || 0 }})--></span>
                </template>
              </el-tree>
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 右侧面板 -->
      <template #paneR>
        <!-- 使用search-with-column组件，用于搜索事件数据 -->
        <div class="flex justify-between items-center mb-2 mr-6">
          <search-with-column v-model="columnCondition.value" v-model:fuzzy-enable="columnCondition.fuzzyable"
            v-model:column-val="columnCondition.field" :column-options="columnSearchOptions" :column-select-width="116"
            @search="resetTablePageAndQuery('', 'toggleQueryCriteria')" @reset="resetSearchHandler"
            class="flex-c w-3/4 ml-10" input-class-name="w-1/2" />

          <!-- 右侧按钮组 -->
          <div class="flex items-center gap-2">
            <!-- 刷新按钮 -->
            <el-button type="primary" :icon="RefreshIcon" @click="refreshTable">刷新</el-button>

            <!-- 导出按钮 -->
            <el-button type="success" :icon="DownloadIcon" @click="exportEventHandler">导出</el-button>

            <!-- 刷新间隔下拉菜单 -->
            <el-select class="ml-2" v-model="refreshInterval" placeholder="刷新间隔" style="width: 120px"
              @change="handleRefreshIntervalChange">
              <el-option v-for="item in refreshIntervalOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </el-select>
          </div>
        </div>
        <!-- 风险类型 -->
        <div class="flex mt-3 mb-2">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">风险类型:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag class="mr-2 tag-container" :checked="state.riskTypeCheckAll"
                @change="riskTypeChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in state.riskTypeData" :key="item.value"
                :checked="!state.riskTypeCheckAll && item.checked" class="mr-2" @change="riskTypeChangeHandler(item)">
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 风险类型下的虚线分割线 -->
        <div class="dashed-divider mx-4 my-1 pr-6"></div>

        <!-- 风险等级 -->
        <div class="flex mt-2 mb-2">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">风险等级:</span>
          <div style="padding-bottom: 4px">
            <div>
              <el-check-tag class="mr-2 tag-container" :checked="state.riskLevelCheckAll"
                @change="riskLevelChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in state.riskLevelData" :key="item.value"
                :checked="!state.riskLevelCheckAll && item.checked" class="mr-2" @change="riskLevelChangeHandler(item)">
                {{ item.label }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 虚线分割线 -->
        <div class="dashed-divider mx-4 my-1 pr-3"></div>

        <!-- 触发规则名称 -->
        <div ref="vulTagRef" class="flex mt-2 mb-3">
          <span style="
              width: 6em;
              opacity: 0.75;
              white-space: nowrap;
              padding-left: 0.6rem;
              margin-right: 33px;
              text-align: right;
            ">触发规则名称:</span>
          <div style="padding-bottom: 4px">
            <div ref="innerRef">
              <el-check-tag class="mr-2 tag-container" :checked="state.checkAll" @change="tagChangeHandler(null)">全部
              </el-check-tag>
              <el-check-tag style="margin-bottom: 6px" v-for="item in eventTagData" :key="item.tagId"
                :checked="!state.checkAll && item.checked" class="mr-2" @change="tagChangeHandler(item)">
                {{ item.tagName }}
              </el-check-tag>
            </div>
          </div>
        </div>

        <!-- 处理状态标签 -->
        <div class="mt-2 mb-6 pl-3 pr-3">
          <el-tabs v-model="searchCondition.dealStatus" @tab-change="dealStatusChangeHandler" class="deal-status-tabs">
            <el-tab-pane name="undisposed">
              <template #label>
                <div class="flex items-center">
                  <span>未处置</span>
                  <!-- <el-badge v-if="state.vulnerabilityStatisticsData[1].value > 0"
                    :value="state.vulnerabilityStatisticsData[1].value" class="ml-1" /> -->
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane name="disposed">
              <template #label>
                <div class="flex items-center">
                  <span>已处置</span>
                  <!-- <el-badge v-if="state.vulnerabilityStatisticsData[2].value > 0"
                    :value="state.vulnerabilityStatisticsData[2].value" class="ml-1" /> -->
                </div>
              </template>
            </el-tab-pane>
            <el-tab-pane name="nodisposed">
              <template #label>
                <div class="flex items-center">
                  <span>无需处置</span>
                  <!-- <el-badge v-if="state.vulnerabilityStatisticsData[3].value > 0"
                    :value="state.vulnerabilityStatisticsData[3].value" class="ml-1" /> -->
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 表格 -->
        <div class="ml-2 mr-2">
          <im-table ref="tableRef" :data="tableData" center toolbar :table-alert="{
            closable: false
          }" operator :height="tableOption.height" :stripe="tableOption.stripe" show-checkbox
            :column-storage="createColumnStorage('api-risk-management', 'remote')" :pagination="tablePage"
            :loading="tableLoading" :filter-data-provider="filterDataProvider" @on-reload="resetTablePageAndQuery"
            @selection-change="selectionChangeHandler" @on-page-change="queryEventData">
            <!-- 自定义 alert 提示内容 -->
            <template #tip>
              <!-- <span class="statistics-tip" style="margin-left: 100px">
                <span class="statistics-item mr-2 ml-2" v-for="(item, index) in vulnerabilityStatisticsData"
                  :key="index">
                  <span class="label">{{ item.label }}: </span>
                  <span class="count text-[16px] font-bold ml-1">{{ item.value || 0 }}</span>
                </span>
              </span> -->
            </template>

            <!-- 表格右侧菜单 -->
            <template #toolbar-right="{ size }">
              <div class="float-left flex-sc pr-3 gap-3">
                <el-segmented v-model="state.dateRangeSign" :options="state.timeSegmentOptions" @change="
                  () => {
                    dateTimeRange = [];
                    resetTablePageAndQuery();
                  }
                ">
                </el-segmented>
                <!-- 日期选择器，用于选择事件时间范围 -->
                <el-date-picker clearable v-model="dateTimeRange" type="daterange" range-separator="到"
                  start-placeholder="开始日期" end-placeholder="结束日期" style="width: 200px" @change="dateChangeFunction" />

                <!-- 资产类别 -->
                <!-- <el-tree-select clearable v-model="searchCondition.assetTypeName" placeholder="资产类别"
                  :data="assetTypeName" check-strictly :render-after-expand="false" style="width: 160px"
                  node-key="id" @change="resetTablePageAndQuery" /> -->

                <!-- 处置状态 -->
                <!-- <el-select v-model="searchCondition.dealStatus" placeholder="处置状态" clearable collapse-tags
                  style="width: 160px" @change="resetTablePageAndQuery">
                  <el-option v-for="item in state.dsSelData" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select> -->
                <!-- 风险级别选择器，用于选择事件风险级别 -->
                <!-- <el-select v-model="searchCondition.reseverity" placeholder="漏洞级别" multiple collapse-tags clearable
                  style="width: 150px" @change="riskLevelChangeHandler">
                  <el-option v-for="item in riskLevelData" :key="item.id" :label="item.label" :value="item.id" />
                </el-select> -->

                <!-- <el-dropdown style="margin: 0 10px">
                  <el-button type="primary"> 批量操作 </el-button>
                  <template #dropdown>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :disabled="state.selectedEventRows.length == 0" style="padding: 0.4rem 1rem"
                        @click.native="
                          handleCommand('批量派单', '批量派单')
                          ">
                        批量派单
                      </el-dropdown-item>
                      <el-dropdown-item style="padding: 0.4rem 1rem" :disabled="state.selectedEventRows.length == 0"
                        @click="
                          openEditTaskDialog({
                            operationDataList: state.selectedEvents
                          })
                          " size="small" type="primary">批量验证</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
    </el-dropdown> -->

                <!-- 导出事件数据 -->
                <!-- <el-tooltip content="导出数据" placement="top" :open-delay="1000">
                  <el-button style="margin-left: 0.1rem" :icon="useRenderIcon('EP-Download')" circle :size="size"
                    :disabled="tableData.length == 0" @click="exportEventHandler" />
                </el-tooltip> -->
              </div>
            </template>
            <!-- 表格操作按钮 -->
            <template #operator="{ row, size }">
              <el-button :size="size" type="primary" text :icon="useRenderIcon('EP-Checked')"
                @click="handleCommand(row)">
                处置
              </el-button>
              <!-- 详情按钮 -->
              <el-button :size="size" type="primary" text :icon="useRenderIcon('EP-View')"
                @click="detailViewHandler(row)">
                详情
              </el-button>
            </template>
            <!-- 风险级别列 -->
            <template #riskLevel="{ row }">
              <el-tag :color="getRiskLevelColor(row.riskLevel + '')" class="text-white border-none">
                {{ getRiskLevelLabel(row.riskLevel + "") }}
              </el-tag>
            </template>
            <!-- 事件处理状态列 -->
            <template #dealStatus="{ row }">
              <el-tag effect="light" :type="getDealStatusType(row.dealStatus)">
                {{ getSegmentLabel(row.dealStatus) }}
              </el-tag>
            </template>

            <template #event_subtype_name="{ row }">
              <el-tag>{{ row.event_subtype_name }}</el-tag>
            </template>
          </im-table>
        </div>
      </template>
    </splitpane>

    <el-dialog title="处置" width="33%" destroy-on-close append-to-body v-model="showHandle">
      <Disposal v-if="showHandle" @closeDraw="closeDraw"
        :batchOperationModelData="batchOperationRowsData" />
    </el-dialog>
    <!-- <EventDispatchModal @success="queryEventData" :form="eventDispatchModalForm" :modelValue="eventDispatchModalVisable"
      @update:modelValue="eventDispatchModalVisable = false" /> -->
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  toRefs,
  watch,
  h,
  CSSProperties,
  markRaw
} from "vue";
import dayjs from "dayjs";
import { Refresh, Download } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import Disposal from "@/views/modules/security/apiRiskManagement/components/Disposal.vue";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumnAddSelect.vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData,
  segmentData
} from "@/views/modules/security/apiRiskManagement/util/vulnerability_data";

import {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  getBusinessSystemData,
  getUserDeptTreeAxios,
  queryAllCategoryAxios,
  queryEventDeptTree,
  getTagData,
  vulTotalByStatus,
  vulAddNewTaskForVulMethod,
  queryApiEventAssetGroup
} from "@/views/modules/security/apiRiskManagement/api/vulnerabilityHomeInterface";
import { useRoute } from "vue-router";
const route = useRoute();
const tableRef = ref<ImTableInstance>();
import { ElScrollbar } from "element-plus";

// 刷新按钮图标
const RefreshIcon = markRaw(Refresh);
// 导出按钮图标
const DownloadIcon = markRaw(Download);

// 刷新间隔定时器
const refreshTimer = ref<number | null>(null);
// 刷新间隔（毫秒）
const refreshInterval = ref<number | string>("noRefresh");

// 刷新间隔选项
const refreshIntervalOptions = [
  {
    label: "不刷新",
    value: "noRefresh"
  },
  {
    label: "10秒",
    value: 10000
  },
  {
    label: "1分钟",
    value: 60000
  },
  {
    label: "10分钟",
    value: 600000
  },
  {
    label: "30分钟",
    value: 1800000
  }
];
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import { queryEventTableHeadGroup } from "@/views/modules/security/event/api/SecurityEventApi";
const showHandle = ref(false);
const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree> | null>(null);
const appTreeRef = ref<InstanceType<typeof ElTree> | null>(null);

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

// 移除了视角切换，只保留资产视角

const deptNameData = ref<any[]>([]);

const assetTypeName = ref<any[]>([]);

const assetPhoto = ref(
  `data:image/svg+xml;base64,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`
);
// 移除了漏洞视角图标

/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};



// 打开编辑任务窗口

const openEditTaskDialog = (t: any) => {
  // console.log(t);
  vulAddNewTaskForVulMethod(t)
    .then(res => {
      $message({
        type: "success",
        message: "开始验证"
      });
      resetTablePageAndQuery();
    })
    .catch(err => {
      $message({
        type: "error",
        message: "验证失败"
      });
    });

};

// 处理树形选择器数据
const buildTree = (
  data: any[],
  deptId: string | number,
  parentId: string | number
) => {
  const map = new Map();
  const rootNodes = [];
  // 首先将所有节点添加到映射中
  data.forEach(item => {
    map.set(item[deptId], { ...item, label: item["name"], children: [] });
  });
  // 然后根据parentId构建树形结构
  data.forEach(item => {
    if (item[parentId] == "-1" || item[parentId] == null) {
      rootNodes.push(map.get(item[deptId]));
    } else {
      if (map.has(item[parentId])) {
        map.get(item[parentId]).children.push(map.get(item[deptId]));
      } else {
        // 如果找不到parentId，则将该节点作为顶级父节点
        rootNodes.push(map.get(item[deptId]));
      }
    }
  });

  return rootNodes;
};
// 资产类别
// queryAllCategoryAxios
const queryAllCategoryAxiosMethod = async () => {
  const res = await queryAllCategoryAxios();
  // console.log(buildTree(res["data"], "id", "parentId"));

  assetTypeName.value = [...buildTree(res["data"], "id", "parentId")];
};
queryAllCategoryAxiosMethod();

const expendTreeOrg = ref([]);
const queryDeptTree = () => {
  state.deptData = [];
  // 清空关键字
  state.deptKeyWord = null;
  queryEventDeptTree().then(res => {
    // console.log("queryEventDeptTree", res.data);
    const resDeptData = Array.isArray(res.data)
      ? [...buildTree(res.data, "deptId", "parentId")]
      : [res.data || {}];
    state.deptData = [
      {
        deptiD: "",
        deptName: "全部",
        children: resDeptData
      }
    ];

    expendTreeOrg.value.splice(
      0,
      expendTreeOrg.value.push.length,
      ...["", "0", "-1"]
    );

    deptTree.value.setCurrentKey(null);
    deptSelectChange({ deptId: "" });
    queryEventData();
  });
};

//首层组织数据 计算属性
const firstLevelDeptData = computed(() => {
  return parseFirstLevelDept(
    useDeptStoreHook()?.deptData.filter(
      (d: SimpleDeptInfo) => d.parentId === "-1"
    )
  );
});

//数据对象
interface StateType {
  checkAll: boolean;
  totalTagCount: number;
  activeTabName: string;
  tableLoading: boolean;
  deptKeyWord: string | null;
  deptData: any[];
  appKeyWord: string | null;
  appData: any[];
  eventTagData: any[];
  columnCondition: {
    value: string | null;
    field: string;
    fuzzyable: boolean;
    operator: string;
  };
  dateRangeSign: string;
  timeSegmentOptions: Array<{ label: string; value: string }>;
  dateTimeRange: any[];
  searchCondition: {
    orgId: string;
    asset_app_name: string;
    dealStatus: string;
    riskType: string;
    riskLevel: string;
    [key: string]: any;
  };
  riskTypeCheckAll: boolean;
  riskLevelCheckAll: boolean;
  riskTypeData: Array<{ label: string; value: string; checked: boolean }>;
  riskLevelData: Array<{ label: string; value: string; checked: boolean }>;
  columnSearchOptions: any[];
  tableData: any[];
  assetByIpMap: Record<string, any>;
  tablePage: {
    align: string;
    total: number;
    currentPage: number;
    pageSize: number;
    pageSizes: number[];
  };
  deal: {
    visible: boolean;
    title: string;
    unitIds: any[];
    defaultAction: any;
  };
  selectedEvents: string[];
  selectedEventRows: any[];
  ipBlock: {
    visible: boolean;
    ipAddress: string;
    defaultPlugLabel: string;
  };
  dsSelData: Array<{ value: string; label: string }>;
  vulnerabilityStatisticsData: Array<{ label: string; code: string; value: number }>;
  filters: any[];
}

const state = reactive<StateType>({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "appView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "apiName",
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateRangeSign: "30d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    },
    {
      label: "近6月",
      value: "6m"
    },
    {
      label: "近1年",
      value: "1y"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    orgId: "",
    asset_app_name: "",
    dealStatus: "undisposed",
    riskType: "",
    riskLevel: ""
  },

  riskTypeCheckAll: true,
  riskLevelCheckAll: true,

  riskTypeData: [],

  riskLevelData: [
    {
      label: "高风险",
      value: "3",
      checked: false
    },
    {
      label: "中风险",
      value: "2",
      checked: false
    },
    {
      label: "低风险",
      value: "1",
      checked: false
    }
  ],

  columnSearchOptions: [],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: [
    {
      value: "undisposed",
      label: "未处置"
    },
    {
      value: "disposed",
      label: "已处置"
    },
    {
      value: "nodisposed",
      label: "无需处置"
    }
  ],

  vulnerabilityStatisticsData: [
    {
      label: "风险总数",
      code: "total",
      value: 0
    },
    {
      label: "未处置数",
      code: "undisposed",
      value: 0
    },
    {
      label: "已处置数",
      code: "disposed",
      value: 0
    },
    {
      label: "无需处置数",
      code: "nodisposed",
      value: 0
    }
  ],

  filters: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  vulnerabilityStatisticsData,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const eventDispatchContext = reactive({
  dispatchVisible: false,
  dispatchForm: {
    alertIds: "",
    userId: [],
    title: "",
    deptId: ""
  }
});

// 移除了视角切换相关的监听逻辑
const initColumnOptions = () => {
  state.columnSearchOptions = [];
  state.columnCondition.value = "";
  state.columnCondition.field = "apiName";
  queryEventData();
};

const searchQuery = (val: any) => {
  state.columnSearchOptions = val;
  state.columnCondition.field = val[0]["value"];
};



watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

// 动态高度
const tmpWidth = ref(0);
const vulTagRef = ref<HTMLElement | null>(null);
const innerRef = ref<HTMLElement | null>(null); // 添加对内部标签容器的引用
const tableKey = ref(0);
const vulTagHeight = (skipQuery = false) => {
  nextTick(() => {
    nextTick(() => {
      // 获取触发规则名称部分的高度
      if (!vulTagRef.value) return;

      // 使用 innerRef 获取实际标签容器的高度，这样可以正确计算多行标签的高度
      const actualTagHeight = innerRef.value ? innerRef.value.offsetHeight : 0;
      const vulTagHeight = vulTagRef.value.offsetHeight;

      // 确保我们使用的是实际内容的高度，而不仅仅是容器的高度
      const effectiveHeight = Math.max(vulTagHeight, actualTagHeight + 30); // 添加一些额外空间

      // 获取风险类型和风险等级部分的高度
      // 由于我们减小了间距，每个部分大约高度为60px，所以总高度需要加上120px
      const additionalHeight = 120; // 风险类型和风险等级的高度

      // 计算总高度
      tmpWidth.value = effectiveHeight + additionalHeight;
      // console.log("总高度:", tmpWidth.value);

      // 计算表格高度
      tableHeight.value =
        document.documentElement.offsetHeight - 350 - tmpWidth.value; // 减小了固定偏移量
      tableOption.height = tableHeight.value;

      // console.log("标签实际高度:", actualTagHeight, "表格高度:", tableOption.height);

      // 只有在非跳过查询模式下，且tableKey为0时才查询数据
      if (tableKey.value == 0 && !skipQuery) {
        queryEventData();
      }
      tableKey.value = tableKey.value + 1;
    });
  });
};


//根据页面高度设置表格高度
// const tableHeight = computed(() => {
//   // console.log(document.documentElement.offsetHeight - 400);
//   return document.documentElement.offsetHeight - 400 - tmpWidth.value;
// });
const tableHeight = ref(0);


interface TableColumnType {
  hide: boolean;
  prop: string;
  label: string;
  fieldType: string;
  width: string | number;
  unit?: string;
  component?: any;
  filters?: boolean;
  sortable?: boolean;
  [key: string]: any;
}

interface TableOptionType {
  align: string;
  menuAlign: string;
  border: boolean;
  stripe: boolean;
  height?: number;
  rowKey: string;
  column: TableColumnType[];
  [key: string]: any;
}

const tableOption = reactive<TableOptionType>({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: tableHeight.value,
  rowKey: "ip",
  column: []
});

const dateChangeFunction = (query: any) => {
  state.dateRangeSign = "";
  if (!query) {
    state.dateRangeSign = "30d";
  }
  resetTablePageAndQuery();
};

//加载组织树节点数据


const treeStyle = computed((): CSSProperties => {
  return {
    height: tableHeight.value + 80 + "px",
    overflow: "auto"
  };
});

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (!data.deptId) {
    return true;
  }

  return data.deptName.indexOf(value) > -1;
};

//应用数据搜索
const filterAppNode = (value: string, data: any) => {
  if (!value) return true;

  // 不确定组件是否有bug，根节点直接返回true
  if (data.value == "全部") return true;

  return data.value.indexOf(value) > -1;
};

//组织树选择改变触发
const deptSelectChange = (data: any) => {
  if (data && data.deptId) {
    state.searchCondition.orgId = data.deptId;
  } else {
    state.searchCondition.orgId = "";
  }

  triggerVulViewTable["orgId"] = state.searchCondition.orgId;
  triggerVulViewTable["assetApp"] = "";
  triggerVulViewTable["vulType"] = "";
  state.searchCondition["event_type_tag"] = "";
  state.checkAll = true;
  resetTablePageAndQuery();
};

//应用选择改变
const appChangeHandler = (data: any) => {
  if (!data) {
    // console.log("aaaaaaaaaa")
    if (appTreeRef.value) {
      appTreeRef.value.setCurrentKey("");
    }
  }

  state.searchCondition.asset_app_name = data?.value;
  if (state.searchCondition.asset_app_name == "全部") {
    state.searchCondition.asset_app_name = "";
  }

  triggerVulViewTable["assetApp"] = state.searchCondition.asset_app_name;
  triggerVulViewTable["orgId"] = "";
  triggerVulViewTable["vulType"] = "";
  // 不再重置触发规则名称
  // state.searchCondition["event_type_tag"] = "";
  // state.checkAll = true;
  resetTablePageAndQuery();
};

//视图标签改变触发 (组织视图已隐藏)
const viewTabChangeHandler = (activeName: string) => {
  // console.log(activeName);
  /* 组织视图已隐藏
  if (activeName === "deptView") {
    //处理组织视图初始化
    state.searchCondition.asset_app_name = "";
    state.searchCondition.orgId = "";
    nextTick(() => {
      if ("-1" == deptTree.value!.getCurrentKey()) {
        state.searchCondition.orgId = "-1";
        resetTablePageAndQuery();
      }
      deptTree.value!.setCurrentKey("-1");
      queryDeptTree();
    });
    // loadAppData();
    resetTablePageAndQuery();
  } else */
  if (activeName === "appView") {
    //处理应用视图初始化
    state.searchCondition.asset_app_name = "";
    state.searchCondition.orgId = "";
    // state.deptData = [];
    if (state.appData && state.appData.length > 0) {
      const firstData = state.appData[0];
      nextTick(() => {
        if (firstData.value == appTreeRef.value!.getCurrentKey()) {
          state.searchCondition.asset_app_name = firstData.value;
          resetTablePageAndQuery();
        }
        appTreeRef.value!.setCurrentKey(firstData.value);
      });
    }
  }
  // 注意：在视图标签切换时不重置触发规则名称
};

//加载业务系统数据
const loadAppData = async (skipQuery = false) => {
  // console.log("aaa")
  const appRes = await getBusinessSystemData({
    "conditions": [],
    "model": "",
    "field": "appName",
    "headerFilter": {
      "filters": [
      ]
    }
  });

  // 新的数据格式中，options 数组包含 {value, label, count} 结构
  const options = appRes.data?.options || [];

  state.appData = [
    {
      value: "全部",
      label: "全部",
      children: options
    }
  ];
  // console.log(state.appData)
  if (!skipQuery) {
    // 保存当前的触发规则名称状态
    const currentEventTypeTag = state.searchCondition["event_type_tag"];
    const currentCheckAll = state.checkAll;

    // 调用应用选择改变函数
    appChangeHandler(null);

    // 恢复触发规则名称状态
    state.searchCondition["event_type_tag"] = currentEventTypeTag;
    state.checkAll = currentCheckAll;
  } else {
    // 只设置选中状态，不触发查询
    if (appTreeRef.value) {
      appTreeRef.value.setCurrentKey("全部");
    }
    state.searchCondition.asset_app_name = "";
  }
};

const triggerVulViewTable = reactive({
  number: "1",
  query: {},
  orgId: "",
  assetApp: "",
  vulType: "",
  toggleQueryCriteria: ""
});
//重置分页后查询事件数据
const resetTablePageAndQuery = (
  info = "",
  toggleQueryCriteria = "",
  keepFiltersFlag?: boolean
) => {
  // 移除了视角切换相关的日志
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
    state.filters = [];
    tableRef.value.clearAllFilters();
  }
  state.tablePage.currentPage = 1;
  queryEventData();
};

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange: string | string[] | null;
  if (state?.dateRangeSign) {
    dateRange = state?.dateRangeSign;
  } else {
    if (state?.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state?.dateTimeRange?.[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state?.dateTimeRange?.[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

const searchTmpData = (tmpConditions: any[]) => {

  try {
    if (state?.searchCondition?.["deptName"]) {
      tmpConditions.push({
        field: "refOrgNameTree",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["deptName"]
      });
    }
  } catch (error) {

  }
  try {
    if (state?.searchCondition?.["assetTypeName"]) {
      tmpConditions.push({
        field: "assetTypeName",
        fuzzyable: columnCondition.value.fuzzyable,
        value: state.searchCondition["assetTypeName"]
      });
    }
  } catch (error) {

  }

};

// 查询漏洞统计数据
const queryVulnerabilityStatisticsData = (queryParams: any) => {
  vulTotalByStatus(queryParams).then((res: any) => {
    let resData = res.data || {};
    for (let item of state.vulnerabilityStatisticsData) {
      let { code } = item;
      item.value = resData[code] || 0;
    }
  });
};

const buildQueryCondition = () => {
  const tmpConditions = [];
  searchTmpData(tmpConditions);
  // 添加处置状态条件
  if (state?.searchCondition?.dealStatus) {
    tmpConditions.push({
      field: "dealStatus",
      fuzzyable: false,
      operator: "exact",
      value: state.searchCondition.dealStatus
    });
  }

  // 添加风险等级条件
  if (state?.searchCondition?.riskLevel) {
    tmpConditions.push({
      field: "riskLevel",
      fuzzyable: false,
      operator: "exact",
      value: state.searchCondition.riskLevel
    });
  }

  // 添加风险类型条件
  if (state?.searchCondition?.riskType) {
    tmpConditions.push({
      field: "riskType",
      fuzzyable: false,
      operator: "exact",
      value: state.searchCondition.riskType
    });
  }

  // 添加触发规则名称条件
  if (state?.searchCondition?.ruleName) {
    tmpConditions.push({
      field: "ruleNames",
      fuzzyable: true,
      operator: "fuzzy",
      value: state.searchCondition.ruleName
    });
  }

  // 添加应用名称条件
  if (state?.searchCondition?.asset_app_name) {
    tmpConditions.push({
      field: 'appName',
      fuzzyable: false,
      operator: "exact",
      value: state.searchCondition.asset_app_name
    });
  }

  return {
    //查询条件
    conditions: state?.columnCondition.value
      ? [state?.columnCondition, ...tmpConditions]
      : [...tmpConditions],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    // ...state.searchCondition,
    // 不再单独传递处置状态，已添加到conditions中
    //当前页码
    pageNum: state?.tablePage?.currentPage,
    //每页显示条数
    pageSize: state?.tablePage?.pageSize,
    // orgId: state?.searchCondition?.orgId,
    // assetApp: state?.searchCondition?.asset_app_name,
    // vulLevel:
    //   state?.searchCondition?.["reseverity"] &&
    //   state?.searchCondition?.["reseverity"].join(","),
    // vulType:
    //   state?.searchCondition?.["event_type_tag"] &&
    //   state?.searchCondition?.["event_type_tag"]?.tagName,
    // 风险类型
    // riskType: state?.searchCondition?.riskType || null,
    // 风险等级不再单独传递，已添加到conditions中

    // 视角标识
    // viewType: "asset",
    // 列头过滤器
    headerFilter: {
      filters: state?.filters
    }
  };
};

//查询事件数据
const queryEventData = async () => {
  // 防止重复调用
  if (state.tableLoading) return;

  state.selectedEventRows = [];
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    // 查询条件
    const queryParams = buildQueryCondition();
    // console.log("queryParams=========================>", queryParams);

    // 查询统计数据(异步)
    // queryVulnerabilityStatisticsData(queryParams);

    //根据条件查询事件列表
    const res = await vulDetailByAssetMethod(queryParams);
    //设置表格数据
    state.tableData = res["data"].list || [];

    // 不再需要处理动态表头，因为我们使用了固定的表头配置

    // 设置默认的搜索字段
    interface QuerySearchItem {
      value: string;
      label: string;
      type: string;
    }

    const tmpQuerySearch: QuerySearchItem[] = [
      { value: "apiName", label: "触发规则名称", type: "Input" },
      { value: "clientIp", label: "风险主体 (IP)", type: "Input" },
      { value: "appName", label: "应用名称", type: "Input" }
    ];
    columnSearchOptions.value = tmpQuerySearch;

    // 查询关联的资产
    // queryAssetData(state.tableData);
    //设置表格总条数
    state.tablePage.total = res["data"].total || 0;
    //加载标签数据，但跳过高度计算以避免循环调用
    await loadEventTagData(true);
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};



//加载标签数据
const loadEventTagData = async (skipHeightCalc = false) => {
  dealFuzzEnable();
  //构建查询条件
  const condition = {
    // //查询条件
    // conditions: state.columnCondition.value ? [state.columnCondition] : [],
    // //日期范围
    // dateRange: computedDateRange.value,
    // //搜索条件
    // ...state.searchCondition

    assetApp: state.searchCondition.asset_app_name,
    orgId: state.searchCondition.orgId
  };
  // condition.event_type_tag = "";

  //查询数据
  const tagRes = await getTagData(condition);

  // 处理新的数据格式
  const resData = (tagRes as any).data || [];

  // 将新的数据格式转换为标签格式
  const formattedData = resData.map((item: any) => ({
    tagId: item.ruleId,
    tagName: item.ruleName,
    tagCount: 0,
    checked: false
  }));

  //处理标签已选中状态
  if (state.searchCondition["event_type_tag"]) {
    //查找已选中的标签
    const matchData = formattedData.find(
      (item: any) =>
        item.tagId === state.searchCondition?.["event_type_tag"].tagId
    );
    //设置标签已选中状态
    if (matchData) {
      matchData.checked = true;
    }
  }
  //设置标签数据
  state.eventTagData = formattedData;

  // 设置标签数据后，等待DOM更新，然后重新计算高度
  nextTick(() => {
    // 只有在非跳过高度计算模式下才计算高度
    if (!skipHeightCalc) {
      vulTagHeight(true); // 传入true表示跳过查询
    }
  });

  // 全部数量 - 由于新格式没有 tagCount，我们不再计算总数
  state.totalTagCount = formattedData.length;
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

//触发规则标签选中改变
const tagChangeHandler = (tag: any) => {
  // console.log(tag);
  state.searchCondition["event_type_tag"] = tag ? tag : "";

  // 如果选中了标签，将其添加到查询条件中
  if (tag) {
    // 将触发规则名称添加到查询条件中
    state.searchCondition["ruleName"] = tag.tagName;
  } else {
    // 如果选中了“全部”，则清除触发规则名称条件
    state.searchCondition["ruleName"] = "";
  }

  // 不再使用 vulType
  // triggerVulViewTable["vulType"] =
  //   state.searchCondition["event_type_tag"]?.tagName;
  state.checkAll = !tag;
  state.eventTagData.forEach((item: any) => {
    item.checked = item.tagId === tag?.tagId;
  });
  resetTablePageAndQuery();
};

//风险类型标签选中改变
const riskTypeChangeHandler = (tag: any) => {
  // console.log(tag);
  state.searchCondition["riskType"] = tag ? tag.value : "";
  state.riskTypeCheckAll = !tag;
  state.riskTypeData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });
  resetTablePageAndQuery();
};

//风险等级标签选中改变
const riskLevelChangeHandler = (tag: any) => {
  // console.log(tag);
  state.searchCondition["riskLevel"] = tag ? tag.value : "";
  state.riskLevelCheckAll = !tag;
  state.riskLevelData.forEach((item: any) => {
    item.checked = item.value === tag?.value;
  });
  resetTablePageAndQuery();
};

//处理状态变更处理函数
const dealStatusChangeHandler = () => {
  // console.log("处理状态变更为:", searchCondition.value.dealStatus);
  resetTablePageAndQuery();
};

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "apiName",
    fuzzyable: true,
    operator: "fuzzy"
  };
  //重置标签选择状态
  state.eventTagData.forEach((item: any) => (item.checked = false));
  state.searchCondition["event_type_tag"] = null;
  state.searchCondition["ruleName"] = "";
  // 不再使用 vulType
  // triggerVulViewTable["vulType"] = "";
  state.checkAll = true;
  //重置处置状态
  state.searchCondition["dealWith"] = "1";
  //重置时间范围
  state.dateTimeRange = [];
  state.dateRangeSign = "30d";
  //重置风险级别
  state.searchCondition["reseverity"] = "";
  state.searchCondition["deptName"] = "";
  state.searchCondition["assetTypeName"] = "";
  //重置处理状态
  state.searchCondition["dealStatus"] = "undisposed";
  //重置风险类型
  state.searchCondition["riskType"] = "";
  state.riskTypeCheckAll = true;
  state.riskTypeData.forEach((item: any) => (item.checked = false));
  //重置风险等级
  state.searchCondition["riskLevel"] = "";
  state.riskLevelCheckAll = true;
  state.riskLevelData.forEach((item: any) => (item.checked = false));
  resetTablePageAndQuery("漏洞重置", "toggleQueryCriteria");
};

//查看事件详情触发
const detailViewHandler = (evt: any) => {
  // 将当前行数据传递给事件选择处理函数
  emit("event-select", evt);

  // 创建需要传递的数据对象
  let needsPassedData = {
    // 基本查询条件
    timeSelect: state.dateRangeSign,
    dealStatus: evt?.["dealStatus"] || state.searchCondition["dealStatus"],
    reseverity: state.searchCondition["reseverity"],

    // 将当前行的完整数据传递给详情组件
    rowData: evt
  };

  // 跳转到详情页面
  jumpTo("apiAlertInfo", needsPassedData);
};

//事件处置触发-单条
const eventDealHandler = (evt: any) => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "";
  state.deal.unitIds = [evt.ip];
  state.deal.visible = true;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds: string[] = [];
  selRows.forEach(row => selectIds.push(row.ip));
  state.selectedEvents = selectIds;
  state.selectedEventRows = selRows;
};



//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });

    // 直接使用与查询表格数据相同的参数
    // 调用buildQueryCondition函数构建查询条件
    const queryParams = buildQueryCondition();

    // 将分页参数设置为导出所有数据
    // 导出时不需要分页，设置足够大的pageSize来导出所有数据
    // queryParams.pageNum = 1;
    // queryParams.pageSize = 10000; // 设置一个足够大的值来导出所有数据

    // 调用导出接口
    await importVulDetailByAssetMethod(queryParams);
  });
};



// 刷新表格
const refreshTable = () => {
  queryEventData();
};

// 处理刷新间隔变化
const handleRefreshIntervalChange = () => {
  // 清除之前的定时器
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }

  // 如果选择了自动刷新，则设置定时器
  if (refreshInterval.value !== "noRefresh") {
    refreshTimer.value = window.setInterval(() => {
      refreshTable();
    }, refreshInterval.value as number);
  }
};

//初始化应用视图
const initAppView = (skipQuery = false) => {
  //处理应用视图初始化
  state.searchCondition.asset_app_name = "";
  state.searchCondition.orgId = "";
  if (state.appData && state.appData.length > 0) {
    const firstData = state.appData[0];
    nextTick(() => {
      if (firstData.value == appTreeRef.value?.getCurrentKey() && !skipQuery) {
        state.searchCondition.asset_app_name = firstData.value;
        resetTablePageAndQuery();
      }
      appTreeRef.value?.setCurrentKey(firstData.value);
    });
  }
};

//加载风险类型数据
const loadRiskTypeData = async () => {
  const riskTypeRes = await getBusinessSystemData({
    "conditions": [],
    "model": "",
    "field": "riskType",
    "headerFilter": {
      "filters": []
    }
  });

  // 新的数据格式中，options 数组包含 {value, label, count} 结构
  const options = riskTypeRes.data?.options || [];

  // 将选项转换为风险类型数据格式
  state.riskTypeData = options.map((option: { value: string, label: string, count: number }) => ({
    label: option.label,
    value: option.value,
    checked: false
  }));
};

//挂载后初始化
onMounted(() => {
  // 接收路由传递的参数，这里简单处理下
  if (route.query.dateRange == "") {
    state.dateRangeSign = "";
  } else {
    state.dateRangeSign = (route.query.dateRange || "30d") as string;
  }

  // 初始化流程：先加载数据，然后只在最后调用一次查询
  const initializeComponent = async () => {
    try {
      // 1. 加载应用数据（跳过查询）
      await loadAppData(true);

      // 2. 加载风险类型数据
      await loadRiskTypeData();

      // 3. 初始化应用视图（跳过查询）
      initAppView(true);

      // 4. 计算高度（跳过查询）
      vulTagHeight(true);

      // 5. 最后只调用一次查询数据
      queryEventData();
    } catch (error) {
      console.error('初始化组件时出错:', error);
    }
  };

  // 执行初始化
  initializeComponent();

  // 创建一个变量来存储resize定时器ID
  let resizeTimerId: number | null = null;

  // 添加窗口大小变化的监听器，以便在窗口大小变化时重新计算表格高度
  const resizeHandler = () => {
    // 使用防抖函数避免频繁触发
    if (resizeTimerId) {
      clearTimeout(resizeTimerId);
    }
    resizeTimerId = window.setTimeout(() => {
      vulTagHeight(true);
    }, 200);
  };

  // 添加窗口大小变化的监听器
  window.addEventListener('resize', resizeHandler);

  // 在组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler);
    // 清除resize定时器
    if (resizeTimerId) {
      clearTimeout(resizeTimerId);
      resizeTimerId = null;
    }
  });
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
});

const eventDispatchModalVisable = ref(false);
const eventDispatchModalForm = reactive({
  title: "",
  deptId: "",
  userId: [],
  params: "",
  currentView: "assetView"
});
const initEventDispatchModalForm = () => {
  eventDispatchModalForm.title = "";
  eventDispatchModalForm.deptId = "";
  eventDispatchModalForm.userId = [];
  eventDispatchModalForm.params = "";
  // 移除了视角切换，只保留资产视角
};

const batchOperationRowsData = reactive({
  apiEventUid: ""
});
const closeDraw = () => {
  queryEventData();
  showHandle.value = false;
};
const handleCommand = (row: any) => {
  initEventDispatchModalForm();

  batchOperationRowsData.apiEventUid = row.apiEventUid;
  showHandle.value = true;
};

const filterDataProvider: TableFilterDataProvider = {
  options: (prop: string, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryApiEventAssetGroup(query)
        .then((res: any) => {
          // console.log(res);
          resolve(res.data);
        })
        .catch((err: any) => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery("", "", true);
  }
};

//跳转
const jumpTo = (sign: string, needsPassedData: Object) => {
  emit("jump-to", sign, needsPassedData);
};
</script>

<style scoped>
.deal-status-tabs :deep(.el-tabs__header),
.risk-type-tabs :deep(.el-tabs__header),
.risk-level-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
}

/* 保留灰色分割横线 */
.deal-status-tabs :deep(.el-tabs__nav-wrap::after),
.risk-type-tabs :deep(.el-tabs__nav-wrap::after),
.risk-level-tabs :deep(.el-tabs__nav-wrap::after) {
  /* 不再隐藏分割线 */
  height: 1px;
  background-color: #e4e7ed;
}

.deal-status-tabs :deep(.el-tabs__active-bar),
.risk-type-tabs :deep(.el-tabs__active-bar),
.risk-level-tabs :deep(.el-tabs__active-bar) {
  background-color: var(--el-color-primary);
}

.deal-status-tabs :deep(.el-tabs__item),
.risk-type-tabs :deep(.el-tabs__item),
.risk-level-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.deal-status-tabs :deep(.el-tabs__item.is-active),
.risk-type-tabs :deep(.el-tabs__item.is-active),
.risk-level-tabs :deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
  font-weight: bold;
}

/* 虚线分割线样式 */
.dashed-divider {
  height: 1px;
  border: none;
  border-top: 1px dashed #e4e7ed;
  width: 98%;
}
</style>
