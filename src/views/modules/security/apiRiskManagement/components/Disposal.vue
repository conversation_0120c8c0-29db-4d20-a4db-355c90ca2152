<template>
    <div ref="container">
        <el-form style="margin-top: 1rem;" :model="batchOperationForm" :rules="rules01" ref="batchOperationFormRef"
            class="demo-ruleForm">
            <el-form-item label="处置动作" prop="region">
                <el-select style="width: 16rem;" clearable v-model="batchOperationForm.region" placeholder="请选择处置动作">
                    <el-option label="已处置" value="disposed" />
                    <el-option label="无需处理" value="nodisposed" />
                    <!-- <el-option label="暂不处理" value="notHandledYet" /> -->
                </el-select>
            </el-form-item>
            <el-form-item label="处置意见" prop="deal_idea">
                <el-input placeholder="请输入处置意见" maxlength="200" show-word-limit v-model="batchOperationForm.deal_idea"
                    type="textarea" :rows="4"></el-input>
            </el-form-item>

        </el-form>
        <div class="demo-drawer__footer" style="text-align: center;">
            <el-button type="primary" @click="submit">提 交</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import { computed, getCurrentInstance, nextTick, onMounted, reactive, ref, toRefs, watch } from "vue";
import { http } from "@/utils/http";

const props = defineProps({
    batchOperationModelData: Object
})

const batchOperationForm = reactive({
    deal_idea: "",
    batchOperationModel: "",
    region: ""
});

watch(props.batchOperationModelData, val => {
    if (val?.apiEventUid?.length > 0) {
        console.log(val);
        batchOperationForm.batchOperationModel = val.apiEventUid as any;
    }
}, {immediate: true})




const rules01 = reactive({
    deal_idea: [
        { required: true, message: "请输入处置意见", trigger: "blur" },
    ],
    // batchOperationModel: [
    //     { required: true, trigger: "change" },
    // ],
    region: [
        {
            required: true,
            message: '请选择处置动作',
            trigger: 'change',
        },
    ],
})



const emit = defineEmits(['closeDraw'])
const batchOperationFormRef = ref(null);
const submit = () => {
    batchOperationFormRef.value.validate((valid) => {
        if (valid) {
            http
                .postJson("security-event/apiEvent/disposal/manual", {
                    "dealIdea": batchOperationForm.deal_idea,
                    "apiEventUid": batchOperationForm.batchOperationModel,
                    "dealStatus": batchOperationForm.region,
                })
                .then(res => {
                    if (!res['errors']) {
                        ElMessage.success("处置提交成功！");
                    }
                    emit('closeDraw');
                })
                .catch(() => {
                    ElMessage.error("处置提交失败！");
                });
        } else {
            ElMessage.warning("请完成表单必填项");
            return false;
        }
    });
}

const cancel = () => {
    emit('closeDraw');
}

const container = ref(null);
const tableWidth = ref("");
onMounted(() => {
    nextTick(() => {
        console.log(container.value.offsetWidth);
        tableWidth.value = container.value.offsetWidth - 100 + '';
    })
})
</script>

<style lang="scss" scoped></style>