<template>
  <div>
    <!-- 使用transition组件实现组件切换的过渡效果 -->
    <transition mode="out-in" name="fade-transform">
      <!-- 使用keep-alive包裹动态组件，保持组件状态 -->
      <keep-alive>
        <component :is="currentComponent" :event-info="selectedEvent" @jump-to="comChange"
          @event-select="eventSelectHandler" @back="handleBack" :needsPassedData="state.needsPassedData" />
      </keep-alive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, shallowRef, toRefs } from "vue";
import vulnerabilityHomePage from "@/views/modules/security/apiRiskManagement/components/apiManagementHomePage.vue";
import apiAlertDetails from "@/views/modules/security/apiRiskManagement/components/apiAlertDetails.vue";

const vulnerabilityHome = shallowRef(vulnerabilityHomePage);
const apiAlertInfo = shallowRef(apiAlertDetails);

//数据对象
const state = reactive({
  // 当前显示的组件
  currentComponent: null,
  // 选中的事件
  selectedEvent: null,
  // 需要传递的数据
  needsPassedData: {}
});
const { currentComponent, selectedEvent } = toRefs(state);



// 默认显示EventDealManage组件
state.currentComponent = vulnerabilityHome;

// 根据传入的值切换组件
const comChange = (val: string, portableData: Object) => {
  if (val == "vulnerabilityHome") {
    state.currentComponent = vulnerabilityHome;
  } else if (val == "apiAlertInfo") {
    state.currentComponent = apiAlertInfo;
    state.needsPassedData = portableData; // 传递需要的数据
  }
};

// 处理事件选择
const eventSelectHandler = (evt: any) => {
  state.selectedEvent = evt;
};

// 处理返回事件
const handleBack = () => {
  state.currentComponent = vulnerabilityHome;
};

</script>
