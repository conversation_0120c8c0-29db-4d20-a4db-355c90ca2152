<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { onBeforeUnmount, onMounted, reactive, ref, toRaw } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { initRouter, getTopMenu } from "@/router/utils";
import { getVerifyCode } from "@/api/user";
import { ResultStatus } from "@/utils/http/types";
import { bg, avatar, security } from "./utils/static";
import Lock from "@iconify-icons/ri/lock-fill";
import User from "@iconify-icons/ri/user-3-fill";
import Code from "@iconify-icons/ri/checkbox-multiple-fill";
import ThemeSwitch from "@/layout/components/setting/components/ThemeSwitch.vue";
import { useDark } from "@pureadmin/utils";

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const codeIco = ref("");
const requireVerifyCode = ref(false);
const ruleFormRef = ref<FormInstance>();

const { initStorage } = useLayout();
initStorage();

const { dataThemeChange } = useDataThemeChange();
dataThemeChange();
const { title } = useNav();
const { isDark } = useDark();

const ruleForm = reactive({
  username: "",
  password: "",
  verifyCode: "",
  rnd: ""
});

const getCode = async () => {
  const backColor = isDark.value ? "38.39.39" : "245.247.250";
  getVerifyCode(backColor).then(result => {
    if (result.data.requireVerifyCode === "false") {
      requireVerifyCode.value = false;
    } else {
      requireVerifyCode.value = true;
      codeIco.value = result.data.image;
      ruleForm.rnd = result.data.rnd;
    }
  });
};

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      useUserStoreHook()
        .loginByUsername(ruleForm)
        .then(res => {
          if (res.status === ResultStatus.Success) {
            // 获取后端路由
            initRouter().then(() => {
              router.push(getTopMenu(true).path);
              message("登录成功", { type: "success" });
            });
          } else {
            loading.value = false;
            message(res.msg, { type: "error" });
          }
        });
    } else {
      loading.value = false;
      return fields;
    }
  });
};

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    onLogin(ruleFormRef.value);
  }
}

//主题改变触发
const themeChangeHandler = () => {
  setTimeout(() => getCode(), 200);
};

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress);
  getCode();
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <theme-switch @theme-change="themeChangeHandler" />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(security)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h3 class="p-3 outline-zinc-300">{{ title }}</h3>
          </Motion>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
                v-show="!loading"
              >
                <el-input
                  clearable
                  v-model="ruleForm.username"
                  placeholder="账号"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password" v-show="!loading">
                <el-input
                  clearable
                  show-password
                  v-model="ruleForm.password"
                  placeholder="密码"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>
            <Motion :delay="250">
              <el-form-item
                prop="verifyCode"
                v-show="requireVerifyCode && !loading"
              >
                <el-input
                  v-model.trim="ruleForm.verifyCode"
                  placeholder="验证码"
                  :prefix-icon="useRenderIcon(Code)"
                  clearable
                >
                  <template #append>
                    <el-tooltip
                      effect="dark"
                      content="点击更换图片"
                      placement="right-start"
                    >
                      <img
                        :src="'data:image/png;base64,' + codeIco"
                        @click="getCode()"
                        style="padding-right: 2px"
                        alt="验证码"
                      />
                    </el-tooltip>
                  </template>
                </el-input>
              </el-form-item>
            </Motion>

            <Motion :delay="350">
              <el-button
                class="w-full mt-4"
                size="default"
                type="primary"
                :loading="loading"
                @click="onLogin(ruleFormRef)"
              >
                {{ loading ? "系统加载中..." : "登录" }}
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
