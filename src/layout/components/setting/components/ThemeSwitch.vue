<template>
  <div class="search-container w-[40px] h-[48px] flex-c navbar-bg-hover">
    <el-switch
      v-model="dataTheme"
      inline-prompt
      :active-icon="darkIcon"
      :inactive-icon="dayIcon"
      class="theme-color"
      @change="themeChangeHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import dayIcon from "@/assets/svg/day.svg?component";
import darkIcon from "@/assets/svg/dark.svg?component";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";

const { dataTheme, dataThemeChange } = useDataThemeChange();

//定义事件
const emit = defineEmits(["theme-change"]);

//主题改变触发
const themeChangeHandler = () => {
  dataThemeChange();
  emit("theme-change");
};
</script>

<style lang="scss" scoped>
.theme-color {
  --el-switch-off-color: var(--el-color-primary);
  --el-switch-on-color: #5d5c5c;
}
</style>
