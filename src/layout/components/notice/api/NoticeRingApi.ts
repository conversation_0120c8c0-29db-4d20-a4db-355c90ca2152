import { http } from "@/utils/http";

import { ServerNames } from "@/utils/http/serverNames";
import { MessageTableInfo } from "@/views/system/message/api/MessageManageApi";

const basePath = `${ServerNames.portalServer}/message/manage`;

export type MessageRingData = {
  needDoingData: Array<MessageTableInfo>;
  noticeData: Array<MessageTableInfo>;
};

export type UnReadMessageCountInfo = {
  total: number;
  needDeal: number;
  notice: number;
};

//查询消息铃铛数据
const getMessageRingData = () =>
  http.get<any, RestResult<MessageRingData>>(`${basePath}/getMessageRingData`);

//获取消息铃铛数据刷新间隔时长
const getRingRefreshTimeLong = () =>
  http.get<any, RestResult<number>>(`${basePath}/getRingRefreshTimeLong`);

//计算用户未读消息数据
const countUserUnReadMessage = () =>
  http.get<any, RestResult<UnReadMessageCountInfo>>(
    `${basePath}/countUserUnReadMessage`
  );

//一键已读消息
const oneKeyRead = (subject: number) =>
  http.get<string, RestResult<string>>(
    `${basePath}/oneKeyRead?subject=${subject}`
  );

export {
  getMessageRingData,
  getRingRefreshTimeLong,
  countUserUnReadMessage,
  oneKeyRead
};
