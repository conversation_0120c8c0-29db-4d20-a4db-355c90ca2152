<template>
  <el-table
    v-if="tableData && tableData.length > 0"
    :data="tableData"
    :show-header="false"
    class="w-full"
    @row-click="rowClickHandler"
  >
    <el-table-column label="内容">
      <template #default="scope">
        <el-badge
          v-if="!scope.row.status"
          :value="1"
          :is-dot="true"
          class="pt-2"
        />
        [{{ scope.row.typeName }}] &nbsp;
        <span style="font-size: 10px; color: #8d8d8d">
          {{ scope.row.createTime }}
        </span>
        <br />
        <el-link type="primary">{{ scope.row.messageTitle }}</el-link>
      </template>
    </el-table-column>
    <el-table-column label="优先级" width="50">
      <template #default="scope">
        <el-tag v-if="scope.row.priority === 0" type="danger">高</el-tag>
        <el-tag v-if="scope.row.priority === 1" type="warning">中</el-tag>
        <el-tag v-if="scope.row.priority === 2">低</el-tag>
      </template>
    </el-table-column>
  </el-table>
  <el-empty v-else :image-size="100" description="暂无新待办" />
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useUnReadMessageHook } from "@/store/modules/message";
import { MessageTableInfo } from "@/views/system/message/api/MessageManageApi";

//定义事件
const emit = defineEmits(["view-detail"]);

//未读待办消息数据 计算属性
const tableData = computed(
  () => useUnReadMessageHook().getUnReadMessageData.needDoingData
);

//表格行点击Handler
const rowClickHandler = (row: MessageTableInfo) => {
  emit("view-detail", row);
};
</script>
