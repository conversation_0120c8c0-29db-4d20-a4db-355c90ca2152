<template>
  <el-dropdown
    trigger="click"
    placement="bottom-end"
    @visible-change="visibleChangeHandler"
  >
    <span class="dropdown-badge navbar-bg-hover select-none">
      <el-badge
        :value="unReadCountData.total"
        :max="99"
        :hidden="unReadCountData.total == 0"
      >
        <span class="header-notice-icon">
          <IconifyIconOffline :icon="Bell" />
        </span>
      </el-badge>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-tabs :stretch="true" v-model="activeKey" class="w-[360px]">
          <el-tab-pane name="needDoing">
            <template #label>
              <span class="flex-c">
                <el-badge
                  :value="unReadCountData.needDeal"
                  :hidden="unReadCountData.needDeal == 0"
                  class="mt-1"
                >
                  <span>待办</span>
                </el-badge>
              </span>
            </template>
            <need-deal-message-table
              :height="260"
              @view-detail="viewDetailHandler"
            />
          </el-tab-pane>
          <el-tab-pane name="notice">
            <template #label>
              <el-badge
                :value="unReadCountData.notice"
                :hidden="unReadCountData.notice == 0"
                class="mt-1"
              >
                <span class="flex-c">
                  <span>通知</span>
                </span>
              </el-badge>
            </template>
            <notice-message-table
              :height="260"
              @view-detail="viewDetailHandler"
            />
          </el-tab-pane>
        </el-tabs>
        <div class="w-full space-x-3 flex-bc px-5 py-3">
          <el-link
            type="primary"
            :icon="useRenderIcon('EP-Finished')"
            @click="batchUpdateReadStatus"
          >
            一键已读
          </el-link>
          <el-link
            type="primary"
            :icon="useRenderIcon('EP-View')"
            @click="toMessageCenter"
          >
            查看全部
          </el-link>
          <el-link
            type="primary"
            :icon="useRenderIcon('EP-Tools')"
            @click="toMessageConfig"
            v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
          >
            消息配置
          </el-link>
        </div>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import Bell from "@iconify-icons/ep/bell";
import NeedDealMessageTable from "@/layout/components/notice/components/NeedDealMessageTable.vue";
import NoticeMessageTable from "@/layout/components/notice/components/NoticeMessageTable.vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUnReadMessageHook } from "@/store/modules/message";
import MessageDetailInfo from "@/views/system/message/components/MessageDetailInfo.vue";
import { addDialog } from "@/components/ReDialog/index";
import { ResultStatus } from "@/utils/http/types";
import { AdminEnum } from "@/utils/CommonTypes";
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
  h,
  getCurrentInstance
} from "vue";
import {
  MessageTableInfo,
  updateReadStatus
} from "@/views/system/message/api/MessageManageApi";
import {
  getRingRefreshTimeLong,
  oneKeyRead
} from "@/layout/components/notice/api/NoticeRingApi";

const { $router, $confirm, $notify } =
  getCurrentInstance().appContext.config.globalProperties;
const activeKey = ref("needDoing");
const messageDetailRef = ref<InstanceType<typeof MessageDetailInfo>>();
let refreshUnReadMsgInterval: any;

//初始化 设置数据定时拉取定时器
const init = async () => {
  getRingRefreshTimeLong().then(result => {
    const timeLong = result.data;
    if (timeLong > 0) {
      refreshUnReadMsgInterval = setInterval(() => {
        getUnReadNum();
      }, timeLong * 60 * 1000);
    }
  });
};

//获取未读消息数据
const getUnReadNum = () => {
  useUnReadMessageHook()?.loadUnReadCountData();
};

onMounted(() => {
  // 初始化
  init();
  getUnReadNum();
});

onUnmounted(() => {
  // 清除刷新未读消息的定时器
  clearInterval(refreshUnReadMsgInterval);
});

//消息铃铛下拉框展示、隐藏时触发
const visibleChangeHandler = (visible: boolean) => {
  if (visible) {
    useUnReadMessageHook()?.loadUnReadMessageData();
    useUnReadMessageHook()?.loadUnReadCountData();
  }
};

//未读消息数统计计算属性
const unReadCountData = computed(
  () => useUnReadMessageHook()?.getUnReadCountData
);

//查看消息详细信息
const viewDetailHandler = (msg: MessageTableInfo) => {
  addDialog({
    title: msg.messageTitle,
    fullscreenIcon: true,
    closeOnClickModal: true,
    hideFooter: true,
    contentRenderer: () => h(MessageDetailInfo, { ref: messageDetailRef }),
    open: () => {
      messageDetailRef.value.loadMessageInfo(msg.messageId);
    },
    closeCallBack: () => {
      updateReadStatus(msg.relationId).then(() => {
        useUnReadMessageHook()?.loadUnReadCountData();
      });
    }
  });
};

//跳转到消息中心
const toMessageCenter = () => {
  $router.push({ name: "SystemConfig_Message_Center" });
};

//跳转到消息配置
const toMessageConfig = () => {
  $router.push({ name: "SystemConfig_Message_MetaDataManage" });
};

//一键已读
const batchUpdateReadStatus = () => {
  let subject = -1;
  if (activeKey.value === "needDoing") {
    subject = 0;
  } else if (activeKey.value === "notice") {
    subject = 1;
  }
  $confirm(
    `您确认要将所有的未读 ${subject == 0 ? "待办" : "通知"} 消息设置为已读么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  ).then(() => {
    oneKeyRead(subject).then(res => {
      if (res.status == ResultStatus.Success) {
        useUnReadMessageHook()?.loadUnReadMessageData();
        useUnReadMessageHook()?.loadUnReadCountData();
        $notify({
          title: "提示",
          message: "已成功将消息状态设置为已读！",
          type: "success"
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.dropdown-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 48px;
  cursor: pointer;

  .header-notice-icon {
    font-size: 18px;
  }
}
</style>
