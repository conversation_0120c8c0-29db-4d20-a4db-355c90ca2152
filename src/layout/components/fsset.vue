<script lang="ts">
import { defineComponent, ref } from "vue";
import {
  MoreFilled,
  CaretLeft,
  CaretRight,
  ArrowRightBold,
  SwitchButton
} from "@element-plus/icons-vue";
import { emitter } from "@/utils/mitt";
import { useTags } from "@/layout/hooks/useTag";
import { useFullscreen } from "@vueuse/core";
export default defineComponent({
  name: "fsset",
  components: {
    MoreFilled,
    CaretLeft,
    CaretRight,
    ArrowRightBold,
    SwitchButton
  },
  setup() {
    const { pureSetting, onContentFullScreen } = useTags();
    const { toggle } = useFullscreen();
    const showFlag = ref(false);
    return { pureSetting, onContentFullScreen, toggle, showFlag };
  },
  methods: {
    back() {
      history.back();
    },
    forward() {
      history.forward();
    },
    toogleFullScreen() {
      this.pureSetting.hiddenTag = !this.pureSetting.hiddenTag;
      this.pureSetting.screenMirroringMode =
        !this.pureSetting.screenMirroringMode;
      this.toggle();
      this.onContentFullScreen();
      emitter.emit(
        "tagViewsChange",
        this.pureSetting.hiddenTag as unknown as string
      );
    },
    handleKeyDown(e) {
      if (e.altKey) {
        switch (e.key) {
          // case "ArrowLeft": // Alt+Left触发后退
          //   this.back();
          //   break;
          // case "ArrowRight": // Alt+Right触发前进
          //   this.forward();
          //   break;
          case "Q":
          case "q": // Alt+Q触发退出
            this.toogleFullScreen(); // 或自定义退出逻辑
            break;
        }
      }
    },
    handleMousemove(e: MouseEvent) {
      try {
        let { clientX, clientY } = e;
        this.showFlag = clientY < 95;
      } catch (e) {
        this.showFlag = false;
      }
    },
    bindEvents() {
      document.addEventListener("keydown", this.handleKeyDown, true);
      document.addEventListener("mousemove", this.handleMousemove, true);
    },
    unbindEvents() {
      document.removeEventListener("keydown", this.handleKeyDown, true);
      document.removeEventListener("mousemove", this.handleMousemove, true);
    }
  },
  mounted() {
    this.bindEvents();
  },
  beforeUnmount() {
    this.unbindEvents();
  },
  watch: {
    // "pureSetting.screenMirroringMode"(val) {
    //   if (val) {
    //     this.bindEvents();
    //   } else {
    //     this.unbindEvents();
    //   }
    // },
    $route(val, val2) {}
  }
});
</script>
<template>
  <div
    v-if="pureSetting.screenMirroringMode"
    v-show="showFlag"
    class="fsset"
    @keydown="handleKeyDown"
    @hook:mounted="bindEvents"
    @hook:beforeUnmount="unbindEvents"
  >
    <el-icon class="icon" @click="back" title="后退"><CaretLeft /></el-icon>
    <el-icon
      class="icon font-bold"
      style="font-weight: bold; font-size: 12px"
      title="退出投屏模式"
      @click="toogleFullScreen"
      ><SwitchButton
    /></el-icon>
    <el-icon class="icon" @click="forward" title="前进"><CaretRight /></el-icon>
  </div>
</template>

<style scoped lang="scss">
.fsset {
  position: fixed;
  top: 2px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 0 12px;
  height: 24px;
  background: rgba(29, 29, 29, 0.7);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  .icon {
    font-size: 16px;
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
</style>
