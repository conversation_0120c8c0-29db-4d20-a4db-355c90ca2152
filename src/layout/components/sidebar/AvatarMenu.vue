<template>
  <div>
    <el-dropdown placement="bottom-end">
      <div
        class="flex justify-start items-center pr-2 pl-2 h-[48px] el-dropdown-link navbar-bg-hover"
      >
        <el-avatar
          v-if="avatarUrl != null && avatarUrl.length > 0"
          :src="avatarUrl"
        />
        <el-avatar v-else size="default">
          {{ realName.split("")[0] }}</el-avatar
        >
        <p v-if="realName" class="pl-2 dark:text-white">{{ realName }}</p>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="backTopMenu">
            <IconifyIconOffline icon="EP-HomeFilled" class="w-5 pr-1" />
            首页
          </el-dropdown-item>
          <el-dropdown-item @click="goToPersonalCenter">
            <IconifyIconOffline icon="EP-UserFilled" class="w-5 pr-1" />
            个人中心
          </el-dropdown-item>
          <el-dropdown-item @click="contactAdminVisible = true">
            <IconifyIconOffline icon="RI-UserVoiceFill" class="w-5 pr-1" />
            联系管理员
          </el-dropdown-item>
          <el-dropdown-item divided @click="logout">
            <IconifyIconOffline icon="RI-LogoutCircleRLine" class="w-5 pr-1" />
            退出系统
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <admin-contact-viewer v-model:visible="contactAdminVisible" />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, computed, reactive, toRefs } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import { useUserStoreHook } from "@/store/modules/user";
import AdminContactViewer from "@/views/system/usermanage/components/AdminContactViewer.vue";

const { $router } = getCurrentInstance().appContext.config.globalProperties;

const { logout, backTopMenu } = useNav();

const realName = computed(() => useUserStoreHook()?.getRealName);
const avatarUrl = computed(() => useUserStoreHook()?.getUserAvatar);

//数据对象
const state = reactive({
  contactAdminVisible: false
});
const { contactAdminVisible } = toRefs(state);

//进入个人中心
const goToPersonalCenter = () => {
  $router.push({ name: "Personal_Center" });
};
</script>
