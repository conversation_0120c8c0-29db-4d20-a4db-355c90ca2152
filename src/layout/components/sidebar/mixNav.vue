<script setup lang="ts">
import Search from "../search/index.vue";
import Notice from "../notice/index.vue";
import { isAllEmpty } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import { nextTick, onMounted, ref, toRaw, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import tShirtFill from "@iconify-icons/ri/t-shirt-fill";
import { storageSession } from "@pureadmin/utils";
import { DataInfo, sessionKey } from "@/utils/auth";
import AvatarMenu from "@/layout/components/sidebar/AvatarMenu.vue";
import ThemeSwitch from "@/layout/components/setting/components/ThemeSwitch.vue";
import ExtraIcon from "@/layout/components/sidebar/extraIcon.vue";

const menuRef = ref();
const defaultActive = ref(null);

const { route, device, onPanel, resolvePath, getDivStyle } = useNav();

const viewRealName = ref("");

function getDefaultActive(routePath) {
  const wholeMenus = usePermissionStoreHook().wholeMenus;
  /** 当前路由的父级路径 */
  const parentRoutes = getParentPaths(routePath, wholeMenus)[0];
  defaultActive.value = !isAllEmpty(route.meta?.activePath)
    ? route.meta.activePath
    : findRouteByPath(parentRoutes, wholeMenus)?.children[0]?.path;
}

onMounted(() => {
  getDefaultActive(route.path);
  viewRealName.value =
    storageSession().getItem<DataInfo<number>>(sessionKey)?.realName ?? "";
});

nextTick(() => {
  menuRef.value?.handleResize();
});

watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    getDefaultActive(route.path);
  }
);
</script>

<template>
  <div
    v-if="device !== 'mobile'"
    class="horizontal-header"
    v-loading="usePermissionStoreHook().wholeMenus.length === 0"
  >
    <el-menu
      router
      ref="menuRef"
      mode="horizontal"
      class="horizontal-header-menu"
      :default-active="defaultActive"
    >
      <el-menu-item
        v-for="route in usePermissionStoreHook().wholeMenus"
        :key="route.path"
        :index="resolvePath(route) || route.redirect"
      >
        <template #title>
          <div
            v-if="toRaw(route.meta.icon)"
            :class="['sub-menu-icon', route.meta.icon]"
          >
            <component
              :is="useRenderIcon(route.meta && toRaw(route.meta.icon))"
            />
          </div>
          <div :style="getDivStyle">
            <span class="select-none">
              {{ route.meta.title }}
            </span>
            <extra-icon :extraIcon="route.meta.extraIcon" />
          </div>
        </template>
      </el-menu-item>
    </el-menu>
    <div class="horizontal-header-right space-x-1">
      <!-- 菜单搜索 -->
      <Search />

      <!-- 主题切换 -->
      <theme-switch />

      <!-- 通知 -->
      <Notice id="header-notice" />

      <!-- 界面设置 -->
      <span
        class="set-icon navbar-bg-hover"
        title="打开界面设置"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="tShirtFill" />
      </span>

      <!-- 用户头像菜单 -->
      <avatar-menu />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
