<script setup lang="ts">
import Search from "./search/index.vue";
import Notice from "./notice/index.vue";
import { useNav } from "@/layout/hooks/useNav";
import Breadcrumb from "./sidebar/breadCrumb.vue";
import tShirtFill from "@iconify-icons/ri/t-shirt-fill";
import { storageSession } from "@pureadmin/utils";
import { onMounted, ref } from "vue";
import { DataInfo, sessionKey } from "@/utils/auth";
import AvatarMenu from "@/layout/components/sidebar/AvatarMenu.vue";
import ThemeSwitch from "@/layout/components/setting/components/ThemeSwitch.vue";
import MixNav from "@/layout/components/sidebar/mixNav.vue";

const { layout, device, onPanel, pureApp, toggleSideBar } = useNav();

const viewRealName = ref("");

onMounted(() => {
  viewRealName.value =
    storageSession().getItem<DataInfo<number>>(session<PERSON>ey)?.realName ?? "";
});
</script>

<template>
  <div
    class="navbar bg-[#fff] shadow-sm shadow-[rgba(0, 21, 41, 0.08)] dark:shadow-[#0d0d0d]"
  >
    <topCollapse
      v-if="device === 'mobile'"
      class="hamburger-container"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />

    <!-- 路由面包屑 -->
    <Breadcrumb
      v-if="layout !== 'mix' && device !== 'mobile'"
      class="breadcrumb-container"
    />

    <!-- 左侧系统导航菜单 -->
    <mix-nav v-if="layout === 'mix'" />

    <!-- 右侧固定菜单 -->
    <div v-if="layout === 'vertical'" class="vertical-header-right space-x-1">
      <!-- 菜单搜索 -->
      <Search />

      <!-- 主题切换 -->
      <theme-switch />

      <!-- 通知 -->
      <Notice id="header-notice" />

      <!-- 界面设置 -->
      <span
        class="set-icon navbar-bg-hover"
        title="打开界面设置"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="tShirtFill" />
      </span>

      <!-- 用户头像菜单 -->
      <avatar-menu />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 48px;
  overflow: hidden;

  .hamburger-container {
    float: left;
    height: 100%;
    line-height: 48px;
    cursor: pointer;
  }

  .vertical-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 280px;
    height: 48px;
    color: #000000d9;
  }

  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
}
</style>
