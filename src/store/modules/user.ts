import { defineStore } from "pinia";
import { store } from "@/store";
import { userType } from "./types";
import { routerArrays } from "@/layout/types";
import { resetRouter, router } from "@/router";
import { storageSession } from "@pureadmin/utils";
import { getLogin, refreshTokenApi, UserSessionInfo } from "@/api/user";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { DataInfo, removeToken, sessionKey, setToken } from "@/utils/auth";
import { ResultStatus } from "@/utils/http/types";
import { sm3 } from "sm-crypto";

export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    //用户登录名
    username:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "",

    //用户姓名
    realName:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.realName ?? "",

    //用户头像
    userAvatar:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.userAvatar ?? "",

    //页面级别权限
    roles: storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [],

    //用户缓存信息
    userSessionInfo: storageSession().getItem<DataInfo<number>>(sessionKey)
  }),
  getters: {
    //获取用户头像地址
    getUserAvatar: state =>
      state.userAvatar != null && state.userAvatar.length > 0
        ? "/avatar/" + state.userAvatar
        : null,

    //获取用户姓名
    getRealName: state => state.realName,

    //获取用户会话信息
    getUserSessionInfo: state =>
      state.userSessionInfo != null
        ? state.userSessionInfo
        : storageSession().getItem<DataInfo<number>>(sessionKey)
  },
  actions: {
    //存储用户名
    SET_USERNAME(username: string) {
      this.username = username;
    },

    //存储角色
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },

    //更新用户头像
    SET_AVATAR(avatar: string) {
      this.userAvatar = avatar;
      const data = storageSession().getItem<DataInfo<number>>(sessionKey);
      data.userAvatar = avatar;
      setToken(data);
    },

    //账密登录
    loginByUsername: async function (data) {
      return new Promise<RestResult<string | null>>((resolve, reject) => {
        const requestData = {
          u: data.username,
          p: sm3(data.password),
          c: data.verifyCode,
          r: data.rnd
        };

        getLogin(requestData)
          .then(result => {
            if (result.status === ResultStatus.Success) {
              const token: string = result.data;
              const userSession: UserSessionInfo = JSON.parse(result.msg);
              this.dealSessionInfo(userSession, token);
            }
            resolve(result);
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    //登出系统
    logOut() {
      this.username = "";
      this.roles = [];
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },

    //刷新`token`
    async handRefreshToken(data: any) {
      return new Promise<RestResult<string>>((resolve, reject) => {
        refreshTokenApi(data)
          .then(result => {
            if (result) {
              const token: string = result.data;
              const userSession: UserSessionInfo = JSON.parse(result.msg);
              this.dealSessionInfo(userSession, token);
              resolve(result);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },

    //统一处理用会话信息和Token
    dealSessionInfo(userSession: UserSessionInfo, token: string) {
      this.userAvatar = userSession.userAvatar;
      this.realName = userSession.realName;
      const dataInfo = toDataInfo(userSession, token);
      this.userSessionInfo = dataInfo;
      setToken(dataInfo);
    }
  }
});

//转换到 DataInfo<number>
const toDataInfo = (
  userSession: UserSessionInfo,
  token: string
): DataInfo<number> => {
  return {
    accessToken: token,
    refreshToken: token,
    expires: userSession.expireTime,
    userId: userSession.userId,
    username: userSession.userName,
    userAvatar: userSession.userAvatar,
    realName: userSession.realName,
    pwdInvalid: userSession.pwdInvalid,
    deptId: userSession.deptId,
    roles: userSession.roleIds,
    tenantIds: userSession.tenantIds,
    maxRoleLevel: userSession.maxRoleLevel,
    enableSwitch: userSession.enableSwitch,
    projectCode: userSession.projectCode,
    zoneId: userSession.zoneId,
    zoneType: userSession.zoneType
  };
};

export function useUserStoreHook() {
  return useUserStore(store);
}
