import {defineStore} from "pinia";
import {getNeedCacheData, SimpleDeptInfo} from "@/views/system/deptmanage/api/DeptManageApi";
import {ResultStatus} from "@/utils/http/types";
import {store} from "@/store";

export const useDeptStore = defineStore("dept-store", {
  state: () => ({
    deptData: [] as SimpleDeptInfo[]
  }),
  getters: {
    getCacheData: state => {
      return state.deptData;
    }
  },
  actions: {
    //加载缓存数据
    async loadCacheData() {
      getNeedCacheData().then(result => {
        if (result.status == ResultStatus.Success) {
          this.deptData = result.data;
        }
      });
    },

    //获取子节点数据
    getChildren(parentId: string): Array<SimpleDeptInfo> {
      return this.deptData.filter(
        (d: SimpleDeptInfo) => parentId === d.parentId
      );
    },

    //获取指定 deptId 的组织信息
    getDept(deptId: string): SimpleDeptInfo | undefined {
      return this.deptData.find((d: SimpleDeptInfo) => deptId === d.deptId);
    },

    //获取当前节点以及所有层级子节点的 deptId 数组
    getChildrenIds(deptId: string): Array<string> {
      //处理当前组织节点
      const res = new Set<string>();
      const currentDept = this.getDept(deptId);
      if (!currentDept) {
        return [deptId];
      }

      //获取所有子节点
      res.add(deptId);
      this.deptData
        .filter((d: SimpleDeptInfo) => d.deptPath.startsWith(currentDept.deptPath))
        .forEach((d: SimpleDeptInfo) => res.add(d.deptId));

      return Array.from(res);
    }
  }
});

export function useDeptStoreHook() {
  return useDeptStore(store);
}
