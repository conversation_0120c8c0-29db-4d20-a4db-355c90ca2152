import { defineStore } from "pinia";
import { store } from "@/store";
import {
  MessageRingData,
  UnReadMessageCountInfo,
  getMessageRingData,
  countUserUnReadMessage
} from "@/layout/components/notice/api/NoticeRingApi";

export const useUnReadMessage = defineStore("un-read-message", {
  state: () => ({
    unReadCountData: {} as UnReadMessageCountInfo,
    unReadMessageData: {} as MessageRingData
  }),
  getters: {
    getUnReadCountData: state => state.unReadCountData,
    getUnReadMessageData: state => state.unReadMessageData
  },
  actions: {
    //加载未读消息统计数据
    async loadUnReadCountData() {
      countUserUnReadMessage().then(result => {
        this.unReadCountData = result.data;
      });
    },

    //加载未读消息数据
    async loadUnReadMessageData() {
      getMessageRingData().then(result => {
        this.unReadMessageData = result.data;
      });
    }
  }
});

export function useUnReadMessageHook() {
  return useUnReadMessage(store);
}
